##############################################################
Point in the ONwinsor subgrid.
82d00'00.000"W 42d00'00.000"N 0.0	81d59'59.6104"W	42d0'0.1602"N 0.000
82d00'01.000"W 42d00'00.000"N 0.0	82d0'0.6104"W	42d0'0.1602"N 0.000
82d00'02.000"W 42d00'00.000"N 0.0	82d0'1.6104"W	42d0'0.1602"N 0.000
84d00'00.000"W 42d00'00.000"N 0.0	83d59'59.8623"W	42d0'0.1807"N 0.000
##############################################################
Try with NTv2 and NTv1 together ... falls back to NTv1
99d00'00.000"W 65d00'00.000"N 0.0	99d0'1.5926"W	65d0'1.3478"N 0.000
111d00'00.000"W 46d00'00.000"N 0.0	111d0'3.1897"W	45d59'59.7489"N 0.000
111d00'00.000"W 47d30'00.000"N 0.0	111d0'2.8054"W	47d29'59.9899"N 0.000
##############################################################
Switching between NTv2 subgrids
-112.5839956 49.4914451 0	-112.58307621	49.49144267 0.00000000
##############################################################
Interpolating very close (and sometimes a bit outside) to the edges a NTv2 subgrid (#209)
-115.58333333 51.25000000 0	-115.58228512	51.24997866 0.00000000
-115.58333333 51.25000010 0	-115.58228512	51.24997876 0.00000000
-115.58333334 51.25000000 0	-115.58228513	51.24997866 0.00000000
-115.49166667 51.07500000 0	-115.49063575	51.07497568 0.00000000
-115.49166668 51.07500000 0	-115.49063576	51.07497568 0.00000000
-115.49166667 51.07499990 0	-115.49063575	51.07497558 0.00000000
##############################################################
Attempt first with ntv2_0.gsb and then conus
-111.5 45.26	-111.50079772	45.25992835 0.00000000
##############################################################
NAD27 -> NAD83: 1st through ntv2, 2nd through conus
55d00'00.000"N 111d00'00.000"W 0.0	55d0'0.367"N	111d0'3.231"W 0.000
39d00'00.000"N 111d00'00.000"W 0.0	38d59'59.912"N	111d0'2.604"W 0.000
