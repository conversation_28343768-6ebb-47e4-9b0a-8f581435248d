:
# Script to test proj exe
#
TEST_CLI_DIR=`dirname $0`
EXE=$1

usage()
{
    echo "Usage: ${0} <path to 'proj' program>" 
    echo
    exit 1
}

if test -z "${EXE}"; then
    EXE=../../src/proj
fi

if test ! -x ${EXE}; then
    echo "*** ERROR: Can not find '${EXE}' program!"
    exit 1
fi

if test -z "${PROJ_LIB}"; then
    export PROJ_LIB="`dirname $0`/../../data"
fi

echo "============================================"
echo "Running ${0} using ${EXE}:"
echo "============================================"

OUT=testproj_out
#
echo "doing tests into file ${OUT}, please wait"
#
$EXE +ellps=WGS84 +proj=ob_tran +o_proj=latlon +o_lon_p=0.0 +o_lat_p=90.0 +lon_0=360.0 +to_meter=0.0174532925199433 +no_defs -E -f '%.3f' >${OUT} <<EOF
2 49
EOF

#
# do 'diff' with distribution results
echo "diff ${OUT} with testproj_out.dist"
diff -u -b ${OUT} ${TEST_CLI_DIR}/testproj_out.dist
if [ $? -ne 0 ] ; then
	echo  ""
	echo "PROBLEMS HAVE OCCURRED"
	echo "test file ${OUT} saved"
    echo
	exit 100
else
	echo "TEST OK"
	echo "test file ${OUT} removed"
    echo
	/bin/rm -f ${OUT}
	exit 0
fi
