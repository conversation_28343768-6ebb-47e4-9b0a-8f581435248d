:
# Script to do some testing of various transformations depending on datum
# files that are not always available.
#
#
TEST_CLI_DIR=`dirname $0`
EXE=$1

usage()
{
    echo "Usage: ${0} <path to 'cs2cs' program>" 
    echo
    exit 1
}

if test -z "${EXE}"; then
    EXE=../../src/cs2cs
fi

if test ! -x ${EXE}; then
    echo "*** ERROR: Can not find '${EXE}' program!"
    exit 1
fi

echo "============================================"
echo "Running ${0} using ${EXE}:"
echo "============================================"

mkdir "dir with \" space"
if test -f "${PROJ_LIB}/conus"; then
    cp "${PROJ_LIB}/conus" "dir with \" space/myconus"
else
    cp "`dirname $0`/../../data/conus" "dir with \" space/myconus"
fi

OUT=td_out
#EXE=../src/cs2cs
#
echo "doing tests into file ${OUT}, please wait"
rm -f ${OUT}
#
echo "##############################################################" >> ${OUT}
echo 1st through ntv1, 2nd through conus >> ${OUT}
#
$EXE +proj=latlong +ellps=clrk66 +nadgrids=ntv1_can.dat,conus \
 +to +proj=latlong +datum=NAD83 \
 -E >>${OUT} <<EOF
111d00'00.000"W 44d00'00.000"N 0.0
111d00'00.000"W 39d00'00.000"N 0.0
EOF

echo "##############################################################" >> ${OUT}
echo As above, but without ntv1 everything goes through conus file. >> ${OUT}
#
$EXE +proj=latlong +ellps=clrk66 '+nadgrids="./dir with "" space/myconus"' \
 +to +proj=latlong +datum=NAD83 \
 -E >>${OUT} <<EOF
111d00'00.000"W 44d00'00.000"N 0.0
111d00'00.000"W 39d00'00.000"N 0.0
EOF

echo "##############################################################" >> ${OUT}
echo "Test --bbox -141.01,40.04,-47.74,83.17 NAD27 NAD83 (using NTv2)" >> ${OUT}
$EXE --bbox -141.01,40.04,-47.74,83.17 NAD27 NAD83 >>${OUT} <<EOF
40.5 -60
EOF

echo "##############################################################" >> ${OUT}
echo "Test --area \"USA - CONUS - onshore\" NAD27 NAD83 (using conus)" >> ${OUT}
$EXE --area "USA - CONUS - onshore" NAD27 NAD83 >>${OUT} <<EOF
44 -111
40.5 -60
EOF

echo "##############################################################" >> ${OUT}
echo Test MD used where available >> ${OUT}
#
$EXE +proj=latlong +ellps=clrk66 +nadgrids=MD,conus \
 +to +proj=latlong +datum=NAD83 \
 -E >>${OUT} <<EOF
79d58'00.000"W 37d02'00.000"N 0.0
79d58'00.000"W 36d58'00.000"N 0.0
EOF
#
$EXE +proj=latlong +ellps=clrk66 +nadgrids=conus \
 +to +proj=latlong +datum=NAD83 \
 -E >>${OUT} <<EOF
79d58'00.000"W 37d02'00.000"N 0.0
79d58'00.000"W 36d58'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test that we use grid shift files even if we are right on the" >> ${OUT}
echo "edge or even a wee bit outside (#141)."  >> ${OUT}
#
# Our test points are (1) right on mesh corner, (2) outside but within 
# epsilon (3) inside a bit (4) outside by more than epsilon
#
$EXE +proj=latlong +ellps=WGS84 +nadgrids=ntf_r93.gsb,null \
 +to +proj=latlong +datum=WGS84 \
 -E -f "%.12f" >>${OUT} <<EOF
-5.5 52.0
-5.5000000000001 52.0000000000001
-5.4999 51.9999
-5.5001 52.0
EOF
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=latlong +ellps=WGS84 +nadgrids=ntf_r93.gsb,null \
 -E -f "%.12f" >>${OUT} <<EOF
-5.5 52.0
-5.5000000000001 52.0000000000001
-5.4999 51.9999
-5.5001 52.0
EOF
#
echo "##############################################################" >> ${OUT}
echo "NAD27 -> NAD83: 1st through ntv1 or ntv2, 2nd through conus" >> ${OUT}
#
$EXE NAD27 NAD83 -E -f "%.4f" >>${OUT} <<EOF
55d00'00.000"N 111d00'00.000"W 0.0
39d00'00.000"N 111d00'00.000"W 0.0
EOF

#
echo "##############################################################" >> ${OUT}
echo "WGS84 -> WGS84+EGM96" >> ${OUT}
#
$EXE +init=epsg:4326 +to +init=epsg:4326 +geoidgrids=egm96_15.gtx -E >>${OUT} <<EOF
2dE 49dN 0
EOF

# Cleanup
rm -rf "dir with \" space"

#
##############################################################################
# Done!     
# do 'diff' with distribution results
echo "diff ${OUT} with ${OUT}.dist"
diff -u -b ${OUT} ${TEST_CLI_DIR}/${OUT}.dist
if [ $? -ne 0 ] ; then
	echo  ""
	echo "PROBLEMS HAVE OCCURRED"
	echo "test file ${OUT} saved"
    echo
	exit 100
else
	echo "TEST OK"
	echo "test file ${OUT} removed"
    echo
	/bin/rm -f ${OUT}
	exit 0
fi
