:
# Script to test some points in IGNF managed SRS.
# The expected coordinates have been calculated by
# other means.
# Real points locations are found in proj_outIGNF.dist-real
# while distribution results are found in proj_outIGNF.dist
#
# 2009-03-08 :  the null grid (nad2bin < null.lla null) MUST
#               be around otherwise the LAMBE to LAMB93
#               transformations will be wrong and vice-versa
# 2009-10-04 :  addition of NTFG to RGF93G test to check if
#               the gsb grid is still ok

TEST_CLI_DIR=`dirname $0`
EXE=$1

usage()
{
    echo "Usage: ${0} <path to 'cs2cs' program>" 
    echo
    exit 1
}

if test -z "${EXE}"; then
    EXE=../../src/cs2cs
fi

if test ! -x ${EXE}; then
    echo "*** ERROR: Can not find '${EXE}' program!"
    exit 1
fi

if test -z "${PROJ_LIB}"; then
    export PROJ_LIB="`dirname $0`/../../data"
fi

echo "============================================"
echo "Running ${0} using ${EXE}:"
echo "============================================"

OUT=proj_outIGNF

INIT_FILE=IGNF
RES="-f %.3f"
#
echo "doing tests into file ${OUT}, please wait"
#
rm -f ${OUT}
echo "+init=${INIT_FILE}:NTFG +to +init=${INIT_FILE}:RGF93G" >>${OUT}
$EXE +init=${INIT_FILE}:NTFG +to +init=${INIT_FILE}:RGF93G -E >>${OUT} <<EOF
 3.300866856 43.4477976569 0.0000
EOF
echo "+init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93" >>${OUT}
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
 600000.0000 2600545.4523  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
 135638.3592 2418760.4094  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
 998137.3947 2413822.2844  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
 600000.0000 2200000.0000  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
 311552.5340 1906457.4840  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
 960488.4138 1910172.8812  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
 600000.0000 1699510.8340  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:LAMB93 -E $RES >>${OUT} <<EOF
1203792.5981 626873.17210  0.0000
EOF
echo "+init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX" >>${OUT}
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
 600000.0000 2600545.4523  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
 135638.3592 2418760.4094  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
 998137.3947 2413822.2844  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
 600000.0000 2200000.0000  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
 311552.5340 1906457.4840  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
 960488.4138 1910172.8812  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
 600000.0000 1699510.8340  0.0000
EOF
$EXE +init=${INIT_FILE}:LAMBE +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
1203792.5981 626873.17210  0.0000
EOF
echo "+init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX" >>${OUT}
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
2d20'11.4239243" 50d23'59.7718445" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
-3d57'49.4051448" 48d35'59.7121716" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
7d44'12.1439796" 48d35'59.7832558" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
2d20'11.4951975" 46d47'59.8029841" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
-1d15'48.9240599" 44d05'59.8251878" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
6d50'12.2276489" 44d06'00.0517019" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
2d20'11.7754730" 42d18'00.0824436" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:GEOPORTALFXX -E $RES >>${OUT} <<EOF
9d32'12.6680218" 41d24'00.3542556" 0.0
EOF
echo "+init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER" >>${OUT}
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
2d20'11.4239243" 50d23'59.7718445" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
-3d57'49.4051448" 48d35'59.7121716" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
7d44'12.1439796" 48d35'59.7832558" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
2d20'11.4951975" 46d47'59.8029841" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
-1d15'48.9240599" 44d05'59.8251878" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
6d50'12.2276489" 44d06'00.0517019" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
2d20'11.7754730" 42d18'00.0824436" 0.0
EOF
$EXE +init=${INIT_FILE}:RGF93G +to +init=${INIT_FILE}:MILLER -E $RES >>${OUT} <<EOF
9d32'12.6680218" 41d24'00.3542556" 0.0
EOF
echo "+init=${INIT_FILE}:RGR92 +to +init=${INIT_FILE}:REUN47" >>${OUT}
$EXE +init=${INIT_FILE}:RGR92 +to +init=${INIT_FILE}:REUN47 -E $RES >>${OUT} <<EOF
3356123.5400 1303218.3090 5247430.6050
EOF

#
# do 'diff' with distribution results
echo "diff ${OUT} with ${OUT}.dist"
diff -u -b ${OUT} ${TEST_CLI_DIR}/${OUT}.dist
if [ $? -ne 0 ] ; then
echo  ""
echo "PROBLEMS HAVE OCCURRED"
echo "test file ${OUT} saved"
echo
exit 100
else
echo "TEST OK"
echo "test file ${OUT} removed"
echo
/bin/rm -f ${OUT}
exit 0
fi
