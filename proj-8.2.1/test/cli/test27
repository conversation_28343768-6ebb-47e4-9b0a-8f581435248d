:
# Script to test most of the SPCS zones.
# This script generated from execution of NMD's program l176, where
#   the second pair of numbers are respective easting and northing output.
#
# Proj will vary in the .001ft range with projections using Transverse
#   Mercator due to greater precision of meridional distance function.
#
TEST_CLI_DIR=`dirname $0`
EXE=$1

usage()
{
    echo "Usage: ${0} <path to 'proj' program>" 
    echo
    exit 1
}

if test -z "${EXE}"; then
    EXE=../../src/cs2cs
fi

if test ! -x ${EXE}; then
    echo "*** ERROR: Can not find '${EXE}' program!"
    exit 1
fi

if test -z "${PROJ_LIB}"; then
    export PROJ_LIB="`dirname $0`/../../data"
fi

echo "============================================"
echo "Running ${0} using ${EXE}:"
echo "============================================"

OUT=proj_out27
INIT_FILE=nad27
#
echo "doing tests into file ${OUT}, please wait"
#
$EXE +units=us-ft +init=${INIT_FILE}:5001 -E -f '%.3f' >${OUT} <<EOF
 -134d00'00.000  55d00'00.000   2615716.535   1156768.938 AK 1 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5001 -E -f '%.3f' >>${OUT} <<EOF
 -133d40'00.000  57d00'00.000   2685642.815   1887198.473 AK 1 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5001 -E -f '%.3f' >>${OUT} <<EOF
 -131d35'45.432  54d39'02.654   3124247.971   1035731.647 AK 1 GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5001 -E -f '%.3f' >>${OUT} <<EOF
 -129d32'30.000  54d32'30.000   3561180.429   1015414.284 AK 1 GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5001 -E -f '%.3f' >>${OUT} <<EOF
 -141d30'00.000  60d30'00.000   1275974.313   3248584.184 AK 1 GP6
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5002 -E -f '%.3f' >>${OUT} <<EOF
 -142d00'00.000  56d30'30.000    500000.000    916085.508 AK 2 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5003 -E -f '%.3f' >>${OUT} <<EOF
 -146d00'00.000  56d30'30.000    500000.000    916085.508 AK 3 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5004 -E -f '%.3f' >>${OUT} <<EOF
 -150d00'00.000  56d30'30.000    500000.000    916085.508 AK 4 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5005 -E -f '%.3f' >>${OUT} <<EOF
 -152d28'56.134  60d53'28.765    770312.640   2520850.031 AK 5 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5005 -E -f '%.3f' >>${OUT} <<EOF
 -154d00'00.000  56d30'30.000    500000.000    916085.508 AK 5 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5006 -E -f '%.3f' >>${OUT} <<EOF
 -155d00'00.000  71d00'00.000    857636.168   6224356.320 AK 6 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5006 -E -f '%.3f' >>${OUT} <<EOF
 -158d00'00.000  71d00'00.000    500000.000   6215501.078 AK 6 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5007 -E -f '%.3f' >>${OUT} <<EOF
 -162d00'00.000  65d15'00.000    700000.000   4111525.687 AK 7 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5008 -E -f '%.3f' >>${OUT} <<EOF
 -166d00'00.000  65d15'00.000    500000.000   4111525.687 AK 8 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5009 -E -f '%.3f' >>${OUT} <<EOF
 -170d00'00.000  63d20'00.000    600000.000   3410550.008 AK 9 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5010 -E -f '%.3f' >>${OUT} <<EOF
 -164d02'30.000  54d27'30.000   5533424.392   1473805.123 AK10 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5010 -E -f '%.3f' >>${OUT} <<EOF
 -176d00'00.000  52d30'00.000   3000000.000    547583.333 AK10 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:101 -E -f '%.3f' >>${OUT} <<EOF
  -85d50'00.000  31d20'00.000    500000.000    303093.746 AL E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:101 -E -f '%.3f' >>${OUT} <<EOF
  -85d12'41.738  32d38'57.737    691376.573    782394.791 AL E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:101 -E -f '%.3f' >>${OUT} <<EOF
  -86d36'58.670  34d48'58.708    264979.900   1571249.667 AL E GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:102 -E -f '%.3f' >>${OUT} <<EOF
  -87d30'00.000  33d20'00.000    500000.000   1212487.425 AL W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:102 -E -f '%.3f' >>${OUT} <<EOF
  -87d30'00.000  33d20'30.000    500000.000   1215519.455 AL W GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:301 -E -f '%.3f' >>${OUT} <<EOF
  -91d34'46.321  35d18'37.443   2125448.748    355890.988 AR N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:301 -E -f '%.3f' >>${OUT} <<EOF
  -92d04'11.625  35d19'34.269   1979150.162    361375.766 AR N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:302 -E -f '%.3f' >>${OUT} <<EOF
  -92d00'00.000  34d45'00.000   2000000.000    758096.040 AR S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:302 -E -f '%.3f' >>${OUT} <<EOF
  -92d00'00.000  33d15'00.000   2000000.000    212263.845 AR S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5300 -E -f '%.3f' >>${OUT} <<EOF
 -170d00'00.000 -14d16'00.000    500000.000    312234.650 AS   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5300 -E -f '%.3f' >>${OUT} <<EOF
 -166d50'38.406  -9d34'41.556   1640416.676   2007870.029 AS   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:202 -E -f '%.3f' >>${OUT} <<EOF
 -111d55'00.000  34d45'00.000    500000.000   1364267.386 AZ C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:202 -E -f '%.3f' >>${OUT} <<EOF
 -111d55'00.000  32d20'00.000    500000.000    484978.270 AZ C GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:201 -E -f '%.3f' >>${OUT} <<EOF
 -110d24'59.771  35d09'58.568    425301.125   1515853.426 AZ E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:201 -E -f '%.3f' >>${OUT} <<EOF
 -109d34'33.127  31d59'53.103    683147.830    363527.538 AZ E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:201 -E -f '%.3f' >>${OUT} <<EOF
 -110d30'34.948  35d07'28.243    397422.297   1500739.241 AZ E GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:201 -E -f '%.3f' >>${OUT} <<EOF
 -109d45'13.226  32d08'41.778    627823.092    416691.532 AZ E GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:203 -E -f '%.3f' >>${OUT} <<EOF
 -113d45'00.000  34d45'00.000    500000.000   1364312.866 AZ W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:203 -E -f '%.3f' >>${OUT} <<EOF
 -113d45'00.000  34d45'30.000    500000.000   1367345.603 AZ W GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:203 -E -f '%.3f' >>${OUT} <<EOF
 -113d45'00.000  34d46'00.000    500000.000   1370378.345 AZ W GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:401 -E -f '%.3f' >>${OUT} <<EOF
 -122d00'00.000  41d30'00.000   2000000.000    789314.699 CA 1 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:401 -E -f '%.3f' >>${OUT} <<EOF
 -122d00'00.000  41d30'30.000   2000000.000    792351.052 CA 1 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:402 -E -f '%.3f' >>${OUT} <<EOF
 -122d00'00.000  39d20'00.000   2000000.000    606975.074 CA 2 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:402 -E -f '%.3f' >>${OUT} <<EOF
 -122d00'00.000  39d20'30.000   2000000.000    610010.158 CA 2 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:403 -E -f '%.3f' >>${OUT} <<EOF
 -120d30'00.000  37d05'00.000   2000000.000    212394.029 CA 3 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:403 -E -f '%.3f' >>${OUT} <<EOF
 -121d22'26.019  37d30'30.324   1746516.910    368350.900 CA 3 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:403 -E -f '%.3f' >>${OUT} <<EOF
 -119d46'32.733  37d07'41.470   2211146.746    229541.692 CA 3 GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:403 -E -f '%.3f' >>${OUT} <<EOF
 -119d38'26.434  36d55'48.009   2251190.696    157720.169 CA 3 GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:403 -E -f '%.3f' >>${OUT} <<EOF
 -120d42'59.779  38d06'52.815   1937681.203    587984.757 CA 3 GP5
EOF
$EXE +units=us-ft +init=${INIT_FILE}:404 -E -f '%.3f' >>${OUT} <<EOF
 -119d00'00.000  36d20'00.000   2000000.000    364036.106 CA 4 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:404 -E -f '%.3f' >>${OUT} <<EOF
 -119d00'00.000  36d20'30.000   2000000.000    367069.711 CA 4 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:405 -E -f '%.3f' >>${OUT} <<EOF
 -118d00'00.000  34d45'00.000   2000000.000    454894.032 CA 5 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:405 -E -f '%.3f' >>${OUT} <<EOF
 -118d00'00.000  34d45'30.000   2000000.000    457926.735 CA 5 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:406 -E -f '%.3f' >>${OUT} <<EOF
 -116d15'00.000  33d20'00.000   2000000.000    424481.703 CA 6 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:406 -E -f '%.3f' >>${OUT} <<EOF
 -116d15'00.000  33d20'30.000   2000000.000    427513.796 CA 6 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:407 -E -f '%.3f' >>${OUT} <<EOF
 -118d20'00.000  34d30'00.000   4186692.580   4294365.712 CA 7 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:502 -E -f '%.3f' >>${OUT} <<EOF
 -105d30'00.000  39d15'00.000   2000000.000    515936.228 CO C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:502 -E -f '%.3f' >>${OUT} <<EOF
 -105d30'00.000  39d15'30.000   2000000.000    518971.313 CO C GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:501 -E -f '%.3f' >>${OUT} <<EOF
 -108d45'55.378  40d25'33.504   1091086.832    414752.176 CO N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:501 -E -f '%.3f' >>${OUT} <<EOF
 -105d14'45.588  40d12'42.711   2070940.652    320120.166 CO N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:503 -E -f '%.3f' >>${OUT} <<EOF
 -105d30'00.000  37d30'00.000   2000000.000    303425.100 CO S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:503 -E -f '%.3f' >>${OUT} <<EOF
 -105d30'00.000  37d30'30.000   2000000.000    306459.335 CO S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:600 -E -f '%.3f' >>${OUT} <<EOF
  -72d43'30.515  41d16'55.847    606832.139    163540.219 CT   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:600 -E -f '%.3f' >>${OUT} <<EOF
  -73d01'15.609  41d13'25.985    525446.203    142415.891 CT   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:700 -E -f '%.3f' >>${OUT} <<EOF
  -75d33'00.748  39d21'15.214    462235.881    493228.846 DE   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:700 -E -f '%.3f' >>${OUT} <<EOF
  -75d19'01.889  39d45'14.765    527969.596    638870.822 DE   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:901 -E -f '%.3f' >>${OUT} <<EOF
  -80d11'00.000  25d45'00.000    768810.056    515637.939 FL E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:903 -E -f '%.3f' >>${OUT} <<EOF
  -82d45'52.412  29d39'06.589   2551254.254    241240.008 FL N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:903 -E -f '%.3f' >>${OUT} <<EOF
  -84d55'11.533  29d38'51.982   1866620.008    235814.655 FL N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:902 -E -f '%.3f' >>${OUT} <<EOF
  -82d38'00.000  27d47'00.000    295216.148   1254408.638 FL W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1001 -E -f '%.3f' >>${OUT} <<EOF
  -81d27'15.592  32d38'03.003    719287.314    958818.262 GA E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1001 -E -f '%.3f' >>${OUT} <<EOF
  -83d15'39.990  33d29'58.626    166361.311   1274706.363 GA E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1002 -E -f '%.3f' >>${OUT} <<EOF
  -84d23'00.000  33d45'00.000    434141.824   1364117.672 GA W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5400 -E -f '%.3f' >>${OUT} <<EOF
  144d44'55.503  13d28'20.879    164041.712    164041.680 GU   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5400 -E -f '%.3f' >>${OUT} <<EOF
  144d38'07.193  13d20'20.538    123728.401    115623.086 GU   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5101 -E -f '%.3f' >>${OUT} <<EOF
 -155d59'16.911  19d37'23.477    332050.939    287068.342 HI 1 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5101 -E -f '%.3f' >>${OUT} <<EOF
 -155d18'06.262  19d31'24.578    568270.061    250663.241 HI 1 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5101 -E -f '%.3f' >>${OUT} <<EOF
 -155d30'00.000  19d42'00.000    500000.000    314722.985 HI 1 GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5101 -E -f '%.3f' >>${OUT} <<EOF
 -155d30'00.000  19d42'30.000    500000.000    317749.315 HI 1 GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5102 -E -f '%.3f' >>${OUT} <<EOF
 -156d40'00.000  20d42'00.000    500000.000    133170.903 HI 2 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5102 -E -f '%.3f' >>${OUT} <<EOF
 -156d40'00.000  20d42'30.000    500000.000    136197.580 HI 2 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5103 -E -f '%.3f' >>${OUT} <<EOF
 -158d00'00.000  21d30'00.000    500000.000    121078.981 HI 3 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5103 -E -f '%.3f' >>${OUT} <<EOF
 -158d01'30.000  21d37'30.000    491508.215    166485.537 HI 3 GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5104 -E -f '%.3f' >>${OUT} <<EOF
 -159d30'00.000  22d05'00.000    500000.000     90816.138 HI 4 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5105 -E -f '%.3f' >>${OUT} <<EOF
 -160d10'00.000  21d42'00.000    500000.000     12108.532 HI 5 GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1401 -E -f '%.3f' >>${OUT} <<EOF
  -93d28'33.966  42d44'50.101   2006419.316    454523.076 IA N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1401 -E -f '%.3f' >>${OUT} <<EOF
  -93d54'22.084  42d40'23.699   1890779.351    427816.212 IA N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1402 -E -f '%.3f' >>${OUT} <<EOF
  -93d37'00.000  41d35'00.000   1968081.762    576880.709 IA S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1102 -E -f '%.3f' >>${OUT} <<EOF
 -114d24'00.000  42d56'00.000    392878.009    461838.231 ID C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1101 -E -f '%.3f' >>${OUT} <<EOF
 -111d42'29.824  43d48'07.616    621017.480    778569.749 ID E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1101 -E -f '%.3f' >>${OUT} <<EOF
 -112d22'35.516  43d35'26.260    444398.356    701217.958 ID E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1103 -E -f '%.3f' >>${OUT} <<EOF
 -116d22'02.592  48d07'50.941    349231.302   2357247.272 ID W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1201 -E -f '%.3f' >>${OUT} <<EOF
  -88d07'06.790  41d46'11.855    558591.507   1858801.531 IL E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1201 -E -f '%.3f' >>${OUT} <<EOF
  -88d41'35.208  40d43'37.202    400279.755   1478930.010 IL E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1202 -E -f '%.3f' >>${OUT} <<EOF
  -90d10'00.000  38d30'00.000    500000.000    667527.020 IL W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1301 -E -f '%.3f' >>${OUT} <<EOF
  -85d40'00.000  40d00'00.000    500000.000    910470.786 IN E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1301 -E -f '%.3f' >>${OUT} <<EOF
  -85d40'00.000  40d00'30.000    500000.000    913506.351 IN E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1301 -E -f '%.3f' >>${OUT} <<EOF
  -86d14'27.780  40d00'12.690    339087.973    912273.325 IN E GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1301 -E -f '%.3f' >>${OUT} <<EOF
  -86d14'27.790  40d00'31.660    339099.565    914192.836 IN E GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1301 -E -f '%.3f' >>${OUT} <<EOF
  -86d14'28.103  40d00'47.412    339085.485    915786.883 IN E GP6
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -87d05'00.000  40d00'00.000    500000.000    910470.786 IN W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -87d05'00.000  40d00'30.000    500000.000    913506.351 IN W GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -86d45'10.717  39d41'24.840    592969.921    797807.077 IN W GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -87d41'44.075  37d54'24.755    323351.583    148732.658 IN W GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -86d32'13.179  39d32'46.419    654071.692    745650.467 IN W GP5
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -87d25'26.675  38d26'17.646    402398.078    341828.410 IN W GP6
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -86d14'28.103  40d00'47.412    735905.989    916383.007 IN W GP7
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -86d14'27.780  40d00'12.690    735964.329    912869.812 IN W GP8
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1302 -E -f '%.3f' >>${OUT} <<EOF
  -86d14'27.790  40d00'31.660    735945.409    914789.331 IN W GP9
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1501 -E -f '%.3f' >>${OUT} <<EOF
  -96d47'54.567  38d58'52.096   2341555.463    238196.375 KS N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1501 -E -f '%.3f' >>${OUT} <<EOF
  -98d35'23.954  39d58'41.967   1834645.786    599682.614 KS N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1502 -E -f '%.3f' >>${OUT} <<EOF
  -97d21'00.000  37d42'00.000   2332714.529    378302.303 KS S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1601 -E -f '%.3f' >>${OUT} <<EOF
  -84d05'43.283  38d14'35.963   2044414.776    270720.831 KY N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1601 -E -f '%.3f' >>${OUT} <<EOF
  -84d26'49.265  39d04'03.099   1944057.054    570906.807 KY N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1602 -E -f '%.3f' >>${OUT} <<EOF
  -86d05'00.000  37d10'00.000   1902871.440    303569.007 KY S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1701 -E -f '%.3f' >>${OUT} <<EOF
  -91d34'46.483  31d57'26.243   2285456.159    470671.781 LA N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1701 -E -f '%.3f' >>${OUT} <<EOF
  -92d52'46.615  32d54'52.264   1883486.181    817905.853 LA N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1701 -E -f '%.3f' >>${OUT} <<EOF
  -91d29'09.480  31d56'44.721   2314527.078    466735.568 LA N GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1701 -E -f '%.3f' >>${OUT} <<EOF
  -93d59'38.241  32d48'43.467   1540965.776    783590.902 LA N GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d00'00.000  28d50'00.000   2747176.527     68218.410 LA S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d30'00.000  28d50'00.000   2587082.796     65307.429 LA S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d29'59.999  29d19'59.994   2584173.994    247106.020 LA S GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d00'00.004  29d19'59.998   2743474.038    250002.972 LA S GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d10'23.487  29d20'32.615   2688234.966    252215.035 LA S GP5
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d06'34.632  29d15'19.642   2709099.980    220994.973 LA S GP6
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d01'33.803  29d07'47.918   2736661.987    175901.967 LA S GP7
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1702 -E -f '%.3f' >>${OUT} <<EOF
  -89d08'45.781  28d58'27.979   2699434.976    118600.021 LA S GP9
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2002 -E -f '%.3f' >>${OUT} <<EOF
  -70d30'00.000  41d30'00.000    200000.000    182180.613 MA I GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2001 -E -f '%.3f' >>${OUT} <<EOF
  -70d27'00.716  41d40'15.808    886823.958    246295.510 MA M GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2001 -E -f '%.3f' >>${OUT} <<EOF
  -73d25'59.173  42d06'06.860     75432.106    407473.253 MA M GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1900 -E -f '%.3f' >>${OUT} <<EOF
  -76d11'27.492  39d12'06.132   1029272.677    499353.154 MD   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1900 -E -f '%.3f' >>${OUT} <<EOF
  -77d02'30.406  38d26'37.492    788033.549    222300.512 MD   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1900 -E -f '%.3f' >>${OUT} <<EOF
  -77d30'10.460  38d59'25.903    657055.715    421819.661 MD   GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1801 -E -f '%.3f' >>${OUT} <<EOF
  -68d24'25.489  46d32'46.920    523379.868    989125.403 ME E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1801 -E -f '%.3f' >>${OUT} <<EOF
  -68d37'29.366  47d02'12.659    468876.638   1168006.571 ME E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:1802 -E -f '%.3f' >>${OUT} <<EOF
  -70d16'00.000  43d40'00.000    473538.933    303746.300 ME W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2112 -E -f '%.3f' >>${OUT} <<EOF
  -85d40'00.000  44d45'00.000   1653612.784    525406.529 MI C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2113 -E -f '%.3f' >>${OUT} <<EOF
  -83d29'17.919  42d19'19.299   2228532.810    300724.433 MI S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2113 -E -f '%.3f' >>${OUT} <<EOF
  -83d35'24.656  42d20'02.682   2200944.119    304856.048 MI S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2113 -E -f '%.3f' >>${OUT} <<EOF
  -85d55'26.569  41d50'10.236   1566471.427    126614.633 MI S GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2113 -E -f '%.3f' >>${OUT} <<EOF
  -85d45'59.490  41d49'22.346   1609315.113    120996.336 MI S GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2103 -E -f '%.3f' >>${OUT} <<EOF
  -89d20'00.000  46d50'00.000    353999.488   1944621.410 MI W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2201 -E -f '%.3f' >>${OUT} <<EOF
  -91d27'51.183  47d08'19.177   2407087.310    237254.364 MN N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2201 -E -f '%.3f' >>${OUT} <<EOF
  -95d51'05.998  48d19'26.552   1330690.998    677229.560 MN N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2402 -E -f '%.3f' >>${OUT} <<EOF
  -92d30'00.000  38d15'00.000    500000.000    879833.618 MO C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2402 -E -f '%.3f' >>${OUT} <<EOF
  -92d30'00.000  38d15'30.000    500000.000    882868.158 MO C GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2401 -E -f '%.3f' >>${OUT} <<EOF
  -91d42'04.297  37d22'05.932    150919.587    561018.127 MO E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2401 -E -f '%.3f' >>${OUT} <<EOF
  -90d08'08.896  36d53'44.124    606497.861    386893.306 MO E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2403 -E -f '%.3f' >>${OUT} <<EOF
  -94d30'00.000  38d15'00.000    500000.000    758504.732 MO W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2403 -E -f '%.3f' >>${OUT} <<EOF
  -94d30'00.000  38d15'30.000    500000.000    761539.296 MO W GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2301 -E -f '%.3f' >>${OUT} <<EOF
  -89d10'14.013  30d30'51.338    393805.810    308399.629 MS E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2301 -E -f '%.3f' >>${OUT} <<EOF
  -88d26'04.338  30d43'01.454    625321.316    382224.788 MS E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2302 -E -f '%.3f' >>${OUT} <<EOF
  -90d10'00.000  32d17'00.000    551507.962    648697.041 MS W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2502 -E -f '%.3f' >>${OUT} <<EOF
 -109d25'00.000  47d05'00.000   2020760.609    455889.692 MT C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2501 -E -f '%.3f' >>${OUT} <<EOF
 -106d29'11.521  47d52'21.103   2739443.845    332808.759 MT N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2501 -E -f '%.3f' >>${OUT} <<EOF
 -114d30'43.122  48d52'46.764    794693.447    725072.329 MT N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2503 -E -f '%.3f' >>${OUT} <<EOF
 -109d15'00.000  45d39'00.000   2063931.561    601700.560 MT S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3200 -E -f '%.3f' >>${OUT} <<EOF
  -81d12'31.790  35d09'31.049   1339854.041    519988.737 NC   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3200 -E -f '%.3f' >>${OUT} <<EOF
  -76d31'54.918  35d33'51.452   2733941.071    669408.798 NC   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3200 -E -f '%.3f' >>${OUT} <<EOF
  -78d28'26.580  36d15'15.480   2155088.262    911860.343 NC   GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3301 -E -f '%.3f' >>${OUT} <<EOF
  -98d46'03.232  48d08'13.483   2422983.823    419525.823 ND N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3301 -E -f '%.3f' >>${OUT} <<EOF
 -101d18'21.456  47d39'18.935   1801367.700    240053.997 ND N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3302 -E -f '%.3f' >>${OUT} <<EOF
 -100d46'00.000  46d48'00.000   1933213.911    413422.204 ND S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2601 -E -f '%.3f' >>${OUT} <<EOF
  -96d17'52.930  42d04'48.305   3004688.243    293978.208 NE N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2601 -E -f '%.3f' >>${OUT} <<EOF
 -100d49'26.949  41d58'54.025   1775916.042    237340.591 NE N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2602 -E -f '%.3f' >>${OUT} <<EOF
  -96d43'00.000  40d49'00.000   2770252.364    431225.617 NE S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2800 -E -f '%.3f' >>${OUT} <<EOF
  -70d56'11.287  43d08'15.006    694907.496    233185.793 NH   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2800 -E -f '%.3f' >>${OUT} <<EOF
  -72d32'32.197  42d51'25.984    265213.564    131404.574 NH   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2900 -E -f '%.3f' >>${OUT} <<EOF
  -74d13'55.737  39d52'02.095   2121971.499    376878.657 NJ   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2900 -E -f '%.3f' >>${OUT} <<EOF
  -74d51'24.058  41d12'07.401   1947709.569    862915.876 NJ   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3002 -E -f '%.3f' >>${OUT} <<EOF
 -106d15'00.000  33d30'00.000    500000.000    909419.295 NM C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3002 -E -f '%.3f' >>${OUT} <<EOF
 -106d15'00.000  33d30'30.000    500000.000    912451.306 NM C GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3001 -E -f '%.3f' >>${OUT} <<EOF
 -104d11'42.410  33d17'21.732    542236.924    832820.301 NM E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3001 -E -f '%.3f' >>${OUT} <<EOF
 -104d47'37.948  33d22'32.349    359406.535    864495.732 NM E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3003 -E -f '%.3f' >>${OUT} <<EOF
 -107d50'00.000  32d30'00.000    500000.000    545616.897 NM W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3003 -E -f '%.3f' >>${OUT} <<EOF
 -107d50'00.000  32d30'30.000    500000.000    548648.466 NM W GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2702 -E -f '%.3f' >>${OUT} <<EOF
 -116d48'00.000  36d58'00.000    461048.286    806858.042 NV C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2701 -E -f '%.3f' >>${OUT} <<EOF
 -114d49'09.337  35d43'09.299    726805.224    353637.053 NV E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2701 -E -f '%.3f' >>${OUT} <<EOF
 -116d50'32.766  41d30'37.869    155162.931   2464191.579 NV E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:2703 -E -f '%.3f' >>${OUT} <<EOF
 -119d49'00.000  39d32'00.000    152145.548   1743820.924 NV W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3102 -E -f '%.3f' >>${OUT} <<EOF
  -76d10'00.000  43d05'00.000    611313.134   1123706.621 NY C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3101 -E -f '%.3f' >>${OUT} <<EOF
  -74d02'53.671  42d17'01.775    577147.690    832219.885 NY E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3101 -E -f '%.3f' >>${OUT} <<EOF
  -74d44'39.818  42d30'07.382    389148.814    911884.889 NY E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3104 -E -f '%.3f' >>${OUT} <<EOF
  -73d02'36.247  40d47'50.624   2264860.626    209793.919 NY L GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3104 -E -f '%.3f' >>${OUT} <<EOF
  -74d06'58.125  40d36'07.281   1967746.807    137190.013 NY L GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3104 -E -f '%.3f' >>${OUT} <<EOF
  -74d00'00.000  40d45'00.000   2000000.000    191080.202 NY L GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3104 -E -f '%.3f' >>${OUT} <<EOF
  -73d15'00.000  40d37'30.000   2208197.581    146431.086 NY L GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3104 -E -f '%.3f' >>${OUT} <<EOF
  -73d22'30.000  40d45'00.000   2173173.707    191697.996 NY L GP5
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3103 -E -f '%.3f' >>${OUT} <<EOF
  -78d51'00.000  42d54'00.000    428547.567   1056727.674 NY W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3401 -E -f '%.3f' >>${OUT} <<EOF
  -80d49'28.238  40d17'50.894   2467363.986    234305.751 OH N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3401 -E -f '%.3f' >>${OUT} <<EOF
  -82d37'31.021  40d20'14.678   1965071.932    244391.910 OH N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3402 -E -f '%.3f' >>${OUT} <<EOF
  -84d15'00.000  39d45'00.000   1507970.925    642141.152 OH S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3501 -E -f '%.3f' >>${OUT} <<EOF
  -98d42'45.414  36d50'19.568   1791448.615    670119.442 OK N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3501 -E -f '%.3f' >>${OUT} <<EOF
  -95d38'44.046  35d20'36.925   2702176.218    133585.952 OK N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3502 -E -f '%.3f' >>${OUT} <<EOF
  -97d08'00.000  34d34'00.000   2260914.787    449942.599 OK S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3601 -E -f '%.3f' >>${OUT} <<EOF
 -123d41'00.000  45d31'00.000   1184216.898    690530.257 OR N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3602 -E -f '%.3f' >>${OUT} <<EOF
 -119d46'26.562  44d24'25.943   2189746.353    999672.239 OR S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3602 -E -f '%.3f' >>${OUT} <<EOF
 -121d09'56.105  44d23'08.924   1825970.576    991740.899 OR S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3701 -E -f '%.3f' >>${OUT} <<EOF
  -74d33'20.644  41d23'48.566   2876202.339    464358.775 PA N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3701 -E -f '%.3f' >>${OUT} <<EOF
  -78d09'48.121  40d51'35.455   1885652.438    252829.477 PA N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -67d08'56.930  18d29'56.972    251990.753    242253.319 PR F GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -66d52'30.000  18d15'00.000    346756.548    151479.295 PR F GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -66d26'00.000  18d15'00.000    500000.000    151294.491 PR F GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -66d26'00.000  18d30'00.000    500000.000    242074.012 PR F GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -67d08'56.930  18d29'56.972    251990.753    242253.319 PR M GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -66d52'30.000  18d15'00.000    346756.548    151479.295 PR M GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -66d26'00.000  18d15'00.000    500000.000    151294.491 PR M GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5201 -E -f '%.3f' >>${OUT} <<EOF
  -66d26'00.000  18d30'00.000    500000.000    242074.012 PR M GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5202 -E -f '%.3f' >>${OUT} <<EOF
  -64d43'00.000  17d40'00.000   1097602.972     42283.509 PS S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3800 -E -f '%.3f' >>${OUT} <<EOF
  -71d16'00.833  41d32'24.848    563817.074    166563.592 RI   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3800 -E -f '%.3f' >>${OUT} <<EOF
  -71d37'13.730  41d23'53.266    466943.554    114721.079 RI   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3901 -E -f '%.3f' >>${OUT} <<EOF
  -80d32'30.000  34d32'30.000   2138028.224    561330.721 SC N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3901 -E -f '%.3f' >>${OUT} <<EOF
  -81d00'00.000  34d32'30.000   2000000.000    561019.077 SC N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3902 -E -f '%.3f' >>${OUT} <<EOF
  -80d32'30.000  33d32'30.000   2139661.529    621836.603 SC S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:3902 -E -f '%.3f' >>${OUT} <<EOF
  -81d00'00.000  33d32'30.000   2000000.000    621532.356 SC S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4001 -E -f '%.3f' >>${OUT} <<EOF
  -99d12'21.983  44d06'08.121   2208566.880     99065.808 SD N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4001 -E -f '%.3f' >>${OUT} <<EOF
 -100d32'28.873  44d32'34.917   1858852.206    259207.243 SD N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4002 -E -f '%.3f' >>${OUT} <<EOF
 -103d14'00.000  44d06'00.000   1238344.555    657205.595 SD S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4100 -E -f '%.3f' >>${OUT} <<EOF
  -85d13'55.967  36d21'48.503   2226074.895    718522.870 TN   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4100 -E -f '%.3f' >>${OUT} <<EOF
  -88d43'05.849  36d30'08.410   1201097.659    779285.593 TN   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4203 -E -f '%.3f' >>${OUT} <<EOF
  -97d06'00.000  31d35'00.000   3006704.541    711708.204 TX C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4201 -E -f '%.3f' >>${OUT} <<EOF
 -100d33'06.303  34d39'35.684   2285173.373    241550.390 TX N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4201 -E -f '%.3f' >>${OUT} <<EOF
 -102d48'50.949  34d43'39.249   1605118.921    267430.718 TX N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4205 -E -f '%.3f' >>${OUT} <<EOF
  -97d30'00.000  25d55'00.000   2328727.194     92175.721 TX S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4202 -E -f '%.3f' >>${OUT} <<EOF
  -96d48'00.000  32d45'00.000   2215204.973    394833.169 TXNC GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4204 -E -f '%.3f' >>${OUT} <<EOF
  -98d30'00.000  29d25'00.000   2159176.237    576022.948 TXSC GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4302 -E -f '%.3f' >>${OUT} <<EOF
 -111d30'00.000  38d40'00.000   2000000.000    121415.345 UT C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4302 -E -f '%.3f' >>${OUT} <<EOF
 -111d30'00.000  38d40'30.000   2000000.000    124450.619 UT C GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4301 -E -f '%.3f' >>${OUT} <<EOF
 -111d30'00.000  41d30'00.000   2000000.000    425057.445 UT N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4301 -E -f '%.3f' >>${OUT} <<EOF
 -111d30'00.000  41d30'30.000   2000000.000    428093.810 UT N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4303 -E -f '%.3f' >>${OUT} <<EOF
 -109d48'37.967  38d29'30.877   2483568.472    668988.098 UT S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4303 -E -f '%.3f' >>${OUT} <<EOF
 -113d52'56.922  37d09'18.788   1305706.243    186731.606 UT S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4501 -E -f '%.3f' >>${OUT} <<EOF
  -77d13'46.945  38d55'12.407   2361415.621    458962.786 VA N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4501 -E -f '%.3f' >>${OUT} <<EOF
  -79d18'51.557  38d09'59.020   1765875.433    183017.881 VA N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4501 -E -f '%.3f' >>${OUT} <<EOF
  -77d38'10.823  37d49'23.964   2249484.834     58221.695 VA N GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4501 -E -f '%.3f' >>${OUT} <<EOF
  -79d26'19.475  37d47'25.852   1728704.621     46487.604 VA N GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4501 -E -f '%.3f' >>${OUT} <<EOF
  -77d44'30.336  39d00'06.804   2215488.016    487135.448 VA N GP6
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4501 -E -f '%.3f' >>${OUT} <<EOF
  -77d43'47.013  38d59'55.454   2218917.620    486015.701 VA N GP9
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4502 -E -f '%.3f' >>${OUT} <<EOF
  -78d30'00.000  37d30'00.000   2000000.000    424763.516 VA S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4502 -E -f '%.3f' >>${OUT} <<EOF
  -78d30'00.000  37d30'30.000   2000000.000    427797.710 VA S GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4502 -E -f '%.3f' >>${OUT} <<EOF
  -77d32'33.000  36d54'42.507   2279939.213    212030.192 VA S GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4502 -E -f '%.3f' >>${OUT} <<EOF
  -77d21'55.732  38d04'53.901   2326572.191    638519.064 VA S GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5202 -E -f '%.3f' >>${OUT} <<EOF
  -64d45'30.000  17d45'30.000   1082794.001     75432.552 VI F GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5202 -E -f '%.3f' >>${OUT} <<EOF
  -66d26'00.000  17d45'56.426    500000.000     75432.505 VI F GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5202 -E -f '%.3f' >>${OUT} <<EOF
  -64d45'30.000  17d45'30.000   1082794.001     75432.552 VI M GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:5202 -E -f '%.3f' >>${OUT} <<EOF
  -66d26'00.000  17d45'56.426    500000.000     75432.505 VI M GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4400 -E -f '%.3f' >>${OUT} <<EOF
  -72d29'31.418  43d09'58.526    502118.227    242816.621 VT   GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4400 -E -f '%.3f' >>${OUT} <<EOF
  -73d12'06.978  44d22'22.810    316451.963    683472.660 VT   GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4601 -E -f '%.3f' >>${OUT} <<EOF
 -119d51'37.006  47d50'51.069   2238927.196    310658.148 WA N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4601 -E -f '%.3f' >>${OUT} <<EOF
 -123d59'49.087  48d09'29.131   1228043.506    438306.777 WA N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4602 -E -f '%.3f' >>${OUT} <<EOF
 -122d54'00.000  46d09'00.000   1391814.257    307059.945 WA S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4802 -E -f '%.3f' >>${OUT} <<EOF
  -88d04'00.000  44d30'00.000   2504399.560    249042.105 WI C GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4801 -E -f '%.3f' >>${OUT} <<EOF
  -88d44'40.778  45d22'21.598   2322632.765     77666.151 WI N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4801 -E -f '%.3f' >>${OUT} <<EOF
  -92d12'19.275  45d48'35.812   1437681.450    242373.846 WI N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4803 -E -f '%.3f' >>${OUT} <<EOF
  -89d23'00.000  43d05'00.000   2164743.544    395445.420 WI S GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4701 -E -f '%.3f' >>${OUT} <<EOF
  -77d53'39.269  39d14'39.339   2454764.840    275139.246 WV N GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4701 -E -f '%.3f' >>${OUT} <<EOF
  -81d33'23.549  39d18'08.535   1418073.862    298900.611 WV N GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4701 -E -f '%.3f' >>${OUT} <<EOF
  -77d30'10.460  38d59'25.903   2567632.286    184970.946 WV N GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4901 -E -f '%.3f' >>${OUT} <<EOF
 -105d07'00.000  44d38'00.000    513016.009   1445570.355 WY E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4901 -E -f '%.3f' >>${OUT} <<EOF
 -105d31'02.882  43d30'40.600    406937.677   1036750.418 WY E GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4901 -E -f '%.3f' >>${OUT} <<EOF
 -105d22'42.856  43d30'14.685    443778.141   1034002.062 WY E GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4901 -E -f '%.3f' >>${OUT} <<EOF
 -105d28'42.827  43d36'33.391    417392.389   1072428.186 WY E GP3
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4901 -E -f '%.3f' >>${OUT} <<EOF
 -105d23'43.223  42d00'59.422    437860.186    491889.060 WY E GP4
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4901 -E -f '%.3f' >>${OUT} <<EOF
 -104d35'06.686  42d34'50.366    656606.905    697923.643 WY E GP5
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4904 -E -f '%.3f' >>${OUT} <<EOF
 -110d36'00.000  41d48'00.000    359125.204    413338.815 WY W GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4902 -E -f '%.3f' >>${OUT} <<EOF
 -106d13'03.224  41d36'14.640    805153.891    343496.746 WYEC GP1
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4902 -E -f '%.3f' >>${OUT} <<EOF
 -108d01'56.720  41d51'57.518    309581.204    437731.262 WYEC GP2
EOF
$EXE +units=us-ft +init=${INIT_FILE}:4903 -E -f '%.3f' >>${OUT} <<EOF
 -108d24'00.000  43d02'00.000    593579.361    862553.590 WYWC GP1
EOF
#
# do 'diff' with distribution results
echo "diff ${OUT} with pj_out27.dist"
diff -u -b ${OUT} ${TEST_CLI_DIR}/pj_out27.dist
if [ $? -ne 0 ] ; then
	echo  ""
	echo "PROBLEMS HAVE OCCURRED"
	echo "test file ${OUT} saved"
    echo
	exit 100
else
	echo "TEST OK"
	echo "test file ${OUT} removed"
    echo
	/bin/rm -f ${OUT}
	exit 0
fi
