:
# Script to do some testing of various transformation that do not depend
# on datum files.
#
#
TEST_CLI_DIR=`dirname $0`
EXE=$1

usage()
{
    echo "Usage: ${0} <path to 'cs2cs' program>"
    echo
    exit 1
}

if test -z "${EXE}"; then
    EXE=../../src/cs2cs
fi

if test ! -x ${EXE}; then
    echo "*** ERROR: Can not find '${EXE}' program!"
    exit 1
fi

if test -z "${PROJ_LIB}"; then
    export PROJ_LIB="`dirname $0`/../../data"
fi

# Would be great to have a universale way of selecting a locale with
# a decimal separator that is not '.'
if command locale >/dev/null 2>/dev/null; then
    if test `locale -a | grep fr_FR.utf8`; then
        echo "Using locale with comma as decimal separator"
        export LC_ALL=fr_FR.UTF-8
        export PROJ_USE_ENV_LOCALE=1
    fi
fi

echo "============================================"
echo "Running ${0} using ${EXE}:"
echo "============================================"

OUT=tv_out

#
echo "doing tests into file ${OUT}, please wait"
rm -f ${OUT}
#
echo "##############################################################" >> ${OUT}
echo Test raw ellipse to raw ellipse >> ${OUT}
#
$EXE +proj=latlong +ellps=clrk66 \
 +to +proj=latlong +ellps=bessel \
 -E >>${OUT} <<EOF
79d58'00.000"W 37d02'00.000"N 0.0
79d58'00.000"W 36d58'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo Test NAD27 to raw ellipse >> ${OUT}
#
$EXE +proj=latlong +datum=NAD27 \
 +to +proj=latlong +ellps=bessel \
 -E >>${OUT} <<EOF
79d00'00.000"W 35d00'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo Between two 3parameter approximations on same ellipsoid >> ${OUT}
#
$EXE +proj=latlong +ellps=bessel +towgs84=5,0,0 \
 +to +proj=latlong +ellps=bessel +towgs84=1,0,0 \
 -E >>${OUT} <<EOF
0d00'00.000"W 0d00'00.000"N 0.0
79d00'00.000"W 45d00'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo 3param to raw ellipsoid on same ellipsoid >> ${OUT}
#
$EXE +proj=latlong +ellps=bessel +towgs84=5,0,0 \
 +to +proj=latlong +ellps=bessel  \
 -E >>${OUT} <<EOF
0d00'00.000"W 0d00'00.000"N 0.0
79d00'00.000"W 45d00'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo Test simple prime meridian handling. >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 +pm=greenwich  \
 +to +proj=latlong +datum=WGS84 +pm=1 \
 -E >>${OUT} <<EOF
0d00'00.000"W 0d00'00.000"N 0.0
79d00'00.000"W 45d00'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo Test support for the lon_wrap switch. >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84  \
 +to +proj=latlong +datum=WGS84 +lon_wrap=180 \
 -E >>${OUT} <<EOF
1d00'00.000"W 10d00'00.000"N 0.0
0d00'00.000"W 10d00'00.000"N 0.0
0d00'00.000"E 10d00'00.000"N 0.0
1d00'00.000"E 45d00'00.000"N 0.0
179d00'00.000"E 45d00'00.000"N 0.0
181d00'00.000"E 45d00'00.000"N 0.0
350d00'00.000"E 45d00'00.000"N 0.0
370d00'00.000"E 45d00'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo Test simple prime meridian handling within a projection. >> ${OUT}
#
$EXE +proj=utm +zone=11 +datum=WGS84 +pm=3 \
 +to +proj=latlong +datum=WGS84 +pm=1w \
 -E >>${OUT} <<EOF
500000 3000000
EOF
echo "##############################################################" >> ${OUT}
echo Test geocentric x/y/z generation. >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84  \
 +to +proj=geocent +datum=WGS84  \
 -E >>${OUT} <<EOF
0d00'00.001"W 0d00'00.001"N 0.0
0d00'00.001"W 0d00'00.001"N 10.0
79d00'00.000"W 45d00'00.000"N 0.0
45d00'00.000"W 89d59'59.990"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo Test geocentric x/y/z consumption. >> ${OUT}
#
$EXE +proj=geocent +datum=WGS84  \
 +to +proj=latlong +datum=WGS84  \
 -E >>${OUT} <<EOF
6378137.00      -0.00 0.00
6378147.00      -0.00 0.00
861996.98       -4434590.01 4487348.41
0.00    -0.00 6356752.31
EOF
#
echo "#############################################################" >> ${OUT}
echo Test conversion from geodetic latlong to geocentric latlong >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=latlong +datum=WGS84 +geoc  \
 -E >>${OUT} <<EOF
0d00'00.000"W 0d00'00.000"N 0.0
79d00'00.000"W 45d00'00.000"N 0.0
12d00'00.000"W 45d00'00.000"N 0.0
0d00'00.000"W 90d00'00.000"N 0.0
EOF
#
echo "#############################################################" >> ${OUT}
echo Test conversion from geocentric latlong to geodetic latlong >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 +geoc \
 +to +proj=latlong +datum=WGS84  \
 -E >>${OUT} <<EOF
0d00'00.000"W 0d00'00.000"N 0.0
79d00'00.000"W 44d48'27.276"N 0.000
12d00'00.000"W 44d48'27.276"N 0.0
0d00'00.000"W 90d00'00.000"N 0.0
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test stere projection (re: win32 ticket 12)" >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=stere +lat_0=90 +lon_0=0 +lat_ts=70 +datum=WGS84 \
 -E >>${OUT} <<EOF
105 40
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test stere without lat_ts (#147)" >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=stere +lat_0=40 +lon_0=10  +datum=WGS84 \
 -E >>${OUT} <<EOF
20 45
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test sts projection (re: ticket 12)" >> ${OUT}
#
$EXE +proj=latlong +ellps=WGS84 \
 +to +proj=kav5 +ellps=WGS84 +units=m \
 -E >>${OUT} <<EOF
4.897000 52.371000
EOF
$EXE +proj=kav5 +ellps=WGS84 +units=m \
 +to +proj=latlong +ellps=WGS84 \
 -E >>${OUT} <<EOF
383646.088858 5997047.888175
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test RSO Borneo projection (re: ticket 62)" >> ${OUT}
#
$EXE +proj=latlong +a=6377298.556 +rf=300.8017 \
 +to +proj=omerc +a=6377298.556 +rf=300.8017 +lat_0=4 +lonc=115 \
                 +alpha=53d18\'56.9537 +gamma=53d7\'48.3685  +k_0=0.99984 \
                 +x_0=590476.87 +y_0=442857.65 \
 -E >>${OUT} <<EOF
116d2'11.12630 5d54'19.90183
EOF
echo "##############################################################" >> ${OUT}
echo "Test extended transverse mercator (#97)" >> ${OUT}
#
$EXE +proj=etmerc +k=0.998 +lon_0=-20 +datum=WGS84 +x_0=10000 +y_0=20000 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
10000 20000
500000 2000000
1000000 2000000
2000000 2000000
4000000 2000000
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test extended transverse mercator inverse (#97)" >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=etmerc +k=0.998 +lon_0=-20 +datum=WGS84 +x_0=10000 +y_0=20000 \
 -E >>${OUT} <<EOF
0dN 0.000
15d22'16.108"W	17d52'53.478"N 0.000
10d40'55.532"W	17d42'48.526"N 0.000
1d32'21.33"W	17d3'47.233"N 0.000
15d4'42.357"E	14d48'56.372"N 0.000
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test transverse mercator (#97)" >> ${OUT}
#
$EXE +proj=tmerc +approx +k=0.998 +lon_0=-20 +datum=WGS84 +x_0=10000 +y_0=20000 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
10000 20000
500000 2000000
1000000 2000000
2000000 2000000
4000000 2000000
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test transverse mercator inverse (#97)" >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=tmerc +approx +k=0.998 +lon_0=-20 +datum=WGS84 +x_0=10000 +y_0=20000 \
 -E >>${OUT} <<EOF
0dN 0.000
15d22'16.108"W	17d52'53.478"N 0.000
10d40'55.532"W	17d42'48.526"N 0.000
1d32'21.33"W	17d3'47.233"N 0.000
15d4'42.357"E	14d48'56.372"N 0.000
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test robinson projection (#113)" >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=robin +datum=WGS84 \
 -E >>${OUT} <<EOF
-30 40
-35 45
20 40
EOF
$EXE +proj=robin +datum=WGS84 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
-2612095.95     4276351.58 0.00
-2963455.42     4805073.65 0.00
1741397.30      4276351.58 0.00
EOF
echo "##############################################################" >> ${OUT}
echo "Test hammer projection (pull request #329)" >> ${OUT}
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=hammer  +datum=WGS84 \
 -E >>${OUT} <<EOF
-30 40
-35 45
20 40
EOF
$EXE +proj=hammer  +datum=WGS84 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
-2711575.08	4395506.62 0.00
-2964412.70	4929091.33 0.00
1811748.54	4377349.50 0.00
EOF
echo "##############################################################" >> ${OUT}
echo "Test healpix forward projection on sphere" >> ${OUT}
$EXE +proj=latlong +R=1 +lon_0=0 \
 +to +proj=healpix +R=1 +lon_0=0  -f '%.'5'f' \
 -E >>${OUT} <<EOF
0 41.81031
-90 0
EOF
$EXE +proj=latlong +R=5 \
 +to +proj=healpix +R=5 -f '%.'5'f' \
 -E >>${OUT} <<EOF
0 0
0 41.810314895778596
0 -41.810314895778596
90.0 0
-90.0 0
-180 0
-180 90.0
-180 -90.0
0 60.0
0 -60.0
EOF
echo "Test healpix forward projection on ellipsoid" >> ${OUT}
$EXE +proj=latlong +a=1 +lon_0=0 +ellps=WGS84 \
 +to +proj=healpix +a=1 +lon_0=0 +ellps=WGS84 -f '%.'5'f' \
 -E >>${OUT} <<EOF
0 41.937853904844985
-90 0
EOF
$EXE +proj=latlong +a=5 +e=0.8 +r_a=4.3220011711888882\
 +to +proj=healpix +a=5 +e=0.8 +r_a=4.3220011711888882 -f '%.'5'f' \
 -E >>${OUT} <<EOF
0 0
0 41.810314895778596
0 -41.810314895778596
90.0 0
-90.0 0
-180 0
-180 90.0
-180 -90.0
0 60.0
0 -60.0
EOF
echo "Test healpix inverse projection on ellipsoid" >> ${OUT}
$EXE +proj=latlong +a=1 +lon_0=0 +ellps=WGS84 \
 +to +proj=healpix +a=1 +lon_0=0 +ellps=WGS84 -f '%.'5'f' -I\
 -E >>${OUT} <<EOF
0 0.7853981633974483
-1.5707963267948966 0
EOF
$EXE +proj=latlong +a=5 +e=0.8 +r_a=4.3220011711888882\
 +to +proj=healpix +a=5 +e=0.8 +r_a=4.3220011711888882 -f '%.'5'f' -I\
 -E >>${OUT} <<EOF
0.0 0.0
0.0 2.0547874222147415
0.0 -2.0547874222147415
6.788983564106746 0.0
-6.788983564106746 0.0
-13.577967128213492 0.0
-10.183475346160119 6.788983564106746
-10.183475346160119 -6.788983564106746
0.0 3.351278550178025
0.0 -3.351278550178025
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix forward projection on sphere north=0 south=0" >> ${OUT}
$EXE +proj=latlong +R=5 \
 +to +proj=rhealpix +R=5 +north_square=0 +south_square=0 -f '%.'5'f' \
  -E >>${OUT} <<EOF
-180 30.0
-180 -25.714285714285715
0 0
60.0 41.809314895778598
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix forward projection on sphere north=1 south=1" >> ${OUT}
$EXE +proj=latlong +R=5 \
 +to +proj=rhealpix +R=5 +north_square=1 +south_square=1 -f '%.'5'f' \
  -E >>${OUT} <<EOF
-180 30.0
-180 -25.714285714285715
0 0
60.0 41.809314895778598
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix inverse projection on sphere north=0 south=0" >> ${OUT}
$EXE +proj=latlong +R=5 \
 +to +proj=rhealpix +R=5 +north_square=0 +south_square=0 -f '%.'5'f' -I\
  -E >>${OUT} <<EOF
0.0 0.0
0.0 3.9269908169872414
0.0 -3.9269908169872414
7.853981633974483 0.0
-7.853981633974483 0.0
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix inverse projection on sphere north=1 south=1" >> ${OUT}
$EXE +proj=latlong +R=5 \
 +to +proj=rhealpix +R=5 +north_square=1 +south_square=1 -f '%.'5'f' -I\
  -E >>${OUT} <<EOF
0.0 0.0
0.0 3.9269908169872414
0.0 -3.9269908169872414
7.853981633974483 0.0
-7.853981633974483 0.0
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix forward projection on ellipsoid north=0 south=0" >> ${OUT}
$EXE +proj=latlong +a=5 +e=0.8 +r_a=4.3220011711888882\
 +to +proj=rhealpix +a=5 +e=0.8 +r_a=4.3220011711888882 +north_square=0 +south_square=0 -f '%.'5'f' \
  -E >>${OUT} <<EOF
0 0
0 41.810314895778596
0 -41.810314895778596
90.0 0
-90.0 0
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix forward projection on ellipsoid north=1 south=1" >> ${OUT}
$EXE +proj=latlong +a=5 +e=0.8 +r_a=4.3220011711888882\
 +to +proj=rhealpix +a=5 +e=0.8 +r_a=4.3220011711888882 +north_square=1 +south_square=1 -f '%.'5'f' \
  -E >>${OUT} <<EOF
0 0
0 41.810314895778596
0 -41.810314895778596
90.0 0
-90.0 0
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix inverse projection on ellipsoid north=0 south=0" >> ${OUT}
$EXE +proj=latlong +a=5 +e=0.8 +r_a=4.3220011711888882\
 +to +proj=rhealpix +a=5 -I +e=0.8 +r_a=4.3220011711888882 +north_square=0 +south_square=0 -f '%.'5'f'\
  -E >>${OUT} <<EOF
0.0 0.0
0.0 2.0547874222147415
0.0 -2.0547874222147415
6.788983564106746 0.0
-6.788983564106746 0.0
EOF

echo "##############################################################" >> ${OUT}
echo "Test rHEALPix inverse projection on ellipsoid north=1 south=1" >> ${OUT}
$EXE +proj=latlong +a=5 +e=0.8 +r_a=4.3220011711888882\
 +to +proj=rhealpix +a=5 -I +e=0.8 +r_a=4.3220011711888882 +north_square=1 +south_square=1 -f '%.'5'f'\
 -E >>${OUT} <<EOF
0.0 0.0
0.0 2.0547874222147415
0.0 -2.0547874222147415
6.788983564106746 0.0
-6.788983564106746 0.0
EOF

echo "##############################################################" >> ${OUT}
echo "Test geos projection" >> ${OUT}
echo "Test geos on a sphere" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=sphere -E >>${OUT} <<EOF
16d11'8" 58d35'31"
-43d11'47" -22d54'30"
18d25'26" -33d55'31"
47d58'42" 29d22'11"
EOF
echo "Test geos on a ellipsoid" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=WGS84 -E >>${OUT} <<EOF
16d11'8" 58d35'31"
-43d11'47" -22d54'30"
18d25'26" -33d55'31"
47d58'42" 29d22'11"
EOF
echo "Test inv geos on a sphere" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=sphere -I -E >>${OUT} <<EOF
849736.77 4960015.43
-3780930.93 -2326595.36
1608689.65 -3412115.56
3825202.59 2885980.79
EOF
echo "Test inv geos on a ellipsoid" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=WGS84 -I -E >>${OUT} <<EOF
852862.53 4945122.70
-3787026.57 -2314765.32
1612331.00 -3397031.37
3832522.65 2872185.29
EOF
echo "Test geos on a sphere with alternate sweep" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=sphere +sweep=x -E >>${OUT} <<EOF
16d11'8" 58d35'31"
-43d11'47" -22d54'30"
18d25'26" -33d55'31"
47d58'42" 29d22'11"
EOF
echo "Test geos on a ellipsoid with alternate sweep" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=WGS84 +sweep=x -E >>${OUT} <<EOF
16d11'8" 58d35'31"
-43d11'47" -22d54'30"
18d25'26" -33d55'31"
47d58'42" 29d22'11"
EOF
echo "Test inv geos on a sphere with alternate sweep" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=sphere +sweep=x -I -E >>${OUT} <<EOF
841586.28 4961396.21
-3772913.22 -2339604.71
1601377.77 -3415545.15
3812722.89 2902474.62
EOF
echo "Test inv geos on a ellipsoid with alternate sweep" >> ${OUT}
$EXE +proj=latlong +ellps=sphere \
 +to +proj=geos +h=35785831.0 +lon_0=0 +ellps=WGS84 +sweep=x -I -E >>${OUT} <<EOF
844731.03 4946509.59
-3779077.27 -2327750.87
1605067.15 -3400461.47
3820138.08 2888664.15
EOF
#
echo "##############################################################" >> ${OUT}
echo "Test the Natural Earth Projection" >> ${OUT}
$EXE +proj=latlong +a=6371008.7714 +b=6371008.7714 \
 +to +proj=natearth +a=6371008.7714 +b=6371008.7714 -f '%.'7'f' \
 -E >>${OUT} <<EOF
0.0 0.0 0 0.0 0.0
0.0 22.5 0 0.0 2525419.569383768
0.0 45.0 0 0.0 5052537.389973222
0.0 67.5 0 0.0 7400065.6562573705
0.0 90.0 0 0.0 9062062.394736718
45.0 0.0 0 4356790.016612169 0.0
45.0 22.5 0 4253309.544984069 2525419.569383768
45.0 45.0 0 3924521.5829515466 5052537.389973222
45.0 67.5 0 3354937.47115583 7400065.6562573705
45.0 90.0 0 2397978.2448443635 9062062.394736718
90.0 0.0 0 8713580.033224339 0.0
90.0 22.5 0 8506619.089968137 2525419.569383768
90.0 45.0 0 7849043.165903093 5052537.389973222
90.0 67.5 0 6709874.94231166 7400065.6562573705
90.0 90.0 0 4795956.489688727 9062062.394736718
135.0 0.0 0 1.3070370049836507E7 0.0
135.0 22.5 0 1.2759928634952208E7 2525419.569383768
135.0 45.0 0 1.177356474885464E7 5052537.389973222
135.0 67.5 0 1.0064812413467491E7 7400065.6562573705
135.0 90.0 0 7193934.734533091 9062062.394736718
180.0 0.0 0 1.7427160066448677E7 0.0
180.0 22.5 0 1.7013238179936275E7 2525419.569383768
180.0 45.0 0 1.5698086331806187E7 5052537.389973222
180.0 67.5 0 1.341974988462332E7 7400065.6562573705
180.0 90.0 0 9591912.979377454 9062062.394736718
EOF
echo "##############################################################" >> ${OUT}
echo "Test the Natural Earth II Projection" >> ${OUT}
$EXE +proj=latlong +a=6371008.7714 +b=6371008.7714 \
 +to +proj=natearth2 +a=6371008.7714 +b=6371008.7714 -f '%.'7'f' \
 -E >>${OUT} <<EOF
0.0 0.0 0 0.00000000 0.00000000
0.0 22.5 0 0.00000000 2531453.57080958
0.0 45.0 0 0.00000000 5051471.50086845
0.0 67.5 0 0.00000000 7395411.22478983
0.0 90.0 0 0.00000000 9073776.52662810
45.0 0.0 0 4239151.18200719 0.00000000
45.0 22.5 0 4138348.61904244 2531453.57080958
45.0 45.0 0 3830621.33880773 5051471.50086845
45.0 67.5 0 3158326.32836996 7395411.22478983
45.0 90.0 0 957973.37034235 9073776.52662810
90.0 0.0 0 8478302.36401439 0.00000000
90.0 22.5 0 8276697.23808488 2531453.57080958
90.0 45.0 0 7661242.67761547 5051471.50086845
90.0 67.5 0 6316652.65673992 7395411.22478983
90.0 90.0 0 1915946.74068470 9073776.52662810
135.0 0.0 0 12717453.54602160 0.00000000
135.0 22.5 0 12415045.85712730 2531453.57080958
135.0 45.0 0 11491864.01642320 5051471.50086845
135.0 67.5 0 9474978.98510988 7395411.22478983
135.0 90.0 0 2873920.11102705 9073776.52662810
180.0 0.0 0 16956604.72802880 0.00000000
180.0 22.5 0 16553394.47616980 2531453.57080958
180.0 45.0 0 15322485.35523090 5051471.50086845
180.0 67.5 0 12633305.31347990 7395411.22478983
180.0 90.0 0 3831893.48136940 9073776.52662810
EOF
echo "##############################################################" >> ${OUT}
echo "Test the Compact Miller projection" >> ${OUT}
$EXE +proj=latlong +a=6371008.7714 +b=6371008.7714 \
 +to +proj=comill +a=6371008.7714 +b=6371008.7714 -f '%.'7'f' \
 -E >>${OUT} <<EOF
0.0 0.0 0 0.0 0.0
0.0 22.5 0 0.0 2537439.6610749415
0.0 45.0 0 0.0 5391682.432264133
0.0 67.5 0 0.0 8661480.510260897
0.0 90.0 0 0.0 12009484.264916677
45.0 0.0 0 5003778.588046594 0.0
45.0 22.5 0 5003778.588046594 2537439.6610749415
45.0 45.0 0 5003778.588046594 5391682.432264133
45.0 67.5 0 5003778.588046594 8661480.510260897
45.0 90.0 0 5003778.588046594 12009484.264916677
90.0 0.0 0 10007557.176093187 0.0
90.0 22.5 0 10007557.176093187 2537439.6610749415
90.0 45.0 0 10007557.176093187 5391682.432264133
90.0 67.5 0 10007557.176093187 8661480.510260897
90.0 90.0 0 10007557.176093187 12009484.264916677
135.0 0.0 0 15011335.76413978 0.0
135.0 22.5 0 15011335.76413978 2537439.6610749415
135.0 45.0 0 15011335.76413978 5391682.432264133
135.0 67.5 0 15011335.76413978 8661480.510260897
135.0 90.0 0 15011335.76413978 12009484.264916677
180.0 0.0 0 20015114.352186374 0.0
180.0 22.5 0 20015114.352186374 2537439.6610749415
180.0 45.0 0 20015114.352186374 5391682.432264133
180.0 67.5 0 20015114.352186374 8661480.510260897
180.0 90.0 0 20015114.352186374 12009484.264916677
EOF
echo "##############################################################" >> ${OUT}
echo "Test pconic (#148)" >> ${OUT}
#
$EXE +proj=latlong +datum=WGS84 \
 +to +proj=pconic  +units=m +lat_1=20n +lat_2=60n +lon_0=60W +datum=WGS84 \
 -E >>${OUT} <<EOF
-70.4 -23.65
EOF
$EXE +proj=pconic  +units=m +lat_1=20n +lat_2=60n +lon_0=60W +datum=WGS84 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
-2240096.40 -6940342.15
EOF
echo "##############################################################" >> ${OUT}
echo "Test laea" >> ${OUT}
#
$EXE -f '%.12f' \
     +proj=laea +lat_0=45 +lon_0=-100 +units=m +datum=WGS84 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
-6086629.0 4488761.0
EOF
echo "##############################################################" >> ${OUT}
echo "Test forward calcofi projection" >> ${OUT}
$EXE +proj=latlong +ellps=clrk66 \
 +to +proj=calcofi +ellps=clrk66 \
 -E >>${OUT} <<EOF
120d40'42.273"W	38d56'50.766"N
121d9'W	34d9'N
123d59'56.066"W	30d25'4.617"N
EOF
echo "Test inverse calcofi projection" >> ${OUT}
$EXE +proj=calcofi +ellps=clrk66 \
 +to +proj=longlat +ellps=clrk66 \
 -E >>${OUT} <<EOF
60 20
80 60
90 120
EOF
echo "##############################################################" >> ${OUT}
echo "Check inverse error handling with ob_tran (#225)" >> ${OUT}
$EXE +proj=ob_tran \
    +o_proj=moll +a=6378137 +es=0 +o_lon_p=0 +o_lat_p=0 +lon_0=180 \
 -E >>${OUT} <<EOF
300000 400000
20000000 30000000
EOF
echo "Test inverse handling" >> ${OUT}
$EXE -I +proj=ob_tran \
    +o_proj=moll +a=6378137 +es=0 +o_lon_p=0 +o_lat_p=0 +lon_0=180 \
 -E >>${OUT} <<EOF
10 20
EOF
echo "##############################################################" >> ${OUT}
echo "Test MGI datum gives expected results (#207)" >> ${OUT}
#
$EXE -f '%.7f' \
     +proj=latlong +datum=WGS84 \
 +to +init=epsg:31284 \
 -E >>${OUT} <<EOF
16.33 48.20
EOF
echo "##############################################################" >> ${OUT}
echo "Test omerc sensitivity with locations 90d from origin(#114)" >> ${OUT}
#
$EXE -f '%.8f' \
     +proj=latlong +ellps=WGS84 \
 +to +proj=omerc +ellps=WGS84 +lon_1=62.581150 +lat_1=74.856102 \
                 +lon_2=53.942810 +lat_2=74.905884 +units=km +no_rot \
 -E >>${OUT} <<EOF
56.958381652832 72.8798
56.9584 72.8798
EOF
echo "##############################################################" >> ${OUT}
echo "Test omerc differences between poles (#190)" >> ${OUT}
# First, north pole.
$EXE -f '%.3f' \
     +proj=latlong +ellps=WGS84 \
 +to +proj=omerc +ellps=WGS84 +datum=WGS84 +no_rot \
                 +lon_1=-27 +lat_1=70 +lon_2=-38 +lat_2=80 +lat_0=70 \
 -E >>${OUT} <<EOF
-27 70
-27 80
-27 89.9
163 89.9
163 80
EOF
# Again, for the south pole.
$EXE -f '%.3f' \
     +proj=latlong +ellps=WGS84 \
 +to +proj=omerc +ellps=WGS84 +datum=WGS84 +no_rot \
                 +lon_1=-27 +lat_1=-70 +lon_2=-38 +lat_2=-80 +lat_0=-70 \
 -E >>${OUT} <<EOF
-27 -70
-27 -80
-27 -89.9
163 -89.9
163 -80
EOF
echo "##############################################################" >> ${OUT}
echo "Test qsc" >> ${OUT}
#
$EXE -f '%.7f' \
     +proj=latlong +datum=WGS84 \
 +to +proj=qsc +datum=WGS84 \
 -E >>${OUT} <<EOF
13 -10
EOF
$EXE -f '%.13f' \
     +proj=qsc +datum=WGS84 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
2073986.94908809568733	-1680858.27222427958623
EOF
echo "##############################################################" >> ${OUT}
echo "Test bug 229" >> ${OUT}
#
$EXE -f '%.13f' \
     +init=epsg:4326 +proj=longlat +ellps=WGS84 +datum=WGS84 +towgs84=0,0,0 \
 +to +proj=latlong +datum=WGS84 \
 -E >>${OUT} <<EOF
13 -10
EOF
echo "##############################################################" >> ${OUT}
echo "Test bug 229 (2)" >> ${OUT}
#
$EXE -f '%.13f' \
     +init=epsg:4326 +to +init=epsg:4326 \
 -E >>${OUT} <<EOF
13 -10
EOF
echo "##############################################################" >> ${OUT}
echo "Test bug 244 " >> ${OUT}
#
$EXE -f '%.8f' \
     +init=epsg:4326 \
     +to +proj=aeqd +lon_0=130.0 +lat_0=40.0 +a=6378137 +b=6378137 +units=m \
 -E >>${OUT} <<EOF
-140.100000 -87.000000
EOF
echo "##############################################################" >> ${OUT}
echo "Test bug 244 (2)" >> ${OUT}
#
$EXE -f '%.11f' \
     +proj=aeqd +lon_0=130.0 +lat_0=40.0 +a=6378137 +b=6378137 +units=m \
     +to +init=epsg:4326 \
 -E >>${OUT} <<EOF
987122.418330284 -14429896.539530909
EOF
echo "##############################################################" >> ${OUT}
echo "Test bug 245 (use +datum=carthage)" >> ${OUT}
#
$EXE -f '%.7f' \
     +proj=longlat +datum=WGS84 +to +proj=utm +zone=32 +datum=carthage \
 -E >>${OUT} <<EOF
10 34
EOF
echo "##############################################################" >> ${OUT}
echo "Test bug 245 (use expansion of +datum=carthage)" >> ${OUT}
#
$EXE -f '%.7f' \
     +proj=longlat +datum=WGS84 +to +proj=utm +zone=32 +a=6378249.2 +b=6356515 +towgs84=-263.0,6.0,431.0 \
 -E >>${OUT} <<EOF
10 34
EOF
echo  "##############################################################" >> ${OUT}
echo  "Test SCH forward projection" >> ${OUT}
#
$EXE -f '%.7f' \
    +proj=latlong +datum=WGS84 +to +proj=sch +datum=WGS84 +plat_0=30.0 +plon_0=45.0 \
    +phdg_0=-12.0 +nodefs \
 -E >> ${OUT} <<EOF
0.0 0.0
0.0 90.0
45.0 45.0
45.1 44.9
44.9 45.1
30.0 45.0
EOF
echo  "##############################################################" >> ${OUT}
echo  "Test SCH inverse projection" >> ${OUT}
#
$EXE -f '%.6f' \
    +proj=sch +datum=WGS84 +plat_0=30.0 +plon_0=45.0 +phdg_0=-12.0 +nodefs +to \
    +proj=latlong +datum=WGS84 \
 -E >> ${OUT} <<EOF
0. 0. 2.
0. 1000.
1000. 0.
1000. 1000.
EOF
##############################################################################
echo "##############################################################" >> ${OUT}
echo "Test issue #316 (switch utm to use etmerc)" >> ${OUT}
#
$EXE -f '%.6f' \
     +proj=latlong +datum=WGS84 +to +proj=utm +zone=35 +datum=WGS84 \
 -E >>${OUT} <<EOF
0 83
EOF
##############################################################################
echo "##############################################################" >> ${OUT}
echo "Test issue #316 (switch utm to use etmerc)" >> ${OUT}
#
$EXE -f '%.6f' \
     +proj=latlong +datum=WGS84 +to +proj=etmerc +datum=WGS84 +k=0.9996 +lon_0=27 +x_0=500000 -f %.6f \
 -E >>${OUT} <<EOF
0 83
EOF
##############################################################################
echo  "##############################################################" >> ${OUT}
echo  "Test nzmg forward projection" >> ${OUT}
#
$EXE -f '%.7f' \
    +proj=latlong +datum=WGS84 +to \
    +proj=nzmg +lat_0=-41 +lon_0=173 +x_0=2510000 +y_0=6023150 +ellps=WGS84 +units=m \
 -E >> ${OUT} <<EOF
175. -40. 0.
EOF
##############################################################################
echo  "##############################################################" >> ${OUT}
echo  "Test nzmg inverse projection" >> ${OUT}
#
$EXE -f '%.7f' \
    +proj=nzmg +lat_0=-41 +lon_0=173 +x_0=2510000 +y_0=6023150 +ellps=WGS84 +units=m +to \
    +proj=latlong +datum=WGS84 \
 -E >> ${OUT} <<EOF
2680778.57267967 6132228.07645127 0.
EOF
##############################################################################
echo  "##############################################################" >> ${OUT}
echo  "Test misrsom forward projection" >> ${OUT}
#
$EXE -f '%.7f' \
    +proj=latlong +datum=WGS84 +to \
    +proj=misrsom +path=41 +ellps=WGS84 +units=m \
 -E >> ${OUT} <<EOF
48.64966165540372 66.2263195368941 0.
EOF
##############################################################################
echo  "##############################################################" >> ${OUT}
echo  "Test misrsom inverse projection" >> ${OUT}
#
$EXE -f '%.7f' \
    +proj=misrsom +path=41 +ellps=WGS84 +units=m +to \
    +proj=latlong +datum=WGS84 -f '%0.7f' \
 -E >> ${OUT} <<EOF
7461300.0 528000.0 0.0
EOF
echo  "##############################################################" >> ${OUT}
echo  "Test patterson forward projection" >> ${OUT}
#
$EXE -f '%0.8f' \
    +proj=latlong +datum=WGS84 \
    +to +proj=patterson +a=6371008.7714 +b=6371008.7714 +units=m \
 -E >> ${OUT} <<EOF
-180 90
-135 67.5
-90 45
-45 22.5
0 0
45 -22.5
90 -45
135 -67.5
180 -90
EOF
echo  "##############################################################" >> ${OUT}
echo  "Test patterson inverse projection" >> ${OUT}
#
$EXE -f '%0.3f' \
    +proj=patterson +a=6371008.7714 +b=6371008.7714 +units=m \
    +to +proj=latlong +datum=WGS84 \
 -E >> ${OUT} <<EOF
-20015114.352186374 11409566.822831295
-15011335.76413978 8729502.054111844
-10007557.176093187 5366413.421153781
-5003778.588046594 2551415.729669344
0.0 0.0
5003778.588046594 -2551415.729669344
10007557.176093187 -5366413.421153781
15011335.76413978 -8729502.054111844
20015114.352186374 -11409566.822831295
EOF
echo  "##############################################################" >> ${OUT}
echo  "Test Web Mercator to avoid issue #834 in the future" >> ${OUT}
#
$EXE -f '%0.3f' \
    +proj=utm +zone=15 +datum=NAD83 \
    +to +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 \
    +k=1.0 +units=m +nadgrids=@null \
 -E >> ${OUT} <<EOF
487147.594520173 4934316.46263998
EOF

echo  "##############################################################" >> ${OUT}
echo  "Test vto_meter" >> ${OUT}
#
$EXE -f '%0.3f' \
    +proj=longlat +a=1 +b=1 +vto_meter=1000 \
    +to +proj=longlat +a=1 +b=1  \
 -E >> ${OUT} <<EOF
0 0 1
EOF

$EXE -f '%0.3f' \
    +proj=merc +a=1 +b=1 +vto_meter=1000 \
    +to +proj=longlat +a=1 +b=1  \
 -E >> ${OUT} <<EOF
0 0 1
EOF

$EXE -f '%0.3f' \
    +proj=longlat +a=1 +b=1 \
    +to +proj=longlat +a=1 +b=1 +vto_meter=1000 \
 -E >> ${OUT} <<EOF
0 0 1000
EOF

$EXE -f '%0.3f' \
    +proj=longlat +a=1 +b=1 \
    +to +proj=merc +a=1 +b=1 +vto_meter=1000 \
 -E >> ${OUT} <<EOF
0 0 1000
EOF

echo  "##############################################################" >> ${OUT}
echo  "Test EPSG:4326 to EPSG:32631" >> ${OUT}
# Input is latitude, longitude order
$EXE EPSG:4326 +to EPSG:32631 -E >> ${OUT} <<EOF
49 3 0
EOF

echo  "##############################################################" >> ${OUT}
echo  "Test EPSG:32631 to EPSG:4326" >> ${OUT}
# Input is latitude, longitude order
$EXE EPSG:32631 EPSG:4326 -E >> ${OUT} <<EOF
400000 5000000 0
EOF


echo  "##############################################################" >> ${OUT}
echo  "Test EPSG:4896 to EPSG:7930" >> ${OUT}
# Here we test that 4D coordinates are handled by cs2cs. Due to backwards
# compatibility, the t-component is not written to STDOUT as part of the
# coordinate data, but rather as part of the string that follows the xyz
# components. This is only seen by users when the -E option is used. Which
# means that this test also experience that behavior.
$EXE -f %.4f EPSG:4896 EPSG:7930 -E >> ${OUT} <<EOF
3496737.2679    743254.4507  5264462.9620 2019.0
3496737.2679    743254.4507  5264462.9620 2029.0
EOF


echo  "##############################################################" >> ${OUT}
echo  "Test ITRF2000 to ITRF1993" >> ${OUT}
# Here we test that HUGE_VAL is passed as the time value if not explicitly
# specified
$EXE -f %.7f ITRF2000 ITRF1993 -E >> ${OUT} <<EOF
59.4967 -117.61748 329.396
59.4967 -117.61748 329.396 1988
EOF

echo "##############################################################" >> ${OUT}
echo "Check ob_tran with o_proj=longlat (#1525)" >> ${OUT}
$EXE -f %.7f +proj=longlat +ellps=GRS80 +to +proj=ob_tran +o_proj=longlat +lon_0=10 +o_lat_p=90 +ellps=GRS80 -E >>${OUT} <<EOF
-122 46
EOF
echo "Test inverse handling" >> ${OUT}
$EXE -f %.7f -I +proj=longlat +ellps=GRS80 +to +proj=ob_tran +o_proj=longlat +lon_0=10 +o_lat_p=90 +ellps=GRS80 -E >>${OUT} <<EOF
-122 46
EOF

echo "##############################################################" >> ${OUT}
echo "Check +init=epsg:4326 +over +to +init=epsg:3857 +over" >> ${OUT}
$EXE -f %.7f +init=epsg:4326 +over +to +init=epsg:3857 +over -E >>${OUT} <<EOF
-181 49
EOF

echo "##############################################################" >> ${OUT}
echo "Check +proj=longlat +over +datum=WGS84 +to proj=merc +a=6378137 +b=6378137 +lat_ts=0 +lon_0=0 +x_0=0 +y_0=0 +k=1 +units=m +nadgrids=@null +over" >> ${OUT}
$EXE -f %.7f +proj=longlat +over +datum=WGS84 +to proj=merc +a=6378137 +b=6378137 +lat_ts=0 +lon_0=0 +x_0=0 +y_0=0 +k=1 +units=m +nadgrids=@null +over -E >>${OUT} <<EOF
-181 49
EOF

echo  "##############################################################" >> ${OUT}
echo  "Test EPSG:xxxx EPSG:yyyy filename" >> ${OUT}
echo "2 49" > tmp.txt
$EXE EPSG:4326 EPSG:4326 tmp.txt -E >> ${OUT}
rm tmp.txt

echo  "##############################################################" >> ${OUT}
echo  "Test Colombia Urban" >> ${OUT}
$EXE -f %.3f EPSG:4686 EPSG:6247 -E >> ${OUT} <<EOF
4.8 -74.25
EOF

echo  "##############################################################" >> ${OUT}
echo  "Test effect of --authority (https://github.com/OSGeo/PROJ/issues/2442)" >> ${OUT}
echo  "The first result should use the EPSG:8076 operation" >> ${OUT}
echo  "and the second one a no-op" >> ${OUT}
$EXE -E ITRF96 ITRF2014 >> ${OUT} <<EOF
49 2
EOF
$EXE --authority PROJ -E ITRF96 ITRF2014 >> ${OUT} <<EOF
49 2
EOF

echo  "##############################################################" >> ${OUT}
echo  "Test effect of --accuracy" >> ${OUT}
$EXE -E --accuracy 0.05 EPSG:4326 EPSG:4258 >> ${OUT} <<EOF
49 2
EOF

echo  "##############################################################" >> ${OUT}
echo  "Test effect of --no-ballpark" >> ${OUT}
$EXE -E --no-ballpark EPSG:4267 EPSG:4258 >> ${OUT} <<EOF
49 2
EOF

echo  "##############################################################" >> ${OUT}
echo  "Check that we can use a transformation spanning the antimeridian (should use Pulkovo 1942 to WGS 84 (20))" >> ${OUT}
$EXE -E "Pulkovo 1942" "WGS 84" >> ${OUT} <<EOF
50 179.999999999
50 -179.999999999
EOF

echo  "##############################################################" >> ${OUT}
echo  "Check that we can use a transformation spanning the antimeridian (should use Pulkovo 1942 to WGS 84 (20))" >> ${OUT}
$EXE -E EPSG:2636 "WGS 84" >> ${OUT} <<EOF
5540944.47  499999.999
5540944.47  500000.001
EOF

echo  "##############################################################" >> ${OUT}
echo  "Check that we select the operation that has the smallest area of use, when 2 have the same accuracy" >> ${OUT}
$EXE -E EPSG:4326 "NAD83(HARN)" >> ${OUT} <<EOF
34 -120
EOF


# Done!
# do 'diff' with distribution results
echo "diff ${OUT} with ${OUT}.dist"
diff -u -b ${OUT} ${TEST_CLI_DIR}/${OUT}.dist
if [ $? -ne 0 ] ; then
	echo  ""
	echo "PROBLEMS HAVE OCCURRED"
	echo "test file ${OUT} saved"
    echo
    echo "----------------------------------------------------------"
	echo "${OUT}"
    echo "----------------------------------------------------------"
    cat ${OUT}
    echo "----------------------------------------------------------"
	exit 100
else
	echo "TEST OK"
	echo "test file ${OUT} removed"
    echo
	/bin/rm -f ${OUT}
	exit 0
fi
