:
# <PERSON>ript to do some testing of flaky transformation that do not depend
# on datum files.
#
#
TEST_CLI_DIR=`dirname $0`
EXE=$1

usage()
{
    echo "Usage: ${0} <path to 'cs2cs' program>"
    echo
    exit 1
}

if test -z "${EXE}"; then
    EXE=../../src/cs2cs
fi

if test ! -x ${EXE}; then
    echo "*** ERROR: Can not find '${EXE}' program!"
    exit 1
fi

echo "============================================"
echo "Running ${0} using ${EXE}:"
echo "============================================"

OUT=tf_out
#EXE=../src/cs2cs
#
echo "doing tests into file ${OUT}, please wait"
rm -f ${OUT}
#
echo "Test healpix inverse projection on sphere" >> ${OUT}
$EXE +proj=latlong +a=1 +lon_0=0 \
 +to +proj=healpix +a=1 +lon_0=0  -f '%.'5'f' -I\
 -E >>${OUT} <<EOF
0 0.7853981633974483
-1.5707963267948966 0
EOF
$EXE +proj=latlong +a=5 \
 +to +proj=healpix +a=5 -f '%.'5'f' -I\
 -E >>${OUT} <<EOF
0.0 0.0
0.0 3.9269908169872414
0.0 -3.9269908169872414
7.853981633974483 0.0
-7.853981633974483 0.0
-15.707963267948966 0.0
-11.780972450961723 7.853981633974483
-11.780972450961723 -7.853981633974483
1.437378399445537 5.364369216432778
1.437378399445537 -5.364369216432778
EOF

##############################################################################
# Done!
# do 'diff' with distribution results
echo "diff ${OUT} with ${OUT}.dist"
diff -u -b ${OUT} ${TEST_CLI_DIR}/${OUT}.dist
if [ $? -ne 0 ] ; then
	echo  ""
	echo "PROBLEMS HAVE OCCURRED"
	echo "test file ${OUT} saved"
    echo
	exit 100
else
	echo "TEST OK"
	echo "test file ${OUT} removed"
    echo
	/bin/rm -f ${OUT}
	exit 0
fi
