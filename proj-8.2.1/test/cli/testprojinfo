:
# Test projinfo

TEST_CLI_DIR=`dirname $0`
EXE=$1

usage()
{
    echo "Usage: ${0} <path to 'projinfo' program>" 
    echo
    exit 1
}

if test -z "${EXE}"; then
    EXE=../../src/projinfo
fi

if test ! -x ${EXE}; then
    echo "*** ERROR: Can not find '${EXE}' program!"
    exit 1
fi

echo "============================================"
echo "Running ${0} using ${EXE}:"
echo "============================================"

OUT=testprojinfo_out

rm -f ${OUT}

export PROJINFO_NO_GRID_CHECK=YES

echo "Testing projinfo EPSG:4326" >> ${OUT}
$EXE EPSG:4326 >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -o WKT1_GDAL EPSG:4326" >> ${OUT}
$EXE -o WKT1_GDAL EPSG:4326 >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -o WKT2_2015 EPSG:4326" >> ${OUT}
$EXE -o WKT2_2015 EPSG:4326 >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -o WKT2_2019 EPSG:4326" >> ${OUT}
$EXE -o WKT2_2019 EPSG:4326 >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -o ALL EPSG:4326" >> ${OUT}
$EXE -o ALL EPSG:4326 >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo \"+proj=merc +lat_ts=5 +datum=WGS84 +type=crs\" --output-id HOBU:MY_CRS -o SQL -q" >> ${OUT}
$EXE "+proj=merc +lat_ts=5 +datum=WGS84 +type=crs" --output-id HOBU:MY_CRS -o SQL -q >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo \"+proj=merc +lat_ts=5 +datum=WGS84 +type=crs\" --output-id HOBU:MY_CRS --authority HOBU -o SQL -q" >> ${OUT}
$EXE "+proj=merc +lat_ts=5 +datum=WGS84 +type=crs" --output-id HOBU:MY_CRS --authority HOBU -o SQL -q >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s EPSG:4326 -t EPSG:32631 --single-line" >> ${OUT}
$EXE -s EPSG:4326 -t EPSG:32631 --single-line >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s NAD27 -t NAD83" >> ${OUT}
$EXE -s NAD27 -t NAD83 >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s NAD27 -t NAD83 --grid-check none --spatial-test intersects --summary --hide-ballpark" >> ${OUT}
$EXE -s NAD27 -t NAD83 --grid-check none --spatial-test intersects --summary --hide-ballpark >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s NAD27 -t NAD83 --grid-check none --spatial-test intersects" >> ${OUT}
$EXE -s NAD27 -t NAD83 --grid-check none --spatial-test intersects >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s EPSG:4230 -t EPSG:4258 --bbox 8,54.51,15.24,57.8 --summary" >> ${OUT}
$EXE -s EPSG:4230 -t EPSG:4258 --bbox 8,54.51,15.24,57.8 --summary >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s EPSG:23031 -t EPSG:4326 --bbox -13.87,34.91,-7.24,41.88 --crs-extent-use none --summary" >> ${OUT}
$EXE -s EPSG:23031 -t EPSG:4326 --bbox -13.87,34.91,-7.24,41.88 --crs-extent-use none --summary >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s EPSG:4230 -t EPSG:4258 --area EPSG:3237 --summary" >> ${OUT}
$EXE -s EPSG:4230 -t EPSG:4258 --area EPSG:3237 --summary >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo -s EPSG:4230 -t EPSG:4258 --area 'Denmark - onshore' --summary" >> ${OUT}
$EXE -s EPSG:4230 -t EPSG:4258 --area 'Denmark - onshore' --summary >>${OUT}
echo "" >>${OUT}

# several match
echo "Testing projinfo -s EPSG:4230 -t EPSG:4258 --area 'Denmark -' --summary" >> ${OUT}
$EXE -s EPSG:4230 -t EPSG:4258 --area 'Denmark -' --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing projinfo -s EPSG:4230 -t EPSG:4258 --area no_match --summary" >> ${OUT}
$EXE -s EPSG:4230 -t EPSG:4258 --area no_match --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing projinfo -s EPSG:4230 -t EPSG:4258 --area WRONG:CODE --summary" >> ${OUT}
$EXE -s EPSG:4230 -t EPSG:4258 --area WRONG:CODE --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing deprecated CRS: projinfo EPSG:26591" >> ${OUT}
$EXE EPSG:26591 >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing non compliant WKT1" >> ${OUT}
$EXE 'GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563]],UNIT["degree",0.0174532925199433]]' >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing CRS with towgs84: projinfo -o PROJ EPSG:25832" >> ${OUT}
$EXE -o PROJ EPSG:25832 >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing RH2000 height to SWEREF99: projinfo -s EPSG:5613 -t EPSG:4977" >> ${OUT}
$EXE -s EPSG:5613 -t EPSG:4977 >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing NAD83(2011) + NAVD88 height -> NAD83(2011) : projinfo -s EPSG:6349 -t EPSG:6319 --spatial-test intersects -o PROJ" >> ${OUT}
$EXE -s EPSG:6349 -t EPSG:6319 --spatial-test intersects -o PROJ >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing NGF IGN69 height to RGF93: projinfo -s EPSG:5720 -t EPSG:4965 -o PROJ" >> ${OUT}
$EXE -s EPSG:5720 -t EPSG:4965 -o PROJ >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing EPSG:32631 --3d" >> ${OUT}
$EXE EPSG:32631 --3d >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing -s EPSG:32631 -t EPSG:4326+3855 --summary" >> ${OUT}
$EXE -s EPSG:32631 -t EPSG:4326+3855 --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing -s EPSG:32631 -t EPSG:4326+3855 --3d --summary" >> ${OUT}
$EXE -s EPSG:32631 -t EPSG:4326+3855 --3d --summary >>${OUT} 2>&1
echo "" >>${OUT}

# Undocumented option: --normalize-axis-order
echo "Testing -s EPSG:4326 -t EPSG:32661 --normalize-axis-order -o PROJ -q --single-line" >> ${OUT}
$EXE -s EPSG:4326 -t EPSG:32661 --normalize-axis-order -o PROJ -q --single-line >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing -s EPSG:4936 -t EPSG:4978 --spatial-test intersects --summary where WGS 84 to ETRS89 (2) uses a transformation method not supported by PROJ currently (time-specific Helmert), and thus must be sorted last" >> ${OUT}
$EXE -s EPSG:4936 -t EPSG:4978 --spatial-test intersects --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing -s "+proj=longlat +datum=WGS84 +geoidgrids=@foo.gtx +type=crs" -t EPSG:4326 -o PROJ -q" >> ${OUT}
$EXE -s "+proj=longlat +datum=WGS84 +geoidgrids=@foo.gtx +type=crs" -t EPSG:4326 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s "AGD66" -t "WGS 84 (G1762)" --spatial-test intersects --summary. Should include a transformation through GDA2020' >> ${OUT}
$EXE -s "AGD66" -t "WGS 84 (G1762)" --spatial-test intersects --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s EPSG:31467 -t ETRS89 --spatial-test intersects --grid-check none --bbox 8,48,9,49 --summary. Should include both DHDN to ETRS89 (8) and DHDN to ETRS89 (BWTA2017)' >> ${OUT}
$EXE -s EPSG:31467 -t ETRS89 --spatial-test intersects --grid-check none --bbox 8,48,9,49 --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s "GDA94" -t "AHD height" --grid-check none -o PROJ --spatial-test intersects' >> ${OUT}
$EXE -s "GDA94" -t "AHD height" --grid-check none -o PROJ --spatial-test intersects >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s "GDA2020" -t "AHD height" --grid-check none -o PROJ --spatial-test intersects' >> ${OUT}
$EXE -s "GDA2020" -t "AHD height" --grid-check none -o PROJ --spatial-test intersects >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -k ellipsoid WGS84' >> ${OUT}
$EXE -k ellipsoid WGS84 >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -k ellipsoid EPSG:7030' >> ${OUT}
$EXE -k ellipsoid EPSG:7030 >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -k datum WGS84' >> ${OUT}
$EXE -k datum WGS84 >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -k datum EPSG:6326' >> ${OUT}
$EXE -k datum EPSG:6326 >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -k ensemble WGS84' >> ${OUT}
$EXE -k ensemble WGS84 >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -k operation EPSG:8457 -o PROJ -q' >> ${OUT}
$EXE -k operation EPSG:8457 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing D_WGS_1984' >> ${OUT}
$EXE D_WGS_1984 >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -k datum D_WGS_1984' >> ${OUT}
$EXE -k datum  D_WGS_1984 >>${OUT} 2>&1
echo "" >>${OUT}

# Testing --searchpaths
if ! $EXE --searchpaths > testprojinfo_out_searchpaths.txt; then
    echo "--searchpaths failed"
    exit 100
fi
# Hard to test content of testprojinfo_out_searchpaths.txt
rm testprojinfo_out_searchpaths.txt

# Testing --remote-data
if ! $EXE --remote-data > testprojinfo_out_remotedata.txt; then
    echo "--remote-data failed"
    exit 100
fi
# Hard to test content of testprojinfo_out_remotedata.txt
rm testprojinfo_out_remotedata.txt

# Testing --remote-data
export PROJ_NETWORK=ON
if ! $EXE --remote-data > testprojinfo_out_remotedata.txt; then
    echo "--remote-data failed"
    exit 100
fi
# Hard to test content of testprojinfo_out_remotedata.txt
rm testprojinfo_out_remotedata.txt
unset PROJ_NETWORK

echo 'Testing --accuracy 0.05 -s EPSG:4326 -t EPSG:4258' >> ${OUT}
$EXE --accuracy 0.05 -s EPSG:4326 -t EPSG:4258 >>${OUT} 2>&1
echo "" >>${OUT}

######################
# NZGD2000 -> ITRFxx #
######################
echo 'Testing -s NZGD2000 -t ITRF96 -o PROJ -q' >> ${OUT}
$EXE -s NZGD2000 -t ITRF96 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s NZGD2000 -t ITRF97 -o PROJ -q' >> ${OUT}
$EXE -s NZGD2000 -t ITRF97 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s NZGD2000 -t ITRF2000 -o PROJ -q' >> ${OUT}
$EXE -s NZGD2000 -t ITRF2000 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s NZGD2000 -t ITRF2005 -o PROJ -q' >> ${OUT}
$EXE -s NZGD2000 -t ITRF2005 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s NZGD2000 -t ITRF2008 -o PROJ -q' >> ${OUT}
$EXE -s NZGD2000 -t ITRF2008 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s NZGD2000 -t ITRF2014 -o PROJ -q' >> ${OUT}
$EXE -s NZGD2000 -t ITRF2014 -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

######################
# Finland TINs
######################

echo 'Testing -s "KKJ / Finland Uniform Coordinate System" -t "ETRS89 / TM35FIN(E,N)" --grid-check none -o PROJ -q' >> ${OUT}
$EXE -s "KKJ / Finland Uniform Coordinate System" -t "ETRS89 / TM35FIN(E,N)" --grid-check none -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s KKJ -t ETRS89 -o PROJ --grid-check none -q' >> ${OUT}
$EXE -s KKJ -t ETRS89 -o PROJ --grid-check none -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s "KKJ + N43 height" -t "KKJ + N60 height" --grid-check none -o PROJ -q' >> ${OUT}
$EXE -s "KKJ + N43 height" -t "KKJ + N60 height" --grid-check none -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing -s "KKJ + N60 height" -t "KKJ + N2000 height" --grid-check none -o PROJ -q' >> ${OUT}
$EXE -s "KKJ + N60 height" -t "KKJ + N2000 height" --grid-check none -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

# Advanced !
echo 'Testing -s "KKJ + N43 height" -t "ETRS89 + N2000 height" --grid-check none -o PROJ -q' >> ${OUT}
$EXE -s "KKJ + N43 height" -t "ETRS89 + N2000 height" --grid-check none -o PROJ -q >>${OUT} 2>&1
echo "" >>${OUT}

# Advanced !
echo 'Testing -s "KKJ / Finland Uniform Coordinate System + N43 height" -t "ETRS89 / TM35FIN(E,N) + N2000 height" --grid-check none -o PROJ -q' >> ${OUT}
$EXE -s "KKJ / Finland Uniform Coordinate System + N43 height" -t "ETRS89 / TM35FIN(E,N) + N2000 height" --grid-check none -o PROJ -q  >>${OUT} 2>&1
echo "" >>${OUT}

# Advanced !
echo 'Testing -s "ETRS89 / TM35FIN(E,N) + N2000 height" -t "KKJ / Finland Uniform Coordinate System + N43 height" --grid-check none -o PROJ -q' >> ${OUT}
$EXE -s "ETRS89 / TM35FIN(E,N) + N2000 height" -t "KKJ / Finland Uniform Coordinate System + N43 height" --grid-check none -o PROJ -q  >>${OUT} 2>&1
echo "" >>${OUT}

# Quick check of NKG transformations
echo 'Testing NKG: -s EPSG:7789 -t EPSG:4936 --area  EPSG:1080 --summary --hide-ballpark' >> ${OUT}
$EXE -s EPSG:7789 -t EPSG:4936 --area  EPSG:1080 --summary --hide-ballpark >>${OUT} 2>&1
echo "" >>${OUT}

echo "Testing projinfo --dump-db-structure | head -n 5" >> ${OUT}
$EXE --dump-db-structure | head -n 5 >>${OUT}
echo "" >>${OUT}

echo "Testing projinfo --dump-db-structure --output-id HOBU:XXXX EPSG:4326 | tail -n 4" >> ${OUT}
$EXE --dump-db-structure --output-id HOBU:XXXX EPSG:4326 | tail -n 4 >>${OUT}
echo "" >>${OUT}

echo "Testing PROJ_AUX_DB environment variable" >> ${OUT}
rm -f tmp_projinfo_aux.db
$EXE --dump-db-structure --output-id HOBU:XXXX EPSG:4326 | sqlite3 tmp_projinfo_aux.db
export PROJ_AUX_DB=tmp_projinfo_aux.db
$EXE HOBU:XXXX >>${OUT}
unset PROJ_AUX_DB
rm -f tmp_projinfo_aux.db
echo "" >>${OUT}

echo 'Testing -s EPSG:23030 -t EPSG:25830 --bbox -6,40,-5,41 --grid-check known_available --hide-ballpark --summary' >> ${OUT}
echo 'Checks that ED50 to ETRS89 (12) is in the output (superseded transformation, but replacements has unknown grid)' >> ${OUT}
$EXE -s EPSG:23030 -t EPSG:25830 --bbox -6,40,-5,41 --grid-check known_available --hide-ballpark --summary >>${OUT} 2>&1
echo "" >>${OUT}

echo 'Testing --list-crs | grep "EPSG:32632\|ESRI:103668\|OGC" | sort' >> ${OUT}
$EXE --list-crs | grep "EPSG:32632\|ESRI:103668\|OGC" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs --authority OGC,EPSG | grep "EPSG:4326\|OGC" | sort' >> ${OUT}
$EXE --list-crs --authority OGC,EPSG | grep "EPSG:4326\|OGC" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs | grep deprecated | sort' >> ${OUT}
$EXE --list-crs | grep deprecated | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs vertical --bbox 0,40,1,41 --spatial-test intersects | grep "Alicante\|NAVD88" | sort' >> ${OUT}
$EXE --list-crs vertical --bbox 0,40,1,41 --spatial-test intersects | grep "Alicante\|NAVD88" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs vertical --bbox -10,35,5,45 --spatial-test contains | grep "Alicante\|NAVD88" | sort' >> ${OUT}
$EXE --list-crs vertical --bbox -10,35,5,45 --spatial-test contains | grep "Alicante\|NAVD88" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs --area Spain --spatial-test intersects | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort' >> ${OUT}
$EXE --list-crs --area Spain --spatial-test intersects | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs --area Spain --spatial-test contains | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort' >> ${OUT}
$EXE --list-crs --area Spain --spatial-test contains | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs --area Spain | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort' >> ${OUT}
$EXE --list-crs --area Spain | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs geodetic | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort' >> ${OUT}
$EXE --list-crs geodetic | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs geographic | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort' >> ${OUT}
$EXE --list-crs geographic | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs geocentric,geographic_3d | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort' >> ${OUT}
$EXE --list-crs geocentric,geographic_3d | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs geographic_2d,allow_deprecated --bbox -100,40,-90,41 --spatial-test intersects | grep deprecated | grep "NAD83(FBN)\|GCS_IGS08" | sort' >> ${OUT}
$EXE --list-crs geographic_2d,allow_deprecated --bbox -100,40,-90,41 --spatial-test intersects | grep deprecated | grep "NAD83(FBN)\|GCS_IGS08" | sort >> ${OUT}
echo "" >>${OUT}

echo 'Testing --list-crs projected --bbox -100,40,-90,41 --spatial-test intersects | grep "(2011).*Missouri East\|York" | sort' >> ${OUT}
$EXE --list-crs projected --bbox -100,40,-90,41 --spatial-test intersects | grep "(2011).*Missouri East\|York" | sort >> ${OUT}
echo "" >>${OUT}

# Test case where --area has several matches
echo 'Testing --list-crs projected --area France | grep "RGF93 v1 / Lambert-93\|UTM zone 11N" | sort' >> ${OUT}
$EXE --list-crs projected --area France | grep "RGF93 v1 / Lambert-93\|UTM zone 11N" | sort >> ${OUT}
echo "" >>${OUT}

# do 'diff' with distribution results
echo "diff ${OUT} with testprojinfo_out.dist"
diff -u ${OUT} ${TEST_CLI_DIR}/testprojinfo_out.dist
if [ $? -ne 0 ] ; then
	echo  ""
	echo "PROBLEMS HAVE OCCURRED"
	echo "test file ${OUT} saved"
    echo
	exit 100
else
	echo "TEST OK"
	echo "test file ${OUT} removed"
    echo
	/bin/rm -f ${OUT}
	exit 0
fi
