Testing projinfo EPSG:4326
PROJ.4 string:
+proj=longlat +datum=WGS84 +no_defs +type=crs

WKT2:2019 string:
GEOGCRS["WGS 84",
    ENSEMBLE["World Geodetic System 1984 ensemble",
        ME<PERSON><PERSON>["World Geodetic System 1984 (Transit)"],
        <PERSON><PERSON><PERSON>["World Geodetic System 1984 (G730)"],
        <PERSON><PERSON><PERSON>["World Geodetic System 1984 (G873)"],
        <PERSON><PERSON><PERSON>["World Geodetic System 1984 (G1150)"],
        <PERSON><PERSON><PERSON>["World Geodetic System 1984 (G1674)"],
        MEMBER["World Geodetic System 1984 (G1762)"],
        MEMBER["World Geodetic System 1984 (G2139)"],
        ELLIPSOID["WGS 84",6378137,298.*********,
            <PERSON>EN<PERSON>THUNIT["metre",1]],
        ENSEMBLEACCURACY[2.0]],
    <PERSON><PERSON><PERSON>["Greenwich",0,
        ANGLEUNIT["degree",0.0174532925199433]],
    CS[ellipsoidal,2],
        AXIS["geodetic latitude (Lat)",north,
            ORDER[1],
            ANG<PERSON><PERSON><PERSON>["degree",0.0174532925199433]],
        AXIS["geodetic longitude (Lon)",east,
            ORDER[2],
            ANGLEUNIT["degree",0.0174532925199433]],
    USAGE[
        SCOPE["Horizontal component of 3D system."],
        AREA["World."],
        BBOX[-90,-180,90,180]],
    ID["EPSG",4326]]

Testing projinfo -o WKT1_GDAL EPSG:4326
WKT1:GDAL string:
GEOGCS["WGS 84",
    DATUM["WGS_1984",
        SPHEROID["WGS 84",6378137,298.*********,
            AUTHORITY["EPSG","7030"]],
        AUTHORITY["EPSG","6326"]],
    PRIMEM["Greenwich",0,
        AUTHORITY["EPSG","8901"]],
    UNIT["degree",0.0174532925199433,
        AUTHORITY["EPSG","9122"]],
    AUTHORITY["EPSG","4326"]]


Testing projinfo -o WKT2_2015 EPSG:4326
WKT2:2015 string:
GEODCRS["WGS 84",
    DATUM["World Geodetic System 1984",
        ELLIPSOID["WGS 84",6378137,298.*********,
            LENGTHUNIT["metre",1]]],
    PRIMEM["Greenwich",0,
        ANGLEUNIT["degree",0.0174532925199433]],
    CS[ellipsoidal,2],
        AXIS["geodetic latitude (Lat)",north,
            ORDER[1],
            ANGLEUNIT["degree",0.0174532925199433]],
        AXIS["geodetic longitude (Lon)",east,
            ORDER[2],
            ANGLEUNIT["degree",0.0174532925199433]],
    SCOPE["Horizontal component of 3D system."],
    AREA["World."],
    BBOX[-90,-180,90,180],
    ID["EPSG",4326]]

Testing projinfo -o WKT2_2019 EPSG:4326
WKT2:2019 string:
GEOGCRS["WGS 84",
    ENSEMBLE["World Geodetic System 1984 ensemble",
        MEMBER["World Geodetic System 1984 (Transit)"],
        MEMBER["World Geodetic System 1984 (G730)"],
        MEMBER["World Geodetic System 1984 (G873)"],
        MEMBER["World Geodetic System 1984 (G1150)"],
        MEMBER["World Geodetic System 1984 (G1674)"],
        MEMBER["World Geodetic System 1984 (G1762)"],
        MEMBER["World Geodetic System 1984 (G2139)"],
        ELLIPSOID["WGS 84",6378137,298.*********,
            LENGTHUNIT["metre",1]],
        ENSEMBLEACCURACY[2.0]],
    PRIMEM["Greenwich",0,
        ANGLEUNIT["degree",0.0174532925199433]],
    CS[ellipsoidal,2],
        AXIS["geodetic latitude (Lat)",north,
            ORDER[1],
            ANGLEUNIT["degree",0.0174532925199433]],
        AXIS["geodetic longitude (Lon)",east,
            ORDER[2],
            ANGLEUNIT["degree",0.0174532925199433]],
    USAGE[
        SCOPE["Horizontal component of 3D system."],
        AREA["World."],
        BBOX[-90,-180,90,180]],
    ID["EPSG",4326]]

Testing projinfo -o ALL EPSG:4326
PROJ.4 string:
+proj=longlat +datum=WGS84 +no_defs +type=crs

WKT2:2015 string:
GEODCRS["WGS 84",
    DATUM["World Geodetic System 1984",
        ELLIPSOID["WGS 84",6378137,298.*********,
            LENGTHUNIT["metre",1]]],
    PRIMEM["Greenwich",0,
        ANGLEUNIT["degree",0.0174532925199433]],
    CS[ellipsoidal,2],
        AXIS["geodetic latitude (Lat)",north,
            ORDER[1],
            ANGLEUNIT["degree",0.0174532925199433]],
        AXIS["geodetic longitude (Lon)",east,
            ORDER[2],
            ANGLEUNIT["degree",0.0174532925199433]],
    SCOPE["Horizontal component of 3D system."],
    AREA["World."],
    BBOX[-90,-180,90,180],
    ID["EPSG",4326]]

WKT2:2019 string:
GEOGCRS["WGS 84",
    ENSEMBLE["World Geodetic System 1984 ensemble",
        MEMBER["World Geodetic System 1984 (Transit)"],
        MEMBER["World Geodetic System 1984 (G730)"],
        MEMBER["World Geodetic System 1984 (G873)"],
        MEMBER["World Geodetic System 1984 (G1150)"],
        MEMBER["World Geodetic System 1984 (G1674)"],
        MEMBER["World Geodetic System 1984 (G1762)"],
        MEMBER["World Geodetic System 1984 (G2139)"],
        ELLIPSOID["WGS 84",6378137,298.*********,
            LENGTHUNIT["metre",1]],
        ENSEMBLEACCURACY[2.0]],
    PRIMEM["Greenwich",0,
        ANGLEUNIT["degree",0.0174532925199433]],
    CS[ellipsoidal,2],
        AXIS["geodetic latitude (Lat)",north,
            ORDER[1],
            ANGLEUNIT["degree",0.0174532925199433]],
        AXIS["geodetic longitude (Lon)",east,
            ORDER[2],
            ANGLEUNIT["degree",0.0174532925199433]],
    USAGE[
        SCOPE["Horizontal component of 3D system."],
        AREA["World."],
        BBOX[-90,-180,90,180]],
    ID["EPSG",4326]]

WKT1:GDAL string:
GEOGCS["WGS 84",
    DATUM["WGS_1984",
        SPHEROID["WGS 84",6378137,298.*********,
            AUTHORITY["EPSG","7030"]],
        AUTHORITY["EPSG","6326"]],
    PRIMEM["Greenwich",0,
        AUTHORITY["EPSG","8901"]],
    UNIT["degree",0.0174532925199433,
        AUTHORITY["EPSG","9122"]],
    AUTHORITY["EPSG","4326"]]


WKT1:ESRI string:
GEOGCS["GCS_WGS_1984",DATUM["D_WGS_1984",SPHEROID["WGS_1984",6378137.0,298.*********]],PRIMEM["Greenwich",0.0],UNIT["Degree",0.0174532925199433]]


PROJJSON:
{
  "$schema": "https://proj.org/schemas/v0.4/projjson.schema.json",
  "type": "GeographicCRS",
  "name": "WGS 84",
  "datum_ensemble": {
    "name": "World Geodetic System 1984 ensemble",
    "members": [
      {
        "name": "World Geodetic System 1984 (Transit)",
        "id": {
          "authority": "EPSG",
          "code": 1166
        }
      },
      {
        "name": "World Geodetic System 1984 (G730)",
        "id": {
          "authority": "EPSG",
          "code": 1152
        }
      },
      {
        "name": "World Geodetic System 1984 (G873)",
        "id": {
          "authority": "EPSG",
          "code": 1153
        }
      },
      {
        "name": "World Geodetic System 1984 (G1150)",
        "id": {
          "authority": "EPSG",
          "code": 1154
        }
      },
      {
        "name": "World Geodetic System 1984 (G1674)",
        "id": {
          "authority": "EPSG",
          "code": 1155
        }
      },
      {
        "name": "World Geodetic System 1984 (G1762)",
        "id": {
          "authority": "EPSG",
          "code": 1156
        }
      },
      {
        "name": "World Geodetic System 1984 (G2139)",
        "id": {
          "authority": "EPSG",
          "code": 1309
        }
      }
    ],
    "ellipsoid": {
      "name": "WGS 84",
      "semi_major_axis": 6378137,
      "inverse_flattening": 298.*********
    },
    "accuracy": "2.0",
    "id": {
      "authority": "EPSG",
      "code": 6326
    }
  },
  "coordinate_system": {
    "subtype": "ellipsoidal",
    "axis": [
      {
        "name": "Geodetic latitude",
        "abbreviation": "Lat",
        "direction": "north",
        "unit": "degree"
      },
      {
        "name": "Geodetic longitude",
        "abbreviation": "Lon",
        "direction": "east",
        "unit": "degree"
      }
    ]
  },
  "scope": "Horizontal component of 3D system.",
  "area": "World.",
  "bbox": {
    "south_latitude": -90,
    "west_longitude": -180,
    "north_latitude": 90,
    "east_longitude": 180
  },
  "id": {
    "authority": "EPSG",
    "code": 4326
  }
}

Testing projinfo "+proj=merc +lat_ts=5 +datum=WGS84 +type=crs" --output-id HOBU:MY_CRS -o SQL -q
INSERT INTO geodetic_crs VALUES('HOBU','GEODETIC_CRS_MY_CRS','unknown','','geographic 2D','EPSG','6424','EPSG','6326',NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_GEODETIC_CRS_MY_CRS','geodetic_crs','HOBU','GEODETIC_CRS_MY_CRS','PROJ','EXTENT_UNKNOWN','PROJ','SCOPE_UNKNOWN');
INSERT INTO conversion VALUES('HOBU','CONVERSION_MY_CRS','unknown','','EPSG','9805','Mercator (variant B)','EPSG','8823','Latitude of 1st standard parallel',5,'EPSG','9122','EPSG','8802','Longitude of natural origin',0,'EPSG','9122','EPSG','8806','False easting',0,'EPSG','9001','EPSG','8807','False northing',0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_CONVERSION_MY_CRS','conversion','HOBU','CONVERSION_MY_CRS','PROJ','EXTENT_UNKNOWN','PROJ','SCOPE_UNKNOWN');
INSERT INTO projected_crs VALUES('HOBU','MY_CRS','unknown','','EPSG','4400','HOBU','GEODETIC_CRS_MY_CRS','HOBU','CONVERSION_MY_CRS',NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_PROJECTED_CRS_MY_CRS','projected_crs','HOBU','MY_CRS','PROJ','EXTENT_UNKNOWN','PROJ','SCOPE_UNKNOWN');

Testing projinfo "+proj=merc +lat_ts=5 +datum=WGS84 +type=crs" --output-id HOBU:MY_CRS --authority HOBU -o SQL -q
INSERT INTO ellipsoid VALUES('HOBU','ELLPS_GEODETIC_DATUM_GEODETIC_CRS_MY_CRS','WGS 84','','IAU_2015','399',6378137,'EPSG','9001',298.*********,NULL,0);
INSERT INTO prime_meridian VALUES('HOBU','PM_GEODETIC_DATUM_GEODETIC_CRS_MY_CRS','Greenwich',0,'EPSG','9122',0);
INSERT INTO geodetic_datum VALUES('HOBU','GEODETIC_DATUM_GEODETIC_CRS_MY_CRS','World Geodetic System 1984','','HOBU','ELLPS_GEODETIC_DATUM_GEODETIC_CRS_MY_CRS','HOBU','PM_GEODETIC_DATUM_GEODETIC_CRS_MY_CRS',NULL,NULL,NULL,NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_GEODETIC_DATUM_GEODETIC_CRS_MY_CRS','geodetic_datum','HOBU','GEODETIC_DATUM_GEODETIC_CRS_MY_CRS','PROJ','EXTENT_UNKNOWN','PROJ','SCOPE_UNKNOWN');
INSERT INTO geodetic_crs VALUES('HOBU','GEODETIC_CRS_MY_CRS','unknown','','geographic 2D','EPSG','6424','HOBU','GEODETIC_DATUM_GEODETIC_CRS_MY_CRS',NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_GEODETIC_CRS_MY_CRS','geodetic_crs','HOBU','GEODETIC_CRS_MY_CRS','PROJ','EXTENT_UNKNOWN','PROJ','SCOPE_UNKNOWN');
INSERT INTO conversion VALUES('HOBU','CONVERSION_MY_CRS','unknown','','EPSG','9805','Mercator (variant B)','EPSG','8823','Latitude of 1st standard parallel',5,'EPSG','9122','EPSG','8802','Longitude of natural origin',0,'EPSG','9122','EPSG','8806','False easting',0,'EPSG','9001','EPSG','8807','False northing',0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_CONVERSION_MY_CRS','conversion','HOBU','CONVERSION_MY_CRS','PROJ','EXTENT_UNKNOWN','PROJ','SCOPE_UNKNOWN');
INSERT INTO projected_crs VALUES('HOBU','MY_CRS','unknown','','EPSG','4400','HOBU','GEODETIC_CRS_MY_CRS','HOBU','CONVERSION_MY_CRS',NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_PROJECTED_CRS_MY_CRS','projected_crs','HOBU','MY_CRS','PROJ','EXTENT_UNKNOWN','PROJ','SCOPE_UNKNOWN');

Testing projinfo -s EPSG:4326 -t EPSG:32631 --single-line
Candidate operations found: 1
-------------------------------------
Operation No. 1:

EPSG:16031, UTM zone 31N, 0 m, Between 0°E and 6°E, northern hemisphere between equator and 84°N, onshore and offshore.

PROJ string:
+proj=pipeline +step +proj=axisswap +order=2,1 +step +proj=unitconvert +xy_in=deg +xy_out=rad +step +proj=utm +zone=31 +ellps=WGS84

WKT2:2019 string:
CONVERSION["UTM zone 31N",METHOD["Transverse Mercator",ID["EPSG",9807]],PARAMETER["Latitude of natural origin",0,ANGLEUNIT["degree",0.0174532925199433],ID["EPSG",8801]],PARAMETER["Longitude of natural origin",3,ANGLEUNIT["degree",0.0174532925199433],ID["EPSG",8802]],PARAMETER["Scale factor at natural origin",0.9996,SCALEUNIT["unity",1],ID["EPSG",8805]],PARAMETER["False easting",500000,LENGTHUNIT["metre",1],ID["EPSG",8806]],PARAMETER["False northing",0,LENGTHUNIT["metre",1],ID["EPSG",8807]],ID["EPSG",16031]]

Testing projinfo -s NAD27 -t NAD83
Candidate operations found: 1
Note: using '--spatial-test intersects' would bring more results (10)
-------------------------------------
Operation No. 1:

unknown id, Ballpark geographic offset from NAD27 to NAD83, unknown accuracy, World, has ballpark transformation

PROJ string:
+proj=noop

WKT2:2019 string:
COORDINATEOPERATION["Ballpark geographic offset from NAD27 to NAD83",
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["Geographic2D offsets",
        ID["EPSG",9619]],
    PARAMETER["Latitude offset",0,
        ANGLEUNIT["degree",0.0174532925199433],
        ID["EPSG",8601]],
    PARAMETER["Longitude offset",0,
        ANGLEUNIT["degree",0.0174532925199433],
        ID["EPSG",8602]],
    USAGE[
        SCOPE["unknown"],
        AREA["World"],
        BBOX[-90,-180,90,180]]]

Testing projinfo -s NAD27 -t NAD83 --grid-check none --spatial-test intersects --summary --hide-ballpark
Candidate operations found: 9
DERIVED_FROM(EPSG):1313, NAD27 to NAD83 (4), 1.5 m, Canada - onshore - Alberta; British Columbia; Manitoba; New Brunswick; Newfoundland and Labrador; Northwest Territories; Nova Scotia; Nunavut; Ontario; Prince Edward Island; Quebec; Saskatchewan; Yukon; offshore east coast west of 44°W and north of 40°N.
DERIVED_FROM(EPSG):1312, NAD27 to NAD83 (3), 2.0 m, Canada - onshore - Alberta; British Columbia; Manitoba; New Brunswick; Newfoundland and Labrador; Northwest Territories; Nova Scotia; Nunavut; Ontario; Prince Edward Island; Quebec; Saskatchewan; Yukon; offshore east coast west of 44°W and north of 40°N.
DERIVED_FROM(EPSG):1241, NAD27 to NAD83 (1), 0.15 m, United States (USA) - CONUS including EEZ - onshore and offshore - Alabama; Arizona; Arkansas; California; Colorado; Connecticut; Delaware; Florida; Georgia; Idaho; Illinois; Indiana; Iowa; Kansas; Kentucky; Louisiana; Maine; Maryland; Massachusetts; Michigan; Minnesota; Mississippi; Missouri; Montana; Nebraska; Nevada; New Hampshire; New Jersey; New Mexico; New York; North Carolina; North Dakota; Ohio; Oklahoma; Oregon; Pennsylvania; Rhode Island; South Carolina; South Dakota; Tennessee; Texas; Utah; Vermont; Virginia; Washington; West Virginia; Wisconsin; Wyoming. US Gulf of Mexico (GoM) OCS.
DERIVED_FROM(EPSG):1243, NAD27 to NAD83 (2), 0.5 m, United States (USA) - Alaska including EEZ.
DERIVED_FROM(EPSG):1573, NAD27 to NAD83 (6), 1.5 m, Canada - Quebec.
EPSG:1462, NAD27 to NAD83 (5), 2.0 m, Canada - Quebec.
EPSG:9111, NAD27 to NAD83 (9), 1.5 m, Canada - Saskatchewan.
EPSG:8555, NAD27 to NAD83 (7), 0.15 m, United States (USA) - CONUS onshore - Alabama; Arizona; Arkansas; California; Colorado; Connecticut; Delaware; Florida; Georgia; Idaho; Illinois; Indiana; Iowa; Kansas; Kentucky; Louisiana; Maine; Maryland; Massachusetts; Michigan; Minnesota; Mississippi; Missouri; Montana; Nebraska; Nevada; New Hampshire; New Jersey; New Mexico; New York; North Carolina; North Dakota; Ohio; Oklahoma; Oregon; Pennsylvania; Rhode Island; South Carolina; South Dakota; Tennessee; Texas; Utah; Vermont; Virginia; Washington; West Virginia; Wisconsin; Wyoming. US Gulf of Mexico offshore continental shelf (GoM OCS).
EPSG:8549, NAD27 to NAD83 (8), 0.5 m, United States (USA) - Alaska.

Testing projinfo -s NAD27 -t NAD83 --grid-check none --spatial-test intersects
Candidate operations found: 10
-------------------------------------
Operation No. 1:

DERIVED_FROM(EPSG):1313, NAD27 to NAD83 (4), 1.5 m, Canada - onshore - Alberta; British Columbia; Manitoba; New Brunswick; Newfoundland and Labrador; Northwest Territories; Nova Scotia; Nunavut; Ontario; Prince Edward Island; Quebec; Saskatchewan; Yukon; offshore east coast west of 44°W and north of 40°N.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=hgridshift +grids=ca_nrc_ntv2_0.tif
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (4)",
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["HORIZONTAL_SHIFT_GTIFF"],
    PARAMETERFILE["Latitude and longitude difference file","ca_nrc_ntv2_0.tif"],
    OPERATIONACCURACY[1.5],
    USAGE[
        SCOPE["Transformation of coordinates at 1m to 2m level of accuracy."],
        AREA["Canada - onshore - Alberta; British Columbia; Manitoba; New Brunswick; Newfoundland and Labrador; Northwest Territories; Nova Scotia; Nunavut; Ontario; Prince Edward Island; Quebec; Saskatchewan; Yukon; offshore east coast west of 44°W and north of 40°N."],
        BBOX[40,-141.01,83.17,-44]],
    ID["DERIVED_FROM(EPSG)",1313],
    REMARK["Uses NTv2 data files. Replaces NTv1 (transformation code 1312) except in Quebec. Input expects longitudes to be positive west; EPSG GeogCRS NAD27 (code 4267) and (code 4269) have longitudes positive east. May be used as tfm to WGS 84 - see code 1693."]]

-------------------------------------
Operation No. 2:

DERIVED_FROM(EPSG):1312, NAD27 to NAD83 (3), 2.0 m, Canada - onshore - Alberta; British Columbia; Manitoba; New Brunswick; Newfoundland and Labrador; Northwest Territories; Nova Scotia; Nunavut; Ontario; Prince Edward Island; Quebec; Saskatchewan; Yukon; offshore east coast west of 44°W and north of 40°N.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=hgridshift +grids=ca_nrc_ntv1_can.tif
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (3)",
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["HORIZONTAL_SHIFT_GTIFF"],
    PARAMETERFILE["Latitude and longitude difference file","ca_nrc_ntv1_can.tif"],
    OPERATIONACCURACY[2.0],
    USAGE[
        SCOPE["Historic record only - now superseded - see remarks."],
        AREA["Canada - onshore - Alberta; British Columbia; Manitoba; New Brunswick; Newfoundland and Labrador; Northwest Territories; Nova Scotia; Nunavut; Ontario; Prince Edward Island; Quebec; Saskatchewan; Yukon; offshore east coast west of 44°W and north of 40°N."],
        BBOX[40,-141.01,83.17,-44]],
    ID["DERIVED_FROM(EPSG)",1312],
    REMARK["Uses NTv1 method. Replaced in Quebec by code 1462 and elsewhere in 1997 by NTv2 (transformation code 1313). Input expects longitudes to be positive west; EPSG GeogCRS NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east."]]

-------------------------------------
Operation No. 3:

DERIVED_FROM(EPSG):1241, NAD27 to NAD83 (1), 0.15 m, United States (USA) - CONUS including EEZ - onshore and offshore - Alabama; Arizona; Arkansas; California; Colorado; Connecticut; Delaware; Florida; Georgia; Idaho; Illinois; Indiana; Iowa; Kansas; Kentucky; Louisiana; Maine; Maryland; Massachusetts; Michigan; Minnesota; Mississippi; Missouri; Montana; Nebraska; Nevada; New Hampshire; New Jersey; New Mexico; New York; North Carolina; North Dakota; Ohio; Oklahoma; Oregon; Pennsylvania; Rhode Island; South Carolina; South Dakota; Tennessee; Texas; Utah; Vermont; Virginia; Washington; West Virginia; Wisconsin; Wyoming. US Gulf of Mexico (GoM) OCS.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=hgridshift +grids=us_noaa_conus.tif
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (1)",
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["HORIZONTAL_SHIFT_GTIFF"],
    PARAMETERFILE["Latitude and longitude difference file","us_noaa_conus.tif"],
    OPERATIONACCURACY[0.15],
    USAGE[
        SCOPE["Transformation of coordinates at 0.2m level of accuracy."],
        AREA["United States (USA) - CONUS including EEZ - onshore and offshore - Alabama; Arizona; Arkansas; California; Colorado; Connecticut; Delaware; Florida; Georgia; Idaho; Illinois; Indiana; Iowa; Kansas; Kentucky; Louisiana; Maine; Maryland; Massachusetts; Michigan; Minnesota; Mississippi; Missouri; Montana; Nebraska; Nevada; New Hampshire; New Jersey; New Mexico; New York; North Carolina; North Dakota; Ohio; Oklahoma; Oregon; Pennsylvania; Rhode Island; South Carolina; South Dakota; Tennessee; Texas; Utah; Vermont; Virginia; Washington; West Virginia; Wisconsin; Wyoming. US Gulf of Mexico (GoM) OCS."],
        BBOX[23.81,-129.16,49.38,-65.69]],
    ID["DERIVED_FROM(EPSG)",1241],
    REMARK["Uses NADCON method which expects longitudes positive west; EPSG GeogCRS NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east. For application in Gulf of Mexico refer to IOGP report 373-26."]]

-------------------------------------
Operation No. 4:

DERIVED_FROM(EPSG):1243, NAD27 to NAD83 (2), 0.5 m, United States (USA) - Alaska including EEZ.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=hgridshift +grids=us_noaa_alaska.tif
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (2)",
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["HORIZONTAL_SHIFT_GTIFF"],
    PARAMETERFILE["Latitude and longitude difference file","us_noaa_alaska.tif"],
    OPERATIONACCURACY[0.5],
    USAGE[
        SCOPE["Geodesy."],
        AREA["United States (USA) - Alaska including EEZ."],
        BBOX[47.88,167.65,74.71,-129.99]],
    ID["DERIVED_FROM(EPSG)",1243],
    REMARK["Uses NADCON method which expects longitudes positive west; EPSG GeogCRS NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east. May be used as transformation to WGS 84 - see NAD27 to WGS 84 (85) (code 15864)."]]

-------------------------------------
Operation No. 5:

DERIVED_FROM(EPSG):1573, NAD27 to NAD83 (6), 1.5 m, Canada - Quebec.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=hgridshift +grids=ca_que_mern_na27na83.tif
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (6)",
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["HORIZONTAL_SHIFT_GTIFF"],
    PARAMETERFILE["Latitude and longitude difference file","ca_que_mern_na27na83.tif"],
    OPERATIONACCURACY[1.5],
    USAGE[
        SCOPE["Transformation of coordinates at 1m to 2m level of accuracy."],
        AREA["Canada - Quebec."],
        BBOX[44.99,-79.85,62.62,-57.1]],
    ID["DERIVED_FROM(EPSG)",1573],
    REMARK["Also distributed with file name QUE27-83.gsb. Replaces NAD27 to NAD83 (5) (code 1462). Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east."]]

-------------------------------------
Operation No. 6:

EPSG:1462, NAD27 to NAD83 (5), 2.0 m, Canada - Quebec.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=hgridshift +grids=GS2783v1.QUE
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (5)",
    VERSION["SGQ-Can QC NT1"],
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["NTv1",
        ID["EPSG",9614]],
    PARAMETERFILE["Latitude and longitude difference file","GS2783v1.QUE"],
    OPERATIONACCURACY[2.0],
    USAGE[
        SCOPE["Historic record only - now superseded - see remarks."],
        AREA["Canada - Quebec."],
        BBOX[44.99,-79.85,62.62,-57.1]],
    ID["EPSG",1462],
    REMARK["Densification for Quebec of code 1312. Replaced by NAD27 to NAD83 (6) (code 1573). Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east."]]

-------------------------------------
Operation No. 7:

EPSG:9111, NAD27 to NAD83 (9), 1.5 m, Canada - Saskatchewan.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=hgridshift +grids=SK27-83.gsb
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (9)",
    VERSION["ISC-Can SK"],
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["NTv2",
        ID["EPSG",9615]],
    PARAMETERFILE["Latitude and longitude difference file","SK27-83.gsb"],
    OPERATIONACCURACY[1.5],
    USAGE[
        SCOPE["Geodesy."],
        AREA["Canada - Saskatchewan."],
        BBOX[49,-110,60.01,-101.34]],
    ID["EPSG",9111]]

-------------------------------------
Operation No. 8:

unknown id, Ballpark geographic offset from NAD27 to NAD83, unknown accuracy, World, has ballpark transformation

PROJ string:
+proj=noop

WKT2:2019 string:
COORDINATEOPERATION["Ballpark geographic offset from NAD27 to NAD83",
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["Geographic2D offsets",
        ID["EPSG",9619]],
    PARAMETER["Latitude offset",0,
        ANGLEUNIT["degree",0.0174532925199433],
        ID["EPSG",8601]],
    PARAMETER["Longitude offset",0,
        ANGLEUNIT["degree",0.0174532925199433],
        ID["EPSG",8602]],
    USAGE[
        SCOPE["unknown"],
        AREA["World"],
        BBOX[-90,-180,90,180]]]

-------------------------------------
Operation No. 9:

EPSG:8555, NAD27 to NAD83 (7), 0.15 m, United States (USA) - CONUS onshore - Alabama; Arizona; Arkansas; California; Colorado; Connecticut; Delaware; Florida; Georgia; Idaho; Illinois; Indiana; Iowa; Kansas; Kentucky; Louisiana; Maine; Maryland; Massachusetts; Michigan; Minnesota; Mississippi; Missouri; Montana; Nebraska; Nevada; New Hampshire; New Jersey; New Mexico; New York; North Carolina; North Dakota; Ohio; Oklahoma; Oregon; Pennsylvania; Rhode Island; South Carolina; South Dakota; Tennessee; Texas; Utah; Vermont; Virginia; Washington; West Virginia; Wisconsin; Wyoming. US Gulf of Mexico offshore continental shelf (GoM OCS).

PROJ string:

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (7)",
    VERSION["NGS-Usa Conus Nadcon5"],
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["NADCON5 (2D)",
        ID["EPSG",1074]],
    PARAMETERFILE["Latitude difference file","nadcon5.nad27.nad83_1986.conus.lat.trn.20160901.b"],
    PARAMETERFILE["Longitude difference file","nadcon5.nad27.nad83_1986.conus.lon.trn.20160901.b"],
    OPERATIONACCURACY[0.15],
    USAGE[
        SCOPE["Geodesy."],
        AREA["United States (USA) - CONUS onshore - Alabama; Arizona; Arkansas; California; Colorado; Connecticut; Delaware; Florida; Georgia; Idaho; Illinois; Indiana; Iowa; Kansas; Kentucky; Louisiana; Maine; Maryland; Massachusetts; Michigan; Minnesota; Mississippi; Missouri; Montana; Nebraska; Nevada; New Hampshire; New Jersey; New Mexico; New York; North Carolina; North Dakota; Ohio; Oklahoma; Oregon; Pennsylvania; Rhode Island; South Carolina; South Dakota; Tennessee; Texas; Utah; Vermont; Virginia; Washington; West Virginia; Wisconsin; Wyoming. US Gulf of Mexico offshore continental shelf (GoM OCS)."],
        BBOX[23.82,-124.79,49.38,-66.91]],
    ID["EPSG",8555],
    REMARK["NADCON5 method expects longitudes 0-360°; source and target CRS longitudes in range ±180°. Accuracy at 67% confidence level is 0.15m onshore, 1m nearshore and undetermined farther offshore. For application in Gulf of Mexico refer to IOGP report 373-26."]]

-------------------------------------
Operation No. 10:

EPSG:8549, NAD27 to NAD83 (8), 0.5 m, United States (USA) - Alaska.

PROJ string:

WKT2:2019 string:
COORDINATEOPERATION["NAD27 to NAD83 (8)",
    VERSION["NGS-Usa AK Nadcon5"],
    SOURCECRS[
        GEOGCRS["NAD27",
            DATUM["North American Datum 1927",
                ELLIPSOID["Clarke 1866",6378206.4,294.************,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4267]]],
    TARGETCRS[
        GEOGCRS["NAD83",
            DATUM["North American Datum 1983",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,2],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
            ID["EPSG",4269]]],
    METHOD["NADCON5 (2D)",
        ID["EPSG",1074]],
    PARAMETERFILE["Latitude difference file","nadcon5.nad27.nad83_1986.alaska.lat.trn.20160901.b"],
    PARAMETERFILE["Longitude difference file","nadcon5.nad27.nad83_1986.alaska.lon.trn.20160901.b"],
    OPERATIONACCURACY[0.5],
    USAGE[
        SCOPE["Geodesy."],
        AREA["United States (USA) - Alaska."],
        BBOX[51.3,172.42,71.4,-129.99]],
    ID["EPSG",8549],
    REMARK["Uses NADCON5 method which expects longitudes positive east in range 0-360°; source and target CRSs have longitudes positive east in range -180° to +180°. Accuracy at 67% confidence level is 0.5m onshore, 5m nearshore and undetermined farther offshore."]]

Testing projinfo -s EPSG:4230 -t EPSG:4258 --bbox 8,54.51,15.24,57.8 --summary
Candidate operations found: 1
Note: using '--spatial-test intersects' would bring more results (2)
EPSG:1626, ED50 to ETRS89 (4), 1.0 m, Denmark - onshore.

Testing projinfo -s EPSG:23031 -t EPSG:4326 --bbox -13.87,34.91,-7.24,41.88 --crs-extent-use none --summary
Candidate operations found: 1
Note: using '--spatial-test intersects' would bring more results (9)
unknown id, Inverse of UTM zone 31N + ED50 to WGS 84 (42), 5 m, Portugal - mainland - offshore.

Testing projinfo -s EPSG:4230 -t EPSG:4258 --area EPSG:3237 --summary
Candidate operations found: 1
Note: using '--spatial-test intersects' would bring more results (2)
EPSG:1626, ED50 to ETRS89 (4), 1.0 m, Denmark - onshore.

Testing projinfo -s EPSG:4230 -t EPSG:4258 --area 'Denmark - onshore' --summary
Candidate operations found: 1
Note: using '--spatial-test intersects' would bring more results (2)
EPSG:1626, ED50 to ETRS89 (4), 1.0 m, Denmark - onshore.

Testing projinfo -s EPSG:4230 -t EPSG:4258 --area 'Denmark -' --summary
Several candidates area of use matching provided name :
  EPSG:2531 : Denmark - Jutland and Funen - onshore.
  EPSG:2532 : Denmark - Zealand and Lolland (onshore).
  EPSG:2533 : Denmark - Bornholm onshore.
  EPSG:3237 : Denmark - onshore.
  EPSG:3471 : Denmark - onshore west of 12°E - Zealand, Jutland, Fuen and Lolland.
  EPSG:3472 : Denmark - onshore east of 12°E - Zealand and Falster, Bornholm.
  EPSG:3631 : Denmark - Jutland onshore west of 10°E.
  EPSG:3632 : Denmark - onshore - Jutland east of 9°E and Funen.
  EPSG:4575 : Denmark - onshore Jutland, Funen, Zealand and Lolland.

Testing projinfo -s EPSG:4230 -t EPSG:4258 --area no_match --summary
No area of use matching provided name

Testing projinfo -s EPSG:4230 -t EPSG:4258 --area WRONG:CODE --summary
Area of use retrieval failed: extent not found

Testing deprecated CRS: projinfo EPSG:26591
Warning: object is deprecated
Alternative non-deprecated CRS:
  EPSG:3003

PROJ.4 string:
+proj=tmerc +lat_0=0 +lon_0=9 +k=0.9996 +x_0=1500000 +y_0=0 +ellps=intl +pm=rome +units=m +no_defs +type=crs

WKT2:2019 string:
PROJCRS["Monte Mario (Rome) / Italy zone 1",
    BASEGEOGCRS["Monte Mario (Rome)",
        DATUM["Monte Mario (Rome)",
            ELLIPSOID["International 1924",6378388,297,
                LENGTHUNIT["metre",1]]],
        PRIMEM["Rome",12.4523333333333,
            ANGLEUNIT["degree",0.0174532925199433]],
        ID["EPSG",4806]],
    CONVERSION["Italy zone 1",
        METHOD["Transverse Mercator",
            ID["EPSG",9807]],
        PARAMETER["Latitude of natural origin",0,
            ANGLEUNIT["degree",0.0174532925199433],
            ID["EPSG",8801]],
        PARAMETER["Longitude of natural origin",9,
            ANGLEUNIT["degree",0.0174532925199433],
            ID["EPSG",8802]],
        PARAMETER["Scale factor at natural origin",0.9996,
            SCALEUNIT["unity",1],
            ID["EPSG",8805]],
        PARAMETER["False easting",1500000,
            LENGTHUNIT["metre",1],
            ID["EPSG",8806]],
        PARAMETER["False northing",0,
            LENGTHUNIT["metre",1],
            ID["EPSG",8807]]],
    CS[Cartesian,2],
        AXIS["easting (X)",east,
            ORDER[1],
            LENGTHUNIT["metre",1]],
        AXIS["northing (Y)",north,
            ORDER[2],
            LENGTHUNIT["metre",1]],
    USAGE[
        SCOPE["Engineering survey, topographic mapping."],
        AREA["Italy - onshore and offshore - west of 12°E."],
        BBOX[36.53,5.94,47.04,12]],
    ID["EPSG",26591]]

Testing non compliant WKT1
Warning: GEOGCS should have a PRIMEM node
Warning: Parsing error : syntax error, unexpected UNIT, expecting PRIMEM. Error occurred around:
HEROID["WGS 84",6378137,298.*********]],UNIT["degree",0.0174532925199433]]
                                        ^
PROJ.4 string:
+proj=longlat +datum=WGS84 +no_defs +type=crs

WKT2:2019 string:
GEOGCRS["WGS 84",
    DATUM["World Geodetic System 1984",
        ELLIPSOID["WGS 84",6378137,298.*********,
            LENGTHUNIT["metre",1,
                ID["EPSG",9001]]]],
    PRIMEM["Greenwich",0,
        ANGLEUNIT["degree",0.0174532925199433],
        ID["EPSG",8901]],
    CS[ellipsoidal,2],
        AXIS["longitude",east,
            ORDER[1],
            ANGLEUNIT["degree",0.0174532925199433]],
        AXIS["latitude",north,
            ORDER[2],
            ANGLEUNIT["degree",0.0174532925199433]]]

Testing CRS with towgs84: projinfo -o PROJ EPSG:25832
PROJ.4 string:
+proj=utm +zone=32 +ellps=GRS80 +towgs84=0,0,0,0,0,0,0 +units=m +no_defs +type=crs

Testing RH2000 height to SWEREF99: projinfo -s EPSG:5613 -t EPSG:4977
Candidate operations found: 2
-------------------------------------
Operation No. 1:

INVERSE(DERIVED_FROM(PROJ)):EPSG_4977_TO_EPSG_5613, Inverse of SWEREF99 to RH2000 height, unknown accuracy, Sweden - onshore.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=vgridshift +grids=se_lantmateriet_SWEN17_RH2000.tif +multiplier=1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

WKT2:2019 string:
COORDINATEOPERATION["Inverse of SWEREF99 to RH2000 height",
    SOURCECRS[
        VERTCRS["RH2000 height",
            DYNAMIC[
                FRAMEEPOCH[2000]],
            VDATUM["Rikets hojdsystem 2000"],
            CS[vertical,1],
                AXIS["gravity-related height (H)",up,
                    LENGTHUNIT["metre",1]],
            ID["EPSG",5613]]],
    TARGETCRS[
        GEOGCRS["SWEREF99",
            DATUM["SWEREF99",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,3],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["ellipsoidal height (h)",up,
                    ORDER[3],
                    LENGTHUNIT["metre",1]],
            ID["EPSG",4977]]],
    METHOD["Inverse of Geographic3D to GravityRelatedHeight (gtx)",
        ID["INVERSE(EPSG)",9665]],
    PARAMETERFILE["Geoid (height correction) model file","se_lantmateriet_SWEN17_RH2000.tif"],
    USAGE[
        SCOPE["Not known."],
        AREA["Sweden - onshore."],
        BBOX[55.28,10.93,69.07,24.17]],
    ID["INVERSE(DERIVED_FROM(PROJ))","EPSG_4977_TO_EPSG_5613"]]

-------------------------------------
Operation No. 2:

unknown id, Transformation from RH2000 height to SWEREF99 (ballpark vertical transformation, without ellipsoid height to vertical height correction), unknown accuracy, World, has ballpark transformation

PROJ string:
+proj=noop

WKT2:2019 string:
COORDINATEOPERATION["Transformation from RH2000 height to SWEREF99 (ballpark vertical transformation, without ellipsoid height to vertical height correction)",
    SOURCECRS[
        VERTCRS["RH2000 height",
            DYNAMIC[
                FRAMEEPOCH[2000]],
            VDATUM["Rikets hojdsystem 2000"],
            CS[vertical,1],
                AXIS["gravity-related height (H)",up,
                    LENGTHUNIT["metre",1]],
            ID["EPSG",5613]]],
    TARGETCRS[
        GEOGCRS["SWEREF99",
            DATUM["SWEREF99",
                ELLIPSOID["GRS 1980",6378137,298.257222101,
                    LENGTHUNIT["metre",1]]],
            PRIMEM["Greenwich",0,
                ANGLEUNIT["degree",0.0174532925199433]],
            CS[ellipsoidal,3],
                AXIS["geodetic latitude (Lat)",north,
                    ORDER[1],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["geodetic longitude (Lon)",east,
                    ORDER[2],
                    ANGLEUNIT["degree",0.0174532925199433]],
                AXIS["ellipsoidal height (h)",up,
                    ORDER[3],
                    LENGTHUNIT["metre",1]],
            ID["EPSG",4977]]],
    METHOD["Change of Vertical Unit",
        ID["EPSG",1069]],
    PARAMETER["Unit conversion scalar",1,
        SCALEUNIT["unity",1],
        ID["EPSG",1051]],
    USAGE[
        SCOPE["unknown"],
        AREA["World"],
        BBOX[-90,-180,90,180]]]

Testing NAD83(2011) + NAVD88 height -> NAD83(2011) : projinfo -s EPSG:6349 -t EPSG:6319 --spatial-test intersects -o PROJ
Candidate operations found: 3
-------------------------------------
Operation No. 1:

unknown id, Inverse of NAD83(2011) to NAVD88 height (3), 0.015 m, United States (USA) - CONUS onshore - Alabama; Arizona; Arkansas; California; Colorado; Connecticut; Delaware; Florida; Georgia; Idaho; Illinois; Indiana; Iowa; Kansas; Kentucky; Louisiana; Maine; Maryland; Massachusetts; Michigan; Minnesota; Mississippi; Missouri; Montana; Nebraska; Nevada; New Hampshire; New Jersey; New Mexico; New York; North Carolina; North Dakota; Ohio; Oklahoma; Oregon; Pennsylvania; Rhode Island; South Carolina; South Dakota; Tennessee; Texas; Utah; Vermont; Virginia; Washington; West Virginia; Wisconsin; Wyoming.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=vgridshift +grids=us_noaa_g2018u0.tif +multiplier=1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

-------------------------------------
Operation No. 2:

unknown id, Inverse of NAD83(2011) to NAVD88 height (2), 0.02 m, United States (USA) - Alaska.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=vgridshift +grids=us_noaa_g2012ba0.tif +multiplier=1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

-------------------------------------
Operation No. 3:

unknown id, Transformation from NAVD88 height to NAD83(2011) (ballpark vertical transformation, without ellipsoid height to vertical height correction), unknown accuracy, World, has ballpark transformation

PROJ string:
+proj=noop

Testing NGF IGN69 height to RGF93: projinfo -s EPSG:5720 -t EPSG:4965 -o PROJ
Candidate operations found: 2
-------------------------------------
Operation No. 1:

INVERSE(DERIVED_FROM(EPSG)):10000, Inverse of RGF93 v1 to NGF-IGN69 height (1), 0.5 m, France - mainland onshore.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=vgridshift +grids=fr_ign_RAF18.tif +multiplier=1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

-------------------------------------
Operation No. 2:

unknown id, Transformation from NGF-IGN69 height to RGF93 v1 (ballpark vertical transformation, without ellipsoid height to vertical height correction), unknown accuracy, World, has ballpark transformation

PROJ string:
+proj=noop

Testing EPSG:32631 --3d
PROJ.4 string:
+proj=utm +zone=31 +datum=WGS84 +units=m +no_defs +type=crs

WKT2:2019 string:
PROJCRS["WGS 84 / UTM zone 31N",
    BASEGEOGCRS["WGS 84",
        ENSEMBLE["World Geodetic System 1984 ensemble",
            MEMBER["World Geodetic System 1984 (Transit)"],
            MEMBER["World Geodetic System 1984 (G730)"],
            MEMBER["World Geodetic System 1984 (G873)"],
            MEMBER["World Geodetic System 1984 (G1150)"],
            MEMBER["World Geodetic System 1984 (G1674)"],
            MEMBER["World Geodetic System 1984 (G1762)"],
            MEMBER["World Geodetic System 1984 (G2139)"],
            ELLIPSOID["WGS 84",6378137,298.*********,
                LENGTHUNIT["metre",1]],
            ENSEMBLEACCURACY[2.0]],
        PRIMEM["Greenwich",0,
            ANGLEUNIT["degree",0.0174532925199433]],
        ID["EPSG",4979]],
    CONVERSION["UTM zone 31N",
        METHOD["Transverse Mercator",
            ID["EPSG",9807]],
        PARAMETER["Latitude of natural origin",0,
            ANGLEUNIT["degree",0.0174532925199433],
            ID["EPSG",8801]],
        PARAMETER["Longitude of natural origin",3,
            ANGLEUNIT["degree",0.0174532925199433],
            ID["EPSG",8802]],
        PARAMETER["Scale factor at natural origin",0.9996,
            SCALEUNIT["unity",1],
            ID["EPSG",8805]],
        PARAMETER["False easting",500000,
            LENGTHUNIT["metre",1],
            ID["EPSG",8806]],
        PARAMETER["False northing",0,
            LENGTHUNIT["metre",1],
            ID["EPSG",8807]],
        ID["EPSG",16031]],
    CS[Cartesian,3],
        AXIS["(E)",east,
            ORDER[1],
            LENGTHUNIT["metre",1,
                ID["EPSG",9001]]],
        AXIS["(N)",north,
            ORDER[2],
            LENGTHUNIT["metre",1,
                ID["EPSG",9001]]],
        AXIS["ellipsoidal height (h)",up,
            ORDER[3],
            LENGTHUNIT["metre",1,
                ID["EPSG",9001]]],
    USAGE[
        SCOPE["unknown"],
        AREA["Between 0°E and 6°E, northern hemisphere between equator and 84°N, onshore and offshore. Algeria. Andorra. Belgium. Benin. Burkina Faso. Denmark - North Sea. France. Germany - North Sea. Ghana. Luxembourg. Mali. Netherlands. Niger. Nigeria. Norway. Spain. Togo. United Kingdom (UK) - North Sea."],
        BBOX[0,0,84,6]],
    REMARK["Promoted to 3D from EPSG:32631"]]

Testing -s EPSG:32631 -t EPSG:4326+3855 --summary
Candidate operations found: 3
unknown id, Inverse of UTM zone 31N + WGS 84 to EGM2008 height (1), 1 m, World.
unknown id, Inverse of UTM zone 31N + WGS 84 to EGM2008 height (2), 0.5 m, World.
unknown id, Inverse of UTM zone 31N + Inverse of Transformation from EGM2008 height to WGS 84 (ballpark vertical transformation, without ellipsoid height to vertical height correction), unknown accuracy, World, has ballpark transformation

Testing -s EPSG:32631 -t EPSG:4326+3855 --3d --summary
Candidate operations found: 3
unknown id, Inverse of UTM zone 31N + WGS 84 to EGM2008 height (1), 1 m, World.
unknown id, Inverse of UTM zone 31N + WGS 84 to EGM2008 height (2), 0.5 m, World.
unknown id, Inverse of UTM zone 31N + Inverse of Transformation from EGM2008 height to WGS 84 (ballpark vertical transformation, without ellipsoid height to vertical height correction), unknown accuracy, World, has ballpark transformation

Testing -s EPSG:4326 -t EPSG:32661 --normalize-axis-order -o PROJ -q --single-line
+proj=pipeline +step +proj=unitconvert +xy_in=deg +xy_out=rad +step +proj=stere +lat_0=90 +lon_0=0 +k=0.994 +x_0=2000000 +y_0=2000000 +ellps=WGS84

Testing -s EPSG:4936 -t EPSG:4978 --spatial-test intersects --summary where WGS 84 to ETRS89 (2) uses a transformation method not supported by PROJ currently (time-specific Helmert), and thus must be sorted last
Candidate operations found: 2
unknown id, Ballpark geocentric translation from ETRS89 to WGS 84, unknown accuracy, World, has ballpark transformation
INVERSE(EPSG):9225, Inverse of WGS 84 to ETRS89 (2), 0.1 m, Germany - offshore North Sea. Netherlands - offshore east of 5E.

Testing -s +proj=longlat +datum=WGS84 +geoidgrids=@foo.gtx +type=crs -t EPSG:4326 -o PROJ -q
+proj=pipeline
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=vgridshift +grids=@foo.gtx +multiplier=1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s "AGD66" -t "WGS 84 (G1762)" --spatial-test intersects --summary. Should include a transformation through GDA2020
Candidate operations found: 19
unknown id, AGD66 to WGS 84 (18) + WGS 84 to WGS 84 (G1762), 5 m, Australia - offshore including EEZ.
unknown id, AGD66 to WGS 84 (16) + WGS 84 to WGS 84 (G1762), 7 m, Australia - Australian Capital Territory; New South Wales; Northern Territory; Queensland; South Australia; Tasmania; Western Australia; Victoria.
unknown id, AGD66 to WGS 84 (20) + WGS 84 to WGS 84 (G1762), 11 m, Australia - Australian Capital Territory; New South Wales; Northern Territory; Queensland; South Australia; Tasmania; Western Australia; Victoria.
unknown id, AGD66 to WGS 84 (15) + WGS 84 to WGS 84 (G1762), 5 m, Australia - Northern Territory.
unknown id, AGD66 to WGS 84 (13) + WGS 84 to WGS 84 (G1762), 5 m, Australia - New South Wales and Victoria.
unknown id, AGD66 to WGS 84 (21) + WGS 84 to WGS 84 (G1762), 7 m, Papua New Guinea - mainland onshore.
unknown id, AGD66 to WGS 84 (14) + WGS 84 to WGS 84 (G1762), 5 m, Australia - Tasmania including islands - onshore.
unknown id, AGD66 to WGS 84 (19) + WGS 84 to WGS 84 (G1762), 4 m, Papua New Guinea - Papuan fold and thrust belt.
unknown id, AGD66 to WGS 84 (22) + WGS 84 to WGS 84 (G1762), 6 m, Papua New Guinea - Papuan fold and thrust belt.
unknown id, AGD66 to WGS 84 (23) + WGS 84 to WGS 84 (G1762), 6 m, Papua New Guinea - North Fly area (between 5°04'S and 6°36'S and west of 141°32'E).
unknown id, AGD66 to WGS 84 (12) + WGS 84 to WGS 84 (G1762), 5 m, Australia - Australian Capital Territory.
unknown id, AGD66 to GDA94 (11) + GDA94 to GDA2020 (2) + Conversion from GDA2020 (geog2D) to GDA2020 (geocentric) + GDA2020 to WGS 84 (G1762) (1) + Conversion from WGS 84 (G1762) (geocentric) to WGS 84 (G1762) (geog2D), 0.75 m, Australia - Australian Capital Territory; New South Wales; Northern Territory; Queensland; South Australia; Tasmania; Western Australia; Victoria.
unknown id, AGD66 to GDA94 (12) + GDA94 to GDA2020 (2) + Conversion from GDA2020 (geog2D) to GDA2020 (geocentric) + GDA2020 to WGS 84 (G1762) (1) + Conversion from WGS 84 (G1762) (geocentric) to WGS 84 (G1762) (geog2D), 3.25 m, Australia - Australian Capital Territory; New South Wales; Northern Territory; Queensland; South Australia; Tasmania; Western Australia; Victoria.
unknown id, AGD66 to WGS 84 (17) + WGS 84 to WGS 84 (G1762), 4.9 m, Australia - Australian Capital Territory; New South Wales; Northern Territory; Queensland; South Australia; Tasmania; Western Australia; Victoria.
unknown id, AGD66 to GDA94 (9) + GDA94 to GDA2020 (2) + Conversion from GDA2020 (geog2D) to GDA2020 (geocentric) + GDA2020 to WGS 84 (G1762) (1) + Conversion from WGS 84 (G1762) (geocentric) to WGS 84 (G1762) (geog2D), 1.25 m, Australia - Northern Territory.
unknown id, AGD66 to GDA94 (4) + GDA94 to GDA2020 (2) + Conversion from GDA2020 (geog2D) to GDA2020 (geocentric) + GDA2020 to WGS 84 (G1762) (1) + Conversion from WGS 84 (G1762) (geocentric) to WGS 84 (G1762) (geog2D), 1.25 m, Australia - New South Wales and Victoria.
unknown id, AGD66 to GDA94 (8) + GDA94 to GDA2020 (2) + Conversion from GDA2020 (geog2D) to GDA2020 (geocentric) + GDA2020 to WGS 84 (G1762) (1) + Conversion from WGS 84 (G1762) (geocentric) to WGS 84 (G1762) (geog2D), 1.25 m, Australia - Tasmania including islands - onshore.
unknown id, AGD66 to GDA94 (19) + GDA94 to GDA2020 (2) + Conversion from GDA2020 (geog2D) to GDA2020 (geocentric) + GDA2020 to WGS 84 (G1762) (1) + Conversion from WGS 84 (G1762) (geocentric) to WGS 84 (G1762) (geog2D), 0.75 m, Australia - Australian Capital Territory.
unknown id, Ballpark geographic offset from AGD66 to WGS 84 (G1762), unknown accuracy, World, has ballpark transformation

Testing -s EPSG:31467 -t ETRS89 --spatial-test intersects --grid-check none --bbox 8,48,9,49 --summary. Should include both DHDN to ETRS89 (8) and DHDN to ETRS89 (BWTA2017)
Candidate operations found: 4
unknown id, Inverse of 3-degree Gauss-Kruger zone 3 + DHDN to ETRS89 (9), 0.1 m, Germany - Baden-Wurttemberg.
unknown id, Inverse of 3-degree Gauss-Kruger zone 3 + DHDN to ETRS89 (8), 0.9 m, Germany - onshore - states of Baden-Wurtemberg, Bayern, Berlin, Brandenburg, Bremen, Hamburg, Hessen, Mecklenburg-Vorpommern, Niedersachsen, Nordrhein-Westfalen, Rheinland-Pfalz, Saarland, Sachsen, Sachsen-Anhalt, Schleswig-Holstein, Thuringen.
unknown id, Inverse of 3-degree Gauss-Kruger zone 3 + DHDN to ETRS89 (3), 1 m, Germany - states of former West Germany - south of 50°20'N.
unknown id, Inverse of 3-degree Gauss-Kruger zone 3 + DHDN to ETRS89 (2), 3 m, Germany - states of former West Germany onshore - Baden-Wurtemberg, Bayern, Bremen, Hamburg, Hessen, Niedersachsen, Nordrhein-Westfalen, Rheinland-Pfalz, Saarland, Schleswig-Holstein.

Testing -s "GDA94" -t "AHD height" --grid-check none -o PROJ --spatial-test intersects
Candidate operations found: 1
-------------------------------------
Operation No. 1:

DERIVED_FROM(EPSG):5656, GDA94 to AHD height (49), 0.15 m, Australia - Australian Capital Territory; New South Wales; Northern Territory; Queensland; South Australia; Western Australia; Victoria.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +inv +proj=vgridshift +grids=au_ga_AUSGeoid09_V1.01.tif +multiplier=1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s "GDA2020" -t "AHD height" --grid-check none -o PROJ --spatial-test intersects
Candidate operations found: 1
-------------------------------------
Operation No. 1:

DERIVED_FROM(EPSG):8451, GDA2020 to AHD height (1), 0.15 m, Australia - Australian Capital Territory, New South Wales, Northern Territory, Queensland, South Australia, Tasmania, Western Australia and Victoria - onshore. Christmas Island - onshore. Cocos and Keeling Islands - onshore.

PROJ string:
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +inv +proj=vgridshift +grids=au_ga_AUSGeoid2020_20180201.tif
        +multiplier=1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -k ellipsoid WGS84
PROJ string:
+ellps=WGS84

WKT2:2019 string:
ELLIPSOID["WGS 84",6378137,298.*********,
    LENGTHUNIT["metre",1],
    ID["EPSG",7030]]

Testing -k ellipsoid EPSG:7030
PROJ string:
+ellps=WGS84

WKT2:2019 string:
ELLIPSOID["WGS 84",6378137,298.*********,
    LENGTHUNIT["metre",1],
    ID["EPSG",7030]]

Testing -k datum WGS84
WKT2:2019 string:
DATUM["World Geodetic System 1984",
    ELLIPSOID["WGS 84",6378137,298.*********,
        LENGTHUNIT["metre",1]],
    ID["EPSG",6326]]

Testing -k datum EPSG:6326
WKT2:2019 string:
DATUM["World Geodetic System 1984",
    ELLIPSOID["WGS 84",6378137,298.*********,
        LENGTHUNIT["metre",1]],
    ID["EPSG",6326]]

Testing -k ensemble WGS84
WKT2:2019 string:
ENSEMBLE["World Geodetic System 1984 ensemble",
    MEMBER["World Geodetic System 1984 (Transit)",
        ID["EPSG",1166]],
    MEMBER["World Geodetic System 1984 (G730)",
        ID["EPSG",1152]],
    MEMBER["World Geodetic System 1984 (G873)",
        ID["EPSG",1153]],
    MEMBER["World Geodetic System 1984 (G1150)",
        ID["EPSG",1154]],
    MEMBER["World Geodetic System 1984 (G1674)",
        ID["EPSG",1155]],
    MEMBER["World Geodetic System 1984 (G1762)",
        ID["EPSG",1156]],
    MEMBER["World Geodetic System 1984 (G2139)",
        ID["EPSG",1309]],
    ELLIPSOID["WGS 84",6378137,298.*********,
        LENGTHUNIT["metre",1],
        ID["EPSG",7030]],
    ENSEMBLEACCURACY[2.0],
    ID["EPSG",6326]]

Testing -k operation EPSG:8457 -o PROJ -q
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=push +v_3
  +step +proj=cart +ellps=bessel
  +step +proj=helmert +x=674.374 +y=15.056 +z=405.346
  +step +inv +proj=cart +ellps=WGS84
  +step +proj=pop +v_3
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing D_WGS_1984
WKT2:2019 string:
ENSEMBLE["World Geodetic System 1984 ensemble",
    MEMBER["World Geodetic System 1984 (Transit)",
        ID["EPSG",1166]],
    MEMBER["World Geodetic System 1984 (G730)",
        ID["EPSG",1152]],
    MEMBER["World Geodetic System 1984 (G873)",
        ID["EPSG",1153]],
    MEMBER["World Geodetic System 1984 (G1150)",
        ID["EPSG",1154]],
    MEMBER["World Geodetic System 1984 (G1674)",
        ID["EPSG",1155]],
    MEMBER["World Geodetic System 1984 (G1762)",
        ID["EPSG",1156]],
    MEMBER["World Geodetic System 1984 (G2139)",
        ID["EPSG",1309]],
    ELLIPSOID["WGS 84",6378137,298.*********,
        LENGTHUNIT["metre",1],
        ID["EPSG",7030]],
    ENSEMBLEACCURACY[2.0],
    ID["EPSG",6326]]

Testing -k datum D_WGS_1984
WKT2:2019 string:
DATUM["World Geodetic System 1984",
    ELLIPSOID["WGS 84",6378137,298.*********,
        LENGTHUNIT["metre",1]],
    ID["EPSG",6326]]

Testing --accuracy 0.05 -s EPSG:4326 -t EPSG:4258
Candidate operations found: 0

Testing -s NZGD2000 -t ITRF96 -o PROJ -q
+proj=pipeline
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=axisswap +order=2,1
  +step +proj=defmodel +model=nz_linz_nzgd2000-20180701.json
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=rad +xy_out=deg

Testing -s NZGD2000 -t ITRF97 -o PROJ -q
+proj=pipeline
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=axisswap +order=2,1
  +step +proj=defmodel +model=nz_linz_nzgd2000-20180701.json
  +step +proj=cart +ellps=GRS80
  +step +inv +proj=helmert +x=0 +y=-0.00051 +z=0.01553 +rx=-0.00016508
        +ry=0.00026897 +rz=5.984e-05 +s=-0.00151099 +dx=0.00069 +dy=-0.0001
        +dz=0.00186 +drx=-1.347e-05 +dry=1.514e-05 +drz=-2.7e-07 +ds=-0.00019201
        +t_epoch=2000 +convention=position_vector
  +step +inv +proj=cart +ellps=GRS80
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s NZGD2000 -t ITRF2000 -o PROJ -q
+proj=pipeline
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=axisswap +order=2,1
  +step +proj=defmodel +model=nz_linz_nzgd2000-20180701.json
  +step +proj=cart +ellps=GRS80
  +step +inv +proj=helmert +x=0.0067 +y=0.00379 +z=-0.00717 +rx=-0.00016508
        +ry=0.00026897 +rz=0.00011984 +s=6.901e-05 +dx=0.00069 +dy=-0.0007
        +dz=0.00046 +drx=-1.347e-05 +dry=1.514e-05 +drz=1.973e-05 +ds=-0.00018201
        +t_epoch=2000 +convention=position_vector
  +step +inv +proj=cart +ellps=GRS80
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s NZGD2000 -t ITRF2005 -o PROJ -q
+proj=pipeline
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=axisswap +order=2,1
  +step +proj=defmodel +model=nz_linz_nzgd2000-20180701.json
  +step +proj=cart +ellps=GRS80
  +step +inv +proj=helmert +x=0.0068 +y=0.00299 +z=-0.01297 +rx=-0.00016508
        +ry=0.00026897 +rz=0.00011984 +s=0.00046901 +dx=0.00049 +dy=-0.0006
        +dz=-0.00134 +drx=-1.347e-05 +dry=1.514e-05 +drz=1.973e-05
        +ds=-0.00010201 +t_epoch=2000 +convention=position_vector
  +step +inv +proj=cart +ellps=GRS80
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s NZGD2000 -t ITRF2008 -o PROJ -q
+proj=pipeline
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=axisswap +order=2,1
  +step +proj=defmodel +model=nz_linz_nzgd2000-20180701.json
  +step +proj=cart +ellps=GRS80
  +step +inv +proj=helmert +x=0.0048 +y=0.00209 +z=-0.01767 +rx=-0.00016508
        +ry=0.00026897 +rz=0.00011984 +s=0.00140901 +dx=0.00079 +dy=-0.0006
        +dz=-0.00134 +drx=-1.347e-05 +dry=1.514e-05 +drz=1.973e-05
        +ds=-0.00010201 +t_epoch=2000 +convention=position_vector
  +step +inv +proj=cart +ellps=GRS80
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s NZGD2000 -t ITRF2014 -o PROJ -q
+proj=pipeline
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=axisswap +order=2,1
  +step +proj=defmodel +model=nz_linz_nzgd2000-20180701.json
  +step +proj=cart +ellps=GRS80
  +step +inv +proj=helmert +x=0.0064 +y=0.00399 +z=-0.01427 +rx=-0.00016508
        +ry=0.00026897 +rz=0.00011984 +s=0.00108901 +dx=0.00079 +dy=-0.0006
        +dz=-0.00144 +drx=-1.347e-05 +dry=1.514e-05 +drz=1.973e-05 +ds=-7.201e-05
        +t_epoch=2000 +convention=position_vector
  +step +inv +proj=cart +ellps=GRS80
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s "KKJ / Finland Uniform Coordinate System" -t "ETRS89 / TM35FIN(E,N)" --grid-check none -o PROJ -q
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=tinshift +file=fi_nls_ykj_etrs35fin.json

Testing -s KKJ -t ETRS89 -o PROJ --grid-check none -q
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=tmerc +lat_0=0 +lon_0=27 +k=1 +x_0=3500000 +y_0=0 +ellps=intl
  +step +proj=tinshift +file=fi_nls_ykj_etrs35fin.json
  +step +inv +proj=utm +zone=35 +ellps=GRS80
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s "KKJ + N43 height" -t "KKJ + N60 height" --grid-check none -o PROJ -q
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=tmerc +lat_0=0 +lon_0=27 +k=1 +x_0=3500000 +y_0=0 +ellps=intl
  +step +proj=tinshift +file=fi_nls_n43_n60.json
  +step +inv +proj=tmerc +lat_0=0 +lon_0=27 +k=1 +x_0=3500000 +y_0=0 +ellps=intl
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s "KKJ + N60 height" -t "KKJ + N2000 height" --grid-check none -o PROJ -q
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=tmerc +lat_0=0 +lon_0=27 +k=1 +x_0=3500000 +y_0=0 +ellps=intl
  +step +proj=tinshift +file=fi_nls_n60_n2000.json
  +step +inv +proj=tmerc +lat_0=0 +lon_0=27 +k=1 +x_0=3500000 +y_0=0 +ellps=intl
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s "KKJ + N43 height" -t "ETRS89 + N2000 height" --grid-check none -o PROJ -q
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=unitconvert +xy_in=deg +xy_out=rad
  +step +proj=tmerc +lat_0=0 +lon_0=27 +k=1 +x_0=3500000 +y_0=0 +ellps=intl
  +step +proj=tinshift +file=fi_nls_n43_n60.json
  +step +proj=tinshift +file=fi_nls_n60_n2000.json
  +step +proj=tinshift +file=fi_nls_ykj_etrs35fin.json
  +step +inv +proj=utm +zone=35 +ellps=GRS80
  +step +proj=unitconvert +xy_in=rad +xy_out=deg
  +step +proj=axisswap +order=2,1

Testing -s "KKJ / Finland Uniform Coordinate System + N43 height" -t "ETRS89 / TM35FIN(E,N) + N2000 height" --grid-check none -o PROJ -q
+proj=pipeline
  +step +proj=axisswap +order=2,1
  +step +proj=tinshift +file=fi_nls_n43_n60.json
  +step +proj=tinshift +file=fi_nls_n60_n2000.json
  +step +proj=tinshift +file=fi_nls_ykj_etrs35fin.json

Testing -s "ETRS89 / TM35FIN(E,N) + N2000 height" -t "KKJ / Finland Uniform Coordinate System + N43 height" --grid-check none -o PROJ -q
+proj=pipeline
  +step +inv +proj=tinshift +file=fi_nls_ykj_etrs35fin.json
  +step +inv +proj=tinshift +file=fi_nls_n60_n2000.json
  +step +inv +proj=tinshift +file=fi_nls_n43_n60.json
  +step +proj=axisswap +order=2,1

Testing NKG: -s EPSG:7789 -t EPSG:4936 --area  EPSG:1080 --summary --hide-ballpark
Candidate operations found: 1
Note: using '--spatial-test intersects' would bring more results (2)
NKG:ITRF2014_TO_DK, ITRF2014 to ETRS89(DK), 0.01 m, Denmark - onshore and offshore.

Testing projinfo --dump-db-structure | head -n 5
CREATE TABLE metadata(
    key TEXT NOT NULL PRIMARY KEY CHECK (length(key) >= 1),
    value TEXT NOT NULL
) WITHOUT ROWID;
CREATE TABLE unit_of_measure(

Testing projinfo --dump-db-structure --output-id HOBU:XXXX EPSG:4326 | tail -n 4
INSERT INTO metadata VALUES('DATABASE.LAYOUT.VERSION.MAJOR',1);
INSERT INTO metadata VALUES('DATABASE.LAYOUT.VERSION.MINOR',2);
INSERT INTO geodetic_crs VALUES('HOBU','XXXX','WGS 84','','geographic 2D','EPSG','6422','EPSG','6326',NULL,0);
INSERT INTO usage VALUES('HOBU','USAGE_GEODETIC_CRS_XXXX','geodetic_crs','HOBU','XXXX','EPSG','1262','EPSG','1183');

Testing PROJ_AUX_DB environment variable
PROJ.4 string:
+proj=longlat +datum=WGS84 +no_defs +type=crs

WKT2:2019 string:
GEOGCRS["WGS 84",
    ENSEMBLE["World Geodetic System 1984 ensemble",
        MEMBER["World Geodetic System 1984 (Transit)"],
        MEMBER["World Geodetic System 1984 (G730)"],
        MEMBER["World Geodetic System 1984 (G873)"],
        MEMBER["World Geodetic System 1984 (G1150)"],
        MEMBER["World Geodetic System 1984 (G1674)"],
        MEMBER["World Geodetic System 1984 (G1762)"],
        MEMBER["World Geodetic System 1984 (G2139)"],
        ELLIPSOID["WGS 84",6378137,298.*********,
            LENGTHUNIT["metre",1]],
        ENSEMBLEACCURACY[2.0]],
    PRIMEM["Greenwich",0,
        ANGLEUNIT["degree",0.0174532925199433]],
    CS[ellipsoidal,2],
        AXIS["geodetic latitude (Lat)",north,
            ORDER[1],
            ANGLEUNIT["degree",0.0174532925199433]],
        AXIS["geodetic longitude (Lon)",east,
            ORDER[2],
            ANGLEUNIT["degree",0.0174532925199433]],
    USAGE[
        SCOPE["Horizontal component of 3D system."],
        AREA["World."],
        BBOX[-90,-180,90,180]],
    ID["HOBU","XXXX"]]

Testing -s EPSG:23030 -t EPSG:25830 --bbox -6,40,-5,41 --grid-check known_available --hide-ballpark --summary
Checks that ED50 to ETRS89 (12) is in the output (superseded transformation, but replacements has unknown grid)
Candidate operations found: 2
unknown id, Inverse of UTM zone 30N + ED50 to ETRS89 (12) + UTM zone 30N, 0.2 m, Spain - mainland, Balearic Islands, Ceuta and Melila - onshore.
unknown id, Inverse of UTM zone 30N + ED50 to ETRS89 (7) + UTM zone 30N, 1.5 m, Spain - onshore mainland except northwest (north of 41°30'N and west of 4°30'W).

Testing --list-crs | grep "EPSG:32632\|ESRI:103668\|OGC" | sort
EPSG:32632 "WGS 84 / UTM zone 32N"
ESRI:103668 "NAD_1983_HARN_Adj_MN_Ramsey_Meters"
OGC:CRS27 "NAD27 (CRS27)"
OGC:CRS83 "NAD83 (CRS83)"
OGC:CRS84 "WGS 84 (CRS84)"

Testing --list-crs --authority OGC,EPSG | grep "EPSG:4326\|OGC" | sort
EPSG:4326 "WGS 84"
OGC:CRS27 "NAD27 (CRS27)"
OGC:CRS83 "NAD83 (CRS83)"
OGC:CRS84 "WGS 84 (CRS84)"

Testing --list-crs | grep deprecated | sort

Testing --list-crs vertical --bbox 0,40,1,41 --spatial-test intersects | grep "Alicante\|NAVD88" | sort
EPSG:5782 "Alicante height"

Testing --list-crs vertical --bbox -10,35,5,45 --spatial-test contains | grep "Alicante\|NAVD88" | sort
EPSG:5782 "Alicante height"

Testing --list-crs --area Spain --spatial-test intersects | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort
EPSG:4258 "ETRS89"
EPSG:9398 "Tenerife height"
EPSG:9505 "ETRS89 + Alicante height"

Testing --list-crs --area Spain --spatial-test contains | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort
EPSG:9398 "Tenerife height"
EPSG:9505 "ETRS89 + Alicante height"

Testing --list-crs --area Spain | grep "EPSG:9505\|EPSG:9398\|EPSG:4258\|EPSG:5703" | sort
EPSG:9398 "Tenerife height"
EPSG:9505 "ETRS89 + Alicante height"

Testing --list-crs geodetic | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort
EPSG:4326 "WGS 84"
EPSG:4978 "WGS 84"
EPSG:4979 "WGS 84"

Testing --list-crs geographic | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort
EPSG:4326 "WGS 84"
EPSG:4979 "WGS 84"

Testing --list-crs geocentric,geographic_3d | grep "EPSG:4326\|EPSG:4979\|EPSG:4978" | sort
EPSG:4978 "WGS 84"
EPSG:4979 "WGS 84"

Testing --list-crs geographic_2d,allow_deprecated --bbox -100,40,-90,41 --spatial-test intersects | grep deprecated | grep "NAD83(FBN)\|GCS_IGS08" | sort
EPSG:8449 "NAD83(FBN)" [deprecated]
ESRI:104010 "GCS_IGS08" [deprecated]

Testing --list-crs projected --bbox -100,40,-90,41 --spatial-test intersects | grep "(2011).*Missouri East\|York" | sort
EPSG:6512 "NAD83(2011) / Missouri East"

Testing --list-crs projected --area France | grep "RGF93 v1 / Lambert-93\|UTM zone 11N" | sort
EPSG:2154 "RGF93 v1 / Lambert-93"

