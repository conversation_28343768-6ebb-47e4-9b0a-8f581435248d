# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@


VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
noinst_PROGRAMS = pj_phi2_test$(EXEEXT) \
	proj_errno_string_test$(EXEEXT) proj_angular_io_test$(EXEEXT) \
	proj_context_test$(EXEEXT) test_cpp_api$(EXEEXT) \
	gie_self_tests$(EXEEXT) include_proj_h_from_c$(EXEEXT) \
	test_network$(EXEEXT) test_defmodel$(EXEEXT) \
	test_tinshift$(EXEEXT) test_misc$(EXEEXT) test_fork$(EXEEXT)
subdir = test/unit
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_cflags_warn_all.m4 \
	$(top_srcdir)/m4/ax_check_compile_flag.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx_11.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/m4/pkg.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(noinst_HEADERS) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/src/proj_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
PROGRAMS = $(noinst_PROGRAMS)
am_gie_self_tests_OBJECTS = gie_self_tests.$(OBJEXT) main.$(OBJEXT)
gie_self_tests_OBJECTS = $(am_gie_self_tests_OBJECTS)
gie_self_tests_DEPENDENCIES = ../../src/libproj.la
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
am_include_proj_h_from_c_OBJECTS = include_proj_h_from_c.$(OBJEXT)
include_proj_h_from_c_OBJECTS = $(am_include_proj_h_from_c_OBJECTS)
include_proj_h_from_c_LDADD = $(LDADD)
am_pj_phi2_test_OBJECTS = pj_phi2_test.$(OBJEXT) main.$(OBJEXT)
pj_phi2_test_OBJECTS = $(am_pj_phi2_test_OBJECTS)
pj_phi2_test_DEPENDENCIES = ../../src/libproj.la
am_proj_angular_io_test_OBJECTS = proj_angular_io_test.$(OBJEXT) \
	main.$(OBJEXT)
proj_angular_io_test_OBJECTS = $(am_proj_angular_io_test_OBJECTS)
proj_angular_io_test_DEPENDENCIES = ../../src/libproj.la
am_proj_context_test_OBJECTS = proj_context_test.$(OBJEXT) \
	main.$(OBJEXT)
proj_context_test_OBJECTS = $(am_proj_context_test_OBJECTS)
proj_context_test_DEPENDENCIES = ../../src/libproj.la
am_proj_errno_string_test_OBJECTS = proj_errno_string_test.$(OBJEXT) \
	main.$(OBJEXT)
proj_errno_string_test_OBJECTS = $(am_proj_errno_string_test_OBJECTS)
proj_errno_string_test_DEPENDENCIES = ../../src/libproj.la
am_test_cpp_api_OBJECTS = test_util.$(OBJEXT) test_common.$(OBJEXT) \
	test_crs.$(OBJEXT) test_metadata.$(OBJEXT) test_io.$(OBJEXT) \
	test_operation.$(OBJEXT) test_operationfactory.$(OBJEXT) \
	test_datum.$(OBJEXT) test_factory.$(OBJEXT) \
	test_c_api.$(OBJEXT) test_grids.$(OBJEXT) main.$(OBJEXT)
test_cpp_api_OBJECTS = $(am_test_cpp_api_OBJECTS)
test_cpp_api_DEPENDENCIES = ../../src/libproj.la
am_test_defmodel_OBJECTS = test_defmodel.$(OBJEXT) main.$(OBJEXT)
test_defmodel_OBJECTS = $(am_test_defmodel_OBJECTS)
test_defmodel_DEPENDENCIES = ../../src/libproj.la
am_test_fork_OBJECTS = test_fork.$(OBJEXT)
test_fork_OBJECTS = $(am_test_fork_OBJECTS)
test_fork_DEPENDENCIES = ../../src/libproj.la
am_test_misc_OBJECTS = test_misc.$(OBJEXT) main.$(OBJEXT)
test_misc_OBJECTS = $(am_test_misc_OBJECTS)
test_misc_DEPENDENCIES = ../../src/libproj.la
am_test_network_OBJECTS = test_network-test_network.$(OBJEXT) \
	test_network-main.$(OBJEXT)
test_network_OBJECTS = $(am_test_network_OBJECTS)
test_network_DEPENDENCIES = ../../src/libproj.la
test_network_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(test_network_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
am_test_tinshift_OBJECTS = test_tinshift.$(OBJEXT) main.$(OBJEXT)
test_tinshift_OBJECTS = $(am_test_tinshift_OBJECTS)
test_tinshift_DEPENDENCIES = ../../src/libproj.la
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)/src
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/gie_self_tests.Po \
	./$(DEPDIR)/include_proj_h_from_c.Po ./$(DEPDIR)/main.Po \
	./$(DEPDIR)/pj_phi2_test.Po \
	./$(DEPDIR)/proj_angular_io_test.Po \
	./$(DEPDIR)/proj_context_test.Po \
	./$(DEPDIR)/proj_errno_string_test.Po \
	./$(DEPDIR)/test_c_api.Po ./$(DEPDIR)/test_common.Po \
	./$(DEPDIR)/test_crs.Po ./$(DEPDIR)/test_datum.Po \
	./$(DEPDIR)/test_defmodel.Po ./$(DEPDIR)/test_factory.Po \
	./$(DEPDIR)/test_fork.Po ./$(DEPDIR)/test_grids.Po \
	./$(DEPDIR)/test_io.Po ./$(DEPDIR)/test_metadata.Po \
	./$(DEPDIR)/test_misc.Po ./$(DEPDIR)/test_network-main.Po \
	./$(DEPDIR)/test_network-test_network.Po \
	./$(DEPDIR)/test_operation.Po \
	./$(DEPDIR)/test_operationfactory.Po \
	./$(DEPDIR)/test_tinshift.Po ./$(DEPDIR)/test_util.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_@AM_V@)
am__v_CXX_ = $(am__v_CXX_@AM_DEFAULT_V@)
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_@AM_V@)
am__v_CXXLD_ = $(am__v_CXXLD_@AM_DEFAULT_V@)
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
SOURCES = $(gie_self_tests_SOURCES) $(include_proj_h_from_c_SOURCES) \
	$(pj_phi2_test_SOURCES) $(proj_angular_io_test_SOURCES) \
	$(proj_context_test_SOURCES) $(proj_errno_string_test_SOURCES) \
	$(test_cpp_api_SOURCES) $(test_defmodel_SOURCES) \
	$(test_fork_SOURCES) $(test_misc_SOURCES) \
	$(test_network_SOURCES) $(test_tinshift_SOURCES)
DIST_SOURCES = $(gie_self_tests_SOURCES) \
	$(include_proj_h_from_c_SOURCES) $(pj_phi2_test_SOURCES) \
	$(proj_angular_io_test_SOURCES) $(proj_context_test_SOURCES) \
	$(proj_errno_string_test_SOURCES) $(test_cpp_api_SOURCES) \
	$(test_defmodel_SOURCES) $(test_fork_SOURCES) \
	$(test_misc_SOURCES) $(test_network_SOURCES) \
	$(test_tinshift_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
HEADERS = $(noinst_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CURL_CFLAGS = @CURL_CFLAGS@
CURL_ENABLED_FLAGS = @CURL_ENABLED_FLAGS@
CURL_LIBS = @CURL_LIBS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CXX_WFLAGS = @CXX_WFLAGS@
CYGPATH_W = @CYGPATH_W@
C_WFLAGS = @C_WFLAGS@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
EXTRA_LIBS = @EXTRA_LIBS@
FGREP = @FGREP@
FLTO_FLAG = @FLTO_FLAG@
GREP = @GREP@
GTEST_CFLAGS = @GTEST_CFLAGS@
GTEST_LIBS = @GTEST_LIBS@
HAVE_CXX11 = @HAVE_CXX11@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBCURL_CONFIG = @LIBCURL_CONFIG@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
MUTEX_SETTING = @MUTEX_SETTING@
NM = @NM@
NMEDIT = @NMEDIT@
NO_ZERO_AS_NULL_POINTER_CONSTANT_FLAG = @NO_ZERO_AS_NULL_POINTER_CONSTANT_FLAG@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKG_CONFIG = @PKG_CONFIG@
PROJ_LIB_ENV_VAR_TRIED_LAST_FLAGS = @PROJ_LIB_ENV_VAR_TRIED_LAST_FLAGS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SQLITE3_CFLAGS = @SQLITE3_CFLAGS@
SQLITE3_CHECK = @SQLITE3_CHECK@
SQLITE3_LIBS = @SQLITE3_LIBS@
STRIP = @STRIP@
THREAD_LIB = @THREAD_LIB@
TIFF_CFLAGS = @TIFF_CFLAGS@
TIFF_ENABLED_FLAGS = @TIFF_ENABLED_FLAGS@
TIFF_LIBS = @TIFF_LIBS@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AUTOMAKE_OPTIONS = subdir-objects
EXTRA_DIST = CMakeLists.txt
noinst_HEADERS = gtest_include.h test_primitives.hpp
AM_CPPFLAGS = -I$(top_srcdir)/src -I$(top_srcdir)/include -I$(top_srcdir)/test @GTEST_CFLAGS@ @SQLITE3_CFLAGS@ -DMUTEX_@MUTEX_SETTING@
AM_CXXFLAGS = @CXX_WFLAGS@ @NO_ZERO_AS_NULL_POINTER_CONSTANT_FLAG@
pj_phi2_test_SOURCES = pj_phi2_test.cpp main.cpp
pj_phi2_test_LDADD = ../../src/libproj.la @GTEST_LIBS@
proj_errno_string_test_SOURCES = proj_errno_string_test.cpp main.cpp
proj_errno_string_test_LDADD = ../../src/libproj.la @GTEST_LIBS@
proj_angular_io_test_SOURCES = proj_angular_io_test.cpp main.cpp
proj_angular_io_test_LDADD = ../../src/libproj.la @GTEST_LIBS@
proj_context_test_SOURCES = proj_context_test.cpp main.cpp
proj_context_test_LDADD = ../../src/libproj.la @GTEST_LIBS@
test_cpp_api_SOURCES = test_util.cpp \
			test_common.cpp \
			test_crs.cpp \
			test_metadata.cpp \
			test_io.cpp \
			test_operation.cpp \
			test_operationfactory.cpp \
			test_datum.cpp \
			test_factory.cpp \
			test_c_api.cpp \
			test_grids.cpp \
			main.cpp

test_cpp_api_LDADD = ../../src/libproj.la @GTEST_LIBS@ @SQLITE3_LIBS@
gie_self_tests_SOURCES = gie_self_tests.cpp main.cpp
gie_self_tests_LDADD = ../../src/libproj.la @GTEST_LIBS@ @SQLITE3_LIBS@
include_proj_h_from_c_SOURCES = include_proj_h_from_c.c
test_network_SOURCES = test_network.cpp main.cpp
test_network_CXXFLAGS = @CURL_CFLAGS@ @CURL_ENABLED_FLAGS@
test_network_LDADD = ../../src/libproj.la @GTEST_LIBS@ @SQLITE3_LIBS@ @CURL_LIBS@
test_defmodel_SOURCES = test_defmodel.cpp main.cpp
test_defmodel_LDADD = ../../src/libproj.la @GTEST_LIBS@
test_tinshift_SOURCES = test_tinshift.cpp main.cpp
test_tinshift_LDADD = ../../src/libproj.la @GTEST_LIBS@
test_misc_SOURCES = test_misc.cpp main.cpp
test_misc_LDADD = ../../src/libproj.la @GTEST_LIBS@
test_fork_SOURCES = test_fork.c
test_fork_LDADD = ../../src/libproj.la @THREAD_LIB@
all: all-am

.SUFFIXES:
.SUFFIXES: .c .cpp .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu test/unit/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu test/unit/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

gie_self_tests$(EXEEXT): $(gie_self_tests_OBJECTS) $(gie_self_tests_DEPENDENCIES) $(EXTRA_gie_self_tests_DEPENDENCIES) 
	@rm -f gie_self_tests$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(gie_self_tests_OBJECTS) $(gie_self_tests_LDADD) $(LIBS)

include_proj_h_from_c$(EXEEXT): $(include_proj_h_from_c_OBJECTS) $(include_proj_h_from_c_DEPENDENCIES) $(EXTRA_include_proj_h_from_c_DEPENDENCIES) 
	@rm -f include_proj_h_from_c$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(include_proj_h_from_c_OBJECTS) $(include_proj_h_from_c_LDADD) $(LIBS)

pj_phi2_test$(EXEEXT): $(pj_phi2_test_OBJECTS) $(pj_phi2_test_DEPENDENCIES) $(EXTRA_pj_phi2_test_DEPENDENCIES) 
	@rm -f pj_phi2_test$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(pj_phi2_test_OBJECTS) $(pj_phi2_test_LDADD) $(LIBS)

proj_angular_io_test$(EXEEXT): $(proj_angular_io_test_OBJECTS) $(proj_angular_io_test_DEPENDENCIES) $(EXTRA_proj_angular_io_test_DEPENDENCIES) 
	@rm -f proj_angular_io_test$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(proj_angular_io_test_OBJECTS) $(proj_angular_io_test_LDADD) $(LIBS)

proj_context_test$(EXEEXT): $(proj_context_test_OBJECTS) $(proj_context_test_DEPENDENCIES) $(EXTRA_proj_context_test_DEPENDENCIES) 
	@rm -f proj_context_test$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(proj_context_test_OBJECTS) $(proj_context_test_LDADD) $(LIBS)

proj_errno_string_test$(EXEEXT): $(proj_errno_string_test_OBJECTS) $(proj_errno_string_test_DEPENDENCIES) $(EXTRA_proj_errno_string_test_DEPENDENCIES) 
	@rm -f proj_errno_string_test$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(proj_errno_string_test_OBJECTS) $(proj_errno_string_test_LDADD) $(LIBS)

test_cpp_api$(EXEEXT): $(test_cpp_api_OBJECTS) $(test_cpp_api_DEPENDENCIES) $(EXTRA_test_cpp_api_DEPENDENCIES) 
	@rm -f test_cpp_api$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(test_cpp_api_OBJECTS) $(test_cpp_api_LDADD) $(LIBS)

test_defmodel$(EXEEXT): $(test_defmodel_OBJECTS) $(test_defmodel_DEPENDENCIES) $(EXTRA_test_defmodel_DEPENDENCIES) 
	@rm -f test_defmodel$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(test_defmodel_OBJECTS) $(test_defmodel_LDADD) $(LIBS)

test_fork$(EXEEXT): $(test_fork_OBJECTS) $(test_fork_DEPENDENCIES) $(EXTRA_test_fork_DEPENDENCIES) 
	@rm -f test_fork$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(test_fork_OBJECTS) $(test_fork_LDADD) $(LIBS)

test_misc$(EXEEXT): $(test_misc_OBJECTS) $(test_misc_DEPENDENCIES) $(EXTRA_test_misc_DEPENDENCIES) 
	@rm -f test_misc$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(test_misc_OBJECTS) $(test_misc_LDADD) $(LIBS)

test_network$(EXEEXT): $(test_network_OBJECTS) $(test_network_DEPENDENCIES) $(EXTRA_test_network_DEPENDENCIES) 
	@rm -f test_network$(EXEEXT)
	$(AM_V_CXXLD)$(test_network_LINK) $(test_network_OBJECTS) $(test_network_LDADD) $(LIBS)

test_tinshift$(EXEEXT): $(test_tinshift_OBJECTS) $(test_tinshift_DEPENDENCIES) $(EXTRA_test_tinshift_DEPENDENCIES) 
	@rm -f test_tinshift$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(test_tinshift_OBJECTS) $(test_tinshift_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/gie_self_tests.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/include_proj_h_from_c.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pj_phi2_test.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/proj_angular_io_test.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/proj_context_test.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/proj_errno_string_test.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_c_api.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_common.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_crs.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_datum.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_defmodel.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_factory.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_fork.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_grids.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_io.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_metadata.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_misc.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_network-main.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_network-test_network.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_operation.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_operationfactory.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_tinshift.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/test_util.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

.cpp.o:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ $<

.cpp.obj:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cpp.lo:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCXX_TRUE@	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LTCXXCOMPILE) -c -o $@ $<

test_network-test_network.o: test_network.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -MT test_network-test_network.o -MD -MP -MF $(DEPDIR)/test_network-test_network.Tpo -c -o test_network-test_network.o `test -f 'test_network.cpp' || echo '$(srcdir)/'`test_network.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/test_network-test_network.Tpo $(DEPDIR)/test_network-test_network.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='test_network.cpp' object='test_network-test_network.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -c -o test_network-test_network.o `test -f 'test_network.cpp' || echo '$(srcdir)/'`test_network.cpp

test_network-test_network.obj: test_network.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -MT test_network-test_network.obj -MD -MP -MF $(DEPDIR)/test_network-test_network.Tpo -c -o test_network-test_network.obj `if test -f 'test_network.cpp'; then $(CYGPATH_W) 'test_network.cpp'; else $(CYGPATH_W) '$(srcdir)/test_network.cpp'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/test_network-test_network.Tpo $(DEPDIR)/test_network-test_network.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='test_network.cpp' object='test_network-test_network.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -c -o test_network-test_network.obj `if test -f 'test_network.cpp'; then $(CYGPATH_W) 'test_network.cpp'; else $(CYGPATH_W) '$(srcdir)/test_network.cpp'; fi`

test_network-main.o: main.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -MT test_network-main.o -MD -MP -MF $(DEPDIR)/test_network-main.Tpo -c -o test_network-main.o `test -f 'main.cpp' || echo '$(srcdir)/'`main.cpp
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/test_network-main.Tpo $(DEPDIR)/test_network-main.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='main.cpp' object='test_network-main.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -c -o test_network-main.o `test -f 'main.cpp' || echo '$(srcdir)/'`main.cpp

test_network-main.obj: main.cpp
@am__fastdepCXX_TRUE@	$(AM_V_CXX)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -MT test_network-main.obj -MD -MP -MF $(DEPDIR)/test_network-main.Tpo -c -o test_network-main.obj `if test -f 'main.cpp'; then $(CYGPATH_W) 'main.cpp'; else $(CYGPATH_W) '$(srcdir)/main.cpp'; fi`
@am__fastdepCXX_TRUE@	$(AM_V_at)$(am__mv) $(DEPDIR)/test_network-main.Tpo $(DEPDIR)/test_network-main.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='main.cpp' object='test_network-main.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(test_network_CXXFLAGS) $(CXXFLAGS) -c -o test_network-main.obj `if test -f 'main.cpp'; then $(CYGPATH_W) 'main.cpp'; else $(CYGPATH_W) '$(srcdir)/main.cpp'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) check-local
check: check-am
all-am: Makefile $(PROGRAMS) $(HEADERS)
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstPROGRAMS \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/gie_self_tests.Po
	-rm -f ./$(DEPDIR)/include_proj_h_from_c.Po
	-rm -f ./$(DEPDIR)/main.Po
	-rm -f ./$(DEPDIR)/pj_phi2_test.Po
	-rm -f ./$(DEPDIR)/proj_angular_io_test.Po
	-rm -f ./$(DEPDIR)/proj_context_test.Po
	-rm -f ./$(DEPDIR)/proj_errno_string_test.Po
	-rm -f ./$(DEPDIR)/test_c_api.Po
	-rm -f ./$(DEPDIR)/test_common.Po
	-rm -f ./$(DEPDIR)/test_crs.Po
	-rm -f ./$(DEPDIR)/test_datum.Po
	-rm -f ./$(DEPDIR)/test_defmodel.Po
	-rm -f ./$(DEPDIR)/test_factory.Po
	-rm -f ./$(DEPDIR)/test_fork.Po
	-rm -f ./$(DEPDIR)/test_grids.Po
	-rm -f ./$(DEPDIR)/test_io.Po
	-rm -f ./$(DEPDIR)/test_metadata.Po
	-rm -f ./$(DEPDIR)/test_misc.Po
	-rm -f ./$(DEPDIR)/test_network-main.Po
	-rm -f ./$(DEPDIR)/test_network-test_network.Po
	-rm -f ./$(DEPDIR)/test_operation.Po
	-rm -f ./$(DEPDIR)/test_operationfactory.Po
	-rm -f ./$(DEPDIR)/test_tinshift.Po
	-rm -f ./$(DEPDIR)/test_util.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/gie_self_tests.Po
	-rm -f ./$(DEPDIR)/include_proj_h_from_c.Po
	-rm -f ./$(DEPDIR)/main.Po
	-rm -f ./$(DEPDIR)/pj_phi2_test.Po
	-rm -f ./$(DEPDIR)/proj_angular_io_test.Po
	-rm -f ./$(DEPDIR)/proj_context_test.Po
	-rm -f ./$(DEPDIR)/proj_errno_string_test.Po
	-rm -f ./$(DEPDIR)/test_c_api.Po
	-rm -f ./$(DEPDIR)/test_common.Po
	-rm -f ./$(DEPDIR)/test_crs.Po
	-rm -f ./$(DEPDIR)/test_datum.Po
	-rm -f ./$(DEPDIR)/test_defmodel.Po
	-rm -f ./$(DEPDIR)/test_factory.Po
	-rm -f ./$(DEPDIR)/test_fork.Po
	-rm -f ./$(DEPDIR)/test_grids.Po
	-rm -f ./$(DEPDIR)/test_io.Po
	-rm -f ./$(DEPDIR)/test_metadata.Po
	-rm -f ./$(DEPDIR)/test_misc.Po
	-rm -f ./$(DEPDIR)/test_network-main.Po
	-rm -f ./$(DEPDIR)/test_network-test_network.Po
	-rm -f ./$(DEPDIR)/test_operation.Po
	-rm -f ./$(DEPDIR)/test_operationfactory.Po
	-rm -f ./$(DEPDIR)/test_tinshift.Po
	-rm -f ./$(DEPDIR)/test_util.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: check-am install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am \
	check-local clean clean-generic clean-libtool \
	clean-noinstPROGRAMS cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags tags-am uninstall uninstall-am

.PRECIOUS: Makefile


PROJ_LIB ?= ../../data/for_tests

pj_phi2_test-check: pj_phi2_test
	./pj_phi2_test

proj_errno_string_test-check: proj_errno_string_test
	./proj_errno_string_test

proj_angular_io_test-check: proj_angular_io_test
	./proj_angular_io_test

proj_context_test-check: proj_context_test
	PROJ_SKIP_READ_USER_WRITABLE_DIRECTORY=YES ./proj_context_test

test_cpp_api-check: test_cpp_api
	PROJ_SKIP_READ_USER_WRITABLE_DIRECTORY=YES PROJ_LIB=$(PROJ_LIB) ./test_cpp_api

gie_self_tests-check: gie_self_tests
	PROJ_SKIP_READ_USER_WRITABLE_DIRECTORY=YES PROJ_LIB=$(PROJ_LIB) ./gie_self_tests

test_network-check: test_network
	PROJ_SKIP_READ_USER_WRITABLE_DIRECTORY=YES PROJ_LIB=$(PROJ_LIB) PROJ_SOURCE_DATA=$(PROJ_LIB) ./test_network

test_defmodel-check: test_defmodel
	PROJ_LIB=$(PROJ_LIB) ./test_defmodel

test_tinshift-check: test_tinshift
	PROJ_LIB=$(PROJ_LIB) ./test_tinshift

test_misc-check: test_misc
	PROJ_LIB=$(PROJ_LIB) ./test_misc

test_fork-check: test_fork
	PROJ_LIB=$(PROJ_LIB) ./test_fork

check-local: pj_phi2_test-check proj_errno_string_test-check \
		proj_angular_io_test-check proj_context_test-check test_cpp_api-check \
		gie_self_tests-check test_network-check test_defmodel-check test_tinshift-check \
		test_misc-check test_fork-check

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
