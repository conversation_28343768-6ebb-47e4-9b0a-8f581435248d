
-------------------------------------------------------------------------------
===============================================================================
Test GeoTIFF grids
===============================================================================

<gie-strict>

# Those first tests using +proj=vgridshift only test the capability of reading
# correctly a value from various formulations of GeoTIFF file, hence only the
# forward path is tested (reverse path is tested in other files)

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_pixelispoint.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_pixelisarea.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_deflate.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_deflate_floatingpointpredictor.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_uint16.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_uint16_with_scale_offset.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_int16.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_int32.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_uint32.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_float64.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

# The overview should be ignored
-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_with_overview.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_in_second_channel.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_bigtiff.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_bigendian.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_bigendian_bigtiff.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_bottomup_with_scale.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_bottomup_with_matrix.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_with_subgrid.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.5        52.5        0
expect      4.5        52.5        11.5

# In subgrid
accept      5.5        53.5        0
expect      5.5        53.5        110.0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_nodata.tif +multiplier=1
-------------------------------------------------------------------------------
accept      4.05        52.1        0
expect      4.05        52.1        10
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_invalid_channel_type.tif +multiplier=1
-------------------------------------------------------------------------------
expect      failure errno invalid_op_file_not_found_or_invalid
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_vgrid_unsupported_byte.tif +multiplier=1
-------------------------------------------------------------------------------
expect      failure errno invalid_op_file_not_found_or_invalid
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/us_noaa_geoid06_ak_subset_at_antimeridian.tif +multiplier=1
-------------------------------------------------------------------------------
tolerance   1 mm

accept      179.99          54.5        0
expect      179.99          54.5        -2.2226

accept      -179.99         54.5        0
expect      -179.99         54.5        -2.3488

accept      179.999999      54.5        0
expect      179.999999      54.5        -2.2872

accept      -179.999999     54.5        0
expect      -179.999999     54.5        -2.2872

accept      179.8           54.5        0
expect      179.8           54.5        -0.7011

accept      179.799         54.5        0
expect      failure errno coord_transfm_outside_grid

accept      180.1833333     54.5        0
expect      -179.8166667    54.5        -3.1933

accept      -179.8166667    54.5        0
expect      -179.8166667    54.5        -3.1933

accept      180.184         54.5        0
expect      failure errno coord_transfm_outside_grid

accept      -179.816        54.5        0
expect      failure errno coord_transfm_outside_grid

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_separate.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_strip.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_tiled.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_tiled_separate.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_positive_west.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_lon_shift_first.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_radian.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_degree.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

# The overview should be ignored
-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_with_overview.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_extra_ifd_with_other_info.tif
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

# Subset of NTv2_0.gsb
-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_with_subgrid.tif
-------------------------------------------------------------------------------
# In subgrid ALbanff, of parent CAwest
accept     -115.5416667      51.1666667        0
expect     -115.5427092888   51.1666899972     0

# In subgrid ONtronto, of parent CAeast
accept     -80.5041667       44.5458333        0
expect     -80.50401615833   44.5458827236     0
-------------------------------------------------------------------------------

# Subset of NTv2_0.gsb
-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_with_subgrid_no_grid_name.tif
-------------------------------------------------------------------------------
# In subgrid ALbanff, of parent CAwest
accept     -115.5416667      51.1666667        0
expect     -115.5427092888   51.1666899972     0

# In subgrid ONtronto, of parent CAeast
accept     -80.5041667       44.5458333        0
expect     -80.50401615833   44.5458827236     0
-------------------------------------------------------------------------------

# Check a nested grid of a nested grid only based on spatial extent analysis
-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_with_two_level_of_subgrids_no_grid_name.tif
-------------------------------------------------------------------------------
accept      -45.0              22.5
accept      -44.9983333334     22.5013888889

# Check a nested grid of a nested grid only based on spatial extent analysis
-------------------------------------------------------------------------------
operation   +proj=vgridshift +grids=tests/test_hgrid_with_two_level_of_subgrids_no_grid_name.tif +multiplier=1
-------------------------------------------------------------------------------
accept      -45.0              22.5             0
accept      -45.0              22.5             5

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_vgrid.tif
-------------------------------------------------------------------------------
expect      failure errno invalid_op_file_not_found_or_invalid
-------------------------------------------------------------------------------


# IGNF:LAMBE to IGNF:LAMB93 using xyzgridshift operation
-------------------------------------------------------------------------------
operation   +proj=pipeline \
                +step +inv +proj=lcc +lat_1=46.8 +lat_0=46.8 +lon_0=0 \
                    +k_0=0.99987742 +x_0=600000 +y_0=2200000 +ellps=clrk80ign +pm=paris \
                +step +proj=push +v_3 \
                +step +proj=cart +ellps=clrk80ign \
                +step +proj=xyzgridshift +grids=tests/subset_of_gr3df97a.tif +grid_ref=output_crs +ellps=GRS80 \
                +step +proj=cart +ellps=GRS80 +inv \
                +step +proj=pop +v_3 \
                +step +proj=lcc +lat_0=46.5 +lon_0=3 +lat_1=49 +lat_2=44 \
                    +x_0=700000 +y_0=6600000 +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 1 mm

accept    814149.529   1887019.768   0
expect    860690.804   6319036.849   0
# If using ntf_r93.gsb, one gets: 860690.805   6319036.850

roundtrip 1
-------------------------------------------------------------------------------


</gie-strict>
