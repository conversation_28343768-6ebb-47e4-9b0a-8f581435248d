-------------------------------------------------------------------------------
                        Tests for the axisswap operation
-------------------------------------------------------------------------------

<gie-strict>

operation   proj=axisswap order=1,2,3,4
tolerance   0.000001 m
accept      1 2 3 4
expect      1 2 3 4
roundtrip   100

operation   proj=axisswap order=4,3,2,1
tolerance   0.000001 m
accept      1 2 3 4
expect      4 3 2 1
roundtrip   100

operation   proj=axisswap order=-1,-2,-3,-4
tolerance   0.000001 m
accept       1  2  3  4
expect      -1 -2 -3 -4
roundtrip   100

operation   proj=axisswap order=1,2,-3,4
tolerance   0.000001 m
accept      1 2  3 4
expect      1 2 -3 4
roundtrip   100

operation   proj=axisswap order=-1,2,3,4
tolerance   0.000001 m
accept       1 2 3 4
expect      -1 2 3 4
roundtrip   100

operation   proj=axisswap order=1,2,3,-4
tolerance   0.000001 m
accept      1 2 3  4
expect      1 2 3 -4
roundtrip   100

operation   proj=axisswap order=-2,1
tolerance   0.000001 m
accept       1 2 3 4
expect      -2 1 3 4
roundtrip   100

operation   proj=axisswap order=3,-2,1
tolerance   0.000001 m
accept      1  2 3 4
expect      3 -2 1 4
roundtrip   100

operation   proj=axisswap axis=neu
tolerance   0 m
accept      1 2 3
expect      2 1 3

# when using the +axis parameter we specify the order of the INPUT coordinate,
# as opposed to +order which relates to the OUTPUT coordinate. Here we test
# that n(1), u(2) and e(3) are swapped correctly to enu ordering.
operation   proj=axisswap axis=nue
tolerance   0 m
accept      1 2 3
expect      2 3 1

operation   proj=axisswap axis=swd
tolerance   0.000001 m
accept      1 2 3 4
expect      -2 -1 -3 4

operation   proj=pipeline \
            step proj=latlong +ellps=WGS84 \
            step proj=axisswap \
            order=1,2,3,4

tolerance   0.00001 m
accept      12 55 0 0
expect      12 55 0 0

operation   proj=pipeline \
            step proj=latlong +ellps=WGS84 \
            step proj=axisswap \
            order=-2,-1,3,4

tolerance   0.00001 m
accept       12 55 0 0
expect      -55 -12 0 0

operation   proj=axisswap order=1,2,3,4 axis=enu
expect      failure pjd_err_axis

operation   proj=axisswap
expect      failure pjd_err_axis

operation   proj=axisswap order=1,2,1,4
expect      failure pjd_err_axis

operation   proj=axisswap order=2,3
expect      failure pjd_err_axis

operation   proj=axisswap order=2,3,4
expect      failure pjd_err_axis

operation   proj=axisswap order=1,2,3,5
expect      failure pjd_err_axis

</gie-strict>
