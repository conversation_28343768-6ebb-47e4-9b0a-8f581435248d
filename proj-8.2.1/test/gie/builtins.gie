===============================================================================

Test material, mostly converted from selftest entries in PJ_xxx.c

Most of this material was autogenerated, and does not attempt to exercise
corner cases etc.

See more_builtins.gie for some test cases with a more human touch.

===============================================================================


# First test non strict gie

<gie>

operation +proj=aea
          +ellps=GRS80  +lat_1=0 +lat_2=2
tolerance 0.1 mm
accept  2 1
expect  222571.608757106 110653.326743030

unknown_keyword

</gie>


<gie-strict>

===============================================================================
# Albers Equal Area
# 	Conic Sph&Ell
# 	lat_1= lat_2=
===============================================================================
-------------------------------------------------------------------------------
operation +proj=aea   +ellps=GRS80  +lat_1=0 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222571.608757106 110653.326743030
accept  2 -1
expect  222706.306508391 -110484.267144400
accept  -2 1
expect  -222571.608757106 110653.326743030
accept  -2 -1
expect  -222706.306508391 -110484.267144400

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796630 -0.000904370
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796630 -0.000904370

-------------------------------------------------------------------------------
operation +proj=aea   +R=6400000    +lat_1=0 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223334.085170885 111780.431884472
accept  2 -1
expect  223470.154991687 -111610.339430990
accept  -2 1
expect  -223334.085170885 111780.431884472
accept  -2 -1
expect  -223470.154991687 -111610.339430990

direction inverse
accept  200 100
expect  0.001790494 0.000895246
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790494 0.000895246
accept  -200 -100
expect  -0.001790493 -0.000895247

operation +proj=aea +ellps=GRS80 +lat_1=900
expect  failure errno invalid_op_illegal_arg_value

operation +proj=aea +ellps=GRS80 +lat_2=900
expect  failure errno invalid_op_illegal_arg_value

operation +proj=aea   +R=6400000    +lat_1=1 +lat_2=-1
expect failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=aea +a=9999999 +b=.9 +lat_2=1
-------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

===============================================================================
# Azimuthal Equidistant
# 	Azi, Sph&Ell
# 	lat_0 guam
===============================================================================

-------------------------------------------------------------------------------
# Test equatorial aspect of the spherical azimuthal equidistant. Test data from
# Snyder pp. 196-197, table 30.
-------------------------------------------------------------------------------
operation +proj=aeqd +R=1 +lat_0=0
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       0
expect  0       0
roundtrip   100
accept  0       90
expect  0       1.57080
roundtrip   100
accept  10      80
expect  0.04281 1.39829
roundtrip   100
accept  40      30
expect  0.62896 0.56493
roundtrip   100
accept  90      0
expect  1.57080 0
roundtrip   100
accept  90      90
expect  0       1.57080
roundtrip   100

# point opposite projection center is undefined
accept  180     0
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test equatorial aspect of the ellipsoidal azimuthal equidistant. Test data from
# Snyder pp. 196-197, table 30.
-------------------------------------------------------------------------------
operation +proj=aeqd +ellps=GRS80 +lat_0=0
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   90
expect  0   10001965.7292
roundtrip   100
accept  0   0
expect  0   0
roundtrip   100
accept  90  0
expect  10_018_754.1714  0
roundtrip   100
accept  90  0
expect  10_018_754.1714  0
roundtrip   100
accept  45  45
expect  3_860_398.3783  5_430_089.0490
roundtrip   100


# Test oblique aeqd with point very close lon_0, lat_0, on a perfect sphere
operation +proj=aeqd +a=6371008.771415 +b=6371008.771415 +lat_0=30.2345 +lon_0=-120.2345
tolerance 1 mm
accept    -120.234501 30.234501
expect    -0.096      0.111
roundtrip 1

accept    -120.2345   30.2345
expect    0.000       0.000
roundtrip 1

# Same on an ellipsoid very close to the sphere
operation +proj=aeqd +a=6371008.771415 +b=6371008.771414 +lat_0=30.2345 +lon_0=-120.2345
tolerance 1 mm
accept    -120.234501 30.234501
expect    -0.096      0.111
roundtrip 1

accept    -120.2345   30.2345
expect    0.000       0.000
roundtrip 1

-------------------------------------------------------------------------------
# Test the Modified Azimuthal Equidistant / EPSG 9832. Test data from the EPSG
# Guidance Note 7 part 2, April 2018,  p. 85
-------------------------------------------------------------------------------
operation +proj=aeqd +ellps=clrk66 +lat_0=9.546708325068591 +lon_0=138.1687444500492 +x_0=40000.00 +y_0=60000.00
-------------------------------------------------------------------------------
tolerance 1 cm
accept  138.19303001104092 9.596525859439623
expect  42665.90    65509.82
roundtrip 100

direction inverse
accept   42665.90    65509.82
expect  138.19303001104092 9.596525859439623

-------------------------------------------------------------------------------
# Test the azimuthal equidistant modified for Guam. Test data from the EPSG
# Guidance Note 7 part 2, September 2016,  p. 85
-------------------------------------------------------------------------------
operation +proj=aeqd +guam +ellps=clrk66 +x_0=50000.00 +y_0=50000.00 \
          +lon_0=144.74875069444445 +lat_0=13.47246633333333
-------------------------------------------------------------------------------
tolerance 1 cm
accept  144.635331291666660     13.33903846111111
expect  37712.48    35242.00
roundtrip 100

direction inverse
accept   37712.48   35242.00
expect  144.635331291666660     13.33903846111111

-------------------------------------------------------------------------------
# Test northern polar aspect of the ellipsoidal azimuthal equidistant. Test data
# from Snyder p. 198, table 31.
-------------------------------------------------------------------------------
operation +proj=aeqd +ellps=intl +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 m
accept  0   90
expect  0   0
roundtrip   100
accept  0   85
expect  0   -558_485.4
roundtrip   100
accept  0   80
expect  0   -1_116_885.2
roundtrip   100
accept  0   70
expect  0   -2_233_100.9
roundtrip   100

-------------------------------------------------------------------------------
# Test southern polar aspect of the ellipsoidal azimuthal equidistant. Test data
# from Snyder p. 198, table 31.
-------------------------------------------------------------------------------
operation +proj=aeqd +ellps=intl +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 m
accept  0   -90
expect  0   0
roundtrip   100
accept  0   -85
expect  0   558_485.4
roundtrip   100
accept  0   -80
expect  0   1_116_885.2
roundtrip   100
accept  0   -70
expect  0   2_233_100.9
roundtrip   100

-------------------------------------------------------------------------------
# Test northern polar aspect of the spherical azimuthal equidistant.
-------------------------------------------------------------------------------
operation +proj=aeqd +R=1 +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 m
accept  0       0
expect  0       -1.5708
roundtrip   100
accept  0       90
expect  0       0
roundtrip   100
accept  90      90
expect  0       0
roundtrip   100
accept  90      0
expect  1.5708  0
roundtrip   100
accept  45      45
expect  0.5554  -0.5554
roundtrip   100

#point opposite of projection center is undefined
accept  0   -90
expect  failure errno coord_transfm_outside_projection_domain

direction inverse
accept  0   5
expect  failure errno coord_transfm_outside_projection_domain

accept  0   3.14159265359
expect  180 -90

-------------------------------------------------------------------------------
# Test sourthnern polar aspect of the spherical azimuthal equidistant.
-------------------------------------------------------------------------------
operation +proj=aeqd +R=1 +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 m
accept  0       0
expect  0       1.5708
roundtrip   100
accept  0       -90
expect  0       0
roundtrip   100
accept  90      -90
expect  0       0
roundtrip   100
accept  90      0
expect  1.5708  0
roundtrip   100
accept  45      -45
expect  0.5554  0.5554
roundtrip   100

#point opposite of projection center is undefined
accept  0   90
expect  failure errno coord_transfm_outside_projection_domain


-------------------------------------------------------------------------------
# Test oblique aspect of the spherical azimuthal equidistant.
-------------------------------------------------------------------------------
operation +proj=aeqd +R=1 +lat_0=45
-------------------------------------------------------------------------------
tolerance 0.1 m
accept  0 0
expect  0.0000  -0.7854
roundtrip   100
accept  0 45
expect  0.0000  0.0000
roundtrip   100
accept  0 90
expect  0.0000  0.7854
roundtrip   100
accept  90 0
expect  1.5708  -0.0000
roundtrip   100
accept  90 45
expect  0.8550  0.6046
#roundtrip   100 # roundtrip performs badly for this test on some platforms
accept  90 90
expect  0.0000  0.7854
roundtrip   100

-------------------------------------------------------------------------------
# Test oblique aspect of the ellipsoidal azimuthal equidistant.
-------------------------------------------------------------------------------
operation +proj=aeqd +ellps=GRS80 +lat_0=45
-------------------------------------------------------------------------------
tolerance 0.1 m
accept  0 0
expect  0.0000  -4984944.3779
roundtrip   100
accept  0 45
expect  0.0000  0.0000
roundtrip   100
accept  0 90
expect  0.0000  5017021.3514
roundtrip   100
accept  90 0
expect  10010351.5666   26393.3781
roundtrip   100
accept  90 45
expect  5461910.9128    3863514.7047
roundtrip   100
accept  90 90
expect  0.0000  5017021.3514
roundtrip   100

===============================================================================
# Airy
# 	Misc Sph, no inv.
# 	no_cut lat_b=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=airy   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  189109.886908621 94583.752387504
accept  2 -1
expect  189109.886908621 -94583.752387504
accept  -2 1
expect  -189109.886908621 94583.752387504
accept  -2 -1
expect  -189109.886908621 -94583.752387504

-------------------------------------------------------------------------------
# Test north polar aspect
-------------------------------------------------------------------------------
operation +proj=airy +R=1 +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   0
expect  0   -1.3863
accept  0   90
expect  0   0
accept  0   -90
expect  failure errno coord_transfm_outside_projection_domain


-------------------------------------------------------------------------------
# Test south polar aspect
-------------------------------------------------------------------------------
operation +proj=airy +R=1 +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   0
expect  0   1.3863
accept  0   -90
expect  0   0
accept  0   90
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test oblique aspect
-------------------------------------------------------------------------------
operation +proj=airy +R=1 +lon_0=45 +lat_0=45
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  45      45
expect  0       0
accept  0       0
expect  -0.7336 -0.5187
accept  -45     -45
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test that coordinates on the opposing hemisphere are projected when using
# +no_cut.
-------------------------------------------------------------------------------
operation +proj=airy +R=1 +lat_0=-90 +no_cut
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   10
expect  0   1.5677


-------------------------------------------------------------------------------
# Test the +lat_b parameter
-------------------------------------------------------------------------------
operation +proj=airy +R=1 +lat_b=89.99999999 # check tolerance
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   0
expect  0   0
-------------------------------------------------------------------------------
operation +proj=airy +R=1 +lat_b=30
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       0
expect  0       0
accept  25      25
expect  0.3821  0.4216

-------------------------------------------------------------------------------
operation +proj=airy +R=1 +no_cut
-------------------------------------------------------------------------------
accept  -180 0
expect  failure errno coord_transfm_outside_projection_domain

===============================================================================
# Aitoff
# 	Misc Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=aitoff   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223379.458811696 111706.742883853
accept  2 -1
expect  223379.458811696 -111706.742883853
accept  -2 1
expect  -223379.458811696 111706.742883853
accept  -2 -1
expect  -223379.458811696 -111706.742883853

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Mod. Stereographic of Alaska
# 	Azi(mod)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=alsk +ellps=clrk66
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  -160.000000000 55.000000000
expect  -513253.146950842 -968928.031867943
accept  -160.000000000 70.000000000
expect  -305001.133897637 687494.464958651
accept  -145.000000000 70.000000000
expect  266454.305088600 683423.477493031
accept  -145.000000000 60.000000000
expect  389141.322439244 -423913.251230397

direction inverse
accept  -500000.000000000 -950000.000000000
expect  -159.830804303 55.183195262
accept  -305000.000000000 700000.000000000
expect  -160.042203156 70.111086864
accept  250000.000000000 700000.000000000
expect  -145.381043551 70.163900908
accept  400000.000000000 -400000.000000000
expect  -144.758985461 60.202929201

-------------------------------------------------------------------------------
operation +proj=alsk +R=6370997
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  -160.000000000 55.000000000
expect  -511510.319410844 -967150.991676078
accept  -160.000000000 70.000000000
expect  -303744.771290369 685439.745941123
accept  -145.000000000 70.000000000
expect  265354.974019663 681386.892874573
accept  -145.000000000 60.000000000
expect  387711.995394027 -422980.685505463

direction inverse
accept  -500000.000000000 -950000.000000000
expect  -159.854014458 55.165653849
accept  -305000.000000000 700000.000000000
expect  -160.082332372 70.128307618
accept  250000.000000000 700000.000000000
expect  -145.347827407 70.181566919
accept  400000.000000000 -400000.000000000
expect  -144.734239827 60.193564733


===============================================================================
# Apian Globular I
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=apian   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223374.577355253 111701.072127637
accept  2 -1
expect  223374.577355253 -111701.072127637
accept  -2 1
expect  -223374.577355253 111701.072127637
accept  -2 -1
expect  -223374.577355253 -111701.072127637


===============================================================================
# August Epicycloidal
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=august   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223404.978180972 111722.340289763
accept  2 -1
expect  223404.978180972 -111722.340289763
accept  -2 1
expect  -223404.978180972 111722.340289763
accept  -2 -1
expect  -223404.978180972 -111722.340289763


===============================================================================
# Bacon Globular
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=bacon   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223334.132555965 175450.725922666
accept  2 -1
expect  223334.132555965 -175450.725922666
accept  -2 1
expect  -223334.132555965 175450.725922666
accept  -2 -1
expect  -223334.132555965 -175450.725922666


===============================================================================
# Bipolar conic of western hemisphere
# 	Conic Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=bipc   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  2452160.217725756 -14548450.759654747
accept  2 -1
expect  2447915.213725341 -14763427.212798730
accept  -2 1
expect  2021695.522934909 -14540413.695283702
accept  -2 -1
expect  2018090.503004699 -14755620.651414108

direction inverse
accept  200 100
expect  -73.038700285 17.248118466
accept  200 -100
expect  -73.037303739 17.249414978
accept  -200 100
expect  -73.035893173 17.245536403
accept  -200 -100
expect  -73.034496627 17.246832896

-------------------------------------------------------------------------------
operation +proj=bipc   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  2460565.740974965 -14598319.989330800
accept  2 -1
expect  2456306.185935200 -14814033.339502094
accept  -2 1
expect  2028625.497819099 -14590255.375482792
accept  -2 -1
expect  2025008.120589143 -14806200.018759441

direction inverse
accept  200 100
expect  -73.038693105 17.248116270
accept  200 -100
expect  -73.037301330 17.249408353
accept  -200 100
expect  -73.035895582 17.245543028
accept  -200 -100
expect  -73.034503807 17.246835092


===============================================================================
# Boggs Eumorphic
# 	PCyl., no inv., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=boggs   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  211949.700808182 117720.998305411
accept  2 -1
expect  211949.700808182 -117720.998305411
accept  -2 1
expect  -211949.700808182 117720.998305411
accept  -2 -1
expect  -211949.700808182 -117720.998305411


===============================================================================
# Bonne (Werner lat_1=90)
# 	Conic Sph&Ell
# 	lat_1=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=bonne   +ellps=GRS80  +lat_1=0.5
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222605.296097157 55321.139565495
accept  2 -1
expect  222605.296099239 -165827.647799052
accept  -2 1
expect  -222605.296097157 55321.139565495
accept  -2 -1
expect  -222605.296099239 -165827.647799052

direction inverse
accept  200 100
expect  0.001796699 0.500904369
accept  200 -100
expect  0.001796698 0.499095631
accept  -200 100
expect  -0.001796699 0.500904369
accept  -200 -100
expect  -0.001796698 0.499095631

-------------------------------------------------------------------------------
operation +proj=bonne   +ellps=GRS80  +lat_1=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0 90
expect  0 0

direction inverse
accept  0 0
expect  0 90

-------------------------------------------------------------------------------
operation +proj=bonne   +R=6400000    +lat_1=0.5
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223368.115572528 55884.555246394
accept  2 -1
expect  223368.115574632 -167517.599369694
accept  -2 1
expect  -223368.115572528 55884.555246394
accept  -2 -1
expect  -223368.115574632 -167517.599369694

direction inverse
accept  200 100
expect  0.001790562 0.500895246
accept  200 -100
expect  0.001790561 0.499104753
accept  -200 100
expect  -0.001790562 0.500895246
accept  -200 -100
expect  -0.001790561 0.499104753

-------------------------------------------------------------------------------
operation +proj=bonne   +R=6400000  +lat_1=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0 90
expect  0 0

direction inverse
accept  0 0
expect  0 90

===============================================================================
# Cal Coop Ocean Fish Invest Lines/Stations
# 	Cyl, Sph&Ell
===============================================================================

-------------------------------------------------------------------------------
operation +proj=calcofi   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  508.444872150 -1171.764860418
accept  2 -1
expect  514.999168152 -1145.821981468
accept  -2 1
expect  500.685384125 -1131.445377920
accept  -2 -1
expect  507.369719137 -1106.178201483

direction inverse
accept  200 100
expect  -110.363307925 12.032056976
accept  200 -100
expect  -98.455008863 18.698723643
accept  -200 100
expect  -207.447024504 81.314089279
accept  -200 -100
expect  -62.486322854 87.980755945

-------------------------------------------------------------------------------
operation +proj=calcofi   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  507.090507488 -1164.727375198
accept  2 -1
expect  513.686136375 -1138.999268217
accept  -2 1
expect  499.336261476 -1124.435130997
accept  -2 -1
expect  506.060570393 -1099.375665067

direction inverse
accept  200 100
expect  -110.305190410 12.032056976
accept  200 -100
expect  -98.322360950 18.698723643
accept  -200 100
expect  -207.544906814 81.314089279
accept  -200 -100
expect  -62.576950372 87.980755945

operation +proj=calcofi +lon_0=50 +ellps=WGS84
accept  10 50
expect  303.525850      -1576.974388
roundtrip 100

operation +proj=calcofi +ellps=GRS80 +lon_0=50
accept  10 50
expect  303.525850      -1576.974388
roundtrip 100

operation +proj=calcofi +R=400 +lon_0=50 +x_0=10000 +y_0=500000
accept  10 50
expect  301.769827      -1567.849822
roundtrip 100


===============================================================================
# Cassini
# 	Cyl, Sph&Ell
===============================================================================

-------------------------------------------------------------------------------
operation +proj=cass   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222605.285776991 110642.229253999
roundtrip 1
accept  2 -1
expect  222605.285776991 -110642.229253999
accept  -2 1
expect  -222605.285776991 110642.229253999
accept  -2 -1
expect  -222605.285776991 -110642.229253999

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=cass   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223368.105203484 111769.145040586
accept  2 -1
expect  223368.105203484 -111769.145040586
accept  -2 1
expect  -223368.105203484 111769.145040586
accept  -2 -1
expect  -223368.105203484 -111769.145040586

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247

-------------------------------------------------------------------------------
# Hyperbolic variant: test point from EPSG Guidance Note 7.2
operation +proj=cass +hyperbolic +a=6378306.376305601 +rf=293.466307 \
          +lat_0=-16.25 +lon_0=179.33333333333333 +to_meter=20.1168 \
          +x_0=251727.9155424 +y_0=334519.953768
-------------------------------------------------------------------------------

tolerance 0.1 mm
accept  179.99433652777776 -16.841456527777776
expect  16015.************ 13369.************
roundtrip 1

===============================================================================
# Central Conic
# 	Sph
# 	lat_1
===============================================================================

-------------------------------------------------------------------------------
operation +proj=pipeline +R=6390000 \
    +step +proj=ccon +lat_1=52 +lat_0=52 +lon_0=19 +x_0=330000 +y_0=-350000 \
    +step +proj=axisswap +order=1,-2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept 24 55
expect 650031.54109413219363 4106.1617770643609028
accept 15 49
expect 37074.189007307473069 676826.23559270039774
accept 24 49
expect 696053.36061617843913 672294.56795827199940
accept 19 52
expect 330000.00000000000000 350000.00000000000000

direction inverse
accept 0 0
expect 13.840227318521004431 55.030403993648806391
accept 0 700000
expect 14.514453594615022781 48.773847834747808675
accept 700000 0
expect 24.782707184271129766 55.003515505218481835
accept 700000 700000
expect 24.027610763560529927 48.750476070495021286
accept 330000 350000
expect 19.000000000000000000 52.000000000000000000


===============================================================================
# Central Cylindrical
# 	Cyl, Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=cc   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 111712.415540593
accept  2 -1
expect  223402.144255274 -111712.415540593
accept  -2 1
expect  -223402.144255274 111712.415540593
accept  -2 -1
expect  -223402.144255274 -111712.415540593

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Equal Area Cylindrical
# 	Cyl, Sph&Ell
# 	lat_ts=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=cea   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222638.981586547 110568.812396267
accept  2 -1
expect  222638.981586547 -110568.812396266
accept  -2 1
expect  -222638.981586547 110568.812396267
accept  -2 -1
expect  -222638.981586547 -110568.812396266

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=cea   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 111695.401198614
accept  2 -1
expect  223402.144255274 -111695.401198614
accept  -2 1
expect  -223402.144255274 111695.401198614
accept  -2 -1
expect  -223402.144255274 -111695.401198614

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Chamberlin Trimetric
# 	Misc Sph, no inv.
# 	lat_1= lon_1= lat_2= lon_2= lat_3= lon_3=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=chamb   +R=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 2.5 mm
accept  2 1
expect  -27864.779586801 -223364.324593274
accept  2 -1
expect  -251312.283053493 -223402.145526208
accept  -2 1
expect  -27864.785649105 223364.327328827
accept  -2 -1
expect  -251312.289116443 223402.142197287


===============================================================================
# Collignon
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=collg   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  249872.921577930 99423.174788460
accept  2 -1
expect  254272.532301245 -98559.307760743
accept  -2 1
expect  -249872.921577930 99423.174788460
accept  -2 -1
expect  -254272.532301245 -98559.307760743

direction inverse
accept  200 100
expect  0.001586797 0.001010173
accept  200 -100
expect  0.001586769 -0.001010182
accept  -200 100
expect  -0.001586797 0.001010173
accept  -200 -100
expect  -0.001586769 -0.001010182


===============================================================================
# Compact Miller
# 	Cyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=comill   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 110611.859089459
accept  2 -1
expect  223402.144255274 -110611.859089459
accept  -2 1
expect  -223402.144255274 110611.859089459
accept  -2 -1
expect  -223402.144255274 -110611.859089459

direction inverse
accept  200 100
expect  0.001790493 0.000904107
accept  200 -100
expect  0.001790493 -0.000904107
accept  -200 100
expect  -0.001790493 0.000904107
accept  -200 -100
expect  -0.001790493 -0.000904107


===============================================================================
# Craster Parabolic (Putnins P4)
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=crast   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  218280.142056781 114306.045604280
accept  2 -1
expect  218280.142056781 -114306.045604280
accept  -2 1
expect  -218280.142056781 114306.045604280
accept  -2 -1
expect  -218280.142056781 -114306.045604280

direction inverse
accept  200 100
expect  0.001832259 0.000874839
accept  200 -100
expect  0.001832259 -0.000874839
accept  -200 100
expect  -0.001832259 0.000874839
accept  -200 -100
expect  -0.001832259 -0.000874839


===============================================================================
# Denoyer Semi-Elliptical
# 	PCyl., no inv., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=denoy   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223377.422876954 111701.072127637
accept  2 -1
expect  223377.422876954 -111701.072127637
accept  -2 1
expect  -223377.422876954 111701.072127637
accept  -2 -1
expect  -223377.422876954 -111701.072127637


===============================================================================
# Eckert I
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eck1   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  204680.888202951 102912.178426065
accept  2 -1
expect  204680.888202951 -102912.178426065
accept  -2 1
expect  -204680.888202951 102912.178426065
accept  -2 -1
expect  -204680.888202951 -102912.178426065

direction inverse
accept  200 100
expect  0.001943415 0.000971702
accept  200 -100
expect  0.001943415 -0.000971702
accept  -200 100
expect  -0.001943415 0.000971702
accept  -200 -100
expect  -0.001943415 -0.000971702


===============================================================================
# Eckert II
# 	PCyl. Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eck2   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  204472.870907960 121633.734975242
accept  2 -1
expect  204472.870907960 -121633.734975242
accept  -2 1
expect  -204472.870907960 121633.734975242
accept  -2 -1
expect  -204472.870907960 -121633.734975242

direction inverse
accept  200 100
expect  0.001943415 0.000824804
accept  200 -100
expect  0.001943415 -0.000824804
accept  -200 100
expect  -0.001943415 0.000824804
accept  -200 -100
expect  -0.001943415 -0.000824804


===============================================================================
# Eckert III
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eck3   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  188652.015721538 94328.919337031
accept  2 -1
expect  188652.015721538 -94328.919337031
accept  -2 1
expect  -188652.015721538 94328.919337031
accept  -2 -1
expect  -188652.015721538 -94328.919337031

direction inverse
accept  200 100
expect  0.002120241 0.001060120
accept  200 -100
expect  0.002120241 -0.001060120
accept  -200 100
expect  -0.002120241 0.001060120
accept  -200 -100
expect  -0.002120241 -0.001060120


===============================================================================
# Eckert IV
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eck4   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  188646.389356416 132268.540174065
accept  2 -1
expect  188646.389356416 -132268.540174065
accept  -2 1
expect  -188646.389356416 132268.540174065
accept  -2 -1
expect  -188646.389356416 -132268.540174065

direction inverse
accept  200 100
expect  0.002120241 0.000756015
accept  200 -100
expect  0.002120241 -0.000756015
accept  -200 100
expect  -0.002120241 0.000756015
accept  -200 -100
expect  -0.002120241 -0.000756015


===============================================================================
# Eckert V
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eck5   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  197031.392134061 98523.198847227
accept  2 -1
expect  197031.392134061 -98523.198847227
accept  -2 1
expect  -197031.392134061 98523.198847227
accept  -2 -1
expect  -197031.392134061 -98523.198847227

direction inverse
accept  200 100
expect  0.002029979 0.001014989
accept  200 -100
expect  0.002029979 -0.001014989
accept  -200 100
expect  -0.002029979 0.001014989
accept  -200 -100
expect  -0.002029979 -0.001014989


===============================================================================
# Eckert VI
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eck6   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  197021.605628992 126640.420733174
accept  2 -1
expect  197021.605628992 -126640.420733174
accept  -2 1
expect  -197021.605628992 126640.420733174
accept  -2 -1
expect  -197021.605628992 -126640.420733174

direction inverse
accept  200 100
expect  0.002029979 0.000789630
accept  200 -100
expect  0.002029979 -0.000789630
accept  -200 100
expect  -0.002029979 0.000789630
accept  -200 -100
expect  -0.002029979 -0.000789630


===============================================================================
# Equidistant Cylindrical (Plate Carree)
# 	Cyl, Sph
# 	lat_ts=[, lat_0=0]
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eqc   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 111701.072127637
accept  2 -1
expect  223402.144255274 -111701.072127637
accept  -2 1
expect  -223402.144255274 111701.072127637
accept  -2 -1
expect  -223402.144255274 -111701.072127637

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Equidistant Conic
# 	Conic, Sph&Ell
# 	lat_1= lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=eqdc   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222588.440269286 110659.134907347
accept  2 -1
expect  222756.836702042 -110489.578087221
accept  -2 1
expect  -222588.440269286 110659.134907347
accept  -2 -1
expect  -222756.836702042 -110489.578087221

direction inverse
accept  200 100
expect  0.001796359 0.000904369
accept  200 -100
expect  0.001796358 -0.000904370
accept  -200 100
expect  -0.001796359 0.000904369
accept  -200 -100
expect  -0.001796358 -0.000904370

-------------------------------------------------------------------------------
operation +proj=eqdc   +R=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223351.088175114 111786.108747174
accept  2 -1
expect  223521.200266735 -111615.970741241
accept  -2 1
expect  -223351.088175114 111786.108747174
accept  -2 -1
expect  -223521.200266735 -111615.970741241

direction inverse
accept  200 100
expect  0.001790221 0.000895246
accept  200 -100
expect  0.001790220 -0.000895247
accept  -200 100
expect  -0.001790221 0.000895246
accept  -200 -100
expect  -0.001790220 -0.000895247

operation +proj=eqdc +a=9999999 +b=.9 +lat_2=1
expect    failure

operation +proj=eqdc   +R=6400000    +lat_1=1 +lat_2=-1
expect    failure errno invalid_op_illegal_arg_value

operation +proj=eqdc   +R=6400000    +lat_1=91
expect    failure errno invalid_op_illegal_arg_value

operation +proj=eqdc   +R=6400000    +lat_2=91
expect    failure errno invalid_op_illegal_arg_value

operation +proj=eqdc   +R=1 +lat_1=1e-9
expect    failure errno invalid_op_illegal_arg_value

===============================================================================
# Euler
# 	Conic, Sph
# 	lat_1= and lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=euler   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222597.634659108 111404.240549919
accept  2 -1
expect  222767.165631876 -111234.676491018
accept  -2 1
expect  -222597.634659108 111404.240549919
accept  -2 -1
expect  -222767.165631876 -111234.676491018

direction inverse
accept  200 100
expect  0.001796281 0.000898315
accept  200 -100
expect  0.001796279 -0.000898316
accept  -200 100
expect  -0.001796281 0.000898315
accept  -200 -100
expect  -0.001796279 -0.000898316

-------------------------------------------------------------------------------
operation +proj=euler   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223360.655598694 111786.112389791
accept  2 -1
expect  223530.767690316 -111615.967098624
accept  -2 1
expect  -223360.655598694 111786.112389791
accept  -2 -1
expect  -223530.767690316 -111615.967098624

direction inverse
accept  200 100
expect  0.001790144 0.000895246
accept  200 -100
expect  0.001790143 -0.000895247
accept  -200 100
expect  -0.001790144 0.000895246
accept  -200 -100
expect  -0.001790143 -0.000895247


===============================================================================
# Extended Transverse Mercator
# 	Cyl, Sph
# 	lat_ts=(0)
# lat_0=(0)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=etmerc   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 50 nm
accept  2 1
expect  222650.796797586 110642.229411933
accept  2 -1
expect  222650.796797586 -110642.229411933
accept  -2 1
expect  -222650.796797586 110642.229411933
accept  -2 -1
expect  -222650.796797586 -110642.229411933
# near pole
accept  30 89.9999
expect  5.584698978 10001956.056248082
# 3900 km from central meridian
accept  44.69 35.37
expect  4168136.489446198 4985511.302287407

direction inverse
accept  200 100
expect  0.00179663056816 0.00090436947663
accept  200 -100
expect  0.00179663056816 -0.00090436947663
accept  -200 100
expect  -0.00179663056816 0.00090436947663
accept  -200 -100
expect  -0.00179663056816 -0.00090436947663
# near pole
accept  6 1.0001e7
expect  0.35596960759234 89.99135362646302
# 3900 km from central meridian
accept  4168136.489446198 4985511.302287407
expect  44.69 35.37

===============================================================================
# Fahey
# 	Pcyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=fahey   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  182993.344649124 101603.193569884
accept  2 -1
expect  182993.344649124 -101603.193569884
accept  -2 1
expect  -182993.344649124 101603.193569884
accept  -2 -1
expect  -182993.344649124 -101603.193569884

direction inverse
accept  200 100
expect  0.002185789 0.000984246
accept  200 -100
expect  0.002185789 -0.000984246
accept  -200 100
expect  -0.002185789 0.000984246
accept  -200 -100
expect  -0.002185789 -0.000984246


===============================================================================
# Foucaut
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=fouc   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222588.120675892 111322.316700694
accept  2 -1
expect  222588.120675892 -111322.316700694
accept  -2 1
expect  -222588.120675892 111322.316700694
accept  -2 -1
expect  -222588.120675892 -111322.316700694

direction inverse
accept  200 100
expect  0.001796631 0.000898315
accept  200 -100
expect  0.001796631 -0.000898315
accept  -200 100
expect  -0.001796631 0.000898315
accept  -200 -100
expect  -0.001796631 -0.000898315

-------------------------------------------------------------------------------
operation +proj=fouc   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223351.109003414 111703.907721713
accept  2 -1
expect  223351.109003414 -111703.907721713
accept  -2 1
expect  -223351.109003414 111703.907721713
accept  -2 -1
expect  -223351.109003414 -111703.907721713

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Foucaut Sinusoidal
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=fouc_s   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 111695.401198614
accept  2 -1
expect  223402.144255274 -111695.401198614
accept  -2 1
expect  -223402.144255274 111695.401198614
accept  -2 -1
expect  -223402.144255274 -111695.401198614

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Gall (Gall Stereographic)
# 	Cyl, Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=gall   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  157969.171134520 95345.249178386
accept  2 -1
expect  157969.171134520 -95345.249178386
accept  -2 1
expect  -157969.171134520 95345.249178386
accept  -2 -1
expect  -157969.171134520 -95345.249178386

direction inverse
accept  200 100
expect  0.002532140 0.001048847
accept  200 -100
expect  0.002532140 -0.001048847
accept  -200 100
expect  -0.002532140 0.001048847
accept  -200 -100
expect  -0.002532140 -0.001048847


===============================================================================
# Geocentric

===============================================================================

-------------------------------------------------------------------------------
operation +proj=geocent   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1 0
expect  6373287.27950247        222560.09599219 110568.77482092
accept  2 -1 0
expect  6373287.27950247        222560.09599219 -110568.77482092
accept  -2 1 0
expect  6373287.27950247        -222560.09599219 110568.77482092
accept  -2 -1 0
expect  6373287.27950247        -222560.09599219 -110568.77482092

direction inverse
accept  6373287.27950247        222560.09599219 110568.77482092
expect  2 1 0

-------------------------------------------------------------------------------
operation +proj=geocent   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm


===============================================================================
# Geostationary Satellite View
# 	Azi, Sph&Ell
# 	h=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=geos   +ellps=GRS80 +h=35785831
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222527.070365800 110551.303413329
accept  2 -1
expect  222527.070365800 -110551.303413329
accept  -2 1
expect  -222527.070365800 110551.303413329
accept  -2 -1
expect  -222527.070365800 -110551.303413329

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=geos   +R=6400000 +h=35785831
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223289.457635795 111677.657456537
accept  2 -1
expect  223289.457635795 -111677.657456537
accept  -2 1
expect  -223289.457635795 111677.657456537
accept  -2 -1
expect  -223289.457635795 -111677.657456537

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247

-------------------------------------------------------------------------------
operation +proj=geos +R=1 +h=0
-------------------------------------------------------------------------------
expect  failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=geos +R=1 +h=1e11
-------------------------------------------------------------------------------
expect  failure errno invalid_op_illegal_arg_value



===============================================================================
# Ginsburg VIII (TsNIIGAiK)
# 	PCyl, Sph., no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=gins8   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  194350.250939590 111703.907635335
accept  2 -1
expect  194350.250939590 -111703.907635335
accept  -2 1
expect  -194350.250939590 111703.907635335
accept  -2 -1
expect  -194350.250939590 -111703.907635335


===============================================================================
# General Sinusoidal Series
# 	PCyl, Sph.
# 	m= n=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=gn_sinu   +a=6400000 +m=1 +n=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223385.132504696 111698.236447187
accept  2 -1
expect  223385.132504696 -111698.236447187
accept  -2 1
expect  -223385.132504696 111698.236447187
accept  -2 -1
expect  -223385.132504696 -111698.236447187

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Gnomonic
# 	Azi, Sph.
===============================================================================

-------------------------------------------------------------------------------
# Test material from Snyder p. 168, table 26.
# Tests the equatorial aspect of the projection.
-------------------------------------------------------------------------------
operation +proj=gnom +R=1
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       0
expect  0       0
roundtrip   100
accept  10      80
expect  0.1763  5.7588
roundtrip   100
accept  20      70
expect  0.3640  2.9238
roundtrip   100
accept  30      60
expect  0.5774  2.0000
roundtrip   100
accept  40      50
expect  0.8391  1.5557
roundtrip   100
accept  50      40
expect  1.1918  1.3054
roundtrip   100
accept  60      30
expect  1.7321  1.1547
roundtrip   100
accept  70      20
expect  2.7475  1.0642
roundtrip   100
accept  80      10
expect  5.6713  1.0154
roundtrip   100
accept  80      80
expect  5.6713  32.6596
roundtrip   100
accept  0   90
expect  failure errno coord_transfm_outside_projection_domain

# test that extreme northings are mapped to the sphere
direction   inverse
accept      0   1e8
expect      0   90


-------------------------------------------------------------------------------
# Test the northern polar aspect of the gnonomic projection
-------------------------------------------------------------------------------
operation +proj=gnom +R=1 +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       90
expect  0       0
roundtrip   100
accept  45      45
expect  0.7071  -0.7071
roundtrip   100
accept  0       0
expect  failure errno coord_transfm_outside_projection_domain
accept  90      0
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test the southern polar aspect of the gnonomic projection
-------------------------------------------------------------------------------
operation +proj=gnom +R=1 +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       -90
expect  0       0
roundtrip   100
accept  45      -45
expect  0.7071  0.7071
roundtrip   100
accept  0       0
expect  failure errno coord_transfm_outside_projection_domain
accept  90      0
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test the oblique aspect of the gnonomic projection
-------------------------------------------------------------------------------
operation +proj=gnom +R=1 +lat_0=45
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       45
expect  0       0
roundtrip   100
accept  0       0
expect  0       -1
roundtrip   100
accept  0       90
expect  0       1
roundtrip   100
accept  0       -45
expect  failure errno coord_transfm_outside_projection_domain

===============================================================================
# Goode Homolosine
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=goode   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223368.119026632 111701.072127637
accept  2 -1
expect  223368.119026632 -111701.072127637
accept  -2 1
expect  -223368.119026632 111701.072127637
accept  -2 -1
expect  -223368.119026632 -111701.072127637

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Mod. Stereographic of 48 U.S.
# 	Azi(mod)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=gs48 +R=6370997
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  -119.000000000 40.000000000
expect  -1923908.446529346 355874.658944479
accept  -70.000000000 64.000000000
expect  1354020.375109298 3040846.007866525
accept  -80.000000000 25.000000000
expect  1625139.160484320 -1413614.894029108
accept  -95.000000000 35.000000000
expect  90241.658071458 -439595.048485902

direction inverse
accept  -1923000.000000000 355000.000000000
expect  -118.987112613 39.994449789
accept  1354000.000000000 3040000.000000000
expect  -70.005208999 63.993387836
accept  1625000.000000000 -1413000.000000000
expect  -80.000346610 25.005602547
accept  90000.000000000 -439000.000000000
expect  -95.002606473 35.005424705


===============================================================================
# Mod. Stereographic of 50 U.S.
# 	Azi(mod)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=gs50 +ellps=clrk66
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  -160.000000000 65.000000000
expect  -1874628.537740233 2660907.942291015
accept  -130.000000000 45.000000000
expect  -771831.518853336 48465.166491305
accept  -65.000000000 45.000000000
expect  4030931.833981509 1323687.864777399
accept  -80.000000000 36.000000000
expect  3450764.261536101 -175619.041820732

# For some reason, does not fail on MacOSX
#accept  60 -45
#expect  failure errno coord_transfm_outside_projection_domain

direction inverse
accept  -1800000.000000000 2600000.000000000
expect  -157.989285000 64.851559610
accept  -800000.000000000 500000.000000000
expect  -131.171390467 49.084969746
accept  4000000.000000000 1300000.000000000
expect  -65.491568685 44.992837924
accept  3900000.000000000 -170000.000000000
expect  -75.550660091 34.191114076

-------------------------------------------------------------------------------
operation +proj=gs50 +R=6370997
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  -160.000000000 65.000000000
expect  -1867268.253460009 2656506.230401823
accept  -130.000000000 45.000000000
expect  -769572.189672994 48324.312440864
accept  -65.000000000 45.000000000
expect  4019393.068680791 1320191.309350289
accept  -80.000000000 36.000000000
expect  3442685.615172346 -178760.423489429

direction inverse
accept  -1800000.000000000 2600000.000000000
expect  -158.163295045 64.854288365
accept  -800000.000000000 500000.000000000
expect  -131.206816960 49.082915351
accept  4000000.000000000 1300000.000000000
expect  -65.348945221 44.957292682
accept  3900000.000000000 -170000.000000000
expect  -75.446820242 34.185406226


===============================================================================
# Hammer & Eckert-Greifendorff
# 	Misc Sph,
# 	W= M=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=hammer   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223373.788703241 111703.907397767
accept  2 -1
expect  223373.788703241 -111703.907397767
accept  -2 1
expect  -223373.788703241 111703.907397767
accept  -2 -1
expect  -223373.788703241 -111703.907397767

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


-------------------------------------------------------------------------------
operation +proj=hammer   +a=6400000 +W=1
-------------------------------------------------------------------------------
accept  -180 0
expect  failure errno coord_transfm_outside_projection_domain

===============================================================================
# Hatano Asymmetrical Equal Area
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=hatano   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  189878.878946528 131409.802440626
accept  2 -1
expect  189881.081952445 -131409.142276074
accept  -2 1
expect  -189878.878946528 131409.802440626
accept  -2 -1
expect  -189881.081952445 -131409.142276074

direction inverse
accept  200 100
expect  0.002106462 0.000760957
accept  200 -100
expect  0.002106462 -0.000760958
accept  -200 100
expect  -0.002106462 0.000760957
accept  -200 -100
expect  -0.002106462 -0.000760958


===============================================================================
# HEALPix
# 	Sph., Ellps.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=healpix   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222390.103949239 130406.588664482
accept  2 -1
expect  222390.103949239 -130406.588664481
accept  -2 1
expect  -222390.103949239 130406.588664482
accept  -2 -1
expect  -222390.103949239 -130406.588664481

direction inverse
accept  200 100
expect  0.001798641 0.000766795
accept  200 -100
expect  0.001798641 -0.000766795
accept  -200 100
expect  -0.001798641 0.000766795
accept  -200 -100
expect  -0.001798641 -0.000766795

-------------------------------------------------------------------------------
operation +proj=healpix   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 131588.044441999
accept  2 -1
expect  223402.144255274 -131588.044441999
accept  -2 1
expect  -223402.144255274 131588.044441999
accept  -2 -1
expect  -223402.144255274 -131588.044441999

direction inverse
accept  200 100
expect  0.001790493 0.000759909
accept  200 -100
expect  0.001790493 -0.000759909
accept  -200 100
expect  -0.001790493 0.000759909
accept  -200 -100
expect  -0.001790493 -0.000759909

-------------------------------------------------------------------------------
operation +proj=healpix   +R=6400000   +lat_1=0.5 +lat_2=2  +rot_xy=42
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  254069.735470912856 -51696.237925639456
accept  2 -1
expect  77970.559536809917 -247274.186569161975
accept  -2 1
expect  -77970.559536809917 247274.186569161975
accept  -2 -1
expect  -254069.735470912856 51696.237925639456

direction inverse
accept  254069.735470912856 -51696.237925639456
expect  2 1
accept  77970.559536809917 -247274.186569161975
expect  2 -1
accept  -77970.559536809917 247274.186569161975
expect  -2 1
accept  -254069.735470912856 51696.237925639456
expect  -2 -1


===============================================================================
# rHEALPix
# 	Sph., Ellps.
# 	north_square= south_square=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=rhealpix   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222390.103949239 130406.588664482
accept  2 -1
expect  222390.103949239 -130406.588664481
accept  -2 1
expect  -222390.103949239 130406.588664482
accept  -2 -1
expect  -222390.103949239 -130406.588664481

direction inverse
accept  200 100
expect  0.001798641 0.000766795
accept  200 -100
expect  0.001798641 -0.000766795
accept  -200 100
expect  -0.001798641 0.000766795
accept  -200 -100
expect  -0.001798641 -0.000766795

-------------------------------------------------------------------------------
operation +proj=rhealpix   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 131588.044441999
accept  2 -1
expect  223402.144255274 -131588.044441999
accept  -2 1
expect  -223402.144255274 131588.044441999
accept  -2 -1
expect  -223402.144255274 -131588.044441999

direction inverse
accept  200 100
expect  0.001790493 0.000759909
accept  200 -100
expect  0.001790493 -0.000759909
accept  -200 100
expect  -0.001790493 0.000759909
accept  -200 -100
expect  -0.001790493 -0.000759909

-------------------------------------------------------------------------------
operation +proj=rhealpix   +south_square=2  +north_square=3  +ellps=WGS84
-------------------------------------------------------------------------------
tolerance 1 m
accept  45 50
expect  10806592   10007554
accept  45 -50
expect  5003777   -5802815
accept  135 50
expect  15011332   5802815

direction inverse
accept  10806592   10007554
expect  45 50
accept  5003777   -5802815
expect  45 -50
accept  15011332   5802815
expect  135 50


===============================================================================
# Interrupted Goode Homolosine
# 	PCyl, Sph.
# (Each of the 12 sub-projections tested separately)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=igh   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223878.497456271 111701.072127637
roundtrip 1
accept  2 -1
expect  223708.371313058 -111701.072127637
roundtrip 1
accept  -2 1
expect  -222857.740596992 111701.072127637
roundtrip 1
accept  -2 -1
expect  -223027.866740205 -111701.072127637
roundtrip 1

accept -100.0 22.0
expect -11170107.212763708 2457423.5868080168
roundtrip 1
accept -30.0 22.0
expect -2863013.673043605 2457423.586808016
roundtrip 1
accept -100.0 67.0
expect -11170107.212763708 7205942.523056464
roundtrip 1
accept -30.0 67.0
expect 17045.719482862 7205942.523056464
roundtrip 1
accept -160.0 -22.0
expect -17872171.540421933 -2457423.586808016
roundtrip 1
accept -60.0 -22.0
expect -6702064.327658225 -2457423.586808016
roundtrip 1
accept 20.0 -22.0
expect 2234021.442552742 -2457423.586808016
roundtrip 1
accept 140.0 -22.0
expect 15638150.097869191 -2457423.586808016
roundtrip 1
accept -160.0 -67.0
expect -17872171.540421933 -7205942.523056464
roundtrip 1
accept -60.0 -67.0
expect -6702064.327658225 -7205942.523056464
roundtrip 1
accept 20.0 -67.0
expect 2234021.442552742 -7205942.523056464
roundtrip 1
accept 140.0 -67.0
expect 15638150.097869191 -7205942.523056464
roundtrip 1

direction inverse
accept  200 100
expect  0.001790489 0.000895247
accept  200 -100
expect  0.001790491 -0.000895247
accept  -200 100
expect  -0.001790497 0.000895247
accept  -200 -100
expect  -0.001790496 -0.000895247

===============================================================================
# Interrupted Goode Homolosine Ocean View
# 	PCyl, Sph.
# (Each of the 12 sub-projections tested separately)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=igh_o   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223197.992883418 111701.072127637
roundtrip 1
accept  2 -1
expect  223708.371313058 -111701.072127637
roundtrip 1
accept  -2 1
expect  -223538.245169845 111701.072127637
roundtrip 1
accept  -2 -1
expect  -223027.866740205 -111701.072127637
roundtrip 1

accept -140.0 22.0
expect -15638150.097869192 2457423.586808016
roundtrip 1
accept 170.0 70.0
expect 16560870.317293623 7463176.386461447
roundtrip 1
accept -10.0 22.0
expect -1117010.721276371 2457423.586808016
roundtrip 1
accept 130.0 22.0
expect 14521139.376592822 2457423.586808016
roundtrip 1
accept -170.0 70.0
expect -17167948.303394791 7463176.386461447
roundtrip 1
accept -140.0 67.0
expect -15638150.097869191 7205942.523056464
roundtrip 1
accept -10.0 67.0
expect -1117010.721276371 7205942.523056464
roundtrip 1
accept 130.0 67.0
expect 14521139.376592822 7205942.523056464
roundtrip 1
accept -110.0 -22.0
expect -12287117.934040081 -2457423.586808016
roundtrip 1
accept 20.0 -22.0
expect 2234021.442552742 -2457423.586808016
roundtrip 1
accept 150.0 -22.0
expect 16755160.819145568 -2457423.586808016
roundtrip 1
accept -110.0 -67.0
expect -12287117.934040081 -7205942.523056464
roundtrip 1
accept 20.0 -67.0
expect 2234021.442552742 -7205942.523056464
roundtrip 1
accept 95.0 -67.0
expect 13699006.578494834 -7205942.523056464
roundtrip 1
accept 150.0 -67.0
expect 16755160.819145564 -7205942.523056464
roundtrip 1

direction inverse
accept  200 100
expect  0.001790494 0.000895247
accept  200 -100
expect  0.001790491 -0.000895247
accept  -200 100
expect  -0.001790492 0.000895247
accept  -200 -100
expect  -0.001790496 -0.000895247

===============================================================================
# International Map of the World Polyconic
# 	Mod. Polyconic, Ell
# 	lat_1= and lat_2= [lon_1=]
===============================================================================

-------------------------------------------------------------------------------
operation +proj=imw_p   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222588.441139376 55321.128653810
accept  2 -1
expect  222756.906377687 -165827.584288324
accept  -2 1
expect  -222588.441139376 55321.128653810
accept  -2 -1
expect  -222756.906377687 -165827.584288324

direction inverse
accept  200 100
expect  0.001796699 0.500904924
accept  200 -100
expect  0.001796698 0.499095076
accept  -200 100
expect  -0.001796699 0.500904924
accept  -200 -100
expect  -0.001796698 0.499095076

-------------------------------------------------------------------------------
operation +proj=imw_p   +ellps=GRS80  +lat_1=0 +lat_2=10
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0 0
expect  0 0
accept  0.000000000000   0.000904928485
expect  0 100
accept  0.000898315284   0.000000000000
expect  100 0

direction inverse
accept  0 0
expect  0 0
accept  0 100
expect  0.000000000000   0.000904928485
accept  100 0
expect  0.000898315284   0.000000000000


===============================================================================
# Icosahedral Snyder Equal Area
# 	Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=isea   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  -1097074.948022474 3442909.309037183
accept  2 -1
expect  -1097074.948264795 3233611.728585708
accept  -2 1
expect  -1575486.353641554 3442168.342028188
accept  -2 -1
expect  -1575486.353880283 3234352.695594706

operation +proj=isea   +mode=hex +resolution=31
accept  0 0
expect  failure

===============================================================================
# Kavraisky V
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=kav5   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  200360.905308829 123685.082476998
accept  2 -1
expect  200360.905308829 -123685.082476998
accept  -2 1
expect  -200360.905308829 123685.082476998
accept  -2 -1
expect  -200360.905308829 -123685.082476998

direction inverse
accept  200 100
expect  0.001996259 0.000808483
accept  200 -100
expect  0.001996259 -0.000808483
accept  -200 100
expect  -0.001996259 0.000808483
accept  -200 -100
expect  -0.001996259 -0.000808483

-------------------------------------------------------------------------------
operation +proj=kav5   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  201047.703110878 124109.050629171
accept  2 -1
expect  201047.703110878 -124109.050629171
accept  -2 1
expect  -201047.703110878 124109.050629171
accept  -2 -1
expect  -201047.703110878 -124109.050629171

direction inverse
accept  200 100
expect  0.001989440 0.000805721
accept  200 -100
expect  0.001989440 -0.000805721
accept  -200 100
expect  -0.001989440 0.000805721
accept  -200 -100
expect  -0.001989440 -0.000805721


===============================================================================
# Kavraisky VII
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=kav7   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  193462.974943729 111701.072127637
accept  2 -1
expect  193462.974943729 -111701.072127637
accept  -2 1
expect  -193462.974943729 111701.072127637
accept  -2 -1
expect  -193462.974943729 -111701.072127637

direction inverse
accept  200 100
expect  0.002067483 0.000895247
accept  200 -100
expect  0.002067483 -0.000895247
accept  -200 100
expect  -0.002067483 0.000895247
accept  -200 -100
expect  -0.002067483 -0.000895247


===============================================================================
# Krovak
# 	PCyl., Ellps.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=krovak +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  -3196535.232563641 -6617878.867551444
accept  2 -1
expect  -3260035.440552109 -6898873.614878031
accept  -2 1
expect  -3756305.328869175 -6478142.561571511
accept  -2 -1
expect  -3831703.658501982 -6759107.170155395
accept  24.833333333333   59.757598563058
expect  0 0

direction inverse
accept  200 100
expect  24.836218919 59.758403933
accept  200 -100
expect  24.836315485 59.756888426
accept  -200 100
expect  24.830447748 59.758403933
accept  -200 -100
expect  24.830351182 59.756888426
accept  0 0
expect  24.833333333333   59.757598563058

-------------------------------------------------------------------------------
operation +proj=krovak +lat_0=-90
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

===============================================================================
# Laborde
# 	Cyl, Sph
# 	Special for Madagascar
===============================================================================

-------------------------------------------------------------------------------
operation +proj=labrd   +ellps=GRS80  +lon_0=0.5 +lat_0=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  166973.166090228 -110536.912730266
accept  2 -1
expect  166973.168287157 -331761.993650884
accept  -2 1
expect  -278345.500519976 -110469.032642032
accept  -2 -1
expect  -278345.504185270 -331829.870790275

direction inverse
accept  200 100
expect  0.501797719 2.000904357
accept  200 -100
expect  0.501797717 1.999095641
accept  -200 100
expect  0.498202281 2.000904357
accept  -200 -100
expect  0.498202283 1.999095641

-------------------------------------------------------------------------------
operation +proj=labrd   +ellps=GRS80 +lat_0=0
accept  0 0
expect  failure errno invalid_op_illegal_arg_value

===============================================================================
# Lambert Azimuthal Equal Area
# 	Azi, Sph&Ell
===============================================================================

-------------------------------------------------------------------------------
operation +proj=laea +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222602.471450095 110589.827224410
accept  2 -1
expect  222602.471450095 -110589.827224409
accept  -2 1
expect  -222602.471450095 110589.827224410
accept  -2 -1
expect  -222602.471450095 -110589.827224409

accept  180 0
expect  failure errno coord_transfm_outside_projection_domain

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

accept  13000000 0
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
operation +proj=laea +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223365.281370125 111716.668072916
accept  2 -1
expect  223365.281370125 -111716.668072916
accept  -2 1
expect  -223365.281370125 111716.668072916
accept  -2 -1
expect  -223365.281370125 -111716.668072916

accept  180 0
expect  failure errno coord_transfm_outside_projection_domain

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247

-------------------------------------------------------------------------------
# Test oblique aspect of the spherical form
-------------------------------------------------------------------------------
operation +proj=laea +R=1 +lat_0=45
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   45
expect  0   0
accept  0   0
expect  0   -0.7654
accept  0   90
expect  0   0.7654
accept  0   -45
expect  0   -1.4142
accept  45  45
expect  0.5194  0.1521

tolerance 0.1 mm
accept  45  45
roundtrip   100

# error when waaay outside the sphere
direction inverse
accept  0   10
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test oblique aspect of the ellipsoidal form
-------------------------------------------------------------------------------
operation +proj=laea +ellps=GRS80 +lat_0=45
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   45
expect  0   0
accept  0   0
expect  0   -4860248.8602
accept  0   90
expect  0   4886594.2207
accept  0   -45
expect  0   -8984728.0442
accept  45  45
expect  3318800.8682    968788.2336

tolerance 10 cm
accept  45 45
roundtrip 100

# test rho <EPS10
direction inverse
accept  0   0
expect  0   45

-------------------------------------------------------------------------------
# Test south polar aspect for the spherical form
-------------------------------------------------------------------------------
operation +proj=laea +R=1 +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   -90
expect  0   0
accept  0   0
expect  0   1.4142
accept  0   -45
expect  0   0.7654
accept  0   45
expect  0   1.8478
accept  0   90
expect failure errno coord_transfm_outside_projection_domain

tolerance 0.1 mm
accept  45  45
roundtrip   100

-------------------------------------------------------------------------------
# Test south polar aspect for the ellipsoidal form
-------------------------------------------------------------------------------
operation +proj=laea +ellps=GRS80 +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   -90
expect  0   0
accept  0   0
expect  0   9009964.7611
accept  0   -45
expect  0   4889334.8030
accept  0   45
expect  0   11766619.5307
accept  0   90
expect failure errno coord_transfm_outside_projection_domain

tolerance 10 cm
accept  45  45
roundtrip   100

# test q == 0.0 condition
direction inverse
accept  0   0
expect  0   -90

-------------------------------------------------------------------------------
# Test north polar aspect for the spherical form
-------------------------------------------------------------------------------
operation +proj=laea +R=1 +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   90
expect  0   0
accept  0   0
expect  0   -1.4142
accept  0   45
expect  0   -0.7654
accept  0   -45
expect  0   -1.8478
accept  0   -90
expect failure errno coord_transfm_outside_projection_domain

tolerance 0.1 mm
accept  45  45
roundtrip   100

-------------------------------------------------------------------------------
# Test north polar aspect
-------------------------------------------------------------------------------
operation +proj=laea +ellps=GRS80 +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0   90
expect  0   0
accept  0   0
expect  0   -9009964.7611
accept  0   45
expect  0   -4889334.8030
accept  0   -45
expect  0   -11766619.5307
accept  0   -90
expect failure errno coord_transfm_outside_projection_domain

tolerance 10 cm
accept 45   45
roundtrip   100

-------------------------------------------------------------------------------
# Test error in projection setup
-------------------------------------------------------------------------------
operation +proj=laea +ellps=GRS80 +lat_0=91
expect  failure errno invalid_op_illegal_arg_value

===============================================================================
# Lagrange
# 	Misc Sph, no inv.
# 	W= lat_1=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=lagrng   +a=6400000 +W=2   +lat_1=0.5
-------------------------------------------------------------------------------
tolerance   0.1 mm
accept      2 1
expect      111703.375917226 27929.831908033

roundtrip   100
accept      2 -1
expect      111699.122088816 -83784.178013358
roundtrip   100

accept      -2 1
expect      -111703.375917226 27929.831908033
roundtrip   100

accept      -2 -1
expect      -111699.122088816 -83784.178013358
roundtrip   100

accept      0   90
expect      0.0000  12800000.0
roundtrip   100

-------------------------------------------------------------------------------
operation +proj=lagrng +R=1 +lat_1=56
-------------------------------------------------------------------------------
tolerance   1 cm
accept      12  56
expect      0.10    0.0

-------------------------------------------------------------------------------
operation +proj=lagrng +R=1 +W=-1
-------------------------------------------------------------------------------
expect      failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lagrng +R=1 +lat_1=90.00001
-------------------------------------------------------------------------------
expect      failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lagrng +R=1 +W=0.5
-------------------------------------------------------------------------------
accept      90 0 0
expect      failure errno coord_transfm_outside_projection_domain

direction   inverse
accept      2 0 0
expect      failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
operation +proj=lagrng +R=1
-------------------------------------------------------------------------------
tolerance   0.1 mm
accept      0 89.9999999
expect      0 2
accept      0 -89.9999999
expect      0 -2

===============================================================================
# Larrivee
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=larr   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223393.637624201 111707.215961256
accept  2 -1
expect  223393.637624201 -111707.215961256
accept  -2 1
expect  -223393.637624201 111707.215961256
accept  -2 -1
expect  -223393.637624201 -111707.215961256


===============================================================================
# Laskowski
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=lask   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  217928.275907355 112144.329220142
accept  2 -1
expect  217928.275907355 -112144.329220142
accept  -2 1
expect  -217928.275907355 112144.329220142
accept  -2 -1
expect  -217928.275907355 -112144.329220142

===============================================================================
# Lambert Conformal Conic
# 	Conic, Sph&Ell
# 	lat_1= and lat_2= or lat_0, k_0=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=lcc   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222588.439735968 110660.533870800
accept  2 -1
expect  222756.879700279 -110532.797660827
accept  -2 1
expect  -222588.439735968 110660.533870800
accept  -2 -1
expect  -222756.879700279 -110532.797660827

direction inverse
accept  200 100
expect  0.001796359 0.000904232
accept  200 -100
expect  0.001796358 -0.000904233
accept  -200 100
expect  -0.001796359 0.000904232
accept  -200 -100
expect  -0.001796358 -0.000904233

-------------------------------------------------------------------------------
# Tests with +k_0 (Lambert Conformal Conic 2SP Michigan).
-------------------------------------------------------------------------------
operation +proj=lcc +ellps=GRS80 +lat_1=0.5 +lat_2=2 +k_0=1.0000382
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222596.942614366 110664.761103214
accept  2 -1
expect  222765.389013083 -110537.020013748
accept  -2 1
expect  -222596.942614366 110664.761103214
accept  -2 -1
expect  -222765.389013083 -110537.020013748

direction inverse
accept  200 100
expect  0.001796291 0.000904198
accept  200 -100
expect  0.001796290 -0.000904199
accept  -200 100
expect  -0.001796291 0.000904198
accept  -200 -100
expect  -0.001796290 -0.000904199


-------------------------------------------------------------------------------
# Test various corner cases, spherical projection and one standard
# parallel to improve test coverage.
-------------------------------------------------------------------------------
operation +proj=lcc +ellps=GRS80 +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept  0 90
expect  0 292411117.537843227
accept 0 0
expect 0 0

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=sphere +lat_1=30 +lat_2=40
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept 1 2
expect 129391.909521100 262101.674176860
accept 0 0
expect 0 0

direction inverse

accept 129391.909521100 262101.674176860
expect 1 2
accept 0 0
expect 0 0

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=GRS80 +lat_1=30 +lat_2=45
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept 1 2
expect 131833.493971117 265456.213515346
accept 1 -2
expect 137536.205750651 -269686.591917190
accept -1 2
expect -131833.493971117 265456.213515346
accept -1 -2
expect -137536.205750651 -269686.591917190

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=sphere +lat_1=30 +lat_2=45
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept 1 2
expect 131824.206082557 267239.875053699
accept -1 -2
expect -137565.475967350 -271546.945608449
accept 1 -2
expect 137565.475967350 -271546.945608449
accept -1 2
expect -131824.206082557 267239.875053699

direction inverse

accept 131824.206082557	267239.875053699
expect 1 2

-------------------------------------------------------------------------------
operation +proj=lcc +a=9999999 +b=.9 +lat_2=1
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
# This case is incredible. ossfuzz has found the exact value of lat_1 that
# triggers a division by zero
operation +proj=lcc +lat_1=2D32 +lat_2=0 +a=6378137 +b=0.2
-------------------------------------------------------------------------------
expect     failure
# For some reason fails on MacOSX with a different error
# errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=GRS80 +lat_1=0 +lat_2=90
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=GRS80 +lat_1=90 +lat_2=0
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=GRS80 +lat_1=90 +lat_2=90
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=sphere +lat_1=0 +lat_2=90
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=sphere +lat_1=90 +lat_2=0
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=sphere +lat_1=91
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=lcc +ellps=sphere +lat_2=91
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

===============================================================================
# Lambert Conformal Conic Alternative
# 	Conic, Sph&Ell
# 	lat_0=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=lcca   +ellps=GRS80  +lat_0=1 +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222605.285770237 67.806007272
accept  2 -1
expect  222740.037637937 -221125.539829602
accept  -2 1
expect  -222605.285770237 67.806007272
accept  -2 -1
expect  -222740.037637937 -221125.539829602

direction inverse
accept  200 100
expect  0.001796903 1.000904366
accept  200 -100
expect  0.001796902 0.999095633
accept  -200 100
expect  -0.001796903 1.000904366
accept  -200 -100
expect  -0.001796902 0.999095633


===============================================================================
# Lambert Equal Area Conic
# 	Conic, Sph&Ell
# 	lat_1= south
===============================================================================

-------------------------------------------------------------------------------
operation +proj=leac   +ellps=GRS80  +lat_1=0 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  220685.140542979 112983.500889396
accept  2 -1
expect  224553.312279826 -108128.636744873
accept  -2 1
expect  -220685.140542979 112983.500889396
accept  -2 -1
expect  -224553.312279826 -108128.636744873

direction inverse
accept  200 100
expect  0.001796645 0.000904352
accept  200 -100
expect  0.001796616 -0.000904387
accept  -200 100
expect  -0.001796645 0.000904352
accept  -200 -100
expect  -0.001796616 -0.000904387

-------------------------------------------------------------------------------
operation +proj=leac   +R=6400000    +lat_1=0 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  221432.868592852 114119.454526532
accept  2 -1
expect  225331.724127111 -109245.829435056
accept  -2 1
expect  -221432.868592852 114119.454526532
accept  -2 -1
expect  -225331.724127111 -109245.829435056

direction inverse
accept  200 100
expect  0.001790507 0.000895229
accept  200 -100
expect  0.001790479 -0.000895264
accept  -200 100
expect  -0.001790507 0.000895229
accept  -200 -100
expect  -0.001790479 -0.000895264


===============================================================================
# Lee Oblated Stereographic
# 	Azi(mod)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=lee_os   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  -25564478.952605054 154490848.828625500
accept  2 -1
expect  30115393.938574642 125193997.439701970
accept  -2 1
expect  -31039340.592166007 57678685.044891544
accept  -2 -1
expect  -3088419.939423571 58150091.099111013

direction inverse
accept  200 100
expect  -164.997479458 -9.998758861
accept  200 -100
expect  -164.997479439 -10.001241120
accept  -200 100
expect  -165.002520542 -9.998758861
accept  -200 -100
expect  -165.002520561 -10.001241120


===============================================================================
# Loximuthal
# 	PCyl Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=loxim   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223382.295791339 55850.536063819
accept  2 -1
expect  223393.637462243 -167551.608191456
accept  -2 1
expect  -223382.295791339 55850.536063819
accept  -2 -1
expect  -223393.637462243 -167551.608191456

direction inverse
accept  200 100
expect  0.001790561 0.500895247
accept  200 -100
expect  0.001790561 0.499104753
accept  -200 100
expect  -0.001790561 0.500895247
accept  -200 -100
expect  -0.001790561 0.499104753


===============================================================================
# Space oblique for LANDSAT
# 	Cyl, Sph&Ell
# 	lsat= path=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=lsat +ellps=GRS80 +lsat=1 +path=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  18241950.014558550 9998256.839822935
accept  2 -1
expect  18746856.253319457 10215761.669925211
accept  -2 1
expect  18565503.683633164 9085039.146727053
accept  -2 -1
expect  19019696.902028911 9247763.039432822

direction inverse
accept  200 100
expect  126.000423835 0.001723782
accept  200 -100
expect  126.002213738 0.001880155
accept  -200 100
expect  126.000734469 -0.001880155
accept  -200 -100
expect  126.002524373 -0.001723782

-------------------------------------------------------------------------------
operation +proj=lsat +path=1 +lsat=5 +ellps=sphere
-------------------------------------------------------------------------------
direction inverse
accept  0 1e10
expect  failure errno coord_transfm_outside_projection_domain

===============================================================================
# McBryde-Thomas Flat-Polar Sine (No. 1)
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=mbt_s   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  204131.517850273 121400.330225508
accept  2 -1
expect  204131.517850273 -121400.330225508
accept  -2 1
expect  -204131.517850273 121400.330225508
accept  -2 -1
expect  -204131.517850273 -121400.330225508

direction inverse
accept  200 100
expect  0.001959383 0.000823699
accept  200 -100
expect  0.001959383 -0.000823699
accept  -200 100
expect  -0.001959383 0.000823699
accept  -200 -100
expect  -0.001959383 -0.000823699

-------------------------------------------------------------------------------
operation +proj=mbt_s   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  204831.240570992 121816.466696035
accept  2 -1
expect  204831.240570992 -121816.466696035
accept  -2 1
expect  -204831.240570992 121816.466696035
accept  -2 -1
expect  -204831.240570992 -121816.466696035

direction inverse
accept  200 100
expect  0.001952689 0.000820885
accept  200 -100
expect  0.001952689 -0.000820885
accept  -200 100
expect  -0.001952689 0.000820885
accept  -200 -100
expect  -0.001952689 -0.000820885


===============================================================================
# McBryde-Thomas Flat-Pole Sine (No. 2)
# 	Cyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=mbt_fps   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  198798.176129850 125512.017254531
accept  2 -1
expect  198798.176129850 -125512.017254531
accept  -2 1
expect  -198798.176129850 125512.017254531
accept  -2 -1
expect  -198798.176129850 -125512.017254531

direction inverse
accept  200 100
expect  0.002011971 0.000796712
accept  200 -100
expect  0.002011971 -0.000796712
accept  -200 100
expect  -0.002011971 0.000796712
accept  -200 -100
expect  -0.002011971 -0.000796712


===============================================================================
# McBride-Thomas Flat-Polar Parabolic
# 	Cyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=mbtfpp   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  206804.786929820 120649.762565793
accept  2 -1
expect  206804.786929820 -120649.762565793
accept  -2 1
expect  -206804.786929820 120649.762565793
accept  -2 -1
expect  -206804.786929820 -120649.762565793

direction inverse
accept  200 100
expect  0.001933954 0.000828837
accept  200 -100
expect  0.001933954 -0.000828837
accept  -200 100
expect  -0.001933954 0.000828837
accept  -200 -100
expect  -0.001933954 -0.000828837


===============================================================================
# McBryde-Thomas Flat-Polar Quartic
# 	Cyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=mbtfpq   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  209391.854738393 119161.040199055
accept  2 -1
expect  209391.854738393 -119161.040199055
accept  -2 1
expect  -209391.854738393 119161.040199055
accept  -2 -1
expect  -209391.854738393 -119161.040199055

direction inverse
accept  200 100
expect  0.001910106 0.000839185
accept  200 -100
expect  0.001910106 -0.000839185
accept  -200 100
expect  -0.001910106 0.000839185
accept  -200 -100
expect  -0.001910106 -0.000839185


===============================================================================
# McBryde-Thomas Flat-Polar Sinusoidal
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=mbtfps   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  204740.117478572 121864.729719340
accept  2 -1
expect  204740.117478572 -121864.729719340
accept  -2 1
expect  -204740.117478572 121864.729719340
accept  -2 -1
expect  -204740.117478572 -121864.729719340

direction inverse
accept  200 100
expect  0.001953415 0.000820580
accept  200 -100
expect  0.001953415 -0.000820580
accept  -200 100
expect  -0.001953415 0.000820580
accept  -200 -100
expect  -0.001953415 -0.000820580


===============================================================================
# Mercator
# 	Cyl, Sph&Ell
# 	lat_ts=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=merc   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0 m
accept  0 0
expect  0 0
tolerance 50 nm
accept  2 1
expect  222638.981586547 110579.965218249
accept  2 -1
expect  222638.981586547 -110579.965218249
accept  -2 1
expect  -222638.981586547 110579.965218249
accept  -2 -1
expect  -222638.981586547 -110579.965218249
# inflate tolerance by scale (k = 5.7e15)
tolerance 3e8
accept  0 89.99999999999999
expect  0 235805185.015130176
accept  0 -89.99999999999999
expect  0 -235805185.015130176

direction inverse
tolerance 0 m
accept  0 0
expect  0 0
tolerance 50 nm
accept  200 100
expect  0.00179663056824 0.00090436947704
accept  200 -100
expect  0.00179663056824 -0.00090436947704
accept  -200 100
expect  -0.00179663056824 0.00090436947704
accept  -200 -100
expect  -0.00179663056824 -0.00090436947704
accept  0 235805185.015130176
expect  0 89.99999999999999
accept  0 -235805185.015130176
expect  0 -89.99999999999999
accept  0 1e10
expect  0 90
accept  0 -1e10
expect  0 -90

-------------------------------------------------------------------------------
operation +proj=merc   +R=6400000
-------------------------------------------------------------------------------
tolerance 0 m
accept  0 0
expect  0 0
tolerance 50 nm
accept  2 1
expect  223402.144255274 111706.743574944
accept  2 -1
expect  223402.144255274 -111706.743574944
accept  -2 1
expect  -223402.144255274 111706.743574944
accept  -2 -1
expect  -223402.144255274 -111706.743574944

direction inverse
tolerance 0 m
accept  0 0
expect  0 0
tolerance 50 nm
accept  200 100
expect  0.00179049310978 0.00089524655486
accept  200 -100
expect  0.00179049310978 -0.00089524655486
accept  -200 100
expect  -0.00179049310978 0.00089524655486
accept  -200 -100
expect  -0.00179049310978 -0.00089524655486

-------------------------------------------------------------------------------
operation +proj=merc +R=1
-------------------------------------------------------------------------------
# Test the numerical stability of the inverse spherical Mercator
-------------------------------------------------------------------------------
tolerance   1e-17 m
accept  0   57.295779513e-15
expect  0   1e-15

direction inverse
accept  0   1e-15
expect  0   57.295779513e-15


===============================================================================
# Miller Oblated Stereographic
# 	Azi(mod)
===============================================================================

-------------------------------------------------------------------------------
operation +proj=mil_os   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  -1908527.949594205 -1726237.473061448
accept  2 -1
expect  -1916673.022918485 -1943133.888125523
accept  -2 1
expect  -2344429.412089623 -1706258.051218912
accept  -2 -1
expect  -2354637.835532999 -1926468.605135417

direction inverse
accept  200 100
expect  20.002036394 18.000968347
accept  200 -100
expect  20.002036372 17.999031632
accept  -200 100
expect  19.997963606 18.000968347
accept  -200 -100
expect  19.997963628 17.999031632


===============================================================================
# Miller Cylindrical
# 	Cyl, Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=mill   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 111704.701754394
accept  2 -1
expect  223402.144255274 -111704.701754396
accept  -2 1
expect  -223402.144255274 111704.701754394
accept  -2 -1
expect  -223402.144255274 -111704.701754396

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Space oblique for MISR
# 	Cyl, Sph&Ell
# 	path=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=misrsom   +ellps=GRS80 +path=1
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  18556630.368369825 9533394.675311271
accept  2 -1
expect  19041866.006729737 9707182.175323525
accept  -2 1
expect  18816810.130184799 8647669.649802955
accept  -2 -1
expect  19252610.784536730 8778164.085801404

direction inverse
accept  200 100
expect  127.759503988 0.001735150
accept  200 -100
expect  127.761295471 0.001871966
accept  -200 100
expect  127.759775774 -0.001871966
accept  -200 -100
expect  127.761567257 -0.001735150

-------------------------------------------------------------------------------
operation +proj=misrsom   +R=6400000 +path=1
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  18641249.279170386 9563342.532334166
accept  2 -1
expect  19130982.461581279 9739539.593504636
accept  -2 1
expect  18903483.515011538 8675064.500617975
accept  -2 -1
expect  19343388.399800610 8807471.904068489

direction inverse
accept  200 100
expect  127.759505148 0.001716231
accept  200 -100
expect  127.761290324 0.001854121
accept  -200 100
expect  127.759780921 -0.001854121
accept  -200 -100
expect  127.761566096 -0.001716231


===============================================================================
# Mollweide
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=moll   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  201113.698641813 124066.283433860
roundtrip 1
accept  2 -1
expect  201113.698641813 -124066.283433860
roundtrip 1
accept  -2 1
expect  -201113.698641813 124066.283433860
roundtrip 1
accept  -2 -1
expect  -201113.698641813 -124066.283433860
roundtrip 1

direction inverse
accept  200 100
expect  0.001988738 0.000806005
accept  200 -100
expect  0.001988738 -0.000806005
accept  -200 100
expect  -0.001988738 0.000806005
accept  -200 -100
expect  -0.001988738 -0.000806005


===============================================================================
# Murdoch I
# 	Conic, Sph
# 	lat_1= and lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=murd1   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222600.813473554 111404.244180546
accept  2 -1
expect  222770.349287864 -111234.672856675
accept  -2 1
expect  -222600.813473554 111404.244180546
accept  -2 -1
expect  -222770.349287864 -111234.672856675

direction inverse
accept  200 100
expect  0.001796255 0.000898315
accept  200 -100
expect  0.001796254 -0.000898316
accept  -200 100
expect  -0.001796255 0.000898315
accept  -200 -100
expect  -0.001796254 -0.000898316

-------------------------------------------------------------------------------
operation +proj=murd1   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223363.845309492 111786.116032863
accept  2 -1
expect  223533.962259251 -111615.963451823
accept  -2 1
expect  -223363.845309492 111786.116032863
accept  -2 -1
expect  -223533.962259251 -111615.963451823

direction inverse
accept  200 100
expect  0.001790119 0.000895246
accept  200 -100
expect  0.001790118 -0.000895247
accept  -200 100
expect  -0.001790119 0.000895246
accept  -200 -100
expect  -0.001790118 -0.000895247


===============================================================================
# Murdoch II
# 	Conic, Sph
# 	lat_1= and lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=murd2   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222588.099751230 111426.140027412
accept  2 -1
expect  222757.726267018 -111341.431317505
accept  -2 1
expect  -222588.099751230 111426.140027412
accept  -2 -1
expect  -222757.726267018 -111341.431317505

direction inverse
accept  200 100
expect  0.001796357 0.000897887
accept  200 -100
expect  0.001796356 -0.000897888
accept  -200 100
expect  -0.001796357 0.000897887
accept  -200 -100
expect  -0.001796356 -0.000897888

-------------------------------------------------------------------------------
operation +proj=murd2   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223351.088007027 111808.086934388
accept  2 -1
expect  223521.295969170 -111723.087859673
accept  -2 1
expect  -223351.088007027 111808.086934388
accept  -2 -1
expect  -223521.295969170 -111723.087859673

direction inverse
accept  200 100
expect  0.001790221 0.000894820
accept  200 -100
expect  0.001790220 -0.000894821
accept  -200 100
expect  -0.001790221 0.000894820
accept  -200 -100
expect  -0.001790220 -0.000894821


===============================================================================
# Murdoch III
# 	Conic, Sph
# 	lat_1= and lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=murd3   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222600.814077577 111404.246601372
accept  2 -1
expect  222770.354733899 -111234.670432178
accept  -2 1
expect  -222600.814077577 111404.246601372
accept  -2 -1
expect  -222770.354733899 -111234.670432178

direction inverse
accept  200 100
expect  0.001796255 0.000898315
accept  200 -100
expect  0.001796254 -0.000898316
accept  -200 100
expect  -0.001796255 0.000898315
accept  -200 -100
expect  -0.001796254 -0.000898316

-------------------------------------------------------------------------------
operation +proj=murd3   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223363.845915585 111786.118461987
accept  2 -1
expect  223533.967723953 -111615.961019015
accept  -2 1
expect  -223363.845915585 111786.118461987
accept  -2 -1
expect  -223533.967723953 -111615.961019015

direction inverse
accept  200 100
expect  0.001790119 0.000895246
accept  200 -100
expect  0.001790118 -0.000895247
accept  -200 100
expect  -0.001790119 0.000895246
accept  -200 -100
expect  -0.001790118 -0.000895247


===============================================================================
# Natural Earth
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=natearth   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  194507.265257889 112508.737358295
accept  2 -1
expect  194507.265257889 -112508.737358295
accept  -2 1
expect  -194507.265257889 112508.737358295
accept  -2 -1
expect  -194507.265257889 -112508.737358295

direction inverse
accept  200 100
expect  0.002056383 0.000888824
accept  200 -100
expect  0.002056383 -0.000888824
accept  -200 100
expect  -0.002056383 0.000888824
accept  -200 -100
expect  -0.002056383 -0.000888824


===============================================================================
# Natural Earth 2
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=natearth2   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  189255.172934731 113022.495810907
accept  2 -1
expect  189255.172934731 -113022.495810907
accept  -2 1
expect  -189255.172934731 113022.495810907
accept  -2 -1
expect  -189255.172934731 -113022.495810907

direction inverse
accept  200 100
expect  0.002113449 0.000884780
accept  200 -100
expect  0.002113449 -0.000884780
accept  -200 100
expect  -0.002113449 0.000884780
accept  -200 -100
expect  -0.002113449 -0.000884780


===============================================================================
# Nell
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=nell   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223385.132504696 111698.236447187
accept  2 -1
expect  223385.132504696 -111698.236447187
accept  -2 1
expect  -223385.132504696 111698.236447187
accept  -2 -1
expect  -223385.132504696 -111698.236447187

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Nell-Hammer
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=nell_h   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223385.131640953 111698.236533562
accept  2 -1
expect  223385.131640953 -111698.236533562
accept  -2 1
expect  -223385.131640953 111698.236533562
accept  -2 -1
expect  -223385.131640953 -111698.236533562

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Nicolosi Globular
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=nicol   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223374.561814140 111732.553988545
accept  2 -1
expect  223374.561814140 -111732.553988545
accept  -2 1
expect  -223374.561814140 111732.553988545
accept  -2 -1
expect  -223374.561814140 -111732.553988545


===============================================================================
# Near-sided perspective
# 	Azi, Sph
# 	h=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=nsper   +a=6400000  +h=1000000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222239.816114100 111153.763991925
accept  2 -1
expect  222239.816114100 -111153.763991925
accept  -2 1
expect  -222239.816114100 111153.763991925
accept  -2 -1
expect  -222239.816114100 -111153.763991925

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247

-------------------------------------------------------------------------------
# Test north polar aspect
-------------------------------------------------------------------------------
operation +proj=nsper +R=1 +h=3 +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       90
expect  0       0
accept  45      45
expect  0.4555 -0.4555
roundtrip   100
accept  0       0
expect  failure errno coord_transfm_outside_projection_domain

direction inverse
accept  0       0
expect  0       90
accept  0       2   # projected coordinate is outside the sphere
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test south polar aspect
-------------------------------------------------------------------------------
operation +proj=nsper +R=1 +h=3 +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       -90
expect  0       0
accept  -45      -45
expect  -0.4555 0.4555
roundtrip   100
accept  0       0
expect  failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
operation +proj=nsper +R=1 +h=3 +lat_0=45
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0       45
expect  0       0
accept  0       90
expect  0       0.6442
accept  0       0
expect  0       -0.6442
accept  45      45
expect  0.4767  0.1396
roundtrip   100


-------------------------------------------------------------------------------
operation +proj=nsper +R=1 +h=0
-------------------------------------------------------------------------------
expect  failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=nsper +R=1 +h=1e11
-------------------------------------------------------------------------------
expect  failure errno invalid_op_illegal_arg_value


===============================================================================
# New Zealand Map Grid
# 	fixed Earth
===============================================================================

-------------------------------------------------------------------------------
operation +proj=nzmg   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  3352675144.747425100 -7043205391.100243600
accept  2 -1
expect  3691989502.779306400 -6729069415.332104700
accept  -2 1
expect  4099000768.453238500 -7863208779.667248700
accept  -2 -1
expect  4466166927.369976000 -7502531736.628604900

direction inverse
accept  200000.000000000 100000.000000000
expect  175.482086827 -69.422692183
accept  200000.000000000 -100000.000000000
expect  175.756819473 -69.533571088
accept  -200000.000000000 100000.000000000
expect  134.605119233 -61.459995711
accept  -200000.000000000 -100000.000000000
expect  134.333684316 -61.621553676


===============================================================================
# General Oblique Transformation
# 	Misc Sph
# 	o_proj= plus parameters for projection
# 	o_lat_p= o_lon_p= (new pole) or
# 	o_alpha= o_lon_c= o_lat_c= or
# 	o_lon_1= o_lat_1= o_lon_2= o_lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=ob_tran +R=6400000 +o_proj=latlon +o_lon_p=20 +o_lat_p=20 +lon_0=180
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  -2.685687214 1.237430235
accept  2 -1
expect  -2.695406975 1.202683395
accept  -2 1
expect  -2.899366393 1.237430235
accept  -2 -1
expect  -2.889646631 1.202683395

direction inverse
accept  200 100
expect  121.551874841 -2.536100157
accept  200 -100
expect  63.261184340 17.585319579
accept  -200 100
expect  -141.100733224 26.091712305
accept  -200 -100
expect  -65.862385599 51.830295078

-------------------------------------------------------------------------------
operation +proj=ob_tran +R=6400000 +o_proj +o_proj=ob_tran
-------------------------------------------------------------------------------
expect  failure errno invalid_op_missing_arg

===============================================================================
# Oblique Cylindrical Equal Area
# 	Cyl, Sphlonc= alpha= or
# 	lat_1= lat_2= lon_1= lon_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  19994423.837934088 223322.760576728
accept  2 -1
expect  20217962.128015257 223322.760576729
accept  -2 1
expect  19994423.837934092 -223322.760576727
accept  -2 -1
expect  20217962.128015265 -223322.760576725

direction inverse
accept  200 100
expect  179.999104753 0.001790493
accept  200 -100
expect  -179.999104753 0.001790493
accept  -200 100
expect  179.999104753 -0.001790493
accept  -200 -100
expect  -179.999104753 -0.001790493

# Direction North
-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_1=45 +lat_2=45.0000001 +lon_1=0 +lon_2=0
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  19994423.837934091687   223322.760576728586

-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_0=45 +alpha=0
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  19994423.837934091687   223322.760576728586


# Direction South
-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_1=45 +lat_2=44.999999 +lon_1=0 +lon_2=0
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  111769.145040585790   -223322.760576727480

-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_0=45 +alpha=180
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  111769.145040585790   -223322.760576727480


# Direction East
-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_1=45 +lat_2=45 +lon_1=0 +lon_2=1e-8
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  9742698.935838246718   4443057.188599349000

-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_0=45 +alpha=90
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  9742698.935838246718   4443057.188599349000


# Direction West
-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_1=45 +lat_2=45 +lon_1=0 +lon_2=-1e-8
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  10363494.047136424109   -4443057.188599349000

-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_0=45 +alpha=270
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  10363494.047136424109   -4443057.188599349000


# Direction North-East
-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_1=45 +lat_2=45.00001 +lon_1=0 +lon_2=1e-5
-------------------------------------------------------------------------------
# 3 mm needed for MacOSX...
tolerance 3 mm
accept  2 1
expect  18596261.668446537107   2747542.17330662999

-------------------------------------------------------------------------------
operation +proj=ocea   +a=6400000    +lat_0=45 +alpha=35.264383770917604
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  18596261.668446537107   2747542.17330662999

===============================================================================
# Oblated Equal Area
# 	Misc Sph
# 	n= m= theta=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=oea   +a=6400000 +n=1 +m=2 +theta=3
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  228926.872097864 99870.488430076
accept  2 -1
expect  217242.584036940 -123247.885607475
accept  -2 1
expect  -217242.584036940 123247.885607475
accept  -2 -1
expect  -228926.872097864 -99870.488430076

direction inverse
accept  200 100
expect  0.001741186 0.000987727
accept  200 -100
expect  0.001834893 -0.000800312
accept  -200 100
expect  -0.001834893 0.000800312
accept  -200 -100
expect  -0.001741186 -0.000987727


===============================================================================
# Oblique Mercator
# 	Cyl, Sph&Ell no_rot
# 	alpha= [gamma=] [no_off] lonc= or
# 	 lon_1= lat_1= lon_2= lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=omerc   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222650.796885261 110642.229314984
accept  2 -1
expect  222650.796885261 -110642.229314984
accept  -2 1
expect  -222650.796885262 110642.229314984
accept  -2 -1
expect  -222650.796885262 -110642.229314984

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=omerc   +ellps=GRS80  +lat_1=0.5 +lat_2=2 +no_rot
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  110642.229314984 222650.796885261
accept  2 -1
expect  -110642.229314984 222650.796885261
accept  -2 1
expect  110642.229314984 -222650.796885262
accept  -2 -1
expect  -110642.229314984 -222650.796885262

direction inverse
accept  200 100
expect  0.000898315 0.001808739
accept  200 -100
expect  -0.000898315 0.001808739
accept  -200 100
expect  0.000898315 -0.001808739
accept  -200 -100
expect  -0.000898315 -0.001808739

-------------------------------------------------------------------------------
operation +proj=omerc   +R=1 +lat_0=1 +lat_1=2 +no_rot
-------------------------------------------------------------------------------
direction inverse
accept  0 1e200
expect  failure errno coord_transfm_outside_projection_domain



# Direction North-East
-------------------------------------------------------------------------------
operation +proj=omerc   +a=6400000    +lat_0=45 +lat_1=45 +lat_2=45.00001 +lon_1=0 +lon_2=1e-5
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  -3569.825230822232   -5093592.310871849768

-------------------------------------------------------------------------------
operation +proj=omerc   +a=6400000    +lat_0=45 +alpha=35.264383770917604
-------------------------------------------------------------------------------
tolerance 1 mm
accept  2 1
expect  -3569.825230822232   -5093592.310871849768

-------------------------------------------------------------------------------
operation +proj=omerc   +R=1  +alpha=0 +lat_0=90
-------------------------------------------------------------------------------
expect  failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=omerc   +lat_1=0.1 +a=6400000 +b=1
-------------------------------------------------------------------------------
# Disabled since fails on i386. Not so important. Edge condition found by ossfuzz
#expect  failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=omerc    +lat_1=0.8 +a=6400000 +b=.4
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=omerc    +lat_1=91
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
operation +proj=omerc    +lat_2=91
-------------------------------------------------------------------------------
expect     failure errno invalid_op_illegal_arg_value


===============================================================================
# Ortelius Oval
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=ortel   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223374.577355253 111701.072127637
accept  2 -1
expect  223374.577355253 -111701.072127637
accept  -2 1
expect  -223374.577355253 111701.072127637
accept  -2 -1
expect  -223374.577355253 -111701.072127637


===============================================================================
# Orthographic
# 	Azi, Sph&Ell.
===============================================================================

-------------------------------------------------------------------------------
# Test the equatorial aspect of the Orthographic projection.

# Test data from Snyder (1987), table 22, p. 151.
-------------------------------------------------------------------------------
operation   +proj=ortho +R=1 +lat_0=0 +lon_0=0
-------------------------------------------------------------------------------
tolerance   0.1 mm
accept      0       0
expect      0       0
roundtrip   100
accept      0       90
expect      0       1
roundtrip   100
accept      0       30
expect      0       0.5000
roundtrip   100
accept      10      50
expect      0.1116  0.7660
roundtrip   100
accept      20      40
expect      0.2620  0.6428
roundtrip   100
accept      30      80
expect      0.0868  0.9848
roundtrip   100
accept      40      70
expect      0.2198  0.9397
roundtrip   100
accept      50      60
expect      0.3830  0.8660
roundtrip   100
accept      60      50
expect      0.5567  0.7660
roundtrip   100
accept      70      20
expect      0.8830  0.3420
roundtrip   100
accept      80      10
expect      0.9698  0.1736
roundtrip   100
accept      90      90
expect      0       1
roundtrip   100
accept      120     0
expect      failure errno coord_transfm_outside_projection_domain

direction   inverse
accept      2   2
expect      failure errno coord_transfm_outside_projection_domain


-------------------------------------------------------------------------------
# Test the oblique aspect of the Orthographic projection.

# Test data from Snyder (1987), table 23, pp. 152-153.
-------------------------------------------------------------------------------
operation   +proj=ortho +R=1 +lat_0=40 +lon_0=0
-------------------------------------------------------------------------------
tolerance   0.1 mm
accept      0.0     90
expect      0.0     0.7660
roundtrip   100
accept      20      60
expect      0.1710  0.3614
roundtrip   100
accept      40     -30
expect      0.5567  -0.8095
roundtrip   100
accept      100     70
expect      0.3368  0.7580
roundtrip   100
accept      130     40
expect      0.5868  0.8089
roundtrip   100
accept      170     60
expect      0.0868  0.9799
roundtrip   100
accept      140     20
expect      failure errno coord_transfm_outside_projection_domain

direction   inverse
accept      2   2
expect      failure errno coord_transfm_outside_projection_domain


-------------------------------------------------------------------------------
# Test the north polar aspect of the Orthographic projection.
-------------------------------------------------------------------------------
operation   +proj=ortho +R=1 +lat_0=90 +lon_0=0
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept      0   0
expect      0   -1
roundtrip   100
accept      180 0
expect      0   1
roundtrip   100
accept      90  90
expect      0   0
roundtrip   100
accept      0   90
expect      0   0
roundtrip   100
accept      90  0
expect      1   0
roundtrip   100
accept      180 -90
expect      failure errno coord_transfm_outside_projection_domain
accept      0   -45
expect      failure errno coord_transfm_outside_projection_domain

direction   inverse
accept      2   2
expect      failure errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# Test the south polar aspect of the Orthographic projection.
-------------------------------------------------------------------------------
operation   +proj=ortho +R=1 +lat_0=-90 +lon_0=0
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept      0   0
expect      0   1
roundtrip   100
accept      180 0
expect      0   -1
roundtrip   100
accept      90  -90
expect      0   0
roundtrip   100
accept      0   -90
expect      0   0
roundtrip   100
accept      90  0
expect      1   0
roundtrip   100
accept      180 90
expect      failure errno coord_transfm_outside_projection_domain
accept      0   45
expect      failure errno coord_transfm_outside_projection_domain

direction   inverse
accept      2   2
expect      failure errno coord_transfm_outside_projection_domain

# Put a point a tiny tiny bit outside the radius of the sphere.
# Since we are right at the numerical limit of floating point representation
# it should still results in a correct coordinate.
accept      0.70710678118 0.7071067812
expect      45  0


-------------------------------------------------------------------------------
# Test the ellipsoidal formulation

# Test data from Guidance Note 7 part 2, March 2020,  p. 90
-------------------------------------------------------------------------------
operation   +proj=ortho +ellps=WGS84 +lat_0=55 +lon_0=5
-------------------------------------------------------------------------------
tolerance   1 mm
accept      2.12955      53.80939444444444
expect      -189011.711  -128640.567
roundtrip   1

-------------------------------------------------------------------------------
# Oblique
-------------------------------------------------------------------------------
operation   +proj=ortho +ellps=WGS84 +lat_0=30

# On boundary of visibility domain.
direction   forward
tolerance   0.1 mm
accept      -90             0
expect      -6378137        18504.1253

# This test is fragile. Note the slightly important tolerance
# direction   inverse
# tolerance   100 mm
# accept      -6378137        18504.125313223721605027
# expect      -90             0

# Slightly outside
direction   inverse
accept      -6378137.001    18504.1253
expect      failure errno coord_transfm_outside_projection_domain

# On boundary of visibility domain
direction   forward
tolerance   0.1 mm
accept      0               -60
expect      0               -6343601.0991

# Just on it, but fails to converge. This test might be fragile
direction   inverse
accept      0               -6343601.099075031466782093
expect      failure errno coord_transfm_outside_projection_domain

# Slightly inside
direction   inverse
tolerance   0.1 mm
accept      0               -6343600
expect      0               -59.966377950099655436

# At pole or very close to it
direction   forward
tolerance   0.1 mm
accept      0              90
expect      0              5523613.1150

direction   inverse
tolerance   0.1 mm
accept      0              5523613.1150
expect      0              90

direction   forward
tolerance   0.1 mm
accept      0              89.99999999
expect      0              5523613.1145
roundtrip   1

direction   forward
tolerance   0.1 mm
accept      180            89.99999999
expect      0              5523613.1156
roundtrip   1

direction   forward
tolerance   0.1 mm
accept      90             89.99999999
expect      0.0011         5523613.1150
# Roundrip doesn't work on 32bit builds
#roundtrip   1

direction   forward
tolerance   0.1 mm
accept      -90            89.99999999
expect      -0.0011        5523613.1150
roundtrip   1

# Southern hemisphere
operation   +proj=ortho +ellps=WGS84 +lat_0=-30

# At pole or very close to it
direction   forward
tolerance   0.1 mm
accept      0              -90
expect      0              -5523613.1150

direction   inverse
tolerance   0.1 mm
accept      0              -5523613.1150
expect      0              -90

direction   forward
tolerance   0.1 mm
accept      0              -89.99999999
expect      0              -5523613.1145
roundtrip   1

direction   forward
tolerance   0.1 mm
accept      180            -89.99999999
expect      0              -5523613.1156
roundtrip   1

-------------------------------------------------------------------------------
# Oblique
# Test case from https://github.com/OSGeo/PROJ/issues/2844
-------------------------------------------------------------------------------
operation   +proj=ortho +ellps=WGS84 +lon_0=23.0 +lat_0=37.0
tolerance   0.1 mm
accept      120.0           84.0
expect      663929.0678     5118338.2423
roundtrip   1

tolerance   0.1 mm
accept      -132.1803037588 60.171013757
expect      -1335097.4621   6158336.7476
roundtrip   1

tolerance   0.1 mm
accept      171.66658585876 55.670679094
expect      1874756.6327    6061693.5075
roundtrip   1

-------------------------------------------------------------------------------
# Equatorial
-------------------------------------------------------------------------------
operation   +proj=ortho +ellps=WGS84
tolerance   0.1 mm
accept      0               0
expect      0               0
roundtrip   1

accept      1               1
expect      111296.9991     110568.7748
roundtrip   1

accept      1               -1
expect      111296.9991     -110568.7748
roundtrip   1

accept      -1              1
expect      -111296.9991    110568.7748
roundtrip   1

accept      -1              -1
expect      -111296.9991    -110568.7748
roundtrip   1

accept      89.99           0
expect      6378136.9029    0
roundtrip   1

accept      -89.99          0
expect      -6378136.9029   0
roundtrip   1

accept      0               89.99
expect      0               6356752.2167
roundtrip   1

accept      0               -89.99
expect      0               -6356752.2167
roundtrip   1

# Point not visible from the projection plane
accept      90.00001        0
expect      failure         errno coord_transfm_outside_projection_domain

# Point not visible from the projection plane
accept      -90.00001       0
expect      failure         errno coord_transfm_outside_projection_domain

# Consistent with WGS84 semi-major axis
accept      90              0
expect      6378137         0
roundtrip   1

accept      -90             0
expect      -6378137        0
roundtrip   1

# Consistent with WGS84 semi-minor axis
accept      0               90
expect      0               6356752.3142
roundtrip   1

accept      0               -90
expect      0               -6356752.3142
roundtrip   1

# Point not visible from the projection plane
direction   inverse
accept      0               6356752.3143
expect      failure         errno coord_transfm_outside_projection_domain

# Point not visible from the projection plane
direction   inverse
accept      1000            6356752.314
expect      failure         errno coord_transfm_outside_projection_domain

# Point not visible from the projection plane
direction   inverse
accept      6378137.0001    0
expect      failure         errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# North pole tests
-------------------------------------------------------------------------------
operation   +proj=ortho +ellps=WGS84 +lat_0=90
tolerance   0.1 mm
accept      0               90
expect      0               0
roundtrip   1

accept      30              45
expect      2258795.4394   -3912348.4650
roundtrip   1

accept      135             89.999999873385
expect      0.01            0.01
roundtrip   1

# Point not visible from the projection plane
accept      0               -0.0000001
expect      failure         errno coord_transfm_outside_projection_domain

# Consistent with WGS84 semi-major axis
accept      0               0
expect      0               -6378137
roundtrip   1

# Point not visible from the projection plane
direction   inverse
accept      0               -6378137.1
expect      failure         errno coord_transfm_outside_projection_domain

-------------------------------------------------------------------------------
# South pole tests
-------------------------------------------------------------------------------
operation   +proj=ortho +ellps=WGS84 +lat_0=-90
tolerance   0.1 mm
accept      0               -90
expect      0               0
roundtrip   1

accept      135             -89.999999873385
expect      0.01            -0.01
roundtrip   1

# Point not visible from the projection plane
accept      0               0.0000001
expect      failure         errno coord_transfm_outside_projection_domain

# Consistent with WGS84 semi-major axis
accept      0               0
expect      0               6378137
roundtrip   1

# Point not visible from the projection plane
direction   inverse
accept      0               6378137.1
expect      failure         errno coord_transfm_outside_projection_domain


===============================================================================
# Perspective Conic
# 	Conic, Sph
# 	lat_1= and lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=pconic   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222588.098841617 111416.604770067
accept  2 -1
expect  222757.718091090 -111331.881531080
accept  -2 1
expect  -222588.098841617 111416.604770067
accept  -2 -1
expect  -222757.718091090 -111331.881531080

direction inverse
accept  200 100
expect  0.001796358 0.000897964
accept  200 -100
expect  0.001796356 -0.000897965
accept  -200 100
expect  -0.001796358 0.000897964
accept  -200 -100
expect  -0.001796356 -0.000897965

-------------------------------------------------------------------------------
operation +proj=pconic   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223351.087094295 111798.518992055
accept  2 -1
expect  223521.287765217 -111713.505338457
accept  -2 1
expect  -223351.087094295 111798.518992055
accept  -2 -1
expect  -223521.287765217 -111713.505338457

direction inverse
accept  200 100
expect  0.001790221 0.000894897
accept  200 -100
expect  0.001790220 -0.000894897
accept  -200 100
expect  -0.001790221 0.000894897
accept  -200 -100
expect  -0.001790220 -0.000894897


===============================================================================
# Patterson Cylindrical
# 	Cyl.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=patterson   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 113354.250397780
accept  2 -1
expect  223402.144255274 -113354.250397780
accept  -2 1
expect  -223402.144255274 113354.250397780
accept  -2 -1
expect  -223402.144255274 -113354.250397780

direction inverse
accept  200 100
expect  0.001790493 0.000882190
accept  200 -100
expect  0.001790493 -0.000882190
accept  -200 100
expect  -0.001790493 0.000882190
accept  -200 -100
expect  -0.001790493 -0.000882190


===============================================================================
# Polyconic (American)
# 	Conic, Sph&Ell
===============================================================================

-------------------------------------------------------------------------------
operation +proj=poly   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222605.285770237 110642.194561440
accept  2 -1
expect  222605.285770237 -110642.194561440
accept  -2 1
expect  -222605.285770237 110642.194561440
accept  -2 -1
expect  -222605.285770237 -110642.194561440

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=poly   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223368.105210219 111769.110491225
accept  2 -1
expect  223368.105210219 -111769.110491225
accept  -2 1
expect  -223368.105210219 111769.110491225
accept  -2 -1
expect  -223368.105210219 -111769.110491225

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Putnins P1
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp1   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  211642.762754160 105831.180787330
accept  2 -1
expect  211642.762754160 -105831.180787330
accept  -2 1
expect  -211642.762754160 105831.180787330
accept  -2 -1
expect  -211642.762754160 -105831.180787330

direction inverse
accept  200 100
expect  0.001889802 0.000944901
accept  200 -100
expect  0.001889802 -0.000944901
accept  -200 100
expect  -0.001889802 0.000944901
accept  -200 -100
expect  -0.001889802 -0.000944901


===============================================================================
# Putnins P2
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp2   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  211638.039634339 117895.033043380
accept  2 -1
expect  211638.039634339 -117895.033043380
accept  -2 1
expect  -211638.039634339 117895.033043380
accept  -2 -1
expect  -211638.039634339 -117895.033043380

direction inverse
accept  200 100
expect  0.001889802 0.000848202
accept  200 -100
expect  0.001889802 -0.000848202
accept  -200 100
expect  -0.001889802 0.000848202
accept  -200 -100
expect  -0.001889802 -0.000848202


===============================================================================
# Putnins P3
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp3   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  178227.115507794 89124.560786088
accept  2 -1
expect  178227.115507794 -89124.560786088
accept  -2 1
expect  -178227.115507794 89124.560786088
accept  -2 -1
expect  -178227.115507794 -89124.560786088

direction inverse
accept  200 100
expect  0.002244050 0.001122025
accept  200 -100
expect  0.002244050 -0.001122025
accept  -200 100
expect  -0.002244050 0.001122025
accept  -200 -100
expect  -0.002244050 -0.001122025


===============================================================================
# Putnins P3'
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp3p   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  178238.118539985 89124.560786088
accept  2 -1
expect  178238.118539985 -89124.560786088
accept  -2 1
expect  -178238.118539985 89124.560786088
accept  -2 -1
expect  -178238.118539985 -89124.560786088

direction inverse
accept  200 100
expect  0.002244050 0.001122025
accept  200 -100
expect  0.002244050 -0.001122025
accept  -200 100
expect  -0.002244050 0.001122025
accept  -200 -100
expect  -0.002244050 -0.001122025


===============================================================================
# Putnins P4'
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp4p   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  195241.477349386 127796.782307926
accept  2 -1
expect  195241.477349386 -127796.782307926
accept  -2 1
expect  -195241.477349386 127796.782307926
accept  -2 -1
expect  -195241.477349386 -127796.782307926

direction inverse
accept  200 100
expect  0.002048528 0.000782480
accept  200 -100
expect  0.002048528 -0.000782480
accept  -200 100
expect  -0.002048528 0.000782480
accept  -200 -100
expect  -0.002048528 -0.000782480


===============================================================================
# Putnins P5
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp5   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  226367.213380562 113204.568558475
accept  2 -1
expect  226367.213380562 -113204.568558475
accept  -2 1
expect  -226367.213380562 113204.568558475
accept  -2 -1
expect  -226367.213380562 -113204.568558475

direction inverse
accept  200 100
expect  0.001766713 0.000883357
accept  200 -100
expect  0.001766713 -0.000883357
accept  -200 100
expect  -0.001766713 0.000883357
accept  -200 -100
expect  -0.001766713 -0.000883357


===============================================================================
# Putnins P5'
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp5p   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  226388.175248756 113204.568558475
accept  2 -1
expect  226388.175248756 -113204.568558475
accept  -2 1
expect  -226388.175248756 113204.568558475
accept  -2 -1
expect  -226388.175248756 -113204.568558475

direction inverse
accept  200 100
expect  0.001766713 0.000883357
accept  200 -100
expect  0.001766713 -0.000883357
accept  -200 100
expect  -0.001766713 0.000883357
accept  -200 -100
expect  -0.001766713 -0.000883357


===============================================================================
# Putnins P6
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp6   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  226369.395133403 110218.523796521
accept  2 -1
expect  226369.395133403 -110218.523796521
accept  -2 1
expect  -226369.395133403 110218.523796521
accept  -2 -1
expect  -226369.395133403 -110218.523796521

direction inverse
accept  200 100
expect  0.001766713 0.000907296
accept  200 -100
expect  0.001766713 -0.000907296
accept  -200 100
expect  -0.001766713 0.000907296
accept  -200 -100
expect  -0.001766713 -0.000907296


===============================================================================
# Putnins P6'
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=putp6p   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  198034.195132195 125989.475461323
accept  2 -1
expect  198034.195132195 -125989.475461323
accept  -2 1
expect  -198034.195132195 125989.475461323
accept  -2 -1
expect  -198034.195132195 -125989.475461323

direction inverse
accept  200 100
expect  0.002019551 0.000793716
accept  200 -100
expect  0.002019551 -0.000793716
accept  -200 100
expect  -0.002019551 0.000793716
accept  -200 -100
expect  -0.002019551 -0.000793716


===============================================================================
# Quartic Authalic
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=qua_aut   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222613.549033097 111318.077887984
accept  2 -1
expect  222613.549033097 -111318.077887984
accept  -2 1
expect  -222613.549033097 111318.077887984
accept  -2 -1
expect  -222613.549033097 -111318.077887984

direction inverse
accept  200 100
expect  0.001796631 0.000898315
accept  200 -100
expect  0.001796631 -0.000898315
accept  -200 100
expect  -0.001796631 0.000898315
accept  -200 -100
expect  -0.001796631 -0.000898315

-------------------------------------------------------------------------------
operation +proj=qua_aut   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223376.624524029 111699.654379186
accept  2 -1
expect  223376.624524029 -111699.654379186
accept  -2 1
expect  -223376.624524029 111699.654379186
accept  -2 -1
expect  -223376.624524029 -111699.654379186

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Quadrilateralized Spherical Cube
# 	Azi, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=qsc   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  304638.450843852 164123.870923794
accept  2 -1
expect  304638.450843852 -164123.870923794
accept  -2 1
expect  -304638.450843852 164123.870923794
accept  -2 -1
expect  -304638.450843852 -164123.870923794

direction inverse
accept  200 100
expect  0.001321341 0.000610653
accept  200 -100
expect  0.001321341 -0.000610653
accept  -200 100
expect  -0.001321341 0.000610653
accept  -200 -100
expect  -0.001321341 -0.000610653

-------------------------------------------------------------------------------
operation +proj=qsc   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  305863.792402891 165827.722754715
accept  2 -1
expect  305863.792402891 -165827.722754715
accept  -2 1
expect  -305863.792402891 165827.722754715
accept  -2 -1
expect  -305863.792402891 -165827.722754715

direction inverse
accept  200 100
expect  0.001316827 0.000604493
accept  200 -100
expect  0.001316827 -0.000604493
accept  -200 100
expect  -0.001316827 0.000604493
accept  -200 -100
expect  -0.001316827 -0.000604493


===============================================================================
# Robinson
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=robin   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  189588.423282508 107318.530350703
accept  2 -1
expect  189588.423282508 -107318.530350703
accept  -2 1
expect  -189588.423282508 107318.530350703
accept  -2 -1
expect  -189588.423282508 -107318.530350703

accept  0 89.5
expect  0.000000000000   8639799.718722090125

accept  0 90
expect  0.000000000000   8654720.000000000000

accept  0 -89.5
expect  0.000000000000   -8639799.718722090125

accept  0 -90
expect  0.000000000000   -8654720.000000000000

direction inverse
accept  200 100
expect  0.002109689 0.000931806
accept  200 -100
expect  0.002109689 -0.000931806
accept  -200 100
expect  -0.002109689 0.000931806
accept  -200 -100
expect  -0.002109689 -0.000931806

accept  0.000000000000   8639799.718722090125
expect  0 89.5

accept  0.000000000000   8654720.000000000000
expect  0 90

accept  0.000000000000   -8639799.718722090125
expect  0 -89.5

accept  0.000000000000   -8654720.000000000000
expect  0 -90

accept 17250000 100000
expect failure errno coord_transfm_outside_projection_domain


===============================================================================
# Roussilhe Stereographic
# 	Azi., Ellps.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=rouss   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222644.894131617 110611.091868370
accept  2 -1
expect  222644.894131617 -110611.091868370
accept  -2 1
expect  -222644.894131617 110611.091868370
accept  -2 -1
expect  -222644.894131617 -110611.091868370

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369


===============================================================================
# Rectangular Polyconic
# 	Conic, Sph., no inv.
# 	lat_ts=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=rpoly   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223368.098302014 111769.110486991
accept  2 -1
expect  223368.098302014 -111769.110486991
accept  -2 1
expect  -223368.098302014 111769.110486991
accept  -2 -1
expect  -223368.098302014 -111769.110486991

===============================================================================
# S2
# Input lats are converted from nice spherical values to
# messy ellipsoidal lats e.g. 45 vs 45.1924232....
===============================================================================

-------------------------------------------------------------------------------
operation +proj=s2   +ellps=WGS84 +lat_0=0 +lon_0=0 +UVtoST=linear
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept 0 0
expect 0.5 0.5
accept 0 45.19242321598196
expect 0.5 1
accept 0 -45.19242321598196
expect 0.5 0
accept -45 0
expect 0 0.5
accept 45 0
expect 1 0.5
accept  -45 -35.446011426401625
expect  0 0
accept  45 -35.446011426401625
expect  1 0
accept  45 35.446011426401625
expect  1 1
accept  -45 35.446011426401625
expect  0 1
accept 20 20.124006563576454
expect 0.6819851171331012 0.6936645165744716
accept 20 -20.124006563576454
expect 0.6819851171331012 0.3063354834255284
accept -20 -20.124006563576454
expect 0.31801488286689883 0.3063354834255284
accept -20 20.124006563576454
expect 0.31801488286689883 0.6936645165744716

direction inverse
accept  0.5 0.5
expect  0 0
accept 0.5 1
expect 0 45.19242321598196
accept 0.5 0
expect 0 -45.19242321598196
accept 0 0.5
expect -45 0
accept 1 0.5
expect 45 0
accept 0 0
expect -45 -35.446011426401625
accept 1 0 
expect 45 -35.446011426401625
accept 1 1
expect 45 35.446011426401625
accept 0 1
expect -45 35.446011426401625
accept 0.6819851171331012 0.6936645165744716
expect 20 20.124006563576454
accept 0.6819851171331012 0.3063354834255284
expect 20 -20.124006563576454
accept 0.31801488286689883 0.3063354834255284
expect -20 -20.124006563576454
accept 0.31801488286689883 0.6936645165744716
expect -20 20.124006563576454

-------------------------------------------------------------------------------
operation +proj=s2   +ellps=WGS84 +lat_0=0 +lon_0=90 +UVtoST=quadratic
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept 90 0
expect 0.5 0.5
accept 70 20.124006563576454
expect 0.27682804555233764 0.7351848576118168
accept 110 20.124006563576454
expect 0.7231719544476624 0.7351848576118168

direction inverse
accept 0.5 0.5
expect 90 0
accept 0.27682804555233764 0.7351848576118168
expect 70 20.124006563576454
accept 0.7231719544476624 0.7351848576118168
expect 110 20.124006563576454

-------------------------------------------------------------------------------
operation +proj=s2   +ellps=WGS84 +lat_0=90 +UVtoST=tangent
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept 0 90
expect 0.5 0.5
accept 20 70.12337013762532
expect 0.29020309743436806 0.4211558922141421
accept -20 70.12337013762532
expect 0.29020309743436806 0.5788441077858579

direction inverse
accept 0.5 0.5
expect 0 90
accept 0.29020309743436806 0.4211558922141421
expect 20 70.12337013762532
accept 0.29020309743436806 0.5788441077858579
expect -20 70.12337013762532

-------------------------------------------------------------------------------
operation +proj=s2   +ellps=WGS84 +lat_0=0 +lon_0=180 +UVtoST=none
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept 180 0
expect 0 0
accept 160 20.124006563576454
expect -0.3873290331489431 -0.3639702342662023
accept -160 20.124006563576454
expect -0.3873290331489431 0.3639702342662023

direction inverse
accept 0 0
expect 180 0
accept -0.3873290331489431 -0.3639702342662023
expect 160 20.124006563576454
accept -0.3873290331489431 0.3639702342662023
expect -160 20.124006563576454

-------------------------------------------------------------------------------
operation +proj=s2   +ellps=WGS84 +lat_0=0 +lon_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept -90 0
expect 0.5 0.5
accept -70 20.124006563576454
expect 0.26481514238818316 0.7231719544476624
accept -110 20.124006563576454
expect 0.26481514238818316 0.27682804555233764

direction inverse
accept 0.5 0.5
expect -90 0
accept 0.26481514238818316 0.7231719544476624
expect -70 20.124006563576454
accept 0.26481514238818316 0.27682804555233764
expect -110 20.124006563576454

-------------------------------------------------------------------------------
operation +proj=s2   +ellps=WGS84 +lat_0=-90 +UVtoST=linear
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept 0 -90
expect 0.5 0.5
accept 20 -70.12337013762533
expect 0.5622425758450019 0.6710100716628344
accept -20 -70.12337013762533
expect 0.4377574241549981 0.6710100716628344

direction inverse
accept 0.5 0.5
expect 0 -90
accept 0.5622425758450019 0.6710100716628344
expect 20 -70.12337013762533
accept 0.4377574241549981 0.6710100716628344
expect -20 -70.12337013762533

-------------------------------------------------------------------------------
operation +proj=s2   +ellps=WGS84 +lat_0=0 +lon_0=0 +UVtoST=invalid
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept 0 0
expect failure errno invalid_op_illegal_arg_value

===============================================================================
# Sinusoidal (Sanson-Flamsteed)
# 	PCyl, Sph&Ell
===============================================================================

-------------------------------------------------------------------------------
operation +proj=sinu   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222605.299539466 110574.388554153
accept  2 -1
expect  222605.299539466 -110574.388554153
accept  -2 1
expect  -222605.299539466 110574.388554153
accept  -2 -1
expect  -222605.299539466 -110574.388554153

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=sinu   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223368.119026632 111701.072127637
accept  2 -1
expect  223368.119026632 -111701.072127637
accept  -2 1
expect  -223368.119026632 111701.072127637
accept  -2 -1
expect  -223368.119026632 -111701.072127637

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Swiss. Obl. Mercator
# 	Cyl, Ell
# 	For CH1903
===============================================================================

-------------------------------------------------------------------------------
operation +proj=somerc   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222638.981586547 110579.965218249
accept  2 -1
expect  222638.981586547 -110579.965218251
accept  -2 1
expect  -222638.981586547 110579.965218249
accept  -2 -1
expect  -222638.981586547 -110579.965218251

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=somerc   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223402.144255274 111706.743574944
accept  2 -1
expect  223402.144255274 -111706.743574945
accept  -2 1
expect  -223402.144255274 111706.743574944
accept  -2 -1
expect  -223402.144255274 -111706.743574945

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Stereographic
# 	Azi, Sph&Ell
# 	lat_ts=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=stere   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222644.854550117 110610.883474174
accept  2 -1
expect  222644.854550117 -110610.883474174
accept  -2 1
expect  -222644.854550117 110610.883474174
accept  -2 -1
expect  -222644.854550117 -110610.883474174

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=stere   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223407.810259507 111737.938996443
accept  2 -1
expect  223407.810259507 -111737.938996443
accept  -2 1
expect  -223407.810259507 111737.938996443
accept  -2 -1
expect  -223407.810259507 -111737.938996443

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


-------------------------------------------------------------------------------
operation +proj=stere   +ellps=GRS80        +lat_0=90  +lat_ts=70
-------------------------------------------------------------------------------
tolerance 1e-15m
accept    0 90
expect    0 0
roundtrip 1

tolerance 0.1 mm
accept    20            70
expect    748315.3282   -2055979.4669
roundtrip 1

# Polar Stereographic Variant B, ellipsoidal
-------------------------------------------------------------------------------
operation +proj=stere   +ellps=GRS80        +lat_0=-90  +lat_ts=-70
-------------------------------------------------------------------------------
tolerance 1e-15m
accept    0 -90
expect    0 0
roundtrip 1

tolerance 0.1 mm
accept    20            -70
expect    748315.3282   2055979.4669
roundtrip 1

# Polar Stereographic Variant A, ellipsoidal
-------------------------------------------------------------------------------
operation +proj=stere   +ellps=GRS80       +lat_0=-90  +k_0=0.97
-------------------------------------------------------------------------------
tolerance 1e-15m
accept    0 -90
expect    0 0
roundtrip 1

tolerance 0.1 mm
accept    20            -70
expect    748424.7446   2056280.0858
roundtrip 1

# Polar Stereographic Variant B, spherical
-------------------------------------------------------------------------------
operation +proj=stere   +R=6378137        +lat_0=-90  +lat_ts=-70
-------------------------------------------------------------------------------
tolerance 1e-15m
accept    0 -90
expect    0 0
roundtrip 1

tolerance 0.1 mm
accept    20            -70
expect    746100.2968   2049893.7182
roundtrip 1


# Polar Stereographic Variant A, spherical
# k_0 = (1 + math.sin(abs(lat_ts=-70) / 180. * math.pi)) / 2.0
-------------------------------------------------------------------------------
operation +proj=stere   +R=6378137        +lat_0=-90  +k_0=0.9698463103929542
-------------------------------------------------------------------------------
tolerance 1e-15m
accept    0 -90
expect    0 0
roundtrip 1

tolerance 0.1 mm
accept    20            -70
expect    746100.2968   2049893.7182
roundtrip 1

===============================================================================
# Oblique Stereographic Alternative
# 	Azimuthal, Sph&Ell
===============================================================================

-------------------------------------------------------------------------------
operation +proj=sterea   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222644.894109198 110611.091871737
accept  2 -1
expect  222644.894109198 -110611.091871738
accept  -2 1
expect  -222644.894109198 110611.091871737
accept  -2 -1
expect  -222644.894109198 -110611.091871738

direction inverse
accept  200 100
expect  0.001796631 0.000904369
accept  200 -100
expect  0.001796631 -0.000904369
accept  -200 100
expect  -0.001796631 0.000904369
accept  -200 -100
expect  -0.001796631 -0.000904369

-------------------------------------------------------------------------------
operation +proj=sterea   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223407.810259507 111737.938996443
accept  2 -1
expect  223407.810259507 -111737.938996443
accept  -2 1
expect  -223407.810259507 111737.938996443
accept  -2 -1
expect  -223407.810259507 -111737.938996443
accept  180 0
expect  failure errno coord_transfm_outside_projection_domain

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247

-------------------------------------------------------------------------------
operation +proj=sterea   +ellps=GRS80    +lat_0=90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0 90
expect  0 0
accept  0 89
expect  0.000000000000   -111696.700323081997
accept  0 45
expect  0.000000000000   -5291160.727484324016
accept  0 0
expect  0.000000000000   -12713600.098641794175

-------------------------------------------------------------------------------
operation +proj=sterea   +ellps=GRS80    +lat_0=89
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0 90
expect  0.000000000000   111696.700314355621
accept  0 89
expect  0.000000000000   0
accept  0 45
expect  0.000000000000   -5160845.342319893651
accept  0 0
expect  0.000000000000   -12493602.143489977345

-------------------------------------------------------------------------------
operation +proj=sterea   +ellps=GRS80    +lat_0=-90
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0 -90
expect  0 0
accept  0 -89
expect  0.000000000000   111696.700323081997
accept  0 -45
expect  0.000000000000   5291160.727484324016
accept  0 0
expect  0.000000000000   12713600.098641794175

-------------------------------------------------------------------------------
operation +proj=sterea   +ellps=GRS80   +lat_0=-89
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0 -90
expect  0.000000000000   -111696.700314355621
accept  0 -89
expect  0.000000000000   0
accept  0 -45
expect  0.000000000000   5160845.342319893651
accept  0 0
expect  0.000000000000   12493602.143489977345


operation +proj=sterea   +a=9999 +b=.9 +lat_0=73
expect failure

===============================================================================
# Gauss-Schreiber Transverse Mercator (aka Gauss-Laborde Reunion)
# 	Cyl, Sph&Ell
# 	lat_0= lon_0= k_0=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=gstmerc   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223413.466406322 111769.145040586
accept  2 -1
expect  223413.466406322 -111769.145040587
accept  -2 1
expect  -223413.466406323 111769.145040586
accept  -2 -1
expect  -223413.466406323 -111769.145040587

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Transverse Central Cylindrical
# 	Cyl, Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tcc   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223458.844192458 111769.145040586
accept  2 -1
expect  223458.844192458 -111769.145040586
accept  -2 1
expect  -223458.844192458 111769.145040586
accept  -2 -1
expect  -223458.844192458 -111769.145040586


===============================================================================
# Transverse Cylindrical Equal Area
# 	Cyl, Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tcea   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223322.760576727 111769.145040586
accept  2 -1
expect  223322.760576727 -111769.145040586
accept  -2 1
expect  -223322.760576727 111769.145040586
accept  -2 -1
expect  -223322.760576727 -111769.145040586

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Times
# 	Cyl, Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=times +ellps=sphere
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  0.000000000 0.000000000
expect  0.000000000 0.000000000
accept  80.000000000 70.000000000
expect  5785183.576067096 7615452.066120422
accept  25.000000000 -10.000000000
expect  2065971.530107881 -951526.064849459
accept  -35.000000000 20.000000000
expect  -2873054.045485095 1917730.953000521
accept  -45.000000000 -30.000000000
expect  -3651383.203521487 -2914213.457815921

direction inverse
accept  0.000000000 0.000000000
expect  0.000000000 0.000000000
accept  5785183.576067096 7615452.066120422
expect  80.000000000 70.000000000
accept  2065971.530107881 -951526.064849459
expect  25.000000000 -10.000000000
accept  -2873054.045485095 1917730.953000521
expect  -35.000000000 20.000000000
accept  -3651383.203521487 -2914213.457815921
expect  -45.000000000 -30.000000000


===============================================================================
# Tissot
# 	Conic, Sph
# 	lat_1= and lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tissot   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222641.078699631 54347.828487281
accept  2 -1
expect  222810.614513941 -168291.088549939
accept  -2 1
expect  -222641.078699631 54347.828487281
accept  -2 -1
expect  -222810.614513941 -168291.088549939

direction inverse
accept  200 100
expect  0.001796281 0.513444955
accept  200 -100
expect  0.001796279 0.511648325
accept  -200 100
expect  -0.001796281 0.513444955
accept  -200 -100
expect  -0.001796279 0.511648325

-------------------------------------------------------------------------------
operation +proj=tissot   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223404.248556849 54534.122161158
accept  2 -1
expect  223574.365506608 -168867.957323528
accept  -2 1
expect  -223404.248556849 54534.122161158
accept  -2 -1
expect  -223574.365506608 -168867.957323528

direction inverse
accept  200 100
expect  0.001790144 0.513441886
accept  200 -100
expect  0.001790143 0.511651393
accept  -200 100
expect  -0.001790144 0.513441886
accept  -200 -100
expect  -0.001790143 0.511651393


===============================================================================
# Transverse Mercator
# 	Cyl, Sph&Ell
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tmerc   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 50 nm
accept  2 1
expect  222650.796797586 110642.229411933
accept  2 -1
expect  222650.796797586 -110642.229411933
accept  -2 1
expect  -222650.796797586 110642.229411933
accept  -2 -1
expect  -222650.796797586 -110642.229411933
# near pole
accept  30 89.9999
expect  5.584698978 10001956.056248082
# 3900 km from central meridian
accept  44.69 35.37
expect  4168136.489446198 4985511.302287407

direction inverse
accept  200 100
expect  0.00179663056816 0.00090436947663
accept  200 -100
expect  0.00179663056816 -0.00090436947663
accept  -200 100
expect  -0.00179663056816 0.00090436947663
accept  -200 -100
expect  -0.00179663056816 -0.00090436947663
# near pole
accept  6 1.0001e7
expect  0.35596960759234 89.99135362646302
# 3900 km from central meridian
accept  4168136.489446198 4985511.302287407
expect  44.69 35.37

-------------------------------------------------------------------------------
operation +proj=tmerc   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223413.466406322 111769.145040597
accept  2 -1
expect  223413.466406322 -111769.145040597
accept  -2 1
expect  -223413.466406322 111769.145040597
accept  -2 -1
expect  -223413.466406322 -111769.145040597

accept    0      0
expect    0      0
roundtrip 1

accept    0      90
expect    0      10053096.491487337276
roundtrip 1

accept    0      -90
expect    0      -10053096.491487337276
roundtrip 1

accept    170                       60
expect    557076.820490954560       13361866.764138307422
roundtrip 1

accept    89                        0.01
expect    30344312.098578717560     64001.116414904580
roundtrip 1

accept    91                        0.01
expect    30344312.098578717560     20042191.866555366665
roundtrip 1

accept    179.999                   0.01
expect    111.701070433669          20105075.972255337983
roundtrip 1

accept    -89                       0.01
expect    -30344312.098578717560    64001.116414904580
roundtrip 1

accept    -91                       0.01
expect    -30344312.098578717560    20042191.866555366665
roundtrip 1

accept    -179.999                  0.01
expect    -111.701070433669         20105075.972255337983
roundtrip 1

accept    89                       -0.01
expect    30344312.098578717560    -64001.116414904580
roundtrip 1

accept    91                       -0.01
expect    30344312.098578717560    -20042191.866555366665
roundtrip 1

accept    179.999                  -0.01
expect    111.701070433669         -20105075.972255337983
roundtrip 1

accept    -89                      -0.01
expect    -30344312.098578717560   -64001.116414904580
roundtrip 1

accept    -91                       -0.01
expect    -30344312.098578717560    -20042191.866555366665
roundtrip 1

accept    -179.999                  -0.01
expect    -111.701070433669         -20105075.972255337983
roundtrip 1

accept    150                       0
expect    3515559.323737951461      20106192.982974674553
roundtrip 1

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247

-------------------------------------------------------------------------------
# Approx tmerc on a almost spherical ellipsoid, lat_0 north hemisphere

operation +proj=tmerc   +a=6400000 +rf=1e12 +k=0.9 +lat_0=40 +approx
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept    0             -30
expect    0             -7037167.5440
roundtrip 1

accept    1             -30
expect    87064.5795    -7037547.4590
roundtrip 1

accept    -1            -30
expect    -87064.5795   -7037547.4590
roundtrip 1

accept    0             30
expect    0             -1005309.6491
roundtrip 1

accept    0             40
expect    0             0
roundtrip 1

accept    1             41
expect    75872.2182    100965.3718
roundtrip 1

-------------------------------------------------------------------------------
# Approx tmerc on a sphere, lat_0 north hemisphere

operation +proj=tmerc   +R=6400000 +k=0.9 +lat_0=40
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept    0             -30
expect    0             -7037167.5440
roundtrip 1

accept    1             -30
expect    87064.5795    -7037547.4590
roundtrip 1

accept    -1            -30
expect    -87064.5795   -7037547.4590
roundtrip 1

accept    0             30
expect    0             -1005309.6491
roundtrip 1

accept    0             40
expect    0             0
roundtrip 1

accept    1             41
expect    75872.2182    100965.3718
roundtrip 1

-------------------------------------------------------------------------------
# Approx tmerc on a almost spherical ellipsoid, lat_0 south hemisphere

operation +proj=tmerc   +a=6400000 +rf=1e12 +k=0.9 +lat_0=-40 +approx
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept    0             -30
expect    0             1005309.6491
roundtrip 1

accept    1             -30
expect    87064.5795    1004929.7341
roundtrip 1

accept    -1            -30
expect    -87064.5795   1004929.7341
roundtrip 1

accept    0             30
expect    0             7037167.5440
roundtrip 1

accept    0             -40
expect    0             0
roundtrip 1

accept    1             -41
expect    75872.2182    -100965.3718
roundtrip 1

-------------------------------------------------------------------------------
# Approx tmerc on a sphere, lat_0 south hemisphere

operation +proj=tmerc   +R=6400000 +k=0.9 +lat_0=-40
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept    0             -30
expect    0             1005309.6491
roundtrip 1

accept    1             -30
expect    87064.5795    1004929.7341
roundtrip 1

accept    -1            -30
expect    -87064.5795   1004929.7341
roundtrip 1

accept    0             30
expect    0             7037167.5440
roundtrip 1

accept    0             -40
expect    0             0
roundtrip 1

accept    1             -41
expect    75872.2182    -100965.3718
roundtrip 1

-------------------------------------------------------------------------------
operation +proj=tmerc   +R=1
-------------------------------------------------------------------------------
direction inverse
accept -1e200 0
expect  failure errno coord_transfm_outside_projection_domain


===============================================================================
# Test Transverse Mercator +algo
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tmerc +ellps=GRS80 +algo=auto
# We show that the values are the same as poder_engsager within 0.1 mm
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept    2.9                 0
expect    322965.3802         0.0000
roundtrip 1

accept    2.9                 40
expect    247660.7575         4433559.6623
roundtrip 1

accept    2.9                 85
expect    28218.2464          9444221.7042
roundtrip 1

accept    6                   0
expect    669149.3483         0.0000
roundtrip 1

accept    6                   40
expect    512526.6344         4446813.3655
roundtrip 1

accept    6                   85
expect    58302.0560          9446554.0371
roundtrip 1

-------------------------------------------------------------------------------
operation +proj=tmerc +ellps=GRS80 +algo=poder_engsager
# Same values as above
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept    2.9                 0
expect    322965.3802         0.0000
roundtrip 1

accept    2.9                 40
expect    247660.7575         4433559.6623
roundtrip 1

accept    2.9                 85
expect    28218.2464          9444221.7042
roundtrip 1

accept    6                   0
expect    669149.3483         0.0000
roundtrip 1

accept    6                   40
expect    512526.6344         4446813.3655
roundtrip 1

accept    6                   85
expect    58302.0560          9446554.0371
roundtrip 1

-------------------------------------------------------------------------------
operation +proj=tmerc +ellps=GRS80 +algo=evenden_snyder
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept    2.9                 0
expect    322965.3802         0.0000
roundtrip 1

accept    2.9                 40
expect    247660.7575         4433559.6623
roundtrip 1

accept    2.9                 85
expect    28218.2464          9444221.7042
roundtrip 1

# Small difference with poder_engsager
accept    6                   0
expect    669149.3474         0.0000
#roundtrip 1

# Small difference with poder_engsager
accept    6                   40
expect    512526.6346         4446813.3655
#roundtrip 1

accept    6                   85
expect    58302.0560          9446554.0371
roundtrip 1

===============================================================================
# Tobler-Mercator
# 	Cyl, Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tobmerc +ellps=sphere
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222322.011656333081 111200.520030584055
accept  2 -1
expect  222322.011656333081 -111200.520030584463
accept  -2 1
expect  -222322.011656333081 111200.520030584055
accept  -2 -1
expect  -222322.011656333081 -111200.520030584463

accept  2 75
expect  14897.288383530242 12917766.123520378023
accept  2 80
expect  6705.871450149517 15521316.299485698342
accept  -2 -75
expect  -14897.288383530242 -12917766.123520381749
accept  -2 -80
expect  -6705.871450149517 -15521316.299485692754

direction inverse
accept  200 100
expect  0.001798644059 0.000899322029
accept  200 -100
expect  0.001798644059 -0.000899322029
accept  -200 100
expect  -0.001798644059 0.000899322029
accept  -200 -100
expect  -0.001798644059 -0.000899322029

accept  14897.288383530242 12917766.123520378023
expect  2 75
accept  6705.871450149517 15521316.299485698342
expect  2 80
accept  -14897.288383530242 -12917766.123520381749
expect  -2 -75
accept  -6705.871450149517 -15521316.299485692754
expect  -2 -80

-------------------------------------------------------------------------------
operation +proj=tobmerc +R=1
-------------------------------------------------------------------------------
# Test the numerical stability of the inverse spherical Tobler-Mercator
-------------------------------------------------------------------------------
tolerance   1e-15 m
accept  0   1e-15
expect  0   1e-15

-------------------------------------------------------------------------------
operation +proj=tobmerc +ellps=sphere
-------------------------------------------------------------------------------
# Test expected failure at the poles:
-------------------------------------------------------------------------------
accept  0 90
expect  failure errno coord_transfm_outside_projection_domain

accept  0 -90
expect  failure errno coord_transfm_outside_projection_domain


===============================================================================
# Two Point Equidistant
# 	Misc Sph
# 	lat_1= lon_1= lat_2= lon_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tpeqd   +ellps=GRS80  +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.18 mm
accept  2 1
expect  -27750.758831679 -222599.403691777
accept  2 -1
expect  -250434.937024036 -222655.938193266
accept  -2 1
expect  -27750.758831679 222599.403691777
accept  -2 -1
expect  -250434.937024036 222655.938193266

direction inverse
accept  200 100
expect  -0.000898556 1.251796630
accept  200 -100
expect  0.000898556 1.251796630
accept  -200 100
expect  -0.000898554 1.248203369
accept  -200 -100
expect  0.000898554 1.248203369

-------------------------------------------------------------------------------
operation +proj=tpeqd   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  -27845.882978485 -223362.430695260
accept  2 -1
expect  -251293.378764651 -223419.158985908
accept  -2 1
expect  -27845.882978485 223362.430695260
accept  -2 -1
expect  -251293.378764651 223419.158985908

direction inverse
accept  200 100
expect  -0.000895486 1.251790493
accept  200 -100
expect  0.000895486 1.251790493
accept  -200 100
expect  -0.000895485 1.248209507
accept  -200 -100
expect  0.000895485 1.248209507

-------------------------------------------------------------------------------
operation +proj=tpeqd   +a=6400000    +lat_1=90 +lat_2=90 +lon_1=0 +lon_2=1
-------------------------------------------------------------------------------
expect  failure

===============================================================================
# Tilted perspective
# 	Azi, Sph
# 	tilt= azi= h=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=tpers   +a=6400000  +h=1000000 +azi=20
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  170820.288955531 180460.865555805
accept  2 -1
expect  246853.941538942 -28439.878035775
accept  -2 1
expect  -246853.941538942 28439.878035775
accept  -2 -1
expect  -170820.288955531 -180460.865555805

direction inverse
accept  200 100
expect  0.001988706 0.000228872
accept  200 -100
expect  0.001376321 -0.001453641
accept  -200 100
expect  -0.001376321 0.001453641
accept  -200 -100
expect  -0.001988706 -0.000228872
accept  0 0
expect  0 0

-------------------------------------------------------------------------------
operation +proj=tpers   +a=6400000  +h=1000000 +tilt=20
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  213598.340357101 113687.930830744
accept  2 -1
expect  231609.982792523 -123274.645577324
accept  -2 1
expect  -213598.340357101 113687.930830744
accept  -2 -1
expect  -231609.982792523 -123274.645577324

direction inverse
accept  200 100
expect  0.001790554 0.000841285
accept  200 -100
expect  0.001790432 -0.000841228
accept  -200 100
expect  -0.001790554 0.000841285
accept  -200 -100
expect  -0.001790432 -0.000841228


===============================================================================
# Universal Polar Stereographic
# 	Azi, Ell
# 	south
===============================================================================

-------------------------------------------------------------------------------
operation +proj=ups   +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  2433455.563438467 -10412543.301512826
accept  2 -1
expect  2448749.118568199 -10850493.419804076
accept  -2 1
expect  1566544.436561533 -10412543.301512826
accept  -2 -1
expect  1551250.881431801 -10850493.419804076

direction inverse
accept  200 100
expect  -44.998567498 64.918236287
accept  200 -100
expect  -44.995702709 64.917020251
accept  -200 100
expect  -45.004297076 64.915804281
accept  -200 -100
expect  -45.001432287 64.914588378

-------------------------------------------------------------------------------
operation +proj=ups   +a=6400000
-------------------------------------------------------------------------------
# ups not possible on sphere
expect failure errno invalid_op_illegal_arg_value

===============================================================================
# Urmaev V
# 	PCyl., Sph., no inv.
# 	n= q= alpha=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=urm5   +a=6400000    +n=0.5
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223393.638433964 111696.818785117
accept  2 -1
expect  223393.638433964 -111696.818785117
accept  -2 1
expect  -223393.638433964 111696.818785117
accept  -2 -1
expect  -223393.638433964 -111696.818785117

operation +proj=urm5   +a=6400000    +n=1 +alpha=90
expect  failure errno invalid_op_illegal_arg_value

===============================================================================
# Urmaev Flat-Polar Sinusoidal
# 	PCyl, Sph.
# 	n=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=urmfps   +a=6400000 +n=0.5
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  196001.708134192 127306.843329993
accept  2 -1
expect  196001.708134192 -127306.843329993
accept  -2 1
expect  -196001.708134192 127306.843329993
accept  -2 -1
expect  -196001.708134192 -127306.843329993

direction inverse
accept  200 100
expect  0.002040721 0.000785474
accept  200 -100
expect  0.002040721 -0.000785474
accept  -200 100
expect  -0.002040721 0.000785474
accept  -200 -100
expect  -0.002040721 -0.000785474


===============================================================================
# Universal Transverse Mercator (UTM)
# 	Cyl, Ell
# 	zone= south
===============================================================================

-------------------------------------------------------------------------------
operation +proj=utm   +ellps=GRS80 +zone=30
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  1057002.405491298 110955.141175949
accept  2 -1
expect  1057002.405491298 -110955.141175949
accept  -2 1
expect  611263.812278905 110547.105696804
accept  -2 -1
expect  611263.812278905 -110547.105696804

direction inverse
accept  200 100
expect  -7.486952083 0.000901940
accept  200 -100
expect  -7.486952083 -0.000901940
accept  -200 100
expect  -7.490535682 0.000901935
accept  -200 -100
expect  -7.490535682 -0.000901935


operation +proj=utm +zone=32
tolerance 0.001 mm
accept	12 56 0 2000
expect  687071.43910944  6210141.32674801    0.00000000     2000.0000

operation +proj=utm +zone=32 +approx
tolerance 0.001 mm
accept	12 56 0 2000
expect  687071.43911000  6210141.32675053    0.00000000     2000.0000


-------------------------------------------------------------------------------
operation +proj=utm   +a=6400000 +zone=30
-------------------------------------------------------------------------------
# utm not possible on sphere
expect failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
# Test that we error out when the UTM zone is not an integer (#2671)
-------------------------------------------------------------------------------
operation   +proj=utm +zone=11ooops +ellps=WGS84
expect      failure   errno invalid_op_illegal_arg_value

===============================================================================
# van der Grinten (I)
# 	Misc Sph
===============================================================================

-------------------------------------------------------------------------------
operation +proj=vandg   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.25 mm
accept  2 1
expect  223395.249543407 111704.596633675

accept  2 -1
expect  223395.249543407 -111704.596633675
accept  -2 1
expect  -223395.249543407 111704.596633675
accept  -2 -1
expect  -223395.249543407 -111704.596633675

direction inverse
accept  200 100
expect  0.001790494 0.000895247
accept  200 -100
expect  0.001790494 -0.000895247
accept  -200 100
expect  -0.001790494 0.000895247
accept  -200 -100
expect  -0.001790494 -0.000895247

-------------------------------------------------------------------------------
operation +proj=vandg   +R=1
-------------------------------------------------------------------------------
direction inverse
accept  0 -1e100
expect  failure errno coord_transfm_outside_projection_domain


===============================================================================
# van der Grinten II
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=vandg2   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.5 mm
accept  2 1
expect  223395.247850437 111718.491037226
accept  2 -1
expect  223395.247850437 -111718.491037226
accept  -2 1
expect  -223395.247850437 111718.491037226
accept  -2 -1
expect  -223395.247850437 -111718.491037226


===============================================================================
# van der Grinten III
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=vandg3   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223395.249552831 111704.519904421
accept  2 -1
expect  223395.249552831 -111704.519904421
accept  -2 1
expect  -223395.249552831 111704.519904421
accept  -2 -1
expect  -223395.249552831 -111704.519904421


===============================================================================
# van der Grinten IV
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=vandg4   +R=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223374.577294355 111701.195484154
accept  2 -1
expect  223374.577294355 -111701.195484154
accept  -2 1
expect  -223374.577294355 111701.195484154
accept  -2 -1
expect  -223374.577294355 -111701.195484154


===============================================================================
# Vitkovsky I
# 	Conic, Sph
# 	lat_1= and lat_2=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=vitk1   +ellps=GRS80    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  222607.171211458 111404.251442435
accept  2 -1
expect  222776.716709598 -111234.665587445
accept  -2 1
expect  -222607.171211458 111404.251442435
accept  -2 -1
expect  -222776.716709598 -111234.665587445

direction inverse
accept  200 100
expect  0.001796204 0.000898315
accept  200 -100
expect  0.001796202 -0.000898316
accept  -200 100
expect  -0.001796204 0.000898315
accept  -200 -100
expect  -0.001796202 -0.000898316

-------------------------------------------------------------------------------
operation +proj=vitk1   +a=6400000    +lat_1=0.5 +lat_2=2
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223370.224840471 111786.123319644
accept  2 -1
expect  223540.351507255 -111615.956157675
accept  -2 1
expect  -223370.224840471 111786.123319644
accept  -2 -1
expect  -223540.351507255 -111615.956157675

direction inverse
accept  200 100
expect  0.001790068 0.000895246
accept  200 -100
expect  0.001790066 -0.000895247
accept  -200 100
expect  -0.001790068 0.000895246
accept  -200 -100
expect  -0.001790066 -0.000895247


===============================================================================
# Wagner I (Kavraisky VI)
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wag1   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  195986.781561158 127310.075060660
accept  2 -1
expect  195986.781561158 -127310.075060660
accept  -2 1
expect  -195986.781561158 127310.075060660
accept  -2 -1
expect  -195986.781561158 -127310.075060660

direction inverse
accept  200 100
expect  0.002040721 0.000785474
accept  200 -100
expect  0.002040721 -0.000785474
accept  -200 100
expect  -0.002040721 0.000785474
accept  -200 -100
expect  -0.002040721 -0.000785474


===============================================================================
# Wagner II
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wag2   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  206589.888099962 120778.040357547
accept  2 -1
expect  206589.888099962 -120778.040357547
accept  -2 1
expect  -206589.888099962 120778.040357547
accept  -2 -1
expect  -206589.888099962 -120778.040357547

direction inverse
accept  200 100
expect  0.001936024 0.000827958
accept  200 -100
expect  0.001936024 -0.000827958
accept  -200 100
expect  -0.001936024 0.000827958
accept  -200 -100
expect  -0.001936024 -0.000827958


===============================================================================
# Wagner III
# 	PCyl., Sph.
# 	lat_ts=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wag3   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223387.021718166 111701.072127637
accept  2 -1
expect  223387.021718166 -111701.072127637
accept  -2 1
expect  -223387.021718166 111701.072127637
accept  -2 -1
expect  -223387.021718166 -111701.072127637

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Wagner IV
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wag4   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  192801.218662384 129416.216394803
accept  2 -1
expect  192801.218662384 -129416.216394803
accept  -2 1
expect  -192801.218662384 129416.216394803
accept  -2 -1
expect  -192801.218662384 -129416.216394803

direction inverse
accept  200 100
expect  0.002074503 0.000772683
accept  200 -100
expect  0.002074503 -0.000772683
accept  -200 100
expect  -0.002074503 0.000772683
accept  -200 -100
expect  -0.002074503 -0.000772683


===============================================================================
# Wagner V
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wag5   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  203227.051925325 138651.631442713
accept  2 -1
expect  203227.051925325 -138651.631442713
accept  -2 1
expect  -203227.051925325 138651.631442713
accept  -2 -1
expect  -203227.051925325 -138651.631442713

direction inverse
accept  200 100
expect  0.001968072 0.000721216
accept  200 -100
expect  0.001968072 -0.000721216
accept  -200 100
expect  -0.001968072 0.000721216
accept  -200 -100
expect  -0.001968072 -0.000721216


===============================================================================
# Wagner VI
# 	PCyl, Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wag6   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  211652.562164410 105831.180787330
accept  2 -1
expect  211652.562164410 -105831.180787330
accept  -2 1
expect  -211652.562164410 105831.180787330
accept  -2 -1
expect  -211652.562164410 -105831.180787330

direction inverse
accept  200 100
expect  0.001889802 0.000944901
accept  200 -100
expect  0.001889802 -0.000944901
accept  -200 100
expect  -0.001889802 0.000944901
accept  -200 -100
expect  -0.001889802 -0.000944901


===============================================================================
# Wagner VII
# 	Misc Sph, no inv.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wag7   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  198601.876957312 125637.045714171
accept  2 -1
expect  198601.876957312 -125637.045714171
accept  -2 1
expect  -198601.876957312 125637.045714171
accept  -2 -1
expect  -198601.876957312 -125637.045714171


===============================================================================
# Werenskiold I
# 	PCyl., Sph.
===============================================================================

-------------------------------------------------------------------------------
operation +proj=weren   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223378.515757634 146214.093042288
accept  2 -1
expect  223378.515757634 -146214.093042288
accept  -2 1
expect  -223378.515757634 146214.093042288
accept  -2 -1
expect  -223378.515757634 -146214.093042288

direction inverse
accept  200 100
expect  0.001790493 0.000683918
accept  200 -100
expect  0.001790493 -0.000683918
accept  -200 100
expect  -0.001790493 0.000683918
accept  -200 -100
expect  -0.001790493 -0.000683918


===============================================================================
# Winkel I
# 	PCyl., Sph.
# 	lat_ts=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wink1   +a=6400000
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223385.131640953 111701.072127637
accept  2 -1
expect  223385.131640953 -111701.072127637
accept  -2 1
expect  -223385.131640953 111701.072127637
accept  -2 -1
expect  -223385.131640953 -111701.072127637

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Winkel II
# 	PCyl., Sph., no inv.
# 	lat_1=
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wink2   +a=6400000    +lat_1=0.5
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept  2 1
expect  223387.396433786 124752.032797445
roundtrip 1

accept  2 -1
expect  223387.396433786 -124752.032797445
roundtrip 1

accept  -2 1
expect  -223387.396433786 124752.032797445
roundtrip 1

accept  -2 -1
expect  -223387.396433786 -124752.032797445

accept  -179.999 89.999
expect  -10052657.852   10053040.641
roundtrip 1

accept  179.999 89.999
expect  10052657.852   10053040.641
roundtrip 1

accept  -179.999 -89.999
expect  -10052657.852   -10053040.641
roundtrip 1

accept  179.999 -89.999
expect  10052657.852   -10053040.641
roundtrip 1


===============================================================================
# Winkel Tripel
# 	Misc Sph
# 	lat_1
===============================================================================

-------------------------------------------------------------------------------
operation +proj=wintri   +a=6400000    +lat_1=0
-------------------------------------------------------------------------------
tolerance 0.1 mm
accept  2 1
expect  223390.801533485 111703.907505745
accept  2 -1
expect  223390.801533485 -111703.907505745
accept  -2 1
expect  -223390.801533485 111703.907505745
accept  -2 -1
expect  -223390.801533485 -111703.907505745

direction inverse
accept  200 100
expect  0.001790493 0.000895247
accept  200 -100
expect  0.001790493 -0.000895247
accept  -200 100
expect  -0.001790493 0.000895247
accept  -200 -100
expect  -0.001790493 -0.000895247


===============================================================================
# Colombia Urbian
# Test point from IOGP Publication 373-7-2 - Geomatics Guidance Note number 7, part 2 - March 2020
===============================================================================

-------------------------------------------------------------------------------
operation +proj=col_urban +lat_0=4.68048611111111 +lon_0=-74.1465916666667 +x_0=92334.879 +y_0=109320.965 +h_0=2550 +ellps=GRS80
-------------------------------------------------------------------------------
tolerance 1 mm
accept    -74.25    4.8
expect    80859.033 122543.174
roundtrip 1

===============================================================================
# Geocentric/topocentric conversion
===============================================================================

# Test parameter and point from IOGP Publication 373-7-2 - Geomatics Guidance Note number 7, part 2 - October 2020
operation +proj=topocentric +ellps=WGS84 +X_0=3652755.3058 +Y_0=319574.6799 +Z_0=5201547.3536
tolerance 1 mm
accept    3771793.968  140253.342         5124304.349
expect    -189013.869  -128642.040        -4220.171
roundtrip 1

===============================================================================
# Geographic/topocentric conversion
===============================================================================

# Test parameter and point from IOGP Publication 373-7-2 - Geomatics Guidance Note number 7, part 2 - October 2020
operation +proj=pipeline +step +proj=cart +ellps=WGS84 +step +proj=topocentric +ellps=WGS84 +lon_0=5 +lat_0=55 +h_0=200
tolerance 1 mm
accept    2.12955      53.80939444444444  73
expect    -189013.869  -128642.040        -4220.171
roundtrip 1

===============================================================================
# Error cases of topocentric
===============================================================================

# missing X_0,Y_0,Z_0 or lon_0,lat_0
operation +proj=topocentric +ellps=WGS84
expect failure errno invalid_op_missing_arg

# missing Z_0
operation +proj=topocentric +ellps=WGS84 +X_0=0 +Y_0=0
expect failure errno invalid_op_missing_arg

# missing lat_0
operation +proj=topocentric +ellps=WGS84 +lon_0=0
expect failure errno invalid_op_missing_arg

# X_0 and lon_0 are mutually exclusive
operation +proj=topocentric +ellps=WGS84 +X_0=0 +lon_0=0
expect failure errno invalid_op_mutually_exclusive_args

</gie-strict>
