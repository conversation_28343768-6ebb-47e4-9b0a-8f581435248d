===============================================================================

Various test material, mostly converted from selftest entries in PJ_xxx.c

Contrary to the material in builtins.gie, this material is handwritten and
intends to exercise corner cases.

===============================================================================


<gie-strict>

-------------------------------------------------------------------------------
# Two ob_tran tests from data/testvarious
-------------------------------------------------------------------------------
operation +proj=ob_tran  +o_proj=moll  +R=6378137.0  +o_lon_p=0  +o_lat_p=0  +lon_0=180
-------------------------------------------------------------------------------
tolerance 1 mm
direction inverse

accept    300000 400000
expect    -42.7562158333  85.5911341667

direction forward

accept    10 20
expect    -1384841.18787 7581707.88240
-------------------------------------------------------------------------------





-------------------------------------------------------------------------------
# Two tests from PJ_molodensky.c
-------------------------------------------------------------------------------
operation proj=molodensky a=6378160 rf=298.25 \
          da=-23  df=-8.120449e-8  dx=-134  dy=-48  dz=149 \
          abridged
-------------------------------------------------------------------------------
tolerance 2 m

accept    144.9667  -37.8      50     0
expect    144.968   -37.79848  46.378 0

roundtrip 100    1 m
-------------------------------------------------------------------------------
# Same thing once more, but this time unabridged
-------------------------------------------------------------------------------
operation proj=molodensky a=6378160 rf=298.25 \
          da=-23  df=-8.120449e-8  dx=-134  dy=-48  dz=149
-------------------------------------------------------------------------------
tolerance 2 m

accept    144.9667  -37.8      50     0
expect    144.968   -37.79848  46.378 0

roundtrip 100    1 m
-------------------------------------------------------------------------------
-------------------------------------------------------------------------------
# Molodensky with all 0 parameters
-------------------------------------------------------------------------------
operation proj=molodensky a=6378160 rf=298.25 \
          da=0  df=0 dx=0 dy=0 dz=0
-------------------------------------------------------------------------------
tolerance 1 mm

accept    144.9667  -37.8      50     0
expect    144.9667  -37.8      50     0

roundtrip 1
-------------------------------------------------------------------------------
-------------------------------------------------------------------------------
# Test error cases of molodensky
-------------------------------------------------------------------------------
# No arguments
operation  proj=molodensky a=6378160 rf=298.25
expect     failure   errno invalid_op_missing_arg

# Missing arguments
operation  proj=molodensky a=6378160 rf=298.25 dx=0
expect     failure   errno invalid_op_missing_arg


-------------------------------------------------------------------------------
# Tests for PJ_bertin1953.c
-------------------------------------------------------------------------------
operation proj=bertin1953 +R=1
-------------------------------------------------------------------------------
accept    0 0
expect    -0.260206554508 -0.685226058142

accept    16.5 42
expect    0.0 0.0

accept    -180 90
expect    0.0 0.813473286152

accept    0 90
expect    0.0 0.813473286152

accept    10 -35
expect    -0.138495501548 -1.221408328101

accept    -70 -35
expect    -1.504967424950 -0.522846035499

accept    80 7
expect    0.929377425352 -0.215443296201

accept    128 35
expect    0.920230566844   0.713170409026

accept    170 -41
expect    2.162845830414 -0.046534568425

-------------------------------------------------------------------------------





-------------------------------------------------------------------------------
# Some tests from PJ_pipeline.c
-------------------------------------------------------------------------------
# Forward-reverse geo->utm->geo (4D functions)
-------------------------------------------------------------------------------
operation proj=pipeline zone=32 step \
          proj=utm  ellps=GRS80 step \
          proj=utm  ellps=GRS80 inv
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept 12 55 0 0
expect 12 55 0 0

# Now the inverse direction (still same result: the pipeline is symmetrical)

direction inverse
expect 12 55 0 0
-------------------------------------------------------------------------------
# And now the back-to-back situation utm->geo->utm (4D functions)
-------------------------------------------------------------------------------
operation proj=pipeline zone=32 ellps=GRS80 step \
          proj=utm inv                      step \
          proj=utm
-------------------------------------------------------------------------------
accept 691875.63214  6098907.82501  0  0
expect 691875.63214  6098907.82501  0  0
direction inverse
expect 691875.63214  6098907.82501  0  0
-------------------------------------------------------------------------------
# Forward-reverse geo->utm->geo (3D functions)
-------------------------------------------------------------------------------
operation proj=pipeline zone=32 step \
          proj=utm  ellps=GRS80 step \
          proj=utm  ellps=GRS80 inv
-------------------------------------------------------------------------------
tolerance 0.1 mm

accept 12 55 0
expect 12 55 0

# Now the inverse direction (still same result: the pipeline is symmetrical)

direction inverse
expect 12 55 0
-------------------------------------------------------------------------------
# And now the back-to-back situation utm->geo->utm (3D functions)
-------------------------------------------------------------------------------
operation proj=pipeline zone=32 ellps=GRS80 step \
          proj=utm inv                      step \
          proj=utm
-------------------------------------------------------------------------------
accept 691875.63214  6098907.82501  0
expect 691875.63214  6098907.82501  0
direction inverse
expect 691875.63214  6098907.82501  0
-------------------------------------------------------------------------------
# Test a corner case: A rather pointless one-step pipeline geo->utm
-------------------------------------------------------------------------------
operation proj=pipeline step proj=utm zone=32 ellps=GRS80
-------------------------------------------------------------------------------
accept    12 55 0 0
expect    691875.63214  6098907.82501  0  0
direction inverse
accept    691875.63214  6098907.82501  0  0
expect    12 55 0 0
-------------------------------------------------------------------------------
# Finally test a pipeline with more than one init step
-------------------------------------------------------------------------------
use_proj4_init_rules true
operation proj=pipeline \
          step init=epsg:25832 inv \
          step init=epsg:25833 \
          step init=epsg:25833 inv \
          step init=epsg:25832
-------------------------------------------------------------------------------
accept    691875.63214  6098907.82501  0  0
expect    691875.63214  6098907.82501  0  0
direction inverse
accept    12 55 0 0
expect    12 55 0 0
-------------------------------------------------------------------------------
# Test a few inversion scenarios (urm5 has no inverse operation)
-------------------------------------------------------------------------------
operation   proj=pipeline       step \
            proj=urm5 n=0.5 inv
expect      failure pjd_err_malformed_pipeline

operation   proj=pipeline   inv step \
            proj=urm5 n=0.5
expect      failure pjd_err_malformed_pipeline

operation   proj=pipeline   inv step \
            proj=urm5 n=0.5 ellps=WGS84 inv
accept      12 56
expect      1215663.2814182492      5452209.5424045017

operation   proj=pipeline step \
            proj=urm5 ellps=WGS84 n=0.5
accept      12 56
expect      1215663.2814182492      5452209.5424045017
-------------------------------------------------------------------------------
# Test various failing scenarios.
-------------------------------------------------------------------------------
operation   proj=pipeline   step \
            proj=pipeline   step \
            proj=merc
expect      failure pjd_err_malformed_pipeline

operation   step proj=pipeline step proj=merc
expect      failure pjd_err_malformed_pipeline

operation   proj=pipeline
expect      failure pjd_err_malformed_pipeline


-------------------------------------------------------------------------------
# Some tests from PJ_vgridshift.c
-------------------------------------------------------------------------------
operation  proj=vgridshift  grids=egm96_15.gtx  ellps=GRS80
-------------------------------------------------------------------------------
tolerance 1 cm
accept    12.5 55.5   0                  0
expect    12.5 55.5 -36.394090697  0

accept    -180.1 0 0
expect    -180.1 0 -20.835222268

accept    179.9 0 0
expect    179.9 0 -20.835222268

accept    180 0 0
expect    180 0 -20.756538510

accept    540 0 0
expect    540 0 -20.756538510

accept    -180 0 0
expect    -180 0 -20.756538510

accept    -540 0 0
expect    -540 0 -20.756538510

roundtrip 100 1 nm
-------------------------------------------------------------------------------
# Fail on purpose: +grids parameter is mandatory
operation proj=vgridshift
expect    failure errno invalid_op_missing_arg

# Fail on purpose: open non-existing grid
operation proj=vgridshift grids=nonexistinggrid.gtx
expect    failure errno invalid_op_file_not_found_or_invalid
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation  proj=vgridshift  grids=egm96_15.gtx  ellps=GRS80     multiplier=0.1
tolerance 15 cm
accept    12.5 55.5   0                  0
expect    12.5 55.5  3.6021305084228516  0

-------------------------------------------------------------------------------
# Some tests from PJ_hgridshift.c
-------------------------------------------------------------------------------
operation  proj=hgridshift +grids=ntf_r93.gsb  ellps=GRS80
-------------------------------------------------------------------------------
tolerance 1 mm
accept    2.250704350387       46.500051597273
expect    2.25                 46.5
direction inverse
accept    2.25                 46.5
expect    2.250704350387       46.500051597273
-------------------------------------------------------------------------------



-------------------------------------------------------------------------------
# Fail on purpose: open non-existing grid:
operation proj=hgridshift grids=@nonexistinggrid.gsb,anothernonexistinggrid.gsb
expect    failure errno invalid_op_file_not_found_or_invalid

# Fail on purpose: +grids parameter is mandatory:
operation proj=hgridshift
expect    failure errno invalid_op_missing_arg
-------------------------------------------------------------------------------



-------------------------------------------------------------------------------
# Tests for LCC 2SP Michigan (from PJ_lcc.c)
-------------------------------------------------------------------------------
# This test is taken from EPSG guidance note 7-2 (version 54, August 2018,
# page 25)
-------------------------------------------------------------------------------
operation +proj=lcc +ellps=clrk66 +lat_1=44d11'N +lat_2=45d42'N +x_0=609601.2192 +lon_0=84d20'W +lat_0=43d19'N +k_0=1.0000382 +units=us-ft
-------------------------------------------------------------------------------
tolerance 5 mm
accept 83d10'W 43d45'N
expect 2308335.75 160210.48

direction inverse
accept 2308335.75 160210.48
expect 83d10'W 43d45'N
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# A number of tests from PJ_helmert.c
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# This example is from Lotti Jivall: "Simplified transformations from
# ITRF2008/IGS08 to ETRS89 for maritime applications"
-------------------------------------------------------------------------------
operation  proj=helmert convention=coordinate_frame \
           x=0.67678    y=0.65495   z=-0.52827 \
           rx=-0.022742 ry=0.012667 rz=0.022704  s=-0.01070
-------------------------------------------------------------------------------
tolerance 1 um
accept     3565285.00000000  855949.00000000  5201383.00000000
expect     3565285.41342351  855948.67986759  5201382.72939791
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# This example is a random point, transformed from ED50 to ETRS89 using KMStrans2
-------------------------------------------------------------------------------
operation   proj=helmert   exact    convention=coordinate_frame \
            x=-081.0703    rx=-0.48488 \
            y=-089.3603    ry=-0.02436 \
            z=-115.7526    rz=-0.41321   s=-0.540645
-------------------------------------------------------------------------------
tolerance  1 um
accept     3494994.30120000 1056601.97250000 5212382.16660000
expect     3494909.84026368 1056506.78938633 5212265.66699761
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# This example is a coordinate from the geodetic observatory in Onsala,
# Sweden transformed from ITRF2000 @ 2017.0 to ITRF93 @ 2017.0.

# The test coordinate was transformed using GNSStrans, using transformation
# parameters published by ITRF: ftp://itrf.ensg.ign.fr/pub/itrf/ITRF.TP
-------------------------------------------------------------------------------
operation  proj=helmert convention=position_vector \
           x =  0.0127   dx = -0.0029   rx = -0.00039  drx = -0.00011 \
           y =  0.0065   dy = -0.0002   ry =  0.00080  dry = -0.00019 \
           z = -0.0209   dz = -0.0006   rz = -0.00114  drz =  0.00007 \
           s =  0.00195  ds =  0.00001  t_epoch = 1988.0
-------------------------------------------------------------------------------
tolerance  0.03 mm
accept     3370658.37800 711877.31400 5349787.08600  2017.0  # ITRF2000@2017.0
expect     3370658.18890 711877.42370 5349787.12430  2017.0  # ITRF93@2017.0
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# This example is from "A mathematical relationship between NAD27 and NAD83 (91)
# State Plane coordinates in Southeastern Wisconsin":
# http://www.sewrpc.org/SEWRPCFiles/Publications/TechRep/tr-034-Mathematical-Relationship-Between-NAD27-and-NAD83-91-State-Plane-Coordinates-Southeastern-Wisconsin.pdf

# The test data is taken from p. 29. Here we are using point 203 and converting it
# from NAD27 (ft) -> NAD83 (m). The paper reports a difference of 0.0014 m from
# measured to computed coordinates, hence the test tolerance is set accordingly.
-------------------------------------------------------------------------------
operation  proj=helmert \
           x=-9597.3572 y=.6112 \
           s=0.304794780637 theta=-1.244048
-------------------------------------------------------------------------------
tolerance  1 mm
accept     2546506.957 542256.609  0
expect     766563.675  165282.277  0
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# Finally test the 4D-capabilities of the proj.h API, especially that the
# rotation matrix is updated when necessary.

# Test coordinates from GNSStrans.
-------------------------------------------------------------------------------
operation  proj=helmert convention=position_vector \
           x = 0.01270  dx =-0.0029  rx =-0.00039  drx =-0.00011 \
           y = 0.00650  dy =-0.0002  ry = 0.00080  dry =-0.00019 \
           z =-0.0209   dz =-0.0006  rz =-0.00114  drz = 0.00007 \
           s = 0.00195  ds = 0.00001 \
           t_epoch=1988.0
-------------------------------------------------------------------------------
tolerance  0.1 mm
accept     3370658.378    711877.314    5349787.086    2017.0
expect     3370658.18890  711877.42370  5349787.12430  2017.0
accept     3370658.378    711877.314    5349787.086    2018.0
expect     3370658.18087  711877.42750  5349787.12648  2018.0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
# Test case of https://github.com/OSGeo/PROJ/issues/2333
-------------------------------------------------------------------------------
operation  +proj=helmert +x=-0.0019 +y=-0.0017 +z=-0.0105 +s=0.00134 \
           +dx=0.0001 +dy=0.0001 +dz=-0.0018 +ds=0.00008 +t_epoch=2000.0 \
           +convention=position_vector
-------------------------------------------------------------------------------
tolerance  0.1 mm
accept     3513638.1938  778956.4525  5248216.4690  2008.75
expect     3513638.1999  778956.4533  5248216.4535  2008.75
-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# Test error cases of helmert
-------------------------------------------------------------------------------
# A rotational term implies an explicit convention to be specified
operation  proj=helmert rx=1
expect     failure   errno invalid_op_missing_arg

operation  proj=helmert rx=1 convention=foo
expect     failure   errno invalid_op_illegal_arg_value

operation  proj=helmert rx=1 convention=1
expect     failure   errno invalid_op_illegal_arg_value

# towgs84 in helmert context should always be position_vector
operation  proj=helmert towgs84=1,2,3,4,5,6,7 convention=coordinate_frame
expect     failure   errno invalid_op_illegal_arg_value

# Transpose no longer accepted
operation  proj=helmert transpose
expect     failure   errno invalid_op_illegal_arg_value

# Use of 2D Helmert interface with 3D Helmert setup
operation  +proj=ob_tran +o_proj=helmert +o_lat_p=0
direction  inverse
accept     0 0
expect     failure   errno no_inverse_op

-------------------------------------------------------------------------------
# Molodensky-Badekas from IOGP Guidance 7.2, Transformation from La Canoa to REGVEN
# between geographic 2D coordinate reference systems (EPSG Dataset transformation code 1771).
# Here just taking the Cartesian step of the transformation.
-------------------------------------------------------------------------------
operation  proj=molobadekas convention=coordinate_frame \
           x=-270.933 y=115.599 z=-360.226 rx=-5.266 ry=-1.238 rz=2.381 \
           s=-5.109 px=2464351.59 py=-5783466.61 pz=974809.81
-------------------------------------------------------------------------------
tolerance  1 cm
roundtrip  1
accept     2550408.96 -5749912.26 1054891.11
expect     2550138.45 -5749799.87 1054530.82
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
# Test error cases of molobadekas
-------------------------------------------------------------------------------

# Missing convention
operation  proj=molobadekas
expect     failure   errno invalid_op_missing_arg


-------------------------------------------------------------------------------
# geocentric latitude
-------------------------------------------------------------------------------
operation  proj=geoc ellps=GRS80
accept     12   55                  0 0
expect     12   54.818973308324573  0 0
roundtrip  1000

accept     12   90                  0 0
expect     12   90                  0 0

accept     12  -90                  0 0
expect     12  -90                  0 0

accept     12   89.99999999999      0 0
expect     12   89.999999999989996  0 0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
# geocentric latitude using old +geoc flag
-------------------------------------------------------------------------------
operation  proj=pipeline step proj=longlat ellps=GRS80 geoc inv
accept     12   55                  0 0
expect     12   54.818973308324573  0 0
roundtrip  1
-------------------------------------------------------------------------------



-------------------------------------------------------------------------------
# some less used options
-------------------------------------------------------------------------------
operation  proj=utm  ellps=GRS80  zone=32  to_meter=0
expect     failure   errno invalid_op_illegal_arg_value

operation  proj=utm  ellps=GRS80  zone=32  to_meter=10
accept     12   55
expect        69187.5632    609890.7825

operation  proj=utm  ellps=GRS80  zone=32  to_meter=1/0
expect     failure   errno invalid_op_illegal_arg_value

operation  proj=utm  ellps=GRS80  zone=32  to_meter=2.0/0.2
accept     12   55
expect        69187.5632    609890.7825

-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
# Test that gie can read DMS style coordinates as well as coordinates where _
# is used as a thousands separator.
-------------------------------------------------------------------------------
operation      +step +proj=latlong +ellps=WGS84 
-------------------------------------------------------------------------------
tolerance   1 m

accept      -64d43'75.34    17d32'45.6
expect      -64.737589      17.546000

accept      164d43'75.34    17d32'45.6
expect      164.737589      17.546000

accept      164d43'75.34    17d32'45.6
expect      164d43'75.34    17d32'45.6

accept      164d43'75.34W   17d32'45.6S
expect      -164.737589     -17.546000

accept      90d00'00.00     0d00'00.00
expect      90.0             0.0

accept      0d00'00.00     0d00'00.00
expect      0.0             0.0



operation      +proj=pipeline \
               +step +proj=latlong +datum=NAD27 +inv \
               +step +units=us-ft +init=nad27:3901
tolerance      1 mm

accept       -80d32'30.000 34d32'30.000 0.0
expect       2_138_028.224 561_330.721 0.0

accept       -81d00'00.000 34d32'30.000 0.0
expect       2_000_000.000 561_019.077 0.0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
# Some tests from PJ_eqearth.c
-------------------------------------------------------------------------------
operation +proj=eqearth +ellps=WGS84
-------------------------------------------------------------------------------
direction forward
tolerance 1cm

accept    0 0
expect    0 0

accept    -180 90
expect    -10216474.79 8392927.6

accept    0 90
expect    0 8392927.6

accept    180 90
expect    10216474.79 8392927.6

accept    180 45
expect    14792474.75 5466867.76

accept    180 0
expect    17243959.06 0

accept    -70 -31.2
expect    -6241081.64 -3907019.16


direction inverse

accept    -6241081.64 -3907019.16
expect    -70 -31.2

accept    17243959.06 0
expect    180 0

accept    14792474.75 5466867.76
expect    180 45

accept    0 0
expect    0 0

accept    -10216474.79 8392927.6
expect    -180 90

accept    0 8392927.6
expect    0 90

accept    10216474.79 8392927.6
expect    180 90


operation +proj=eqearth +R=6378137
direction forward
tolerance 1cm

accept    0 0
expect    0 0

accept    -180 90
expect    -10227908.09 8402320.16

accept    0 90
expect    0.00 8402320.16

accept    180 90
expect    10227908.09 8402320.16

accept    180 45
expect    14795421.79 5486671.72

accept    180 0
expect    17263256.84 0.00

accept    -70 -31.2
expect    -6244707.88 -3924893.29

direction inverse

accept    -6244707.88 -3924893.29
expect    -70 -31.2

accept    17263256.84 0.00
expect    180 0

accept    14795421.79 5486671.72
expect    180 45

accept    0 0
expect    0 0

accept    -10227908.09 8402320.16
expect    -180 90

accept    0.00 8402320.16
expect    0 90

accept    10227908.09 8402320.16
expect    180 90

operation +proj=eqearth +R=1
direction inverse

# coordinate in valid region
accept    0 -1.3
expect    0 -82.318

# coordinate on edge
accept    0 -1.3173627591574
expect    0 -90

# coordinate outside valid region, should be clamped
accept    0 -1.4
expect    0 -90

-------------------------------------------------------------------------------


-------------------------------------------------------------------------------
# Test for PJ_affine
-------------------------------------------------------------------------------
-------------------------------------------------------------------------------
operation +proj=geogoffset
-------------------------------------------------------------------------------
direction forward
tolerance 1mm

accept    10 20
expect    10 20
roundtrip  1

-------------------------------------------------------------------------------
operation +proj=geogoffset +dlon=3600 +dlat=-3600 +dh=3
-------------------------------------------------------------------------------
direction forward
tolerance 1mm

accept    10 20
expect    11 19
roundtrip  1

accept    10 20 30
expect    11 19 33
roundtrip  1

accept    10 20 30 40
expect    11 19 33 40
roundtrip  1

-------------------------------------------------------------------------------
operation +proj=affine
-------------------------------------------------------------------------------
direction forward
tolerance 1mm

accept    10 20 30 40
expect    10 20 30 40
roundtrip  1

-------------------------------------------------------------------------------
operation +proj=affine +xoff=1 +yoff=2 +zoff=3 +toff=4 +s11=11 +s12=12 +s13=13 +s21=21 +s22=22 +s23=23 +s31=-31 +s32=32 +s33=33 +tscale=34
-------------------------------------------------------------------------------
direction forward
tolerance 1mm

accept    2 49 10 100
expect    741.0000      1352.0000     1839.0000     3404.0000
roundtrip  1

accept    2 49 10
expect    741.0000      1352.0000     1839.0000
roundtrip  1

accept    2 49
expect    611.0000      1122.0000
roundtrip  1

-------------------------------------------------------------------------------
# Non invertible
operation +proj=affine +s11=0 +s22=0 +s23=0
-------------------------------------------------------------------------------
direction reverse
accept    0 0 0 0
expect    failure

-------------------------------------------------------------------------------
# Non invertible
operation +proj=affine +tscale=0
-------------------------------------------------------------------------------
direction reverse
accept    0 0 0 0
expect    failure

-------------------------------------------------------------------------------
# Test lon_wrap
operation +proj=longlat +ellps=WGS84 +lon_wrap=180
-------------------------------------------------------------------------------
direction forward
accept    -1 10 0
expect    359 10 0

-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
# No-op
-------------------------------------------------------------------------------
operation +proj=noop
direction forward
accept  25  25
expect  25  25

accept  25  25  25
expect  25  25  25

accept  25  25  25  25
expect  25  25  25  25
-------------------------------------------------------------------------------


# Test invalid lat_0
operation +proj=aeqd +R=1 +lat_0=91
expect  failure errno invalid_op_illegal_arg_value

-------------------------------------------------------------------------------
# cart
-------------------------------------------------------------------------------

operation   +proj=cart +ellps=GRS80
tolerance 0.001mm

accept      0 0 0
expect      6378137 0 0

accept      0 90 0
expect      0 0 6356752.314140347

accept      0 -90 0
expect      0 0 -6356752.314140347

accept      90 0 0
expect      0 6378137 0

accept      -90 0 0
expect      0 -6378137 0

accept      180 0 0
expect      -6378137 0 0

accept      -180 0 0
expect      -6378137 0 0

# Center of Earth !
accept      0 0 -6378137
expect      0 0 0

accept      0 90 -6356752.314140347
expect      0 0 0

direction inverse

accept      6378137 0 0
expect      0 0 0

accept      0 0 6356752.314140347
expect      0 90 0

accept      0 0 -6356752.314140347
expect      0 -90 0

accept      0 6378137 0
expect      90 0 0

accept      0 -6378137 0
expect      -90 0 0

accept      -6378137 0 0
expect      180 0 0

# Center of Earth !
accept      0 0 0
expect      0 90 -6356752.314140356

accept      0 0 1e-6
expect      0 90 -6356752.314139356

accept      0 0 -1e-6
expect      0 -90 -6356752.314139356


-------------------------------------------------------------------------------
# Test handling of endianness of NTv2 grids
-------------------------------------------------------------------------------
-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_little_endian.gsb
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------

-------------------------------------------------------------------------------
operation   +proj=hgridshift +grids=tests/test_hgrid_big_endian.gsb
-------------------------------------------------------------------------------
tolerance   2 mm
accept      4.5        52.5        0
expect      5.875      55.375      0
-------------------------------------------------------------------------------


</gie-strict>
