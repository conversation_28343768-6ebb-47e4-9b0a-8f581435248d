8.2.1 Release Notes
-------------------

 Updates
 -------

 o Database updated with EPSG v. 10.041 (#2974)

 Bug fixes
 ---------

 o BoundCRS WKT import: fix setting of name (#2917)

 o PROJStringFormatter::toString(): avoid invalid iterator increment (#2932)

 o Ensure CApi test are cross-platform (#2934)

 o createOperations(): do not stop at the first operation in the PROJ namespace
   for vertical transformations (#2937)

 o createOperationsCompoundToCompound(): fix null pointer dereference when
   connection to proj.db doesn't exist. (#2938)

 o Fix windows.h conflict with Criterion::STRICT (#2950)

 o Cache result of proj_get_type() to help for performance of
   proj_factors() (#2967)

 o createOperations(): improvement for "NAD83(CSRS) + CGVD28 height" to
   "NAD83(CSRS) + CGVD2013(CGG2013) height" (#2977)

 o WKT1 import: correctly deal with missing rectified_grid_angle
   parameter (#2986)

 o Fix and additional options for Peirce Quincuncial projections (#2978)

 o Fix build with Intel C++ compiler (#2995)


8.2.0 Release Notes
-------------------

 Announcements
 -------------

 From PROJ 9.0.0 and onwards CMake will be the only build system bundled
 with the PROJ package. As a consequence support for Autotools builds will
 stop when the 8.2 branch of PROJ reaches end of life. We encourage
 everyone to adjust their build workflows as soon as possible and report
 any discrepancies discovered between Autotools and CMake builds.

 Details about the build system unification can be found in PROJ RFC 7.


 Updates
 -------

 o Added the S2 projection (#2749)

 o Added support for Degree Sign on input (#2791)

 o ESRI WKT: add support for import/export of (non interrupted)
   Goode Homolosine (#2827)

 o Make filemanager aware of UWP Win32 API (#2831)

 o Add proj_create_conversion_pole_rotation_netcdf_cf_convention() to
   address netCDF datasets using a pole rotation method (#2835)

 o Emit better debug message when a grid isn't found (#2838)

 o Add support for GeodeticCRS using a Spherical planetocentric
   coordinate system (#2847)

 o PROJJSON: support additional properties allowed in id object (version,
   authority_citation, uri) for parity with WKT2:2019 (#2850)

 o Database layout modified to include "anchor" field to geodetic_datum and
   vertical_datum tables, consequently database layout version is increased
   to 1.2 (#2859)

 o proj_factors(): accept P to be a projected CRS (#2868)

 o Add IAU_2015 CRS definitions (#2876)

 o CRS::extractGeodeticCRS(): implement for DerivedProjectedCRS (#2877)

 o Added proj_trans_bounds() (#2882)

 o CMake: add a BUILD_APPS to be able to disable build of all applications (#2895)

 o CMake: generate invproj/invgeod binaries (symlinks on Unix, copy otherwise)
   (#2897)

 o CMake build: add generate_wkt1_parser and generate_wkt2_parser manual
   target, and logic to detect when they must be run (#2900)

 o Add fallback strategy for tinshift transform to use closest triangle for
   points not in any (#2907)

 o Database: update to EPSG v10.038 (#2910)

 o CMake: revise handling of symbol export and static builds (#2912)

 Bug fixes
 ---------

 o Fix O(n^2) performance patterns where n is the number of steps of
   a pipeline (#2820)

 o Detect ESRI WKT better in certain circumstances (#2823)

 o Fix performance issue on pipeline instanciation of huge (broken)
   pipelines (#2824)

 o Make sure to re-order projection parameters according to their canonical
   order if needed (#2842)

 o Fix database access across fork() when SQLite3 doesn't use pread[64]() (#2845)

 o Fix error in implementation of Inverse ellipsoidal orthographic projection
   that cause convergence to sometimes fail (#2853)

 o Fix handling of edge-case coordinates in invers ortho ellipsoidal
   oblique (#2855)

 o proj_normalize_for_visualization(): set input and output units when there
   are several alternative transformations (#2867)

 o CRS::identify(): fix ignoring CS order when identifying a geodetic CRS
   by a PROJ string with just the ellipsoid (#2881)

 o Fix CRS Equality with PROJ parameter order (#2887)

 o WKT concatenated operation parsing: fix when a axis order reversal conversion
   is the first or last operation (#2891)

 o WKT1 parser: recognize Lambert_Conformal_Conic as projection name for
   LCC 1SP or 2SP (#2893)

 o CMake: Always build gie if testing is requested (#2899)

 o Geographic 3D CRS: allow to export to WKT1:ESRI if only the GEOGCS is known
   (and thus extrapolating a VERTCS) (#2902)

 o lib_proj.cmake: add a PROJ::proj alias and add BUILD_INTERFACE include
   directories, so that proj can be used as a subdirectory of a larger
   project (#2913)


 THANKS TO
 ---------

 Thomas Knudsen
 Alan D. Snow
 Johannes Schauer Marin Rodrigues
 Howard Butler
 Geoff Evans
 Joris Van den Bossche
 marcus-elia
 Waheed Barghouthi
 snowman2
 Ben Boeckel
 Mike Taves
 Javier Jimenez Shaw
 Brendan Jurd
 Kristian Evers
 Even Rouault


8.1.1 Release Notes
-------------------

 Updates
 -------

 o EPSG Database updated to version 10.028 (#2773)

 Bug Fixes
 ---------

 o Include algorithm header file to avoid build errors on Alpine Linux (#2769)

 o CMake: fix installation of executables on iOS (#2766)

 o Associate extents to transformations of CRS's that include GEOIDMODEL (#2769)

 o Logging: avoid some overhead when logging is not enabled (#2775)

 o ortho: remove useless and invalid log trace (#2777)

 o CMake: remove external nlohmann_json from INTERFACE_LINK_LIBRARIES target (#2781)
 o reateOperations(): fix SourceTargetCRSExtentUse::NONE mode (#2783)

 o GeoTIFF grid reading: perf improvements (#2788)

 o Conversion::createUTM(): avoid integer overflow (#2796)

 o Inverse laea ellipsoidal: return PROJ_ERR_COORD_TRANSFM_OUTSIDE_PROJECTION_DOMAIN
   when appropriates (#2801)

 o Make sure that proj_crs_promote_to_3D returns a derived CRS (#2806)

 o createOperations(): fix missing deg<-->rad conversion when transforming with a
   CRS that has a fallback-to-PROJ4-string behaviour and is a BoundCRS of a
   GeographicCRS (#2808)

 o WKT2 import/export: preserve PROJ.4 CRS extension string in REMARKS[] (#2812)

 o BoundCRS: accept importing/exporting in WKT2 and PROJJSON the
   scope/area/extent/id attributes (#2815)

 o ConcatenatedOperation::fixStepsDirection(): fix bad chaining of steps when
   inverse map projection is involved in non-final step (#2819)


 THANKS TO
 ---------

 Brendan Jurd
 Kristian Evers
 Even Rouault

8.1.0 Release Notes
-------------------

 Updates
 -------

 + Database

   o Update to EPSG v10.027 (#2751)

   o Decrease DB size by using WITHOUT ROWID tables (#2730) (#2647)

   o Add a ANALYZE step during proj.db creation allowing for
     faster lookups (#2729)

   o Added a PROJ.VERSION metadata entry (#2646)

   o Added NGO48 (EPSG:4273) to ETRS89 (EPSG:4258) triangulation-based
     transformation (#2554)

   o Additions to the norwegian NKG2020 transformation (#2548)

   o ESRI projection database updated to version 12.8 (#2717)

 + API additions

   o Added proj_get_geoid_models_from_database() function that returns a list of
     geoid models available for a given CRS (#2681)

   o Added proj_get_celestial_body_list_from_database that returns a list
     of celestial bodies in the PROJ database (#2667)

   o Added proj_get_celestial_body_name() (#2662)

 + Various improvements

   o proj_trans/cs2cs: If two operations have the same accuracy,
     use the one that is contained within a larger one (#2750)

   o Share SQLite database handle among all contexts (#2738)

   o Add proj/internal/mutex.hpp as compat layer for mingw32 for std::mutex (#2736)

   o projsync: make it filter out files not intended for the current version (#2725)

   o Improvements related to DerivedVerticalCRS using Change Unit and
     Height/Depth reversal methods (#2696)

   o Update internal nlohmann/json to 3.9.1, and add a CMake option to
     be able to use external nlohmann/json (#2686)

   o createFromUserInput(): change name of CRS built from URN combined references to match the convention of EPSG projected CRS (#2677)

   o Parse compound id with two authorities, like ESRI:103668+EPSG:5703 (#2669)

   o Added projinfo option --list-crs (supports --area) (#2663)

   o Added support for hyperbolic Cassini-Soldner (#2637)

   o Added capability to get SQL statements to add custom CRS in the database (#2577)

 Bug fixes
 ---------

 o Fix 'Please include winsock2.h before windows.h' warning with msys (#2692)

 o Minor changes to address lint in geodesic.c (#2752)

 o BoundCRS::identify(): avoid incompatible transformation for
   WKT1 / TOWGS84 export (#2747)

 o proj_create(): do not open proj.db if string is a PROJ string,
   even if proj_context_set_autoclose_database() has been set (#2735)

 o Fix export of transformation to PROJ string in a particular situation
   where CompoundCRS are involved (#2721)

 Thanks to
 ---------

 Howard Butler
 Alan D. Snow
 Roel van den Berg
 Heidi Vanparys
 Sveinung Himle
 積丹尼 Dan Jacobson
 Nyall Dawson
 Javier Jimenez Shaw
 Charles Karney
 Mike Taves
 Kristian Evers
 Even Rouault


8.0.1 Release Notes
-------------------

 Updates
 -------

 o Database: update to EPSG v10.018 (#2636)

 o Add transformations for CHGeo2004, Swiss geoid model (#2604)

 o Additions to the norwegian NKG2020 transformation (#2600)

 Bug fixes
 ---------

 o pj_vlog(): fix buffer overflow in case of super lengthy error message (#2693)

 o Revert "proj_create_crs_to_crs_from_pj(): do not use PROJ_SPATIAL_CRITERION_PARTIAL_INTERSECTION if area is specified" (#2679)

 o UTM: error out when value of +zone= is not an integer (#2672)

 o getCRSInfoList(): make result order deterministic (by increasing auth_name,
   code) (#2661)

 o createOperation(): make sure no to discard deprecated operations if the
   replacement uses an unknow grid (#2623)

 o Fix build on Solaris 11.4 (#2621)

 o Add mapping of ESRI Equal_Area projection method to EPSG (#2612)

 o Fix incorrect EPGS extent code for EPSG:7789>EPSG:4976 NKG transformation (#2599)

 o fix wrong capitalization of CHENyx06_ETRS.gsb (#2597)

 o createOperations(): improve handling of vertical transforms when
   when compound CRSs are used (#2592)

 o CRS::promoteTo3D(): propagate the extent from the 2D CRS (#2589)

 o createFromCRSCodesWithIntermediates(): improve performance when there is
   no match (#2583)

 o Fix proj_clone() to work on 'meta' coordinate operation PJ* objects that
   can be returned by proj_create_crs_to_crs() (#2582)

 o add PROJ_COMPUTE_VERSION, PROJ_VERSION_NUMBER,
   PROJ_AT_LEAST_VERSION macros (#2581)

 o Make proj_lp_dist() and proj_geod() work on a PJ* CRS object (#2570)

 o Fix gcc 11 -Wnonnull compilation warnings (#2559)

 o Fix use of uninitialized memory in gie tests (#2558)

 o createOperations(): fix incorrect height transformation between 3D promoted RGF93 and CH1903+ (#2555)


 THANKS TO
 ---------

 Dan Jacobson
 Sveinung Himle
 Mike Taves
 Javier Jimenez Shaw
 Kristian Evers
 Even Rouault


8.0.0 Release Notes
-------------------

With the release of PROJ 8 the proj_api.h API is finally removed. See
https://proj.org/development/migration.html for more info on how to migrate
from the old to the proj.h API.

With the removal of proj_api.h it has been possible to simplify error codes
and messages given by the software. The error codes are exposed in the API.

Several improvements has been made to the command line utilities as well as
tweaks in the underlying API.

 Updates
 -------

 o Public header file proj_api.h removed (#837)

 o Improved accuracy of the Mercator projection (#2397)

 o Copyright statement wording updated (#2417)

 o Allow cct to instantiate operations via object codes or names (#2419)

 o Allow @filename syntax in cct (#2420)

 o Added geocentric->topocentric conversion (+proj=topocentric) (#2444)

 o Update GeographicLib to version 1.51 (#2445)

 o Added option to allow export of Geographic/Projected 3D CRS
   in WKT1_GDAL (#2450)

 o Added --area and --bbox options in cs2cs to restrict candidate
   coordinate operations (#2466)

 o Added build time option to make PROJ_LIB env var tested last (#2476)

 o Added --authority switch in cs2cs to control where coordinate operations
   are looked for. C API function proj_create_crs_to_crs_from_pj() updated
   accordingly (#2477)

 o Error codes revised and exposed in the public API (#2487)

 o Added --accuracy options to projinfo. C API function
   proj_create_crs_to_crs_from_pj() updated accordingly (#2488)

 o Added proj_crs_is_derived() function to C API (#2496)

 o Enabled linking against static cURL on Windows (#2514)

 o Updated ESRI CRS database to 12.7 (10.8.1/2.6) (#2519)

 o Allow a WKT BoundCRS to use a PROJ string transformation (#2521)

 o Update to EPSG v10.015 (#2539)

 o Default log level set to PJ_LOG_ERROR (#2542)

 o CMake installs a pkg-config file proj.pc, where supported (#2547)

 Bug fixes
 ---------

 o Do not restrict longitude to [-90;90] range in spherical transverse Mercator
   forward projection (#2471)

 o createOperations(): fix Compound to Geog3D/Projected3D CRS with non-metre ellipsoidal height (#2500)

 o Avoid error messages to be emitted log level is set to PJ_LOG_NONE (#2527)

 o Close database connection when autoclose set to True (#2532)

 THANKS TO
 ---------


 Zac Miller
 Juan Hernando
 Thomas Knudsen
 Sveinung Himle
 Olli Raisa
 Nomit Rawat
 Modern Slave
 J.H. van de Water
 Guillaume Lostis
 Martin Steinisch
 Javier Jimenez Shaw
 Mateusz Łoskot
 Martijn Visser
 Alan D. Snow
 Mike Taves
 Nyall Dawson
 Charles Karney
 Kristian Evers
 Even Rouault


7.2.1 Release Notes
-------------------

 Updates
 -------

 o Add metadata with the version number of the database layout (#2474)

 o Split coordinateoperation.cpp and test_operation.cpp in several parts (#2484)

 o Update to EPSG v10.008 (#2490)

 o Added the NKG 2008 and 2020 transformations in proj.db (#2495)

 Bug fixes
 ---------

 o Set CURL_ENABLED definition on projinfo build (#2405)

 o createBoundCRSToWGS84IfPossible(): make it return same result with a CRS
   built from EPSG code or WKT1 (#2412)

 o WKT2 parsing: several fixes related to map projection parameter units (#2428)

 o createOperation(): make it work properly when one of the CRS is a BoundCRS of
   a DerivedGeographicCRS (+proj=ob_tran +o_proj=lonlat +towgs84=....) (#2441)

 o WKT parsing: fix ingestion of WKT with a Geocentric CRS as the base of the
   projected CRS (#2443)

 o GeographicCRS::_isEquivalentTo(EQUIVALENT_EXCEPT_AXIS_ORDER_GEOGCRS):
   make it work when comparing easting,northing,up and northing,easting,up (#2446)

 o createOperation(): add a ballpark vertical transformation when dealing
   with GEOIDMODEL[] (#2449)

 o Use same arguments to printf format string for both radians and degrees in
   output by cct (#2453)

 o PRIMEM WKT handling: fixes on import for 'sexagesimal DMS' or from WKT1:GDAL/ESRI
   when GEOGCS UNIT != Degree; morph to ESRI the PRIMEM name on export (#2455)

 o createObjectsFromName(): in exact match, make looking for 'ETRS89 / UTM zone 32N'
   return only the exact match (#2462)

 o Inverse tmerc spherical: fix wrong sign of latitude when lat_0 is used (#2469)

 o Add option to allow export of Geographic/Projected 3D CRS in WKT1_GDAL (#2470)

 o Fix building proj.db with SQLite built with -DSQLITE_DQS=0 (#2480)

 o Include JSON Schema files in CMake builds (#2485)

 o createOperations(): fix inconsistent chaining exception when transforming from BoundCRS of projected CRS based on NTF Paris to BoundCRS of geog CRS NTF Paris (#2486)

 THANKS TO
 ---------

 Zac Miller
 Nomit Rawat
 Guillaume Lostis
 J.H. van de Water
 Kristian Evers
 Even Rouault


7.2.0 Release Notes
-------------------

 Updates
 -------

 + Command line tools:

   o Add multi-line PROJ string export capability, and use it by default in
     projinfo (unless --single-line is specified) (#2381)

 + Coordinate operations:

    o +proj=col_urban projection, implementing a EPSG projection method
      used by a number of projected CRS in Colombia (#2395)

    o +proj=tinshift for triangulation-based transformations (#2344)

    o Added ellipsoidal formulation of +proj=ortho (#2361)


 + Database

   o Update to EPSG 10.003 and make code base robust to dealing with
     WKT CRS with DatumEnsemble (#2370)

   o Added Finland tinshift operations (#2392)

   o Added transformation from JGD2011 Geographic 3D to JGD2011
     height using GSIGEO2011 (#2393)

   o Improve CompoundCRS identification and name morphing in VerticalCRS
     with ESRI WKT1 (#2386)

   o Added OGC:CRS27 and OGC:CRS83 CRS entries for NAD27 and NAD83
     in longitude, latitude order (#2350)

 + API

 o Added temporal, engineering, and parametric datum
   PJ_TYPE enumerations (#2274)

  o Various improvements to context handling (#2329, #2331)

  o proj_create_vertical_crs_ex(): add a ACCURACY option to provide
     an explicit accuracy, or derive it from the grid name if it is
     known (#2342)

   o proj_crs_create_bound_crs_to_WGS84(): make it work on
     verticalCRS/compoundCRS such as EPSG:4326+5773 and
     EPSG:4326+3855 (#2365)

   o promoteTo3D(): add a remark with the original CRS identifier (#2369)

   o Added proj_context_clone (#2383)


 Bug fixes
 ---------

 o Avoid core dumps when copying contexts in certain scenarios (#2324)

 o proj_trans(): reset errno before attemptying a retry with a new
   coordinate operation (#2353)

 o PROJJSON schema corrected to allow prime meridians values with
   explicitly stating a unit (degrees assumed) (#2354)

 o Adjust createBoundCRSToWGS84IfPossible() and operation filtering
   (for POSGAR 2007 to WGS84 issues) (#2357)

 o createOperations(): several fixes affecting NAD83 -> NAD83(2011) (#2364)

 o WKT2:2019 import/export: handle DATUM (at top level object) with PRIMEM

 o WKT1_ESRI: fix import and export of CompoundCRS (#2389)


 THANKS TO
 ---------

 Alexander Saprykin
 Jeff McKenna
 Nyall Dawson
 Kai Pastor
 Juan Hernando
 Javier Jimenez Shaw
 Howard Butler
 Alan D. Snow
 Charles Karney
 Kristian Evers
 Even Rouault


7.1.1 Release Notes
-------------------

 Updates
 -------

  o Added various Brazillian grids to the database #2277

  o Added geoid file for Canary Islands to the database #2312

  o Updated EPSG database to version 9.8.15 #2310

 Bug fixes
 ---------

  o WKT parser: do not raise warning when parsing a WKT2:2015 TIMECRS
    whose TIMEUNIT is at the CS level, and not inside #2281

  o Parse '+proj=something_not_latlong +vunits=' without +geoidgrids as a
    Projected3D CRS and not a compound CRS with a unknown datum #2289

  o C API: Avoid crashing due to missing SANITIZE_CTX() in entry points #2293

  o CMake build: Check "target_clones" before use #2297

  o PROJ string export of +proj=krovak +czech: make sure we export +czech… #2301

  o Helmert 2D: do not require a useless +convention= parameter #2305

  o Fix a few spelling errors ("vgridshit" vs. "vgridshift") #2307

  o Fix ability to identify EPSG:2154 as a candidate for 'RGF93_Lambert_93' #2316

  o WKT importer: tune for Oracle WKT and 'Lambert Conformal Conic' #2322

  o Revert compiler generated Fused Multiply Addition optimized routines #2328

 THANKS TO
 ---------

 Jeff McKenna
 Kai Pastor
 Javier Jimenez Shaw
 Kristian Evers
 Even Rouault





7.1.0 Release Notes
-------------------

 Updates
 -------

  + New transformations:

    o Add a +proj=defmodel transformation for multi-component time-based deformation models (#2206)

  + New projections:

    o Add square conformal projections from libproject:
      - Adams Hemisphere in a Square
      - Adams World in a Square I
      - Adams World in a Square II
      - Guyou
      - Pierce Quincuncial
      (#2148)

    o Adams Square II: map ESRI WKT to PROJ string, and implement iterative
      inverse method (#2157)

    o Added IGH Oceanic View projection (#2226)

    o Add wink2 inverse by generic inversion of forward method (#2243)

  + Database:

    o Update to EPSG 9.8.12, ESRI 10.8.1 and import scope and remarks for
      conversion (#2238) (#2267)

    o Map the Behrmann projection to cae when converting ESRI CRSes (#1986)

    o Support conversion of Flat_Polar_Quartic projection method (#1987)

    o Register 4 new Austrian height grids (see https://github.com/OSGeo/PROJ-data/pull/13)
      and handle 'Vertical Offset by Grid Interpolation (BEV AT)' method (#1989)

    o Add ESRI projection method mappings for Mercator_Variant_A, Mercator_Variant_B
      and Transverse_Cylindrical_Equal_Area and various grid mappings (#2020) (#2195)

    o Map ESRI Transverse_Mercator_Complex to Transverse Mercator (#2040)

    o Register grids for New Caledonia (see https://github.com/OSGeo/PROJ-data/pull/16) (#2051) (#2239)

    o Register NZGD2000 -> ITRF96 transformation for NZGD2000 database (#2248)

    o Register geoid file for UK added
      (see https://github.com/OSGeo//PROJ-data/pull/25() (#2250)

    o Register Slovakian geoid transformations with needed code changes (#2259)

    o Register Spanish SPED2ETV2 grid for ED50->ETRS89 (#2261)

  + API:

    o Add API function proj_get_units_from_database() (#2065)

    o Add API function proj_get_suggested_operation() (#2068)

    o Add API functions proj_degree_input() and proj_degree_output() (#2144)

    o Moved proj_context_get_url_endpoint & proj_context_get_user_writable_directory
      from proj_experimental.h to proj.h (#2162)

    o createFromUserInput(): allow compound CRS with the 2 parts given by names,
      e.g. 'WGS 84 + EGM96 height' (#2126)

    o createOperations(): when converting CompoundCRS<-->Geographic3DCrs, do not
      use discard change of ellipsoidal height if a Helmert transformation is
      involved (#2227)

    o proj_list_units() deprecated, superceeded by proj_get_units_from_database()

    o proj_list_angular_units() deprecated, superceeded by proj_get_units_from_database()

 + Optimizations:

    o tmerc/utm: add a +algo=auto/evenden_snyder/poder_engsager parameter (#2030)

    o Extended tmerc (Poder/Engsager): speed optimizations (#2036)

    o Approximate tmerc (Snyder): speed optimizations (#2039)

    o pj_phi2(): speed-up computation (and thus inverse ellipsoidal Mercator and LCC) (#2052)

    o Inverse cart: speed-up computation by 33% (#2145)

    o Extended tmerc: speed-up forward path by ~5% (#2147)

  + Various:

    o Follow PDAL's CMake RPATH strategy (#2009)

    o WKT import/export: add support for WKT1_ESRI VERTCS synta (#2024)

    o projinfo: add a --hide-ballpark option (#2127)

    o gie: implement a strict mode with <gie-strict> </gie-strict> (#2168)

    o Allow importing WKT1 COMPD_CS with a VERT_DATUM[Ellipsoid,2002] (#2229)

    o Add runtime checking that sqlite3 is >= 3.11 (#2235)


 Bug fixes
 ---------

 o createOperations(): do not remove ballpark transformation if there are only grid
   based operations, even if they cover the whole area of use (#2155)

 o createFromProjString(): handle default parameters of '+krovak +type=crs', and
   handle +czech correctly (#2200)

 o ProjectedCRS::identify(): fix identification of EPSG:3059 (#2215)

 o Database: add a 'WGS84' alias for the EPSG:4326 CRS (#2218)

 o Fixes related to CompoundCRS and BoundCRS (#2222)

 o  Avoid 2 warnings about missing database indices (#2223)

 o Make projinfo --3d --boundcrs-to-wgs84 work better (#2224)

 o Many fixes regarding BoundCRS, CompoundCRS, Geographic3D CRS with
  non-metre units (#2234)

 o  Fix identification of (one of the) ESRI WKT formulations of EPSG:3035 (#2240)

 o Avoid using deprecated and removed Windows API function with Mingw32 (#2246)

 o normalizeForVisualization(): make it switch axis for EPSG:5482
   (RSRGD2000 / RSPS2000) (#2256)

 o Fix access violation in proj_context_get_database_metadata (#2260)

 THANKS TO
 ---------

 Martin Raspaud
 Jeroen Ooms
 Jeff McKenna
 Colin Doig
 Chris Mayo
 Chatziargyriou Eleftheria
 Bas Couwenberg
 B R S Recht
 積丹尼 Dan Jacobson
 Alan D. Snow
 GitHub user @chrodger
 Pedro Venancio
 Olli Räisä
 John Krasting
 Andrei Marshalov
 Javier Jimenez Shaw
 Martin Dobias
 Howard Butler
 Nyall Dawson
 Mike Taves
 Kristian Evers
 Even Rouault

7.0.1 Release Notes
-------------------

 Updates
 -------

 o Database: update to EPSG v9.8.9 #2141

 Bug fixes
 ---------

 o Make tests independent of proj-datumgrid (#1995)

 o Add missing projection property tables (#1996)

 o Avoid crash when running against SQLite3 binary built with
   -DSQLITE_OMIT_AUTOINIT (#1999)

 o createOperations(): fix wrong pipeline generation with CRS that has +nadgrids=
   and +pm= (#2002)

 o Fix bad copy&replace pattern on HEALPix and rHEALPix projection names (#2007)

 o createUnitOfMeasure(): use full double resolution for the conversion
   factor (#2014)

 o Update README with info on PROJ-data (#2015)

 o utm/ups: make sure to set errno to PJD_ERR_ELLIPSOID_USE_REQUIRED if
   es==0 (#2045)

 o data/Makefile.am: remove bashism (#2048)

 o ProjectedCRS::identify(): tune it to better work with ESRI WKT
   representation of EPSG:2193 (#2059)

 o Fix build with gcc 4.8.5 (#2066)

 o Autotools/pkg-conf: Define datarootdir (#2069)

 o cs2cs: don't require +to for '{source_crs} {target_crs} filename...'
   syntax (#2081)

 o CMake: fix bug with find_package(PROJ) with macOS (#2082)

 o ESRI WKT import / identification: special case for
   NAD_1983_HARN_StatePlane_Colorado_North_FIPS_0501 with Foot_US unit (#2088)

 o ESRI WKT import / identification: special case for
   NAD_1983_HARN_StatePlane_Colorado_North_FIPS_0501 with Foot_US unit (#2089)

 o EngineeringCRS: when exporting to WKT1_GDAL, output unit and axis (#2092)

 o Use jtsk03-jtsk horizontal grid from CDN (#2098)

 o CMake: prefer to use use PROJ_SOURCE_DIR and PROJ_BINARY_DIR (#2100)

 o Fix wrong grids file name in esri.sql (#2104)

 o Fix identification of projected CRS whose name is close but not strictly
   equal to a ESRI alias (#2106)

 o Fix working of Helmert transform between the horizontal part of
   2 compoundCRS (#2111)

 o Database: fix registration of custom entries of grid_transformation_custom.sql
   for geoid grids (#2114)

 o ESRI_WKT ingestion: make sure to identify to non-deprecated EPSG entry when
   possible (#2119)

 o Make sure that importing a Projected 3D CRS from WKT:2019 keeps the base
   geographic CRS as 3D (#2125)

 o createOperations(): improve results of compoundCRS to compoundCRS case (#2131)

 o hgridshift/vgridshift: defer grid opening when grid has already
   been opened (#2132)

 o Resolve a few shadowed declaration warnings (#2142)

 o ProjectedCRS identification: deal with switched 1st/2nd std parallels for
   LCC_2SP(#2153)

 o Fix Robinson inverse projection (#2154)

 o createOperations(): do not remove ballpark transformation if there are only
   grid based operations, even if they cover the whole area of use (#2156)

 o createFromCoordinateReferenceSystemCodes(): 'optimization' to avoid using
   C++ exceptions (#2161)

 o Ingestion of WKT1_GDAL: correctly map 'Cylindrical_Equal_Area' (#2167)

 o Add limited support for non-conformant WKT1 LAS COMPD_CS[] (#2172)

 o PROJ4 string import: take into correctly non-metre unit when the string
   looks like the one for WGS 84 / Pseudo Mercator (#2177)

 o io.hpp: avoid dependency to proj_json_streaming_writer.hpp (#2184)

 o Fix support of WKT1_GDAL with netCDF rotated pole formulation (#2186)


 THANKS TO
 ---------

 Mike Taves
 Chris Mayo
 Kristian Evers
 Even Rouault


7.0.0 Release Notes
-------------------

The major feature in PROJ 7 is significantly improved handling of gridded
models. This was implemented in RFC4 (https://proj.org/community/rfc/rfc-4.html).
The main features of the RFC4 work is that PROJ now implements a new grid format,
Geodetic TIFF grids, for exchaning gridded transformation models. In addition
to the new grid format, PROJ can now also access grids online using a data
store in the cloud.

The grids that was previously available via the proj-datumgrid packages are now
available in two places:

  1. As a single combined data archive including all available resource files
  2. From the cloud via https://cdn.proj.org

In Addition, provided with PROJ is a utility called projsync that can be used
download grids from the data store in the cloud.

The use of the new grid format and the data from the cloud requires that
PROJ is build against libtiff and libcurl. Both are optional dependencies
to PROJ but it is highly encouraged that the software is build against both.


ATTENTION: PROJ 7 will be last major release version that includes the proj_api.h
header. The functionality in proj_api.h is deprecated and only supported in
maintenance mode. It is inferior to the functionality provided by functions
in the proj.h header and all projects still relying on proj_api.h are encouraged
to migrate to the new API in proj.h. See https://proj.org/development/migration.html
for more info on how to migrate from the old to the new API.

 Updates
 -------
 o Added new file access API to proj.h #866

 o Updated the name of the most recent version of the WKT2 standard from
   WKT2_2018 to WKT2_2019 to reflect the proper name of the standard (#1585)

 o Improvements in transformations from/to WGS 84 (Gxxxx) realizations and
   vertical <--> geog transormations #1608

 o Update to version 1.50 of the geodesic library (#1629)

 o Promote proj_assign_context to proj.h from proj_experimental.h (#1630)

 o Add rotation support to the HEALPix projection (#1638)

 o Add c function proj_crs_create_bound_vertical_crs() (#1689)

 o Use Win32 Unicode APIs and expect all strings to be UTF-8 (#1765)

 o Improved name aliases lookup (#1827)

 o CMake: Employ better use of CTest with the BUILD_TESTING option (#1870)

 o Grid correction: fix handling grids spanning antimeridian (#1882)

 o Remove legacy CMake target name "proj" #1883

 o projinfo: add --searchpaths switch (#1892)

 o Add +proj=set operation to set component(s) of a coordinate to a fixed
   value (#1896)

 o Add EPSG records for 'Geocentric translation by Grid Interpolation (IGN)'
   (gr3df97a.txt) and map them to new +proj=xyzgridshift (#1897)

 o Remove 'null' grid file as it is now a special hardcoded case in grid
   code (#1898)

 o Add projsync utility (#1903)

 o Make PROJ the CMake project name #1910

 o Use relative directory to locate PROJ resource files (#1921)


 Bug fixes
 ---------

 o Horizontal grid shift: fix failures on points slightly outside a
   subgrid (#209)

 o Fix ASAN issue with SQLite3VFS class (#1902)

 o tests: force use of bash for proj_add_test_script_sh (#1905)


 Breaking changes
 ----------------

 o Reject NTV2 files where GS_TYPE != SECONDS #1294

 o On Windows the name of the library is now fixed to ``proj.lib`` instead
   of encoding the version number in the library name (#1581)

 o Require C99 compiler (#1624)

 o Remove deprecated JNI bindings (#1825)

 o Remove -ld option from proj and cs2cs (#1844)

 o Increase CMake minimum version from 3.5 to 3.9 (#1907)

 THANKS TO
 ---------

 Jeff McKenna
 Calum Robinson
 Anshul Singhvi
 Bas Couwenberg
 Mike Taves
 Alan D. Snow
 Charles Karney
 Kristian Evers
 Even Rouault

6.3.1 Release Notes
-------------------

 Updates
 -------

 o Update the EPSG database to version 9.8.6

 o Database: add mapping for gg10_smv2.mnt and gg10_sbv2.mnt French grids

 o Database: add mapping for TOR27CSv1.GSB

 Bug fixes
 ---------

 o Fix wrong use of derivingConversionRef() that caused issues with use of
   +init=epsg:XXXX by GDAL (affecting R spatial libraries) or in MapServer

 o fix exporting CoordinateSystem to PROJ JSON with ID

 o projinfo: use No. abbreviation instead of UTF-8 character (#1828)

 o CompoundCRS::identify(): avoid exception when horiz/vertical part is a
   BoundCRS

 o createOperations(): fix dealing with projected 3D CRS whose Z units != metre

 o WKT1_GDAL export: limit datum name massaging to names matching EPSG (#1835)

 o unitconvert with mjd time format: avoid potential integer overflow
  (ossfuzz 20072)

 o ProjectedCRS::identify(): fix wrong identification of some ESRI WKT linked
   to units

 o Database: add a geoid_like value for proj_method column of grid_alternatives,
   fix related entries and simplify/robustify logic to deal with EPSG
   'Geographic3D to GravityRelatedHeight' methods

 o Fix ingestion of +proj=cea with +k_0 (#1881)

 o Fix performance issue, affecting PROJ.4 string generation of EPSG:7842
   (#1913)

 o Fix identification of ESRI-style datum names starting with D_ but without
   alias (#1911)

 o cart: Avoid discontinuity at poles in the inverse case (#1906)

 o Various updates to make regression test suite pass with gcc on i386 (#1906)

 THANKS TO
 ---------

 Alan D. Snow
 GitHub user @russkel
 Gerrit Holl
 Anshul Singhvi
 Raven Kopelman
 Kristian Evers
 Even Rouault

6.3.0 Release Notes
-------------------

 Updates
 -------

 o Database: tune accuracy of Canadian NTv1 file w.r.t NTv2 (#1812)

 o Modify verbosity level of some debug/trace messages (#1811)

 o projinfo: no longer call createBoundCRSToWGS84IfPossible() for WKT1:GDAL
   (#1810)

 o proj_trans: add retry logic to select other transformation if the best one
   fails. (#1809)

 o BoundCRS::identify(): improvements to discard CRS that aren't relevant
   (#1802)

 o Database: update to IGNF v3.1.0 (#1785)

 o Build: Only export symbols if building DLL (#1773)

 o Database: update ESRI entries with ArcGIS Desktop version 10.8.0 database
   (#1762)

 o createOperations(): chain operations whose middle CRSs are not identical but
   have the same datum (#1734)

 o import/export PROJJSON: support a interpolation_crs key to geoid_model
   (#1732)

 o Database: update to EPSG v9.8.4 (#1725)

 o Build: require SQLite 3.11 (#1721)

 o Add support for GEOIDMODEL (#1710)

 o Better filtering based on extent and performance improvements (#1709)


 Bug fixes
 ---------

 o Horizontal grid shift: fix issue on iterative inverse computation when
   switching between (sub)grids (#1797)

 o createOperations(): make filtering out of 'uninteresting' operations less
   aggressive (#1788)

 o Make EPSG:102100 resolve to ESRI:102100 (#1786)

 o ob_tran: restore traditional handling of +to_meter with pj_transform() and
   proj utility (#1783)

 o CRS identification: use case insensitive comparison for authority name
   (#1780)

 o normalizeForVisualization() and other methods applying on a ProjectedCRS: do
   not mess the derivingConversion object of the original object (#1746)

 o createOperations(): fix transformation computation from/to a CRS with
   +geoidgrids and +vunits != m (#1731)

 o Fix proj_assign_context()/pj_set_ctx() with pipelines and alternative coord
   operations (#1726)

 o Database: add an auxiliary concatenated_operation_step table to allow
   arbitrary number of steps (#1696)

 o Fix errors running gie-based tests in Debug mode on Windows (#1688)

 THANKS TO
 ---------

 Pedro Venancio
 Owen Rudge
 Nyall Dawson
 Mateusz Łoskot
 Markus Neteler
 Juergen E. Fischer
 Joaquim Luis
 Jeff McKenna
 Jakob Egger
 Guillaume Lostis
 GitHub user @yonarw
 Asa Packer
 Joe Mann
 Stephan Hügel
 Simon Schneegans
 R. Schmunk
 Alan D. Snow
 Chris Crook
 Howard Butler
 Fabrice Fontaine
 Kai Pastor
 Martin Desruisseaux
 Dalia Prizginiene
 Mike Taves
 Charles Karney
 Kristian Evers
 Even Rouault

6.2.1 Release Notes
-------------------

 Updates
 -------

 o Update the EPSG database to version 9.8.2

 Bug fixes
 -------

 o Fixed erroneous spelling of "Potsdam" (#1573)

 o Calculate y-coordinate correctly in bertin1953 in all cases (#1579)

 o proj_create_crs_to_crs_from_pj(): make the PJ* arguments const PJ* (#1583)

 o PROJStringParser::createFromPROJString(): avoid potential infinite
   recursion (#1574)

 o Avoid core dump when setting ctx==NULL in functions
   proj_coordoperation_is_instantiable and
   proj_coordoperation_has_ballpark_transformation (#1590)

 o createOperations(): fix conversion from/to PROJ.4 CRS strings with
   non-ISO-kosher options and +towgs84/+nadgrids (#1602)

 o proj_trans_generic(): properly set coordinate time to HUGE_VAL when no
   value is passed to the function (#1604)

 o Fix support for +proj=ob_tran +o_proj=lonlat/latlong/latlon instead of only
   only allowing +o_proj=longlat (#1601)

 o Improve backwards compatibility of vertical transforms (#1613)

 o Improve emulation of deprecated +init style initialization (#1614)

 o cs2cs: autopromote CRS to 3D when there's a mix of 2D and 3D (#1563)

 o Avoid divisions by zero in odd situations (#1620)

 o Avoid compile error on Solaris (#1639)

 o proj_create_crs_to_crs(): fix when there are only transformations with
   ballpark steps (#1643)

 o PROJ string CRS ingester: recognize more unit-less parameters, and general
   handling of +key=string_value parameters (#1645)

 o Only call pkg-config in configure when necessary (#1652)

 o aeqd: for spherical forward path, go to higher precision ellipsoidal
   case when the point coordinates are super close to the origin (#1654)

 o proj_create_crs_to_crs(): remove elimination of Ballpark operations
   that caused transformation failures in some cases (#1665)

 o createOperations(): allow transforming from a compoundCRS of a bound
   verticalCRS to a 2D CRS (#1667)

 o Avoid segfaults in case of out-of-memory situations (#1679)

 o createOperations(): fix double vertical unit conversion from CompoundCRS
   to other CRS when the horizontal part of the projected CRS uses non-metre
   unit (#1683)

 o importFromWkt(): fix axis orientation for non-standard ESRI WKT (#1690)


 THANKS TO
 ---------

 R. Schmunk
 Jakob Egger
 Alan D. Snow
 Stephan Hügel
 Kai Pastor
 Kristian Evers
 Even Rouault

6.2.0 Release Notes
-------------------

 Updates
 -------

 o Introduced PROJJSON, a JSON encoding of WKT2 (#1547)

 o Support CRS instantiation of OGC URN's (#1505)

 o Expose scope and remarks of database objects (#1537)

 o EPSG Database updated to version 9.7.0 (#1558)

 o Added C API function proj_grid_get_info_from_database() (#1494)

 o Added C API function
   proj_operation_factory_context_set_discard_superseded() (#1534)

 o Added C API function proj_context_set_autoclose_database() (#1566)

 o Added C API function proj_create_crs_to_crs_from_pj() (#1567)

 o Added C API function proj_cleanup() (#1569)

 Bug Fixes
 ---------

 o Fixed build failure on Solaris systems (#1554)

 THANKS TO
 ---------

 Version 6.2.0 is made possible by the following contributors:

 GitHub user @edechaux
 Michael D. Smith
 Matt Littlemore
 Kristian Evers
 Even Rouault

6.1.1 Release Notes
-------------------

 Updates
 -------

 o Update EPSG registry to version 9.6.3 (1485)


 Bug Fixes
 ---------

 o Take the passed authority into account when identifying
   objects (#1466)

 o Avoid exception when transforming from NAD83 to projected
   CRS using NAD83(2011) (#1477)

 o Avoid off-by-one reading of name argument if name of resource
   file has length 1 (#1489)

 o Do not include PROJ_LIB in proj_info().searchpath when context
   search path is set (#1498)

 o Use  correct delimeter for the current platform when parsing
   PROJ_LIB (#1497)

 o Do not confuse 'ID74' CRS with WKT2 ID[] node (#1506)

 o WKT1 importer: do case insensitive comparison for axis
   direction (#1509)

 o Avoid compile errors on GCC 4.9.3 (#1512)

 o Make sure that pipelines including +proj=ob_tran can be
   created (#1526)


 THANKS TO
 ------------

 Version 6.1.1 is made possible by the following contributors:


 Alan D. Snow
 Paul Menzel
 Mateusz Łoskot
 Bas Couwenberg
 Peter Limkilde Svendsen
 Mike Taves
 Howard Butler
 Nyall Dawson
 Andrew Bell
 Kristian Evers
 Even Rouault

6.1.0 Release Notes
-------------------

 Updates
 -------

 o Include custom ellipsoid definitions from QGIS (#1337)

 o Add "-k ellipsoid" option to projinfo (#1338)

 o Make cs2cs support 4D coordinates (#1355)

 o WKT2 parser: update to OGC 18-010r6 (#1360 #1366)

 o Update internal version of googletest to v1.8.1 (#1361)

 o Database update: EPSG v9.6.2 (#1462), IGNF v3.0.3, ESRI 10.7.0
   and add operation_version column (#1368)

 o Add proj_normalize_for_visualization() that attempts to apply axis
   ordering as used by most GIS applications and PROJ <6 (#1387)

 o Added noop operation (#1391)

 o Paths set by user take priority over PROJ_LIB for search paths (#1398)

 o Reduced database size (#1438)

 o add support for compoundCRS and concatenatedOperation named from
   their components (#1441)

 Bug fixes
 ---------

 o Have gie return non-zero code when file can't be opened (#1312)

 o CMake cross-compilation fix (#1316)

 o Use 1st eccentricity instead of 2nd eccentricity in Molodensky (#1324)

 o Make sure to include grids when doing Geocentric to CompoundCRS with
   nadgrids+geoidgrids transformations (#1326)

 o Handle coordinates outside of bbox better (#1333)

 o Enable system error messages in command line automatically in builds (#1336)

 o Make sure to install projinfo man page with CMake (#1347)

 o Add data dir to pkg-config file proj.pc (#1348)

 o Fix GCC 9 warning about useless std::move() (#1352)

 o Grid related fixes (#1369)

 o Make sure that ISO19111 C++ code sets pj_errno on errors (#1405)

 o vgridshift: handle longitude wrap-around for grids with 360deg
   longitude extent (#1429)

 o proj/cs2cs: validate value of -f parameter to avoid potential crashes (#1434)

 o Many division by zero and similar bug fixes found by OSS Fuzz.

 THANKS TO
 ------------

 Version 6.1.0 is made possible by the following contributors:

 Andrew Hardin
 Sean Warren
 Dan Baston
 Howard Butler
 Joris Van den Bossche
 Elliott Sales de Andrade
 Alan D. Snow
 Nyall Dawson
 Chris Mayo
 Mike Taves
 Kristian Evers
 Even Rouault


6.0.0 Release Notes
-------------------

PROJ 6 has undergone extensive changes to increase its functional scope from a
cartographic projection engine with so-called "early-binding" geodetic datum
transformation capabilities to a more complete library supporting coordinate
transformations and coordinate reference systems.

As a foundation for other enhancements, PROJ now includes a C++ implementation
of the modelisation propopsed by the ISO-19111:2019 standard / OGC Abstract
Specification Topic 2: "Referencing By Coordinates", for geodetic reference
frames (datums), coordinate reference systems and coordinate operations.
Construction and query of those geodetic objects is available through a new C++
API, and also accessible for the most part from bindings in the C API.

Those geodetic objects can be imported and exported from and into the OGC
Well-Known Text format (WKT) in its different variants: ESRI WKT, GDAL WKT 1,
WKT2:2015 (ISO 19162:2015) and WKT2:2018 (ISO 19162:2018). Import and export of
CRS objects from and into PROJ strings is also supported. This functionality
was previously available in the GDAL software library (except WKT2 support
which is a new feature), and is now an integral part of PROJ.

A unified database of geodetic objects, coordinate reference systems and their
metadata, and coordinate operations between those CRS is now available in a
SQLite3 database file, proj.db. This includes definitions imported from the
IOGP EPSG dataset (v9.6.0 release), the IGNF (French national mapping agency)
geodetic registry and the ESRI projection engine database. PROJ is now the
reference software in the "OSGeo C stack" for this CRS and coordinate operation
database, whereas previously this functionality was spread over PROJ, GDAL and
libgeotiff, and used CSV or other adhoc text-based formats.

Late-binding coordinate operation capabilities, that takes  metadata such as
area of use and accuracy into account, has been added. This can avoid in a
number of situations the past requirement of using WGS84 as a pivot system,
which could cause unneeded accuracy loss, or was not doable at all sometimes
when transformation to WGS84 was not available. Those late-binding capabilities
are now used by the proj_create_crs_to_crs() function and the cs2cs utility.

A new command line utility, projinfo, has been added to query information about
a geodetic object of the database, import and export geodetic objects from/into
WKT and PROJ strings, and display coordinate operations available between two
CRSs.

 UPDATES
 -------

 o Removed projects.h as a public interface (#835)

 o Deprecated the proj_api.h interface. The header file is still available
   but will be removed with the next major version release of PROJ. It is
   now required to define ACCEPT_USE_OF_DEPRECATED_PROJ_API_H before the
   interface can be used (#836)

 o Removed support for the nmake build system (#838)

 o Removed support for the proj_def.dat defaults file (#201)

 o C++11 required for building PROJ (#1203)

 o Added build dependency on SQLite 3.7 (#1175)

 o Added projinfo command line application (#1189)

 o Added many functions to proj.h for handling ISO19111 functionality (#1175)

 o Added C++ API exposing ISO19111 functionality (#1175)

 o Updated cs2cs to use late-binding features (#1182)

 o Removed the nad2bin application. Now available in the proj-datumgrid
   git repository (#1236)

 o Removed support for Chebyshev polynomials in proj (#1226)

 o Removed proj_geocentric_latitude from proj.h API (#1170)

 o Changed behaviour of proj: Now only allow initialization of
   projections (#1162)

 o Changed behaviour of tmerc: Now default to the Extended Transverse
   Mercator algorithm (etmerc). Old implementation available by adding
   +approx (#404)

 o Chaged behaviour: Default ellipsoid now set to GRS80 (was WGS84) (#1210)

 o Allow multiple directories in PROJ_LIB environment variable (#1281)

 o Added Lambert Conic Conformal (2SP Michigan) projection (#1142)

 o Added Bertin1953 projection (#1133)

 o Added Tobler-Mercator projection (#1153)

 o Added Molodensky-Badekas transform (#1160)

 o Added push and pop coordinate operations (#1250)

 o Removed +t_obs parameter from helmert and deformation (#1264)

 o Added +dt parameter to deformation as replacement for
   removed +t_obs (#1264)

 BUG FIXES
 ---------

 o Read +towgs84 values correctly on locales not using dot as comma separator (#1136)

 o Fixed file offset for reading of shift values in NTv1 files (#1144)

 o Avoid problems with PTHREAD_MUTEX_RECURSIVE when using CMake (#1158)

 o Avoid raising errors when setting ellipsoid flattening to zero (#1191)

 o Fixed lower square calculations in rHealpix projection (#1206)

 o Allow Molodensky transform parameters to be zero (#1194)

 o Fixed wrong parameter in ITRF2000 init file (#1240)

 o Fixed use of grid paths including spaces (#1152)

 o Robinson: fix wrong values for forward path for latitudes >= 87.5 (#1172),
   and fix inaccurate inverse method.

 THANKS TO
 ------------

 Version 6.0.0 is made possible by the following contributors:

 Aaron Puchert
 Thomas Knudsen
 Phil Elson
 Mateusz Łoskot
 Markus Neteler
 Jürgen Fischer
 Charles Karney
 Bas Couwenberg
 Karoline Skaar
 Alan D. Snow
 Howard Butler
 Marco Bernasocchi
 Ben Boeckel
 Ivan Veselov
 Philippe Rivière
 Mike Taves
 Elliott Sales de Andrade
 Kai Pastor
 Kristian Evers
 Even Rouault

 5.2.0 Release Notes
-------------------

 UPDATES
 -------

 o Added support for deg, rad and grad in unitconvert (#1054)

 o Assume +t_epoch as time input when not otherwise specified (#1065)

 o Added inverse Lagrange projection (#1058)

 o Added +multiplier option to vgridshift (#1072)

 o Added Equal Earth projection (#1085)

 o Added "require_grid" option to gie (#1088)

 o Replace +transpose option of Helmert transform with +convention.
   From now on the convention used should be explicitly written. An
   error will be returned when using the +transpose option (#1091)

 o Improved numerical precision of inverse spherical Mercator
   projection (#1105)

 o cct will now forward text after coordinate input to output
   stream (#1111)


 BUG FIXES
 ------------

 o Do not pivot over WGS84 when doing cs2cs-emulation with geocent (#1026)

 o Do not scan past the end of the read data in pj_ctx_fgets (#1042)

 o Make sure proj_errno_string() is available in DLL (#1050)

 o Respect +to_meter setting when doing cs2cs-emulation (#1053)

 o Fixed unit conversion factors for geod (#1075)

 o Fixed test failures related to GCC 8 (#1084)

 o Improved handling of +geoc flag (#1093)

 o Calculate correct projection factors for Webmercator (#1095)

 o cs2cs now always outputs degrees when transformed coordinates are
   in angular units (#1112)


 All bug fix numbers refer to issues or pull requests indexed at
 https://github.com/OSGeo/proj.4/

 THANKS TO
 ------------

 Version 5.2.0 is made possible by the following contributors:

Søren Holm
Mateusz Łoskot
Jürnjakob Dugge
Greg Minshall
Aaron Puchert
Vedran Stojnović
Bojan Šavrič
Charles Karney
Mateusz Loskot
Howard Butler
Mike Toews
Kurt Schwehr
Even Rouault
Kristian Evers

5.1.0 Release Notes
-------------------

 UPDATES
 -------

 o Function proj_errno_string() added to proj.h API (#847)

 o Validate units between pipeline steps and ensure transformation
   sanity (#906)

 o Print help when calling cct and gie without arguments (#907)

 o CITATION file added to source distribution (#914)

 o Webmercator operation added (#925)

 o Enhanced numerical precision of forward spherical Mercator near
   the Equator (#928)

 o Added --skip-lines option to cct (#923)

 o Consistently return NaN values on NaN input (#949)

 o Removed unused src/org_proj4_Projections.h file (#956)

 o Java Native Interface bindings updated (#957, #969)

 o Horizontal and vertical gridshift operations extended to
   the temporal domain (#1015)


 BUG FIXES
 ------------

 o Handle nan float cast overflow in PJ_robin.c and nad_intr.c (#887)

 o Avoid overflow when Horner order is unreasonably large (#893)

 o Avoid unwanted NaN conversions in etmerc (#899)

 o Avoid memory failure in gie when not specifying x,y,z in gie files (#902)

 o Avoid memory failure when +sweep is initialized incorrectly in geos (#908)

 o Return HUGE_VAL on erroneous input in ortho (#912)

 o Handle commented lines correctly in cct (#933)

 o Avoid segmentation fault when transformation coordinates outside grid
   area in deformation (#934)

 o Avoid doing false easting/northing adjustments on cartesian
   coordinates (#936)

 o Thread-safe creation of proj mutex (#954)

 o Avoid errors when setting up geos with +lat_0!=0 (#986)

 o Reset errno when running proj in verbose mode (#988)

 o Do not interpolate node values at nodata value in vertical
   grid shifts (#1004)

 o Restrict Horner degrees to positive integer values to avoid
   memory allocation issues (#1005)

 All bug fix numbers refer to issues or pull requests indexed at
 https://github.com/OSGeo/proj.4/

 THANKS TO
 ------------

 Version 5.1.0 is made possible by the following contributors:

   Kristian Evers
   Even Rouault
   Kurt Schwehr
   Mike Toews
   Martin Desruisseaux
   Charles Karney
   Thomas Knudsen
   Javier Goizueta
   Bas Couwenberg
   Adam Wulkiewicz
   Aaron Puchert

5.0.1 Release Notes
-------------------

 BUG FIXES
 ------------

 All bug fix numbers refer to issues or pull requests indexed at
 https://github.com/OSGeo/proj.4/

 o Handle ellipsoid change correctly in pipelines when
   +towgs84=0,0,0 is set #881

 o Handle the case where nad_ctable2_init returns NULL #883

 o Avoid shadowed declaration errors with old gcc #880

 o Expand +datum properly +datum in pipelines #872

 o Fail gracefully when incorrect headers are encountered in grid
   files #875

 o Improve roundtrip stability in pipelines using +towgs84 #871

 o Fixed typo in gie error codes #861

 o Numerical stability fixes to the geodesic package #826 #843

 o Make sure that transient errors are returned correctly #857

 o Make sure that locally installed header files are not used when
   building PROJ #849

 o Fix inconsistent parameter names in proj.h/proj_4D_api.c #842

 o Make sure +vunits is applied #833

 o Fix incorrect Web Mercator transformations #834

 THANKS TO
 ------------

 Version 5.0.1 is made possible by the following contributors:

   Mike Toews
   Kurt Schwehr
   Even Rouault
   Charles Karney
   Thomas Knudsen
   Kristian Evers


5.0.0 Release Notes
-------------------

This version of PROJ introduces some significant extensions and
improvements to (primarily) the geodetic functionality of the system.

The main driver for introducing the new features is the emergence of
dynamic reference frames, the increasing use of high accuracy GNSS,
and the related growing demand for accurate coordinate
transformations.  While older versions of PROJ included some geodetic
functionality, the new framework lays the foundation for turning PROJ
into a generic geospatial coordinate transformation engine.

The core of the library is still the well established projection code.
The new functionality is primarily exposed in a new programming
interface and a new command line utility, "cct" (for "Coordinate
Conversion and Transformation").  The old programming interface is
still available and can - to some extent - use the new geodetic
transformation features.

The internal architecture has also seen many changes and much
improvement.  So far, these improvements respect the existing
programming interface. But the process has revealed a need to simplify
and reduce the code base, in order to support sustained active
development.

!!!
!!! Therefore we have scheduled regular releases over the coming years
!!! which will gradually remove the old programming interface.
!!!
!!! This will cause breaking changes with the next two major version
!!! releases, which will affect all projects that depend on PROJ
!!! (cf. section "deprecations" below).
!!!

The decision to break the existing API has not been easy, but has
ultimately been deemed necessary to ensure the long term survival of
the project. Not only by improving the maintainability immensely, but
also by extending the potential user (and hence developer) community.

The end goal is to deliver a generic coordinate transformation
software package with a clean and concise code base appealing to
both users and developers.


VERSIONING AND NAMING
---------------------

For the first time in more than 25 years the major version number of
the software is changed. The decision to do this is based on the many
new features and new API. While backwards compatibility remains -
except in a few rare corner cases - the addition of a new and improved
programming interface warrants a new major release.

The new major version number unfortunately leaves the project in a bit
of a conundrum regarding the name. For the majority of the life-time
of the product it has been known as PROJ.4, but since we have now
reached version 5 the name is no longer aligned with the version
number.

Hence we have decided to decouple the name from the version number and
from this version and onwards the product will simply be called PROJ.

In recognition of the history of the software we are keeping PROJ.4 as
the *name of the organizing project*. The same project team also
produces the datum-grid package.

In summary:

o The PROJ.4 project provides the product PROJ, which is now at
  version 5.0.0.

o The foundational component of PROJ is the library libproj.

o Other PROJ components include the application proj, which provides
  a command line interface to libproj.

o The PROJ.4 project also distributes the datum-grid package,
  which at the time of writing is at version 1.6.0.


 UPDATES
 -------

 o Introduced new API in proj.h.
   - The new API is orthogonal to the existing proj_api.h API and the
     internally used projects.h API.
   - The new API adds the ability to transform spatiotemporal (4D)
     coordinates.
   - Functions in the new API use the "proj_" namespace.
   - Data types in the new API use the "PJ_" namespace, with a few
     historic exceptions such as XY, XYZ, LP and LPZ.

 o Introduced the concept of "transformation pipelines" that makes it
   possible to do complex geodetic transformations of spatiotemporal
   coordinates by daisy chaining simple coordinate operations.

 o Introduced cct, the Coordinate Conversion and Transformation
   application.

 o Introduced gie, the Geospatial Integrity Investigation Environment.
   - Selftest invoked by -C flag in proj has been removed
   - Ported approx. 1300 built-in selftests to gie format
   - Ported approx. 1000 tests from the gigs test framework
   - Added approx. 200 new tests

 o Adopted terminology from the OGC/ISO-19100 geospatial standards
   series. Key definitions are:
   - At the most generic level, a *coordinate operation* is a change
     of coordinates, based on a one-to-one relationship, from one
     coordinate reference system to another.
   - A *transformation* is a coordinate operation in which the two
     coordinate reference systems are based on different datums, e.g.
     a change from a global reference frame to a regional frame.
   - A *conversion* is a coordinate operation in which both
     coordinate reference systems are based on the same datum,
     e.g. change of units of coordinates.
   - A *projection* is a coordinate conversion from an ellipsoidal
     coordinate system to a plane. Although projections are simply
     conversions according to the standard, they are treated as
     separate entities in PROJ as they make up the vast majority
     of operations in the library.

 o New operations:
   - The pipeline operator (pipeline)
   - Transformations:
     + Helmert transform (helmert)
     + Horner real and complex polynomial evaluation (horner)
     + Horizontal gridshift (hgridshift)
     + Vertical gridshift (vgridshift)
     + Molodensky transform (molodensky)
     + Kinematic gridshift with deformation model (deformation)
   - Conversions:
     + Unit conversion (unitconvert)
     + Axis swap (axisswap)
   - Projections:
     + Central Conic projection (ccon)

 o Significant documentation updates, including
   - Overhaul of the structure of the documentation
   - A better introduction to the use of PROJ
   - A complete reference to the new proj.h API
   - a complete rewrite of the section on geodesic calculations
   - Figures for all projections

 o New "free format" option for operation definitions, which
   permits separating tokens by whitespace when specifying key/value-
   pairs, e.g. "proj = merc lat_0 = 45".

 o Added metadata to init-files that can be read with the
   proj_init_info() function in the new proj.h API.

 o Added ITRF2000, ITRF2008 and ITRF2014 init-files with ITRF
   transformation parameters, including plate motion model
   parameters.

 o Added ellipsoid parameters for GSK2011, PZ90 and "danish". The
   latter is similar to the already supported andrae ellipsoid,
   but has a slightly different semimajor axis.

 o Added Copenhagen prime meridian.

 o Updated EPSG database to version 9.2.0.

 o Geodesic library updated to version 1.49.2-c.

 o Support for analytical partial derivatives has been removed.

 o Improved performance in Winkel Tripel and Aitoff.

 o Introduced pj_has_inverse() function to proj_api.h. Checks if an
   operation has an inverse. Use this instead of checking whether
   P->inv exists, since that can no longer be relied on.

 o ABI version number updated to 13:0:0.

 o Removed support for Windows CE.

 o Removed the VB6 COM interface.

 BUG FIXES
 ------------

 All bug fix numbers refer to issues indexed at
 https://github.com/OSGeo/proj.4/issues/

 o Fixed incorrect convergence calculation in Lambert Conformal
   Conic. #16.

 o Handle ellipsoid parameters correctly when using +nadgrids=@null.
   #22.

 o Return correct latitude when using negative northings in
   Transverse Mercator (tmerc). #138.

 o Return correct result at origin in inverse Modified Stereographic
   of Alaska. #161.

 o Return correct result at origin in inverse Modified Stereographic
   of 48 U.S. #162.

 o Return correct result at origin in inverse Modified Stereographic
   of 50 U.S. #163.

 o Return correct result at origin in inverse Lee Oblated
   Stereographic. #164.

 o Return correct result at origin in inverse Miller Oblated
   Stereographic. #164.

 o Fixed scaling and wrap-around issues in Oblique Cylindrical
   Equal Area. #166.

 o Corrected a coefficient error in inverse Transverse Mercator. #174.

 o Respect -r flag when calling proj with -V. #184.

 o Remove multiplication by 2 at the equator error in Stereographic
   projection. #194.

 o Allow +alpha=0 and +gamma=0 when using Oblique Mercator. #195.

 o Return correct result of inverse Oblique Mercator when alpha is
   between 90 and 270. #331.

 o Avoid segmentation fault when accessing point outside grid. #369.

 o Avoid segmentation fault on NaN input in Robin inverse. #463.

 o Very verbose use of proj (-V) on Windows is fixed. #484.

 o Fixed memory leak in General Oblique Transformation. #497.

 o Equations for meridian convergence and partial derivatives have
   been corrected for non-conformal projections. #526.

 o Fixed scaling of cartesian coordinates in pj_transform(). #726.

 o Additional bug fixes courtesy of Google's OSS-Fuzz program:
   https://bugs.chromium.org/p/oss-fuzz/issues/list?can=1&q=proj4


 DEPRECATIONS
 ------------

 o The projects.h header and the functions related to it is
   considered deprecated from version 5.0.0 and onwards.


    !!!     PROJECTS.H WILL BE REMOVED FROM THE LIBRARY     !!!
    !!!                 WITH VERSION 6.0.0                  !!!

 o The nmake build system on Windows will not be supported from
   version 6.0.0 on onwards. Use CMake instead.

    !!! NMAKE BUILD SYSTEM WILL BE REMOVED FROM THE LIBRARY !!!
    !!!                 WITH VERSION 6.0.0                  !!!

 o The proj_api.h header and the functions related to it is
   consided deprecated from version 5.0.0 and onwards.

    !!!     PROJ_API.H WILL BE REMOVED FROM THE LIBRARY     !!!
    !!!                 WITH VERSION 7.0.0                  !!!


 THANKS TO
 ------------

 Version 5.0.0 is made possible by the following contributors:

    Lukasz Komsta
    Maxim Churilin
    edechaux
    dusanjovic
    Zoltan Siki
    Tom Fili
    Nicolas David
    Mike Toews
    Micah Cochran
    Luke Campbell
    Ilya Oshchepkov
    Adam Wulkiewicz
    Jonas Tittmann
    Mateusz Loskot
    Etienne Jacques
    Bas Couwenberg
    Elliott Sales de Andrade
    Charles Karney
    Aaron Puchert
    Julien Moquet
    Charles Karney
    Howard Butler
    Even Rouault
    Thomas Knudsen
    Kristian Evers


4.9.3 Release Notes
-------------------

 o UTM now uses etmerc, following NGA recommendations.  Tests adjusted
   for tiny changes in values.

 o new projections: Times, Natural Earth II, Compact Miller, Patterson
   Cylindrical, and inverse for Hammer and Eckert-Greifendorff.

 o runtime self tests are now opt-in instead of opt-out

 o math constants moved to projects.h

 o bugfixes

 o New (optional) runtime self tests added to proj

4.9.2 Release Notes
-------------------

 o proj_def.dat was missing from source distribution
   see https://github.com/OSGeo/proj.4/issues/274 for more detail

 o Update Geodesic library from GeographicLib

 o Remove setlocale() use in pj_init_ctx()

 o Renamed PVALUE in pj_param.c to prevent clash with Windows

4.9.1 Release Notes
-------------------

 o 4.9.0RC2 release was abandoned because it was not promoted in a
   timely fashion. Subsequent maintenance of tickets has continued,
   and a new 4.9.1 release was issued in its place.

 o Implement inverse solution for Winkel Tripel from Drazan Tutic #250

 o More CMake configuration tweaks. The CMake configuration is probably
   not at feature parity with the autotools builds at this point but it
   is converging #256

 o Tweak initialization ordering around setlocal which may have caused
   issues #237

 o Support out-of-tree autoconf builds more completely #247

 o Fix NaN handling by geod_inverse and geod_polygon_addedge #251 & #253

 o Update config.sub and config.guess #257

 o Adapt Charles Karney's CMake patches for smoother build #258

 o Define default PROJ_LIB location for CMake compilation #261

 o Fix Windows compilation on PJ_aitoff.c

 o Align CMake SOVERSION with autotools #263

 o Regenerate nad/epsg with GDAL r28536 to avoid precision loss in TOWGS84
   parameters, e.g. on Amersfoort / RD EPSG:4289 (#260)

 o Add CMake project-config.cmake scripts (#264 from Charles Karney)

 o Dial back test sensitivity #255

4.9.0 Release Notes
-------------------

 o Implement CMake as an option for building PROJ.4

 o Implement new virtual file api (projFileAPI) so that all access to grid
   shift and init files can be hooked.

 o Replace geodesic implementation with one from Charles Karney and add a
   supported public interface (geodesic.h).

 o Upgraded to EPSG 8.5.

 o Removed old (deprecated) Java bindings in favor of the new api introduced
   in 4.8.0.

 o Implement the calcofi (Cal Coop Ocean Fish Invest Lines/Stations) projection

 o Install projects.h again for applications that want access to internal
   structures and functions despite the inherent fragility.

 o Various bug fixes and cleanup.

 o Added the CalCOFI pseudo-projection, #135

4.8.0 Release Notes
-------------------

 o Added the Natural Earth projection.

 o Added HEALPIX, rHEALPIX and Icosahedral Snyder Equal Area projections.

 o nad2bin now produces "CTable2" format grid shift files by default which
   are platform independent.

 o nad2nad removed, use cs2cs for datum shift operations.

 o projects.h no longer installed as a public include file.  Please try to
   only use proj_api.h.

 o Add pj_get_spheroid_defn() accessor.

 o Added an alternate version of pj_init() that takes a projCtx (execution
   context) structure to address multithreading issues with error management
   and to provide a support for application hookable error reporting and
   logging.

 o Upgrade to EPSG 7.9.  Some changes in ideal datum selection.

 o JNI bindings reworked, org.proj4.Projections deprecated in favor of
   org.proj4.PJ.

 o Added preliminary vertical datum support.

 o Fix various multithreading issues, particular in datum grid handling code.

 o Added support for the +axis= option for alternate axis orientations as
   part of a coordinate system (used for TM South Orientated support).

 o +proj=omerc implementatioin replaced with code from libproj4.  +rot_conv
   flag no longer works, and some coordinate systems (ie. Malaysian) will
   need to use +gamma instead.  "epsg" init file updated accordingly.


4.7.0 Release Notes
-------------------

 o Added in memory caching of init file search results for substantial
   acceleration in some application environments (like MapServer).

 o Regenerated nad/epsg init file with EPSG 7.1 database, including new
   support for Google Mercator (EPSG:3857).

 o Various thread safety improvements, including preliminary support for
   a mutex lock around some operations, like grid loading.


4.6.1 Release Notes
-------------------

 o Upgraded to EPSG 6.17 version for nad/epsg.  Also corrected the precision
   problem introduced in the last version.

 o Added logic for tmerc projection to fail rather than return crazy results
   if more than 90 degrees away from the central meridian (#5).  This change
   may only be temporary till a more comprehensive solution is found.

 o Fixed handling of extra text in cs2cs.

 o Renamed INSTALL to INSTALL.TXT.

 o The eqc projection has been generalized to include a latitude of origin.

 o Added the glabsgm (Gauss Laborde / Sphere Geometric Mean) projection,
   and gstmerc variation.

 o nad/IGNF init catalogue created.

 o added the ntf_r93.gsb datum shift file.

 o Add /Op in nmake.opt compile options to avoid VC7 optimization bug (#12)

 o Fix testvarious test script so it works properly when grid files available



4.6.0 Release Notes
-------------------

 o MAJOR: Rework pj_transform() to avoid applying ellipsoid to ellipsoid
   transformations as a datum shift when no datum info is available.

 o Avoid applying "change of ellipsoid" during datum shifting based on
   ellipsoids getting changed to spheres for spherical projections.

 o Upgrade to EPSG 6.13

 o Added "900913" "Google Mercator" code in nad/esri.extra.

 o Avoid some static variable based multi-threading problems.

 o Improve error recovery if ctable style datum shift grid fails to load.

 o Support drive letters properly in PROJ_LIB paths for win32.

 o Fix occasional problem with DMS parsing.

 o Removed non-functional mpoly projection.

 o add lonlat, and latlon as aliases for longlat.


4.5.0 Release Notes
-------------------

 o Microsoft Visual Studio 8 compatibility changes.

 o Upgraded to EPSG 6.11.1 database.

 o Several bug fixes to pj_transform() to handle transient errors properly.

 o Fix Krovak projection (include +czech flag)

 o Added Roussilhe Stereographic projection from libproj4 (proj=rouss).

 o Added experimental +lon_wrap flag for alternate pj_transform() longitude
   wrapping behavior on output.


4.4.9 Release Notes
-------------------

 o Win32 build uses proj.dll by default now.

 o Added accessor functions for the datums, ellps, prime_meridians, units and
   main projection definition lists so they can be used on windows where
   data objects exported from the DLL don't work easily.

 o Added JNI (Java Native Interface) support within the jniwrap directory,
   and some supporting entry points directly in the proj dll/so.   See
   --with-jni configure switch, and jniwrap/README.

 o Added the proj=stereoa and proj=geos projections, incorporated from
   Gerald's libproj4.

 o A few other bug fixes.


4.4.8 Release Notes
-------------------

 o Updated epsg file to EPSG 6.5, added other.extra pseudo-EPSG WMS definitions

 o Made significant fixes to prime meridian support.

 o Substantially change pj_datum_transform(), so it and pj_transform() will
   work between coordinate systems with different ellipsoids but no datum
   shift information (assume +towgs84=0,0,0).

 o Added pj_get_release() function.

 o Ensure pj_transform() will try to transform all points in provided list if
   even some might transform properly.

 o Improved the accuracy of Geocentric_To_Geodetic() via an iterative
   solution.

 o Various other bug fixes.


4.4.7 Release Notes
-------------------

 o Added NTv2 (.gsb) grid shift file support.

 o Modified datum shift support so that data file data is only loaded if
   needed.  Also added 'null' grid as a fallback option, and support for
   making grids optional (prefix with '@' in +nadgrids).

 o Regenerated nad/epsg file with towgs84 parameters for non-greenwich prime
   meridians.

 o Updated nad/esri file with better generated form from Paul Ramsey.

 o Various bug fixes.


4.4.6 Release Notes
-------------------

 o Incorporated new lcca (Lambert Conformal Conic Alternate) projection from
   Gerald.

 o Updated 'espg' translation file for EPSG 6.2.2 with better support for
   prime meridians.

 o Added Prime Meridians via +pm command switch to cs2cs (and pj_transform).

 o Fixed bug with 7 parameter transforms.

 o Added 'esri' pseudo-epsg coordinate system file.

 o Cleanup so that PROJ.4 compiles clean as C++ code.

 o Added pj_get_def() to expand definitions of stuff like +init clauses.

 o Added a Krovak implementation (proj=krov).  Note this may change again
   in the next release.
