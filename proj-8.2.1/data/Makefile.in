# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
subdir = data
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_cflags_warn_all.m4 \
	$(top_srcdir)/m4/ax_check_compile_flag.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx_11.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/m4/pkg.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/src/proj_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
SOURCES =
DIST_SOURCES =
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
am__installdirs = "$(DESTDIR)$(pkgdatadir)"
DATA = $(pkgdata_DATA)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
am__DIST_COMMON = $(srcdir)/Makefile.in README
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CURL_CFLAGS = @CURL_CFLAGS@
CURL_ENABLED_FLAGS = @CURL_ENABLED_FLAGS@
CURL_LIBS = @CURL_LIBS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CXX_WFLAGS = @CXX_WFLAGS@
CYGPATH_W = @CYGPATH_W@
C_WFLAGS = @C_WFLAGS@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
EXTRA_LIBS = @EXTRA_LIBS@
FGREP = @FGREP@
FLTO_FLAG = @FLTO_FLAG@
GREP = @GREP@
GTEST_CFLAGS = @GTEST_CFLAGS@
GTEST_LIBS = @GTEST_LIBS@
HAVE_CXX11 = @HAVE_CXX11@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBCURL_CONFIG = @LIBCURL_CONFIG@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
MUTEX_SETTING = @MUTEX_SETTING@
NM = @NM@
NMEDIT = @NMEDIT@
NO_ZERO_AS_NULL_POINTER_CONSTANT_FLAG = @NO_ZERO_AS_NULL_POINTER_CONSTANT_FLAG@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKG_CONFIG = @PKG_CONFIG@
PROJ_LIB_ENV_VAR_TRIED_LAST_FLAGS = @PROJ_LIB_ENV_VAR_TRIED_LAST_FLAGS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SQLITE3_CFLAGS = @SQLITE3_CFLAGS@
SQLITE3_CHECK = @SQLITE3_CHECK@
SQLITE3_LIBS = @SQLITE3_LIBS@
STRIP = @STRIP@
THREAD_LIB = @THREAD_LIB@
TIFF_CFLAGS = @TIFF_CFLAGS@
TIFF_ENABLED_FLAGS = @TIFF_ENABLED_FLAGS@
TIFF_LIBS = @TIFF_LIBS@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
DATAPATH = $(top_srcdir)/data
pkgdata_DATA = proj.ini GL27 nad.lst nad27 nad83 world other.extra \
		CH \
		ITRF2000 ITRF2008 ITRF2014 proj.db \
		projjson.schema.json \
		deformation_model.schema.json \
		triangulation.schema.json

SQL_ORDERED_LIST = sql/begin.sql \
		sql/proj_db_table_defs.sql \
		sql/conversion_triggers.sql \
		sql/customizations_early.sql \
		sql/metadata.sql \
		sql/unit_of_measure.sql \
		sql/extent.sql \
		sql/scope.sql \
		sql/coordinate_system.sql \
		sql/axis.sql \
		sql/ellipsoid.sql \
		sql/prime_meridian.sql \
		sql/geodetic_datum.sql \
		sql/geodetic_datum_ensemble_member.sql \
		sql/vertical_datum.sql \
		sql/vertical_datum_ensemble_member.sql \
		sql/conversion.sql \
		sql/geodetic_crs.sql \
		sql/projected_crs.sql \
		sql/vertical_crs.sql \
		sql/compound_crs.sql \
		sql/helmert_transformation.sql \
		sql/grid_transformation.sql \
		sql/grid_transformation_custom.sql \
		sql/other_transformation.sql \
		sql/other_transformation_custom.sql \
		sql/concatenated_operation.sql \
		sql/concatenated_operation_step.sql \
		sql/alias_name.sql \
		sql/supersession.sql \
		sql/deprecation.sql \
		sql/esri.sql \
		sql/ignf.sql \
		sql/nkg.sql \
		sql/iau.sql \
		sql/grid_alternatives.sql \
		sql/grid_alternatives_generated_noaa.sql \
		sql/customizations.sql \
		sql/nkg_post_customizations.sql \
		sql/commit.sql

EXTRA_DIST = proj.ini GL27 nad.lst nad27 nad83 \
		world other.extra \
		CH \
		ITRF2000 ITRF2008 ITRF2014 \
		projjson.schema.json \
		deformation_model.schema.json \
		triangulation.schema.json \
		CMakeLists.txt \
		tests/test_nodata.gtx \
		tests/test_vgrid_bigendian_bigtiff.tif \
		tests/test_vgrid_bigendian.tif \
		tests/test_vgrid_bigtiff.tif \
		tests/test_vgrid_bottomup_with_matrix.tif \
		tests/test_vgrid_bottomup_with_scale.tif \
		tests/test_vgrid_deflate_floatingpointpredictor.tif \
		tests/test_vgrid_deflate.tif \
		tests/test_vgrid_float64.tif \
		tests/test_vgrid_in_second_channel.tif \
		tests/test_vgrid_int16.tif \
		tests/test_vgrid_int32.tif \
		tests/test_vgrid_uint32.tif \
		tests/test_vgrid_invalid_channel_type.tif \
		tests/test_vgrid_nodata.tif \
		tests/test_vgrid_pixelisarea.tif \
		tests/test_vgrid_pixelispoint.tif \
		tests/test_vgrid_uint16.tif \
		tests/test_vgrid_uint16_with_scale_offset.tif \
		tests/test_vgrid_unsupported_byte.tif \
		tests/test_vgrid_with_overview.tif \
		tests/test_vgrid_with_subgrid.tif \
		tests/test_hgrid.tif \
		tests/test_hgrid_separate.tif \
		tests/test_hgrid_tiled.tif \
		tests/test_hgrid_tiled_separate.tif \
		tests/test_hgrid_strip.tif \
		tests/test_hgrid_positive_west.tif \
		tests/test_hgrid_lon_shift_first.tif \
		tests/test_hgrid_radian.tif \
		tests/test_hgrid_degree.tif \
		tests/test_hgrid_with_overview.tif \
		tests/test_hgrid_extra_ifd_with_other_info.tif \
		tests/test_hgrid_with_subgrid.tif \
		tests/test_hgrid_with_subgrid_no_grid_name.tif \
		tests/subset_of_gr3df97a.tif \
		tests/egm96_15_uncompressed_truncated.tif \
		tests/test_vgrid_single_strip_truncated.tif \
		tests/nkgrf03vel_realigned_extract.tif \
		tests/nkgrf03vel_realigned_xy_extract.ct2 \
		tests/nkgrf03vel_realigned_z_extract.gtx \
		tests/test_hgrid_with_two_level_of_subgrids_no_grid_name.tif \
		tests/us_noaa_geoid06_ak_subset_at_antimeridian.tif \
		tests/test_hgrid_little_endian.gsb \
		tests/test_hgrid_big_endian.gsb \
		tests/test_3d_grid_projected.tif \
		tests/BETA2007.gsb \
		tests/MD \
		tests/alaska \
		tests/conus \
		tests/egm96_15_downsampled.gtx \
		tests/ntv1_can.dat \
		tests/ntv2_0_downsampled.gsb \
		tests/ntf_r93.gsb \
		tests/simple_model_degree_3d_grid.tif \
		tests/simple_model_degree_horizontal.json \
		tests/simple_model_degree_3d.json \
		tests/simple_model_metre_3d_grid.tif \
		tests/simple_model_metre_horizontal.json \
		tests/simple_model_metre_3d.json \
		tests/simple_model_metre_3d_geocentric.json \
		tests/simple_model_metre_vertical_grid.tif \
		tests/simple_model_metre_vertical.json \
		tests/simple_model_polar.json \
		tests/simple_model_polar.tif \
		tests/simple_model_wrap_east.json \
		tests/simple_model_wrap_east.tif \
		tests/simple_model_wrap_west.json \
		tests/simple_model_wrap_west.tif \
		tests/simple_model_projected.json \
		tests/tinshift_crs_implicit.json \
		tests/tinshift_simplified_kkj_etrs.json \
		tests/tinshift_simplified_n60_n2000.json \
		tests/tinshift_fallback_nearest_side.json \
		tests/tinshift_fallback_nearest_centroid.json \
		generate_proj_db.cmake sql_filelist.cmake \
		$(SQL_ORDERED_LIST)

all: all-am

.SUFFIXES:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu data/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu data/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-pkgdataDATA: $(pkgdata_DATA)
	@$(NORMAL_INSTALL)
	@list='$(pkgdata_DATA)'; test -n "$(pkgdatadir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(pkgdatadir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(pkgdatadir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_DATA) $$files '$(DESTDIR)$(pkgdatadir)'"; \
	  $(INSTALL_DATA) $$files "$(DESTDIR)$(pkgdatadir)" || exit $$?; \
	done

uninstall-pkgdataDATA:
	@$(NORMAL_UNINSTALL)
	@list='$(pkgdata_DATA)'; test -n "$(pkgdatadir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(pkgdatadir)'; $(am__uninstall_files_from_dir)
tags TAGS:

ctags CTAGS:

cscope cscopelist:


distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) check-local
check: check-am
all-am: Makefile $(DATA)
installdirs:
	for dir in "$(DESTDIR)$(pkgdatadir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-local mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-data-local install-pkgdataDATA

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-pkgdataDATA

.MAKE: check-am install-am install-strip

.PHONY: all all-am check check-am check-local clean clean-generic \
	clean-libtool clean-local cscopelist-am ctags-am distclean \
	distclean-generic distclean-libtool distdir dvi dvi-am html \
	html-am info info-am install install-am install-data \
	install-data-am install-data-local install-dvi install-dvi-am \
	install-exec install-exec-am install-html install-html-am \
	install-info install-info-am install-man install-pdf \
	install-pdf-am install-pkgdataDATA install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags-am uninstall uninstall-am uninstall-pkgdataDATA

.PRECIOUS: Makefile


install-data-local:
	$(mkinstalldirs) $(DESTDIR)$(pkgdatadir)
	@for gridfile in $(DATAPATH)/*.gsb $(DATAPATH)/*.gtx $(DATAPATH)/ntv1_can.dat dummy \
	                 $(DATAPATH)/alaska $(DATAPATH)/conus $(DATAPATH)/hawaii \
	                 $(DATAPATH)/prvi $(DATAPATH)/stgeorge $(DATAPATH)/stlrnc $(DATAPATH)/stpaul \
	                 $(DATAPATH)/FL $(DATAPATH)/MD $(DATAPATH)/TN $(DATAPATH)/WI $(DATAPATH)/WO; do \
	  if test "$$gridfile" != "dummy" -a -f "$$gridfile" ; then \
	    echo $(INSTALL_DATA) $$gridfile $(DESTDIR)$(pkgdatadir)/`basename $$gridfile`; \
	    $(INSTALL_DATA) $$gridfile $(DESTDIR)$(pkgdatadir)/`basename $$gridfile`; \
          fi; \
	done

proj.db: $(DATAPATH)/sql/*.sql
	@echo "Make proj.db"
	$(RM) proj.db
	@export SQL_EXPANDED_LIST=""; \
	 for x in $(SQL_ORDERED_LIST); do \
		 export SQL_EXPANDED_LIST="$${SQL_EXPANDED_LIST} $(DATAPATH)/$$x"; \
	 done; \
	 cat $${SQL_EXPANDED_LIST} | sed 's/$${PROJ_VERSION}/${PACKAGE_VERSION}/' > all.sql.in; \
	 if test "x$(PROJ_DB_CACHE_DIR)" != "x" -a -x "$$(command -v md5sum)" -a -f "$(PROJ_DB_CACHE_DIR)/proj.db" -a -f "$(PROJ_DB_CACHE_DIR)/proj.db.sql.md5" ; then \
	    cat all.sql.in | md5sum | diff - "$(PROJ_DB_CACHE_DIR)/proj.db.sql.md5" > /dev/null \
	    && (echo "Reusing cached proj.db"; cp "$(PROJ_DB_CACHE_DIR)/proj.db" proj.db); \
	 fi; \
	 if test ! -f proj.db ; then \
	   cat all.sql.in | sqlite3 proj.db; \
	 fi; \
	 if [ $$? -ne 0 ] ; then \
		echo "Build of proj.db failed"; \
		$(RM) proj.db; \
		exit 1; \
	 fi; \
	 if test "x$(PROJ_DB_CACHE_DIR)" != "x" -a -x "$$(command -v md5sum)" ; then \
		mkdir -p "$(PROJ_DB_CACHE_DIR)"; \
		cat all.sql.in | md5sum > "$(PROJ_DB_CACHE_DIR)/proj.db.sql.md5"; \
		cp proj.db "$(PROJ_DB_CACHE_DIR)"; \
	 fi; \
	 $(RM) all.sql.in

# For out-of-tree builds, link all file of the source data dir to the generated data
# Also link select resource files in a for_tests subdirectory so that we are not
# influenced by the presence of other grids

# egm96_15_downsampled.gtx created with
# gdal_translate proj-datumgrid/egm96_15.gtx egm96_15_downsampled.gtx -of GTX -outsize 25% 25% -r average

# ntv2_0_downsampled.gsb created with:
# gdal_translate NTv2:0:/home/<USER>/proj/proj-datumgrid/north-america/ntv2_0.gsb  /tmp/ntv2_0_downsampled.gsb -of NTv2 -outsize 10% 10%
# gdal_translate NTv2:1:/home/<USER>/proj/proj-datumgrid/north-america/ntv2_0.gsb  /tmp/ntv2_0_downsampled.gsb -of NTv2 -outsize 10% 10% -co append_subdataset=yes
# gdal_translate NTv2:2:/home/<USER>/proj/proj-datumgrid/north-america/ntv2_0.gsb  /tmp/ntv2_0_downsampled.gsb -of NTv2 -outsize 10% 10% -co append_subdataset=yes
# gdal_translate NTv2:3:/home/<USER>/proj/proj-datumgrid/north-america/ntv2_0.gsb  /tmp/ntv2_0_downsampled.gsb -of NTv2 -outsize 10% 10% -co append_subdataset=yes
# gdal_translate NTv2:99:/home/<USER>/proj/proj-datumgrid/north-america/ntv2_0.gsb  /tmp/ntv2_0_downsampled.gsb -of NTv2 -co append_subdataset=yes
# gdal_translate NTv2:44:/home/<USER>/proj/proj-datumgrid/north-america/ntv2_0.gsb  /tmp/ntv2_0_downsampled.gsb -of NTv2 -co append_subdataset=yes
# gdal_translate NTv2:4:/home/<USER>/proj/proj-datumgrid/north-america/ntv2_0.gsb  /tmp/ntv2_0_downsampled.gsb -of NTv2 -co append_subdataset=yes

check-local:
	@if [ ! -f GL27 ]; then \
		for x in $(DATAPATH)/*; do \
			ln -sf $$x .; \
		done \
	fi; \
	rm -rf for_tests; \
	mkdir for_tests; \
	for x in $(DATAPATH)/GL27 \
		 $(DATAPATH)/nad27 \
		 $(DATAPATH)/nad83 \
		 $(DATAPATH)/tests/ntv1_can.dat \
		 $(DATAPATH)/tests/MD \
		 $(DATAPATH)/tests/ntf_r93.gsb \
		 $(DATAPATH)/tests/conus \
		 $(DATAPATH)/tests/alaska \
		 $(DATAPATH)/ITRF2000 \
		 $(DATAPATH)/tests/BETA2007.gsb; \
	do \
		if test -f "$$x" ; then \
			ln -sf "../$$x" for_tests; \
		else \
			echo "ERROR: grid $$x missing: some tests will be skipped"; \
			exit 1; \
		fi \
	done; \
	ln -sf ../$(DATAPATH)/tests for_tests; \
	ln -sf ../$(DATAPATH)/tests/ntv2_0_downsampled.gsb for_tests/ntv2_0.gsb; \
	ln -sf ../$(DATAPATH)/tests/egm96_15_downsampled.gtx for_tests/egm96_15.gtx; \
	ln -sf ../$(DATAPATH)/proj.ini for_tests; \
	ln -sf ../proj.db for_tests

clean-local:
	$(RM) proj.db
	$(RM) -rf for_tests

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
