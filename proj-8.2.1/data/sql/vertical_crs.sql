--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "vertical_crs" VALUES('EPSG','3855','EGM2008 height',NULL,'EPSG','6499','EPSG','1027',0);
INSERT INTO "usage" VALUES('EPSG','2857','vertical_crs','EPSG','3855','EPSG','1262','EPSG','1027');
INSERT INTO "vertical_crs" VALUES('EPSG','3886','Fao 1979 height',NULL,'EPSG','6499','EPSG','1028',0);
INSERT INTO "usage" VALUES('EPSG','2872','vertical_crs','EPSG','3886','EPSG','3625','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','3900','N2000 height',NULL,'EPSG','6499','EPSG','1030',0);
INSERT INTO "usage" VALUES('EPSG','2880','vertical_crs','EPSG','3900','EPSG','3333','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','4440','NZVD2009 height',NULL,'EPSG','6499','EPSG','1039',0);
INSERT INTO "usage" VALUES('EPSG','3313','vertical_crs','EPSG','4440','EPSG','1175','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','4458','Dunedin-Bluff 1960 height',NULL,'EPSG','6499','EPSG','1040',0);
INSERT INTO "usage" VALUES('EPSG','3317','vertical_crs','EPSG','4458','EPSG','3806','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5193','Incheon height',NULL,'EPSG','6499','EPSG','1049',0);
INSERT INTO "usage" VALUES('EPSG','3864','vertical_crs','EPSG','5193','EPSG','3739','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5195','Trieste height',NULL,'EPSG','6499','EPSG','1050',0);
INSERT INTO "usage" VALUES('EPSG','3865','vertical_crs','EPSG','5195','EPSG','2370','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5214','Genoa 1942 height',NULL,'EPSG','6499','EPSG','1051',0);
INSERT INTO "usage" VALUES('EPSG','3866','vertical_crs','EPSG','5214','EPSG','3736','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5237','SLVD height',NULL,'EPSG','6499','EPSG','1054',0);
INSERT INTO "usage" VALUES('EPSG','3876','vertical_crs','EPSG','5237','EPSG','3310','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5317','FVR09 height',NULL,'EPSG','6499','EPSG','1059',0);
INSERT INTO "usage" VALUES('EPSG','3924','vertical_crs','EPSG','5317','EPSG','3248','EPSG','1029');
INSERT INTO "vertical_crs" VALUES('EPSG','5336','Black Sea depth',NULL,'EPSG','6498','EPSG','5134',0);
INSERT INTO "usage" VALUES('EPSG','3936','vertical_crs','EPSG','5336','EPSG','1102','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5597','FCSVR10 height',NULL,'EPSG','6499','EPSG','1079',0);
INSERT INTO "usage" VALUES('EPSG','4067','vertical_crs','EPSG','5597','EPSG','3890','EPSG','1139');
INSERT INTO "vertical_crs" VALUES('EPSG','5600','NGPF height',NULL,'EPSG','6499','EPSG','5195',0);
INSERT INTO "usage" VALUES('EPSG','4069','vertical_crs','EPSG','5600','EPSG','3134','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5601','IGN 1966 height',NULL,'EPSG','6499','EPSG','5196',0);
INSERT INTO "usage" VALUES('EPSG','4070','vertical_crs','EPSG','5601','EPSG','3124','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5602','Moorea SAU 1981 height',NULL,'EPSG','6499','EPSG','5197',0);
INSERT INTO "usage" VALUES('EPSG','4071','vertical_crs','EPSG','5602','EPSG','3125','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5603','Raiatea SAU 2001 height',NULL,'EPSG','6499','EPSG','5198',0);
INSERT INTO "usage" VALUES('EPSG','4072','vertical_crs','EPSG','5603','EPSG','3136','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5604','Maupiti SAU 2001 height',NULL,'EPSG','6499','EPSG','5199',0);
INSERT INTO "usage" VALUES('EPSG','4073','vertical_crs','EPSG','5604','EPSG','3126','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5605','Huahine SAU 2001 height',NULL,'EPSG','6499','EPSG','5200',0);
INSERT INTO "usage" VALUES('EPSG','4074','vertical_crs','EPSG','5605','EPSG','3135','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5606','Tahaa SAU 2001 height',NULL,'EPSG','6499','EPSG','5201',0);
INSERT INTO "usage" VALUES('EPSG','4075','vertical_crs','EPSG','5606','EPSG','3138','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5607','Bora Bora SAU 2001 height',NULL,'EPSG','6499','EPSG','5202',0);
INSERT INTO "usage" VALUES('EPSG','4076','vertical_crs','EPSG','5607','EPSG','3137','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5608','IGLD 1955 height',NULL,'EPSG','6499','EPSG','5204',0);
INSERT INTO "usage" VALUES('EPSG','4077','vertical_crs','EPSG','5608','EPSG','3468','EPSG','1202');
INSERT INTO "vertical_crs" VALUES('EPSG','5609','IGLD 1985 height',NULL,'EPSG','6499','EPSG','5205',0);
INSERT INTO "usage" VALUES('EPSG','4078','vertical_crs','EPSG','5609','EPSG','3468','EPSG','1202');
INSERT INTO "vertical_crs" VALUES('EPSG','5610','HVRS71 height',NULL,'EPSG','6499','EPSG','5207',0);
INSERT INTO "usage" VALUES('EPSG','4079','vertical_crs','EPSG','5610','EPSG','3234','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5611','Caspian height',NULL,'EPSG','6499','EPSG','5106',0);
INSERT INTO "usage" VALUES('EPSG','4080','vertical_crs','EPSG','5611','EPSG','1291','EPSG','1136');
INSERT INTO "vertical_crs" VALUES('EPSG','5612','Baltic 1977 depth',NULL,'EPSG','6498','EPSG','5105',0);
INSERT INTO "usage" VALUES('EPSG','4081','vertical_crs','EPSG','5612','EPSG','2423','EPSG','1136');
INSERT INTO "vertical_crs" VALUES('EPSG','5613','RH2000 height',NULL,'EPSG','6499','EPSG','5208',0);
INSERT INTO "usage" VALUES('EPSG','4082','vertical_crs','EPSG','5613','EPSG','3313','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5614','KOC WD depth (ft)',NULL,'EPSG','6495','EPSG','5187',0);
INSERT INTO "usage" VALUES('EPSG','4083','vertical_crs','EPSG','5614','EPSG','3267','EPSG','1205');
INSERT INTO "vertical_crs" VALUES('EPSG','5615','RH00 height',NULL,'EPSG','6499','EPSG','5209',0);
INSERT INTO "usage" VALUES('EPSG','4084','vertical_crs','EPSG','5615','EPSG','3313','EPSG','1029');
INSERT INTO "vertical_crs" VALUES('EPSG','5616','IGN 1988 LS height',NULL,'EPSG','6499','EPSG','5210',0);
INSERT INTO "usage" VALUES('EPSG','4085','vertical_crs','EPSG','5616','EPSG','2895','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5617','IGN 1988 MG height',NULL,'EPSG','6499','EPSG','5211',0);
INSERT INTO "usage" VALUES('EPSG','4086','vertical_crs','EPSG','5617','EPSG','2894','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5618','IGN 1992 LD height',NULL,'EPSG','6499','EPSG','5212',0);
INSERT INTO "usage" VALUES('EPSG','4087','vertical_crs','EPSG','5618','EPSG','2893','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5619','IGN 1988 SB height',NULL,'EPSG','6499','EPSG','5213',0);
INSERT INTO "usage" VALUES('EPSG','4088','vertical_crs','EPSG','5619','EPSG','2891','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5620','IGN 1988 SM height',NULL,'EPSG','6499','EPSG','5214',0);
INSERT INTO "usage" VALUES('EPSG','4089','vertical_crs','EPSG','5620','EPSG','2890','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5621','EVRF2007 height',NULL,'EPSG','6499','EPSG','5215',0);
INSERT INTO "usage" VALUES('EPSG','4090','vertical_crs','EPSG','5621','EPSG','3594','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5701','ODN height',NULL,'EPSG','6499','EPSG','5101',0);
INSERT INTO "usage" VALUES('EPSG','4144','vertical_crs','EPSG','5701','EPSG','2792','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5702','NGVD29 height (ftUS)',NULL,'EPSG','6497','EPSG','5102',0);
INSERT INTO "usage" VALUES('EPSG','4145','vertical_crs','EPSG','5702','EPSG','1323','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5703','NAVD88 height',NULL,'EPSG','6499','EPSG','5103',0);
INSERT INTO "usage" VALUES('EPSG','4146','vertical_crs','EPSG','5703','EPSG','4161','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5704','Yellow Sea',NULL,'EPSG','6499','EPSG','5104',1);
INSERT INTO "usage" VALUES('EPSG','4147','vertical_crs','EPSG','5704','EPSG','1067','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5705','Baltic 1977 height',NULL,'EPSG','6499','EPSG','5105',0);
INSERT INTO "usage" VALUES('EPSG','4148','vertical_crs','EPSG','5705','EPSG','2423','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5706','Caspian depth',NULL,'EPSG','6498','EPSG','5106',0);
INSERT INTO "usage" VALUES('EPSG','4149','vertical_crs','EPSG','5706','EPSG','1291','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5709','NAP height',NULL,'EPSG','6499','EPSG','5109',0);
INSERT INTO "usage" VALUES('EPSG','4152','vertical_crs','EPSG','5709','EPSG','1172','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5710','Ostend height',NULL,'EPSG','6499','EPSG','5110',0);
INSERT INTO "usage" VALUES('EPSG','4153','vertical_crs','EPSG','5710','EPSG','1347','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5711','AHD height',NULL,'EPSG','6499','EPSG','5111',0);
INSERT INTO "usage" VALUES('EPSG','14230','vertical_crs','EPSG','5711','EPSG','4493','EPSG','1263');
INSERT INTO "vertical_crs" VALUES('EPSG','5712','AHD (Tasmania) height',NULL,'EPSG','6499','EPSG','5112',0);
INSERT INTO "usage" VALUES('EPSG','4155','vertical_crs','EPSG','5712','EPSG','2947','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5713','CGVD28 height',NULL,'EPSG','6499','EPSG','5114',0);
INSERT INTO "usage" VALUES('EPSG','4156','vertical_crs','EPSG','5713','EPSG','1289','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5714','MSL height',NULL,'EPSG','6499','EPSG','5100',0);
INSERT INTO "usage" VALUES('EPSG','4157','vertical_crs','EPSG','5714','EPSG','1262','EPSG','1199');
INSERT INTO "vertical_crs" VALUES('EPSG','5715','MSL depth',NULL,'EPSG','6498','EPSG','5100',0);
INSERT INTO "usage" VALUES('EPSG','4158','vertical_crs','EPSG','5715','EPSG','1262','EPSG','1199');
INSERT INTO "vertical_crs" VALUES('EPSG','5716','Piraeus height',NULL,'EPSG','6499','EPSG','5115',0);
INSERT INTO "usage" VALUES('EPSG','4159','vertical_crs','EPSG','5716','EPSG','3254','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5717','N60 height',NULL,'EPSG','6499','EPSG','5116',0);
INSERT INTO "usage" VALUES('EPSG','4160','vertical_crs','EPSG','5717','EPSG','3333','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5718','RH70 height',NULL,'EPSG','6499','EPSG','5117',0);
INSERT INTO "usage" VALUES('EPSG','4161','vertical_crs','EPSG','5718','EPSG','3313','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5719','NGF Lallemand height',NULL,'EPSG','6499','EPSG','5118',0);
INSERT INTO "usage" VALUES('EPSG','4162','vertical_crs','EPSG','5719','EPSG','1326','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5720','NGF-IGN69 height',NULL,'EPSG','6499','EPSG','5119',0);
INSERT INTO "usage" VALUES('EPSG','4163','vertical_crs','EPSG','5720','EPSG','1326','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5721','NGF-IGN78 height',NULL,'EPSG','6499','EPSG','5120',0);
INSERT INTO "usage" VALUES('EPSG','4164','vertical_crs','EPSG','5721','EPSG','1327','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5722','Maputo height',NULL,'EPSG','6499','EPSG','5121',0);
INSERT INTO "usage" VALUES('EPSG','4165','vertical_crs','EPSG','5722','EPSG','3281','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5723','JSLD69 height',NULL,'EPSG','6499','EPSG','5122',0);
INSERT INTO "usage" VALUES('EPSG','4166','vertical_crs','EPSG','5723','EPSG','4166','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5724','PHD93 height',NULL,'EPSG','6499','EPSG','5123',0);
INSERT INTO "usage" VALUES('EPSG','4167','vertical_crs','EPSG','5724','EPSG','3288','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5725','Fahud HD height',NULL,'EPSG','6499','EPSG','5124',0);
INSERT INTO "usage" VALUES('EPSG','4168','vertical_crs','EPSG','5725','EPSG','4009','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5726','Ha Tien 1960 height',NULL,'EPSG','6499','EPSG','5125',0);
INSERT INTO "usage" VALUES('EPSG','4169','vertical_crs','EPSG','5726','EPSG','1302','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5727','Hon Dau 1992 height',NULL,'EPSG','6499','EPSG','5126',0);
INSERT INTO "usage" VALUES('EPSG','4170','vertical_crs','EPSG','5727','EPSG','4015','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5728','LN02 height',NULL,'EPSG','6499','EPSG','5127',0);
INSERT INTO "usage" VALUES('EPSG','4171','vertical_crs','EPSG','5728','EPSG','1286','EPSG','1142');
INSERT INTO "vertical_crs" VALUES('EPSG','5729','LHN95 height',NULL,'EPSG','6499','EPSG','5128',0);
INSERT INTO "usage" VALUES('EPSG','4172','vertical_crs','EPSG','5729','EPSG','1286','EPSG','1027');
INSERT INTO "vertical_crs" VALUES('EPSG','5730','EVRF2000 height',NULL,'EPSG','6499','EPSG','5129',0);
INSERT INTO "usage" VALUES('EPSG','4173','vertical_crs','EPSG','5730','EPSG','1299','EPSG','1161');
INSERT INTO "vertical_crs" VALUES('EPSG','5731','Malin Head height',NULL,'EPSG','6499','EPSG','5130',0);
INSERT INTO "usage" VALUES('EPSG','4174','vertical_crs','EPSG','5731','EPSG','1305','EPSG','1153');
INSERT INTO "vertical_crs" VALUES('EPSG','5732','Belfast height',NULL,'EPSG','6499','EPSG','5131',0);
INSERT INTO "usage" VALUES('EPSG','4175','vertical_crs','EPSG','5732','EPSG','2530','EPSG','1209');
INSERT INTO "vertical_crs" VALUES('EPSG','5733','DNN height',NULL,'EPSG','6499','EPSG','5132',0);
INSERT INTO "usage" VALUES('EPSG','4176','vertical_crs','EPSG','5733','EPSG','3237','EPSG','1142');
INSERT INTO "vertical_crs" VALUES('EPSG','5734','AIOC95 depth',NULL,'EPSG','6498','EPSG','5133',0);
INSERT INTO "usage" VALUES('EPSG','4177','vertical_crs','EPSG','5734','EPSG','2592','EPSG','1136');
INSERT INTO "vertical_crs" VALUES('EPSG','5735','Black Sea height',NULL,'EPSG','6499','EPSG','5134',0);
INSERT INTO "usage" VALUES('EPSG','4178','vertical_crs','EPSG','5735','EPSG','3251','EPSG','1153');
INSERT INTO "vertical_crs" VALUES('EPSG','5736','Yellow Sea 1956 height',NULL,'EPSG','6499','EPSG','5104',0);
INSERT INTO "usage" VALUES('EPSG','4179','vertical_crs','EPSG','5736','EPSG','3228','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5737','Yellow Sea 1985 height',NULL,'EPSG','6499','EPSG','5137',0);
INSERT INTO "usage" VALUES('EPSG','4180','vertical_crs','EPSG','5737','EPSG','3228','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5738','HKPD height',NULL,'EPSG','6499','EPSG','5135',0);
INSERT INTO "usage" VALUES('EPSG','4181','vertical_crs','EPSG','5738','EPSG','3334','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5739','HKCD depth',NULL,'EPSG','6498','EPSG','5136',0);
INSERT INTO "usage" VALUES('EPSG','4182','vertical_crs','EPSG','5739','EPSG','3335','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5740','ODN Orkney height',NULL,'EPSG','6499','EPSG','5138',0);
INSERT INTO "usage" VALUES('EPSG','4183','vertical_crs','EPSG','5740','EPSG','2793','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5741','Fair Isle height',NULL,'EPSG','6499','EPSG','5139',0);
INSERT INTO "usage" VALUES('EPSG','4184','vertical_crs','EPSG','5741','EPSG','2794','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5742','Lerwick height',NULL,'EPSG','6499','EPSG','5140',0);
INSERT INTO "usage" VALUES('EPSG','4185','vertical_crs','EPSG','5742','EPSG','2795','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5743','Foula height',NULL,'EPSG','6499','EPSG','5141',0);
INSERT INTO "usage" VALUES('EPSG','4186','vertical_crs','EPSG','5743','EPSG','2796','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5744','Sule Skerry height',NULL,'EPSG','6499','EPSG','5142',0);
INSERT INTO "usage" VALUES('EPSG','4187','vertical_crs','EPSG','5744','EPSG','2797','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5745','North Rona height',NULL,'EPSG','6499','EPSG','5143',0);
INSERT INTO "usage" VALUES('EPSG','4188','vertical_crs','EPSG','5745','EPSG','2798','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5746','Stornoway height',NULL,'EPSG','6499','EPSG','5144',0);
INSERT INTO "usage" VALUES('EPSG','4189','vertical_crs','EPSG','5746','EPSG','2799','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5747','St. Kilda height',NULL,'EPSG','6499','EPSG','5145',0);
INSERT INTO "usage" VALUES('EPSG','4190','vertical_crs','EPSG','5747','EPSG','2800','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5748','Flannan Isles height',NULL,'EPSG','6499','EPSG','5146',0);
INSERT INTO "usage" VALUES('EPSG','4191','vertical_crs','EPSG','5748','EPSG','2801','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5749','St. Marys height',NULL,'EPSG','6499','EPSG','5147',0);
INSERT INTO "usage" VALUES('EPSG','4192','vertical_crs','EPSG','5749','EPSG','2802','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5750','Douglas height',NULL,'EPSG','6499','EPSG','5148',0);
INSERT INTO "usage" VALUES('EPSG','4193','vertical_crs','EPSG','5750','EPSG','2803','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5751','Fao height',NULL,'EPSG','6499','EPSG','5149',0);
INSERT INTO "usage" VALUES('EPSG','4194','vertical_crs','EPSG','5751','EPSG','3390','EPSG','1136');
INSERT INTO "vertical_crs" VALUES('EPSG','5752','Bandar Abbas height',NULL,'EPSG','6499','EPSG','5150',0);
INSERT INTO "usage" VALUES('EPSG','4195','vertical_crs','EPSG','5752','EPSG','3336','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5753','NGNC69 height',NULL,'EPSG','6499','EPSG','5151',0);
INSERT INTO "usage" VALUES('EPSG','4196','vertical_crs','EPSG','5753','EPSG','2822','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5754','Poolbeg height (ft(Br36))',NULL,'EPSG','6496','EPSG','5152',0);
INSERT INTO "usage" VALUES('EPSG','4197','vertical_crs','EPSG','5754','EPSG','1305','EPSG','1153');
INSERT INTO "vertical_crs" VALUES('EPSG','5755','NGG1977 height',NULL,'EPSG','6499','EPSG','5153',0);
INSERT INTO "usage" VALUES('EPSG','4198','vertical_crs','EPSG','5755','EPSG','3146','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5756','Martinique 1987 height',NULL,'EPSG','6499','EPSG','5154',0);
INSERT INTO "usage" VALUES('EPSG','4199','vertical_crs','EPSG','5756','EPSG','3276','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5757','Guadeloupe 1988 height',NULL,'EPSG','6499','EPSG','5155',0);
INSERT INTO "usage" VALUES('EPSG','4200','vertical_crs','EPSG','5757','EPSG','2892','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5758','Reunion 1989 height',NULL,'EPSG','6499','EPSG','5156',0);
INSERT INTO "usage" VALUES('EPSG','4201','vertical_crs','EPSG','5758','EPSG','3337','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5759','Auckland 1946 height',NULL,'EPSG','6499','EPSG','5157',0);
INSERT INTO "usage" VALUES('EPSG','4202','vertical_crs','EPSG','5759','EPSG','3764','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5760','Bluff 1955 height',NULL,'EPSG','6499','EPSG','5158',0);
INSERT INTO "usage" VALUES('EPSG','4203','vertical_crs','EPSG','5760','EPSG','3801','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5761','Dunedin 1958 height',NULL,'EPSG','6499','EPSG','5159',0);
INSERT INTO "usage" VALUES('EPSG','4204','vertical_crs','EPSG','5761','EPSG','3803','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5762','Gisborne 1926 height',NULL,'EPSG','6499','EPSG','5160',0);
INSERT INTO "usage" VALUES('EPSG','4205','vertical_crs','EPSG','5762','EPSG','3771','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5763','Lyttelton 1937 height',NULL,'EPSG','6499','EPSG','5161',0);
INSERT INTO "usage" VALUES('EPSG','4206','vertical_crs','EPSG','5763','EPSG','3804','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5764','Moturiki 1953 height',NULL,'EPSG','6499','EPSG','5162',0);
INSERT INTO "usage" VALUES('EPSG','4207','vertical_crs','EPSG','5764','EPSG','3768','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5765','Napier 1962 height',NULL,'EPSG','6499','EPSG','5163',0);
INSERT INTO "usage" VALUES('EPSG','4208','vertical_crs','EPSG','5765','EPSG','3772','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5766','Nelson 1955 height',NULL,'EPSG','6499','EPSG','5164',0);
INSERT INTO "usage" VALUES('EPSG','4209','vertical_crs','EPSG','5766','EPSG','3802','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5767','One Tree Point 1964 height',NULL,'EPSG','6499','EPSG','5165',0);
INSERT INTO "usage" VALUES('EPSG','4210','vertical_crs','EPSG','5767','EPSG','3762','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5768','Tararu 1952 height',NULL,'EPSG','6499','EPSG','5166',0);
INSERT INTO "usage" VALUES('EPSG','4211','vertical_crs','EPSG','5768','EPSG','3818','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5769','Taranaki 1970 height',NULL,'EPSG','6499','EPSG','5167',0);
INSERT INTO "usage" VALUES('EPSG','4212','vertical_crs','EPSG','5769','EPSG','3769','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5770','Wellington 1953 height',NULL,'EPSG','6499','EPSG','5168',0);
INSERT INTO "usage" VALUES('EPSG','4213','vertical_crs','EPSG','5770','EPSG','3773','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5771','Chatham Island 1959 height',NULL,'EPSG','6499','EPSG','5169',0);
INSERT INTO "usage" VALUES('EPSG','4214','vertical_crs','EPSG','5771','EPSG','3894','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5772','Stewart Island 1977 height',NULL,'EPSG','6499','EPSG','5170',0);
INSERT INTO "usage" VALUES('EPSG','4215','vertical_crs','EPSG','5772','EPSG','3338','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5773','EGM96 height',NULL,'EPSG','6499','EPSG','5171',0);
INSERT INTO "usage" VALUES('EPSG','4216','vertical_crs','EPSG','5773','EPSG','1262','EPSG','1027');
INSERT INTO "vertical_crs" VALUES('EPSG','5774','NG-L height',NULL,'EPSG','6499','EPSG','5172',0);
INSERT INTO "usage" VALUES('EPSG','4217','vertical_crs','EPSG','5774','EPSG','1146','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5775','Antalya height',NULL,'EPSG','6499','EPSG','5173',0);
INSERT INTO "usage" VALUES('EPSG','4218','vertical_crs','EPSG','5775','EPSG','3322','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5776','NN54 height',NULL,'EPSG','6499','EPSG','5174',0);
INSERT INTO "usage" VALUES('EPSG','4219','vertical_crs','EPSG','5776','EPSG','1352','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5777','Durres height',NULL,'EPSG','6499','EPSG','5175',0);
INSERT INTO "usage" VALUES('EPSG','4220','vertical_crs','EPSG','5777','EPSG','3212','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5778','GHA height',NULL,'EPSG','6499','EPSG','5176',0);
INSERT INTO "usage" VALUES('EPSG','4221','vertical_crs','EPSG','5778','EPSG','1037','EPSG','1056');
INSERT INTO "vertical_crs" VALUES('EPSG','5779','SVS2000 height',NULL,'EPSG','6499','EPSG','5177',0);
INSERT INTO "usage" VALUES('EPSG','4222','vertical_crs','EPSG','5779','EPSG','3307','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5780','Cascais height',NULL,'EPSG','6499','EPSG','5178',0);
INSERT INTO "usage" VALUES('EPSG','4223','vertical_crs','EPSG','5780','EPSG','1294','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5781','Constanta height',NULL,'EPSG','6499','EPSG','5179',0);
INSERT INTO "usage" VALUES('EPSG','4224','vertical_crs','EPSG','5781','EPSG','3295','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5782','Alicante height',NULL,'EPSG','6499','EPSG','5180',0);
INSERT INTO "usage" VALUES('EPSG','4225','vertical_crs','EPSG','5782','EPSG','4188','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5783','DHHN92 height',NULL,'EPSG','6499','EPSG','5181',0);
INSERT INTO "usage" VALUES('EPSG','4226','vertical_crs','EPSG','5783','EPSG','3339','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5784','DHHN85 height',NULL,'EPSG','6499','EPSG','5182',0);
INSERT INTO "usage" VALUES('EPSG','4227','vertical_crs','EPSG','5784','EPSG','2326','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5785','SNN76 height',NULL,'EPSG','6499','EPSG','5183',0);
INSERT INTO "usage" VALUES('EPSG','4228','vertical_crs','EPSG','5785','EPSG','1343','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5786','Baltic 1982 height',NULL,'EPSG','6499','EPSG','5184',0);
INSERT INTO "usage" VALUES('EPSG','4229','vertical_crs','EPSG','5786','EPSG','3224','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5787','EOMA 1980 height',NULL,'EPSG','6499','EPSG','5185',0);
INSERT INTO "usage" VALUES('EPSG','4230','vertical_crs','EPSG','5787','EPSG','1119','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5788','Kuwait PWD height',NULL,'EPSG','6499','EPSG','5186',0);
INSERT INTO "usage" VALUES('EPSG','4231','vertical_crs','EPSG','5788','EPSG','3267','EPSG','1248');
INSERT INTO "vertical_crs" VALUES('EPSG','5789','KOC WD depth',NULL,'EPSG','6498','EPSG','5187',0);
INSERT INTO "usage" VALUES('EPSG','4232','vertical_crs','EPSG','5789','EPSG','3267','EPSG','1205');
INSERT INTO "vertical_crs" VALUES('EPSG','5790','KOC CD height',NULL,'EPSG','6499','EPSG','5188',0);
INSERT INTO "usage" VALUES('EPSG','4233','vertical_crs','EPSG','5790','EPSG','3267','EPSG','1206');
INSERT INTO "vertical_crs" VALUES('EPSG','5791','NGC 1948 height',NULL,'EPSG','6499','EPSG','5189',0);
INSERT INTO "usage" VALUES('EPSG','4234','vertical_crs','EPSG','5791','EPSG','1327','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5792','Danger 1950 height',NULL,'EPSG','6499','EPSG','5190',0);
INSERT INTO "usage" VALUES('EPSG','4235','vertical_crs','EPSG','5792','EPSG','3299','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5793','Mayotte 1950 height',NULL,'EPSG','6499','EPSG','5191',0);
INSERT INTO "usage" VALUES('EPSG','4236','vertical_crs','EPSG','5793','EPSG','3340','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','5794','Martinique 1955 height',NULL,'EPSG','6499','EPSG','5192',0);
INSERT INTO "usage" VALUES('EPSG','4237','vertical_crs','EPSG','5794','EPSG','3276','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5795','Guadeloupe 1951 height',NULL,'EPSG','6499','EPSG','5193',0);
INSERT INTO "usage" VALUES('EPSG','4238','vertical_crs','EPSG','5795','EPSG','2892','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5796','Lagos 1955 height',NULL,'EPSG','6499','EPSG','5194',0);
INSERT INTO "usage" VALUES('EPSG','4239','vertical_crs','EPSG','5796','EPSG','3287','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','5797','AIOC95 height',NULL,'EPSG','6499','EPSG','5133',0);
INSERT INTO "usage" VALUES('EPSG','4240','vertical_crs','EPSG','5797','EPSG','2592','EPSG','1136');
INSERT INTO "vertical_crs" VALUES('EPSG','5798','EGM84 height',NULL,'EPSG','6499','EPSG','5203',0);
INSERT INTO "usage" VALUES('EPSG','4241','vertical_crs','EPSG','5798','EPSG','1262','EPSG','1027');
INSERT INTO "vertical_crs" VALUES('EPSG','5799','DVR90 height',NULL,'EPSG','6499','EPSG','5206',0);
INSERT INTO "usage" VALUES('EPSG','4242','vertical_crs','EPSG','5799','EPSG','3237','EPSG','1142');
INSERT INTO "vertical_crs" VALUES('EPSG','5829','Instantaneous Water Level height',NULL,'EPSG','6499','EPSG','5113',0);
INSERT INTO "usage" VALUES('EPSG','4267','vertical_crs','EPSG','5829','EPSG','1262','EPSG','1200');
INSERT INTO "vertical_crs" VALUES('EPSG','5831','Instantaneous Water Level depth',NULL,'EPSG','6498','EPSG','5113',0);
INSERT INTO "usage" VALUES('EPSG','4269','vertical_crs','EPSG','5831','EPSG','1262','EPSG','1200');
INSERT INTO "vertical_crs" VALUES('EPSG','5843','Ras Ghumays height',NULL,'EPSG','6499','EPSG','1146',0);
INSERT INTO "usage" VALUES('EPSG','4278','vertical_crs','EPSG','5843','EPSG','4225','EPSG','1142');
INSERT INTO "vertical_crs" VALUES('EPSG','5861','LAT depth',NULL,'EPSG','6498','EPSG','1080',0);
INSERT INTO "usage" VALUES('EPSG','4295','vertical_crs','EPSG','5861','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5862','LLWLT depth',NULL,'EPSG','6498','EPSG','1083',0);
INSERT INTO "usage" VALUES('EPSG','4296','vertical_crs','EPSG','5862','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5863','ISLW depth',NULL,'EPSG','6498','EPSG','1085',0);
INSERT INTO "usage" VALUES('EPSG','4297','vertical_crs','EPSG','5863','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5864','MLLWS depth',NULL,'EPSG','6498','EPSG','1086',0);
INSERT INTO "usage" VALUES('EPSG','4298','vertical_crs','EPSG','5864','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5865','MLWS depth',NULL,'EPSG','6498','EPSG','1087',0);
INSERT INTO "usage" VALUES('EPSG','4299','vertical_crs','EPSG','5865','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5866','MLLW depth',NULL,'EPSG','6498','EPSG','1089',0);
INSERT INTO "usage" VALUES('EPSG','4300','vertical_crs','EPSG','5866','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5867','MLW depth',NULL,'EPSG','6498','EPSG','1091',0);
INSERT INTO "usage" VALUES('EPSG','4301','vertical_crs','EPSG','5867','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5868','MHW height',NULL,'EPSG','6499','EPSG','1092',0);
INSERT INTO "usage" VALUES('EPSG','4302','vertical_crs','EPSG','5868','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5869','MHHW height',NULL,'EPSG','6499','EPSG','1090',0);
INSERT INTO "usage" VALUES('EPSG','4303','vertical_crs','EPSG','5869','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5870','MHWS height',NULL,'EPSG','6499','EPSG','1088',0);
INSERT INTO "usage" VALUES('EPSG','4304','vertical_crs','EPSG','5870','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5871','HHWLT height',NULL,'EPSG','6499','EPSG','1084',0);
INSERT INTO "usage" VALUES('EPSG','4305','vertical_crs','EPSG','5871','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5872','HAT height',NULL,'EPSG','6499','EPSG','1082',0);
INSERT INTO "usage" VALUES('EPSG','4306','vertical_crs','EPSG','5872','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5873','Low Water depth',NULL,'EPSG','6498','EPSG','1093',0);
INSERT INTO "usage" VALUES('EPSG','4307','vertical_crs','EPSG','5873','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5874','High Water height',NULL,'EPSG','6499','EPSG','1094',0);
INSERT INTO "usage" VALUES('EPSG','4308','vertical_crs','EPSG','5874','EPSG','1262','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','5941','NN2000 height',NULL,'EPSG','6499','EPSG','1096',0);
INSERT INTO "usage" VALUES('EPSG','4345','vertical_crs','EPSG','5941','EPSG','1352','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6130','GCVD54 height (ft)',NULL,'EPSG','1030','EPSG','1097',0);
INSERT INTO "usage" VALUES('EPSG','4457','vertical_crs','EPSG','6130','EPSG','3185','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6131','LCVD61 height (ft)',NULL,'EPSG','1030','EPSG','1098',0);
INSERT INTO "usage" VALUES('EPSG','4458','vertical_crs','EPSG','6131','EPSG','4121','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6132','CBVD61 height (ft)',NULL,'EPSG','1030','EPSG','1099',0);
INSERT INTO "usage" VALUES('EPSG','4459','vertical_crs','EPSG','6132','EPSG','3207','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6178','Cais da Pontinha - Funchal height',NULL,'EPSG','6499','EPSG','1101',0);
INSERT INTO "usage" VALUES('EPSG','4497','vertical_crs','EPSG','6178','EPSG','4125','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6179','Cais da Vila - Porto Santo height',NULL,'EPSG','6499','EPSG','1102',0);
INSERT INTO "usage" VALUES('EPSG','4498','vertical_crs','EPSG','6179','EPSG','3680','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6180','Cais das Velas height',NULL,'EPSG','6499','EPSG','1103',0);
INSERT INTO "usage" VALUES('EPSG','4499','vertical_crs','EPSG','6180','EPSG','2875','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6181','Horta height',NULL,'EPSG','6499','EPSG','1104',0);
INSERT INTO "usage" VALUES('EPSG','4500','vertical_crs','EPSG','6181','EPSG','2873','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6182','Cais da Madalena height',NULL,'EPSG','6499','EPSG','1105',0);
INSERT INTO "usage" VALUES('EPSG','4501','vertical_crs','EPSG','6182','EPSG','2874','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6183','Santa Cruz da Graciosa height',NULL,'EPSG','6499','EPSG','1106',0);
INSERT INTO "usage" VALUES('EPSG','4502','vertical_crs','EPSG','6183','EPSG','3681','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6184','Cais da Figueirinha - Angra do Heroismo height',NULL,'EPSG','6499','EPSG','1107',0);
INSERT INTO "usage" VALUES('EPSG','4503','vertical_crs','EPSG','6184','EPSG','2872','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6185','Santa Cruz das Flores height',NULL,'EPSG','6499','EPSG','1108',0);
INSERT INTO "usage" VALUES('EPSG','4504','vertical_crs','EPSG','6185','EPSG','1344','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6186','Cais da Vila do Porto height',NULL,'EPSG','6499','EPSG','1109',0);
INSERT INTO "usage" VALUES('EPSG','4505','vertical_crs','EPSG','6186','EPSG','4126','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6187','Ponta Delgada height',NULL,'EPSG','6499','EPSG','1110',0);
INSERT INTO "usage" VALUES('EPSG','4506','vertical_crs','EPSG','6187','EPSG','2871','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6357','NAVD88 depth',NULL,'EPSG','6498','EPSG','5103',0);
INSERT INTO "usage" VALUES('EPSG','4591','vertical_crs','EPSG','6357','EPSG','4161','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6358','NAVD88 depth (ftUS)',NULL,'EPSG','1043','EPSG','5103',0);
INSERT INTO "usage" VALUES('EPSG','4592','vertical_crs','EPSG','6358','EPSG','3664','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6359','NGVD29 depth (ftUS)',NULL,'EPSG','1043','EPSG','5102',0);
INSERT INTO "usage" VALUES('EPSG','4593','vertical_crs','EPSG','6359','EPSG','1323','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6360','NAVD88 height (ftUS)',NULL,'EPSG','6497','EPSG','5103',0);
INSERT INTO "usage" VALUES('EPSG','4594','vertical_crs','EPSG','6360','EPSG','3664','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6638','Tutuila 1962 height',NULL,'EPSG','6499','EPSG','1121',0);
INSERT INTO "usage" VALUES('EPSG','4859','vertical_crs','EPSG','6638','EPSG','2288','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6639','Guam 1963 height',NULL,'EPSG','6499','EPSG','1122',0);
INSERT INTO "usage" VALUES('EPSG','4860','vertical_crs','EPSG','6639','EPSG','3255','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6640','NMVD03 height',NULL,'EPSG','6499','EPSG','1119',0);
INSERT INTO "usage" VALUES('EPSG','4861','vertical_crs','EPSG','6640','EPSG','4171','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6641','PRVD02 height',NULL,'EPSG','6499','EPSG','1123',0);
INSERT INTO "usage" VALUES('EPSG','4862','vertical_crs','EPSG','6641','EPSG','3294','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6642','VIVD09 height',NULL,'EPSG','6499','EPSG','1124',0);
INSERT INTO "usage" VALUES('EPSG','4863','vertical_crs','EPSG','6642','EPSG','3330','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6643','ASVD02 height',NULL,'EPSG','6499','EPSG','1125',0);
INSERT INTO "usage" VALUES('EPSG','4864','vertical_crs','EPSG','6643','EPSG','2288','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6644','GUVD04 height',NULL,'EPSG','6499','EPSG','1126',0);
INSERT INTO "usage" VALUES('EPSG','4865','vertical_crs','EPSG','6644','EPSG','3255','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6647','CGVD2013(CGG2013) height',NULL,'EPSG','6499','EPSG','1127',0);
INSERT INTO "usage" VALUES('EPSG','4867','vertical_crs','EPSG','6647','EPSG','1061','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','6693','JSLD72 height',NULL,'EPSG','6499','EPSG','1129',0);
INSERT INTO "usage" VALUES('EPSG','4912','vertical_crs','EPSG','6693','EPSG','4168','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6694','JGD2000 (vertical) height',NULL,'EPSG','6499','EPSG','1130',0);
INSERT INTO "usage" VALUES('EPSG','4913','vertical_crs','EPSG','6694','EPSG','3263','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6695','JGD2011 (vertical) height',NULL,'EPSG','6499','EPSG','1131',0);
INSERT INTO "usage" VALUES('EPSG','4914','vertical_crs','EPSG','6695','EPSG','3263','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','6916','SHD height',NULL,'EPSG','6499','EPSG','1140',0);
INSERT INTO "usage" VALUES('EPSG','5039','vertical_crs','EPSG','6916','EPSG','1210','EPSG','1144');
INSERT INTO "vertical_crs" VALUES('EPSG','7446','Famagusta 1960 height',NULL,'EPSG','6499','EPSG','1148',0);
INSERT INTO "usage" VALUES('EPSG','5289','vertical_crs','EPSG','7446','EPSG','3236','EPSG','1142');
INSERT INTO "vertical_crs" VALUES('EPSG','7447','PNG08 height',NULL,'EPSG','6499','EPSG','1149',0);
INSERT INTO "usage" VALUES('EPSG','5290','vertical_crs','EPSG','7447','EPSG','4384','EPSG','1027');
INSERT INTO "vertical_crs" VALUES('EPSG','7651','Kumul 34 height',NULL,'EPSG','6499','EPSG','1150',0);
INSERT INTO "usage" VALUES('EPSG','5409','vertical_crs','EPSG','7651','EPSG','4013','EPSG','1029');
INSERT INTO "vertical_crs" VALUES('EPSG','7652','Kiunga height',NULL,'EPSG','6499','EPSG','1151',0);
INSERT INTO "usage" VALUES('EPSG','5410','vertical_crs','EPSG','7652','EPSG','4383','EPSG','1029');
INSERT INTO "vertical_crs" VALUES('EPSG','7699','DHHN12 height',NULL,'EPSG','6499','EPSG','1161',0);
INSERT INTO "usage" VALUES('EPSG','5436','vertical_crs','EPSG','7699','EPSG','3339','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','7700','Latvia 2000 height',NULL,'EPSG','6499','EPSG','1162',0);
INSERT INTO "usage" VALUES('EPSG','5437','vertical_crs','EPSG','7700','EPSG','3268','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','7707','ODN (Offshore) height',NULL,'EPSG','6499','EPSG','1164',0);
INSERT INTO "usage" VALUES('EPSG','5439','vertical_crs','EPSG','7707','EPSG','4391','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','7832','POM96 height',NULL,'EPSG','6499','EPSG','1171',0);
INSERT INTO "usage" VALUES('EPSG','5497','vertical_crs','EPSG','7832','EPSG','4425','EPSG','1029');
INSERT INTO "vertical_crs" VALUES('EPSG','7837','DHHN2016 height',NULL,'EPSG','6499','EPSG','1170',0);
INSERT INTO "usage" VALUES('EPSG','5498','vertical_crs','EPSG','7837','EPSG','3339','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','7839','NZVD2016 height',NULL,'EPSG','6499','EPSG','1169',0);
INSERT INTO "usage" VALUES('EPSG','5499','vertical_crs','EPSG','7839','EPSG','1175','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','7841','POM08 height',NULL,'EPSG','6499','EPSG','1172',0);
INSERT INTO "usage" VALUES('EPSG','5500','vertical_crs','EPSG','7841','EPSG','4425','EPSG','1029');
INSERT INTO "vertical_crs" VALUES('EPSG','7888','Jamestown 1971 height',NULL,'EPSG','6499','EPSG','1175',0);
INSERT INTO "usage" VALUES('EPSG','5530','vertical_crs','EPSG','7888','EPSG','3183','EPSG','1153');
INSERT INTO "vertical_crs" VALUES('EPSG','7889','St. Helena Tritan 2011 height',NULL,'EPSG','6499','EPSG','1176',0);
INSERT INTO "usage" VALUES('EPSG','5531','vertical_crs','EPSG','7889','EPSG','3183','EPSG','1145');
INSERT INTO "vertical_crs" VALUES('EPSG','7890','SHVD2015 height',NULL,'EPSG','6499','EPSG','1177',0);
INSERT INTO "usage" VALUES('EPSG','5532','vertical_crs','EPSG','7890','EPSG','3183','EPSG','1027');
INSERT INTO "vertical_crs" VALUES('EPSG','7962','Poolbeg height (m)',NULL,'EPSG','6499','EPSG','5152',0);
INSERT INTO "usage" VALUES('EPSG','5568','vertical_crs','EPSG','7962','EPSG','1305','EPSG','1203');
INSERT INTO "vertical_crs" VALUES('EPSG','7968','NGVD29 height (m)',NULL,'EPSG','6499','EPSG','5102',0);
INSERT INTO "usage" VALUES('EPSG','5569','vertical_crs','EPSG','7968','EPSG','1323','EPSG','1203');
INSERT INTO "vertical_crs" VALUES('EPSG','7976','HKPD depth',NULL,'EPSG','6498','EPSG','5135',0);
INSERT INTO "usage" VALUES('EPSG','5570','vertical_crs','EPSG','7976','EPSG','3334','EPSG','1203');
INSERT INTO "vertical_crs" VALUES('EPSG','7979','KOC WD height',NULL,'EPSG','6499','EPSG','5187',0);
INSERT INTO "usage" VALUES('EPSG','5571','vertical_crs','EPSG','7979','EPSG','3267','EPSG','1205');
INSERT INTO "vertical_crs" VALUES('EPSG','8050','MSL height (ft)',NULL,'EPSG','1030','EPSG','5100',0);
INSERT INTO "usage" VALUES('EPSG','5600','vertical_crs','EPSG','8050','EPSG','1262','EPSG','1199');
INSERT INTO "vertical_crs" VALUES('EPSG','8051','MSL depth (ft)',NULL,'EPSG','6495','EPSG','5100',0);
INSERT INTO "usage" VALUES('EPSG','5601','vertical_crs','EPSG','8051','EPSG','1262','EPSG','1199');
INSERT INTO "vertical_crs" VALUES('EPSG','8052','MSL height (ftUS)',NULL,'EPSG','6497','EPSG','5100',0);
INSERT INTO "usage" VALUES('EPSG','5602','vertical_crs','EPSG','8052','EPSG','1245','EPSG','1199');
INSERT INTO "vertical_crs" VALUES('EPSG','8053','MSL depth (ftUS)',NULL,'EPSG','1043','EPSG','5100',0);
INSERT INTO "usage" VALUES('EPSG','5603','vertical_crs','EPSG','8053','EPSG','1245','EPSG','1199');
INSERT INTO "vertical_crs" VALUES('EPSG','8089','ISH2004 height',NULL,'EPSG','6499','EPSG','1190',0);
INSERT INTO "usage" VALUES('EPSG','5616','vertical_crs','EPSG','8089','EPSG','3262','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','8228','NAVD88 height (ft)',NULL,'EPSG','1030','EPSG','5103',0);
INSERT INTO "usage" VALUES('EPSG','5736','vertical_crs','EPSG','8228','EPSG','4464','EPSG','1144');
INSERT INTO "vertical_crs" VALUES('EPSG','8266','GVR2000 height',NULL,'EPSG','6499','EPSG','1199',0);
INSERT INTO "usage" VALUES('EPSG','5758','vertical_crs','EPSG','8266','EPSG','4461','EPSG','1153');
INSERT INTO "vertical_crs" VALUES('EPSG','8267','GVR2016 height',NULL,'EPSG','6499','EPSG','1200',0);
INSERT INTO "usage" VALUES('EPSG','5759','vertical_crs','EPSG','8267','EPSG','4454','EPSG','1153');
INSERT INTO "vertical_crs" VALUES('EPSG','8357','Baltic 1957 height',NULL,'EPSG','6499','EPSG','1202',0);
INSERT INTO "usage" VALUES('EPSG','5803','vertical_crs','EPSG','8357','EPSG','1306','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','8358','Baltic 1957 depth',NULL,'EPSG','6498','EPSG','1202',0);
INSERT INTO "usage" VALUES('EPSG','5804','vertical_crs','EPSG','8358','EPSG','1306','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','8378','EPSG example wellbore local vertical CRS',NULL,'EPSG','1049','EPSG','1205',0);
INSERT INTO "usage" VALUES('EPSG','5808','vertical_crs','EPSG','8378','EPSG','4393','EPSG','1226');
INSERT INTO "vertical_crs" VALUES('EPSG','8434','Macao height',NULL,'EPSG','6499','EPSG','1210',0);
INSERT INTO "usage" VALUES('EPSG','5831','vertical_crs','EPSG','8434','EPSG','1147','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','8675','N43 height',NULL,'EPSG','6499','EPSG','1213',0);
INSERT INTO "usage" VALUES('EPSG','5861','vertical_crs','EPSG','8675','EPSG','4522','EPSG','1179');
INSERT INTO "vertical_crs" VALUES('EPSG','8690','SVS2010 height',NULL,'EPSG','6499','EPSG','1215',0);
INSERT INTO "usage" VALUES('EPSG','5871','vertical_crs','EPSG','8690','EPSG','3307','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','8691','SRB_VRS12 height',NULL,'EPSG','6499','EPSG','1216',0);
INSERT INTO "usage" VALUES('EPSG','5872','vertical_crs','EPSG','8691','EPSG','4543','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','8841','MVGC height',NULL,'EPSG','6499','EPSG','1219',0);
INSERT INTO "usage" VALUES('EPSG','6006','vertical_crs','EPSG','8841','EPSG','3303','EPSG','1181');
INSERT INTO "vertical_crs" VALUES('EPSG','8881','Vienna height',NULL,'EPSG','6499','EPSG','1267',0);
INSERT INTO "usage" VALUES('EPSG','13973','vertical_crs','EPSG','8881','EPSG','4585','EPSG','1248');
INSERT INTO "vertical_crs" VALUES('EPSG','8897','EPSG example wellbore local vertical CRS (ft)',NULL,'EPSG','1050','EPSG','1205',0);
INSERT INTO "usage" VALUES('EPSG','6013','vertical_crs','EPSG','8897','EPSG','4393','EPSG','1226');
INSERT INTO "vertical_crs" VALUES('EPSG','8904','TWVD 2001 height',NULL,'EPSG','6499','EPSG','1224',0);
INSERT INTO "usage" VALUES('EPSG','6020','vertical_crs','EPSG','8904','EPSG','3982','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','8911','DACR52 height',NULL,'EPSG','6499','EPSG','1226',0);
INSERT INTO "usage" VALUES('EPSG','6027','vertical_crs','EPSG','8911','EPSG','3232','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9130','IGN 2008 LD height',NULL,'EPSG','6499','EPSG','1250',0);
INSERT INTO "usage" VALUES('EPSG','6138','vertical_crs','EPSG','9130','EPSG','2893','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9245','CGVD2013(CGG2013a) height',NULL,'EPSG','6499','EPSG','1256',0);
INSERT INTO "usage" VALUES('EPSG','13877','vertical_crs','EPSG','9245','EPSG','1061','EPSG','1180');
INSERT INTO "vertical_crs" VALUES('EPSG','9255','SRVN16 height',NULL,'EPSG','6499','EPSG','1260',0);
INSERT INTO "usage" VALUES('EPSG','13908','vertical_crs','EPSG','9255','EPSG','4573','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9274','EVRF2000 Austria height',NULL,'EPSG','6499','EPSG','1261',0);
INSERT INTO "usage" VALUES('EPSG','13915','vertical_crs','EPSG','9274','EPSG','1037','EPSG','1027');
INSERT INTO "vertical_crs" VALUES('EPSG','9279','SA LLD height',NULL,'EPSG','6499','EPSG','1262',0);
INSERT INTO "usage" VALUES('EPSG','13916','vertical_crs','EPSG','9279','EPSG','3309','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9287','LAT NL depth',NULL,'EPSG','6498','EPSG','1290',0);
INSERT INTO "usage" VALUES('EPSG','14122','vertical_crs','EPSG','9287','EPSG','1630','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','9288','MSL NL depth',NULL,'EPSG','6498','EPSG','1270',0);
INSERT INTO "usage" VALUES('EPSG','14123','vertical_crs','EPSG','9288','EPSG','1630','EPSG','1265');
INSERT INTO "vertical_crs" VALUES('EPSG','9303','HS2-VRF height',NULL,'EPSG','6499','EPSG','1265',0);
INSERT INTO "usage" VALUES('EPSG','14049','vertical_crs','EPSG','9303','EPSG','4582','EPSG','1260');
INSERT INTO "vertical_crs" VALUES('EPSG','9335','KSA-VRF14 height',NULL,'EPSG','6499','EPSG','1269',0);
INSERT INTO "usage" VALUES('EPSG','13922','vertical_crs','EPSG','9335','EPSG','3303','EPSG','1181');
INSERT INTO "vertical_crs" VALUES('EPSG','9351','NGNC08 height',NULL,'EPSG','6499','EPSG','1255',0);
INSERT INTO "usage" VALUES('EPSG','13977','vertical_crs','EPSG','9351','EPSG','3430','EPSG','1026');
INSERT INTO "vertical_crs" VALUES('EPSG','9389','EVRF2019 height',NULL,'EPSG','6499','EPSG','1274',0);
INSERT INTO "usage" VALUES('EPSG','14658','vertical_crs','EPSG','9389','EPSG','4608','EPSG','1261');
INSERT INTO "vertical_crs" VALUES('EPSG','9390','EVRF2019 mean-tide height',NULL,'EPSG','6499','EPSG','1287',0);
INSERT INTO "usage" VALUES('EPSG','14659','vertical_crs','EPSG','9390','EPSG','4608','EPSG','1262');
INSERT INTO "vertical_crs" VALUES('EPSG','9392','Mallorca height',NULL,'EPSG','6499','EPSG','1275',0);
INSERT INTO "usage" VALUES('EPSG','14031','vertical_crs','EPSG','9392','EPSG','4602','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9393','Menorca height',NULL,'EPSG','6499','EPSG','1276',0);
INSERT INTO "usage" VALUES('EPSG','14032','vertical_crs','EPSG','9393','EPSG','4603','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9394','Ibiza height',NULL,'EPSG','6499','EPSG','1277',0);
INSERT INTO "usage" VALUES('EPSG','14033','vertical_crs','EPSG','9394','EPSG','4604','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9395','Lanzarote height',NULL,'EPSG','6499','EPSG','1278',0);
INSERT INTO "usage" VALUES('EPSG','14034','vertical_crs','EPSG','9395','EPSG','4591','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9396','Fuerteventura height',NULL,'EPSG','6499','EPSG','1279',0);
INSERT INTO "usage" VALUES('EPSG','14035','vertical_crs','EPSG','9396','EPSG','4592','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9397','Gran Canaria height',NULL,'EPSG','6499','EPSG','1280',0);
INSERT INTO "usage" VALUES('EPSG','14036','vertical_crs','EPSG','9397','EPSG','4593','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9398','Tenerife height',NULL,'EPSG','6499','EPSG','1281',0);
INSERT INTO "usage" VALUES('EPSG','14037','vertical_crs','EPSG','9398','EPSG','4594','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9399','La Gomera height',NULL,'EPSG','6499','EPSG','1282',0);
INSERT INTO "usage" VALUES('EPSG','14038','vertical_crs','EPSG','9399','EPSG','4595','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9400','La Palma height',NULL,'EPSG','6499','EPSG','1283',0);
INSERT INTO "usage" VALUES('EPSG','14039','vertical_crs','EPSG','9400','EPSG','4596','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9401','El Hierro height',NULL,'EPSG','6499','EPSG','1284',0);
INSERT INTO "usage" VALUES('EPSG','14040','vertical_crs','EPSG','9401','EPSG','4597','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9402','Ceuta 2 height',NULL,'EPSG','6499','EPSG','1285',0);
INSERT INTO "usage" VALUES('EPSG','14041','vertical_crs','EPSG','9402','EPSG','4590','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9451','BI height',NULL,'EPSG','6499','EPSG','1288',0);
INSERT INTO "usage" VALUES('EPSG','14087','vertical_crs','EPSG','9451','EPSG','4606','EPSG','1026');
INSERT INTO "vertical_crs" VALUES('EPSG','9458','AVWS height',NULL,'EPSG','6499','EPSG','1292',0);
INSERT INTO "usage" VALUES('EPSG','14231','vertical_crs','EPSG','9458','EPSG','4177','EPSG','1264');
INSERT INTO "vertical_crs" VALUES('EPSG','9471','INAGeoid2020 height',NULL,'EPSG','6499','EPSG','1294',0);
INSERT INTO "usage" VALUES('EPSG','14153','vertical_crs','EPSG','9471','EPSG','1122','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9650','Baltic 1986 height',NULL,'EPSG','6499','EPSG','1296',0);
INSERT INTO "usage" VALUES('EPSG','15032','vertical_crs','EPSG','9650','EPSG','3293','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9651','EVRF2007-PL height',NULL,'EPSG','6499','EPSG','1297',0);
INSERT INTO "usage" VALUES('EPSG','15034','vertical_crs','EPSG','9651','EPSG','3293','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9663','EH2000 height',NULL,'EPSG','6499','EPSG','1298',0);
INSERT INTO "usage" VALUES('EPSG','14747','vertical_crs','EPSG','9663','EPSG','3246','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9666','LAS07 height',NULL,'EPSG','6499','EPSG','1299',0);
INSERT INTO "usage" VALUES('EPSG','14754','vertical_crs','EPSG','9666','EPSG','3272','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9669','BGS2005 height',NULL,'EPSG','6499','EPSG','1300',0);
INSERT INTO "usage" VALUES('EPSG','14770','vertical_crs','EPSG','9669','EPSG','3224','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9672','CD Norway depth',NULL,'EPSG','6498','EPSG','1301',0);
INSERT INTO "usage" VALUES('EPSG','14776','vertical_crs','EPSG','9672','EPSG','4615','EPSG','1198');
INSERT INTO "vertical_crs" VALUES('EPSG','9675','Pago Pago 2020 height',NULL,'EPSG','6499','EPSG','1302',0);
INSERT INTO "usage" VALUES('EPSG','14793','vertical_crs','EPSG','9675','EPSG','2288','EPSG','1026');
INSERT INTO "vertical_crs" VALUES('EPSG','9681','NVD 1992 height',NULL,'EPSG','6499','EPSG','1303',0);
INSERT INTO "usage" VALUES('EPSG','14851','vertical_crs','EPSG','9681','EPSG','3217','EPSG','1181');
INSERT INTO "vertical_crs" VALUES('EPSG','9721','Catania 1965 height',NULL,'EPSG','6499','EPSG','1306',0);
INSERT INTO "usage" VALUES('EPSG','15276','vertical_crs','EPSG','9721','EPSG','2340','EPSG','1178');
INSERT INTO "vertical_crs" VALUES('EPSG','9722','Cagliari 1956 height',NULL,'EPSG','6499','EPSG','1307',0);
INSERT INTO "usage" VALUES('EPSG','15278','vertical_crs','EPSG','9722','EPSG','2339','EPSG','1178');
