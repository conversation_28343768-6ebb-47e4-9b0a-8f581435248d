--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "concatenated_operation" VALUES('EPSG','3896','MGI (<PERSON>rro) to WGS 84 (2)','','EPSG','4805','EPSG','4326',1.5,'BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','8965','concatenated_operation','EPSG','3896','EPSG','1037','EPSG','1042');
INSERT INTO "concatenated_operation" VALUES('EPSG','3966','MGI (<PERSON>rro) to WGS 84 (1)','Accuracy estimate is not available.','EPSG','4805','EPSG','4326',6.0,'DMA-balk',0);
INSERT INTO "usage" VALUES('EPSG','9014','concatenated_operation','EPSG','3966','EPSG','2370','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','4435','Puerto Rico to NAD83(HARN) (1)','Accuracy 0.1m at 67% confidence level. May be taken as approximate transformation Puerto Rico to WGS 84 - see code 8583.','EPSG','4139','EPSG','4152',0.08,'NGS-PRVI',0);
INSERT INTO "usage" VALUES('EPSG','9074','concatenated_operation','EPSG','4435','EPSG','3634','EPSG','1031');
INSERT INTO "concatenated_operation" VALUES('EPSG','4837','Amersfoort to ED50 (1)','Adopted by NAM in 2006, replacing polynomial tfms 1046, 6304, 1050 and 6306.','EPSG','4289','EPSG','4230',1.5,'NAM-Nld 2006',0);
INSERT INTO "usage" VALUES('EPSG','9130','concatenated_operation','EPSG','4837','EPSG','1275','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','5190','Tokyo 1892 to Korea 2000 (1)','','EPSG','5132','EPSG','4737',1.0,'OGP-Kor',0);
INSERT INTO "usage" VALUES('EPSG','9275','concatenated_operation','EPSG','5190','EPSG','3266','EPSG','1050');
INSERT INTO "concatenated_operation" VALUES('EPSG','5192','Tokyo 1892 to WGS 84 (1)','','EPSG','5132','EPSG','4326',1.0,'OGP-Kor',0);
INSERT INTO "usage" VALUES('EPSG','9277','concatenated_operation','EPSG','5192','EPSG','3266','EPSG','1050');
INSERT INTO "concatenated_operation" VALUES('EPSG','5230','S-JTSK (Ferro) to WGS 84 (2)','','EPSG','4818','EPSG','4326',1.0,'OGP-Svk',0);
INSERT INTO "usage" VALUES('EPSG','9306','concatenated_operation','EPSG','5230','EPSG','1211','EPSG','1041');
INSERT INTO "concatenated_operation" VALUES('EPSG','5240','S-JTSK/05 (Ferro) to WGS 84 (1)','Replaces S-JTSK (Ferro) to WGS 84 (1) (CRS code 8642) in Czech Republic.','EPSG','5229','EPSG','4326',1.0,'OGP-Cze',0);
INSERT INTO "usage" VALUES('EPSG','9312','concatenated_operation','EPSG','5240','EPSG','1079','EPSG','1041');
INSERT INTO "concatenated_operation" VALUES('EPSG','5242','S-JTSK (Ferro) to WGS 84 (3)','Parameter values from S-JTSK/05 to ETRS89 (1) (code 5226). For applications to an accuracy of 1m. Replaces S-JTSK (Ferro) to WGS 84 (1) (tfm code 8642).','EPSG','4818','EPSG','4326',1.0,'OGP-Cze R05',0);
INSERT INTO "usage" VALUES('EPSG','9314','concatenated_operation','EPSG','5242','EPSG','1079','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','5838','Lisbon (Lisbon) to WGS 84 (2)','','EPSG','4803','EPSG','4326',2.0,'OGP-Prt 2009',0);
INSERT INTO "usage" VALUES('EPSG','9511','concatenated_operation','EPSG','5838','EPSG','1294','EPSG','1042');
INSERT INTO "concatenated_operation" VALUES('EPSG','6714','Tokyo to JGD2011 (1)','See Tokyo to JGD2011 (2) (code 6740) for areas other than northern Honshu.','EPSG','4301','EPSG','6668',0.3,'OGP-Jpn N Honshu',0);
INSERT INTO "usage" VALUES('EPSG','9742','concatenated_operation','EPSG','6714','EPSG','4170','EPSG','1142');
INSERT INTO "concatenated_operation" VALUES('EPSG','6739','NAD27 to NAD83(HARN) (22)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8622.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa SD',0);
INSERT INTO "usage" VALUES('EPSG','9755','concatenated_operation','EPSG','6739','EPSG','1410','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','6874','Tananarive (Paris) to WGS 84 (2)','Used by OMV.','EPSG','4810','EPSG','4326',3.0,'OGP-Mdg',0);
INSERT INTO "usage" VALUES('EPSG','9803','concatenated_operation','EPSG','6874','EPSG','3273','EPSG','1043');
INSERT INTO "concatenated_operation" VALUES('EPSG','7811','NTF (Paris) to RGF93 v1 (2)','Second step is an emulation (using the NTv2 method) of the geocentric interpolation method described in CT code 9337. Note that the grid file parameters are of opposite sign.','EPSG','4807','EPSG','4171',1.0,'IOGP-Fra NTv2',0);
INSERT INTO "usage" VALUES('EPSG','10276','concatenated_operation','EPSG','7811','EPSG','3694','EPSG','1041');
INSERT INTO "concatenated_operation" VALUES('EPSG','7965','Poolbeg height (ft(Br36)) to Malin Head height (1)','','EPSG','5754','EPSG','5731',0.1,'1',0);
INSERT INTO "usage" VALUES('EPSG','10349','concatenated_operation','EPSG','7965','EPSG','1305','EPSG','1059');
INSERT INTO "concatenated_operation" VALUES('EPSG','7967','Poolbeg height (ft(Br36)) to Belfast height (1)','','EPSG','5754','EPSG','5732',0.1,'1',0);
INSERT INTO "usage" VALUES('EPSG','10351','concatenated_operation','EPSG','7967','EPSG','2530','EPSG','1059');
INSERT INTO "concatenated_operation" VALUES('EPSG','7973','NGVD29 height (ftUS) to NAVD88 height (1)','','EPSG','5702','EPSG','5703',0.02,'IOGP - US Conus W',0);
INSERT INTO "usage" VALUES('EPSG','10356','concatenated_operation','EPSG','7973','EPSG','2950','EPSG','1099');
INSERT INTO "concatenated_operation" VALUES('EPSG','7974','NGVD29 height (ftUS) to NAVD88 height (2)','','EPSG','5702','EPSG','5703',0.02,'IOGP - US Conus C',0);
INSERT INTO "usage" VALUES('EPSG','10357','concatenated_operation','EPSG','7974','EPSG','2949','EPSG','1099');
INSERT INTO "concatenated_operation" VALUES('EPSG','7975','NGVD29 height (ftUS) to NAVD88 height (3)','','EPSG','5702','EPSG','5703',0.02,'IOGP - US Conus E',0);
INSERT INTO "usage" VALUES('EPSG','10358','concatenated_operation','EPSG','7975','EPSG','2948','EPSG','1099');
INSERT INTO "concatenated_operation" VALUES('EPSG','7983','HKPD height to HKCD depth (1)','','EPSG','5738','EPSG','5739',0.0,'IOGP-HK',0);
INSERT INTO "usage" VALUES('EPSG','10364','concatenated_operation','EPSG','7983','EPSG','3335','EPSG','1198');
INSERT INTO "concatenated_operation" VALUES('EPSG','7986','KOC CD height to KOC WD depth (1)','','EPSG','5790','EPSG','5789',0.1,'IOGP-Kwt',0);
INSERT INTO "usage" VALUES('EPSG','10367','concatenated_operation','EPSG','7986','EPSG','3267','EPSG','1059');
INSERT INTO "concatenated_operation" VALUES('EPSG','7987','KOC CD height to KOC WD depth (ft) (1)','','EPSG','5790','EPSG','5614',0.1,'IOGP-Kwt',0);
INSERT INTO "usage" VALUES('EPSG','10368','concatenated_operation','EPSG','7987','EPSG','3267','EPSG','1099');
INSERT INTO "concatenated_operation" VALUES('EPSG','8047','ED50 to WGS 84 (15)','Replaced by codes 8569 and 1612 in 1997 and 2001.
The concatenation of transformations 1147 and 1146 gives the following position vector tfm: dX=-84.491 dY=-100.559 dZ=-114.209 metres rX= -2.4006 rY=-0.5367 rZ=-2.3742 microradians dS=+0.2947 ppm.','EPSG','4230','EPSG','4326',1.5,'NMA-Nor N65 1991',0);
INSERT INTO "usage" VALUES('EPSG','10400','concatenated_operation','EPSG','8047','EPSG','2332','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8094','NTF (Paris) to WGS 84 (1)','','EPSG','4807','EPSG','4326',2.0,'EPSG-Fra',0);
INSERT INTO "usage" VALUES('EPSG','10426','concatenated_operation','EPSG','8094','EPSG','3694','EPSG','1024');
INSERT INTO "concatenated_operation" VALUES('EPSG','8174','Bogota 1975 (Bogota) to WGS 84 (1)','Accuracy 6m, 5m and 6m in X, Y and Z axes.','EPSG','4802','EPSG','4326',10.0,'DMA-Col',0);
INSERT INTO "usage" VALUES('EPSG','10427','concatenated_operation','EPSG','8174','EPSG','3229','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8175','Monte Mario (Rome) to WGS 84 (1)','Accuracy 25m in each axis.','EPSG','4806','EPSG','4326',44.0,'EPSG-Ita',0);
INSERT INTO "usage" VALUES('EPSG','10428','concatenated_operation','EPSG','8175','EPSG','2339','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8176','Tananarive (Paris) to WGS 84 (1)','Accuracy not available.','EPSG','4810','EPSG','4326',999.0,'EPSG-Mdg',0);
INSERT INTO "usage" VALUES('EPSG','10429','concatenated_operation','EPSG','8176','EPSG','3273','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8178','Batavia (Jakarta) to WGS 84 (1)','Accuracy 3m in each axis.','EPSG','4813','EPSG','4326',6.0,'EPSG-Idn Sumatra',0);
INSERT INTO "usage" VALUES('EPSG','10430','concatenated_operation','EPSG','8178','EPSG','1285','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8183','HD72 to WGS 84 (1)','Approximation at the +/- 1m level assuming that ETRF89 is equivalent to WGS 84.','EPSG','4237','EPSG','4326',NULL,'EPSG-Hun',1);
INSERT INTO "usage" VALUES('EPSG','10431','concatenated_operation','EPSG','8183','EPSG','1119','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8186','NTF (Paris) to ED50 (1)','','EPSG','4807','EPSG','4230',2.0,'EPSG-Fra',0);
INSERT INTO "usage" VALUES('EPSG','10432','concatenated_operation','EPSG','8186','EPSG','3694','EPSG','1024');
INSERT INTO "concatenated_operation" VALUES('EPSG','8188','NTF (Paris) to WGS 72 (1)','','EPSG','4807','EPSG','4322',2.0,'EPSG-Fra',0);
INSERT INTO "usage" VALUES('EPSG','10433','concatenated_operation','EPSG','8188','EPSG','3694','EPSG','1024');
INSERT INTO "concatenated_operation" VALUES('EPSG','8190','AGD66 to WGS 84 (2)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84. 0.1m accuracy.','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus 5m',1);
INSERT INTO "usage" VALUES('EPSG','10434','concatenated_operation','EPSG','8190','EPSG','2575','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8192','AGD84 to WGS 84 (3)','Approximation assuming that GDA94 is equivalent to WGS 84.','EPSG','4203','EPSG','4326',NULL,'EPSG-Aus 5m',1);
INSERT INTO "usage" VALUES('EPSG','10435','concatenated_operation','EPSG','8192','EPSG','2575','EPSG','1045');
INSERT INTO "concatenated_operation" VALUES('EPSG','8194','AGD84 to WGS 84 (4)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84.','EPSG','4203','EPSG','4326',NULL,'EPSG-Aus 1m',1);
INSERT INTO "usage" VALUES('EPSG','10436','concatenated_operation','EPSG','8194','EPSG','2575','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8195','RT90 to WGS 84 (1)','Approximation at the +/- 1m level assuming that ETRF89 is equivalent to WGS 84.','EPSG','4124','EPSG','4326',NULL,'EPSG-Swe',1);
INSERT INTO "usage" VALUES('EPSG','10437','concatenated_operation','EPSG','8195','EPSG','1225','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8199','Pulkovo 1942 to WGS 84 (2)','Approximation at the +/- 1m level assuming that LKS94(ETRS89) is equivalent to WGS 84.','EPSG','4284','EPSG','4326',NULL,'EPSG-Ltu',1);
INSERT INTO "usage" VALUES('EPSG','10438','concatenated_operation','EPSG','8199','EPSG','1145','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8211','Voirol 1875 (Paris) to WGS 84 (1)','','EPSG','4811','EPSG','4326',999.0,'EPSG-Dza N',0);
INSERT INTO "usage" VALUES('EPSG','10439','concatenated_operation','EPSG','8211','EPSG','1365','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8215','Tete to WGS 84 (1)','Approximation at the +/- 1m level assuming that Moznet is equivalent to WGS 84.','EPSG','4127','EPSG','4326',NULL,'EPSG-Moz',1);
INSERT INTO "usage" VALUES('EPSG','10440','concatenated_operation','EPSG','8215','EPSG','1167','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8217','Tete to WGS 84 (2)','Approximation at the +/- 1m level assuming that Moznet is equivalent to WGS 84.','EPSG','4127','EPSG','4326',NULL,'EPSG-Moz A',1);
INSERT INTO "usage" VALUES('EPSG','10441','concatenated_operation','EPSG','8217','EPSG','2350','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8219','Tete to WGS 84 (3)','Approximation at the +/- 1m level assuming that Moznet is equivalent to WGS 84.','EPSG','4127','EPSG','4326',NULL,'EPSG-Moz B',1);
INSERT INTO "usage" VALUES('EPSG','10442','concatenated_operation','EPSG','8219','EPSG','2351','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8221','Tete to WGS 84 (4)','Approximation at the +/- 1m level assuming that Moznet is equivalent to WGS 84.','EPSG','4127','EPSG','4326',NULL,'EPSG-Moz C',1);
INSERT INTO "usage" VALUES('EPSG','10443','concatenated_operation','EPSG','8221','EPSG','2352','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8223','Tete to WGS 84 (5)','Approximation at the +/- 1m level assuming that Moznet is equivalent to WGS 84.','EPSG','4127','EPSG','4326',NULL,'EPSG-Moz D',1);
INSERT INTO "usage" VALUES('EPSG','10444','concatenated_operation','EPSG','8223','EPSG','2353','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8234','DHDN to WGS 84 (1)','Approximation at the +/- 1m level assuming that ETRF89 is equivalent to WGS 84.','EPSG','4314','EPSG','4326',NULL,'EPSG-Deu W',1);
INSERT INTO "usage" VALUES('EPSG','10446','concatenated_operation','EPSG','8234','EPSG','2326','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8236','Pulkovo 1942 to WGS 84 (11)','Approximation at the +/- 1m level assuming that ETRF89 is equivalent to WGS 84.','EPSG','4284','EPSG','4326',NULL,'EPSG-Deu E',1);
INSERT INTO "usage" VALUES('EPSG','10447','concatenated_operation','EPSG','8236','EPSG','1343','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8243','NAD27 to WGS 84 (25)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84. Superseded by NAD27 to WGS 84 (27) (code 8404) in Quebec and NAD27 to WGS 84 (26) (code 8245) elsewhere in Canada.','EPSG','4267','EPSG','4326',NULL,'EPSG-Can old',1);
INSERT INTO "usage" VALUES('EPSG','10449','concatenated_operation','EPSG','8243','EPSG','1061','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8245','NAD27 to WGS 84 (26)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84.','EPSG','4267','EPSG','4326',NULL,'EPSG-Can',1);
INSERT INTO "usage" VALUES('EPSG','10450','concatenated_operation','EPSG','8245','EPSG','1061','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8263','MGI (Ferro) to WGS 84 (1)','Accuracy estimate is not available.','EPSG','4805','EPSG','4326',NULL,'DMA-balk',1);
INSERT INTO "usage" VALUES('EPSG','10458','concatenated_operation','EPSG','8263','EPSG','2370','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8363','ETRS89 + Baltic 1957 height to ETRS89 + EVRF2007 height (1)','Recommended method for transforming coordinates between Baltic 1957 height and EVRF2007 height and vice-versa in Slovakia. Compound transformation using two separate quasigeoid models (DVRM05 and DMQSK2014E).','EPSG','8360','EPSG','7423',0.05,'UGKK-Svk',0);
INSERT INTO "usage" VALUES('EPSG','10510','concatenated_operation','EPSG','8363','EPSG','1211','EPSG','1059');
INSERT INTO "concatenated_operation" VALUES('EPSG','8386','Old Hawaiian to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84.','EPSG','4135','EPSG','4326',NULL,'EPSG-Usa Hi',1);
INSERT INTO "usage" VALUES('EPSG','10523','concatenated_operation','EPSG','8386','EPSG','1334','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8388','St. Lawrence Island to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84.','EPSG','4136','EPSG','4326',NULL,'EPSG-Usa AK StL',1);
INSERT INTO "usage" VALUES('EPSG','10524','concatenated_operation','EPSG','8388','EPSG','1332','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8390','St. Paul Island to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84.','EPSG','4137','EPSG','4326',NULL,'EPSG-Usa AK StP',1);
INSERT INTO "usage" VALUES('EPSG','10526','concatenated_operation','EPSG','8390','EPSG','1333','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8392','St. George Island to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84.','EPSG','4138','EPSG','4326',NULL,'EPSG-Usa AK StG',1);
INSERT INTO "usage" VALUES('EPSG','10527','concatenated_operation','EPSG','8392','EPSG','1331','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8394','NAD27(CGQ77) to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84. Superseded by NAD27(CGQ77) to WGS 84 (2) (code 8564).','EPSG','4609','EPSG','4326',NULL,'EPSG-Can Qc NT1',1);
INSERT INTO "usage" VALUES('EPSG','10529','concatenated_operation','EPSG','8394','EPSG','1368','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8396','AGD66 to WGS 84 (3)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84. Superseded by AGD66 to WGS 84 (11) (code 8581).','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus ACT 1m',1);
INSERT INTO "usage" VALUES('EPSG','10530','concatenated_operation','EPSG','8396','EPSG','2283','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8398','AGD66 to WGS 84 (4)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84. Superseded by AGD66 to WGS 84 (9) (code 8576).','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus Tas 1m',1);
INSERT INTO "usage" VALUES('EPSG','10531','concatenated_operation','EPSG','8398','EPSG','1282','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8400','AGD66 to WGS 84 (5)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84.','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus NSW Vic 1m',1);
INSERT INTO "usage" VALUES('EPSG','10532','concatenated_operation','EPSG','8400','EPSG','2286','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8402','Puerto Rico to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84.','EPSG','4139','EPSG','4326',NULL,'EPSG-PRVI',1);
INSERT INTO "usage" VALUES('EPSG','10533','concatenated_operation','EPSG','8402','EPSG','1335','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8404','NAD27 to WGS 84 (27)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84. Superseded by NAD27 to WGS 84 (31) (code 8565).','EPSG','4267','EPSG','4326',NULL,'EPSG-Can QC',1);
INSERT INTO "usage" VALUES('EPSG','10534','concatenated_operation','EPSG','8404','EPSG','1368','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8406','NAD27(76) to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83 is equivalent to WGS 84.','EPSG','4608','EPSG','4326',NULL,'EPSG-Can On',1);
INSERT INTO "usage" VALUES('EPSG','10536','concatenated_operation','EPSG','8406','EPSG','1367','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8408','AGD66 to WGS 84 (6)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84.  Superseded by AGD66 to WGS 84 (11) (code 8578).','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus Vic 0.1m',1);
INSERT INTO "usage" VALUES('EPSG','10538','concatenated_operation','EPSG','8408','EPSG','2285','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8418','ATS77 to WGS 84 (1)','Approximation at the +/- 1m level assuming that NAD83(CSRS98) is equivalent to WGS 84.','EPSG','4122','EPSG','4326',NULL,'EPSG-Can NB',1);
INSERT INTO "usage" VALUES('EPSG','10548','concatenated_operation','EPSG','8418','EPSG','1447','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8419','ATS77 to WGS 84 (2)','Approximation at the +/- 1m level assuming that NAD83(CSRS98) is equivalent to WGS 84.','EPSG','4122','EPSG','4326',NULL,'EPSG-Can PEI',1);
INSERT INTO "usage" VALUES('EPSG','10549','concatenated_operation','EPSG','8419','EPSG','1533','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8420','NAD27 to WGS 84 (32)','','EPSG','4267','EPSG','4326',NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','10550','concatenated_operation','EPSG','8420','EPSG','2375','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8421','NAD83 to WGS 84 (7)','','EPSG','4269','EPSG','4326',NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','10551','concatenated_operation','EPSG','8421','EPSG','2375','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8422','NAD83 to WGS 84 (8)','The gridded difference file AB_CSRS.DAC in STEP 1 will need to be renamed to AB_CSRS.gsb to run in some software suites. Formats identical, but AB file is provincial fit only.','EPSG','4269','EPSG','4326',NULL,'Alb Env-Can AB',1);
INSERT INTO "usage" VALUES('EPSG','10552','concatenated_operation','EPSG','8422','EPSG','2376','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8442','ETRS89 to S-JTSK (5)','Recommended method of a transformation from ETRS89 to S-JTSK in Slovakia. For reverse transformation see S-JTSK to ETRS89 (6) (code 8443). Both together replace S-JTSK to ETRS89 (4) (code 4827).','EPSG','4258','EPSG','4156',0.06,'UGKK-Sk JTSK03',0);
INSERT INTO "usage" VALUES('EPSG','10562','concatenated_operation','EPSG','8442','EPSG','1211','EPSG','1027');
INSERT INTO "concatenated_operation" VALUES('EPSG','8443','S-JTSK to ETRS89 (6)','Recommended method of a transformation from S-JTSK to ETRS89 in Slovakia. For reverse transformation see ETRS89 to S-JTSK (5) (code 8442). Both together replace S-JTSK to ETRS89 (4) (code 4827).','EPSG','4156','EPSG','4258',0.06,'UGKK-Sk JTSK03',0);
INSERT INTO "usage" VALUES('EPSG','10563','concatenated_operation','EPSG','8443','EPSG','1211','EPSG','1027');
INSERT INTO "concatenated_operation" VALUES('EPSG','8453','AGD66 to WGS 84 (7)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84.','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus Tas 0.1m',1);
INSERT INTO "usage" VALUES('EPSG','10572','concatenated_operation','EPSG','8453','EPSG','1282','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8454','AGD66 to WGS 84 (8)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84.','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus NT 0.1m',1);
INSERT INTO "usage" VALUES('EPSG','10573','concatenated_operation','EPSG','8454','EPSG','2284','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8457','CH1903+ to WGS 84 (1)','Approximation at the +/- 1m level assuming that CHTRF95 is equivalent to WGS 84.','EPSG','4150','EPSG','4326',NULL,'EPSG-CH',1);
INSERT INTO "usage" VALUES('EPSG','10574','concatenated_operation','EPSG','8457','EPSG','1286','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8460','NAD27 to NAD83(HARN) (1)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8590.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa AL',0);
INSERT INTO "usage" VALUES('EPSG','10577','concatenated_operation','EPSG','8460','EPSG','1372','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8461','NAD27 to NAD83(HARN) (2)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8591.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa AZ',0);
INSERT INTO "usage" VALUES('EPSG','10578','concatenated_operation','EPSG','8461','EPSG','1373','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8462','NAD27 to NAD83(HARN) (3)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8593.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa CA n',0);
INSERT INTO "usage" VALUES('EPSG','10579','concatenated_operation','EPSG','8462','EPSG','2297','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8463','NAD27 to NAD83(HARN) (4)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8594.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa CA s',0);
INSERT INTO "usage" VALUES('EPSG','10580','concatenated_operation','EPSG','8463','EPSG','2298','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8464','NAD27 to NAD83(HARN) (5)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8595.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa CO',0);
INSERT INTO "usage" VALUES('EPSG','10581','concatenated_operation','EPSG','8464','EPSG','1376','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8465','NAD27 to NAD83(HARN) (6)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8597.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa GA',0);
INSERT INTO "usage" VALUES('EPSG','10582','concatenated_operation','EPSG','8465','EPSG','1380','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8466','NAD27 to NAD83(HARN) (7)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8596.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa FL',0);
INSERT INTO "usage" VALUES('EPSG','10583','concatenated_operation','EPSG','8466','EPSG','1379','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8467','NAD27 to NAD83(HARN) (8)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8611.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa ID MT e',0);
INSERT INTO "usage" VALUES('EPSG','10584','concatenated_operation','EPSG','8467','EPSG','2382','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8468','NAD27 to NAD83(HARN) (9)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8612.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa ID MT w',0);
INSERT INTO "usage" VALUES('EPSG','10585','concatenated_operation','EPSG','8468','EPSG','2383','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8469','NAD27 to NAD83(HARN) (10)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8602.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa KY',0);
INSERT INTO "usage" VALUES('EPSG','10586','concatenated_operation','EPSG','8469','EPSG','1386','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8470','NAD27 to NAD83(HARN) (11)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8603.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa LA',0);
INSERT INTO "usage" VALUES('EPSG','10587','concatenated_operation','EPSG','8470','EPSG','1387','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8471','NAD27 to NAD83(HARN) (12)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8605.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa DE MD',0);
INSERT INTO "usage" VALUES('EPSG','10588','concatenated_operation','EPSG','8471','EPSG','2377','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8472','NAD27 to NAD83(HARN) (13)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8604.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa ME',0);
INSERT INTO "usage" VALUES('EPSG','10589','concatenated_operation','EPSG','8472','EPSG','1388','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8473','NAD27 to NAD83(HARN) (14)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8607.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa MI',0);
INSERT INTO "usage" VALUES('EPSG','10590','concatenated_operation','EPSG','8473','EPSG','1391','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8474','NAD27 to NAD83(HARN) (15)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8609.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa MS',0);
INSERT INTO "usage" VALUES('EPSG','10591','concatenated_operation','EPSG','8474','EPSG','1393','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8475','NAD27 to NAD83(HARN) (16)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8613.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa NE',0);
INSERT INTO "usage" VALUES('EPSG','10592','concatenated_operation','EPSG','8475','EPSG','1396','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8476','NAD27 to NAD83(HARN) (17)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8606.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa NewEng',0);
INSERT INTO "usage" VALUES('EPSG','10593','concatenated_operation','EPSG','8476','EPSG','2378','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8477','NAD27 to NAD83(HARN) (18)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8616.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa NM',0);
INSERT INTO "usage" VALUES('EPSG','10594','concatenated_operation','EPSG','8477','EPSG','1400','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8478','NAD27 to NAD83(HARN) (19)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8617.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa NY',0);
INSERT INTO "usage" VALUES('EPSG','10595','concatenated_operation','EPSG','8478','EPSG','1401','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8479','NAD27 to NAD83(HARN) (20)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8618.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa ND',0);
INSERT INTO "usage" VALUES('EPSG','10596','concatenated_operation','EPSG','8479','EPSG','1403','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8480','NAD27 to NAD83(HARN) (21)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8620.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa OK',0);
INSERT INTO "usage" VALUES('EPSG','10597','concatenated_operation','EPSG','8480','EPSG','1405','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8481','Puerto Rico to NAD83(HARN) (1)','May be taken as approximate transformation Puerto Rico to WGS 84 - see code 8583.','EPSG','4139','EPSG','4152',NULL,'NGS-PRVI',1);
INSERT INTO "usage" VALUES('EPSG','10598','concatenated_operation','EPSG','8481','EPSG','1335','EPSG','1031');
INSERT INTO "concatenated_operation" VALUES('EPSG','8482','NAD27 to NAD83(HARN) (22)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8622.','EPSG','4267','EPSG','4152',NULL,'NGS-Usa SD',1);
INSERT INTO "usage" VALUES('EPSG','10599','concatenated_operation','EPSG','8482','EPSG','1410','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8483','NAD27 to NAD83(HARN) (23)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8623.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa TN',0);
INSERT INTO "usage" VALUES('EPSG','10600','concatenated_operation','EPSG','8483','EPSG','1411','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8484','NAD27 to NAD83(HARN) (24)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8624.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa TX e',0);
INSERT INTO "usage" VALUES('EPSG','10601','concatenated_operation','EPSG','8484','EPSG','2379','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8485','NAD27 to NAD83(HARN) (25)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8625.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa TX w',0);
INSERT INTO "usage" VALUES('EPSG','10602','concatenated_operation','EPSG','8485','EPSG','2380','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8486','NAD27 to NAD83(HARN) (26)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8627.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa VA',0);
INSERT INTO "usage" VALUES('EPSG','10603','concatenated_operation','EPSG','8486','EPSG','1415','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8487','NAD27 to NAD83(HARN) (27)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8621.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa OR WA',0);
INSERT INTO "usage" VALUES('EPSG','10604','concatenated_operation','EPSG','8487','EPSG','2381','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8488','NAD27 to NAD83(HARN) (28)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8629.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa WI',0);
INSERT INTO "usage" VALUES('EPSG','10605','concatenated_operation','EPSG','8488','EPSG','1418','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8489','NAD27 to NAD83(HARN) (29)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8630.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa WY',0);
INSERT INTO "usage" VALUES('EPSG','10606','concatenated_operation','EPSG','8489','EPSG','1419','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8496','NAD27 to WGS 84 (28)','','EPSG','4267','EPSG','4326',NULL,'NGS-Usa conus',1);
INSERT INTO "usage" VALUES('EPSG','10613','concatenated_operation','EPSG','8496','EPSG','2374','EPSG','1027');
INSERT INTO "concatenated_operation" VALUES('EPSG','8497','NAD27 to WGS 84 (29)','','EPSG','4267','EPSG','4326',NULL,'NGS-Usa AK',1);
INSERT INTO "usage" VALUES('EPSG','10614','concatenated_operation','EPSG','8497','EPSG','2373','EPSG','1027');
INSERT INTO "concatenated_operation" VALUES('EPSG','8508','Old Hawaiian to NAD83(HARN) (1)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs Old Hawaiian (code 4135), NAD83 (code 4269) and NAD83(HARN) have longitudes positive east. May be taken as approximate transformation Old Hawaiin to WGS 84 - see code 8582.','EPSG','4135','EPSG','4152',0.3,'NGS-Usa HI',0);
INSERT INTO "usage" VALUES('EPSG','10625','concatenated_operation','EPSG','8508','EPSG','1334','EPSG','1041');
INSERT INTO "concatenated_operation" VALUES('EPSG','8509','NAD27 to NAD83(HARN) (30)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8599.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa IN',0);
INSERT INTO "usage" VALUES('EPSG','10626','concatenated_operation','EPSG','8509','EPSG','1383','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8510','NAD27 to NAD83(HARN) (31)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8601.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa KS',0);
INSERT INTO "usage" VALUES('EPSG','10627','concatenated_operation','EPSG','8510','EPSG','1385','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8511','NAD27 to NAD83(HARN) (32)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8614.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa NV',0);
INSERT INTO "usage" VALUES('EPSG','10628','concatenated_operation','EPSG','8511','EPSG','1397','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8512','NAD27 to NAD83(HARN) (33)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8619.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa OH',0);
INSERT INTO "usage" VALUES('EPSG','10629','concatenated_operation','EPSG','8512','EPSG','1404','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8513','NAD27 to NAD83(HARN) (34)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8626.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa UT',0);
INSERT INTO "usage" VALUES('EPSG','10630','concatenated_operation','EPSG','8513','EPSG','1413','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8514','NAD27 to NAD83(HARN) (35)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8628.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa WV',0);
INSERT INTO "usage" VALUES('EPSG','10631','concatenated_operation','EPSG','8514','EPSG','1417','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8517','Chos Malal 1914 to WGS 84 (1)','May be implemented using a single step geocentric translations of dx=+5.5m dY=+176.7m dZ=+141.4m.','EPSG','4160','EPSG','4326',11.0,'TOT-Arg Neu',0);
INSERT INTO "usage" VALUES('EPSG','10634','concatenated_operation','EPSG','8517','EPSG','2325','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8530','South Yemen to WGS 84 (1)','May be implemented as a single transformation using geocentric translations transformation method with parameter values dX=-76m dY=-138m dZ=+67m.','EPSG','4164','EPSG','4326',NULL,'IGN-Yem South',1);
INSERT INTO "usage" VALUES('EPSG','10635','concatenated_operation','EPSG','8530','EPSG','1340','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8532','Indian 1960 to WGS 84 (1)','May be implemented as a single transformation using position vector 7-parameter geocentric transformation method with parameter values dX=+199m dY=+931m dZ=+318.9m rX=rY=0 sec rZ=+0.814 sec dS=-0.38 ppm.','EPSG','4131','EPSG','4326',26.0,'PV-Vnm',0);
INSERT INTO "usage" VALUES('EPSG','10636','concatenated_operation','EPSG','8532','EPSG','1495','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8537','Egypt 1907 to WGS 84 (2)','Used by Shell. May be implemented as a single transformation using position vector 7-parameter geocentric transformation method with parameter values dX=-121.8m dY=+98.1m dZ=-10.7m rX=rY=0 sec rZ=+0.554 sec dS=+0.2263 ppm.','EPSG','4229','EPSG','4326',6.0,'MCE-Egy',0);
INSERT INTO "usage" VALUES('EPSG','10637','concatenated_operation','EPSG','8537','EPSG','1086','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8553','NAD27 to NAD83(HARN) (36)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8598.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa IL',0);
INSERT INTO "usage" VALUES('EPSG','10645','concatenated_operation','EPSG','8553','EPSG','1382','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8554','NAD27 to NAD83(HARN) (37)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8615.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa NJ',0);
INSERT INTO "usage" VALUES('EPSG','10646','concatenated_operation','EPSG','8554','EPSG','1399','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8560','AGD84 to WGS 84 (5)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84. Superseded by AGD84 to WGS 84 (6) (code 8579).','EPSG','4203','EPSG','4326',NULL,'EPSG-Aus WA',1);
INSERT INTO "usage" VALUES('EPSG','10652','concatenated_operation','EPSG','8560','EPSG','1280','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8562','Nord Sahara 1959 to WGS 84 (3)','Derived at IGN monument CFP19 using Transit. Can be implemented as a single 7-param Position Vector transformation with parameter values of dX=-156.5m dY=-87.2m dZ=+287.8m; rX=rY=0 rZ=+0.814sec; dS=-0.38ppm.','EPSG','4307','EPSG','4326',9.0,'CGG-Alg HM',0);
INSERT INTO "usage" VALUES('EPSG','10654','concatenated_operation','EPSG','8562','EPSG','2393','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8563','NZGD49 to WGS 84 (3)','Assumes WGS 84 is coincident with NZGD2000. Accuracy about 1m.','EPSG','4272','EPSG','4326',NULL,'OSG-Nzl 1m',1);
INSERT INTO "usage" VALUES('EPSG','10655','concatenated_operation','EPSG','8563','EPSG','1175','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8564','NAD27(CGQ77) to WGS 84 (2)','','EPSG','4609','EPSG','4326',NULL,'EPSG-Can Qc NT2',1);
INSERT INTO "usage" VALUES('EPSG','10656','concatenated_operation','EPSG','8564','EPSG','1368','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8565','NAD27 to WGS 84 (31)','','EPSG','4267','EPSG','4326',NULL,'EPSG-Can Que',1);
INSERT INTO "usage" VALUES('EPSG','10657','concatenated_operation','EPSG','8565','EPSG','1368','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8566','NAD83 to WGS 84 (6)','','EPSG','4269','EPSG','4326',NULL,'EPSG-Can Qc',1);
INSERT INTO "usage" VALUES('EPSG','10658','concatenated_operation','EPSG','8566','EPSG','1368','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8568','Deir ez Zor to WGS 84 (1)','Can be implemented as a position vector tfm with param. values dX=-174.6 dY=-3.1 dZ=238.1m; rX=rY=0 rZ=0.814"; dS=-0.38 ppm.','EPSG','4227','EPSG','4326',6.0,'EPSG-Syr',0);
INSERT INTO "usage" VALUES('EPSG','10660','concatenated_operation','EPSG','8568','EPSG','2329','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8569','ED50 to WGS 84 (21)','Included in Statens Kartverk programme wsktrans between 1997 (v3.1) and 2001 (v4.0). Replaced by ED50 to WGS 84 (23) (code 1612) in April 2001.','EPSG','4230','EPSG','4326',1.5,'EPSG-Nor N65 1997',0);
INSERT INTO "usage" VALUES('EPSG','10661','concatenated_operation','EPSG','8569','EPSG','2332','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8571','Accra to WGS 84 (2)','Can be implemented as a position vector tfm dX=-171.16 dY=17.29 dZ=325.21m, rX=rY=0 rZ=0.814", dS=-0.38 ppm. See tfm code 15495. Found in use within oil industry erroneously concatenated as dX=-171.16 dY=17.29 dZ=327.81m, rX=rY0 rZ=0.554", dS=0.2263 ppm.','EPSG','4168','EPSG','4326',26.0,'EPSG-Gha',0);
INSERT INTO "usage" VALUES('EPSG','10663','concatenated_operation','EPSG','8571','EPSG','1505','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8572','Amersfoort to WGS 84 (2)','Parameter values for step 1 from Amersfoort to ETRS89 (2) (code 1751). Step 2 assumes that ETRS89 is equivalent to WGS 84 within the accuracy of the transformation. Supersedes Amersfoort to WGS 84 (1) (code 1112).','EPSG','4289','EPSG','4326',NULL,'EPSG-Nld',1);
INSERT INTO "usage" VALUES('EPSG','10664','concatenated_operation','EPSG','8572','EPSG','1275','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8573','RGF93 to WGS 84 (1)','','EPSG','4171','EPSG','4326',NULL,'EPSG-Fra',1);
INSERT INTO "usage" VALUES('EPSG','10665','concatenated_operation','EPSG','8573','EPSG','1096','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8574','American Samoa 1962 to WGS 84 (2)','Transformation actually to NAD83(HARN), but for many purposes NAD83(HARNS) can be considered to be coincident with WGS 84.','EPSG','4169','EPSG','4326',NULL,'EPSG-Asm',1);
INSERT INTO "usage" VALUES('EPSG','10666','concatenated_operation','EPSG','8574','EPSG','2288','EPSG','1045');
INSERT INTO "concatenated_operation" VALUES('EPSG','8575','American Samoa 1962 to WGS 84 (3)','Transformation actually to NAD83(HARN), but for many purposes NAD83(HARNS) can be considered to be coincident with WGS 84.','EPSG','4169','EPSG','4326',NULL,'EPSG-Asm',1);
INSERT INTO "usage" VALUES('EPSG','10667','concatenated_operation','EPSG','8575','EPSG','2289','EPSG','1045');
INSERT INTO "concatenated_operation" VALUES('EPSG','8576','AGD66 to WGS 84 (9)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84. Supersedes AGD66 to WGS 84 (4) (code 8398).','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus Tas 1m',1);
INSERT INTO "usage" VALUES('EPSG','10668','concatenated_operation','EPSG','8576','EPSG','1282','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8577','AGD66 to WGS 84 (10)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84.','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus NT',1);
INSERT INTO "usage" VALUES('EPSG','10669','concatenated_operation','EPSG','8577','EPSG','2284','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8578','AGD66 to WGS 84 (11)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84.  Supersedes AGD66 to WGS 84 (3) (code 8396) and AGD66 to WGS 84 (6) (code 8408).','EPSG','4202','EPSG','4326',NULL,'EPSG-Aus',1);
INSERT INTO "usage" VALUES('EPSG','10670','concatenated_operation','EPSG','8578','EPSG','2287','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8579','AGD84 to WGS 84 (6)','Approximation at the +/- 1m level assuming that GDA94 is equivalent to WGS 84. Supersedes AGD84 to WGS 84 (5) (code 8560).','EPSG','4203','EPSG','4326',NULL,'EPSG-Aus WA',1);
INSERT INTO "usage" VALUES('EPSG','10671','concatenated_operation','EPSG','8579','EPSG','1280','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8580','IRENET95 to WGS 84 (1)','Approximation at the +/- 1m level assuming that ETRS89 is equivalent to WGS 84.','EPSG','4173','EPSG','4326',NULL,'OSI-Ire',1);
INSERT INTO "usage" VALUES('EPSG','10672','concatenated_operation','EPSG','8580','EPSG','1305','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8581','PSD93 to WGS 84 (2)','Replaced by PSD93 to WGS 84 (1) (code 1439) in 1997. Can be implemented as a position vector tfm with parameter values dX= -182.046 dY= -225.604 dZ=+173.384m rX= -0.616 rY= -1.655 rZ=+8.378" dS=16.8673ppm.','EPSG','4134','EPSG','4326',2.5,'PDO-Omn 93',0);
INSERT INTO "usage" VALUES('EPSG','10673','concatenated_operation','EPSG','8581','EPSG','3288','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8582','Old Hawaiian to WGS 84 (2)','Transformation steps are from Old Hawaiian to NAD83(HARN) (1) (code 8508) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4135','EPSG','4326',1.5,'EPSG-Usa Hi',0);
INSERT INTO "usage" VALUES('EPSG','10674','concatenated_operation','EPSG','8582','EPSG','1334','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8583','Puerto Rico to WGS 84 (2)','Transformation steps are from Puerto Rico to NAD83(HARN) (1) (code 4435) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4139','EPSG','4326',1.5,'EPSG-PRVI',0);
INSERT INTO "usage" VALUES('EPSG','10675','concatenated_operation','EPSG','8583','EPSG','3634','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8584','NAD27 to NAD83(CSRS98) (3)','Can be taken as an approximate transformation NAD27 to WGS 84 - see code 8585.','EPSG','4267','EPSG','4140',NULL,'EPSG-Can AB',1);
INSERT INTO "usage" VALUES('EPSG','10676','concatenated_operation','EPSG','8584','EPSG','2376','EPSG','1151');
INSERT INTO "concatenated_operation" VALUES('EPSG','8585','NAD27 to WGS 84 (36)','Steps based on concatenated transformation NAD27 to NAD83(CSRS)v4 (3) (code 9336) assuming that NAD83(CSRS)v4 is equivalent to WGS 84.','EPSG','4267','EPSG','4326',2.5,'EPSG-Can AB',0);
INSERT INTO "usage" VALUES('EPSG','10677','concatenated_operation','EPSG','8585','EPSG','2376','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8586','NAD27 to NAD83(HARN) (38)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8592.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa AR',0);
INSERT INTO "usage" VALUES('EPSG','10678','concatenated_operation','EPSG','8586','EPSG','1374','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8587','NAD27 to NAD83(HARN) (39)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8600.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa IA',0);
INSERT INTO "usage" VALUES('EPSG','10679','concatenated_operation','EPSG','8587','EPSG','1384','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8588','NAD27 to NAD83(HARN) (40)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8608.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa MN',0);
INSERT INTO "usage" VALUES('EPSG','10680','concatenated_operation','EPSG','8588','EPSG','1392','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8589','NAD27 to NAD83(HARN) (41)','May be taken as approximate transformation NAD27 to WGS 84 - see code 8610.','EPSG','4267','EPSG','4152',0.2,'NGS-Usa MO',0);
INSERT INTO "usage" VALUES('EPSG','10681','concatenated_operation','EPSG','8589','EPSG','1394','EPSG','1032');
INSERT INTO "concatenated_operation" VALUES('EPSG','8590','NAD27 to WGS 84 (37)','Transformation steps are from NAD27 to NAD83(HARN) (1) (code 8460) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa AL',0);
INSERT INTO "usage" VALUES('EPSG','10682','concatenated_operation','EPSG','8590','EPSG','1372','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8591','NAD27 to WGS 84 (38)','Transformation steps are from NAD27 to NAD83(HARN) (2) (code 8461) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa AZ',0);
INSERT INTO "usage" VALUES('EPSG','10683','concatenated_operation','EPSG','8591','EPSG','1373','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8592','NAD27 to WGS 84 (39)','Transformation steps are from NAD27 to NAD83(HARN) (38) (code 8586) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa AR',0);
INSERT INTO "usage" VALUES('EPSG','10684','concatenated_operation','EPSG','8592','EPSG','1374','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8593','NAD27 to WGS 84 (40)','Transformation steps are from NAD27 to NAD83(HARN) (3) (code 8462) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa CA n',0);
INSERT INTO "usage" VALUES('EPSG','10685','concatenated_operation','EPSG','8593','EPSG','2297','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8594','NAD27 to WGS 84 (41)','Transformation steps are from NAD27 to NAD83(HARN) (4) (code 8463) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa CA s',0);
INSERT INTO "usage" VALUES('EPSG','10686','concatenated_operation','EPSG','8594','EPSG','2298','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8595','NAD27 to WGS 84 (42)','Transformation steps are from NAD27 to NAD83(HARN) (5) (code 8464) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa CO',0);
INSERT INTO "usage" VALUES('EPSG','10687','concatenated_operation','EPSG','8595','EPSG','1376','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8596','NAD27 to WGS 84 (43)','Transformation steps are from NAD27 to NAD83(HARN) (7) (code 8466) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa FL',0);
INSERT INTO "usage" VALUES('EPSG','10688','concatenated_operation','EPSG','8596','EPSG','1379','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8597','NAD27 to WGS 84 (44)','Transformation steps are from NAD27 to NAD83(HARN) (6) (code 8465) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa GA',0);
INSERT INTO "usage" VALUES('EPSG','10689','concatenated_operation','EPSG','8597','EPSG','1380','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8598','NAD27 to WGS 84 (45)','Transformation steps are from NAD27 to NAD83(HARN) (36) (code 8553) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa IL',0);
INSERT INTO "usage" VALUES('EPSG','10690','concatenated_operation','EPSG','8598','EPSG','1382','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8599','NAD27 to WGS 84 (46)','Transformation steps are from NAD27 to NAD83(HARN) (30) (code 8509) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa IN',0);
INSERT INTO "usage" VALUES('EPSG','10691','concatenated_operation','EPSG','8599','EPSG','1383','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8600','NAD27 to WGS 84 (47)','Transformation steps are from NAD27 to NAD83(HARN) (39) (code 8587) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa IA',0);
INSERT INTO "usage" VALUES('EPSG','10692','concatenated_operation','EPSG','8600','EPSG','1384','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8601','NAD27 to WGS 84 (48)','Transformation steps are from NAD27 to NAD83(HARN) (31) (code 8510) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa KS',0);
INSERT INTO "usage" VALUES('EPSG','10693','concatenated_operation','EPSG','8601','EPSG','1385','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8602','NAD27 to WGS 84 (49)','Transformation steps are from NAD27 to NAD83(HARN) (10) (code 8469) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa KY',0);
INSERT INTO "usage" VALUES('EPSG','10694','concatenated_operation','EPSG','8602','EPSG','1386','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8603','NAD27 to WGS 84 (50)','Transformation steps are from NAD27 to NAD83(HARN) (11) (code 8470) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa LA',0);
INSERT INTO "usage" VALUES('EPSG','10695','concatenated_operation','EPSG','8603','EPSG','1387','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8604','NAD27 to WGS 84 (51)','Transformation steps are from NAD27 to NAD83(HARN) (13) (code 8472) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa ME',0);
INSERT INTO "usage" VALUES('EPSG','10696','concatenated_operation','EPSG','8604','EPSG','1388','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8605','NAD27 to WGS 84 (52)','Transformation steps are from NAD27 to NAD83(HARN) (12) (code 8471) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa DE MD',0);
INSERT INTO "usage" VALUES('EPSG','10697','concatenated_operation','EPSG','8605','EPSG','2377','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8606','NAD27 to WGS 84 (53)','Transformation steps are from NAD27 to NAD83(HARN) (17) (code 8476) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa NewEng',0);
INSERT INTO "usage" VALUES('EPSG','10698','concatenated_operation','EPSG','8606','EPSG','2378','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8607','NAD27 to WGS 84 (54)','Transformation steps are from NAD27 to NAD83(HARN) (14) (code 8473) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa MI',0);
INSERT INTO "usage" VALUES('EPSG','10699','concatenated_operation','EPSG','8607','EPSG','1391','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8608','NAD27 to WGS 84 (55)','Transformation steps are from NAD27 to NAD83(HARN) (40) (code 8588) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa MN',0);
INSERT INTO "usage" VALUES('EPSG','10700','concatenated_operation','EPSG','8608','EPSG','1392','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8609','NAD27 to WGS 84 (56)','Transformation steps are from NAD27 to NAD83(HARN) (15) (code 8474) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa MS',0);
INSERT INTO "usage" VALUES('EPSG','10701','concatenated_operation','EPSG','8609','EPSG','1393','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8610','NAD27 to WGS 84 (57)','Transformation steps are from NAD27 to NAD83(HARN) (41) (code 8589) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa MO',0);
INSERT INTO "usage" VALUES('EPSG','10702','concatenated_operation','EPSG','8610','EPSG','1394','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8611','NAD27 to WGS 84 (58)','Transformation steps are from NAD27 to NAD83(HARN) (8) (code 8467) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa ID MT e',0);
INSERT INTO "usage" VALUES('EPSG','10703','concatenated_operation','EPSG','8611','EPSG','2382','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8612','NAD27 to WGS 84 (59)','Transformation steps are from NAD27 to NAD83(HARN) (9) (code 8468) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa ID MT w',0);
INSERT INTO "usage" VALUES('EPSG','10704','concatenated_operation','EPSG','8612','EPSG','2383','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8613','NAD27 to WGS 84 (60)','Transformation steps are from NAD27 to NAD83(HARN) (16) (code 8475) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa NE',0);
INSERT INTO "usage" VALUES('EPSG','10705','concatenated_operation','EPSG','8613','EPSG','1396','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8614','NAD27 to WGS 84 (61)','Transformation steps are from NAD27 to NAD83(HARN) (32) (code 8511) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa NV',0);
INSERT INTO "usage" VALUES('EPSG','10706','concatenated_operation','EPSG','8614','EPSG','1397','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8615','NAD27 to WGS 84 (62)','Transformation steps are from NAD27 to NAD83(HARN) (37) (code 8554) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa NJ',0);
INSERT INTO "usage" VALUES('EPSG','10707','concatenated_operation','EPSG','8615','EPSG','1399','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8616','NAD27 to WGS 84 (63)','Transformation steps are from NAD27 to NAD83(HARN) (18) (code 8477) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa NM',0);
INSERT INTO "usage" VALUES('EPSG','10708','concatenated_operation','EPSG','8616','EPSG','1400','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8617','NAD27 to WGS 84 (64)','Transformation steps are from NAD27 to NAD83(HARN) (19) (code 8478) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa NY',0);
INSERT INTO "usage" VALUES('EPSG','10709','concatenated_operation','EPSG','8617','EPSG','1401','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8618','NAD27 to WGS 84 (65)','Transformation steps are from NAD27 to NAD83(HARN) (20) (code 8479) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa ND',0);
INSERT INTO "usage" VALUES('EPSG','10710','concatenated_operation','EPSG','8618','EPSG','1403','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8619','NAD27 to WGS 84 (66)','Transformation steps are from NAD27 to NAD83(HARN) (33) (code 8512) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa OH',0);
INSERT INTO "usage" VALUES('EPSG','10711','concatenated_operation','EPSG','8619','EPSG','1404','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8620','NAD27 to WGS 84 (67)','Transformation steps are from NAD27 to NAD83(HARN) (21) (code 8480) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa OK',0);
INSERT INTO "usage" VALUES('EPSG','10712','concatenated_operation','EPSG','8620','EPSG','1405','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8621','NAD27 to WGS 84 (68)','Transformation steps are from NAD27 to NAD83(HARN) (27) (code 8487) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa OR WA',0);
INSERT INTO "usage" VALUES('EPSG','10713','concatenated_operation','EPSG','8621','EPSG','2381','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8622','NAD27 to WGS 84 (69)','Transformation steps are from NAD27 to NAD83(HARN) (22) (code 6739) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa SD',0);
INSERT INTO "usage" VALUES('EPSG','10714','concatenated_operation','EPSG','8622','EPSG','1410','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8623','NAD27 to WGS 84 (70)','Transformation steps are from NAD27 to NAD83(HARN) (23) (code 8483) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa TN',0);
INSERT INTO "usage" VALUES('EPSG','10715','concatenated_operation','EPSG','8623','EPSG','1411','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8624','NAD27 to WGS 84 (71)','Transformation steps are from NAD27 to NAD83(HARN) (24) (code 8484) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa TX e',0);
INSERT INTO "usage" VALUES('EPSG','10716','concatenated_operation','EPSG','8624','EPSG','2379','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8625','NAD27 to WGS 84 (72)','Transformation steps are from NAD27 to NAD83(HARN) (25) (code 8485) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa TX w',0);
INSERT INTO "usage" VALUES('EPSG','10717','concatenated_operation','EPSG','8625','EPSG','2380','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8626','NAD27 to WGS 84 (73)','Transformation steps are from NAD27 to NAD83(HARN) (34) (code 8513) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa UT',0);
INSERT INTO "usage" VALUES('EPSG','10718','concatenated_operation','EPSG','8626','EPSG','1413','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8627','NAD27 to WGS 84 (74)','Transformation steps are from NAD27 to NAD83(HARN) (26) (code 8486) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa VA',0);
INSERT INTO "usage" VALUES('EPSG','10719','concatenated_operation','EPSG','8627','EPSG','1415','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8628','NAD27 to WGS 84 (75)','Transformation steps are from NAD27 to NAD83(HARN) (35) (code 8514) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa WV',0);
INSERT INTO "usage" VALUES('EPSG','10720','concatenated_operation','EPSG','8628','EPSG','1417','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8629','NAD27 to WGS 84 (76)','Transformation steps are from NAD27 to NAD83(HARN) (28) (code 8488) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa WI',0);
INSERT INTO "usage" VALUES('EPSG','10721','concatenated_operation','EPSG','8629','EPSG','1418','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8630','NAD27 to WGS 84 (77)','Transformation steps are from NAD27 to NAD83(HARN) (29) (code 8489) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','4267','EPSG','4326',1.5,'EPSG-Usa WY',0);
INSERT INTO "usage" VALUES('EPSG','10722','concatenated_operation','EPSG','8630','EPSG','1419','EPSG','1252');
INSERT INTO "concatenated_operation" VALUES('EPSG','8631','Garoua to WGS 84 (1)','','EPSG','4197','EPSG','4326',6.0,'EPSG-Cmr',0);
INSERT INTO "usage" VALUES('EPSG','10723','concatenated_operation','EPSG','8631','EPSG','2590','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8632','Kousseri to WGS 84 (1)','','EPSG','4198','EPSG','4326',6.0,'EPSG-Cmr',0);
INSERT INTO "usage" VALUES('EPSG','10724','concatenated_operation','EPSG','8632','EPSG','2591','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8633','Yoff to WGS 84 (1)','Derived via WGS72. Can be used as a single positon vector transformation with parameter vaues of dX = -37 m, dY = +157 m, dZ = +89.5 m, rX = rY = 0 sec, rZ = 0.554 sec, dS = 0.219 ppm','EPSG','4310','EPSG','4326',26.0,'EPSG-SEN',0);
INSERT INTO "usage" VALUES('EPSG','10725','concatenated_operation','EPSG','8633','EPSG','1207','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8634','Beduaram to WGS 84 (1)','Derived via WGS72BE. Can be used as a single positon vector transformation with parameter vaues of dX = -101 m, dY = -111 m, dZ = +188.9 m, rX = rY = 0 sec, rZ = 0.814 sec, dS = -0.38 ppm','EPSG','4213','EPSG','4326',16.0,'ELF-Ner SE',0);
INSERT INTO "usage" VALUES('EPSG','10726','concatenated_operation','EPSG','8634','EPSG','2771','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8635','NAD27 to NAD83(CSRS) (3)','Can be taken as an approximate transformation NAD27 to WGS 84 - see code 8585.','EPSG','4267','EPSG','4617',2.5,'EPSG-Can AB',1);
INSERT INTO "usage" VALUES('EPSG','10727','concatenated_operation','EPSG','8635','EPSG','2376','EPSG','1151');
INSERT INTO "concatenated_operation" VALUES('EPSG','8636','Carthage (Paris) to WGS 84 (1)','','EPSG','4816','EPSG','4326',14.0,'EPSG-Tun',0);
INSERT INTO "usage" VALUES('EPSG','10728','concatenated_operation','EPSG','8636','EPSG','1618','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8637','Lisbon (Lisbon) to WGS 84 (1)','','EPSG','4803','EPSG','4326',NULL,'EPSG-Prt',1);
INSERT INTO "usage" VALUES('EPSG','10729','concatenated_operation','EPSG','8637','EPSG','1294','EPSG','1042');
INSERT INTO "concatenated_operation" VALUES('EPSG','8638','Makassar (Jakarta) to WGS 84 (1)','','EPSG','4804','EPSG','4326',999.0,'EPSG - Idn Sul SW',0);
INSERT INTO "usage" VALUES('EPSG','10730','concatenated_operation','EPSG','8638','EPSG','1316','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8639','NGO 1948 (Oslo) to WGS 84 (1)','','EPSG','4817','EPSG','4326',3.0,'EPSG-Nor',0);
INSERT INTO "usage" VALUES('EPSG','10731','concatenated_operation','EPSG','8639','EPSG','1352','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8640','Nord Sahara 1959 (Paris) to WGS 84 (1)','','EPSG','4819','EPSG','4326',NULL,'EPSG-Dza',1);
INSERT INTO "usage" VALUES('EPSG','10732','concatenated_operation','EPSG','8640','EPSG','1026','EPSG','1160');
INSERT INTO "concatenated_operation" VALUES('EPSG','8641','Segara (Jakarta) to WGS 84 (1)','','EPSG','4820','EPSG','4326',999.0,'EPSG-Idn Kal SW',0);
INSERT INTO "usage" VALUES('EPSG','10733','concatenated_operation','EPSG','8641','EPSG','1360','EPSG','1153');
INSERT INTO "concatenated_operation" VALUES('EPSG','8642','S-JTSK (Ferro) to WGS 84 (1)','Replaced by S-JTSK (Ferro) to WGS 84 (3) (code 5242) in 2009.','EPSG','4818','EPSG','4326',1.0,'EPSG-Cze',0);
INSERT INTO "usage" VALUES('EPSG','10734','concatenated_operation','EPSG','8642','EPSG','1079','EPSG','1041');
INSERT INTO "concatenated_operation" VALUES('EPSG','8643','Greek to WGS 84 (1)','','EPSG','4120','EPSG','4326',6.0,'EPSG-Grc',0);
INSERT INTO "usage" VALUES('EPSG','10735','concatenated_operation','EPSG','8643','EPSG','3254','EPSG','1045');
INSERT INTO "concatenated_operation" VALUES('EPSG','8644','Greek (Athens) to WGS 84 (1)','','EPSG','4815','EPSG','4326',5.0,'EPSG-Grc',0);
INSERT INTO "usage" VALUES('EPSG','10736','concatenated_operation','EPSG','8644','EPSG','3254','EPSG','1045');
INSERT INTO "concatenated_operation" VALUES('EPSG','8645','MGI (Ferro) to WGS 84 (2)','','EPSG','4805','EPSG','4326',NULL,'BEV-Aut',1);
INSERT INTO "usage" VALUES('EPSG','10737','concatenated_operation','EPSG','8645','EPSG','1037','EPSG','1042');
INSERT INTO "concatenated_operation" VALUES('EPSG','8646','Manoca 1962 to WGS 84 (1)','Derived via WGS72BE. Can be used as a single positon vector transformation with parameter vaues of dX = -56.7 m, dY = -171.8 m, dZ = -40.6 m, rX = rY = 0 sec, rZ = 0.814 sec, dS = -0.38 ppm','EPSG','4193','EPSG','4326',NULL,'OGP-Cmr',1);
INSERT INTO "usage" VALUES('EPSG','10738','concatenated_operation','EPSG','8646','EPSG','2555','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8647','NAD27 to WGS 84 (78)','','EPSG','4267','EPSG','4326',3.0,'EPSG-Can E Off',0);
INSERT INTO "usage" VALUES('EPSG','10739','concatenated_operation','EPSG','8647','EPSG','2831','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8648','Lisbon 1890 (Lisbon) to WGS 84 (1)','','EPSG','4904','EPSG','4326',5.0,'EPSG-Prt 5m',0);
INSERT INTO "usage" VALUES('EPSG','10740','concatenated_operation','EPSG','8648','EPSG','1294','EPSG','1157');
INSERT INTO "concatenated_operation" VALUES('EPSG','8649','Lisbon 1890 (Lisbon) to WGS 84 (2)','','EPSG','4904','EPSG','4326',1.0,'EPSG-Prt 1m',0);
INSERT INTO "usage" VALUES('EPSG','10741','concatenated_operation','EPSG','8649','EPSG','1294','EPSG','1158');
INSERT INTO "concatenated_operation" VALUES('EPSG','8650','Palestine 1923 to WGS 84 (2)','Accuracy: 1m to north and 10m to south of east-west line through Beersheba (31°15''N). Can be implemented as a geocentric translation tfm with param. Values dX = -229m, dY = -67m, dZ= +277m.','EPSG','4281','EPSG','4326',2.5,'SoI-Isr',0);
INSERT INTO "usage" VALUES('EPSG','10742','concatenated_operation','EPSG','8650','EPSG','2603','EPSG','1153');
INSERT INTO "concatenated_operation" VALUES('EPSG','8651','Vientiane 1982 to WGS 84 (1)','Can be implemented as a geocentric translation tfm with param. values dX = 42.358m, dY = -124.688m, dZ= -37.366m.','EPSG','4676','EPSG','4326',6.0,'EPSG-Lao',0);
INSERT INTO "usage" VALUES('EPSG','10743','concatenated_operation','EPSG','8651','EPSG','1138','EPSG','1045');
INSERT INTO "concatenated_operation" VALUES('EPSG','8652','Lao 1993 to WGS 84 (1)','Can be implemented as a geocentric translation tfm with param. values dX = 43.933m, dY = -129.593m, dZ= -39.331m.','EPSG','4677','EPSG','4326',6.0,'EPSG-Lao',0);
INSERT INTO "usage" VALUES('EPSG','10744','concatenated_operation','EPSG','8652','EPSG','1138','EPSG','1045');
INSERT INTO "concatenated_operation" VALUES('EPSG','8655','Manoca 1962 to WGS 84 (2)','Derived via WGS 72BE. Can be implemented as a single positon vector transformation with parameter vaues of dX = -56.7 m, dY = -171.8 m, dZ = -38.7 m, rX = rY = 0 sec, rZ = 0.814 sec, dS = -0.38 ppm.','EPSG','4193','EPSG','4326',6.0,'OGP-Cmr',0);
INSERT INTO "usage" VALUES('EPSG','10747','concatenated_operation','EPSG','8655','EPSG','2555','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8656','Mhast (offshore) to WGS 84 (1)','Derived via WGS 72BE. Can be implemented as a single positon vector transformation with parameter vaues of dX = -255.0 m, dY = -29.0 m, dZ = -103.1 m, rX = rY = 0 sec, rZ = 0.814 sec, dS = -0.38 ppm.','EPSG','4705','EPSG','4326',11.0,'OGP-Ago Cab',0);
INSERT INTO "usage" VALUES('EPSG','10748','concatenated_operation','EPSG','8656','EPSG','3180','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8657','Egypt Gulf of Suez S-650 TL to WGS 84 (1)','Can be implemented as a single positon vector transformation with parameter vaues of dX = -123.0 m, dY = 98.0 m, dZ = 3.9 m, rX = rY = 0 sec, rZ = 0.814 sec, dS = -0.38 ppm. Replaced by Egypt Gulf of Suez S-650 TL to WGS 84 (2) (tfm code 15846).','EPSG','4706','EPSG','4326',6.0,'OGP-Egy GoS',0);
INSERT INTO "usage" VALUES('EPSG','10749','concatenated_operation','EPSG','8657','EPSG','2341','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','8659','Kertau (RSO) to WGS 84 (1)','Step 1 is necessary to rescale the grid units before using step 2.','EPSG','4751','EPSG','4326',15.0,'OGP-Mys',0);
INSERT INTO "usage" VALUES('EPSG','10751','concatenated_operation','EPSG','8659','EPSG','1309','EPSG','1164');
INSERT INTO "concatenated_operation" VALUES('EPSG','9103','NAD27 to ITRF2014 (1)','For use with legacy data - see CT code 9104 for alternative for new areas. Note that steps 1 and 2 are documented in the geog2D domain, steps 3 and 4 in the geocentric domain. Steps 3 and 4 may be implemented in one operation using CT code 8970.','EPSG','4267','EPSG','7789',1.5,'IOGP-Usa GoM legacy',0);
INSERT INTO "usage" VALUES('EPSG','10917','concatenated_operation','EPSG','9103','EPSG','3357','EPSG','1136');
INSERT INTO "concatenated_operation" VALUES('EPSG','9336','NAD27 to NAD83(CSRS)v4 (3)','Can be taken as an approximate transformation NAD27 to WGS 84 - see code 8585.','EPSG','4267','EPSG','8246',2.5,'EPSG-Can AB',0);
INSERT INTO "usage" VALUES('EPSG','14011','concatenated_operation','EPSG','9336','EPSG','2376','EPSG','1151');
INSERT INTO "concatenated_operation" VALUES('EPSG','9337','NTF (Paris) to RGF93 v1 (1)','See transformation code 7811 for an alternative which uses the NTv2 method as an emulation of the geocentric interpolation in the second step.','EPSG','4807','EPSG','4171',1.0,'IOGP-Fra',0);
INSERT INTO "usage" VALUES('EPSG','14012','concatenated_operation','EPSG','9337','EPSG','3694','EPSG','1041');
INSERT INTO "concatenated_operation" VALUES('EPSG','9499','ETRS89 to GHA height (2)','This concatenated operation gives the same result as the HoehenGrid-plus offset from ETRS89 to GHA height. HoehenGrid-plus is implemented in BEV-Transformator using MGI (CRS 4312) as the interpolation CRS for the grid','EPSG','4937','EPSG','5778',0.07,'BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','14958','concatenated_operation','EPSG','9499','EPSG','1037','EPSG','1133');
INSERT INTO "concatenated_operation" VALUES('EPSG','9683','ITRF2014 to GDA94 (2)','See ITRF2014 to GDA94 (1) (CT 9682) for conformal-only alternative (i.e. without distortion modelling).','EPSG','9000','EPSG','4283',0.06,'ICSM-Aus Conf+Dist',0);
INSERT INTO "usage" VALUES('EPSG','14962','concatenated_operation','EPSG','9683','EPSG','2575','EPSG','1234');
INSERT INTO "concatenated_operation" VALUES('EPSG','9685','ATRF2014 to GDA94 (2)','See ATRF2014 to GDA94 (1) (CT 9684) for conformal-only alternative (i.e. without distortion modelling).','EPSG','9309','EPSG','4283',0.06,'ICSM-Aus Conf+Dist',0);
INSERT INTO "usage" VALUES('EPSG','14963','concatenated_operation','EPSG','9685','EPSG','2575','EPSG','1234');
INSERT INTO "concatenated_operation" VALUES('EPSG','9687','GDA94 to WGS 84 (G1762) (2)','See GDA94 to WGS 84 (G1762) (1) (CT code 9686) for conformal-only alternative (i.e. without distortion modelling).','EPSG','4283','EPSG','9057',0.25,'ICSM-Aus Conf+Dist',0);
INSERT INTO "usage" VALUES('EPSG','14964','concatenated_operation','EPSG','9687','EPSG','2575','EPSG','1234');
INSERT INTO "concatenated_operation" VALUES('EPSG','9731','ETRS89 to ETRS89 + Catania 1965 height (1)','','EPSG','4937','EPSG','9724',0.035,'IGM-Ita 2005 Sicily',1);
INSERT INTO "usage" VALUES('EPSG','15285','concatenated_operation','EPSG','9731','EPSG','2340','EPSG','1270');
INSERT INTO "concatenated_operation" VALUES('EPSG','9750','ETRS89 to Catania 1965 height (1)','','EPSG','4937','EPSG','9721',0.035,'IGM-Ita 2005 Sicily',0);
INSERT INTO "usage" VALUES('EPSG','15385','concatenated_operation','EPSG','9750','EPSG','2340','EPSG','1133');
