--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "compound_crs" VALUES('EPSG','3901','KKJ / Finland Uniform Coordinate System + N60 height',NULL,'EPSG','2393','EPSG','5717',0);
INSERT INTO "usage" VALUES('EPSG','2881','compound_crs','EPSG','3901','EPSG','3333','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','3902','ETRS89 / TM35FIN(N,E) + N60 height',NULL,'EPSG','5048','EPSG','5717',0);
INSERT INTO "usage" VALUES('EPSG','2882','compound_crs','EPSG','3902','EPSG','3333','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','3903','ETRS89 / TM35FIN(N,E) + N2000 height',NULL,'EPSG','5048','EPSG','3900',0);
INSERT INTO "usage" VALUES('EPSG','2883','compound_crs','EPSG','3903','EPSG','3333','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','4097','ETRS89 / DKTM1 + DVR90 height',NULL,'EPSG','4093','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','3001','compound_crs','EPSG','4097','EPSG','3631','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','4098','ETRS89 / DKTM2 + DVR90 height',NULL,'EPSG','4094','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','3002','compound_crs','EPSG','4098','EPSG','3632','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','4099','ETRS89 / DKTM3 + DVR90 height',NULL,'EPSG','4095','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','3003','compound_crs','EPSG','4099','EPSG','2532','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','4100','ETRS89 / DKTM4 + DVR90 height',NULL,'EPSG','4096','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','3004','compound_crs','EPSG','4100','EPSG','2533','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5318','ETRS89 / Faroe TM + FVR09 height',NULL,'EPSG','5316','EPSG','5317',0);
INSERT INTO "usage" VALUES('EPSG','3925','compound_crs','EPSG','5318','EPSG','3248','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5498','NAD83 + NAVD88 height',NULL,'EPSG','4269','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','4003','compound_crs','EPSG','5498','EPSG','3664','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5499','NAD83(HARN) + NAVD88 height',NULL,'EPSG','4152','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','4004','compound_crs','EPSG','5499','EPSG','1323','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5500','NAD83(NSRS2007) + NAVD88 height',NULL,'EPSG','4759','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','4005','compound_crs','EPSG','5500','EPSG','1323','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5554','ETRS89 / UTM zone 31N + DHHN92 height',NULL,'EPSG','25831','EPSG','5783',0);
INSERT INTO "usage" VALUES('EPSG','4032','compound_crs','EPSG','5554','EPSG','3901','EPSG','1190');
INSERT INTO "compound_crs" VALUES('EPSG','5555','ETRS89 / UTM zone 32N + DHHN92 height',NULL,'EPSG','25832','EPSG','5783',0);
INSERT INTO "usage" VALUES('EPSG','4033','compound_crs','EPSG','5555','EPSG','3904','EPSG','1190');
INSERT INTO "compound_crs" VALUES('EPSG','5556','ETRS89 / UTM zone 33N + DHHN92 height',NULL,'EPSG','25833','EPSG','5783',0);
INSERT INTO "usage" VALUES('EPSG','4034','compound_crs','EPSG','5556','EPSG','3879','EPSG','1190');
INSERT INTO "compound_crs" VALUES('EPSG','5598','FEH2010 / Fehmarnbelt TM + FCSVR10 height',NULL,'EPSG','5596','EPSG','5597',0);
INSERT INTO "usage" VALUES('EPSG','4068','compound_crs','EPSG','5598','EPSG','3890','EPSG','1139');
INSERT INTO "compound_crs" VALUES('EPSG','5628','SWEREF99 + RH2000 height',NULL,'EPSG','4619','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4095','compound_crs','EPSG','5628','EPSG','3313','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5698','RGF93 v1 / Lambert-93 + NGF-IGN69 height',NULL,'EPSG','2154','EPSG','5720',0);
INSERT INTO "usage" VALUES('EPSG','4141','compound_crs','EPSG','5698','EPSG','1326','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5699','RGF93 v1 / Lambert-93 + NGF-IGN78 height',NULL,'EPSG','2154','EPSG','5721',0);
INSERT INTO "usage" VALUES('EPSG','4142','compound_crs','EPSG','5699','EPSG','1327','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5707','NTF (Paris) / Lambert zone I + NGF-IGN69 height',NULL,'EPSG','27571','EPSG','5720',0);
INSERT INTO "usage" VALUES('EPSG','4150','compound_crs','EPSG','5707','EPSG','1731','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5708','NTF (Paris) / Lambert zone IV + NGF-IGN78 height',NULL,'EPSG','27574','EPSG','5721',0);
INSERT INTO "usage" VALUES('EPSG','4151','compound_crs','EPSG','5708','EPSG','1327','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5832','DB_REF / 3-degree Gauss-Kruger zone 2 (E-N) + DHHN92 height',NULL,'EPSG','5682','EPSG','5783',0);
INSERT INTO "usage" VALUES('EPSG','4270','compound_crs','EPSG','5832','EPSG','1624','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','5833','DB_REF / 3-degree Gauss-Kruger zone 3 (E-N) + DHHN92 height',NULL,'EPSG','5683','EPSG','5783',0);
INSERT INTO "usage" VALUES('EPSG','4271','compound_crs','EPSG','5833','EPSG','3993','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','5834','DB_REF / 3-degree Gauss-Kruger zone 4 (E-N) + DHHN92 height',NULL,'EPSG','5684','EPSG','5783',0);
INSERT INTO "usage" VALUES('EPSG','4272','compound_crs','EPSG','5834','EPSG','3996','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','5835','DB_REF / 3-degree Gauss-Kruger zone 5 (E-N) + DHHN92 height',NULL,'EPSG','5685','EPSG','5783',0);
INSERT INTO "usage" VALUES('EPSG','4273','compound_crs','EPSG','5835','EPSG','3998','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','5845','SWEREF99 TM + RH2000 height',NULL,'EPSG','3006','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4280','compound_crs','EPSG','5845','EPSG','3313','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5846','SWEREF99 12 00 + RH2000 height',NULL,'EPSG','3007','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4281','compound_crs','EPSG','5846','EPSG','2833','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5847','SWEREF99 13 30 + RH2000 height',NULL,'EPSG','3008','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4282','compound_crs','EPSG','5847','EPSG','2834','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5848','SWEREF99 15 00 + RH2000 height',NULL,'EPSG','3009','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4283','compound_crs','EPSG','5848','EPSG','2835','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5849','SWEREF99 16 30 + RH2000 height',NULL,'EPSG','3010','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4284','compound_crs','EPSG','5849','EPSG','2836','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5850','SWEREF99 18 00 + RH2000 height',NULL,'EPSG','3011','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4285','compound_crs','EPSG','5850','EPSG','2837','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5851','SWEREF99 14 15 + RH2000 height',NULL,'EPSG','3012','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4286','compound_crs','EPSG','5851','EPSG','2838','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5852','SWEREF99 15 45 + RH2000 height',NULL,'EPSG','3013','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4287','compound_crs','EPSG','5852','EPSG','2839','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5853','SWEREF99 17 15 + RH2000 height',NULL,'EPSG','3014','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4288','compound_crs','EPSG','5853','EPSG','2840','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5854','SWEREF99 18 45 + RH2000 height',NULL,'EPSG','3015','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4289','compound_crs','EPSG','5854','EPSG','2841','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5855','SWEREF99 20 15 + RH2000 height',NULL,'EPSG','3016','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4290','compound_crs','EPSG','5855','EPSG','2842','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5856','SWEREF99 21 45 + RH2000 height',NULL,'EPSG','3017','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4291','compound_crs','EPSG','5856','EPSG','2843','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5857','SWEREF99 23 15 + RH2000 height',NULL,'EPSG','3018','EPSG','5613',0);
INSERT INTO "usage" VALUES('EPSG','4292','compound_crs','EPSG','5857','EPSG','2844','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','5942','ETRS89 + NN2000 height',NULL,'EPSG','4258','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4346','compound_crs','EPSG','5942','EPSG','1352','EPSG','1027');
INSERT INTO "compound_crs" VALUES('EPSG','5945','ETRS89 / NTM zone 5 + NN2000 height',NULL,'EPSG','5105','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4347','compound_crs','EPSG','5945','EPSG','3636','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5946','ETRS89 / NTM zone 6 + NN2000 height',NULL,'EPSG','5106','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4348','compound_crs','EPSG','5946','EPSG','3639','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5947','ETRS89 / NTM zone 7 + NN2000 height',NULL,'EPSG','5107','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4349','compound_crs','EPSG','5947','EPSG','3647','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5948','ETRS89 / NTM zone 8 + NN2000 height',NULL,'EPSG','5108','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4350','compound_crs','EPSG','5948','EPSG','3648','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5949','ETRS89 / NTM zone 9 + NN2000 height',NULL,'EPSG','5109','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4351','compound_crs','EPSG','5949','EPSG','3649','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5950','ETRS89 / NTM zone 10 + NN2000 height',NULL,'EPSG','5110','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4352','compound_crs','EPSG','5950','EPSG','3650','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5951','ETRS89 / NTM zone 11 + NN2000 height',NULL,'EPSG','5111','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4353','compound_crs','EPSG','5951','EPSG','3651','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5952','ETRS89 / NTM zone 12 + NN2000 height',NULL,'EPSG','5112','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4354','compound_crs','EPSG','5952','EPSG','3653','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5953','ETRS89 / NTM zone 13 + NN2000 height',NULL,'EPSG','5113','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4355','compound_crs','EPSG','5953','EPSG','3654','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5954','ETRS89 / NTM zone 14 + NN2000 height',NULL,'EPSG','5114','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4356','compound_crs','EPSG','5954','EPSG','3655','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5955','ETRS89 / NTM zone 15 + NN2000 height',NULL,'EPSG','5115','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4357','compound_crs','EPSG','5955','EPSG','3656','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5956','ETRS89 / NTM zone 16 + NN2000 height',NULL,'EPSG','5116','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4358','compound_crs','EPSG','5956','EPSG','3657','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5957','ETRS89 / NTM zone 17 + NN2000 height',NULL,'EPSG','5117','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4359','compound_crs','EPSG','5957','EPSG','3658','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5958','ETRS89 / NTM zone 18 + NN2000 height',NULL,'EPSG','5118','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4360','compound_crs','EPSG','5958','EPSG','3660','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5959','ETRS89 / NTM zone 19 + NN2000 height',NULL,'EPSG','5119','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4361','compound_crs','EPSG','5959','EPSG','3661','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5960','ETRS89 / NTM zone 20 + NN2000 height',NULL,'EPSG','5120','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4362','compound_crs','EPSG','5960','EPSG','3662','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5961','ETRS89 / NTM zone 21 + NN2000 height',NULL,'EPSG','5121','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4363','compound_crs','EPSG','5961','EPSG','3663','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5962','ETRS89 / NTM zone 22 + NN2000 height',NULL,'EPSG','5122','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4364','compound_crs','EPSG','5962','EPSG','3665','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5963','ETRS89 / NTM zone 23 + NN2000 height',NULL,'EPSG','5123','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4365','compound_crs','EPSG','5963','EPSG','3667','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5964','ETRS89 / NTM zone 24 + NN2000 height',NULL,'EPSG','5124','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4366','compound_crs','EPSG','5964','EPSG','3668','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5965','ETRS89 / NTM zone 25 + NN2000 height',NULL,'EPSG','5125','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4367','compound_crs','EPSG','5965','EPSG','3669','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5966','ETRS89 / NTM zone 26 + NN2000 height',NULL,'EPSG','5126','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4368','compound_crs','EPSG','5966','EPSG','3671','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5967','ETRS89 / NTM zone 27 + NN2000 height',NULL,'EPSG','5127','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4369','compound_crs','EPSG','5967','EPSG','3672','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5968','ETRS89 / NTM zone 28 + NN2000 height',NULL,'EPSG','5128','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4370','compound_crs','EPSG','5968','EPSG','3673','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5969','ETRS89 / NTM zone 29 + NN2000 height',NULL,'EPSG','5129','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4371','compound_crs','EPSG','5969','EPSG','3674','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5970','ETRS89 / NTM zone 30 + NN2000 height',NULL,'EPSG','5130','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4372','compound_crs','EPSG','5970','EPSG','3676','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','5971','ETRS89 / UTM zone 31N + NN2000 height',NULL,'EPSG','25831','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4373','compound_crs','EPSG','5971','EPSG','3636','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','5972','ETRS89 / UTM zone 32N + NN2000 height',NULL,'EPSG','25832','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4374','compound_crs','EPSG','5972','EPSG','4066','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','5973','ETRS89 / UTM zone 33N + NN2000 height',NULL,'EPSG','25833','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4375','compound_crs','EPSG','5973','EPSG','4067','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','5974','ETRS89 / UTM zone 34N + NN2000 height',NULL,'EPSG','25834','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4376','compound_crs','EPSG','5974','EPSG','4068','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','5975','ETRS89 / UTM zone 35N + NN2000 height',NULL,'EPSG','25835','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4377','compound_crs','EPSG','5975','EPSG','4069','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','5976','ETRS89 / UTM zone 36N + NN2000 height',NULL,'EPSG','25836','EPSG','5941',0);
INSERT INTO "usage" VALUES('EPSG','4378','compound_crs','EPSG','5976','EPSG','3676','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','6144','ETRS89 + NN54 height',NULL,'EPSG','4258','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4464','compound_crs','EPSG','6144','EPSG','1352','EPSG','1027');
INSERT INTO "compound_crs" VALUES('EPSG','6145','ETRS89 / NTM zone 5 + NN54 height',NULL,'EPSG','5105','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4465','compound_crs','EPSG','6145','EPSG','3636','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6146','ETRS89 / NTM zone 6 + NN54 height',NULL,'EPSG','5106','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4466','compound_crs','EPSG','6146','EPSG','3639','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6147','ETRS89 / NTM zone 7 + NN54 height',NULL,'EPSG','5107','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4467','compound_crs','EPSG','6147','EPSG','3647','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6148','ETRS89 / NTM zone 8 + NN54 height',NULL,'EPSG','5108','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4468','compound_crs','EPSG','6148','EPSG','3648','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6149','ETRS89 / NTM zone 9 + NN54 height',NULL,'EPSG','5109','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4469','compound_crs','EPSG','6149','EPSG','3649','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6150','ETRS89 / NTM zone 10 + NN54 height',NULL,'EPSG','5110','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4470','compound_crs','EPSG','6150','EPSG','3650','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6151','ETRS89 / NTM zone 11 + NN54 height',NULL,'EPSG','5111','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4471','compound_crs','EPSG','6151','EPSG','3651','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6152','ETRS89 / NTM zone 12 + NN54 height',NULL,'EPSG','5112','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4472','compound_crs','EPSG','6152','EPSG','3653','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6153','ETRS89 / NTM zone 13 + NN54 height',NULL,'EPSG','5113','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4473','compound_crs','EPSG','6153','EPSG','3654','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6154','ETRS89 / NTM zone 14 + NN54 height',NULL,'EPSG','5114','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4474','compound_crs','EPSG','6154','EPSG','3655','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6155','ETRS89 / NTM zone 15 + NN54 height',NULL,'EPSG','5115','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4475','compound_crs','EPSG','6155','EPSG','3656','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6156','ETRS89 / NTM zone 16 + NN54 height',NULL,'EPSG','5116','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4476','compound_crs','EPSG','6156','EPSG','3657','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6157','ETRS89 / NTM zone 17 + NN54 height',NULL,'EPSG','5117','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4477','compound_crs','EPSG','6157','EPSG','3658','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6158','ETRS89 / NTM zone 18 + NN54 height',NULL,'EPSG','5118','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4478','compound_crs','EPSG','6158','EPSG','3660','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6159','ETRS89 / NTM zone 19 + NN54 height',NULL,'EPSG','5119','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4479','compound_crs','EPSG','6159','EPSG','3661','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6160','ETRS89 / NTM zone 20 + NN54 height',NULL,'EPSG','5120','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4480','compound_crs','EPSG','6160','EPSG','3662','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6161','ETRS89 / NTM zone 21 + NN54 height',NULL,'EPSG','5121','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4481','compound_crs','EPSG','6161','EPSG','3663','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6162','ETRS89 / NTM zone 22 + NN54 height',NULL,'EPSG','5122','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4482','compound_crs','EPSG','6162','EPSG','3665','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6163','ETRS89 / NTM zone 23 + NN54 height',NULL,'EPSG','5123','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4483','compound_crs','EPSG','6163','EPSG','3667','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6164','ETRS89 / NTM zone 24 + NN54 height',NULL,'EPSG','5124','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4484','compound_crs','EPSG','6164','EPSG','3668','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6165','ETRS89 / NTM zone 25 + NN54 height',NULL,'EPSG','5125','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4485','compound_crs','EPSG','6165','EPSG','3669','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6166','ETRS89 / NTM zone 26 + NN54 height',NULL,'EPSG','5126','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4486','compound_crs','EPSG','6166','EPSG','3671','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6167','ETRS89 / NTM zone 27 + NN54 height',NULL,'EPSG','5127','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4487','compound_crs','EPSG','6167','EPSG','3672','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6168','ETRS89 / NTM zone 28 + NN54 height',NULL,'EPSG','5128','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4488','compound_crs','EPSG','6168','EPSG','3673','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6169','ETRS89 / NTM zone 29 + NN54 height',NULL,'EPSG','5129','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4489','compound_crs','EPSG','6169','EPSG','3674','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6170','ETRS89 / NTM zone 30 + NN54 height',NULL,'EPSG','5130','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4490','compound_crs','EPSG','6170','EPSG','3676','EPSG','1029');
INSERT INTO "compound_crs" VALUES('EPSG','6171','ETRS89 / UTM zone 31N + NN54 height',NULL,'EPSG','25831','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4491','compound_crs','EPSG','6171','EPSG','3636','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','6172','ETRS89 / UTM zone 32N + NN54 height',NULL,'EPSG','25832','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4492','compound_crs','EPSG','6172','EPSG','4066','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','6173','ETRS89 / UTM zone 33N + NN54 height',NULL,'EPSG','25833','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4493','compound_crs','EPSG','6173','EPSG','4067','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','6174','ETRS89 / UTM zone 34N + NN54 height',NULL,'EPSG','25834','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4494','compound_crs','EPSG','6174','EPSG','4068','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','6175','ETRS89 / UTM zone 35N + NN54 height',NULL,'EPSG','25835','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4495','compound_crs','EPSG','6175','EPSG','4069','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','6176','ETRS89 / UTM zone 36N + NN54 height',NULL,'EPSG','25836','EPSG','5776',0);
INSERT INTO "usage" VALUES('EPSG','4496','compound_crs','EPSG','6176','EPSG','3676','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','6190','BD72 / Belgian Lambert 72 + Ostend height',NULL,'EPSG','31370','EPSG','5710',0);
INSERT INTO "usage" VALUES('EPSG','4507','compound_crs','EPSG','6190','EPSG','1347','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6349','NAD83(2011) + NAVD88 height',NULL,'EPSG','6318','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','4583','compound_crs','EPSG','6349','EPSG','3664','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6649','NAD83(CSRS) + CGVD2013 height',NULL,'EPSG','4617','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4868','compound_crs','EPSG','6649','EPSG','1061','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6650','NAD83(CSRS) / UTM zone 7N + CGVD2013 height',NULL,'EPSG','3154','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4869','compound_crs','EPSG','6650','EPSG','3409','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6651','NAD83(CSRS) / UTM zone 8N + CGVD2013 height',NULL,'EPSG','3155','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4870','compound_crs','EPSG','6651','EPSG','3410','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6652','NAD83(CSRS) / UTM zone 9N + CGVD2013 height',NULL,'EPSG','3156','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4871','compound_crs','EPSG','6652','EPSG','3411','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6653','NAD83(CSRS) / UTM zone 10N + CGVD2013 height',NULL,'EPSG','3157','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4872','compound_crs','EPSG','6653','EPSG','3412','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6654','NAD83(CSRS) / UTM zone 11N + CGVD2013 height',NULL,'EPSG','2955','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4873','compound_crs','EPSG','6654','EPSG','3528','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6655','NAD83(CSRS) / UTM zone 12N + CGVD2013 height',NULL,'EPSG','2956','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4874','compound_crs','EPSG','6655','EPSG','3527','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6656','NAD83(CSRS) / UTM zone 13N + CGVD2013 height',NULL,'EPSG','2957','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4875','compound_crs','EPSG','6656','EPSG','3526','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6657','NAD83(CSRS) / UTM zone 14N + CGVD2013 height',NULL,'EPSG','3158','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4876','compound_crs','EPSG','6657','EPSG','3413','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6658','NAD83(CSRS) / UTM zone 15N + CGVD2013 height',NULL,'EPSG','3159','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4877','compound_crs','EPSG','6658','EPSG','3414','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6659','NAD83(CSRS) / UTM zone 16N + CGVD2013 height',NULL,'EPSG','3160','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4878','compound_crs','EPSG','6659','EPSG','3415','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6660','NAD83(CSRS) / UTM zone 17N + CGVD2013 height',NULL,'EPSG','2958','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4879','compound_crs','EPSG','6660','EPSG','3416','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6661','NAD83(CSRS) / UTM zone 18N + CGVD2013 height',NULL,'EPSG','2959','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4880','compound_crs','EPSG','6661','EPSG','3417','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6662','NAD83(CSRS) / UTM zone 19N + CGVD2013 height',NULL,'EPSG','2960','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4881','compound_crs','EPSG','6662','EPSG','3524','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6663','NAD83(CSRS) / UTM zone 20N + CGVD2013 height',NULL,'EPSG','2961','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4882','compound_crs','EPSG','6663','EPSG','3525','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6664','NAD83(CSRS) / UTM zone 21N + CGVD2013 height',NULL,'EPSG','2962','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4883','compound_crs','EPSG','6664','EPSG','2151','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6665','NAD83(CSRS) / UTM zone 22N + CGVD2013 height',NULL,'EPSG','3761','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','4884','compound_crs','EPSG','6665','EPSG','2152','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6696','JGD2000 + JGD2000 (vertical) height',NULL,'EPSG','4612','EPSG','6694',0);
INSERT INTO "usage" VALUES('EPSG','4915','compound_crs','EPSG','6696','EPSG','3263','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6697','JGD2011 + JGD2011 (vertical) height',NULL,'EPSG','6668','EPSG','6695',0);
INSERT INTO "usage" VALUES('EPSG','4916','compound_crs','EPSG','6697','EPSG','3263','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6700','Tokyo + JSLD72 height',NULL,'EPSG','4301','EPSG','6693',0);
INSERT INTO "usage" VALUES('EPSG','4917','compound_crs','EPSG','6700','EPSG','4168','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','6871','WGS 84 / Pseudo-Mercator +  EGM2008 geoid height',NULL,'EPSG','3857','EPSG','3855',1);
INSERT INTO "usage" VALUES('EPSG','5023','compound_crs','EPSG','6871','EPSG','1262','EPSG','1229');
INSERT INTO "compound_crs" VALUES('EPSG','6893','WGS 84 / World Mercator + EGM2008 height',NULL,'EPSG','3395','EPSG','3855',0);
INSERT INTO "usage" VALUES('EPSG','5036','compound_crs','EPSG','6893','EPSG','1262','EPSG','1229');
INSERT INTO "compound_crs" VALUES('EPSG','6917','SVY21 + SHD height',NULL,'EPSG','4757','EPSG','6916',0);
INSERT INTO "usage" VALUES('EPSG','5040','compound_crs','EPSG','6917','EPSG','1210','EPSG','1144');
INSERT INTO "compound_crs" VALUES('EPSG','6927','SVY21 / Singapore TM + SHD height',NULL,'EPSG','3414','EPSG','6916',0);
INSERT INTO "usage" VALUES('EPSG','5045','compound_crs','EPSG','6927','EPSG','1210','EPSG','1144');
INSERT INTO "compound_crs" VALUES('EPSG','7400','NTF (Paris) + NGF IGN69 height',NULL,'EPSG','4807','EPSG','5720',0);
INSERT INTO "usage" VALUES('EPSG','5265','compound_crs','EPSG','7400','EPSG','1326','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7401','NTF (Paris) / France II + NGF Lallemand',NULL,'EPSG','27582','EPSG','5719',1);
INSERT INTO "usage" VALUES('EPSG','5266','compound_crs','EPSG','7401','EPSG','1326','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7402','NTF (Paris) / France II + NGF IGN69',NULL,'EPSG','27582','EPSG','5720',1);
INSERT INTO "usage" VALUES('EPSG','5267','compound_crs','EPSG','7402','EPSG','1326','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7403','NTF (Paris) / France III + NGF IGN69',NULL,'EPSG','27583','EPSG','5720',1);
INSERT INTO "usage" VALUES('EPSG','5268','compound_crs','EPSG','7403','EPSG','1733','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7404','RT90 + RH70 height',NULL,'EPSG','4124','EPSG','5718',0);
INSERT INTO "usage" VALUES('EPSG','5269','compound_crs','EPSG','7404','EPSG','3313','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7405','OSGB36 / British National Grid + ODN height',NULL,'EPSG','27700','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','5270','compound_crs','EPSG','7405','EPSG','2792','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7406','NAD27 + NGVD29 height (ftUS)',NULL,'EPSG','4267','EPSG','5702',0);
INSERT INTO "usage" VALUES('EPSG','5271','compound_crs','EPSG','7406','EPSG','1323','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7407','NAD27 / Texas North + NGVD29 height (ftUS)',NULL,'EPSG','32037','EPSG','5702',0);
INSERT INTO "usage" VALUES('EPSG','5272','compound_crs','EPSG','7407','EPSG','2253','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7408','RD/NAP',NULL,'EPSG','4289','EPSG','5709',1);
INSERT INTO "usage" VALUES('EPSG','5273','compound_crs','EPSG','7408','EPSG','1275','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7409','ETRS89 + EVRF2000 height',NULL,'EPSG','4258','EPSG','5730',0);
INSERT INTO "usage" VALUES('EPSG','5274','compound_crs','EPSG','7409','EPSG','1299','EPSG','1161');
INSERT INTO "compound_crs" VALUES('EPSG','7410','PSHD93',NULL,'EPSG','4134','EPSG','5724',0);
INSERT INTO "usage" VALUES('EPSG','5275','compound_crs','EPSG','7410','EPSG','3288','EPSG','1136');
INSERT INTO "compound_crs" VALUES('EPSG','7411','NTF (Paris) / Lambert zone II + NGF Lallemand height',NULL,'EPSG','27572','EPSG','5719',0);
INSERT INTO "usage" VALUES('EPSG','5276','compound_crs','EPSG','7411','EPSG','1326','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7412','NTF (Paris) / Lambert zone II + NGF IGN69',NULL,'EPSG','27572','EPSG','5719',1);
INSERT INTO "usage" VALUES('EPSG','5277','compound_crs','EPSG','7412','EPSG','1326','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7413','NTF (Paris) / Lambert zone III + NGF IGN69',NULL,'EPSG','27573','EPSG','5719',1);
INSERT INTO "usage" VALUES('EPSG','5278','compound_crs','EPSG','7413','EPSG','1733','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7414','Tokyo + JSLD69 height',NULL,'EPSG','4301','EPSG','5723',0);
INSERT INTO "usage" VALUES('EPSG','5279','compound_crs','EPSG','7414','EPSG','4166','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7415','Amersfoort / RD New + NAP height',NULL,'EPSG','28992','EPSG','5709',0);
INSERT INTO "usage" VALUES('EPSG','5280','compound_crs','EPSG','7415','EPSG','1275','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7416','ETRS89 / UTM zone 32N + DVR90 height',NULL,'EPSG','25832','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','5281','compound_crs','EPSG','7416','EPSG','3471','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7417','ETRS89 / UTM zone 33N + DVR90 height',NULL,'EPSG','25833','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','5282','compound_crs','EPSG','7417','EPSG','3472','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7418','ETRS89 / Kp2000 Jutland + DVR90 height',NULL,'EPSG','2196','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','5283','compound_crs','EPSG','7418','EPSG','2531','EPSG','1209');
INSERT INTO "compound_crs" VALUES('EPSG','7419','ETRS89 / Kp2000 Zealand + DVR90 height',NULL,'EPSG','2197','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','5284','compound_crs','EPSG','7419','EPSG','2532','EPSG','1209');
INSERT INTO "compound_crs" VALUES('EPSG','7420','ETRS89 / Kp2000 Bornholm + DVR90 height',NULL,'EPSG','2198','EPSG','5799',0);
INSERT INTO "usage" VALUES('EPSG','5285','compound_crs','EPSG','7420','EPSG','2533','EPSG','1209');
INSERT INTO "compound_crs" VALUES('EPSG','7421','NTF (Paris) / Lambert zone II + NGF-IGN69 height',NULL,'EPSG','27572','EPSG','5720',0);
INSERT INTO "usage" VALUES('EPSG','5286','compound_crs','EPSG','7421','EPSG','1326','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7422','NTF (Paris) / Lambert zone III + NGF-IGN69 height',NULL,'EPSG','27573','EPSG','5720',0);
INSERT INTO "usage" VALUES('EPSG','5287','compound_crs','EPSG','7422','EPSG','1733','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','7423','ETRS89 + EVRF2007 height',NULL,'EPSG','4258','EPSG','5621',0);
INSERT INTO "usage" VALUES('EPSG','5288','compound_crs','EPSG','7423','EPSG','3594','EPSG','1161');
INSERT INTO "compound_crs" VALUES('EPSG','7954','Astro DOS 71 / UTM zone 30S + Jamestown 1971 height',NULL,'EPSG','7878','EPSG','7888',0);
INSERT INTO "usage" VALUES('EPSG','5565','compound_crs','EPSG','7954','EPSG','3183','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','7955','St. Helena Tritan / UTM zone 30S + Tritan 2011 height',NULL,'EPSG','7883','EPSG','7889',0);
INSERT INTO "usage" VALUES('EPSG','5566','compound_crs','EPSG','7955','EPSG','3183','EPSG','1146');
INSERT INTO "compound_crs" VALUES('EPSG','7956','SHMG2015 + SHVD2015 height',NULL,'EPSG','7887','EPSG','7890',0);
INSERT INTO "usage" VALUES('EPSG','5567','compound_crs','EPSG','7956','EPSG','3183','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','8349','GR96 + GVR2000 height',NULL,'EPSG','4747','EPSG','8266',0);
INSERT INTO "usage" VALUES('EPSG','5798','compound_crs','EPSG','8349','EPSG','4461','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','8350','GR96 + GVR2016 height',NULL,'EPSG','4747','EPSG','8267',0);
INSERT INTO "usage" VALUES('EPSG','5799','compound_crs','EPSG','8350','EPSG','4454','EPSG','1153');
INSERT INTO "compound_crs" VALUES('EPSG','8360','ETRS89 + Baltic 1957 height',NULL,'EPSG','4258','EPSG','8357',0);
INSERT INTO "usage" VALUES('EPSG','5805','compound_crs','EPSG','8360','EPSG','1306','EPSG','1203');
INSERT INTO "compound_crs" VALUES('EPSG','8370','ETRS89 / Belgian Lambert 2008 + Ostend height',NULL,'EPSG','3812','EPSG','5710',0);
INSERT INTO "usage" VALUES('EPSG','5806','compound_crs','EPSG','8370','EPSG','1347','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8700','NAD83 / Arizona East (ft) + NAVD88 height (ft)',NULL,'EPSG','2222','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5879','compound_crs','EPSG','8700','EPSG','2167','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8701','NAD83 / Arizona Central (ft) + NAVD88 height (ft)',NULL,'EPSG','2223','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5880','compound_crs','EPSG','8701','EPSG','2166','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8702','NAD83 / Arizona West (ft) + NAVD88 height (ft)',NULL,'EPSG','2224','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5881','compound_crs','EPSG','8702','EPSG','2168','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8703','NAD83 / Michigan North (ft) + NAVD88 height (ft)',NULL,'EPSG','2251','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5882','compound_crs','EPSG','8703','EPSG','1723','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8704','NAD83 / Michigan Central (ft) + NAVD88 height (ft)',NULL,'EPSG','2252','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5883','compound_crs','EPSG','8704','EPSG','1724','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8705','NAD83 / Michigan South (ft) + NAVD88 height (ft)',NULL,'EPSG','2253','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5884','compound_crs','EPSG','8705','EPSG','1725','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8706','NAD83 / Montana (ft) + NAVD88 height (ft)',NULL,'EPSG','2256','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5885','compound_crs','EPSG','8706','EPSG','1395','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8707','NAD83 / North Dakota North (ft) + NAVD88 height (ft)',NULL,'EPSG','2265','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5886','compound_crs','EPSG','8707','EPSG','2237','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8708','NAD83 / North Dakota South (ft) + NAVD88 height (ft)',NULL,'EPSG','2266','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5887','compound_crs','EPSG','8708','EPSG','2238','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8709','NAD83 / Oregon North (ft) + NAVD88 height (ft)',NULL,'EPSG','2269','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5888','compound_crs','EPSG','8709','EPSG','2243','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8710','NAD83 / Oregon South (ft) + NAVD88 height (ft)',NULL,'EPSG','2270','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5889','compound_crs','EPSG','8710','EPSG','2244','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8711','NAD83 / South Carolina (ft) + NAVD88 height (ft)',NULL,'EPSG','2273','EPSG','8228',0);
INSERT INTO "usage" VALUES('EPSG','5890','compound_crs','EPSG','8711','EPSG','1409','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8712','NAD83 / Arkansas North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3433','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5891','compound_crs','EPSG','8712','EPSG','2169','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8713','NAD83 / Arkansas South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3434','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5892','compound_crs','EPSG','8713','EPSG','2170','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8714','NAD83 / California zone 1 (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2225','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5893','compound_crs','EPSG','8714','EPSG','2175','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8715','NAD83 / California zone 2 (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2226','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5894','compound_crs','EPSG','8715','EPSG','2176','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8716','NAD83 / California zone 3 (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2227','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5895','compound_crs','EPSG','8716','EPSG','2177','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8717','NAD83 / California zone 4 (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2228','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5896','compound_crs','EPSG','8717','EPSG','2178','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8718','NAD83 / California zone 5 (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2229','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5897','compound_crs','EPSG','8718','EPSG','2182','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8719','NAD83 / California zone 6 (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2230','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5898','compound_crs','EPSG','8719','EPSG','2180','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8720','NAD83 / Colorado North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2231','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5899','compound_crs','EPSG','8720','EPSG','2184','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8721','NAD83 / Colorado Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2232','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5900','compound_crs','EPSG','8721','EPSG','2183','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8722','NAD83 / Colorado South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2233','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5901','compound_crs','EPSG','8722','EPSG','2185','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8723','NAD83 / Connecticut (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2234','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5902','compound_crs','EPSG','8723','EPSG','1377','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8724','NAD83 / Delaware (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2235','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5903','compound_crs','EPSG','8724','EPSG','1378','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8725','NAD83 / Florida North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2238','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5904','compound_crs','EPSG','8725','EPSG','2187','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8726','NAD83 / Florida East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2236','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5905','compound_crs','EPSG','8726','EPSG','2186','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8727','NAD83 / Florida West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2237','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5906','compound_crs','EPSG','8727','EPSG','2188','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8728','NAD83 / Georgia East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2239','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5907','compound_crs','EPSG','8728','EPSG','2189','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8729','NAD83 / Georgia West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2240','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5908','compound_crs','EPSG','8729','EPSG','2190','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8730','NAD83 / Idaho East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2241','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5909','compound_crs','EPSG','8730','EPSG','2192','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8731','NAD83 / Idaho Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2242','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5910','compound_crs','EPSG','8731','EPSG','2191','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8732','NAD83 / Idaho West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2243','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5911','compound_crs','EPSG','8732','EPSG','2193','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8733','NAD83 / Illinois East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3435','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5912','compound_crs','EPSG','8733','EPSG','2194','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8734','NAD83 / Illinois West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3436','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5913','compound_crs','EPSG','8734','EPSG','2195','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8735','NAD83 / Indiana East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2965','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5914','compound_crs','EPSG','8735','EPSG','2196','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8736','NAD83 / Indiana West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2966','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5915','compound_crs','EPSG','8736','EPSG','2197','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8737','NAD83 / Iowa North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3417','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5916','compound_crs','EPSG','8737','EPSG','2198','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8738','NAD83 / Iowa South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3418','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5917','compound_crs','EPSG','8738','EPSG','2199','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8739','NAD83 / Kansas North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3419','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5918','compound_crs','EPSG','8739','EPSG','2200','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8740','NAD83 / Kansas South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3420','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5919','compound_crs','EPSG','8740','EPSG','2201','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8741','NAD83 / Kentucky North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2246','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5920','compound_crs','EPSG','8741','EPSG','2202','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8742','NAD83 / Kentucky South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2247','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5921','compound_crs','EPSG','8742','EPSG','2203','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8743','NAD83 / Louisiana North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3451','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5922','compound_crs','EPSG','8743','EPSG','2204','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8744','NAD83 / Louisiana South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3452','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5923','compound_crs','EPSG','8744','EPSG','2529','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8745','NAD83 / Maine East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26847','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5924','compound_crs','EPSG','8745','EPSG','2206','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8746','NAD83 / Maine West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26848','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5925','compound_crs','EPSG','8746','EPSG','2207','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8747','NAD83 / Maryland (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2248','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5926','compound_crs','EPSG','8747','EPSG','1389','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8748','NAD83 / Massachusetts Mainland (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2249','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5927','compound_crs','EPSG','8748','EPSG','2209','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8749','NAD83 / Massachusetts Island (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2250','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5928','compound_crs','EPSG','8749','EPSG','2208','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8750','NAD83 / Minnesota North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26849','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5929','compound_crs','EPSG','8750','EPSG','2214','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8751','NAD83 / Minnesota Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26850','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5930','compound_crs','EPSG','8751','EPSG','2213','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8752','NAD83 / Minnesota South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26851','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5931','compound_crs','EPSG','8752','EPSG','2215','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8753','NAD83 / Mississippi East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2254','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5932','compound_crs','EPSG','8753','EPSG','2216','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8754','NAD83 / Mississippi West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2255','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5933','compound_crs','EPSG','8754','EPSG','2217','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8755','NAD83 / Nebraska (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26852','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5934','compound_crs','EPSG','8755','EPSG','1396','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8756','NAD83 / Nevada East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3421','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5935','compound_crs','EPSG','8756','EPSG','2224','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8757','NAD83 / Nevada Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3422','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5936','compound_crs','EPSG','8757','EPSG','2223','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8758','NAD83 / Nevada West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3423','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5937','compound_crs','EPSG','8758','EPSG','2225','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8759','NAD83 / New Hampshire (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3437','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5938','compound_crs','EPSG','8759','EPSG','1398','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8760','NAD83 / New Jersey (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3424','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5939','compound_crs','EPSG','8760','EPSG','1399','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8761','NAD83 / New Mexico East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2257','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5940','compound_crs','EPSG','8761','EPSG','2228','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8762','NAD83 / New Mexico Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2258','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5941','compound_crs','EPSG','8762','EPSG','2231','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8763','NAD83 / New Mexico West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2259','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5942','compound_crs','EPSG','8763','EPSG','2232','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8764','NAD83 / New York East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2260','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5943','compound_crs','EPSG','8764','EPSG','2234','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8765','NAD83 / New York Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2261','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5944','compound_crs','EPSG','8765','EPSG','2233','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8766','NAD83 / New York West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2262','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5945','compound_crs','EPSG','8766','EPSG','2236','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8767','NAD83 / New York Long Island (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2263','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5946','compound_crs','EPSG','8767','EPSG','2235','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8768','NAD83 / North Carolina (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2264','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5947','compound_crs','EPSG','8768','EPSG','1402','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8769','NAD83 / Ohio North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3734','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5948','compound_crs','EPSG','8769','EPSG','2239','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8770','NAD83 / Ohio South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3735','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5949','compound_crs','EPSG','8770','EPSG','2240','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8771','NAD83 / Oklahoma North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2267','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5950','compound_crs','EPSG','8771','EPSG','2241','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8772','NAD83 / Oklahoma South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2268','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5951','compound_crs','EPSG','8772','EPSG','2242','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8773','NAD83 / Pennsylvania North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2271','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5952','compound_crs','EPSG','8773','EPSG','2245','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8774','NAD83 / Pennsylvania South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2272','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5953','compound_crs','EPSG','8774','EPSG','2246','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8775','NAD83 / Rhode Island (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3438','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5954','compound_crs','EPSG','8775','EPSG','1408','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8776','NAD83 / South Dakota North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','4457','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5955','compound_crs','EPSG','8776','EPSG','2249','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8777','NAD83 / South Dakota South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3455','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5956','compound_crs','EPSG','8777','EPSG','2250','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8778','NAD83 / Tennessee (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2274','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5957','compound_crs','EPSG','8778','EPSG','1411','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8779','NAD83 / Texas North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2275','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5958','compound_crs','EPSG','8779','EPSG','2253','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8780','NAD83 / Texas North Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2276','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5959','compound_crs','EPSG','8780','EPSG','2254','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8781','NAD83 / Texas Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2277','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5960','compound_crs','EPSG','8781','EPSG','2252','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8782','NAD83 / Texas South Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2278','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5961','compound_crs','EPSG','8782','EPSG','2527','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8783','NAD83 / Texas South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2279','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5962','compound_crs','EPSG','8783','EPSG','2528','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8784','NAD83 / Utah North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3560','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5963','compound_crs','EPSG','8784','EPSG','2258','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8785','NAD83 / Utah Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3566','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5964','compound_crs','EPSG','8785','EPSG','2257','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8786','NAD83 / Utah South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3567','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5965','compound_crs','EPSG','8786','EPSG','2259','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8787','NAD83 / Vermont (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','5646','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5966','compound_crs','EPSG','8787','EPSG','1414','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8788','NAD83 / Virginia North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2283','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5967','compound_crs','EPSG','8788','EPSG','2260','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8789','NAD83 / Virginia South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2284','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5968','compound_crs','EPSG','8789','EPSG','2261','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8790','NAD83 / Washington North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2285','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5969','compound_crs','EPSG','8790','EPSG','2273','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8791','NAD83 / Washington South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2286','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5970','compound_crs','EPSG','8791','EPSG','2274','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8792','NAD83 / West Virginia North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26853','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5971','compound_crs','EPSG','8792','EPSG','2264','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8793','NAD83 / West Virginia South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','26854','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5972','compound_crs','EPSG','8793','EPSG','2265','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8794','NAD83 / Wisconsin North (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2287','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5973','compound_crs','EPSG','8794','EPSG','2267','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8795','NAD83 / Wisconsin Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2288','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5974','compound_crs','EPSG','8795','EPSG','2266','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8796','NAD83 / Wisconsin South (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','2289','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5975','compound_crs','EPSG','8796','EPSG','2268','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8797','NAD83 / Wyoming East (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3736','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5976','compound_crs','EPSG','8797','EPSG','2269','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8798','NAD83 / Wyoming East Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3737','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5977','compound_crs','EPSG','8798','EPSG','2270','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8799','NAD83 / Wyoming West Central (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3738','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5978','compound_crs','EPSG','8799','EPSG','2272','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8800','NAD83 / Wyoming West (ftUS) + NAVD88 height (ftUS)',NULL,'EPSG','3739','EPSG','6360',0);
INSERT INTO "usage" VALUES('EPSG','5979','compound_crs','EPSG','8800','EPSG','2271','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8801','NAD83 / Alabama East + NAVD88 height',NULL,'EPSG','26929','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5980','compound_crs','EPSG','8801','EPSG','2154','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8802','NAD83 / Alabama West + NAVD88 height',NULL,'EPSG','26930','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5981','compound_crs','EPSG','8802','EPSG','2155','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8803','NAD83 / Alaska zone 1 + NAVD88 height',NULL,'EPSG','26931','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5982','compound_crs','EPSG','8803','EPSG','2156','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8804','NAD83 / Alaska zone 2 + NAVD88 height',NULL,'EPSG','26932','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5983','compound_crs','EPSG','8804','EPSG','2158','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8805','NAD83 / Alaska zone 3 + NAVD88 height',NULL,'EPSG','26933','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5984','compound_crs','EPSG','8805','EPSG','2159','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8806','NAD83 / Alaska zone 4 + NAVD88 height',NULL,'EPSG','26934','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5985','compound_crs','EPSG','8806','EPSG','2160','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8807','NAD83 / Alaska zone 5 + NAVD88 height',NULL,'EPSG','26935','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5986','compound_crs','EPSG','8807','EPSG','2161','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8808','NAD83 / Alaska zone 6 + NAVD88 height',NULL,'EPSG','26936','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5987','compound_crs','EPSG','8808','EPSG','2162','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8809','NAD83 / Alaska zone 7 + NAVD88 height',NULL,'EPSG','26937','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5988','compound_crs','EPSG','8809','EPSG','2163','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8810','NAD83 / Alaska zone 8 + NAVD88 height',NULL,'EPSG','26938','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5989','compound_crs','EPSG','8810','EPSG','2164','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8811','NAD83 / Alaska zone 9 + NAVD88 height',NULL,'EPSG','26939','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5990','compound_crs','EPSG','8811','EPSG','2165','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8812','NAD83 / Alaska zone 10 + NAVD88 height',NULL,'EPSG','26940','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5991','compound_crs','EPSG','8812','EPSG','2157','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8813','NAD83 / Missouri East + NAVD88 height',NULL,'EPSG','26996','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5992','compound_crs','EPSG','8813','EPSG','2219','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8814','NAD83 / Missouri Central + NAVD88 height',NULL,'EPSG','26997','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5993','compound_crs','EPSG','8814','EPSG','2218','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8815','NAD83 / Missouri West + NAVD88 height',NULL,'EPSG','26998','EPSG','5703',0);
INSERT INTO "usage" VALUES('EPSG','5994','compound_crs','EPSG','8815','EPSG','2220','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','8912','CR-SIRGAS / CRTM05 + DACR52 height',NULL,'EPSG','8908','EPSG','8911',0);
INSERT INTO "usage" VALUES('EPSG','6028','compound_crs','EPSG','8912','EPSG','3232','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','9286','ETRS89 + NAP height',NULL,'EPSG','4258','EPSG','5709',0);
INSERT INTO "usage" VALUES('EPSG','14121','compound_crs','EPSG','9286','EPSG','1172','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9289','ETRS89 + LAT NL depth',NULL,'EPSG','4258','EPSG','9287',0);
INSERT INTO "usage" VALUES('EPSG','14124','compound_crs','EPSG','9289','EPSG','1630','EPSG','1198');
INSERT INTO "compound_crs" VALUES('EPSG','9290','ETRS89 + MSL NL depth',NULL,'EPSG','4258','EPSG','9288',0);
INSERT INTO "usage" VALUES('EPSG','14125','compound_crs','EPSG','9290','EPSG','1630','EPSG','1265');
INSERT INTO "compound_crs" VALUES('EPSG','9306','HS2 Survey Grid + HS2-VRF height',NULL,'EPSG','9300','EPSG','9303',0);
INSERT INTO "usage" VALUES('EPSG','14050','compound_crs','EPSG','9306','EPSG','4582','EPSG','1260');
INSERT INTO "compound_crs" VALUES('EPSG','9368','TPEN11 Grid + ODN height',NULL,'EPSG','9367','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','13976','compound_crs','EPSG','9368','EPSG','4583','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9374','MML07 Grid + ODN height',NULL,'EPSG','9373','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','13997','compound_crs','EPSG','9374','EPSG','4588','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9388','AbInvA96_2020 Grid + ODN height',NULL,'EPSG','9387','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','14030','compound_crs','EPSG','9388','EPSG','4589','EPSG','1196');
INSERT INTO "compound_crs" VALUES('EPSG','9422','ETRS89 + EVRF2019 height',NULL,'EPSG','4258','EPSG','9389',0);
INSERT INTO "usage" VALUES('EPSG','14082','compound_crs','EPSG','9422','EPSG','4609','EPSG','1261');
INSERT INTO "compound_crs" VALUES('EPSG','9423','ETRS89 + EVRF2019 mean-tide height',NULL,'EPSG','4258','EPSG','9390',0);
INSERT INTO "usage" VALUES('EPSG','14083','compound_crs','EPSG','9423','EPSG','4609','EPSG','1262');
INSERT INTO "compound_crs" VALUES('EPSG','9424','ETRS89 + ODN height',NULL,'EPSG','4258','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','14052','compound_crs','EPSG','9424','EPSG','2792','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9425','ETRS89 + ODN (Offshore) height',NULL,'EPSG','4258','EPSG','7707',0);
INSERT INTO "usage" VALUES('EPSG','14053','compound_crs','EPSG','9425','EPSG','4391','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9426','ETRS89 + ODN Orkney height',NULL,'EPSG','4258','EPSG','5740',0);
INSERT INTO "usage" VALUES('EPSG','14054','compound_crs','EPSG','9426','EPSG','2793','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9427','ETRS89 + Lerwick height',NULL,'EPSG','4258','EPSG','5742',0);
INSERT INTO "usage" VALUES('EPSG','14055','compound_crs','EPSG','9427','EPSG','2795','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9428','ETRS89 + Stornoway height',NULL,'EPSG','4258','EPSG','5746',0);
INSERT INTO "usage" VALUES('EPSG','14056','compound_crs','EPSG','9428','EPSG','2799','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9429','ETRS89 + Douglas height',NULL,'EPSG','4258','EPSG','5750',0);
INSERT INTO "usage" VALUES('EPSG','14057','compound_crs','EPSG','9429','EPSG','2803','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9430','ETRS89 + St. Marys height',NULL,'EPSG','4258','EPSG','5749',0);
INSERT INTO "usage" VALUES('EPSG','14058','compound_crs','EPSG','9430','EPSG','2802','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9449','ETRS89 + Malin Head height',NULL,'EPSG','4258','EPSG','5731',0);
INSERT INTO "usage" VALUES('EPSG','14084','compound_crs','EPSG','9449','EPSG','1305','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9450','ETRS89 + Belfast height',NULL,'EPSG','4258','EPSG','5732',0);
INSERT INTO "usage" VALUES('EPSG','14085','compound_crs','EPSG','9450','EPSG','2530','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9452','ETRS89 + BI height',NULL,'EPSG','4258','EPSG','9451',0);
INSERT INTO "usage" VALUES('EPSG','14088','compound_crs','EPSG','9452','EPSG','4606','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9457','GBK19 Grid + ODN height',NULL,'EPSG','9456','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','14132','compound_crs','EPSG','9457','EPSG','4607','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9462','GDA2020 + AVWS height',NULL,'EPSG','7844','EPSG','9458',0);
INSERT INTO "usage" VALUES('EPSG','14142','compound_crs','EPSG','9462','EPSG','4177','EPSG','1267');
INSERT INTO "compound_crs" VALUES('EPSG','9463','GDA2020 + AHD height',NULL,'EPSG','7844','EPSG','5711',0);
INSERT INTO "usage" VALUES('EPSG','14143','compound_crs','EPSG','9463','EPSG','4493','EPSG','1263');
INSERT INTO "compound_crs" VALUES('EPSG','9464','GDA94 + AHD height',NULL,'EPSG','4283','EPSG','5711',0);
INSERT INTO "usage" VALUES('EPSG','14144','compound_crs','EPSG','9464','EPSG','4493','EPSG','1263');
INSERT INTO "compound_crs" VALUES('EPSG','9500','ETRS89 + EVRF2000 Austria height',NULL,'EPSG','4258','EPSG','9274',0);
INSERT INTO "usage" VALUES('EPSG','14431','compound_crs','EPSG','9500','EPSG','1037','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9501','MGI + EVRF2000 Austria height',NULL,'EPSG','4312','EPSG','9274',0);
INSERT INTO "usage" VALUES('EPSG','14432','compound_crs','EPSG','9501','EPSG','1037','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9502','CIGD11 + CBVD61 height (ft)',NULL,'EPSG','6135','EPSG','6132',0);
INSERT INTO "usage" VALUES('EPSG','14265','compound_crs','EPSG','9502','EPSG','3207','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9503','CIGD11 + GCVD54 height (ft)',NULL,'EPSG','6135','EPSG','6130',0);
INSERT INTO "usage" VALUES('EPSG','14435','compound_crs','EPSG','9503','EPSG','3185','EPSG','1270');
INSERT INTO "compound_crs" VALUES('EPSG','9504','CIGD11 + LCVD61 height (ft)',NULL,'EPSG','6135','EPSG','6131',0);
INSERT INTO "usage" VALUES('EPSG','14267','compound_crs','EPSG','9504','EPSG','4121','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9505','ETRS89 + Alicante height',NULL,'EPSG','4258','EPSG','5782',0);
INSERT INTO "usage" VALUES('EPSG','14270','compound_crs','EPSG','9505','EPSG','4188','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9506','ETRS89 + Ceuta 2 height',NULL,'EPSG','4258','EPSG','9402',0);
INSERT INTO "usage" VALUES('EPSG','14273','compound_crs','EPSG','9506','EPSG','4590','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9507','ETRS89 + Ibiza height',NULL,'EPSG','4258','EPSG','9394',0);
INSERT INTO "usage" VALUES('EPSG','14276','compound_crs','EPSG','9507','EPSG','4604','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9508','ETRS89 + Mallorca height',NULL,'EPSG','4258','EPSG','9392',0);
INSERT INTO "usage" VALUES('EPSG','14440','compound_crs','EPSG','9508','EPSG','4602','EPSG','1270');
INSERT INTO "compound_crs" VALUES('EPSG','9509','ETRS89 + Menorca height',NULL,'EPSG','4258','EPSG','9393',0);
INSERT INTO "usage" VALUES('EPSG','14283','compound_crs','EPSG','9509','EPSG','4603','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9510','REGCAN95 + El Hierro height',NULL,'EPSG','4081','EPSG','9401',0);
INSERT INTO "usage" VALUES('EPSG','14286','compound_crs','EPSG','9510','EPSG','4597','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9511','REGCAN95 + Fuerteventura height',NULL,'EPSG','4081','EPSG','9396',0);
INSERT INTO "usage" VALUES('EPSG','14929','compound_crs','EPSG','9511','EPSG','4592','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9512','REGCAN95 + Gran Canaria height',NULL,'EPSG','4081','EPSG','9397',0);
INSERT INTO "usage" VALUES('EPSG','14930','compound_crs','EPSG','9512','EPSG','4593','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9513','REGCAN95 + La Gomera height',NULL,'EPSG','4081','EPSG','9399',0);
INSERT INTO "usage" VALUES('EPSG','14931','compound_crs','EPSG','9513','EPSG','4595','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9514','REGCAN95 + La Palma height',NULL,'EPSG','4081','EPSG','9400',0);
INSERT INTO "usage" VALUES('EPSG','14302','compound_crs','EPSG','9514','EPSG','4596','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9515','REGCAN95 + Lanzarote height',NULL,'EPSG','4081','EPSG','9395',0);
INSERT INTO "usage" VALUES('EPSG','14305','compound_crs','EPSG','9515','EPSG','4591','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9516','REGCAN95 + Tenerife height',NULL,'EPSG','4081','EPSG','9398',0);
INSERT INTO "usage" VALUES('EPSG','14308','compound_crs','EPSG','9516','EPSG','4594','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9517','SHGD2015 + SHVD2015 height',NULL,'EPSG','7886','EPSG','7890',0);
INSERT INTO "usage" VALUES('EPSG','14319','compound_crs','EPSG','9517','EPSG','3183','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9518','WGS 84 + EGM2008 height',NULL,'EPSG','4326','EPSG','3855',0);
INSERT INTO "usage" VALUES('EPSG','14320','compound_crs','EPSG','9518','EPSG','1262','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9519','FEH2010 + FCSVR10 height',NULL,'EPSG','5593','EPSG','5597',0);
INSERT INTO "usage" VALUES('EPSG','14325','compound_crs','EPSG','9519','EPSG','3890','EPSG','1139');
INSERT INTO "compound_crs" VALUES('EPSG','9520','KSA-GRF17 + KSA-VRF14 height',NULL,'EPSG','9333','EPSG','9335',0);
INSERT INTO "usage" VALUES('EPSG','14336','compound_crs','EPSG','9520','EPSG','3303','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9521','POSGAR 2007 + SRVN16 height',NULL,'EPSG','5340','EPSG','9255',0);
INSERT INTO "usage" VALUES('EPSG','14340','compound_crs','EPSG','9521','EPSG','4573','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9522','NAD83(2011) + PRVD02 height',NULL,'EPSG','6318','EPSG','6641',0);
INSERT INTO "usage" VALUES('EPSG','14357','compound_crs','EPSG','9522','EPSG','3294','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9523','NAD83(2011) + VIVD09 height',NULL,'EPSG','6318','EPSG','6642',0);
INSERT INTO "usage" VALUES('EPSG','14356','compound_crs','EPSG','9523','EPSG','3330','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9524','NAD83(MA11) + GUVD04 height',NULL,'EPSG','6325','EPSG','6644',0);
INSERT INTO "usage" VALUES('EPSG','14364','compound_crs','EPSG','9524','EPSG','3255','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9525','NAD83(MA11) + NMVD03 height',NULL,'EPSG','6325','EPSG','6640',0);
INSERT INTO "usage" VALUES('EPSG','14367','compound_crs','EPSG','9525','EPSG','4521','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9526','NAD83(PA11) + ASVD02 height',NULL,'EPSG','6322','EPSG','6643',0);
INSERT INTO "usage" VALUES('EPSG','14462','compound_crs','EPSG','9526','EPSG','2288','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9527','NZGD2000 + NZVD2009 height',NULL,'EPSG','4167','EPSG','4440',0);
INSERT INTO "usage" VALUES('EPSG','14377','compound_crs','EPSG','9527','EPSG','1175','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9528','NZGD2000 + NZVD2016 height',NULL,'EPSG','4167','EPSG','7839',0);
INSERT INTO "usage" VALUES('EPSG','14380','compound_crs','EPSG','9528','EPSG','1175','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9529','SRGI2013 + INAGeoid2020 height',NULL,'EPSG','9470','EPSG','9471',0);
INSERT INTO "usage" VALUES('EPSG','14383','compound_crs','EPSG','9529','EPSG','1122','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9530','RGFG95 + NGG1977 height',NULL,'EPSG','4624','EPSG','5755',0);
INSERT INTO "usage" VALUES('EPSG','14420','compound_crs','EPSG','9530','EPSG','3146','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9531','RGAF09 + Guadeloupe 1988 height',NULL,'EPSG','5489','EPSG','5757',0);
INSERT INTO "usage" VALUES('EPSG','14936','compound_crs','EPSG','9531','EPSG','2892','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9532','RGAF09 + IGN 1988 LS height',NULL,'EPSG','5489','EPSG','5616',0);
INSERT INTO "usage" VALUES('EPSG','14471','compound_crs','EPSG','9532','EPSG','2895','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9533','RGAF09 + IGN 1988 MG height',NULL,'EPSG','5489','EPSG','5617',0);
INSERT INTO "usage" VALUES('EPSG','14473','compound_crs','EPSG','9533','EPSG','2894','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9534','RGAF09 + IGN 1988 SB height',NULL,'EPSG','5489','EPSG','5619',0);
INSERT INTO "usage" VALUES('EPSG','14478','compound_crs','EPSG','9534','EPSG','2891','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9535','RGAF09 + IGN 1988 SM height',NULL,'EPSG','5489','EPSG','5620',0);
INSERT INTO "usage" VALUES('EPSG','14481','compound_crs','EPSG','9535','EPSG','2890','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9536','RGAF09 + IGN 2008 LD height',NULL,'EPSG','5489','EPSG','9130',0);
INSERT INTO "usage" VALUES('EPSG','14484','compound_crs','EPSG','9536','EPSG','2893','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9537','RGAF09 + Martinique 1987 height',NULL,'EPSG','5489','EPSG','5756',0);
INSERT INTO "usage" VALUES('EPSG','14486','compound_crs','EPSG','9537','EPSG','3276','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9538','RGF93 v2 + NGF-IGN69 height',NULL,'EPSG','9777','EPSG','5720',0);
INSERT INTO "usage" VALUES('EPSG','14489','compound_crs','EPSG','9538','EPSG','1326','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9539','RGF93 v2 + NGF-IGN78 height',NULL,'EPSG','9777','EPSG','5721',0);
INSERT INTO "usage" VALUES('EPSG','14490','compound_crs','EPSG','9539','EPSG','1327','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9540','RGNC91-93 + NGNC08 height',NULL,'EPSG','4749','EPSG','9351',0);
INSERT INTO "usage" VALUES('EPSG','14495','compound_crs','EPSG','9540','EPSG','3430','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9541','RGSPM06 + Danger 1950 height',NULL,'EPSG','4463','EPSG','5792',0);
INSERT INTO "usage" VALUES('EPSG','14498','compound_crs','EPSG','9541','EPSG','3299','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9542','RRAF 1991 + IGN 2008 LD height',NULL,'EPSG','4558','EPSG','9130',0);
INSERT INTO "usage" VALUES('EPSG','14501','compound_crs','EPSG','9542','EPSG','2893','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9543','ITRF2005 + SA LLD height',NULL,'EPSG','8998','EPSG','9279',0);
INSERT INTO "usage" VALUES('EPSG','14522','compound_crs','EPSG','9543','EPSG','3309','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9544','NAD83(CSRS)v6 + CGVD2013(CGG2013a) height',NULL,'EPSG','8252','EPSG','9245',0);
INSERT INTO "usage" VALUES('EPSG','14525','compound_crs','EPSG','9544','EPSG','1061','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9656','ETRF2000-PL + Baltic 1986 height',NULL,'EPSG','9702','EPSG','9650',0);
INSERT INTO "usage" VALUES('EPSG','15108','compound_crs','EPSG','9656','EPSG','3293','EPSG','1270');
INSERT INTO "compound_crs" VALUES('EPSG','9657','ETRF2000-PL + EVRF2007-PL height',NULL,'EPSG','9702','EPSG','9651',0);
INSERT INTO "usage" VALUES('EPSG','15109','compound_crs','EPSG','9657','EPSG','3293','EPSG','1270');
INSERT INTO "compound_crs" VALUES('EPSG','9705','WGS 84 + MSL height',NULL,'EPSG','4326','EPSG','5714',0);
INSERT INTO "usage" VALUES('EPSG','15089','compound_crs','EPSG','9705','EPSG','1262','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9707','WGS 84 + EGM96 height',NULL,'EPSG','4326','EPSG','5773',0);
INSERT INTO "usage" VALUES('EPSG','15091','compound_crs','EPSG','9707','EPSG','1262','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9711','NAD83(CSRS) / UTM zone 23N + CGVD2013 height',NULL,'EPSG','9709','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','15205','compound_crs','EPSG','9711','EPSG','2153','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','9714','NAD83(CSRS) / UTM zone 24N + CGVD2013 height',NULL,'EPSG','9713','EPSG','6647',0);
INSERT INTO "usage" VALUES('EPSG','15197','compound_crs','EPSG','9714','EPSG','4617','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','9715','NAD83(CSRS) / UTM zone 15N + CGVD2013a height',NULL,'EPSG','3159','EPSG','9245',0);
INSERT INTO "usage" VALUES('EPSG','15200','compound_crs','EPSG','9715','EPSG','3414','EPSG','1142');
INSERT INTO "compound_crs" VALUES('EPSG','9723','ETRS89 + Genoa 1942 height',NULL,'EPSG','4258','EPSG','5214',0);
INSERT INTO "usage" VALUES('EPSG','15255','compound_crs','EPSG','9723','EPSG','3736','EPSG','1270');
INSERT INTO "compound_crs" VALUES('EPSG','9724','ETRS89 + Catania 1965 height',NULL,'EPSG','4258','EPSG','9721',0);
INSERT INTO "usage" VALUES('EPSG','15324','compound_crs','EPSG','9724','EPSG','2340','EPSG','1270');
INSERT INTO "compound_crs" VALUES('EPSG','9725','ETRS89 + Cagliari 1956 height',NULL,'EPSG','4258','EPSG','9722',0);
INSERT INTO "usage" VALUES('EPSG','15257','compound_crs','EPSG','9725','EPSG','2339','EPSG','1270');
INSERT INTO "compound_crs" VALUES('EPSG','9742','EOS21 Grid + ODN height',NULL,'EPSG','9741','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','15318','compound_crs','EPSG','9742','EPSG','4620','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9762','ECML14_NB Grid + ODN height',NULL,'EPSG','9761','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','16499','compound_crs','EPSG','9762','EPSG','4621','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9767','EWR2 Grid + ODN height',NULL,'EPSG','9766','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','16510','compound_crs','EPSG','9767','EPSG','4622','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9785','RGF93 v2b + NGF-IGN69 height',NULL,'EPSG','9782','EPSG','5720',0);
INSERT INTO "usage" VALUES('EPSG','16594','compound_crs','EPSG','9785','EPSG','1326','EPSG','1026');
INSERT INTO "compound_crs" VALUES('EPSG','9870','MRH21 Grid + ODN height',NULL,'EPSG','9869','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','16942','compound_crs','EPSG','9870','EPSG','4652','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9881','MOLDOR11 Grid + ODN height',NULL,'EPSG','9880','EPSG','5701',0);
INSERT INTO "usage" VALUES('EPSG','16985','compound_crs','EPSG','9881','EPSG','4655','EPSG','1141');
INSERT INTO "compound_crs" VALUES('EPSG','9883','ETRS89 + CD Norway depth',NULL,'EPSG','4258','EPSG','9672',0);
INSERT INTO "usage" VALUES('EPSG','16974','compound_crs','EPSG','9883','EPSG','4656','EPSG','1198');
