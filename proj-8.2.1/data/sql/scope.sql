--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "scope" VALUES('EPSG','1024','Not known.',0);
INSERT INTO "scope" VALUES('EPSG','1025','?',1);
INSERT INTO "scope" VALUES('EPSG','1026','Spatial referencing.',0);
INSERT INTO "scope" VALUES('EPSG','1027','Geodesy.',0);
INSERT INTO "scope" VALUES('EPSG','1028','Cadastre.',0);
INSERT INTO "scope" VALUES('EPSG','1029','Engineering survey.',0);
INSERT INTO "scope" VALUES('EPSG','1030','Example only (fictitious).',0);
INSERT INTO "scope" VALUES('EPSG','1031','Transformation of coordinates at 0.1m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1032','Transformation of coordinates at 0.2m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1033','Transformation of coordinates at 0.3m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1034','Transformation of coordinates at 0.4m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1035','Transformation of coordinates at 0.5m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1036','Transformation of coordinates at 0.6m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1037','Transformation of coordinates at 0.7m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1038','Transformation of coordinates at 0.8m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1039','Transformation of coordinates at 0.9m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1040','Polar research.',0);
INSERT INTO "scope" VALUES('EPSG','1041','Transformation of coordinates at 1m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1042','Transformation of coordinates at 2m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1043','Transformation of coordinates at 3m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1044','Transformation of coordinates at 4m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1045','Transformation of coordinates at 5m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1046','Atlas of Canada and nationwide web mapping applications.',0);
INSERT INTO "scope" VALUES('EPSG','1047','Remote sensing.',0);
INSERT INTO "scope" VALUES('EPSG','1048','Transformation of coordinates at 0.25m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1049','Transformation of coordinates at 9m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1050','Transformation of coordinates at 10m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1051','Approximation of horizontal component of official 3D RDNAPTRANS(TM) transformation, which since 1st October 2000 has defined Amersfoort geodetic datum.',0);
INSERT INTO "scope" VALUES('EPSG','1052','Basin-wide mapping and analysis.',0);
INSERT INTO "scope" VALUES('EPSG','1053','Boundary demarcation.',0);
INSERT INTO "scope" VALUES('EPSG','1054','Cadastre, engineering survey.',0);
INSERT INTO "scope" VALUES('EPSG','1055','Cadastre, engineering survey, topographic mapping (large scale).',0);
INSERT INTO "scope" VALUES('EPSG','1056','Cadastre, engineering survey, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1057','Cadastre, engineering survey, topographic mapping (medium scale).',0);
INSERT INTO "scope" VALUES('EPSG','1058','Change of coordinate epoch for points referenced to NAD83(CSRS)v6.',0);
INSERT INTO "scope" VALUES('EPSG','1059','Change of height to a different vertical reference surface.',0);
INSERT INTO "scope" VALUES('EPSG','1060','Change of depth to a different vertical reference surface.',0);
INSERT INTO "scope" VALUES('EPSG','1061','Cadastre, engineering survey, topographic mapping (1:5000 and larger scales).',0);
INSERT INTO "scope" VALUES('EPSG','1062','Topographic mapping (1:50,000).',0);
INSERT INTO "scope" VALUES('EPSG','1063','Topographic mapping (1:50,000) published between 1955 and 2000.',0);
INSERT INTO "scope" VALUES('EPSG','1064','Abu Dhabi Municipality GIS.',0);
INSERT INTO "scope" VALUES('EPSG','1065','Academic research, not officially adopted.',0);
INSERT INTO "scope" VALUES('EPSG','1066','Recommended by OSi and OSNI for all horizontal CTs in the Republic and Northern Ireland.',0);
INSERT INTO "scope" VALUES('EPSG','1067','Post-1996 data based on the classical geodetic network.',0);
INSERT INTO "scope" VALUES('EPSG','1068','Pre-1996 data related to the classical geodetic network.',0);
INSERT INTO "scope" VALUES('EPSG','1069','Adopted as official definition of OSGB36 from 2002 to August 2016. Accuracy by definition exact. Accuracy compared to triangulation coordinates 0.1m at 67% confidence level.',0);
INSERT INTO "scope" VALUES('EPSG','1070','Adopted as official definition of OSGB36 from August 2016. Accuracy by definition exact. Accuracy compared to triangulation coordinates 0.1m at 67% confidence level.',0);
INSERT INTO "scope" VALUES('EPSG','1071','Arctic small scale mapping - Alaska-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1072','Arctic small scale mapping - Canada-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1073','Arctic small scale mapping - Greenland-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1074','Arctic small scale mapping - Norway-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1075','Arctic small scale mapping - Russia-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1076','Transformation of coordinates at 25m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1077','Transformation of coordinates at 30m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1078','Transformation of coordinates at 0.15m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1079','Transformation of coordinates at 0.05m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1080','Angola LNG project.',0);
INSERT INTO "scope" VALUES('EPSG','1081','Seismic survey.',0);
INSERT INTO "scope" VALUES('EPSG','1082','Approximate transformation of seismic data acquired before 1985 to CRS used post 1985.',0);
INSERT INTO "scope" VALUES('EPSG','1083','Approximation (to better than 2cm) using NTv2 method of results of FINELTRA programme concatenated with LV-95 parameters.',0);
INSERT INTO "scope" VALUES('EPSG','1084','Parameter values from CH1903+ to ETRS89 (tfm code 1647) and are used as an approximation from CH1903 with a lesser accuracy of 1.5m which equates to the magnitude of distortions in the CH1903 network.',0);
INSERT INTO "scope" VALUES('EPSG','1085','Approximation using NTv2 method of results of FINELTRA programme to an accuracy of 0.01m except at boundary of the Geneva and Vaud cantons, in city of Geneva and in the main valleys of Valais canton where differences are up to 20 cm.',0);
INSERT INTO "scope" VALUES('EPSG','1086','Approximation of horizontal component of official 3D RDNAPTRANS(TM) transformation, which since 1st October 2000 has defined Amersfoort geodetic datum.',0);
INSERT INTO "scope" VALUES('EPSG','1087','Approximation for pan-European small-scale mapping in extended ETRS89.',0);
INSERT INTO "scope" VALUES('EPSG','1088','Approximation for pan-European statistical analysis in extended ETRS89.',0);
INSERT INTO "scope" VALUES('EPSG','1089','Basis for topographic mapping in Republic of Ireland between 1965 and 1975; for scientific purposes only in Northern Ireland.',0);
INSERT INTO "scope" VALUES('EPSG','1090','Basis for topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1091','Cadastre, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1092','Cadastre, engineering survey, topographic mapping (large and medium scale).',0);
INSERT INTO "scope" VALUES('EPSG','1093','Cadastre, engineering survey. Usage restricted to areas below 290m above sea level.',0);
INSERT INTO "scope" VALUES('EPSG','1094','Cadastre in Czechia.',0);
INSERT INTO "scope" VALUES('EPSG','1095','Cadastre in Slovakia.',0);
INSERT INTO "scope" VALUES('EPSG','1096','Cadastre, survey control and engineering survey in urban areas, typically in all municipalities that previously comprised the 73 Municipal Integrated Surveying and Mapping (MISAM) areas, also known as urban cadastral map areas. For rural areas use UTM.',0);
INSERT INTO "scope" VALUES('EPSG','1097','Cartography System of Distrito Federal (SICAD).',0);
INSERT INTO "scope" VALUES('EPSG','1098','Web mapping and visualisation.',0);
INSERT INTO "scope" VALUES('EPSG','1099','Change of height to a different vertical reference surface and unit.',0);
INSERT INTO "scope" VALUES('EPSG','1100','Change of prime meridian.',0);
INSERT INTO "scope" VALUES('EPSG','1101','Change of height or depth unit.',0);
INSERT INTO "scope" VALUES('EPSG','1102','Cadastre, engineering survey, civilian topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1103','Coastal hydrography.',0);
INSERT INTO "scope" VALUES('EPSG','1104','Coastal hydrography, offshore oil and gas exploration and production.',0);
INSERT INTO "scope" VALUES('EPSG','1105','Coastal hydrography. Not used by oil industry.',0);
INSERT INTO "scope" VALUES('EPSG','1106','Construction of the Basic Spatial Unit (BSU) grid.',0);
INSERT INTO "scope" VALUES('EPSG','1107','Conformal mapping at scales of 1:500,000 and smaller.',0);
INSERT INTO "scope" VALUES('EPSG','1108','Transformation of GDA94 coordinates that have been derived through GNSS CORS.',0);
INSERT INTO "scope" VALUES('EPSG','1109','Data analysis and small scale data presentation for contiguous lower 48 states.',0);
INSERT INTO "scope" VALUES('EPSG','1110','US Defense Meteorological Satellite Program (DMSP) SSM/I microwave imagery products.',0);
INSERT INTO "scope" VALUES('EPSG','1111','Change of axis positive direction to facilitate transformation of heights or depths through concatenated operations.',0);
INSERT INTO "scope" VALUES('EPSG','1112','Convert degree representation.',1);
INSERT INTO "scope" VALUES('EPSG','1113','Defined as exact.',0);
INSERT INTO "scope" VALUES('EPSG','1114','Geodesy. Defined as exact for S-JTSK/05 (Ferro) / Modified Krovak projCRSs (CRS codes 5224-25).',0);
INSERT INTO "scope" VALUES('EPSG','1115','Geodesy. Defines the S-JTSK [JTSK03] realization.',0);
INSERT INTO "scope" VALUES('EPSG','1116','Geodesy. Defines ED50 in the Faroe Islands.',0);
INSERT INTO "scope" VALUES('EPSG','1117','Defines ellipsoidal coordinates of origin of topocentric CS.',0);
INSERT INTO "scope" VALUES('EPSG','1118','Defines geocentric coordinates of origin of topocentric CS.',0);
INSERT INTO "scope" VALUES('EPSG','1119','Geodesy. Defines ETRF89.',0);
INSERT INTO "scope" VALUES('EPSG','1120','Geodesy. Defines ETRF90.',0);
INSERT INTO "scope" VALUES('EPSG','1121','Geodesy. Defines ETRF91.',0);
INSERT INTO "scope" VALUES('EPSG','1122','Geodesy. Defines ETRF92.',0);
INSERT INTO "scope" VALUES('EPSG','1123','Geodesy. Defines ETRF93.',0);
INSERT INTO "scope" VALUES('EPSG','1124','Geodesy. Defines ETRF94.',0);
INSERT INTO "scope" VALUES('EPSG','1125','Geodesy. Defines ETRF96.',0);
INSERT INTO "scope" VALUES('EPSG','1126','Geodesy. Defines ETRF97.',0);
INSERT INTO "scope" VALUES('EPSG','1127','Geodesy. Defines ETRF2000.',0);
INSERT INTO "scope" VALUES('EPSG','1128','Geodesy. Defines ETRF2005.',0);
INSERT INTO "scope" VALUES('EPSG','1129','Geodesy. Defines ETRF2014.',0);
INSERT INTO "scope" VALUES('EPSG','1130','EEZ delimitation.',0);
INSERT INTO "scope" VALUES('EPSG','1131','Change of coordinate epoch for points referenced to NAD83(CSRS)v7.',0);
INSERT INTO "scope" VALUES('EPSG','1132','Derivation of approximate gravity-related heights from GNSS observations.',0);
INSERT INTO "scope" VALUES('EPSG','1133','Derivation of gravity-related heights from GNSS observations.',0);
INSERT INTO "scope" VALUES('EPSG','1134','Description of the use or purpose of the CRS.',0);
INSERT INTO "scope" VALUES('EPSG','1135','State-wide spatial data management.',0);
INSERT INTO "scope" VALUES('EPSG','1136','Oil and gas exploration and production.',0);
INSERT INTO "scope" VALUES('EPSG','1137','Emulation of polynomial.',0);
INSERT INTO "scope" VALUES('EPSG','1138','Engineering survey, harbour hydrography.',0);
INSERT INTO "scope" VALUES('EPSG','1139','Engineering survey and construction for Fehmarnbelt tunnel.',0);
INSERT INTO "scope" VALUES('EPSG','1140','Engineering survey for onshore facilities for South Pars phase 11 and Pars LNG.',0);
INSERT INTO "scope" VALUES('EPSG','1141','Engineering survey and topographic mapping for railway applications.',0);
INSERT INTO "scope" VALUES('EPSG','1142','Engineering survey, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1143','Engineering survey, GIS.',0);
INSERT INTO "scope" VALUES('EPSG','1144','Engineering survey, GIS, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1145','Engineering survey 2011 to 2015.',0);
INSERT INTO "scope" VALUES('EPSG','1146','Engineering survey including Airport and Ruperts Wharf construction.',0);
INSERT INTO "scope" VALUES('EPSG','1147','Engineering survey prior to 2016 including Airport and Ruperts Wharfe construction.',0);
INSERT INTO "scope" VALUES('EPSG','1148','Exploration and development, mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1149','Exploration and production operations in Brunei. Formerly also topographic mapping (large and medium scale) and engineering survey.',0);
INSERT INTO "scope" VALUES('EPSG','1150','Transformation of coordinates at 0.01m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1151','Transformation of coordinates at 1m to 2m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1152','Transformation of coordinates at 0.1m to 0.2m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1153','Topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1154','Geocentric to geographic 3D conversions and vice versa.',0);
INSERT INTO "scope" VALUES('EPSG','1155','Geographic 3D to geographic 2D conversions.',0);
INSERT INTO "scope" VALUES('EPSG','1156','Height to depth or depth to height conversions.',0);
INSERT INTO "scope" VALUES('EPSG','1157','Low accuracy applications.',0);
INSERT INTO "scope" VALUES('EPSG','1158','Medium accuracy applications.',0);
INSERT INTO "scope" VALUES('EPSG','1159','Approximation for medium and low accuracy applications ignoring static/dynamic CRS differences.',0);
INSERT INTO "scope" VALUES('EPSG','1160','Military survey.',0);
INSERT INTO "scope" VALUES('EPSG','1161','Pan-European spatial positioning.',0);
INSERT INTO "scope" VALUES('EPSG','1162','Statistical analysis.',0);
INSERT INTO "scope" VALUES('EPSG','1163','Spatial referencing in zoned CRSs where zone boundaries are strictly enforced.',0);
INSERT INTO "scope" VALUES('EPSG','1164','Transformation of MRT68 RSO coordinates.',0);
INSERT INTO "scope" VALUES('EPSG','1165','Forestry.',0);
INSERT INTO "scope" VALUES('EPSG','1166','Geodesy. Defines NAD83(CSRS96).',0);
INSERT INTO "scope" VALUES('EPSG','1167','Geodesy. Defines NAD83(CSRS)v2.',0);
INSERT INTO "scope" VALUES('EPSG','1168','Geodesy. Defines NAD83(CSRS)v3.',0);
INSERT INTO "scope" VALUES('EPSG','1169','Geodesy. Defines NAD83(CSRS)v4.',0);
INSERT INTO "scope" VALUES('EPSG','1170','Geodesy. Defines NAD83(CSRS)v5.',0);
INSERT INTO "scope" VALUES('EPSG','1171','Geodesy. Defines NAD83(CSRS)v6.',0);
INSERT INTO "scope" VALUES('EPSG','1172','Geodesy. Defines NAD83(CSRS)v7.',0);
INSERT INTO "scope" VALUES('EPSG','1173','Geodesy. Defines NAD83(CORS96) from 1st January 1997 through 31st December 1999.',0);
INSERT INTO "scope" VALUES('EPSG','1174','Geodesy. Defines NAD83(CORS96) from January 2000 through December 2001.',0);
INSERT INTO "scope" VALUES('EPSG','1175','Geodesy. Defines NAD83(CORS96) from from 1st January 2002 through 6th September 2011.',0);
INSERT INTO "scope" VALUES('EPSG','1176','Geodesy. Navigation and positioning using GPS satellite system.',0);
INSERT INTO "scope" VALUES('EPSG','1177','Geodesy. Navigation and positioning using Glonass satellite system.',0);
INSERT INTO "scope" VALUES('EPSG','1178','Geodesy, engineering survey, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1179','Geodesy, engineering survey.',0);
INSERT INTO "scope" VALUES('EPSG','1180','Geodesy, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1181','Geodesy, cadastre, engineering survey, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1182','Geodesy; air, land and sea navigation and safety of life purposes.',0);
INSERT INTO "scope" VALUES('EPSG','1183','Horizontal component of 3D system.',0);
INSERT INTO "scope" VALUES('EPSG','1184','Geodesy, cadastre, engineering survey.',0);
INSERT INTO "scope" VALUES('EPSG','1185','Geodesy, cadastre.',0);
INSERT INTO "scope" VALUES('EPSG','1186','Geodesy, GIS.',0);
INSERT INTO "scope" VALUES('EPSG','1187','Geodesy, offshore minerals management.',0);
INSERT INTO "scope" VALUES('EPSG','1188','Geodesy, onshore minerals management.',0);
INSERT INTO "scope" VALUES('EPSG','1189','GIS.',0);
INSERT INTO "scope" VALUES('EPSG','1190','Geological analysis.',0);
INSERT INTO "scope" VALUES('EPSG','1191','Graticule coordinates expressed in simple Cartesian form.',0);
INSERT INTO "scope" VALUES('EPSG','1192','Graticule coordinates in rectangular Cartesian form.',0);
INSERT INTO "scope" VALUES('EPSG','1193','Geological analysis.',1);
INSERT INTO "scope" VALUES('EPSG','1194','Spatial referencing in and around city and county of San Francisco.',0);
INSERT INTO "scope" VALUES('EPSG','1195','Environmental science - used as basis for EASE grid.',0);
INSERT INTO "scope" VALUES('EPSG','1196','Highway engineering.',0);
INSERT INTO "scope" VALUES('EPSG','1197','Historic record only - now superseded - see remarks.',0);
INSERT INTO "scope" VALUES('EPSG','1198','Hydrography and nautical charting.',0);
INSERT INTO "scope" VALUES('EPSG','1199','Hydrography, drilling.',0);
INSERT INTO "scope" VALUES('EPSG','1200','Hydrography, drilling, marine geophysics.',0);
INSERT INTO "scope" VALUES('EPSG','1201','Hydrography, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1202','Hydrology.',0);
INSERT INTO "scope" VALUES('EPSG','1203','Intermediate stage in transformations - not used otherwise.',0);
INSERT INTO "scope" VALUES('EPSG','1204','Preliminary estimate.',0);
INSERT INTO "scope" VALUES('EPSG','1205','KOC exploration and field development subsurface work.',0);
INSERT INTO "scope" VALUES('EPSG','1206','KOC survey control and facilities engineering.',0);
INSERT INTO "scope" VALUES('EPSG','1207','Cadastre, topographic mapping (large scale).',0);
INSERT INTO "scope" VALUES('EPSG','1208','Engineering survey, topographic mapping (large scale).',0);
INSERT INTO "scope" VALUES('EPSG','1209','Topographic mapping (large scale).',0);
INSERT INTO "scope" VALUES('EPSG','1210','Topographic mapping (medium and small scale).',0);
INSERT INTO "scope" VALUES('EPSG','1211','Topographic mapping (medium scale).',0);
INSERT INTO "scope" VALUES('EPSG','1212','No official usage.',0);
INSERT INTO "scope" VALUES('EPSG','1213','Not a valid datum.',0);
INSERT INTO "scope" VALUES('EPSG','1214','Not recommended.',0);
INSERT INTO "scope" VALUES('EPSG','1215','Obsolete.',0);
INSERT INTO "scope" VALUES('EPSG','1216','Oil and gas exploration.',0);
INSERT INTO "scope" VALUES('EPSG','1217','Oil and gas exploration offshore.',0);
INSERT INTO "scope" VALUES('EPSG','1218','NNRMS 1:250,000 national resources database of coarse resolution earth observation imagery and mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1219','NNRMS National Spatial Framework (NSF) state resource database of medium resolution earth observation imagery and mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1220','Province-wide spatial data management.',0);
INSERT INTO "scope" VALUES('EPSG','1221','State-wide spatial data presentation requiring shape preservation.',0);
INSERT INTO "scope" VALUES('EPSG','1222','State-wide spatial data presentation requiring true area measurements.',0);
INSERT INTO "scope" VALUES('EPSG','1223','Very small scale equal-area mapping - Americas-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1224','Very small scale equal-area mapping - Asia-Pacific-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1225','Very small scale equal-area mapping - Europe-Africa-centred.',0);
INSERT INTO "scope" VALUES('EPSG','1226','Wellbore survey.',0);
INSERT INTO "scope" VALUES('EPSG','1227','Pan-European medium scale conformal mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1228','Very small scale conformal mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1229','Engineering design concept visualisation.',0);
INSERT INTO "scope" VALUES('EPSG','1230','User-defined CRS in GPS receiver.',0);
INSERT INTO "scope" VALUES('EPSG','1231','GeoNB Coordinate Transformation Service.',0);
INSERT INTO "scope" VALUES('EPSG','1232','GRANIT coordinate transformation programme between 1987 and 1997.',0);
INSERT INTO "scope" VALUES('EPSG','1233','US space and military operations.',0);
INSERT INTO "scope" VALUES('EPSG','1234','Transformation of GDA94 coordinates when localised distortion needs to be taken into account, e.g. if GDA94 coordinates were derived from survey control monuments.',0);
INSERT INTO "scope" VALUES('EPSG','1235','Approximation at the 0.01m level.',0);
INSERT INTO "scope" VALUES('EPSG','1236','Topographic mapping, environmental studies.',0);
INSERT INTO "scope" VALUES('EPSG','1237','Cadastre, hydrography, topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1238','Territory-wide spatial data management.',0);
INSERT INTO "scope" VALUES('EPSG','1239','Temporary transformation pending introduction of bilinear interpolation gridded dataset.',0);
INSERT INTO "scope" VALUES('EPSG','1240','Spatial analysis for the purposes of Natural Capital Accounting.',0);
INSERT INTO "scope" VALUES('EPSG','1241','Topographic mapping (small scale).',0);
INSERT INTO "scope" VALUES('EPSG','1242','Topographic and geological mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1243','Hydrography and aeronautical charting.',0);
INSERT INTO "scope" VALUES('EPSG','1244','enter as appropriate: seismic data acquisition / processing / interpretation',0);
INSERT INTO "scope" VALUES('EPSG','1245','Satellite navigation.',0);
INSERT INTO "scope" VALUES('EPSG','1246','Regional studies.',0);
INSERT INTO "scope" VALUES('EPSG','1247','Oceanography.',0);
INSERT INTO "scope" VALUES('EPSG','1248','Municipal spatial referencing.',0);
INSERT INTO "scope" VALUES('EPSG','1249','Minerals management (including oil and gas exploration and production).',0);
INSERT INTO "scope" VALUES('EPSG','1250','Metrication of RSO grid.',0);
INSERT INTO "scope" VALUES('EPSG','1251','Marine navigation.',0);
INSERT INTO "scope" VALUES('EPSG','1252','(null/copy) Approximation for medium and low accuracy applications assuming equality between plate-fixed static and earth-fixed dynamic CRSs, ignoring static/dynamic CRS differences.',0);
INSERT INTO "scope" VALUES('EPSG','1253','Approximation at the 1m level.',0);
INSERT INTO "scope" VALUES('EPSG','1254','Antarctic Digital Database and small scale topographic mapping.',0);
INSERT INTO "scope" VALUES('EPSG','1255','Transformation of coordinates at 0.02m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1256','Transformation of coordinates at accuracy of about 1 part in 10^5 of distance between points, depending on relative tectonic motion.',0);
INSERT INTO "scope" VALUES('EPSG','1257','Transformation of coordinates obtained independently of the classical geodetic network (GPS observations conducted after 1994).',0);
INSERT INTO "scope" VALUES('EPSG','1258','Products and services through the BEV Geoportal.',0);
INSERT INTO "scope" VALUES('EPSG','1259','Statistical mapping (small scale).',0);
INSERT INTO "scope" VALUES('EPSG','1260','Engineering survey for HS2 project phases 1 and 2a.',0);
INSERT INTO "scope" VALUES('EPSG','1261','Geodesy (gravity).',0);
INSERT INTO "scope" VALUES('EPSG','1262','Geodesy (GNSS), oceanography.',0);
INSERT INTO "scope" VALUES('EPSG','1263','Cadastre, engineering surveying applications over distances up to 10km.',0);
INSERT INTO "scope" VALUES('EPSG','1264','Geodesy, hydrography, transfer of accurate heights over distances greater than 10km.',0);
INSERT INTO "scope" VALUES('EPSG','1265','Hydrography, drilling, offshore engineering.',0);
INSERT INTO "scope" VALUES('EPSG','1266','Engineering survey, topographic mapping (large and medium scale).',0);
INSERT INTO "scope" VALUES('EPSG','1267','Location-based services, Intelligent Transport Services, navigation, positioning.',0);
INSERT INTO "scope" VALUES('EPSG','1268','Geodesy, location based services, intelligent transport services.',0);
INSERT INTO "scope" VALUES('EPSG','1269','Intermediate CRS in transformation to and from projected CRS.',0);
INSERT INTO "scope" VALUES('EPSG','1270','Reversible geoid model transformation.',0);
INSERT INTO "scope" VALUES('EPSG','1271','Engineering survey and mapping for the Trans-Europe Lyon-Turin (TELT) railway project.',0);
INSERT INTO "scope" VALUES('EPSG','1272','Reversible hydroid model transformation.',0);
INSERT INTO "scope" VALUES('EPSG','1273','Transformation of coordinates at 0.03m level of accuracy.',0);
INSERT INTO "scope" VALUES('EPSG','1274','Spatial referencing including water resource management.',0);
INSERT INTO "scope" VALUES('EPSG','1275','Alignment of datasets referenced to WGS 84 which have been derived from GDA2020 via the null transformation EPSG::8450 with GDA94 datasets.',0);
INSERT INTO "scope" VALUES('EPSG','1276','Alignment of datasets referenced to WGS 84 which have been derived from GDA94 via the null transformation EPSG::1150 with GDA2020 datasets.',0);
INSERT INTO "scope" VALUES('EPSG','1277','Derivation of depths from GNSS observations.',0);
