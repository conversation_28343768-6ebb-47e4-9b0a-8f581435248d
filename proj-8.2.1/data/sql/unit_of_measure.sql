--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "unit_of_measure" VALUES('EPSG','1024','(bin)','scale',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1025','millimetre','length',0.001,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1026','metre per second','length',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1027','millimetres per year','length',3.16887651727314875889e-11,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1028','parts per billion','scale',1.0e-09,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1029','year','time',31556925.445,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1030','parts per billion per year','scale',3.16887651727314834646e-17,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1031','milliarc-second','angle',4.84813681109535528357e-09,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1032','milliarc-seconds per year','angle',1.53631468932075975278e-16,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1033','centimetre','length',0.01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1034','centimetres per year','length',3.1688765172731483714e-10,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1035','radian per second','angle',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1036','unity per second','scale',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1040','second','time',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1041','parts per million per year','scale',3.1688765172731486173e-14,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1042','metres per year','length',3.16887651727314861947e-08,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','1043','arc-seconds per year','angle',1.53631468932075975646e-13,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9001','metre','length',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9002','foot','length',0.3048,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9003','US survey foot','length',3.04800609601219241184e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9005','Clarke''s foot','length',0.3047972654,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9014','fathom','length',1.8288,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9030','nautical mile','length',1852.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9031','German legal metre','length',1.0000135965,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9033','US survey chain','length',2.01168402336804703618e+01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9034','US survey link','length',2.0116840233680469141e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9035','US survey mile','length',1.60934721869443751532e+03,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9036','kilometre','length',1000.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9037','Clarke''s yard','length',0.9143917962,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9038','Clarke''s chain','length',20.1166195164,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9039','Clarke''s link','length',0.201166195164,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9040','British yard (Sears 1922)','length',9.14398414616028665236e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9041','British foot (Sears 1922)','length',3.04799471538676203241e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9042','British chain (Sears 1922)','length',2.01167651215526319683e+01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9043','British link (Sears 1922)','length',2.01167651215526294139e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9050','British yard (Benoit 1895 A)','length',0.9143992,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9051','British foot (Benoit 1895 A)','length',3.04799733333333322526e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9052','British chain (Benoit 1895 A)','length',20.1167824,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9053','British link (Benoit 1895 A)','length',0.201167824,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9060','British yard (Benoit 1895 B)','length',9.14399204289812361778e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9061','British foot (Benoit 1895 B)','length',3.04799734763270768755e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9062','British chain (Benoit 1895 B)','length',2.01167824943758724023e+01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9063','British link (Benoit 1895 B)','length',2.01167824943758705158e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9070','British foot (1865)','length',3.04800833333333354158e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9080','Indian foot','length',3.04799510248146943158e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9081','Indian foot (1937)','length',0.30479841,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9082','Indian foot (1962)','length',0.3047996,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9083','Indian foot (1975)','length',0.3047995,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9084','Indian yard','length',9.14398530744440773965e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9085','Indian yard (1937)','length',0.91439523,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9086','Indian yard (1962)','length',0.9143988,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9087','Indian yard (1975)','length',0.9143985,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9093','Statute mile','length',1609.344,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9094','Gold Coast foot','length',3.04799710181508809458e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9095','British foot (1936)','length',0.3048007491,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9096','yard','length',0.9144,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9097','chain','length',20.1168,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9098','link','length',0.201168,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9099','British yard (Sears 1922 truncated)','length',0.914398,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9101','radian','angle',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9102','degree','angle',1.74532925199432781271e-02,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9103','arc-minute','angle',2.90888208665721309346e-04,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9104','arc-second','angle',4.84813681109535476055e-06,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9105','grad','angle',1.57079632679489496205e-02,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9106','gon','angle',1.57079632679489496205e-02,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9107','degree minute second','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9108','degree minute second hemisphere','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9109','microradian','angle',1.0e-06,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9110','sexagesimal DMS','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9111','sexagesimal DM','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9112','centesimal minute','angle',1.57079632679489491868e-04,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9113','centesimal second','angle',1.57079632679489496951e-06,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9114','mil_6400','angle',9.81747704246809351283e-04,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9115','degree minute','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9116','degree hemisphere','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9117','hemisphere degree','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9118','degree minute hemisphere','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9119','hemisphere degree minute','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9120','hemisphere degree minute second','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9121','sexagesimal DMS.s','angle',NULL,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9122','degree (supplier to define representation)','angle',1.74532925199432781271e-02,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9201','unity','scale',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9202','parts per million','scale',1.0e-06,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9203','coefficient','scale',1.0,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9204','Bin width 330 US survey feet','length',1.00584201168402344707e+02,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9205','Bin width 165 US survey feet','length',5.02921005842011723538e+01,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9206','Bin width 82.5 US survey feet','length',2.51460502921005861769e+01,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9207','Bin width 37.5 metres','length',37.5,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9208','Bin width 25 metres','length',25.0,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9209','Bin width 12.5 metres','length',12.5,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9210','Bin width 6.25 metres','length',6.25,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9211','Bin width 3.125 metres','length',3.125,NULL,1);
INSERT INTO "unit_of_measure" VALUES('EPSG','9300','British foot (Sears 1922 truncated)','length',3.04799333333333366535e-01,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9301','British chain (Sears 1922 truncated)','length',20.116756,NULL,0);
INSERT INTO "unit_of_measure" VALUES('EPSG','9302','British link (Sears 1922 truncated)','length',0.20116756,NULL,0);
