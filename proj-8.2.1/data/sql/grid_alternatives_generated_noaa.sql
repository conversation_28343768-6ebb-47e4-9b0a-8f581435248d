--- This file has been generated by scripts/build_grid_alternatives_generated.py. DO NOT EDIT !

-- NADCON (NAD27 -> NAD83) entries

INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('conus.las',
                              'us_noaa_conus.tif',
                              'conus',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_conus.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('alaska.las',
                              'us_noaa_alaska.tif',
                              'alaska',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_alaska.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('hawaii.las',
                              'us_noaa_hawaii.tif',
                              'hawaii',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_hawaii.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('prvi.las',
                              'us_noaa_prvi.tif',
                              'prvi',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_prvi.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('stgeorge.las',
                              'us_noaa_stgeorge.tif',
                              'stgeorge',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_stgeorge.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('stlrnc.las',
                              'us_noaa_stlrnc.tif',
                              'stlrnc',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_stlrnc.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('stpaul.las',
                              'us_noaa_stpaul.tif',
                              'stpaul',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_stpaul.tif', 1, 1, NULL);
-- NAD83 -> NAD83(HPGN) entries

INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('alhpgn.las',
                              'us_noaa_alhpgn.tif',
                              'alhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_alhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('arhpgn.las',
                              'us_noaa_arhpgn.tif',
                              'arhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_arhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('azhpgn.las',
                              'us_noaa_azhpgn.tif',
                              'azhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_azhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('cnhpgn.las',
                              'us_noaa_cnhpgn.tif',
                              'cnhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_cnhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('cohpgn.las',
                              'us_noaa_cohpgn.tif',
                              'cohpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_cohpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('cshpgn.las',
                              'us_noaa_cshpgn.tif',
                              'cshpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_cshpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('emhpgn.las',
                              'us_noaa_emhpgn.tif',
                              'emhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_emhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('eshpgn.las',
                              'us_noaa_eshpgn.tif',
                              'eshpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_eshpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('ethpgn.las',
                              'us_noaa_ethpgn.tif',
                              'ethpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_ethpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('flhpgn.las',
                              'us_noaa_FL.tif',
                              'FL',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_FL.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('gahpgn.las',
                              'us_noaa_gahpgn.tif',
                              'gahpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_gahpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('guhpgn.las',
                              'us_noaa_guhpgn.tif',
                              'guhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_guhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('hihpgn.las',
                              'us_noaa_hihpgn.tif',
                              'hihpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_hihpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('iahpgn.las',
                              'us_noaa_iahpgn.tif',
                              'iahpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_iahpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('ilhpgn.las',
                              'us_noaa_ilhpgn.tif',
                              'ilhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_ilhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('inhpgn.las',
                              'us_noaa_inhpgn.tif',
                              'inhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_inhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('kshpgn.las',
                              'us_noaa_kshpgn.tif',
                              'kshpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_kshpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('kyhpgn.las',
                              'us_noaa_kyhpgn.tif',
                              'kyhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_kyhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('lahpgn.las',
                              'us_noaa_lahpgn.tif',
                              'lahpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_lahpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('mdhpgn.las',
                              'us_noaa_MD.tif',
                              'MD',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_MD.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('mehpgn.las',
                              'us_noaa_mehpgn.tif',
                              'mehpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_mehpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('mihpgn.las',
                              'us_noaa_mihpgn.tif',
                              'mihpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_mihpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('mnhpgn.las',
                              'us_noaa_mnhpgn.tif',
                              'mnhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_mnhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('mohpgn.las',
                              'us_noaa_mohpgn.tif',
                              'mohpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_mohpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('mshpgn.las',
                              'us_noaa_mshpgn.tif',
                              'mshpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_mshpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('nbhpgn.las',
                              'us_noaa_nbhpgn.tif',
                              'nbhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_nbhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('nchpgn.las',
                              'us_noaa_nchpgn.tif',
                              'nchpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_nchpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('ndhpgn.las',
                              'us_noaa_ndhpgn.tif',
                              'ndhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_ndhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('nehpgn.las',
                              'us_noaa_nehpgn.tif',
                              'nehpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_nehpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('njhpgn.las',
                              'us_noaa_njhpgn.tif',
                              'njhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_njhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('nmhpgn.las',
                              'us_noaa_nmhpgn.tif',
                              'nmhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_nmhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('nvhpgn.las',
                              'us_noaa_nvhpgn.tif',
                              'nvhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_nvhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('nyhpgn.las',
                              'us_noaa_nyhpgn.tif',
                              'nyhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_nyhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('ohhpgn.las',
                              'us_noaa_ohhpgn.tif',
                              'ohhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_ohhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('okhpgn.las',
                              'us_noaa_okhpgn.tif',
                              'okhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_okhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('pahpgn.las',
                              'us_noaa_pahpgn.tif',
                              'pahpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_pahpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('pvhpgn.las',
                              'us_noaa_pvhpgn.tif',
                              'pvhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_pvhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('schpgn.las',
                              'us_noaa_schpgn.tif',
                              'schpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_schpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('sdhpgn.las',
                              'us_noaa_sdhpgn.tif',
                              'sdhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_sdhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('tnhpgn.las',
                              'us_noaa_TN.tif',
                              'TN',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_TN.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('uthpgn.las',
                              'us_noaa_uthpgn.tif',
                              'uthpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_uthpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('vahpgn.las',
                              'us_noaa_vahpgn.tif',
                              'vahpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_vahpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('wihpgn.las',
                              'us_noaa_WI.tif',
                              'WI',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_WI.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('wmhpgn.las',
                              'us_noaa_wmhpgn.tif',
                              'wmhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_wmhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('wohpgn.las',
                              'us_noaa_WO.tif',
                              'WO',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_WO.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('wshpgn.las',
                              'us_noaa_wshpgn.tif',
                              'wshpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_wshpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('wthpgn.las',
                              'us_noaa_wthpgn.tif',
                              'wthpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_wthpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('wvhpgn.las',
                              'us_noaa_wvhpgn.tif',
                              'wvhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_wvhpgn.tif', 1, 1, NULL);
INSERT INTO grid_alternatives(original_grid_name,
                              proj_grid_name,
                              old_proj_grid_name,
                              proj_grid_format,
                              proj_method,
                              inverse_direction,
                              package_name,
                              url, direct_download, open_license, directory)
                      VALUES ('wyhpgn.las',
                              'us_noaa_wyhpgn.tif',
                              'wyhpgn.gsb',
                              'GTiff',
                              'hgridshift',
                              0,
                              NULL,
                              'https://cdn.proj.org/us_noaa_wyhpgn.tif', 1, 1, NULL);
