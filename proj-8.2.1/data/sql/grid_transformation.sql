--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "grid_transformation" VALUES('EPSG','1068','Guam 1963 to NAD83(HARN) (1)','NADCON method which expects longitudes positive west; EPSG GeogCRSs Guam 1963 and NAD83(HARN) (codes 4675 and 4152) have longitudes positive east. Can be used as approximation for tfm between Guam 1963 and WGS 84 - see tfm code 1069.','EPSG','9613','NADCON','EPSG','4675','EPSG','4152',5.0,'EPSG','8657','Latitude difference file','guhpgn.las','EPSG','8658','Longitude difference file','guhpgn.los',NULL,NULL,'NGS-Gum',0);
INSERT INTO "usage" VALUES('EPSG','7989','grid_transformation','EPSG','1068','EPSG','3255','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1069','Guam 1963 to WGS 84 (2)','Parameter files are from Guam 1963 to NAD83(HARN) (1) (code 1068), but for many purposes NAD83(HARN) can be considered to be coincident with WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4675','EPSG','4326',5.0,'EPSG','8657','Latitude difference file','guhpgn.las','EPSG','8658','Longitude difference file','guhpgn.los',NULL,NULL,'EPSG-Gum',0);
INSERT INTO "usage" VALUES('EPSG','7990','grid_transformation','EPSG','1069','EPSG','3255','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1241','NAD27 to NAD83 (1)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRS NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east. For application in Gulf of Mexico refer to IOGP report 373-26.','EPSG','9613','NADCON','EPSG','4267','EPSG','4269',0.15,'EPSG','8657','Latitude difference file','conus.las','EPSG','8658','Longitude difference file','conus.los',NULL,NULL,'NGS-Usa Conus',0);
INSERT INTO "usage" VALUES('EPSG','8162','grid_transformation','EPSG','1241','EPSG','2374','EPSG','1032');
INSERT INTO "grid_transformation" VALUES('EPSG','1243','NAD27 to NAD83 (2)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRS NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east. May be used as transformation to WGS 84 - see NAD27 to WGS 84 (85) (code 15864).','EPSG','9613','NADCON','EPSG','4267','EPSG','4269',0.5,'EPSG','8657','Latitude difference file','alaska.las','EPSG','8658','Longitude difference file','alaska.los',NULL,NULL,'NGS-Usa AK',0);
INSERT INTO "usage" VALUES('EPSG','8164','grid_transformation','EPSG','1243','EPSG','2373','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1295','RGNC91-93 to NEA74 Noumea (4)','Emulation using NTv2 method of tfm NEA74 Noumea to RGNC91-93 (3) (code 15943). Note reversal of sign of parameter values in grid file.','EPSG','9615','NTv2','EPSG','4749','EPSG','4644',0.05,'EPSG','8656','Latitude and longitude difference file','RGNC1991_NEA74Noumea.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ESRI-Ncl 0.05m',0);
INSERT INTO "usage" VALUES('EPSG','8216','grid_transformation','EPSG','1295','EPSG','2823','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1312','NAD27 to NAD83 (3)','Uses NTv1 method. Replaced in Quebec by code 1462 and elsewhere in 1997 by NTv2 (transformation code 1313). Input expects longitudes to be positive west; EPSG GeogCRS NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east.','EPSG','9614','NTv1','EPSG','4267','EPSG','4269',1.0,'EPSG','8656','Latitude and longitude difference file','NTv1_0.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GC-Can NT1',0);
INSERT INTO "usage" VALUES('EPSG','8233','grid_transformation','EPSG','1312','EPSG','4517','EPSG','1197');
INSERT INTO "grid_transformation" VALUES('EPSG','1313','NAD27 to NAD83 (4)','Uses NTv2 data files. Replaces NTv1 (transformation code 1312) except in Quebec. Input expects longitudes to be positive west; EPSG GeogCRS NAD27 (code 4267) and (code 4269) have longitudes positive east. May be used as tfm to WGS 84 - see code 1693.','EPSG','9615','NTv2','EPSG','4267','EPSG','4269',1.5,'EPSG','8656','Latitude and longitude difference file','NTv2_0.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GC-Can NT2',0);
INSERT INTO "usage" VALUES('EPSG','8234','grid_transformation','EPSG','1313','EPSG','4517','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1451','NAD27(CGQ77) to NAD83 (1)','Replaced by NAD27(CGQ77) to NAD83 (2) (code 1575). Uses NT method which expects longitudes positive west; EPSG GeogCRSs CGQ77 (code 4609) and NAD83 (code 4269) have longitudes positive east.','EPSG','9614','NTv1','EPSG','4609','EPSG','4269',1.0,'EPSG','8656','Latitude and longitude difference file','PQV4.DAC',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC NT1',0);
INSERT INTO "usage" VALUES('EPSG','8372','grid_transformation','EPSG','1451','EPSG','1368','EPSG','1197');
INSERT INTO "grid_transformation" VALUES('EPSG','1454','Old Hawaiian to NAD83 (1)','Accuracy 0.2m at 67% confidence level. Uses NADCON method which expects longitudes positive west; source and target CRSs have longitudes positive east. NADCON converts from Old Hawaiian Datum but makes the transformation appear to be from NAD27.','EPSG','9613','NADCON','EPSG','4135','EPSG','4269',0.2,'EPSG','8657','Latitude difference file','hawaii.las','EPSG','8658','Longitude difference file','hawaii.los',NULL,NULL,'NGS-Usa HI',0);
INSERT INTO "usage" VALUES('EPSG','8375','grid_transformation','EPSG','1454','EPSG','1334','EPSG','1032');
INSERT INTO "grid_transformation" VALUES('EPSG','1455','St. Lawrence Island to NAD83 (1)','Accuracy 0.5m at 67% confidence level. Uses NADCON method which expects longitudes positive west; source and target CRSs have longitudes positive east. NADCON data converts from St. Lawrence Datum but makes the transformation appear to be from NAD27.','EPSG','9613','NADCON','EPSG','4136','EPSG','4269',0.5,'EPSG','8657','Latitude difference file','stlrnc.las','EPSG','8658','Longitude difference file','stlrnc.los',NULL,NULL,'NGS-Usa AK StL',0);
INSERT INTO "usage" VALUES('EPSG','8376','grid_transformation','EPSG','1455','EPSG','1332','EPSG','1035');
INSERT INTO "grid_transformation" VALUES('EPSG','1456','St. Paul Island to NAD83 (1)','Accuracy 0.5m at 67% confidence level. Uses NADCON method which expects longitudes positive west; source and target CRSs have longitudes positive east. NADCON converts from St. Paul Datum but makes the transformation appear to be from NAD27.','EPSG','9613','NADCON','EPSG','4137','EPSG','4269',0.5,'EPSG','8657','Latitude difference file','stpaul.las','EPSG','8658','Longitude difference file','stpaul.los',NULL,NULL,'NGS-Usa AK StP',0);
INSERT INTO "usage" VALUES('EPSG','8377','grid_transformation','EPSG','1456','EPSG','1333','EPSG','1035');
INSERT INTO "grid_transformation" VALUES('EPSG','1457','St. George Island to NAD83 (1)','Accuracy 0.5m at 67% confidence level. Uses NADCON method which expects longitudes positive west; source and target CRSs have longitudes positive east. NADCON converts from St. George Datum but makes the transformation appear to be from NAD27.','EPSG','9613','NADCON','EPSG','4138','EPSG','4269',0.5,'EPSG','8657','Latitude difference file','stgeorge.las','EPSG','8658','Longitude difference file','stgeorge.los',NULL,NULL,'NGS-Usa AK StG',0);
INSERT INTO "usage" VALUES('EPSG','8378','grid_transformation','EPSG','1457','EPSG','1331','EPSG','1035');
INSERT INTO "grid_transformation" VALUES('EPSG','1461','Puerto Rico to NAD83 (1)','Accuracy 0.05m at 67% confidence level. May be taken as approximate transformation Puerto Rico-WGS 84 - see code 15841.','EPSG','9613','NADCON','EPSG','4139','EPSG','4269',0.05,'EPSG','8657','Latitude difference file','prvi.las','EPSG','8658','Longitude difference file','prvi.los',NULL,NULL,'NGS-PRVI',0);
INSERT INTO "usage" VALUES('EPSG','8382','grid_transformation','EPSG','1461','EPSG','1335','EPSG','1079');
INSERT INTO "grid_transformation" VALUES('EPSG','1462','NAD27 to NAD83 (5)','Densification for Quebec of code 1312. Replaced by NAD27 to NAD83 (6) (code 1573). Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east.','EPSG','9614','NTv1','EPSG','4267','EPSG','4269',1.0,'EPSG','8656','Latitude and longitude difference file','GS2783v1.QUE',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC NT1',0);
INSERT INTO "usage" VALUES('EPSG','8383','grid_transformation','EPSG','1462','EPSG','1368','EPSG','1197');
INSERT INTO "grid_transformation" VALUES('EPSG','1463','NAD27(76) to NAD83 (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27(76) (code 4608) and NAD83 (code 4269) have longitudes positive east. May be taken as approximate transformation NAD27(76) to WGS 84 - see code 1690.','EPSG','9615','NTv2','EPSG','4608','EPSG','4269',1.0,'EPSG','8656','Latitude and longitude difference file','May76v20.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can Ont',0);
INSERT INTO "usage" VALUES('EPSG','8384','grid_transformation','EPSG','1463','EPSG','1367','EPSG','1024');
INSERT INTO "grid_transformation" VALUES('EPSG','1464','AGD66 to GDA94 (5)','Replaced by AGD66 to GDA94 (10) (code 1596) and then by AGD66 to GDA94 (11) (code 1803). Input expects longitudes to be positive west; EPSG GeogCRS AGD66 (code 4202) and GDA94 (code 4283) both have longitudes positive east.','EPSG','9615','NTv2','EPSG','4202','EPSG','4283',0.1,'EPSG','8656','Latitude and longitude difference file','vic_0799.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OSG-Aus Vic old',0);
INSERT INTO "usage" VALUES('EPSG','8385','grid_transformation','EPSG','1464','EPSG','2285','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','1472','ATS77 to NAD83(CSRS98) (1)','Introduced in 1999. Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1688.','EPSG','9615','NTv2','EPSG','4122','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','NB7783v2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GIC-Can NB',1);
INSERT INTO "usage" VALUES('EPSG','8393','grid_transformation','EPSG','1472','EPSG','1447','EPSG','1025');
INSERT INTO "grid_transformation" VALUES('EPSG','1474','NAD83 to NAD83(HARN) (1)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east. May be taken as approximate transformation NAD83-WGS 84 - see code 1717.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','alhpgn.las','EPSG','8658','Longitude difference file','alhpgn.los',NULL,NULL,'NGS-Usa AL',0);
INSERT INTO "usage" VALUES('EPSG','8395','grid_transformation','EPSG','1474','EPSG','1372','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1475','NAD83 to NAD83(HARN) (2)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1728.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','azhpgn.las','EPSG','8658','Longitude difference file','azhpgn.los',NULL,NULL,'NGS-Usa AZ',0);
INSERT INTO "usage" VALUES('EPSG','8396','grid_transformation','EPSG','1475','EPSG','1373','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1476','NAD83 to NAD83(HARN) (3)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1739.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','cnhpgn.las','EPSG','8658','Longitude difference file','cnhpgn.los',NULL,NULL,'NGS-Usa CA n',0);
INSERT INTO "usage" VALUES('EPSG','8397','grid_transformation','EPSG','1476','EPSG','2297','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1477','NAD83 to NAD83(HARN) (4)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1750.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','cshpgn.las','EPSG','8658','Longitude difference file','cshpgn.los',NULL,NULL,'NGS-Usa CA s',0);
INSERT INTO "usage" VALUES('EPSG','8398','grid_transformation','EPSG','1477','EPSG','2298','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1478','NAD83 to NAD83(HARN) (5)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1712.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','cohpgn.las','EPSG','8658','Longitude difference file','cohpgn.los',NULL,NULL,'NGS-Usa CO',0);
INSERT INTO "usage" VALUES('EPSG','8399','grid_transformation','EPSG','1478','EPSG','1376','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1479','NAD83 to NAD83(HARN) (6)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1713.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','gahpgn.las','EPSG','8658','Longitude difference file','gahpgn.los',NULL,NULL,'NGS-Usa GA',0);
INSERT INTO "usage" VALUES('EPSG','8400','grid_transformation','EPSG','1479','EPSG','1380','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1480','NAD83 to NAD83(HARN) (7)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1714.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','flhpgn.las','EPSG','8658','Longitude difference file','flhpgn.los',NULL,NULL,'NGS-Usa FL',0);
INSERT INTO "usage" VALUES('EPSG','8401','grid_transformation','EPSG','1480','EPSG','1379','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1481','NAD83 to NAD83(HARN) (8)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east. May be taken as approximate transformation NAD83-WGS 84 - see code 1715.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','emhpgn.las','EPSG','8658','Longitude difference file','emhpgn.los',NULL,NULL,'NGS-Usa ID MT e',0);
INSERT INTO "usage" VALUES('EPSG','8402','grid_transformation','EPSG','1481','EPSG','2382','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1482','NAD83 to NAD83(HARN) (9)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1716.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','wmhpgn.las','EPSG','8658','Longitude difference file','wmhpgn.los',NULL,NULL,'NGS-Usa ID MT w',0);
INSERT INTO "usage" VALUES('EPSG','8403','grid_transformation','EPSG','1482','EPSG','2383','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1483','NAD83 to NAD83(HARN) (10)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1718.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','kyhpgn.las','EPSG','8658','Longitude difference file','kyhpgn.los',NULL,NULL,'NGS-Usa KY',0);
INSERT INTO "usage" VALUES('EPSG','8404','grid_transformation','EPSG','1483','EPSG','1386','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1484','NAD83 to NAD83(HARN) (11)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1719.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','lahpgn.las','EPSG','8658','Longitude difference file','lahpgn.los',NULL,NULL,'NGS-Usa LA',0);
INSERT INTO "usage" VALUES('EPSG','8405','grid_transformation','EPSG','1484','EPSG','1387','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1485','NAD83 to NAD83(HARN) (12)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1720.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','mdhpgn.las','EPSG','8658','Longitude difference file','mdhpgn.los',NULL,NULL,'NGS-Usa DE MD',0);
INSERT INTO "usage" VALUES('EPSG','8406','grid_transformation','EPSG','1485','EPSG','2377','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1486','NAD83 to NAD83(HARN) (13)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1721.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','mehpgn.las','EPSG','8658','Longitude difference file','mehpgn.los',NULL,NULL,'NGS-Usa ME',0);
INSERT INTO "usage" VALUES('EPSG','8407','grid_transformation','EPSG','1486','EPSG','1388','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1487','NAD83 to NAD83(HARN) (14)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1722.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','mihpgn.las','EPSG','8658','Longitude difference file','mihpgn.los',NULL,NULL,'NGS-Usa MI',0);
INSERT INTO "usage" VALUES('EPSG','8408','grid_transformation','EPSG','1487','EPSG','1391','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1488','NAD83 to NAD83(HARN) (15)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1723.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','mshpgn.las','EPSG','8658','Longitude difference file','mshpgn.los',NULL,NULL,'NGS-Usa MS',0);
INSERT INTO "usage" VALUES('EPSG','8409','grid_transformation','EPSG','1488','EPSG','1393','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1489','NAD83 to NAD83(HARN) (16)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1724.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nbhpgn.las','EPSG','8658','Longitude difference file','nbhpgn.los',NULL,NULL,'NGS-Usa NE',0);
INSERT INTO "usage" VALUES('EPSG','8410','grid_transformation','EPSG','1489','EPSG','1396','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1490','NAD83 to NAD83(HARN) (17)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1725.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nehpgn.las','EPSG','8658','Longitude difference file','nehpgn.los',NULL,NULL,'NGS-Usa NewEng',0);
INSERT INTO "usage" VALUES('EPSG','8411','grid_transformation','EPSG','1490','EPSG','2378','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1491','NAD83 to NAD83(HARN) (18)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1726.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nmhpgn.las','EPSG','8658','Longitude difference file','nmhpgn.los',NULL,NULL,'NGS-Usa NM',0);
INSERT INTO "usage" VALUES('EPSG','8412','grid_transformation','EPSG','1491','EPSG','1400','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1492','NAD83 to NAD83(HARN) (19)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1727.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nyhpgn.las','EPSG','8658','Longitude difference file','nyhpgn.los',NULL,NULL,'NGS-Usa NY',0);
INSERT INTO "usage" VALUES('EPSG','8413','grid_transformation','EPSG','1492','EPSG','1401','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1493','NAD83 to NAD83(HARN) (20)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1729.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','ndhpgn.las','EPSG','8658','Longitude difference file','ndhpgn.los',NULL,NULL,'NGS-Usa ND',0);
INSERT INTO "usage" VALUES('EPSG','8414','grid_transformation','EPSG','1493','EPSG','1403','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1494','NAD83 to NAD83(HARN) (21)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1730.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','okhpgn.las','EPSG','8658','Longitude difference file','okhpgn.los',NULL,NULL,'NGS-Usa OK',0);
INSERT INTO "usage" VALUES('EPSG','8415','grid_transformation','EPSG','1494','EPSG','1405','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1495','NAD83 to NAD83(HARN) (22)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1731.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','pvhpgn.las','EPSG','8658','Longitude difference file','pvhpgn.los',NULL,NULL,'NGS-PRVI',0);
INSERT INTO "usage" VALUES('EPSG','8416','grid_transformation','EPSG','1495','EPSG','3634','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1496','NAD83 to NAD83(HARN) (23)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1732.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','sdhpgn.las','EPSG','8658','Longitude difference file','sdhpgn.los',NULL,NULL,'NGS-Usa SD',0);
INSERT INTO "usage" VALUES('EPSG','8417','grid_transformation','EPSG','1496','EPSG','1410','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1497','NAD83 to NAD83(HARN) (24)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1733.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','tnhpgn.las','EPSG','8658','Longitude difference file','tnhpgn.los',NULL,NULL,'NGS-Usa TN',0);
INSERT INTO "usage" VALUES('EPSG','8418','grid_transformation','EPSG','1497','EPSG','1411','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1498','NAD83 to NAD83(HARN) (25)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1734.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','ethpgn.las','EPSG','8658','Longitude difference file','ethpgn.los',NULL,NULL,'NGS-Usa TX e',0);
INSERT INTO "usage" VALUES('EPSG','8419','grid_transformation','EPSG','1498','EPSG','2379','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1499','NAD83 to NAD83(HARN) (26)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1735.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','wthpgn.las','EPSG','8658','Longitude difference file','wthpgn.los',NULL,NULL,'NGS-Usa TX w',0);
INSERT INTO "usage" VALUES('EPSG','8420','grid_transformation','EPSG','1499','EPSG','2380','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1500','NAD83 to NAD83(HARN) (27)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1736.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','vahpgn.las','EPSG','8658','Longitude difference file','vahpgn.los',NULL,NULL,'NGS-Usa VA',0);
INSERT INTO "usage" VALUES('EPSG','8421','grid_transformation','EPSG','1500','EPSG','1415','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1501','NAD83 to NAD83(HARN) (28)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1737.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','wohpgn.las','EPSG','8658','Longitude difference file','wohpgn.los',NULL,NULL,'NGS-Usa OR WA',0);
INSERT INTO "usage" VALUES('EPSG','8422','grid_transformation','EPSG','1501','EPSG','2381','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1502','NAD83 to NAD83(HARN) (29)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1738.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','wihpgn.las','EPSG','8658','Longitude difference file','wihpgn.los',NULL,NULL,'NGS-Usa WI',0);
INSERT INTO "usage" VALUES('EPSG','8423','grid_transformation','EPSG','1502','EPSG','1418','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1503','NAD83 to NAD83(HARN) (30)','Accuracy 67% confidence level. Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1740.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','wyhpgn.las','EPSG','8658','Longitude difference file','wyhpgn.los',NULL,NULL,'NGS-Usa WY',0);
INSERT INTO "usage" VALUES('EPSG','8424','grid_transformation','EPSG','1503','EPSG','1419','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1506','AGD66 to GDA94 (6)','Replaced by AGD66 to GDA94 (11) (code 1803). Input expects longitudes to be positive west; EPSG GeogCRS AGD66 (code 4202) and GDA94 (code 4283) both have longitudes positive east.','EPSG','9615','NTv2','EPSG','4202','EPSG','4283',0.1,'EPSG','8656','Latitude and longitude difference file','tas_1098.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OSG-Tas 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','8427','grid_transformation','EPSG','1506','EPSG','1282','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','1507','AGD66 to GDA94 (7)','Replaced by AGD66 to GDA94 (11) (code 1803). Input expects longitudes to be positive west; EPSG GeogCRS AGD66 (code 4202) and GDA94 (code 4283) both have longitudes positive east.','EPSG','9615','NTv2','EPSG','4202','EPSG','4283',0.1,'EPSG','8656','Latitude and longitude difference file','nt_0599.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'Auslig-NT 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','8428','grid_transformation','EPSG','1507','EPSG','2284','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','1520','NAD83 to NAD83(HARN) (31)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1741.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','hihpgn.las','EPSG','8658','Longitude difference file','hihpgn.los',NULL,NULL,'NGS-Usa HI',0);
INSERT INTO "usage" VALUES('EPSG','8441','grid_transformation','EPSG','1520','EPSG','1334','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1521','NAD83 to NAD83(HARN) (32)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1742.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','inhpgn.las','EPSG','8658','Longitude difference file','inhpgn.los',NULL,NULL,'NGS-Usa IN',0);
INSERT INTO "usage" VALUES('EPSG','8442','grid_transformation','EPSG','1521','EPSG','1383','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1522','NAD83 to NAD83(HARN) (33)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1743.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','kshpgn.las','EPSG','8658','Longitude difference file','kshpgn.los',NULL,NULL,'NGS-Usa KS',0);
INSERT INTO "usage" VALUES('EPSG','8443','grid_transformation','EPSG','1522','EPSG','1385','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1523','NAD83 to NAD83(HARN) (34)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1744.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nvhpgn.las','EPSG','8658','Longitude difference file','nvhpgn.los',NULL,NULL,'NGS-Usa NV',0);
INSERT INTO "usage" VALUES('EPSG','8444','grid_transformation','EPSG','1523','EPSG','1397','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1524','NAD83 to NAD83(HARN) (35)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1745.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','ohhpgn.las','EPSG','8658','Longitude difference file','ohhpgn.los',NULL,NULL,'NGS-Usa OH',0);
INSERT INTO "usage" VALUES('EPSG','8445','grid_transformation','EPSG','1524','EPSG','1404','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1525','NAD83 to NAD83(HARN) (36)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1746.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','uthpgn.las','EPSG','8658','Longitude difference file','uthpgn.los',NULL,NULL,'NGS-Usa UT',0);
INSERT INTO "usage" VALUES('EPSG','8446','grid_transformation','EPSG','1525','EPSG','1413','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1526','NAD83 to NAD83(HARN) (37)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1747.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','wvhpgn.las','EPSG','8658','Longitude difference file','wvhpgn.los',NULL,NULL,'NGS-Usa WV',0);
INSERT INTO "usage" VALUES('EPSG','8447','grid_transformation','EPSG','1526','EPSG','1417','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1553','NAD83 to NAD83(HARN) (38)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1748.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','ilhpgn.las','EPSG','8658','Longitude difference file','ilhpgn.los',NULL,NULL,'NGS-Usa IL',0);
INSERT INTO "usage" VALUES('EPSG','8474','grid_transformation','EPSG','1553','EPSG','1382','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1554','NAD83 to NAD83(HARN) (39)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1749.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','njhpgn.las','EPSG','8658','Longitude difference file','njhpgn.los',NULL,NULL,'NGS-Usa NJ',0);
INSERT INTO "usage" VALUES('EPSG','8475','grid_transformation','EPSG','1554','EPSG','1399','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1559','AGD84 to GDA94 (3)','Withdrawn and replaced by AGD84 to GDA94 (4) (code 1593) due to binary file format error. Input expects longitudes to be positive west; EPSG GeogCRS AGD84 (code 4203) and GDA94 (code 4283) have longitudes positive east.','EPSG','9615','NTv2','EPSG','4203','EPSG','4283',0.1,'EPSG','8656','Latitude and longitude difference file','wa_0400.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'DOLA-Aus WA 0.1m old',1);
INSERT INTO "usage" VALUES('EPSG','8480','grid_transformation','EPSG','1559','EPSG','1280','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','1568','NZGD49 to NZGD2000 (3)','These same parameter values may be used to transform to WGS 84 - see NZGD49 to WGS 84 (4) (code 1670).','EPSG','9615','NTv2','EPSG','4272','EPSG','4167',0.2,'EPSG','8656','Latitude and longitude difference file','nzgd2kgrid0005.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OSG-Nzl 1m',0);
INSERT INTO "usage" VALUES('EPSG','8489','grid_transformation','EPSG','1568','EPSG','3285','EPSG','1032');
INSERT INTO "grid_transformation" VALUES('EPSG','1572','NAD83 to NAD83(CSRS98) (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(CSRS98) (code 4140) have longitudes positive east. Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1696.','EPSG','9615','NTv2','EPSG','4269','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','NAD83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',1);
INSERT INTO "usage" VALUES('EPSG','8493','grid_transformation','EPSG','1572','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1573','NAD27 to NAD83 (6)','Also distributed with file name QUE27-83.gsb. Replaces NAD27 to NAD83 (5) (code 1462). Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27 (code 4267) and NAD83 (code 4269) have longitudes positive east.','EPSG','9615','NTv2','EPSG','4267','EPSG','4269',1.5,'EPSG','8656','Latitude and longitude difference file','NA27NA83.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC NT2',0);
INSERT INTO "usage" VALUES('EPSG','8494','grid_transformation','EPSG','1573','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1574','NAD27 to NAD83(CSRS98) (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27 (code 4267) and NAD83(CSRS98) (code 4140) have longitudes positive east. Can be taken as an approximate transformation NAD27 to WGS 84 - see code 1692.','EPSG','9615','NTv2','EPSG','4267','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','QUE27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',1);
INSERT INTO "usage" VALUES('EPSG','8495','grid_transformation','EPSG','1574','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1575','NAD27(CGQ77) to NAD83 (2)','Replaces NAD27(CGQ77) to NAD83 (1) (code 1451). Can be taken as approx transformation to WGS 84 - see code 1691.','EPSG','9615','NTv2','EPSG','4609','EPSG','4269',1.5,'EPSG','8656','Latitude and longitude difference file','CQ77NA83.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC NT2',0);
INSERT INTO "usage" VALUES('EPSG','8496','grid_transformation','EPSG','1575','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1576','NAD27(CGQ77) to NAD83(CSRS98) (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27(CGQ77) (code 4609) and NAD83(CSRS98) (code 4140) have 1691longitudes positive east. Can be taken as an approximate transformation NAD27(CGQ77) to WGS 84 - see code 1691.','EPSG','9615','NTv2','EPSG','4609','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','CGQ77-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',1);
INSERT INTO "usage" VALUES('EPSG','8497','grid_transformation','EPSG','1576','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1578','American Samoa 1962 to NAD83(HARN) (1)','NADCON method which expects longitudes positive west; EPSG GeogCRSs American Samoa 1962 and NAD83(HARN) (codes 4169 and 4152) have longitudes positive east. NADCON expects latitudes in northern hemisphere and values must be made positive prior to input.','EPSG','9613','NADCON','EPSG','4169','EPSG','4152',5.0,'EPSG','8657','Latitude difference file','wshpgn.las','EPSG','8658','Longitude difference file','wshpgn.los',NULL,NULL,'NGS-Asm W',0);
INSERT INTO "usage" VALUES('EPSG','8499','grid_transformation','EPSG','1578','EPSG','2288','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1579','American Samoa 1962 to NAD83(HARN) (2)','NADCON method which expects longitudes positive west; EPSG GeogCRSs American Samoa 1962 and NAD83(HARN) (codes 4169 and 4152) have longitudes positive east. NADCON expects latitudes in northern hemisphere and values must be made positive prior to input.','EPSG','9613','NADCON','EPSG','4169','EPSG','4152',5.0,'EPSG','8657','Latitude difference file','eshpgn.las','EPSG','8658','Longitude difference file','eshpgn.los',NULL,NULL,'NGS-Asm E',0);
INSERT INTO "usage" VALUES('EPSG','8500','grid_transformation','EPSG','1579','EPSG','2289','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1593','AGD84 to GDA94 (4)','Replaces AGD84 to GDA94 (3) (code 1559) and then replaced by AGD84 to GDA94 (5) (code 1804). Input expects longitudes to be positive west; EPSG GeogCRS AGD84 (code 4203) and GDA94 (code 4283) both have longitudes positive east.','EPSG','9615','NTv2','EPSG','4203','EPSG','4283',0.1,'EPSG','8656','Latitude and longitude difference file','wa_0700.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'DOLA-Aus WA 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','8514','grid_transformation','EPSG','1593','EPSG','1280','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','1596','AGD66 to GDA94 (10)','Replaces AGD66 to GDA94 (5) (code 1464). Replaced by AGD66 to GDA94 (11) (code 1803). Input expects longitudes to be positive west; EPSG GeogCRS AGD66 (code 4202) and GDA94 (code 4283) both have longitudes positive east.','EPSG','9615','NTv2','EPSG','4202','EPSG','4283',0.1,'EPSG','8656','Latitude and longitude difference file','SEAust_21_06_00.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OSG-Aus SE 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','8517','grid_transformation','EPSG','1596','EPSG','2287','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','1599','ATS77 to NAD83(CSRS98) (2)','Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1689.','EPSG','9615','NTv2','EPSG','4122','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','PE7783V2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'PEI DOT-Can PEI',1);
INSERT INTO "usage" VALUES('EPSG','8520','grid_transformation','EPSG','1599','EPSG','1533','EPSG','1025');
INSERT INTO "grid_transformation" VALUES('EPSG','1600','NAD27 to NAD83(CSRS98) (2)','Can be taken as an approximate transformation NAD27 to WGS 84 - see code 1703.','EPSG','9615','NTv2','EPSG','4267','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','SK27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','8521','grid_transformation','EPSG','1600','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1601','NAD83 to NAD83(CSRS98) (2)','Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1697.','EPSG','9615','NTv2','EPSG','4269','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','SK83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','8522','grid_transformation','EPSG','1601','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1602','NAD83 to NAD83(CSRS98) (3)','This gridded difference file AB_CSRS.DAC will need to be renamed to AB_CSRS.gsb to run in some software suites. Formats identical, but AB file is provincial fit only.','EPSG','9615','NTv2','EPSG','4267','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','AB_CSRS.DAC',NULL,NULL,NULL,NULL,NULL,NULL,'AB Env-Can AB',1);
INSERT INTO "usage" VALUES('EPSG','8523','grid_transformation','EPSG','1602','EPSG','2376','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1670','NZGD49 to WGS 84 (3)','Parameter file is from NZGD49 to NZGD2000 (3) (code 1568) and assumes WGS 84 is coincident with NZGD2000 to the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4272','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','nzgd2kgrid0005.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Nzl 1m',0);
INSERT INTO "usage" VALUES('EPSG','8591','grid_transformation','EPSG','1670','EPSG','3285','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1688','ATS77 to WGS 84 (1)','Parameter file is from ATS77 to NAD83(CSRS)v2 (1) (code 9237) assuming that NAD83(CSRS)v2 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4122','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','NB7783v2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can NB',0);
INSERT INTO "usage" VALUES('EPSG','8609','grid_transformation','EPSG','1688','EPSG','1447','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1689','ATS77 to WGS 84 (2)','Parameter file is from ATS77 to NAD83(CSRS)v2 (2) (code 9236) assuming that NAD83(CSRS)v2 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4122','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','PE7783V2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can PEI',0);
INSERT INTO "usage" VALUES('EPSG','8610','grid_transformation','EPSG','1689','EPSG','1533','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1690','NAD27(76) to WGS 84 (1)','Parameter file is from NAD27(76) to NAD83 (1) (code 1463) assuming that NAD83 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4608','EPSG','4326',2.0,'EPSG','8656','Latitude and longitude difference file','May76v20.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can On',0);
INSERT INTO "usage" VALUES('EPSG','8611','grid_transformation','EPSG','1690','EPSG','1367','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1691','NAD27(CGQ77) to WGS 84 (3)','Parameter file is from NAD27(CGQ77) to NAD83 (2) (code 1575) assuming that NAD83(CSRS)v2 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4609','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','CQ77NA83.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can Qc NT2',0);
INSERT INTO "usage" VALUES('EPSG','8612','grid_transformation','EPSG','1691','EPSG','1368','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1692','NAD27 to WGS 84 (34)','Parameter file is from NAD27 to NAD83(CSRS)v2 (1) (code 9239) assuming that NAD83(CSRS)v2 is equivalent to WGS 84 within the accuracy of the transformation. Also distributed as QUE27-98.gsb.','EPSG','9615','NTv2','EPSG','4267','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','NA27SCRS.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can QC',0);
INSERT INTO "usage" VALUES('EPSG','8613','grid_transformation','EPSG','1692','EPSG','1368','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1693','NAD27 to WGS 84 (33)','Parameter file is from NAD27 to NAD83 (4) (code 1313) assuming that NAD83 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4267','EPSG','4326',2.0,'EPSG','8656','Latitude and longitude difference file','NTv2_0.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can',0);
INSERT INTO "usage" VALUES('EPSG','8614','grid_transformation','EPSG','1693','EPSG','4517','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1694','American Samoa 1962 to WGS 84 (2)','Parameter files are from American Samoa 1962 to NAD83(HARN) (1) (code 1578), but for many purposes NAD83(HARN) can be considered to be coincident with WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4169','EPSG','4326',5.0,'EPSG','8657','Latitude difference file','wshpgn.las','EPSG','8658','Longitude difference file','wshpgn.los',NULL,NULL,'EPSG-Asm W',0);
INSERT INTO "usage" VALUES('EPSG','8615','grid_transformation','EPSG','1694','EPSG','2288','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1695','American Samoa 1962 to WGS 84 (3)','Parameter files are from American Samoa 1962 to NAD83(HARN) (2) (code 1579), but for many purposes NAD83(HARN) can be considered to be coincident with WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4169','EPSG','4326',5.0,'EPSG','8657','Latitude difference file','eshpgn.las','EPSG','8658','Longitude difference file','eshpgn.los',NULL,NULL,'EPSG-Asm E',0);
INSERT INTO "usage" VALUES('EPSG','8616','grid_transformation','EPSG','1695','EPSG','2289','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1696','NAD83 to WGS 84 (6)','Parameter file is from NAD83 to NAD83(CSRS)v2 (1) (code 9241) assuming that NAD83(CSRS)v2 is equivalent to WGS 84 within the accuracy of the transformation. Also distributed with file name NAD83-98.gsb.','EPSG','9615','NTv2','EPSG','4269','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','NA83SCRS.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can QC',0);
INSERT INTO "usage" VALUES('EPSG','8617','grid_transformation','EPSG','1696','EPSG','1368','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1697','NAD83 to WGS 84 (7)','Parameter file is from NAD83 to NAD83(CSRS)v2 (2) (code 9887) assuming that NAD83(CSRS)v2 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4269','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','SK83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can SK',0);
INSERT INTO "usage" VALUES('EPSG','8618','grid_transformation','EPSG','1697','EPSG','2375','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1698','St. George Island to WGS 84 (1)','Parameter files are from St. George Island to NAD83 (1) (code 1457) assuming that NAD83 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4138','EPSG','4326',1.5,'EPSG','8657','Latitude difference file','stgeorge.las','EPSG','8658','Longitude difference file','stgeorge.los',NULL,NULL,'EPSG-Usa AK StG',0);
INSERT INTO "usage" VALUES('EPSG','8619','grid_transformation','EPSG','1698','EPSG','1331','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1699','St. Lawrence Island to WGS 84 (1)','Parameter files are from St. Lawrence Island to NAD83 (1) (code 1455) assuming that NAD83 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4136','EPSG','4326',1.5,'EPSG','8657','Latitude difference file','stlrnc.las','EPSG','8658','Longitude difference file','stlrnc.los',NULL,NULL,'EPSG-Usa AK StL',0);
INSERT INTO "usage" VALUES('EPSG','8620','grid_transformation','EPSG','1699','EPSG','1332','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1700','St. Paul Island to WGS 84 (1)','Parameter files are from St. Paul Island to NAD83 (1) (code 1456) assuming that NAD83 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4137','EPSG','4326',1.5,'EPSG','8657','Latitude difference file','stpaul.las','EPSG','8658','Longitude difference file','stpaul.los',NULL,NULL,'EPSG-Usa AK StP',0);
INSERT INTO "usage" VALUES('EPSG','8621','grid_transformation','EPSG','1700','EPSG','1333','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1702','NAD83 to WGS 84 (8)','Parameter file is from NAD83 to NAD83(CSRS)v4 (3) (code 9244) assuming that NAD83(CSRS)v4 is equivalent to WGS 84 within the accuracy of the transformation. This file AB_CSRS.DAC will need to be renamed to AB_CSRS.gsb to run in some software.','EPSG','9615','NTv2','EPSG','4269','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','AB_CSRS.DAC',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can AB',0);
INSERT INTO "usage" VALUES('EPSG','8623','grid_transformation','EPSG','1702','EPSG','2376','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1703','NAD27 to WGS 84 (32)','Parameter file is from NAD27 to NAD83(CSRS)v2 (2) (code 9886) assuming that NAD83(CSRS)v2 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4267','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','SK27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can SK',0);
INSERT INTO "usage" VALUES('EPSG','8624','grid_transformation','EPSG','1703','EPSG','2375','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1704','NAD83 to NAD83(HARN) (40)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1708.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','arhpgn.las','EPSG','8658','Longitude difference file','arhpgn.los',NULL,NULL,'NGS-Usa AR',0);
INSERT INTO "usage" VALUES('EPSG','8625','grid_transformation','EPSG','1704','EPSG','1374','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1705','NAD83 to NAD83(HARN) (41)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east. May be taken as approximate transformation NAD83-WGS 84 - see code 1709.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','iahpgn.las','EPSG','8658','Longitude difference file','iahpgn.los',NULL,NULL,'NGS-Usa IA',0);
INSERT INTO "usage" VALUES('EPSG','8626','grid_transformation','EPSG','1705','EPSG','1384','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1706','NAD83 to NAD83(HARN) (42)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 1710.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','mnhpgn.las','EPSG','8658','Longitude difference file','mnhpgn.los',NULL,NULL,'NGS-Usa MN',0);
INSERT INTO "usage" VALUES('EPSG','8627','grid_transformation','EPSG','1706','EPSG','1392','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1707','NAD83 to NAD83(HARN) (43)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east. May be taken as approximate transformation NAD83-WGS 84 - see code 1711.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','mohpgn.las','EPSG','8658','Longitude difference file','mohpgn.los',NULL,NULL,'NGS-Usa MO',0);
INSERT INTO "usage" VALUES('EPSG','8628','grid_transformation','EPSG','1707','EPSG','1394','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','1708','NAD83 to WGS 84 (12)','Parameter files are from NAD83 to NAD83(HARN) (40) (code 1704) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','arhpgn.las','EPSG','8658','Longitude difference file','arhpgn.los',NULL,NULL,'EPSG-USA Ar',0);
INSERT INTO "usage" VALUES('EPSG','8629','grid_transformation','EPSG','1708','EPSG','1374','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1709','NAD83 to WGS 84 (13)','Parameter files are from NAD83 to NAD83(HARN) (41) (code 1705) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','iahpgn.las','EPSG','8658','Longitude difference file','iahpgn.los',NULL,NULL,'EPSG-Usa IA',0);
INSERT INTO "usage" VALUES('EPSG','8630','grid_transformation','EPSG','1709','EPSG','1384','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1710','NAD83 to WGS 84 (14)','Parameter files are from NAD83 to NAD83(HARN) (42) (code 1706) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','mnhpgn.las','EPSG','8658','Longitude difference file','mnhpgn.los',NULL,NULL,'EPSG-Usa MN',0);
INSERT INTO "usage" VALUES('EPSG','8631','grid_transformation','EPSG','1710','EPSG','1392','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1711','NAD83 to WGS 84 (15)','Parameter files are from NAD83 to NAD83(HARN) (43) (code 1707) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','mohpgn.las','EPSG','8658','Longitude difference file','mohpgn.los',NULL,NULL,'EPSG-Usa MO',0);
INSERT INTO "usage" VALUES('EPSG','8632','grid_transformation','EPSG','1711','EPSG','1394','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1712','NAD83 to WGS 84 (16)','Parameter files are from NAD83 to NAD83(HARN) (5) (code 1478) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','cohpgn.las','EPSG','8658','Longitude difference file','cohpgn.los',NULL,NULL,'EPSG-Usa CO',0);
INSERT INTO "usage" VALUES('EPSG','8633','grid_transformation','EPSG','1712','EPSG','1376','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1713','NAD83 to WGS 84 (17)','Parameter files are from NAD83 to NAD83(HARN) (6) (code 1479) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','gahpgn.las','EPSG','8658','Longitude difference file','gahpgn.los',NULL,NULL,'EPSG-Usa GA',0);
INSERT INTO "usage" VALUES('EPSG','8634','grid_transformation','EPSG','1713','EPSG','1380','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1714','NAD83 to WGS 84 (18)','Parameter files are from NAD83 to NAD83(HARN) (7) (code 1480) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','flhpgn.las','EPSG','8658','Longitude difference file','flhpgn.los',NULL,NULL,'EPSG-Usa FL',0);
INSERT INTO "usage" VALUES('EPSG','8635','grid_transformation','EPSG','1714','EPSG','1379','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1715','NAD83 to WGS 84 (19)','Parameter files are from NAD83 to NAD83(HARN) (8) (code 1481) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','emhpgn.las','EPSG','8658','Longitude difference file','emhpgn.los',NULL,NULL,'EPSG-Usa ID MT e',0);
INSERT INTO "usage" VALUES('EPSG','8636','grid_transformation','EPSG','1715','EPSG','2382','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1716','NAD83 to WGS 84 (20)','Parameter files are from NAD83 to NAD83(HARN) (9) (code 1482) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','wmhpgn.las','EPSG','8658','Longitude difference file','wmhpgn.los',NULL,NULL,'EPSG-Usa ID MT w',0);
INSERT INTO "usage" VALUES('EPSG','8637','grid_transformation','EPSG','1716','EPSG','2383','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1717','NAD83 to WGS 84 (21)','Parameter files are from NAD83 to NAD83(HARN) (1) (code 1474) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','alhpgn.las','EPSG','8658','Longitude difference file','alhpgn.los',NULL,NULL,'EPSG-Usa AL',0);
INSERT INTO "usage" VALUES('EPSG','8638','grid_transformation','EPSG','1717','EPSG','1372','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1718','NAD83 to WGS 84 (22)','Parameter files are from NAD83 to NAD83(HARN) (10) (code 1483) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','kyhpgn.las','EPSG','8658','Longitude difference file','kyhpgn.los',NULL,NULL,'EPSG-Usa KY',0);
INSERT INTO "usage" VALUES('EPSG','8639','grid_transformation','EPSG','1718','EPSG','1386','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1719','NAD83 to WGS 84 (23)','Parameter files are from NAD83 to NAD83(HARN) (11) (code 1484) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','lahpgn.las','EPSG','8658','Longitude difference file','lahpgn.los',NULL,NULL,'EPSG-Usa LA',0);
INSERT INTO "usage" VALUES('EPSG','8640','grid_transformation','EPSG','1719','EPSG','1387','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1720','NAD83 to WGS 84 (24)','Parameter files are from NAD83 to NAD83(HARN) (12) (code 1485) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','mdhpgn.las','EPSG','8658','Longitude difference file','mdhpgn.los',NULL,NULL,'EPSG-Usa DE MD',0);
INSERT INTO "usage" VALUES('EPSG','8641','grid_transformation','EPSG','1720','EPSG','2377','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1721','NAD83 to WGS 84 (25)','Parameter files are from NAD83 to NAD83(HARN) (13) (code 1486) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','mehpgn.las','EPSG','8658','Longitude difference file','mehpgn.los',NULL,NULL,'EPSG-Usa ME',0);
INSERT INTO "usage" VALUES('EPSG','8642','grid_transformation','EPSG','1721','EPSG','1388','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1722','NAD83 to WGS 84 (26)','Parameter files are from NAD83 to NAD83(HARN) (14) (code 1487) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','mihpgn.las','EPSG','8658','Longitude difference file','mihpgn.los',NULL,NULL,'EPSG-Usa MI',0);
INSERT INTO "usage" VALUES('EPSG','8643','grid_transformation','EPSG','1722','EPSG','1391','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1723','NAD83 to WGS 84 (27)','Parameter files are from NAD83 to NAD83(HARN) (15) (code 1488) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','mshpgn.las','EPSG','8658','Longitude difference file','mshpgn.los',NULL,NULL,'EPSG-Usa MS',0);
INSERT INTO "usage" VALUES('EPSG','8644','grid_transformation','EPSG','1723','EPSG','1393','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1724','NAD83 to WGS 84 (28)','Parameter files are from NAD83 to NAD83(HARN) (16) (code 1489) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','nbhpgn.las','EPSG','8658','Longitude difference file','nbhpgn.los',NULL,NULL,'EPSG-Usa NE',0);
INSERT INTO "usage" VALUES('EPSG','8645','grid_transformation','EPSG','1724','EPSG','1396','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1725','NAD83 to WGS 84 (29)','Parameter files are from NAD83 to NAD83(HARN) (17) (code 1490) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','nehpgn.las','EPSG','8658','Longitude difference file','nehpgn.los',NULL,NULL,'EPSG-Usa NewEng',0);
INSERT INTO "usage" VALUES('EPSG','8646','grid_transformation','EPSG','1725','EPSG','2378','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1726','NAD83 to WGS 84 (30)','Parameter files are from NAD83 to NAD83(HARN) (18) (code 1491) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','nmhpgn.las','EPSG','8658','Longitude difference file','nmhpgn.los',NULL,NULL,'EPSG-Usa NM',0);
INSERT INTO "usage" VALUES('EPSG','8647','grid_transformation','EPSG','1726','EPSG','1400','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1727','NAD83 to WGS 84 (31)','Parameter files are from NAD83 to NAD83(HARN) (19) (code 1492) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','nyhpgn.las','EPSG','8658','Longitude difference file','nyhpgn.los',NULL,NULL,'EPSG-Usa NY',0);
INSERT INTO "usage" VALUES('EPSG','8648','grid_transformation','EPSG','1727','EPSG','1401','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1728','NAD83 to WGS 84 (32)','Parameter files are from NAD83 to NAD83(HARN) (2) (code 1475) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','azhpgn.las','EPSG','8658','Longitude difference file','azhpgn.los',NULL,NULL,'EPSG-Usa AZ',0);
INSERT INTO "usage" VALUES('EPSG','8649','grid_transformation','EPSG','1728','EPSG','1373','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1729','NAD83 to WGS 84 (33)','Parameter files are from NAD83 to NAD83(HARN) (20) (code 1493) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','ndhpgn.las','EPSG','8658','Longitude difference file','ndhpgn.los',NULL,NULL,'EPSG-Usa ND',0);
INSERT INTO "usage" VALUES('EPSG','8650','grid_transformation','EPSG','1729','EPSG','1403','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1730','NAD83 to WGS 84 (34)','Parameter files are from NAD83 to NAD83(HARN) (21) (code 1494) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','okhpgn.las','EPSG','8658','Longitude difference file','okhpgn.los',NULL,NULL,'EPSG-Usa OK',0);
INSERT INTO "usage" VALUES('EPSG','8651','grid_transformation','EPSG','1730','EPSG','1405','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1731','NAD83 to WGS 84 (35)','Parameter files are from NAD83 to NAD83(HARN) (22) (code 1495) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','pvhpgn.las','EPSG','8658','Longitude difference file','pvhpgn.los',NULL,NULL,'EPSG-PRVI',0);
INSERT INTO "usage" VALUES('EPSG','8652','grid_transformation','EPSG','1731','EPSG','3634','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1732','NAD83 to WGS 84 (36)','Parameter files are from NAD83 to NAD83(HARN) (23) (code 1496) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','sdhpgn.las','EPSG','8658','Longitude difference file','sdhpgn.los',NULL,NULL,'EPSG-Usa SD',0);
INSERT INTO "usage" VALUES('EPSG','8653','grid_transformation','EPSG','1732','EPSG','1410','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1733','NAD83 to WGS 84 (37)','Parameter files are from NAD83 to NAD83(HARN) (24) (code 1497) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','tnhpgn.las','EPSG','8658','Longitude difference file','tnhpgn.los',NULL,NULL,'EPSG-Usa TN',0);
INSERT INTO "usage" VALUES('EPSG','8654','grid_transformation','EPSG','1733','EPSG','1411','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1734','NAD83 to WGS 84 (38)','Parameter files are from NAD83 to NAD83(HARN) (25) (code 1498) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','ethpgn.las','EPSG','8658','Longitude difference file','ethpgn.los',NULL,NULL,'EPSG-Usa TX e',0);
INSERT INTO "usage" VALUES('EPSG','8655','grid_transformation','EPSG','1734','EPSG','2379','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1735','NAD83 to WGS 84 (39)','Parameter files are from NAD83 to NAD83(HARN) (26) (code 1499) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','wthpgn.las','EPSG','8658','Longitude difference file','wthpgn.los',NULL,NULL,'EPSG-Usa TX w',0);
INSERT INTO "usage" VALUES('EPSG','8656','grid_transformation','EPSG','1735','EPSG','2380','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1736','NAD83 to WGS 84 (40)','Parameter files are from NAD83 to NAD83(HARN) (27) (code 1500) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','vahpgn.las','EPSG','8658','Longitude difference file','vahpgn.los',NULL,NULL,'EPSG-Usa VA',0);
INSERT INTO "usage" VALUES('EPSG','8657','grid_transformation','EPSG','1736','EPSG','1415','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1737','NAD83 to WGS 84 (41)','Parameter files are from NAD83 to NAD83(HARN) (28) (code 1501) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','wohpgn.las','EPSG','8658','Longitude difference file','wohpgn.los',NULL,NULL,'EPSG-Usa OR WA',0);
INSERT INTO "usage" VALUES('EPSG','8658','grid_transformation','EPSG','1737','EPSG','2381','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1738','NAD83 to WGS 84 (42)','Parameter files are from NAD83 to NAD83(HARN) (29) (code 1502) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','wihpgn.las','EPSG','8658','Longitude difference file','wihpgn.los',NULL,NULL,'EPSG-Usa WI',0);
INSERT INTO "usage" VALUES('EPSG','8659','grid_transformation','EPSG','1738','EPSG','1418','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1739','NAD83 to WGS 84 (43)','Parameter files are from NAD83 to NAD83(HARN) (3) (code 1476) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','cnhpgn.las','EPSG','8658','Longitude difference file','cnhpgn.los',NULL,NULL,'EPSG-Usa CA n',0);
INSERT INTO "usage" VALUES('EPSG','8660','grid_transformation','EPSG','1739','EPSG','2297','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1740','NAD83 to WGS 84 (44)','Parameter files are from NAD83 to NAD83(HARN) (30) (code 1503) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','wyhpgn.las','EPSG','8658','Longitude difference file','wyhpgn.los',NULL,NULL,'EPSG-Usa WY',0);
INSERT INTO "usage" VALUES('EPSG','8661','grid_transformation','EPSG','1740','EPSG','1419','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1741','NAD83 to WGS 84 (45)','Parameter files are from NAD83 to NAD83(HARN) (31) (code 1520) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','hihpgn.las','EPSG','8658','Longitude difference file','hihpgn.los',NULL,NULL,'EPSG-Usa HI',0);
INSERT INTO "usage" VALUES('EPSG','8662','grid_transformation','EPSG','1741','EPSG','1334','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1742','NAD83 to WGS 84 (46)','Parameter files are from NAD83 to NAD83(HARN) (32) (code 1521) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','inhpgn.las','EPSG','8658','Longitude difference file','inhpgn.los',NULL,NULL,'EPSG-Usa IN',0);
INSERT INTO "usage" VALUES('EPSG','8663','grid_transformation','EPSG','1742','EPSG','1383','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1743','NAD83 to WGS 84 (47)','Parameter files are from NAD83 to NAD83(HARN) (33) (code 1522) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','kshpgn.las','EPSG','8658','Longitude difference file','kshpgn.los',NULL,NULL,'EPSG-Usa KS',0);
INSERT INTO "usage" VALUES('EPSG','8664','grid_transformation','EPSG','1743','EPSG','1385','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1744','NAD83 to WGS 84 (48)','Parameter files are from NAD83 to NAD83(HARN) (34) (code 1523) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','nvhpgn.las','EPSG','8658','Longitude difference file','nvhpgn.los',NULL,NULL,'EPSG-Usa NV',0);
INSERT INTO "usage" VALUES('EPSG','8665','grid_transformation','EPSG','1744','EPSG','1397','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1745','NAD83 to WGS 84 (49)','Parameter files are from NAD83 to NAD83(HARN) (35) (code 1524) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','ohhpgn.las','EPSG','8658','Longitude difference file','ohhpgn.los',NULL,NULL,'EPSG-Usa OH',0);
INSERT INTO "usage" VALUES('EPSG','8666','grid_transformation','EPSG','1745','EPSG','1404','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1746','NAD83 to WGS 84 (50)','Parameter files are from NAD83 to NAD83(HARN) (36) (code 1525) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','uthpgn.las','EPSG','8658','Longitude difference file','uthpgn.los',NULL,NULL,'EPSG-Usa UT',0);
INSERT INTO "usage" VALUES('EPSG','8667','grid_transformation','EPSG','1746','EPSG','1413','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1747','NAD83 to WGS 84 (51)','Parameter files are from NAD83 to NAD83(HARN) (37) (code 1526) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','wvhpgn.las','EPSG','8658','Longitude difference file','wvhpgn.los',NULL,NULL,'EPSG-Usa WV',0);
INSERT INTO "usage" VALUES('EPSG','8668','grid_transformation','EPSG','1747','EPSG','1417','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1748','NAD83 to WGS 84 (52)','Parameter files are from NAD83 to NAD83(HARN) (38) (code 1553) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','ilhpgn.las','EPSG','8658','Longitude difference file','ilhpgn.los',NULL,NULL,'EPSG-Usa IL',0);
INSERT INTO "usage" VALUES('EPSG','8669','grid_transformation','EPSG','1748','EPSG','1382','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1749','NAD83 to WGS 84 (53)','Parameter files are from NAD83 to NAD83(HARN) (39) (code 1554) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','njhpgn.las','EPSG','8658','Longitude difference file','njhpgn.los',NULL,NULL,'EPSG-Usa NJ',0);
INSERT INTO "usage" VALUES('EPSG','8670','grid_transformation','EPSG','1749','EPSG','1399','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1750','NAD83 to WGS 84 (54)','Parameter files are from NAD83 to NAD83(HARN) (4) (code 1477) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','cshpgn.las','EPSG','8658','Longitude difference file','cshpgn.los',NULL,NULL,'EPSG-Usa CA s',0);
INSERT INTO "usage" VALUES('EPSG','8671','grid_transformation','EPSG','1750','EPSG','2298','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','1752','NAD83 to NAD83(CSRS98) (3)','This gridded difference file AB_CSRS.DAC will need to be renamed to AB_CSRS.gsb to run in some software suites. Formats identical, but AB file is provincial fit only. Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1702.','EPSG','9615','NTv2','EPSG','4269','EPSG','4140',NULL,'EPSG','8656','Latitude and longitude difference file','AB_CSRS.DAC',NULL,NULL,NULL,NULL,NULL,NULL,'AB Env-Can AB',1);
INSERT INTO "usage" VALUES('EPSG','8673','grid_transformation','EPSG','1752','EPSG','2376','EPSG','1025');
INSERT INTO "grid_transformation" VALUES('EPSG','1803','AGD66 to GDA94 (11)','Replaces AGD66 to GDA94 variants 6, 7 and 10 (codes 1506 1507 1596). Input expects longitudes to be positive west; source and target CRSs AGD66 (code 4202) and GDA94 (code 4283) both have longitudes positive east.','EPSG','9615','NTv2','EPSG','4202','EPSG','4283',0.5,'EPSG','8656','Latitude and longitude difference file','A66 National (13.09.01).gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICSM-Aus 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','8724','grid_transformation','EPSG','1803','EPSG','2575','EPSG','1035');
INSERT INTO "grid_transformation" VALUES('EPSG','1804','AGD84 to GDA94 (5)','Replaces AGD84 to GDA94 (4) (code 1593) which itself replaced variant 3 (code 1559). Input expects longitudes to be + west; EPSG GeogCRS AGD84 (code 4203) and GDA94 (code 4283) both have longitudes positive east. May be used as tfm to WGS 84 - see 15785','EPSG','9615','NTv2','EPSG','4203','EPSG','4283',0.1,'EPSG','8656','Latitude and longitude difference file','National 84 (02.07.01).gsb',NULL,NULL,NULL,NULL,NULL,NULL,'Auslig-Aus 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','14199','grid_transformation','EPSG','1804','EPSG','2576','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','1841','ATS77 to NAD83(CSRS) (1)','Introduced in 1999. Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1688.','EPSG','9615','NTv2','EPSG','4122','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','NB7783v2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GIC-Can NB',1);
INSERT INTO "usage" VALUES('EPSG','8762','grid_transformation','EPSG','1841','EPSG','1447','EPSG','1231');
INSERT INTO "grid_transformation" VALUES('EPSG','1843','NAD83 to NAD83(CSRS) (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(CSRS) (code 4617) have longitudes positive east. Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1696.','EPSG','9615','NTv2','EPSG','4269','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','NAD83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',1);
INSERT INTO "usage" VALUES('EPSG','8764','grid_transformation','EPSG','1843','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1844','NAD27 to NAD83(CSRS) (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27 (code 4267) and NAD83(CSRS) (code 4617) have longitudes positive east. Can be taken as an approximate transformation NAD27 to WGS 84 - see code 1692.','EPSG','9615','NTv2','EPSG','4267','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','QUE27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',1);
INSERT INTO "usage" VALUES('EPSG','8765','grid_transformation','EPSG','1844','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1845','NAD27(CGQ77) to NAD83(CSRS) (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27(CGQ77) (code 4609) and NAD83(CSRS) (code 4617) have longitudes positive east. Can be taken as an approximate transformation NAD27(CGQ77) to WGS 84 - see code 1691.','EPSG','9615','NTv2','EPSG','4609','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','CGQ77-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',1);
INSERT INTO "usage" VALUES('EPSG','8766','grid_transformation','EPSG','1845','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1846','ATS77 to NAD83(CSRS) (2)','Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1689.','EPSG','9615','NTv2','EPSG','4122','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','PE7783V2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'PEI DOT-Can PEI',1);
INSERT INTO "usage" VALUES('EPSG','8767','grid_transformation','EPSG','1846','EPSG','1533','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1847','NAD27 to NAD83(CSRS) (2)','Can be taken as an approximate transformation NAD27 to WGS 84 - see code 1703.','EPSG','9615','NTv2','EPSG','4267','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','SK27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','8768','grid_transformation','EPSG','1847','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1848','NAD83 to NAD83(CSRS) (2)','Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1697.','EPSG','9615','NTv2','EPSG','4269','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','SK83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','8769','grid_transformation','EPSG','1848','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1849','NAD83 to NAD83(CSRS) (3)','This gridded difference file AB_CSRS.DAC will need to be renamed to AB_CSRS.gsb to run in some software suites. Formats identical, but AB file is provincial fit only. Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1702.','EPSG','9615','NTv2','EPSG','4269','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','AB_CSRS.DAC',NULL,NULL,NULL,NULL,NULL,NULL,'AB Env-Can AB',1);
INSERT INTO "usage" VALUES('EPSG','8770','grid_transformation','EPSG','1849','EPSG','2376','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1850','ATS77 to NAD83(CSRS) (3)','Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1851.','EPSG','9615','NTv2','EPSG','4122','EPSG','4617',1.5,'EPSG','8656','Latitude and longitude difference file','NS778301.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NSGC-Can NS',1);
INSERT INTO "usage" VALUES('EPSG','8771','grid_transformation','EPSG','1850','EPSG','2313','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','1851','ATS77 to WGS 84 (3)','Parameter file is from ATS77 to NAD83(CSRS)v3 (3) (code 9235) assuming that NAD83(CSRS)v3 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4122','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','NS778301.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Can NS',0);
INSERT INTO "usage" VALUES('EPSG','8772','grid_transformation','EPSG','1851','EPSG','2313','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','3858','WGS 84 to EGM2008 height (1)','Replaces WGS 84 to EGM96 height (1) (CT code 15781). Grid spacing is 2.5 arc-minutes. For smaller spacing (in principle more exact) see CT code 3859. For reversible alternative see CT code 9704. An executable using spherical harmonics is also available.','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4979','EPSG','3855',1.0,'EPSG','8666','Geoid (height correction) model file','Und_min2.5x2.5_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,NULL,NULL,'NGA-World',0);
INSERT INTO "usage" VALUES('EPSG','8948','grid_transformation','EPSG','3858','EPSG','1262','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','3859','WGS 84 to EGM2008 height (2)','Replaces WGS 84 to EGM96 height (1) (CT code 15781). Grid spacing is 1 arc-minute. For a larger grid spacing (in principle less exact) see CT code 3858. For reversible alternative see CT code 9618. An executable using spherical harmonics is available.','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4979','EPSG','3855',0.5,'EPSG','8666','Geoid (height correction) model file','Und_min1x1_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,NULL,NULL,'NGA-World',0);
INSERT INTO "usage" VALUES('EPSG','14322','grid_transformation','EPSG','3859','EPSG','1262','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4459','NZGD2000 to NZVD2009 height (1)','Defines NZVD2009 vertical datum (datum code 1039, CRS code 4440).','EPSG','1030','Geographic3D to GravityRelatedHeight (NZgeoid)','EPSG','4959','EPSG','4440',0.1,'EPSG','8666','Geoid (height correction) model file','nzgeoid09.sid',NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ 2009',0);
INSERT INTO "usage" VALUES('EPSG','9090','grid_transformation','EPSG','4459','EPSG','1175','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4561','RRAF 1991 to Martinique 1987 height (1)','May be used for transformations from WGS 84 to IGN 1987. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4557','EPSG','5756',998.0,'EPSG','8666','Geoid (height correction) model file','ggm00.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Mtq',0);
INSERT INTO "usage" VALUES('EPSG','9098','grid_transformation','EPSG','4561','EPSG','3276','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4562','RRAF 1991 to Guadeloupe 1988 height (1)','May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4557','EPSG','5757',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp GT',0);
INSERT INTO "usage" VALUES('EPSG','9099','grid_transformation','EPSG','4562','EPSG','2892','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4563','RRAF 1991 to IGN 1988 MG height (1)','May be used for transformations from WGS 84 to IGN 1988 MG. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4557','EPSG','5617',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_mg.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp MG',0);
INSERT INTO "usage" VALUES('EPSG','9100','grid_transformation','EPSG','4563','EPSG','2894','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4564','RRAF 1991 to IGN 1988 SM height (1)','May be used for transformations from WGS 84 to IGN 1988 SM. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4557','EPSG','5620',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_sm.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StM',0);
INSERT INTO "usage" VALUES('EPSG','9101','grid_transformation','EPSG','4564','EPSG','2890','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4565','RRAF 1991 to IGN 1988 LS height (1)','May be used for transformations from WGS 84 to IGN 1988 LS. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4557','EPSG','5616',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_ls.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp LSt',0);
INSERT INTO "usage" VALUES('EPSG','9102','grid_transformation','EPSG','4565','EPSG','2895','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4566','RRAF 1991 to IGN 1992 LD height (1)','Accuracy 0.5m. Replaced by RRAF 1991 to IGN 2008 LD height (1) (CT code 9132).','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4557','EPSG','5618',0.5,'EPSG','8666','Geoid (height correction) model file','ggg00_ld.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp Des',0);
INSERT INTO "usage" VALUES('EPSG','9103','grid_transformation','EPSG','4566','EPSG','2893','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','4567','RRAF 1991 to IGN 1988 SB height (1)','May be used for transformations from WGS 84 to IGN 1988 SB. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4557','EPSG','5619',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_sb.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StB',0);
INSERT INTO "usage" VALUES('EPSG','9104','grid_transformation','EPSG','4567','EPSG','2891','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5334','ETRS89 to Belfast height (1)','May be used for transformations from WGS 84 to Belfast. Replaced by ETRS89 to Belfast height (2) (code 7958).','EPSG','1045','Geographic3D to GravityRelatedHeight (OSGM02-Ire)','EPSG','4937','EPSG','5732',0.03,'EPSG','8666','Geoid (height correction) model file','OSGM02_NI.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK NI',0);
INSERT INTO "usage" VALUES('EPSG','9347','grid_transformation','EPSG','5334','EPSG','2530','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5335','ETRS89 to Malin Head height (1)','May be used for transformations from WGS 84 to Malin Head. Replaced by ETRS89 to Malin Head height (2) (code 7959).','EPSG','1045','Geographic3D to GravityRelatedHeight (OSGM02-Ire)','EPSG','4937','EPSG','5731',0.04,'EPSG','8666','Geoid (height correction) model file','OSGM02_RoI.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-Ire',0);
INSERT INTO "usage" VALUES('EPSG','9348','grid_transformation','EPSG','5335','EPSG','1305','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5338','OSGB36 to ETRS89 (1)','Approximate alternative to official OSTN02 method (tfm code 7952). Accuracy at 2000 test points compared to OSTN02 (tfm code 1039): latitude 0.5mm av, 17mm max; longitude 0.8mm av, 23mm max. May be taken as approximate CT to WGS 84 - see code 5339.','EPSG','9615','NTv2','EPSG','4277','EPSG','4258',0.03,'EPSG','8656','Latitude and longitude difference file','OSTN02_NTv2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OSGB-UK Gbr02 NT',0);
INSERT INTO "usage" VALUES('EPSG','9349','grid_transformation','EPSG','5338','EPSG','4616','EPSG','1273');
INSERT INTO "grid_transformation" VALUES('EPSG','5339','OSGB36 to WGS 84 (7)','Parameter values taken from OSGB36 to ETRS89 (1) (tfm code 5338) assuming that ETRS89 is coincident with WGS 84 within the accuracy of the tfm.','EPSG','9615','NTv2','EPSG','4277','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','OSTN02_NTv2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-UK Gbr02 NT',0);
INSERT INTO "usage" VALUES('EPSG','9350','grid_transformation','EPSG','5339','EPSG','4616','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','5409','NGVD29 height to NAVD88 height (1)','Interpolation within the gridded data file may be made using any of the horizontal CRSs NAD27, NAD83 or NAD83(HARN).','EPSG','9658','Vertical Offset by Grid Interpolation (VERTCON)','EPSG','5702','EPSG','5703',0.02,'EPSG','8732','Vertical offset file','vertconw.94',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus W',1);
INSERT INTO "usage" VALUES('EPSG','9377','grid_transformation','EPSG','5409','EPSG','2950','EPSG','1255');
INSERT INTO "grid_transformation" VALUES('EPSG','5410','NGVD29 height to NAVD88 height (2)','Interpolation within the gridded data file may be made using any of the horizontal CRSs NAD27, NAD83 or NAD83(HARN).','EPSG','9658','Vertical Offset by Grid Interpolation (VERTCON)','EPSG','5702','EPSG','5703',0.02,'EPSG','8732','Vertical offset file','vertconc.94',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus C',1);
INSERT INTO "usage" VALUES('EPSG','9378','grid_transformation','EPSG','5410','EPSG','2949','EPSG','1255');
INSERT INTO "grid_transformation" VALUES('EPSG','5411','NGVD29 height to NAVD88 height (3)','Interpolation within the gridded data file may be made using any of the horizontal CRSs NAD27, NAD83 or NAD83(HARN).','EPSG','9658','Vertical Offset by Grid Interpolation (VERTCON)','EPSG','5702','EPSG','5703',0.02,'EPSG','8732','Vertical offset file','vertcone.94',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus E',1);
INSERT INTO "usage" VALUES('EPSG','9379','grid_transformation','EPSG','5411','EPSG','2948','EPSG','1255');
INSERT INTO "grid_transformation" VALUES('EPSG','5502','RGAF09 to Martinique 1987 height (1)','Replaces tfm from RRAF 1991, code 4561. May be used for transformations from WGS 84 to IGN 1987. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','5488','EPSG','5756',998.0,'EPSG','8666','Geoid (height correction) model file','gg10_mart.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Mtq',0);
INSERT INTO "usage" VALUES('EPSG','9445','grid_transformation','EPSG','5502','EPSG','3276','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5503','RGAF09 to Guadeloupe 1988 height (1)','Replaces tfm from RRAF 1991, code 4562. May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','5488','EPSG','5757',0.2,'EPSG','8666','Geoid (height correction) model file','gg10_gtbt.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp GT',0);
INSERT INTO "usage" VALUES('EPSG','9446','grid_transformation','EPSG','5503','EPSG','2892','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5504','RGAF09 to IGN 1988 MG height (1)','Replaces tfm from RRAF 1991, code 4563. May be used for transformations from WGS 84 to IGN 1988 MG. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','5488','EPSG','5617',0.2,'EPSG','8666','Geoid (height correction) model file','gg10_mg.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp MG',0);
INSERT INTO "usage" VALUES('EPSG','9447','grid_transformation','EPSG','5504','EPSG','2894','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5505','RGAF09 to IGN 1988 SM height (1)','Replaces tfm from RRAF 1991, code 4564. May be used for transformations from WGS 84 to IGN 1988 SM. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','5488','EPSG','5620',0.2,'EPSG','8666','Geoid (height correction) model file','gg10_sm.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StM',0);
INSERT INTO "usage" VALUES('EPSG','9448','grid_transformation','EPSG','5505','EPSG','2890','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5506','RGAF09 to IGN 1988 LS height (1)','Replaces tfm from RRAF 1991, code 4565. May be used for transformations from WGS 84 to IGN 1988 LS. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','5488','EPSG','5616',0.2,'EPSG','8666','Geoid (height correction) model file','gg10_ls.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp LSt',0);
INSERT INTO "usage" VALUES('EPSG','9449','grid_transformation','EPSG','5506','EPSG','2895','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5507','RGAF09 to IGN 1992 LD height (1)','Replaces tfm from RRAF 1991, code 4566. Replaced by RGAF09 to IGN 2008 LD height (1), CT code 9131. May be used for transformations from WGS 84 to IGN 1992 LD. Accuracy at each 0.025° grid node is given within the geoid model file, approx. average 0.5m.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','5488','EPSG','5618',0.5,'EPSG','8666','Geoid (height correction) model file','gg10_ld.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp Des',0);
INSERT INTO "usage" VALUES('EPSG','9450','grid_transformation','EPSG','5507','EPSG','2893','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5508','RGAF09 to IGN 1988 SB height (1)','Replaces tfm from RRAF 1991, code 4567. May be used for transformations from WGS 84 to IGN 1988 SB. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','5488','EPSG','5619',0.2,'EPSG','8666','Geoid (height correction) model file','gg10_sb.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StB',0);
INSERT INTO "usage" VALUES('EPSG','9451','grid_transformation','EPSG','5508','EPSG','2891','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5525','Corrego Alegre 1961 to SIRGAS 2000 (1)','May be used as transformation between Corrego Alegre 1961 and WGS 84 - see tfm code 5540.','EPSG','9615','NTv2','EPSG','5524','EPSG','4674',2.0,'EPSG','8656','Latitude and longitude difference file','CA61_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IBGE-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9459','grid_transformation','EPSG','5525','EPSG','3874','EPSG','1042');
INSERT INTO "grid_transformation" VALUES('EPSG','5526','Corrego Alegre 1970-72 to SIRGAS 2000 (1)','May be used as transformation between Corrego Alegre 1970-72 and WGS 84 - see tfm code 5541.','EPSG','9615','NTv2','EPSG','4225','EPSG','4674',2.0,'EPSG','8656','Latitude and longitude difference file','CA7072_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IBGE-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9460','grid_transformation','EPSG','5526','EPSG','1293','EPSG','1042');
INSERT INTO "grid_transformation" VALUES('EPSG','5528','SAD69 to SIRGAS 2000 (2)','For IBGE, in onshore east and south Brazil only, replaces SAD69 to SIRGAS 2000 (1) (tfm code 15485) for pre-1996 data based on the classical geodetic network. May be used as transformation between SAD69 and WGS 84 - see tfm code 5542.','EPSG','9615','NTv2','EPSG','4618','EPSG','4674',1.0,'EPSG','8656','Latitude and longitude difference file','SAD69_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IBGE-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9461','grid_transformation','EPSG','5528','EPSG','3887','EPSG','1068');
INSERT INTO "grid_transformation" VALUES('EPSG','5529','SAD69(96) to SIRGAS 2000 (1)','May be used as transformation between SAD69(96) and WGS 84 - see tfm code 5543.','EPSG','9615','NTv2','EPSG','5527','EPSG','4674',0.5,'EPSG','8656','Latitude and longitude difference file','SAD96_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IBGE-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9462','grid_transformation','EPSG','5529','EPSG','3887','EPSG','1067');
INSERT INTO "grid_transformation" VALUES('EPSG','5540','Corrego Alegre 1961 to WGS 84 (1)','Parameters from Corrego Alegre 1961 to SIRGAS 2000 (1) (tfm code 5525) assuming that SIRGAS 2000 and WGS 84 are equal within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','5524','EPSG','4326',2.0,'EPSG','8656','Latitude and longitude difference file','CA61_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9463','grid_transformation','EPSG','5540','EPSG','3874','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','5541','Corrego Alegre 1970-72 to WGS 84 (2)','Parameters from Corrego Alegre 1970-72 to SIRGAS 2000 (1) (tfm code 5526) assuming that SIRGAS 2000 and WGS 84 are equal within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4225','EPSG','4326',2.0,'EPSG','8656','Latitude and longitude difference file','CA7072_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9464','grid_transformation','EPSG','5541','EPSG','1293','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','5542','SAD69 to WGS 84 (15)','Parameters values taken from SAD69 to SIRGAS 2000 (2) (tfm code 5528) assuming that SIRGAS 2000 and WGS 84 are equal within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4618','EPSG','4326',2.0,'EPSG','8656','Latitude and longitude difference file','SAD69_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9465','grid_transformation','EPSG','5542','EPSG','3887','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','5543','SAD69(96) to WGS 84 (1)','Parameters values taken from SAD69(96) to SIRGAS 2000 (1) (tfm code 5529) assuming that SIRGAS 2000 and WGS 84 are equal within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','5527','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','SAD96_003.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Bra',0);
INSERT INTO "usage" VALUES('EPSG','9466','grid_transformation','EPSG','5543','EPSG','3887','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','5594','FEH2010 to FCSVR10 height (1)','','EPSG','1030','Geographic3D to GravityRelatedHeight (NZgeoid)','EPSG','5592','EPSG','5597',0.1,'EPSG','8666','Geoid (height correction) model file','fehmarn_geoid10.gri',NULL,NULL,NULL,NULL,NULL,NULL,'FEM-Dnk-Deu Feh',1);
INSERT INTO "usage" VALUES('EPSG','9477','grid_transformation','EPSG','5594','EPSG','3890','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5626','FEH2010 to FCSVR10 height (1)','For reversible alternative to this transformation see FEH2010 to FEH2010 + FCSVR10 height (1) (code 9619).','EPSG','1047','Geographic3D to GravityRelatedHeight (Gravsoft)','EPSG','5592','EPSG','5597',0.1,'EPSG','8666','Geoid (height correction) model file','fehmarn_geoid10.gri',NULL,NULL,NULL,NULL,NULL,NULL,'FEM-Dnk-Deu Feh',0);
INSERT INTO "usage" VALUES('EPSG','14327','grid_transformation','EPSG','5626','EPSG','3890','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5656','GDA94 to AHD height (49)','File name previously called AUSGeoid09_GDA94_V1.01_DOV_windows.gsb. Replaces AUSGeoid98 model. Uses AUSGeoid09 model which uses bi-cubic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time.','EPSG','1048','Geographic3D to GravityRelatedHeight (AUSGeoid v2)','EPSG','4939','EPSG','5711',0.15,'EPSG','8666','Geoid (height correction) model file','AUSGeoid09_V1.01.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus09 mainland',0);
INSERT INTO "usage" VALUES('EPSG','9488','grid_transformation','EPSG','5656','EPSG','1281','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5657','GDA94 to AHD (Tasmania) height (2)','Replaces AusGeoid98 model. Uses AusGeoid09 model which uses bi-cubic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time. May be used for transformations from WGS 84 to AHD (Tasmania).','EPSG','1048','Geographic3D to GravityRelatedHeight (AUSGeoid v2)','EPSG','4939','EPSG','5712',0.03,'EPSG','8666','Geoid (height correction) model file','AUSGeoid09_GDA94_V1.01_DOV_windows.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus09 Tas',0);
INSERT INTO "usage" VALUES('EPSG','9489','grid_transformation','EPSG','5657','EPSG','2947','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','5661','ED50 to ETRS89 (14)','When included in concatenation from and to projected CRSs, gives same results as ED50 / UTM zone 31N to ETRS89 / UTM zone 31N (1) - see CRS code 5166.','EPSG','9615','NTv2','EPSG','4230','EPSG','4258',0.05,'EPSG','8656','Latitude and longitude difference file','100800401.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICC-Esp Cat',0);
INSERT INTO "usage" VALUES('EPSG','9492','grid_transformation','EPSG','5661','EPSG','3732','EPSG','1079');
INSERT INTO "grid_transformation" VALUES('EPSG','5891','MGI to ETRS89 (5)','Not to be used for cadastral purposes as it does not comply with the rules for control network tie-in as per Paragraph 3 of the Land Survey Regulations (Vermessungsverordnung) 2010.','EPSG','9615','NTv2','EPSG','4312','EPSG','4258',0.15,'EPSG','8656','Latitude and longitude difference file','AT_GIS_GRID.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'BEV-Aut NTv2',0);
INSERT INTO "usage" VALUES('EPSG','14324','grid_transformation','EPSG','5891','EPSG','1037','EPSG','1078');
INSERT INTO "grid_transformation" VALUES('EPSG','6138','CIGD11 to GCVD54 height (ft) (1)','Care: source CRS heights are in metres, transformation file parameter values and target CRS heights are in feet. For reversible alternative to this transformation see CIGD11 to CIGD11 + GCVD54 height (ft)) (code 9603).','EPSG','1050','Geographic3D to GravityRelatedHeight (CI)','EPSG','6134','EPSG','6130',0.03,'EPSG','8666','Geoid (height correction) model file','GCGM0811.TXT',NULL,NULL,NULL,NULL,NULL,NULL,'LSD-Cym',0);
INSERT INTO "usage" VALUES('EPSG','14268','grid_transformation','EPSG','6138','EPSG','3185','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','6139','CIGD11 to LCVD61 height (ft) (1)','Care: source CRS heights are in metres, transformation file parameter values and target CRS heights are in feet. For reversible alternative to this transformation see CIGD11 to CIGD11 + LCVD61 height (ft) (1) (code 9604).','EPSG','1050','Geographic3D to GravityRelatedHeight (CI)','EPSG','6134','EPSG','6131',0.03,'EPSG','8666','Geoid (height correction) model file','LCGM0811.TXT',NULL,NULL,NULL,NULL,NULL,NULL,'LSD-Cym',0);
INSERT INTO "usage" VALUES('EPSG','14269','grid_transformation','EPSG','6139','EPSG','4121','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','6140','CIGD11 to CBVD61 height (ft) (1)','Care: source CRS heights are in metres, transformation file parameter values and target CRS heights are in feet. For reversible alternative to this transformation see CIGD11 to CIGD11 + CBVD61 height (ft) (1) (code 9602).','EPSG','1050','Geographic3D to GravityRelatedHeight (CI)','EPSG','6134','EPSG','6132',0.03,'EPSG','8666','Geoid (height correction) model file','CBGM0811.TXT',NULL,NULL,NULL,NULL,NULL,NULL,'LSD-Cym',0);
INSERT INTO "usage" VALUES('EPSG','14263','grid_transformation','EPSG','6140','EPSG','3207','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','6188','Lisbon to ETRS89 (4)','Derived from 1129 common stations in the national geodetic network. Residuals at 130 further test points average 0.09m, maximum 0.30m.','EPSG','9615','NTv2','EPSG','4207','EPSG','4258',0.1,'EPSG','8656','Latitude and longitude difference file','DLx_ETRS89_geo.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGP-Prt 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','9634','grid_transformation','EPSG','6188','EPSG','1294','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','6189','Datum 73 to ETRS89 (6)','Derived from 1129 common stations in the national geodetic network. Residuals at 130 further test points average 0.06m, maximum 0.16m.','EPSG','9615','NTv2','EPSG','4274','EPSG','4258',0.1,'EPSG','8656','Latitude and longitude difference file','D73_ETRS89_geo.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGP-Prt 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','9635','grid_transformation','EPSG','6189','EPSG','1294','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','6209','NAD27 to NAD83(CSRS) (4)','Introduced in 2011. Precision of 20 cm in area covered by the input data set and 40 cm anywhere else, with the exception of the northwest area of the province (near the border with Quebec) where the precision deteriorates to 80 cm.','EPSG','9615','NTv2','EPSG','4267','EPSG','4617',0.8,'EPSG','8656','Latitude and longitude difference file','NB2783v2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SNB-Can NB',1);
INSERT INTO "usage" VALUES('EPSG','9649','grid_transformation','EPSG','6209','EPSG','1447','EPSG','1231');
INSERT INTO "grid_transformation" VALUES('EPSG','6326','NAD83(2011) to NAVD88 height (1)','Uses Geoid12B hybrid model. Renamed from Geoid12A but with no data changes. See information source for further information.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6319','EPSG','5703',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bu0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus',0);
INSERT INTO "usage" VALUES('EPSG','9717','grid_transformation','EPSG','6326','EPSG','1323','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','6327','NAD83(2011) to NAVD88 height (2)','Uses Geoid12B hybrid model. Renamed from Geoid12A but with no data changes. See information source for further information. For reversible alternative to this transformation see NAD83(2011) to NAD83(2011) + NAVD88 height (2) (code 9596).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6319','EPSG','5703',0.02,'EPSG','8666','Geoid (height correction) model file','g2012ba0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US AK',0);
INSERT INTO "usage" VALUES('EPSG','14351','grid_transformation','EPSG','6327','EPSG','1330','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','6648','NAD83(CSRS) to CGVD2013 height (1)','Uses CGG2013 model which uses bi-quadratic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time.','EPSG','1060','Geographic3D to GravityRelatedHeight (CGG2013)','EPSG','4955','EPSG','6647',0.03,'EPSG','8666','Geoid (height correction) model file','CGG2013n83.byn',NULL,NULL,NULL,NULL,NULL,NULL,'NRC Can CGG2013',1);
INSERT INTO "usage" VALUES('EPSG','9733','grid_transformation','EPSG','6648','EPSG','1061','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','6712','Tokyo to JGD2000 (2)','NTv2 grid derived by ESRI from that supplied by GSI in application tky2jgd. After Tohoku earthquake of 2011 accuracy in northern Honshu reduced to 1-5m and in this area replaced by Tokyo to JGD2011 (tfm code 6714).','EPSG','9615','NTv2','EPSG','4301','EPSG','4612',0.2,'EPSG','8656','Latitude and longitude difference file','tky2jgd.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Jpn',0);
INSERT INTO "usage" VALUES('EPSG','9740','grid_transformation','EPSG','6712','EPSG','3957','EPSG','1142');
INSERT INTO "grid_transformation" VALUES('EPSG','6713','JGD2000 to JGD2011 (1)','NTv2 grid derived by ESRI from that supplied by GSI in application patchjgd.','EPSG','9615','NTv2','EPSG','4612','EPSG','6668',0.2,'EPSG','8656','Latitude and longitude difference file','touhokutaiheiyouoki2011.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Jpn N Honshu',0);
INSERT INTO "usage" VALUES('EPSG','9741','grid_transformation','EPSG','6713','EPSG','4170','EPSG','1050');
INSERT INTO "grid_transformation" VALUES('EPSG','6740','Tokyo to JGD2011 (2)','Parameter values from Tokyo to JGD2000 (2) (tfm code 6712) as  in area of applicability JGD2011 = JGD2000. NTv2 grid derived by ESRI from that supplied by GSI in application tky2jgd. See Tokyo to JGD2011 (1) (tfm code 6714) for northern Honshu area.','EPSG','9615','NTv2','EPSG','4301','EPSG','6668',0.2,'EPSG','8656','Latitude and longitude difference file','tky2jgd.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Jpn ex N Honshu',0);
INSERT INTO "usage" VALUES('EPSG','9756','grid_transformation','EPSG','6740','EPSG','4194','EPSG','1142');
INSERT INTO "grid_transformation" VALUES('EPSG','6946','TM75 to ETRS89 (3)','Approximate alternative to official OS polynomial method (tfm code 1041). May be taken as approximate transformation TM75 to WGS 84 - see code 6947.','EPSG','9615','NTv2','EPSG','4300','EPSG','4258',0.41,'EPSG','8656','Latitude and longitude difference file','tm75_etrs89.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OSI-Ire NT',0);
INSERT INTO "usage" VALUES('EPSG','9849','grid_transformation','EPSG','6946','EPSG','1305','EPSG','1137');
INSERT INTO "grid_transformation" VALUES('EPSG','6947','TM75 to WGS 84 (4)','Parameter values taken from TM75 to ETRS89 (3) (tfm code 6946) assuming that ETRS89 is coincident with WGS 84 within the accuracy of the tfm. Within accuracy of the tfm equivalent to TM75 to WGS 84 (1) (tfm code 1042).','EPSG','9615','NTv2','EPSG','4300','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','tm75_etrs89.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Ire NT',0);
INSERT INTO "usage" VALUES('EPSG','9850','grid_transformation','EPSG','6947','EPSG','1305','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','6948','RD/83 to ETRS89 (2)','Recommended by Saxony State Spatial Data and Land Survey Corporation for transformations based on official geospatial data of Saxony. Accuracy 3mm within Saxony; within the rest of RD/83 definition area results at least coincide with EPSG CT code15868.','EPSG','9615','NTv2','EPSG','4745','EPSG','4258',0.03,'EPSG','8656','Latitude and longitude difference file','NTv2_SN.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GeoSN-Deu SN',0);
INSERT INTO "usage" VALUES('EPSG','9851','grid_transformation','EPSG','6948','EPSG','2545','EPSG','1028');
INSERT INTO "grid_transformation" VALUES('EPSG','7000','Amersfoort to ETRS89 (7)','Consistent to within 1mm with official RNAPTRANS(TM)2008 at ground level onshore and at MSL offshore. The horizontal deviation using this NTv2 grid is approximately 1mm per 50m height difference from ground level or MSL.','EPSG','9615','NTv2','EPSG','4289','EPSG','4258',0.001,'EPSG','8656','Latitude and longitude difference file','rdtrans2008.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'RDNAP-Nld 2008',0);
INSERT INTO "usage" VALUES('EPSG','9881','grid_transformation','EPSG','7000','EPSG','1275','EPSG','1086');
INSERT INTO "grid_transformation" VALUES('EPSG','7001','ETRS89 to NAP height (1)','Alternative to vertical component of official 3D RDNAPTRANS(TM)2008. The naptrans2008 correction grid incorporates the NLGEO2004 geoid model plus a fixed offset.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4937','EPSG','5709',0.01,'EPSG','8666','Geoid (height correction) model file','naptrans2008.gtx',NULL,NULL,NULL,NULL,NULL,NULL,'RDNAP-Nld 2008',1);
INSERT INTO "usage" VALUES('EPSG','9882','grid_transformation','EPSG','7001','EPSG','1275','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7646','NAD83(2011) to PRVD02 height (1)','Uses Geoid12B hybrid model. See information source for further information.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6319','EPSG','6641',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bp0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Pri 12B',0);
INSERT INTO "usage" VALUES('EPSG','10190','grid_transformation','EPSG','7646','EPSG','3294','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','7647','NAD83(2011) to VIVD09 height (1)','Uses Geoid12B hybrid model. See information source for further information.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6319','EPSG','6642',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bp0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Vir 12B',0);
INSERT INTO "usage" VALUES('EPSG','10191','grid_transformation','EPSG','7647','EPSG','3330','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','7648','NAD83(MA11) to GUVD04 height (1)','Uses Geoid12B hybrid model. See information source for further information. For reversible alternative to this transformation see NAD83(MA11) to NAD83(MA11) + GUVD04 height (1) (code 9624).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6324','EPSG','6644',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bg0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Gum 12B',0);
INSERT INTO "usage" VALUES('EPSG','14366','grid_transformation','EPSG','7648','EPSG','3255','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','7649','NAD83(MA11) to NMVD03 height (1)','Uses Geoid12B hybrid model. See information source for further information. For reversible alternative to this transformation see NAD83(MA11) to NAD83(MA11) + NMVD03 height (1) (code 9625).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6324','EPSG','6640',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bg0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Mnp 12B',0);
INSERT INTO "usage" VALUES('EPSG','14369','grid_transformation','EPSG','7649','EPSG','4171','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','7650','NAD83(PA11) to ASVD02 height (1)','Uses Geoid12B hybrid model. See information source for further information. For reversible alternative to this transformation see NAD83(PA11) to NAD83(PA11) + ASVD02 height (1) (code 9626).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6321','EPSG','6643',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bs0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Asm 12B',0);
INSERT INTO "usage" VALUES('EPSG','14372','grid_transformation','EPSG','7650','EPSG','2288','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','7655','PNG94 to PNG08 height (1)','','EPSG','1059','Geographic3D to GravityRelatedHeight (PNG)','EPSG','5545','EPSG','7447',0.2,'EPSG','8666','Geoid (height correction) model file','PNG08.DAT',NULL,NULL,NULL,NULL,NULL,NULL,'QC-Png',0);
INSERT INTO "usage" VALUES('EPSG','10197','grid_transformation','EPSG','7655','EPSG','4384','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7673','CH1903 to CHTRS95 (1)','Equivalent to concatenation of transformations 15486 (CH1903 to CH1903+) and 1509 (CH1903+ to CHTRS95) to within 2cm. Also used as transformation between CH1903 and ETRS89 (see code 7674).','EPSG','9615','NTv2','EPSG','4149','EPSG','4151',0.25,'EPSG','8656','Latitude and longitude difference file','CHENyx06_ETRS.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'BfL-Che NTv2',0);
INSERT INTO "usage" VALUES('EPSG','10205','grid_transformation','EPSG','7673','EPSG','1286','EPSG','1083');
INSERT INTO "grid_transformation" VALUES('EPSG','7674','CH1903 to ETRS89 (2)','Equivalent to concatenation of CTs 15486 (CH1903 to CH1903+) and 1647 (CH1903+ to CHTRS95) to within 2cm. Also used as CT between CH1903 and CHTRS95 (see code 7673). Replaces CT code 1646. May be used as approximate CT CH1903 to WGS 84 - see code 7788.','EPSG','9615','NTv2','EPSG','4149','EPSG','4258',0.25,'EPSG','8656','Latitude and longitude difference file','CHENyx06_ETRS.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'BfL-Che NTv2',0);
INSERT INTO "usage" VALUES('EPSG','10206','grid_transformation','EPSG','7674','EPSG','1286','EPSG','1083');
INSERT INTO "grid_transformation" VALUES('EPSG','7709','OSGB36 to ETRS89 (2)','Approximate alternative to official OSTN15 method (tfm code 7953). May be taken as approximate transformation OSGB36 to WGS 84 - see code 7710. Replaces OSGB36 to ETRS89 (1) (tfm code 5338).','EPSG','9615','NTv2','EPSG','4277','EPSG','4258',0.03,'EPSG','8656','Latitude and longitude difference file','OSTN15_NTv2_OSGBtoETRS.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OSGB-UK Gbr15 NT',0);
INSERT INTO "usage" VALUES('EPSG','10222','grid_transformation','EPSG','7709','EPSG','4390','EPSG','1273');
INSERT INTO "grid_transformation" VALUES('EPSG','7710','OSGB36 to WGS 84 (9)','Parameter values taken from OSGB36 to ETRS89 (2) (tfm code 7709) assuming that ETRS89 is coincident with WGS 84 within the accuracy of the tfm. Replaces OSGB36 to WGS 84 (7) (tfm code 5339).','EPSG','9615','NTv2','EPSG','4277','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','OSTN15_NTv2_OSGBtoETRS.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-UK Gbr15 NT',0);
INSERT INTO "usage" VALUES('EPSG','10223','grid_transformation','EPSG','7710','EPSG','4390','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','7711','ETRS89 to ODN height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Newlyn height (1) (tfm code 10021). For reversible alternative to this transformation see ETRS89 to ETRS89 + ODN height (2) (code 9587).','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5701',0.008,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Gbr 2015',0);
INSERT INTO "usage" VALUES('EPSG','14515','grid_transformation','EPSG','7711','EPSG','2792','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7712','ETRS89 to ODN Orkney height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Newlyn (Orkney Isles) height (1) (tfm code 10029). For reversible alternative to this transformation see ETRS89 to ETRS89 + ODN Orkney height (2) (code 9586).','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5740',0.017,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Ork 2015',0);
INSERT INTO "usage" VALUES('EPSG','14517','grid_transformation','EPSG','7712','EPSG','2793','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7713','ETRS89 to ODN (Offshore) height (1)','Replaces ETRS89 to Fair Isle/Flannan Isles/Foula/North Rona/St Kilda/Sule Skerry height (1) (tfm codes 10024-26, 10030-31 and 10034). For reversible alternative to this transformation see ETRS89 to ETRS89 + ODN (Offshore) height (1) (code 9588).','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','7707',0.02,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Off 2015',0);
INSERT INTO "usage" VALUES('EPSG','14512','grid_transformation','EPSG','7713','EPSG','4391','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7714','ETRS89 to Lerwick height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Lerwick height (1) (tfm code 10027). For reversible alternative to this transformation see ETRS89 to ETRS89 + Lerwick height (2) (code 9589).','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5742',0.018,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS -UK Shet 2015',0);
INSERT INTO "usage" VALUES('EPSG','14510','grid_transformation','EPSG','7714','EPSG','2795','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7715','ETRS89 to Stornoway height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Stornaway height (1) (tfm code 10033). For reversible alternative to this transformation see ETRS89 to ETRS89 + Stornoway height (2) (code 9584).','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5746',0.011,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Heb 2015',0);
INSERT INTO "usage" VALUES('EPSG','14521','grid_transformation','EPSG','7715','EPSG','2799','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7716','ETRS89 to St. Marys height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to St Marys height (1) (tfm code 10032). For reversible alternative to this transformation see ETRS89 to ETRS89 + St. Marys height (2) (code 9585).','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5749',0.01,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Scilly 2015',0);
INSERT INTO "usage" VALUES('EPSG','14519','grid_transformation','EPSG','7716','EPSG','2802','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7717','ETRS89 to Douglas height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Douglas height (1) (tfm code 10023). For reversible alternative to this transformation see ETRS89 to ETRS89 + Douglas height (2) (code 9590).','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5750',0.03,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Man 2015',0);
INSERT INTO "usage" VALUES('EPSG','14508','grid_transformation','EPSG','7717','EPSG','2803','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7718','ETRS89 to Belfast height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Belfast height (1) (tfm code 5334).','EPSG','1045','Geographic3D to GravityRelatedHeight (OSGM02-Ire)','EPSG','4937','EPSG','5732',0.014,'EPSG','8666','Geoid (height correction) model file','OSGM15_Belfast.gri',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK NI 2015',1);
INSERT INTO "usage" VALUES('EPSG','10231','grid_transformation','EPSG','7718','EPSG','2530','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7719','ETRS89 to Malin Head height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Malin Head height (1) (tfm code 5335).','EPSG','1045','Geographic3D to GravityRelatedHeight (OSGM02-Ire)','EPSG','4937','EPSG','5731',0.023,'EPSG','8666','Geoid (height correction) model file','OSGM15_Malin.gri',NULL,NULL,NULL,NULL,NULL,NULL,'OS-Ire 2015',1);
INSERT INTO "usage" VALUES('EPSG','10232','grid_transformation','EPSG','7719','EPSG','1305','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7788','CH1903 to WGS 84 (3)','Parameter values from CH1903 to ETRS89 (2) (code 7674) assuming that ETRS89 is equivalent to WGS 84 within the accuracy of the transformation. Equivalent to concatenation of transformations 15486 and 1676.','EPSG','9615','NTv2','EPSG','4149','EPSG','4326',1.5,'EPSG','8656','Latitude and longitude difference file','CHENyx06_ETRS.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Che NTv2',0);
INSERT INTO "usage" VALUES('EPSG','10268','grid_transformation','EPSG','7788','EPSG','1286','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','7840','NZGD2000 to NZVD2016 height (1)','Defines NZVD2016 vertical datum (datum code 1169, CRS code 7839).','EPSG','1030','Geographic3D to GravityRelatedHeight (NZgeoid)','EPSG','4959','EPSG','7839',0.1,'EPSG','8666','Geoid (height correction) model file','New_Zealand_Quasigeoid_2016.csv',NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ 2016',0);
INSERT INTO "usage" VALUES('EPSG','10293','grid_transformation','EPSG','7840','EPSG','1175','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7860','NZVD2016 height to Auckland 1946 height (1)','Derived at 260 control points. Mean offset 0.292m, standard deviation 0.029m, maximum difference from mean 0.075m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5759',0.02,'EPSG','8732','Vertical offset file','auckland-1946-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ AUCK',0);
INSERT INTO "usage" VALUES('EPSG','10294','grid_transformation','EPSG','7860','EPSG','3764','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7861','NZVD2016 height to Bluff 1955 height (1)','Derived at 71 control points. Mean offset 0.273m, standard deviation 0.034m, maximum difference from mean 0.079m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5760',0.02,'EPSG','8732','Vertical offset file','bluff-1955-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ BLUF',0);
INSERT INTO "usage" VALUES('EPSG','10295','grid_transformation','EPSG','7861','EPSG','3801','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7862','NZVD2016 height to Dunedin 1958 height (1)','Derived at 197 control points. Mean offset 0.326m, standard deviation 0.043m, maximum difference from mean 0.152m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5761',0.02,'EPSG','8732','Vertical offset file','dunedin-1958-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ DUNE',0);
INSERT INTO "usage" VALUES('EPSG','10296','grid_transformation','EPSG','7862','EPSG','3803','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7863','NZVD2016 height to Dunedin-Bluff 1960 height (1)','Derived at 205 control points. Mean offset 0.254m, standard deviation 0.039m, maximum difference from mean 0.11m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','4458',0.02,'EPSG','8732','Vertical offset file','dunedin-bluff-1960-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ DUBL',0);
INSERT INTO "usage" VALUES('EPSG','10297','grid_transformation','EPSG','7863','EPSG','3806','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7864','NZVD2016 height to Gisborne 1926 height (1)','Derived at 274 control points. Mean offset 0.343m, standard deviation 0.025m, maximum difference from mean 0.09m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5762',0.02,'EPSG','8732','Vertical offset file','gisborne-1926-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ GISB',0);
INSERT INTO "usage" VALUES('EPSG','10298','grid_transformation','EPSG','7864','EPSG','3771','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7865','NZVD2016 height to Lyttelton 1937 height (1)','Derived at 923 control points. Mean offset 0.34m, standard deviation 0.041m, maximum difference from mean 0.149m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5763',0.01,'EPSG','8732','Vertical offset file','lyttelton-1937-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ LYTT',0);
INSERT INTO "usage" VALUES('EPSG','10299','grid_transformation','EPSG','7865','EPSG','3804','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7866','NZVD2016 height to Moturiki 1953 height (1)','Derived at 519 control points. Mean offset 0.309m, standard deviation 0.071m, maximum difference from mean 0.231m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5764',0.02,'EPSG','8732','Vertical offset file','moturiki-1953-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ MOTU',0);
INSERT INTO "usage" VALUES('EPSG','10300','grid_transformation','EPSG','7866','EPSG','3768','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7867','NZVD2016 height to Napier 1962 height (1)','Derived at 207 control points. Mean offset 0.203m, standard deviation 0.034m, maximum difference from mean 0.096m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5765',0.02,'EPSG','8732','Vertical offset file','napier-1962-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ NAPI',0);
INSERT INTO "usage" VALUES('EPSG','10301','grid_transformation','EPSG','7867','EPSG','3772','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7868','NZVD2016 height to Nelson 1955 height (1)','Derived at 256 control points. Mean offset 0.329m, standard deviation 0.039m, maximum difference from mean 0.114m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5766',0.02,'EPSG','8732','Vertical offset file','nelson-1955-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ NELS',0);
INSERT INTO "usage" VALUES('EPSG','10302','grid_transformation','EPSG','7868','EPSG','3802','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7869','NZVD2016 height to One Tree Point 1964 height (1)','Derived at 137 control points. Mean offset 0.077m, standard deviation 0.042m, maximum difference from mean 0.107m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5767',0.01,'EPSG','8732','Vertical offset file','onetreepoint-1964-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ ONTP',0);
INSERT INTO "usage" VALUES('EPSG','10303','grid_transformation','EPSG','7869','EPSG','3762','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7870','NZVD2016 height to Stewart Island 1977 height (1)','Derived at 4 control points. Mean offset 0.299m, standard deviation 0.025m, maximum difference from mean 0.039m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5772',0.18,'EPSG','8732','Vertical offset file','stewartisland-1977-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ STWT',0);
INSERT INTO "usage" VALUES('EPSG','10304','grid_transformation','EPSG','7870','EPSG','3338','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7871','NZVD2016 height to Taranaki 1970 height (1)','Derived at 125 control points. Mean offset 0.286m, standard deviation 0.026m, maximum difference from mean 0.07m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5769',0.02,'EPSG','8732','Vertical offset file','taranaki-1970-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ TARA',0);
INSERT INTO "usage" VALUES('EPSG','10305','grid_transformation','EPSG','7871','EPSG','3769','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7872','NZVD2016 height to Wellington 1953 height (1)','Derived at 137 control points. Mean offset 0.408m, standard deviation 0.054m, maximum difference from mean 0.112m.','EPSG','1071','Vertical Offset by Grid Interpolation (NZLVD)','EPSG','7839','EPSG','5770',0.02,'EPSG','8732','Vertical offset file','wellington-1953-to-nzvd2016-conversion.csv',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ WGTN',0);
INSERT INTO "usage" VALUES('EPSG','10306','grid_transformation','EPSG','7872','EPSG','3773','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7891','SHGD2015 to SHVD2015 height (1)','This transformation defines SHVD2015 heights. For reversible alternative to this transformation see SHGD2015 to SHGD2015 + SHVD2015 height (1) (code 9617).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','7885','EPSG','7890',0.0,'EPSG','8666','Geoid (height correction) model file','Und_min2.5x2.5_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,NULL,NULL,'ENRD-Shn Hel',0);
INSERT INTO "usage" VALUES('EPSG','14318','grid_transformation','EPSG','7891','EPSG','3183','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7957','Canada velocity grid v6','NOTE: Before being deprecated this record had a second parameter (code 1048, value 8251) which has been removed due to being non-compliant with the data model.','EPSG','1070','Point motion by grid (Canada NTv2_Vel)','EPSG','8251','EPSG','8251',0.01,'EPSG','1050','Point motion velocity grid file','cvg60.cvb',NULL,NULL,NULL,NULL,NULL,NULL,'NRC-Can cvg6.0',1);
INSERT INTO "usage" VALUES('EPSG','10342','grid_transformation','EPSG','7957','EPSG','1061','EPSG','1058');
INSERT INTO "grid_transformation" VALUES('EPSG','7958','ETRS89 to Belfast height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Belfast height (1) (tfm code 5334). For reversible alternative to this transformation see ETRS89 to ETRS89 + Belfast height (2) (code 9592).','EPSG','1072','Geographic3D to GravityRelatedHeight (OSGM15-Ire)','EPSG','4937','EPSG','5732',0.014,'EPSG','8666','Geoid (height correction) model file','OSGM15_Belfast.gri',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK NI 2015',0);
INSERT INTO "usage" VALUES('EPSG','14505','grid_transformation','EPSG','7958','EPSG','2530','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7959','ETRS89 to Malin Head height (2)','OSGM15 supersedes OSGM02 geoid model. Replaces ETRS89 to Malin Head height (1) (tfm code 5335). For reversible alternative to this transformation see ETRS89 to ETRS89 + Malin Head height (2) (code 9591).','EPSG','1072','Geographic3D to GravityRelatedHeight (OSGM15-Ire)','EPSG','4937','EPSG','5731',0.023,'EPSG','8666','Geoid (height correction) model file','OSGM15_Malin.gri',NULL,NULL,NULL,NULL,NULL,NULL,'OS-Ire 2015',0);
INSERT INTO "usage" VALUES('EPSG','10344','grid_transformation','EPSG','7959','EPSG','1305','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','7969','NGVD29 height (m) to NAVD88 height (1)','In this area the NGVD29 vertical reference surface is approximately 0.6 to 1.6m below the NAVD88 datum surface. Interpolation within the gridded data file may be made using either NAD27 or any of the realizations of NAD83 applicable to US conus.','EPSG','9658','Vertical Offset by Grid Interpolation (VERTCON)','EPSG','7968','EPSG','5703',0.02,'EPSG','8732','Vertical offset file','vertconw.94',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus W',0);
INSERT INTO "usage" VALUES('EPSG','10352','grid_transformation','EPSG','7969','EPSG','2950','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7970','NGVD29 height (m) to NAVD88 height (2)','In the SE of this area the NGVD29 surface is 0 to 0.1m above the NAVD88 surface; in the W it is 0.6 to 0.9m below the NAVD88 surface. Interpolation in the data file may be made using either NAD27 or any of the NAD83 realizations applicable to US conus.','EPSG','9658','Vertical Offset by Grid Interpolation (VERTCON)','EPSG','7968','EPSG','5703',0.02,'EPSG','8732','Vertical offset file','vertconc.94',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus C',0);
INSERT INTO "usage" VALUES('EPSG','10353','grid_transformation','EPSG','7970','EPSG','2949','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','7971','NGVD29 height (m) to NAVD88 height (3)','In this area the NGVD29 vertical reference surface is approximately 0 to 0.5m above the NAVD88 surface. Interpolation within the gridded data file may be made using either NAD27 or any of the realisations of NAD83 applicable to US conus.','EPSG','9658','Vertical Offset by Grid Interpolation (VERTCON)','EPSG','7968','EPSG','5703',0.02,'EPSG','8732','Vertical offset file','vertcone.94',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus E',0);
INSERT INTO "usage" VALUES('EPSG','10354','grid_transformation','EPSG','7971','EPSG','2948','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','8037','WGS 84 to MSL height (1)','Parameter values are from WGS 84 to EGM2008 height (2) (CT code 3859) assuming that the EGM2008 surface equals MSL surface within the accuracy of the transformation. For reversible alternative see WGS 84 to WGS 84 + MSL height (1) (code 9706).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4979','EPSG','5714',0.5,'EPSG','8666','Geoid (height correction) model file','Und_min1x1_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,NULL,NULL,'IOGP-World',0);
INSERT INTO "usage" VALUES('EPSG','10394','grid_transformation','EPSG','8037','EPSG','1262','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8268','GR96 to GVR2000 height (1)','Defines GVR2000. File is also available in NOAA VDatum format (ggeoid2000.gtx) and GeoTIFF format (ggeoid2000.tif). For reversible alternative to this transformation see GR96 to GR96 + GVR2000 height (1) (code 9598).','EPSG','1047','Geographic3D to GravityRelatedHeight (Gravsoft)','EPSG','4909','EPSG','8266',0.1,'EPSG','8666','Geoid (height correction) model file','gr2000g.gri',NULL,NULL,NULL,NULL,NULL,NULL,'SDFE-Grl',0);
INSERT INTO "usage" VALUES('EPSG','14329','grid_transformation','EPSG','8268','EPSG','4461','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8269','GR96 to GVR2016 height (1)','Defines GVR2016. File is also available in NOAA VDatum format (ggeoid2016.gtx) and GeoTIFF format (ggeoid2016.tif). For reversible alternative to this transformation see GR96 to GR96 + GVR2016 height (1) (code 9599).','EPSG','1047','Geographic3D to GravityRelatedHeight (Gravsoft)','EPSG','4909','EPSG','8267',0.1,'EPSG','8666','Geoid (height correction) model file','ggeoid16.gri',NULL,NULL,NULL,NULL,NULL,NULL,'SDFE-Grl',0);
INSERT INTO "usage" VALUES('EPSG','14333','grid_transformation','EPSG','8269','EPSG','4454','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8271','RGF93 to NGF IGN69 height (2)','Replaces RGF93 to NGF IGN69 height (1) (code 10000). Accuracy at each 0.1 deg x 0.1 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4965','EPSG','5720',0.02,'EPSG','8666','Geoid (height correction) model file','RAF09.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra 09',1);
INSERT INTO "usage" VALUES('EPSG','10464','grid_transformation','EPSG','8271','EPSG','1326','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8272','RGF93 to IGN78 Corsica height (1)','Replaces RGF93 to NGF IGN69 height (1) (code 10002). Accuracy at each 0.1 deg x 0.1 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4965','EPSG','5721',0.05,'EPSG','8666','Geoid (height correction) model file','RAC09.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra Cor 09',1);
INSERT INTO "usage" VALUES('EPSG','10465','grid_transformation','EPSG','8272','EPSG','1327','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8361','ETRS89 to ETRS89 + Baltic 1957 height (1)','Uses ETRS89 (realization ETRF2000) and quasigeoid model DVRM05. 1 sigma = 34 mm (test performed on 563 independent points). Recommended as part of transformation between Baltic 1957 height and EVRF2007 height (see concatenated operation code 8363).','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','4937','EPSG','8360',0.03,'EPSG','8666','Geoid (height correction) model file','Slovakia_ETRS89h_to_Baltic1957.gtx',NULL,NULL,NULL,NULL,'EPSG','4258','UGKK-Svk',0);
INSERT INTO "usage" VALUES('EPSG','10508','grid_transformation','EPSG','8361','EPSG','1211','EPSG','1186');
INSERT INTO "grid_transformation" VALUES('EPSG','8362','ETRS89 to ETRS89 + EVRF2007 height (1)','Uses ETRS89 (realization ETRF2000) and quasigeoid model DMQSK2014E. 1 sigma = 29 mm (test performed on 93 independent points). Recommended as part of transformation between Baltic 1957 height and EVRF2007 height (see concatenated operation code 8363).','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','4937','EPSG','7423',0.03,'EPSG','8666','Geoid (height correction) model file','Slovakia_ETRS89h_to_EVRF2007.gtx',NULL,NULL,NULL,NULL,'EPSG','4258','UGKK-Svk',0);
INSERT INTO "usage" VALUES('EPSG','10509','grid_transformation','EPSG','8362','EPSG','1211','EPSG','1186');
INSERT INTO "grid_transformation" VALUES('EPSG','8364','S-JTSK [JTSK03] to S-JTSK (1)','Derived at 684 identical points. Also available as an NTv2 file.','EPSG','9613','NADCON','EPSG','8351','EPSG','4156',0.05,'EPSG','8657','Latitude difference file','Slovakia_JTSK03_to_JTSK.LAS','EPSG','8658','Longitude difference file','Slovakia_JTSK03_to_JTSK.LOS',NULL,NULL,'UGKK-Svk',0);
INSERT INTO "usage" VALUES('EPSG','10511','grid_transformation','EPSG','8364','EPSG','1211','EPSG','1079');
INSERT INTO "grid_transformation" VALUES('EPSG','8369','BD72 to ETRS89 (3)','File name is lower case and despite its name is between geographic CRSs. (Similarly-named file in upper case BD72LB72_ETRS89LB08 operates in projected CRS domain).','EPSG','9615','NTv2','EPSG','4313','EPSG','4258',0.01,'EPSG','8656','Latitude and longitude difference file','bd72lb72_etrs89lb08.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Bel 0.01m',0);
INSERT INTO "usage" VALUES('EPSG','10516','grid_transformation','EPSG','8369','EPSG','1347','EPSG','1150');
INSERT INTO "grid_transformation" VALUES('EPSG','8371','RGF93 v2 to NGF-IGN69 height (2)','Replaces ggf97a geoid model [RGF93 v1 to NGF IGN69 height (1) (code 10000)]. Replaced by RAF18 model. Accuracy at each 0.0333333333333° in longitude and 0.025° in latitude grid node is given within the model file.','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','9776','EPSG','5720',0.02,'EPSG','8666','Geoid (height correction) model file','RAF09.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra 09',0);
INSERT INTO "usage" VALUES('EPSG','10517','grid_transformation','EPSG','8371','EPSG','1326','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8372','RGF93 v2 to NGF-IGN78 height (2)','Replaces ggf97a model [RGF93 v1 to NGF-IGN78 height (1) (code 10002)]. Accuracy at each grid node is given in the geoid model file. For reversible alternative see RGF93 v2 to RGF93 v2 + NGF-IGN78 height (2) (code 9639).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','9776','EPSG','5721',0.05,'EPSG','8666','Geoid (height correction) model file','RAC09.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra Cor 09',0);
INSERT INTO "usage" VALUES('EPSG','10518','grid_transformation','EPSG','8372','EPSG','1327','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8444','GDA94 to GDA2020 (4)','See GDA94 to GDA2020 (1) (code 8048) for transformation using Helmert method which gives identical results.','EPSG','9615','NTv2','EPSG','4283','EPSG','7844',0.05,'EPSG','8656','Latitude and longitude difference file','GDA94_GDA2020_conformal_christmas_island.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICSM-Cxr Conf',0);
INSERT INTO "usage" VALUES('EPSG','10564','grid_transformation','EPSG','8444','EPSG','4169','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8445','GDA94 to GDA2020 (5)','See GDA94 to GDA2020 (1) (code 8048) for transformation using Helmert method which gives identical results.','EPSG','9615','NTv2','EPSG','4283','EPSG','7844',0.05,'EPSG','8656','Latitude and longitude difference file','GDA94_GDA2020_conformal_cocos_island.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICSM-Cck Conf',0);
INSERT INTO "usage" VALUES('EPSG','10565','grid_transformation','EPSG','8445','EPSG','1069','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8446','GDA94 to GDA2020 (3)','Gives identical results to Helmert transformation GDA94 to GDA2020 (1) (code 8048). See GDA94 to GDA2020 (2) (code 8447) for alternative with local distortion modelling included. GDA2020 Technical Manual and fact sheet T1 give guidance on which to use.','EPSG','9615','NTv2','EPSG','4283','EPSG','7844',0.05,'EPSG','8656','Latitude and longitude difference file','GDA94_GDA2020_conformal.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICSM-Aus NTv2 Conf',0);
INSERT INTO "usage" VALUES('EPSG','10566','grid_transformation','EPSG','8446','EPSG','2575','EPSG','1108');
INSERT INTO "grid_transformation" VALUES('EPSG','8447','GDA94 to GDA2020 (2)','See GDA94 to GDA2020 (1) or (3) (codes 8048 and 8446) for alternative conformal-only transformation without local distortion modelling. GDA2020 Technical Manual and fact sheet T1 give guidance on which to use.','EPSG','9615','NTv2','EPSG','4283','EPSG','7844',0.05,'EPSG','8656','Latitude and longitude difference file','GDA94_GDA2020_conformal_and_distortion.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICSM-Aus Conf and Dist',0);
INSERT INTO "usage" VALUES('EPSG','10567','grid_transformation','EPSG','8447','EPSG','2575','EPSG','1234');
INSERT INTO "grid_transformation" VALUES('EPSG','8451','GDA2020 to AHD height (1)','File name previously called AUSGeoid2020_windows_binary.gsb. Uncertainties given in accompanying file AUSGeoid2020_20180201_error.gsb. For reversible alternative to this transformation see GDA2020 to GDA2020 + AHD height (1) (code 9466).','EPSG','1048','Geographic3D to GravityRelatedHeight (AUSGeoid v2)','EPSG','7843','EPSG','5711',0.15,'EPSG','8666','Geoid (height correction) model file','AUSGeoid2020_20180201.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus 2020',0);
INSERT INTO "usage" VALUES('EPSG','10570','grid_transformation','EPSG','8451','EPSG','4493','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','8546','St. George Island to NAD83 (2)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4138','EPSG','4269',0.15,'EPSG','8657','Latitude difference file','nadcon5.sg1952.nad83_1986.stgeorge.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.sg1952.nad83_1986.stgeorge.lon.trn.20160901.b',NULL,NULL,'NGS-Usa AK StG Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10638','grid_transformation','EPSG','8546','EPSG','1331','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8547','St. Lawrence Island to NAD83 (2)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4136','EPSG','4269',0.5,'EPSG','8657','Latitude difference file','nadcon5.sl1952.nad83_1986.stlawrence.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.sl1952.nad83_1986.stlawrence.lon.trn.20160901.b',NULL,NULL,'NGS-Usa AK StL Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10639','grid_transformation','EPSG','8547','EPSG','1332','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8548','St. Paul Island to NAD83 (2)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4137','EPSG','4269',0.5,'EPSG','8657','Latitude difference file','nadcon5.sp1952.nad83_1986.stpaul.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.sp1952.nad83_1986.stpaul.lon.trn.20160901.b',NULL,NULL,'NGS-Usa AK StP Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10640','grid_transformation','EPSG','8548','EPSG','1333','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8549','NAD27 to NAD83 (8)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; source and target CRSs have longitudes positive east in range -180° to +180°. Accuracy at 67% confidence level is 0.5m onshore, 5m nearshore and undetermined farther offshore.','EPSG','1074','NADCON5 (2D)','EPSG','4267','EPSG','4269',0.5,'EPSG','8657','Latitude difference file','nadcon5.nad27.nad83_1986.alaska.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.nad27.nad83_1986.alaska.lon.trn.20160901.b',NULL,NULL,'NGS-Usa AK Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10641','grid_transformation','EPSG','8549','EPSG','1330','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8550','NAD83 to NAD83(HARN) (48)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4269','EPSG','4152',0.15,'EPSG','8657','Latitude difference file','nadcon5.nad83_1986.nad83_1992.alaska.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.nad83_1986.nad83_1992.alaska.lon.trn.20160901.b',NULL,NULL,'NGS-Usa AK Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10642','grid_transformation','EPSG','8550','EPSG','2373','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8555','NAD27 to NAD83 (7)','NADCON5 method expects longitudes 0-360°; source and target CRS longitudes in range ±180°. Accuracy at 67% confidence level is 0.15m onshore, 1m nearshore and undetermined farther offshore. For application in Gulf of Mexico refer to IOGP report 373-26.','EPSG','1074','NADCON5 (2D)','EPSG','4267','EPSG','4269',0.15,'EPSG','8657','Latitude difference file','nadcon5.nad27.nad83_1986.conus.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.nad27.nad83_1986.conus.lon.trn.20160901.b',NULL,NULL,'NGS-Usa Conus Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10647','grid_transformation','EPSG','8555','EPSG','4516','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8556','NAD83 to NAD83(HARN) (47)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nadcon5.nad83_1986.nad83_harn.conus.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.nad83_1986.nad83_harn.conus.lon.trn.20160901.b',NULL,NULL,'NGS-Usa Conus Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10648','grid_transformation','EPSG','8556','EPSG','4516','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8561','Old Hawaiian to NAD83 (2)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4135','EPSG','4269',0.2,'EPSG','8657','Latitude difference file','nadcon5.ohd.nad83_1986.hawaii.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.ohd.nad83_1986.hawaii.lon.trn.20160901.b',NULL,NULL,'NGS-Usa HI Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10653','grid_transformation','EPSG','8561','EPSG','1334','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8660','NAD83 to NAD83(HARN) (49)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nadcon5.nad83_1986.nad83_1993.hawaii.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.nad83_1986.nad83_1993.hawaii.lon.trn.20160901.b',NULL,NULL,'NGS-Usa HI Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10752','grid_transformation','EPSG','8660','EPSG','1334','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8662','American Samoa 1962 to NAD83(HARN) (3)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4169','EPSG','4152',5.0,'EPSG','8657','Latitude difference file','nadcon5.as62.nad83_1993.as.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.as62.nad83_1993.as.lon.trn.20160901.b',NULL,NULL,'NGS-Asm Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10754','grid_transformation','EPSG','8662','EPSG','3109','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8665','Guam 1963 to NAD83(HARN) (2)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4675','EPSG','4152',5.0,'EPSG','8657','Latitude difference file','nadcon5.gu63.nad83_1993.guamcnmi.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.gu63.nad83_1993.guamcnmi.lon.trn.20160901.b',NULL,NULL,'NGS-Gum NADCON5',0);
INSERT INTO "usage" VALUES('EPSG','10757','grid_transformation','EPSG','8665','EPSG','4525','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8668','Puerto Rico to NAD83 (2)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4139','EPSG','4269',0.15,'EPSG','8657','Latitude difference file','nadcon5.pr40.nad83_1986.prvi.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.pr40.nad83_1986.prvi.lon.trn.20160901.b',NULL,NULL,'NGS-Pri Vir Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10760','grid_transformation','EPSG','8668','EPSG','3634','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8669','NAD83 to NAD83(HARN) (50)','Uses NADCON5 method which expects longitudes positive east in range 0-360°; EPSG source and target CRSs have longitudes positive east in range -180° to +180°.','EPSG','1074','NADCON5 (2D)','EPSG','4269','EPSG','4152',0.15,'EPSG','8657','Latitude difference file','nadcon5.nad83_1986.nad83_1993.prvi.lat.trn.20160901.b','EPSG','8658','Longitude difference file','nadcon5.nad83_1986.nad83_1993.prvi.lon.trn.20160901.b',NULL,NULL,'NGS-Pri Vir Nadcon5',0);
INSERT INTO "usage" VALUES('EPSG','10761','grid_transformation','EPSG','8669','EPSG','3634','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','8676','Canada velocity grid v6','','EPSG','1070','Point motion by grid (Canada NTv2_Vel)','EPSG','8251','EPSG','8251',0.01,'EPSG','1050','Point motion velocity grid file','cvg60.cvb',NULL,NULL,NULL,NULL,NULL,NULL,'NRC-Can cvg6.0',0);
INSERT INTO "usage" VALUES('EPSG','10767','grid_transformation','EPSG','8676','EPSG','4618','EPSG','1058');
INSERT INTO "grid_transformation" VALUES('EPSG','8885','RGF93 v2 to NGF-IGN69 height (3)','Replaces RAF09 geoid model [RGF93 v2 to NGF-IGN69 height (2) (code 8371)]. Replaced by RAF18b model. May also be found with filename RAF18.tac. For reversible alternative see RGF93 v2 to RGF93 v2 + NGF-IGN69 height (3) (code 9638).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','9776','EPSG','5720',0.01,'EPSG','8666','Geoid (height correction) model file','RAF18.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra 18',0);
INSERT INTO "usage" VALUES('EPSG','14492','grid_transformation','EPSG','8885','EPSG','1326','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9105','ATS77 to NAD83 (1)','','EPSG','9615','NTv2','EPSG','4122','EPSG','4269',0.5,'EPSG','8656','Latitude and longitude difference file','GS7783.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'NSGC-Can NS',0);
INSERT INTO "usage" VALUES('EPSG','10919','grid_transformation','EPSG','9105','EPSG','2313','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9106','ATS77 to NAD83(CSRS)v6 (4)','Derived through NAD83(CSRS)v6. Replaces ATS77 to NAD83(CSRS)v3 (3) (transformation code 9235).','EPSG','9615','NTv2','EPSG','4122','EPSG','8252',0.06,'EPSG','8656','Latitude and longitude difference file','NS778302.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NSGC-Can NS v2',0);
INSERT INTO "usage" VALUES('EPSG','10920','grid_transformation','EPSG','9106','EPSG','2313','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9107','NAD27 to NAD83(CSRS)v3 (5)','Derived through NAD83(CSRS)v3 but within accuracy of transformation may be assumed to apply to any version. For Toronto use NAD27 to NAD83(CSRS) (6) (CT code 9108).','EPSG','9615','NTv2','EPSG','4267','EPSG','8240',1.5,'EPSG','8656','Latitude and longitude difference file','ON27CSv1.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'LIO-Can Ont ex Tor',0);
INSERT INTO "usage" VALUES('EPSG','10921','grid_transformation','EPSG','9107','EPSG','4537','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9108','NAD27 to NAD83(CSRS)v3 (6)','Derived through NAD83(CSRS)v3 but within accuracy of transformation may be assumed to apply to any version. For Ontario excluding Toronto use NAD27 to NAD83(CSRS) (5) (CT code 9107).','EPSG','9615','NTv2','EPSG','4267','EPSG','8240',1.0,'EPSG','8656','Latitude and longitude difference file','TOR27CSv1.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'LIO-Can Ont Tor',0);
INSERT INTO "usage" VALUES('EPSG','10922','grid_transformation','EPSG','9108','EPSG','4536','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9109','NAD27(76) to NAD83(CSRS)v3 (1)','Derived through NAD83(CSRS)v3 but within accuracy of transformation may be assumed to apply to any version.','EPSG','9615','NTv2','EPSG','4608','EPSG','8240',1.0,'EPSG','8656','Latitude and longitude difference file','ON76CSv1.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'LIO-Can Ont',0);
INSERT INTO "usage" VALUES('EPSG','10923','grid_transformation','EPSG','9109','EPSG','1367','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9110','NAD83 to NAD83(CSRS)v3 (5)','Derived through NAD83(CSRS)v3.','EPSG','9615','NTv2','EPSG','4269','EPSG','8240',0.1,'EPSG','8656','Latitude and longitude difference file','ON83CSv1.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'LIO-Can Ont',0);
INSERT INTO "usage" VALUES('EPSG','10924','grid_transformation','EPSG','9110','EPSG','1367','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9111','NAD27 to NAD83 (9)','','EPSG','9615','NTv2','EPSG','4267','EPSG','4269',1.5,'EPSG','8656','Latitude and longitude difference file','SK27-83.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ISC-Can SK',0);
INSERT INTO "usage" VALUES('EPSG','10925','grid_transformation','EPSG','9111','EPSG','2375','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9112','NAD27 to NAD83(CSRS)v2 (7)','Derived through NAD83(CSRS)v2. Replaced by NAD27 to NAD83(CSRS) (8) (CT code 9113) for CRD, NAD27 to NAD83(CSRS) (9) (CT code 9114) forn north Vancouver Island and NAD27 to NAD83(CSRS) (10) (CT code 9115) for mainland.','EPSG','9615','NTv2','EPSG','4267','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','BC_27_98.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC CSRSv2',0);
INSERT INTO "usage" VALUES('EPSG','10926','grid_transformation','EPSG','9112','EPSG','2832','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9113','NAD27 to NAD83(CSRS)v3 (8)','Derived through NAD83(CSRS)v3.','EPSG','9615','NTv2','EPSG','4267','EPSG','8240',1.5,'EPSG','8656','Latitude and longitude difference file','CRD27_00.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC CRD',0);
INSERT INTO "usage" VALUES('EPSG','10927','grid_transformation','EPSG','9113','EPSG','4533','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9114','NAD27 to NAD83(CSRS)v3 (9)','Derived through NAD83(CSRS)v3.','EPSG','9615','NTv2','EPSG','4267','EPSG','8240',1.5,'EPSG','8656','Latitude and longitude difference file','NVI27_05.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC NVI',0);
INSERT INTO "usage" VALUES('EPSG','10928','grid_transformation','EPSG','9114','EPSG','4534','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9115','NAD27 to NAD83(CSRS)v4 (10)','Derived through NAD83(CSRS)v4.','EPSG','9615','NTv2','EPSG','4267','EPSG','8246',1.5,'EPSG','8656','Latitude and longitude difference file','BC_27_05.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC mainland',0);
INSERT INTO "usage" VALUES('EPSG','10929','grid_transformation','EPSG','9115','EPSG','4535','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9116','NAD83 to NAD83(CSRS)v2 (6)','Derived through NAD83(CSRS)v2. Replaced by NAD83 to NAD83(CSRS) (7) (CT code 9117) for CRD, NAD83 to NAD83(CSRS) (8) (CT code 9118) for north Vancouver Island and NAD83 to NAD83(CSRS) (9) (CT code 9119) for mainland.','EPSG','9615','NTv2','EPSG','4269','EPSG','8237',0.1,'EPSG','8656','Latitude and longitude difference file','BC_93_98.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC CSRSv2',0);
INSERT INTO "usage" VALUES('EPSG','10930','grid_transformation','EPSG','9116','EPSG','2832','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9117','NAD83 to NAD83(CSRS)v3 (7)','Derived through NAD83(CSRS)v3.','EPSG','9615','NTv2','EPSG','4269','EPSG','8240',0.1,'EPSG','8656','Latitude and longitude difference file','CRD93_00.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC CRD',0);
INSERT INTO "usage" VALUES('EPSG','10931','grid_transformation','EPSG','9117','EPSG','4533','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9118','NAD83 to NAD83(CSRS)v3 (8)','Derived through NAD83(CSRS)v3.','EPSG','9615','NTv2','EPSG','4269','EPSG','8240',0.1,'EPSG','8656','Latitude and longitude difference file','NVI93_05.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC NVI',0);
INSERT INTO "usage" VALUES('EPSG','10932','grid_transformation','EPSG','9118','EPSG','4534','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9119','NAD83 to NAD83(CSRS)v4 (9)','Derived through NAD83(CSRS)v4.','EPSG','9615','NTv2','EPSG','4269','EPSG','8246',0.1,'EPSG','8656','Latitude and longitude difference file','BC_93_05.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC mainland',0);
INSERT INTO "usage" VALUES('EPSG','10933','grid_transformation','EPSG','9119','EPSG','4535','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9120','NAD83(CSRS)v2 to NAD83(CSRS)v3 (1)','','EPSG','9615','NTv2','EPSG','8237','EPSG','8240',0.1,'EPSG','8656','Latitude and longitude difference file','CRD98_00.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC CRD',0);
INSERT INTO "usage" VALUES('EPSG','10934','grid_transformation','EPSG','9120','EPSG','4533','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9121','NAD83(CSRS)v2 to NAD83(CSRS)v3 (2)','','EPSG','9615','NTv2','EPSG','8237','EPSG','8240',0.1,'EPSG','8656','Latitude and longitude difference file','NVI98_05.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC NVI',0);
INSERT INTO "usage" VALUES('EPSG','10935','grid_transformation','EPSG','9121','EPSG','4534','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9122','NAD83(CSRS)v2 to NAD83(CSRS)v4 (1)','','EPSG','9615','NTv2','EPSG','8237','EPSG','8240',0.1,'EPSG','8656','Latitude and longitude difference file','BC_98_05.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'GeoBC-Can BC mainland',0);
INSERT INTO "usage" VALUES('EPSG','10936','grid_transformation','EPSG','9122','EPSG','4535','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9123','NAD83(CSRS) to CGVD28 height (1)','Derived through NAD83(CSRS)v3.','EPSG','1060','Geographic3D to GravityRelatedHeight (CGG2013)','EPSG','4955','EPSG','5713',0.05,'EPSG','8666','Geoid (height correction) model file','HT2_0.byn',NULL,NULL,NULL,NULL,NULL,NULL,'NRC Can CGG2000',0);
INSERT INTO "usage" VALUES('EPSG','10937','grid_transformation','EPSG','9123','EPSG','1289','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9124','ITRF2008 to CGVD2013(CGG2013) height (1)','Uses CGG2013 model derived through NAD83(CSRS)v6 which uses bi-quadratic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time. Replaced by CT code 9125.','EPSG','1060','Geographic3D to GravityRelatedHeight (CGG2013)','EPSG','7911','EPSG','6647',0.03,'EPSG','8666','Geoid (height correction) model file','CGG2013i83.byn',NULL,NULL,NULL,NULL,NULL,NULL,'NRC Can CGG2013',0);
INSERT INTO "usage" VALUES('EPSG','10938','grid_transformation','EPSG','9124','EPSG','1061','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9125','ITRF2008 to CGVD2013(CGG2013a) height (2)','Uses CGG2013a model derived through NAD83(CSRS)v6 which uses bi-quadratic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time. Replaces CT code 9124.','EPSG','1060','Geographic3D to GravityRelatedHeight (CGG2013)','EPSG','7911','EPSG','9245',0.03,'EPSG','8666','Geoid (height correction) model file','CGG2013i08a.byn',NULL,NULL,NULL,NULL,NULL,NULL,'NRC Can CGG2013a',0);
INSERT INTO "usage" VALUES('EPSG','10939','grid_transformation','EPSG','9125','EPSG','1061','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9131','RGAF09 to IGN 2008 LD height (1)','Replaces RGAF09 to IGN 1992 LD height (1) (CT code 5507). Accuracy 0.1-0.2m within onshore area. For reversible alternative to this transformation see RGAF09 to RGAF09 + IGN 2008 LD height (1) (code 9636).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9130',0.2,'EPSG','8666','Geoid (height correction) model file','RALD2016.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Glp Des',0);
INSERT INTO "usage" VALUES('EPSG','10944','grid_transformation','EPSG','9131','EPSG','2893','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9132','RRAF 1991 to IGN 2008 LD height (1)','Replaces RRAF 1991 to IGN 1992 LD height (1) (CT code 4566). Accuracy 0.1-0.2m within onshore area. For reversible alternative to this transformation see RRAF 1991 to RRAF 1991 + IGN 2008 LD height (1) (code 9642).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','4557','EPSG','9130',0.2,'EPSG','8666','Geoid (height correction) model file','RALDW842016.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp Des',0);
INSERT INTO "usage" VALUES('EPSG','14503','grid_transformation','EPSG','9132','EPSG','2893','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9133','RGAF09 to Guadeloupe 1988 height (2)','Replaces RGAF09 to Guadeloupe 1988 height (1) (CT code 5503). Accuracy 0.01-0.05m within onshore areas. For reversible alternative to this transformation see RGAF09 to RGAF09 + Guadeloupe 1988 height (2) (code 9631).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','5757',0.05,'EPSG','8666','Geoid (height correction) model file','RAGTBT2016.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp GT 2016',0);
INSERT INTO "usage" VALUES('EPSG','14426','grid_transformation','EPSG','9133','EPSG','2892','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9134','RGAF09 to IGN 1988 LS height (2)','Replaces RGAF09 to IGN 1988 LS height (2) (CT code 5506). Accuracy 0.05-0.1m within onshore area. For reversible alternative to this transformation see RGAF09 to RGAF09 to IGN 1988 LS height (2) (code 9632).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','5616',0.1,'EPSG','8666','Geoid (height correction) model file','RALS2016.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp LSt 2016',0);
INSERT INTO "usage" VALUES('EPSG','14477','grid_transformation','EPSG','9134','EPSG','2895','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9135','RGAF09 to IGN 1988 MG height (2)','Replaces RGAF09 to IGN 1988 MG height (1), CT code 5504. Accuracy 0.0.5-0.1m within onshore area. For reversible alternative to this transformation see RGAF09 to RGAF09 + IGN 1988 MG height (2) (code 9633).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','5617',0.1,'EPSG','8666','Geoid (height correction) model file','RAMG2016.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp MG 2016',0);
INSERT INTO "usage" VALUES('EPSG','14476','grid_transformation','EPSG','9135','EPSG','2894','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9136','RGAF09 to Martinique 1987 height (2)','Replaces RGAF09 to Martinique 1987 height (1) (CT code 5502). Accuracy 0.01m to 0.05m within onshore area. For reversible alternative to this transformation see RGAF09 to RGAF09 + Martinique 1987 height (2) (code 9637).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','5756',0.05,'EPSG','8666','Geoid (height correction) model file','RAMART2016.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Mtq 2016',0);
INSERT INTO "usage" VALUES('EPSG','14488','grid_transformation','EPSG','9136','EPSG','3276','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9137','RGSPM06 to Danger 1950 height (1)','Accuracy 0.10m to 0.20m within onshore area.','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','4466','EPSG','5792',0.2,'EPSG','8666','Geoid (height correction) model file','GGSPM06v1.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-SPM',0);
INSERT INTO "usage" VALUES('EPSG','10950','grid_transformation','EPSG','9137','EPSG','3299','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9160','NAD83(HARN) to NAVD88 height (1)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u01.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus NW',0);
INSERT INTO "usage" VALUES('EPSG','10955','grid_transformation','EPSG','9160','EPSG','2977','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9161','NAD83(HARN) to NAVD88 height (2)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u02.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CNW',0);
INSERT INTO "usage" VALUES('EPSG','10956','grid_transformation','EPSG','9161','EPSG','2978','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9162','NAD83(HARN) to NAVD88 height (3)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u03.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CNE',0);
INSERT INTO "usage" VALUES('EPSG','10957','grid_transformation','EPSG','9162','EPSG','2979','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9163','NAD83(HARN) to NAVD88 height (4)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u04.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus NE',0);
INSERT INTO "usage" VALUES('EPSG','10958','grid_transformation','EPSG','9163','EPSG','2980','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9164','NAD83(HARN) to NAVD88 height (5)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u05.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus SW',0);
INSERT INTO "usage" VALUES('EPSG','10959','grid_transformation','EPSG','9164','EPSG','2973','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9165','NAD83(HARN) to NAVD88 height (6)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u06.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CSW',0);
INSERT INTO "usage" VALUES('EPSG','10960','grid_transformation','EPSG','9165','EPSG','2974','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9166','NAD83(HARN) to NAVD88 height (7)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u07.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CSE',0);
INSERT INTO "usage" VALUES('EPSG','10961','grid_transformation','EPSG','9166','EPSG','2975','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9167','NAD83(HARN) to NAVD88 height (8)','Uses Geoid99 hybrid model. Replaced by 2003 model (CT code 9168).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g1999u08.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus SE',0);
INSERT INTO "usage" VALUES('EPSG','10962','grid_transformation','EPSG','9167','EPSG','2976','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9168','NAD83(FBN) to NAVD88 height (1)','Uses Geoid03 hybrid model. Replaced by 2009 model (CT code 9173).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','8542','EPSG','5703',0.02,'EPSG','8666','Geoid (height correction) model file','geoid03_conus.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus',0);
INSERT INTO "usage" VALUES('EPSG','10963','grid_transformation','EPSG','9168','EPSG','1323','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9169','NAD83(HARN) to NAVD88 height (9)','Uses Geoid06 hybrid model. Replaced by Geoid09 model (CT code 9174).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4957','EPSG','5703',0.02,'EPSG','8666','Geoid (height correction) model file','geoid06_ak.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US AK',0);
INSERT INTO "usage" VALUES('EPSG','10964','grid_transformation','EPSG','9169','EPSG','1330','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9170','NAD83(FBN) to ASVD02 height (1)','Uses Geoid09 hybrid model. Replaced by Geoid12 model (CT code 7650).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','8542','EPSG','6643',0.05,'EPSG','8666','Geoid (height correction) model file','g2009s01.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Asm 09',0);
INSERT INTO "usage" VALUES('EPSG','10965','grid_transformation','EPSG','9170','EPSG','2288','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9171','NAD83(FBN) to GUVD04 height (1)','Uses Geoid09 hybrid model. Replaced by Geoid12 model (CT code 7648).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','8542','EPSG','6644',0.05,'EPSG','8666','Geoid (height correction) model file','g2009g01.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Gum 09',0);
INSERT INTO "usage" VALUES('EPSG','10966','grid_transformation','EPSG','9171','EPSG','3255','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9172','NAD83(FBN) to NMVD03 height (1)','Uses Geoid09 hybrid model. Replaced by Geoid12 model (CT code 7649).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','8542','EPSG','6640',0.05,'EPSG','8666','Geoid (height correction) model file','g2009g01.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Mnp 09',0);
INSERT INTO "usage" VALUES('EPSG','10967','grid_transformation','EPSG','9172','EPSG','4171','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9173','NAD83(NSRS2007) to NAVD88 height (1)','Uses Geoid09 hybrid model. Replaced by 2012 model (CT code 6326).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4893','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','geoid09_conus.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus 09',0);
INSERT INTO "usage" VALUES('EPSG','10968','grid_transformation','EPSG','9173','EPSG','1323','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9174','NAD83(NSRS2007) to NAVD88 height (2)','Uses Geoid09 hybrid model. Replaced by Geoid12 model (CT code 6327).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4893','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','geoid09_ak.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US AK 09',0);
INSERT INTO "usage" VALUES('EPSG','10969','grid_transformation','EPSG','9174','EPSG','1330','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9175','NAD83(NSRS2007) to PRVD02 height (1)','Uses Geoid09 hybrid model. Replaced by Geoid12 model (CT code 7646).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4893','EPSG','6641',0.05,'EPSG','8666','Geoid (height correction) model file','g2009p01.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Pri 09',0);
INSERT INTO "usage" VALUES('EPSG','10970','grid_transformation','EPSG','9175','EPSG','3294','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9176','NAD83(NSRS2007) to VIVD09 height (1)','Uses Geoid09 hybrid model. Replaced by Geoid12 model (CT code 7647).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4893','EPSG','6642',0.05,'EPSG','8666','Geoid (height correction) model file','g2009p01.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Vir 09',0);
INSERT INTO "usage" VALUES('EPSG','10971','grid_transformation','EPSG','9176','EPSG','3330','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9187','RGAF09 to IGN 1988 SB height (2)','Accuracy 0.05m to 0.1m. Replaces RGAF09 to IGN 1988 SB height (1), transformation code 5508. For reversible alternative to this transformation see RGAF09 to RGAF09 + IGN 1988 SB height (2) (code 9634).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','5619',0.1,'EPSG','8666','Geoid (height correction) model file','gg10_sbv2.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StB',0);
INSERT INTO "usage" VALUES('EPSG','14480','grid_transformation','EPSG','9187','EPSG','2891','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9188','RGAF09 to IGN 1988 SM height (2)','Accuracy 0.05m to 0.1m. Replaces RGAF09 to IGN 1988 SM height (1), transformation code 5505. For reversible alternative to this transformation see RGAF09 to RGAF09 + IGN 1988 SM height (2) (code 9635).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','5620',0.1,'EPSG','8666','Geoid (height correction) model file','gg10_smv2.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StM',0);
INSERT INTO "usage" VALUES('EPSG','14483','grid_transformation','EPSG','9188','EPSG','2890','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9228','RGSPM06 to Danger 1950 height (2)','Accuracy 0.01m to 0.05m within onshore area. Replaces RGSPM06 to Danger 1950 height (1), CT code 9137. For
reversible alternative to this transformation see RGSPM06 to RGSPM06 + Danger 1950 height (2) (code
9641).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','4466','EPSG','5792',0.05,'EPSG','8666','Geoid (height correction) model file','RASPM2018.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-SPM',0);
INSERT INTO "usage" VALUES('EPSG','13868','grid_transformation','EPSG','9228','EPSG','3299','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9229','NAD83(2011) to NAVD88 height (3)','Uses Geoid18 hybrid model. Replaces 12B model. See information source for further information. For reversible alternative to this transformation see NAD83(2011) to NAD83(2011) + NAVD88 height (3) (code 9595).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6319','EPSG','5703',0.015,'EPSG','8666','Geoid (height correction) model file','g2018u0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus 18',0);
INSERT INTO "usage" VALUES('EPSG','14353','grid_transformation','EPSG','9229','EPSG','1323','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9230','NAD83(2011) to PRVD02 height (2)','Uses Geoid18 hybrid model. Replaces 12B model. See information source for further information. For reversible alternative to this transformation see NAD83(2011) to PRVD02 height (2) (code 9622).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6319','EPSG','6641',0.015,'EPSG','8666','Geoid (height correction) model file','g2018p0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Pri 18',0);
INSERT INTO "usage" VALUES('EPSG','14360','grid_transformation','EPSG','9230','EPSG','3294','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9231','NAD83(2011) to VIVD09 height (2)','Uses Geoid18 hybrid model. Replaces 12B model. See information source for further information. For reversible alternative to this transformation see NAD83(2011) to NAD83(2011) + VIVD09 height (2) (code 9623).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','6319','EPSG','6642',0.015,'EPSG','8666','Geoid (height correction) model file','g2018p0.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-Vir 18',0);
INSERT INTO "usage" VALUES('EPSG','14362','grid_transformation','EPSG','9231','EPSG','3330','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','9232','ISN93 to ISN2016 (1)','Grid transformation based on Kriging between ISN93 and ISN2016. Accuracy is better than 5 cm in stable areas but can be around 25 cm in areas that have suffered deformation due to earthquake or volcanic eruption.','EPSG','9615','NTv2','EPSG','4659','EPSG','8086',0.05,'EPSG','8656','Latitude and longitude difference file','ISN93_ISN2016.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'LMI-Isl',0);
INSERT INTO "usage" VALUES('EPSG','13872','grid_transformation','EPSG','9232','EPSG','1120','EPSG','1026');
INSERT INTO "grid_transformation" VALUES('EPSG','9233','ISN2004 to ISN2016 (1)','Grid transformation based on Kriging between ISN2004 and ISN2016. Accuracy is better than 5 cm in stable areas but can be around 25 cm in areas that have suffered deformation due to earthquake or volcanic eruption.','EPSG','9615','NTv2','EPSG','5324','EPSG','8086',0.05,'EPSG','8656','Latitude and longitude difference file','ISN2004_ISN2016.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'LMI-Isl',0);
INSERT INTO "usage" VALUES('EPSG','13873','grid_transformation','EPSG','9233','EPSG','1120','EPSG','1026');
INSERT INTO "grid_transformation" VALUES('EPSG','9235','ATS77 to NAD83(CSRS)v3 (3)','Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1851. Replaced by ATS77 to NAD83(CSRS)v6 (4) (transformation code 9106).','EPSG','9615','NTv2','EPSG','4122','EPSG','8240',1.5,'EPSG','8656','Latitude and longitude difference file','NS778301.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NSGC-Can NS',0);
INSERT INTO "usage" VALUES('EPSG','13880','grid_transformation','EPSG','9235','EPSG','2313','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9236','ATS77 to NAD83(CSRS)v2 (2)','Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1689.','EPSG','9615','NTv2','EPSG','4122','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','PE7783V2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'PEI DOT-Can PEI',0);
INSERT INTO "usage" VALUES('EPSG','13881','grid_transformation','EPSG','9236','EPSG','1533','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9237','ATS77 to NAD83(CSRS)v2 (1)','Introduced in 1999. Can be taken as an approximate transformation ATS77 to WGS 84 - see code 1688.','EPSG','9615','NTv2','EPSG','4122','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','NB7783v2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GIC-Can NB',0);
INSERT INTO "usage" VALUES('EPSG','13882','grid_transformation','EPSG','9237','EPSG','1447','EPSG','1231');
INSERT INTO "grid_transformation" VALUES('EPSG','9238','NAD27 to NAD83(CSRS)v2 (4)','Introduced in 2011.  Precision of 20 cm in area covered by the input data set and 40 cm anywhere else, with the exception of the northwest area of the province (near the border with Quebec) where the precision deteriorates to 80 cm.','EPSG','9615','NTv2','EPSG','4267','EPSG','8237',0.8,'EPSG','8656','Latitude and longitude difference file','NB2783v2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SNB-Can NB',0);
INSERT INTO "usage" VALUES('EPSG','13883','grid_transformation','EPSG','9238','EPSG','1447','EPSG','1231');
INSERT INTO "grid_transformation" VALUES('EPSG','9239','NAD27 to NAD83(CSRS)v2 (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27 (code 4267) and NAD83(CSRS) (code 4617) have longitudes positive east. Can be taken as an approximate transformation NAD27 to WGS 84 - see code 1692.','EPSG','9615','NTv2','EPSG','4267','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','QUE27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',0);
INSERT INTO "usage" VALUES('EPSG','13884','grid_transformation','EPSG','9239','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9240','NAD27(CGQ77) to NAD83(CSRS)v2 (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD27(CGQ77) (code 4609) and NAD83(CSRS) (code 4617) have longitudes positive east.','EPSG','9615','NTv2','EPSG','4609','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','CGQ77-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',0);
INSERT INTO "usage" VALUES('EPSG','13885','grid_transformation','EPSG','9240','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9241','NAD83 to NAD83(CSRS)v2 (1)','Uses NT method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(CSRS)v2 (code 8237) have longitudes positive east. Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1696.','EPSG','9615','NTv2','EPSG','4269','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','NAD83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SGQ-Can QC',0);
INSERT INTO "usage" VALUES('EPSG','14556','grid_transformation','EPSG','9241','EPSG','1368','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9242','NAD27 to NAD83(CSRS)v3 (2)','Can be taken as an approximate transformation NAD27 to WGS 84 - see code 1703.','EPSG','9615','NTv2','EPSG','4267','EPSG','8240',1.5,'EPSG','8656','Latitude and longitude difference file','SK27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','13887','grid_transformation','EPSG','9242','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9243','NAD83 to NAD83(CSRS)v3 (2)','Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1697.','EPSG','9615','NTv2','EPSG','4269','EPSG','8240',1.5,'EPSG','8656','Latitude and longitude difference file','SK83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',1);
INSERT INTO "usage" VALUES('EPSG','13888','grid_transformation','EPSG','9243','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9244','NAD83 to NAD83(CSRS)v4 (3)','This gridded difference file AB_CSRS.DAC will need to be renamed to AB_CSRS.gsb to run in some software suites. Formats identical, but AB file is provincial fit only. Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1702.','EPSG','9615','NTv2','EPSG','4269','EPSG','8246',1.5,'EPSG','8656','Latitude and longitude difference file','AB_CSRS.DAC',NULL,NULL,NULL,NULL,NULL,NULL,'AB Env-Can AB',0);
INSERT INTO "usage" VALUES('EPSG','13889','grid_transformation','EPSG','9244','EPSG','2376','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9246','NAD83(CSRS)v6 to CGVD2013(CGG2013) height (1)','Uses CGG2013 model which uses bi-quadratic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time. Replaced by CGG2013a model (CT code 9247).','EPSG','1060','Geographic3D to GravityRelatedHeight (CGG2013)','EPSG','8251','EPSG','6647',0.03,'EPSG','8666','Geoid (height correction) model file','CGG2013n83.byn',NULL,NULL,NULL,NULL,NULL,NULL,'NRC Can CGG2013',0);
INSERT INTO "usage" VALUES('EPSG','13879','grid_transformation','EPSG','9246','EPSG','1061','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9247','NAD83(CSRS)v6 to CGVD2013(CGG2013a) height (1)','Uses bi-quadratic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time. For reversible alternative see NAD83(CSRS)v6 to NAD83(CSRS)v6 + CGVD2013(CGG2013a) height (1) (code 9644).','EPSG','1060','Geographic3D to GravityRelatedHeight (CGG2013)','EPSG','8251','EPSG','9245',0.03,'EPSG','8666','Geoid (height correction) model file','CGG2013n83a.byn',NULL,NULL,NULL,NULL,NULL,NULL,'NRC Can CGG2013a',0);
INSERT INTO "usage" VALUES('EPSG','14527','grid_transformation','EPSG','9247','EPSG','1061','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9256','POSGAR 2007 to SRVN16 height (1)','Uses geoid model Ar16. See information source for more information. For reversible alternative to this transformation see POSGAR 2007 to POSGAR 2007 + SRVN16 height (1) (code 9621).','EPSG','1047','Geographic3D to GravityRelatedHeight (Gravsoft)','EPSG','5342','EPSG','9255',0.05,'EPSG','8666','Geoid (height correction) model file','GEOIDE-Ar16.gri',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Arg',0);
INSERT INTO "usage" VALUES('EPSG','14342','grid_transformation','EPSG','9256','EPSG','4573','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9275','GHA height to EVRF2000 Austria height (1)','Care! Austrian literature describes this transformation in direction EVRF2000 Austria height to GHA height with offset subtracted. EPSG method has offset as an addition. See method formula and example. The grid is implemented in BEV-Transformator.','EPSG','1080','Vertical Offset by Grid Interpolation (BEV AT)','EPSG','5778','EPSG','9274',0.05,'EPSG','8732','Vertical offset file','GV_HoehenGrid_V1.csv',NULL,NULL,NULL,NULL,'EPSG','4312','BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','13933','grid_transformation','EPSG','9275','EPSG','1037','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9276','ETRS89 to EVRF2000 Austria height (1)','Austrian Geoid 2008. Accuracy 5cm (1 sigma). The transformation is implemented in BEV-Transformator. For reversible alternative to this transformation see ETRS89 to ETRS89 + EVRF2000 Austria height (1) (code 9600).','EPSG','1081','Geographic3D to GravityRelatedHeight (BEV AT)','EPSG','4937','EPSG','9274',0.05,'EPSG','8666','Geoid (height correction) model file','GEOID_GRS80_Oesterreich.csv',NULL,NULL,NULL,NULL,'EPSG','4258','BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','14251','grid_transformation','EPSG','9276','EPSG','1037','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9277','MGI to EVRF2000 Austria height (1)','Austrian Geoid 2008 (Bessel ellipsoid). Accuracy 5cm (1 sigma). The transformation is implemented in BEV-Transformator. For reversible alternative to this transformation see MGI to MGI + EVRF2000 Austria height (1) (code 9601).','EPSG','1081','Geographic3D to GravityRelatedHeight (BEV AT)','EPSG','9267','EPSG','9274',0.05,'EPSG','8666','Geoid (height correction) model file','GEOID_BESSEL_Oesterreich.csv',NULL,NULL,NULL,NULL,'EPSG','4312','BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','14254','grid_transformation','EPSG','9277','EPSG','1037','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9278','ETRS89 to GHA height (1)','This transformation gives same result as concatenation of ETRS89 to EVRF2000 Austria height and EVRF2000 Austria height to GHA height transformations, CTs code 9276 and 9275. Accuracy 5cm (1 sigma). The transformation is implemented in BEV-Transformator.','EPSG','1081','Geographic3D to GravityRelatedHeight (BEV AT)','EPSG','4937','EPSG','5778',0.05,'EPSG','8666','Geoid (height correction) model file','GV_Hoehengrid_plus_Geoid_V3.csv',NULL,NULL,NULL,NULL,'EPSG','4258','BEV-Aut',1);
INSERT INTO "usage" VALUES('EPSG','13936','grid_transformation','EPSG','9278','EPSG','1037','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9280','ITRF2005 to SA LLD height (1)','Hybrid geoid referenced to ITRF2005@2010.02. Accuracy 7cm at 1 sigma. For reversible alternative to this transformation see ITRF2005 to ITRF2005 + SA LLD height (1) (code 9643).','EPSG','1082','Geographic3D to GravityRelatedHeight (SA 2010)','EPSG','7910','EPSG','9279',0.07,'EPSG','8666','Geoid (height correction) model file','SAGEOID2010.dat',NULL,NULL,NULL,NULL,NULL,NULL,'NGI-Zaf',0);
INSERT INTO "usage" VALUES('EPSG','14524','grid_transformation','EPSG','9280','EPSG','3309','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9282','Amersfoort to ETRS89 (9)','Replaces Amersfoort to ETRS89 (7) (tfm code 7000). Horizontal component of official transformation RDNAPTRANS(TM)2018.','EPSG','9615','NTv2','EPSG','4289','EPSG','4258',0.001,'EPSG','8656','Latitude and longitude difference file','rdtrans2018.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NSGI-Nld 2018',0);
INSERT INTO "usage" VALUES('EPSG','13939','grid_transformation','EPSG','9282','EPSG','1275','EPSG','1051');
INSERT INTO "grid_transformation" VALUES('EPSG','9283','ETRS89 to NAP height (2)','Vertical component of official transformation RDNAPTRANS(TM)2018. Replaces earlier versions of RDNAPTRANS(TM). For reversible alternative to this transformation see ETRS89 to ETRS89 + NAP height (2) (code 9597).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4937','EPSG','5709',0.001,'EPSG','8666','Geoid (height correction) model file','nlgeo2018.gtx',NULL,NULL,NULL,NULL,NULL,NULL,'NSGI-Nld 2018',0);
INSERT INTO "usage" VALUES('EPSG','14346','grid_transformation','EPSG','9283','EPSG','1275','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9302','HS2-IRF to ETRS89 (1)','In conjunction with the HS2-TM projection (code 9301), emulates zero-distortion HS2P1+14 Snake projection. For use with OSNet v2009 CORS. Supersedes HS2TN02 used with OSNet v2001. Derived at 350000 locations, RMS 0.15 mm; this is considered errorless.','EPSG','9615','NTv2','EPSG','9299','EPSG','4258',0.0,'EPSG','8656','Latitude and longitude difference file','HS2TN15_NTv2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'HS2-Gbr OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','14060','grid_transformation','EPSG','9302','EPSG','4582','EPSG','1260');
INSERT INTO "grid_transformation" VALUES('EPSG','9304','ETRS89 to HS2-VRF height (1)','Defines HS2-VRF using OSNet v2009. Full grid also available in a single file in each of Leica, Topcon and Trimble formats.','EPSG','9661','Geographic3D to GravityRelatedHeight (EGM)','EPSG','4937','EPSG','9303',0.001,'EPSG','8666','Geoid (height correction) model file','HS2GM15W.grd',NULL,NULL,NULL,NULL,NULL,NULL,'HS2-Gbr HS2GM15 W',0);
INSERT INTO "usage" VALUES('EPSG','14061','grid_transformation','EPSG','9304','EPSG','4582','EPSG','1260');
INSERT INTO "grid_transformation" VALUES('EPSG','9305','SRGI2013 to INAGeoid2020 height (1)','Defines INAGeoid2020 reference surface and as such is considered to be errorless. Internal accuracy is between 0.05 and 0.24m on the large islands of Indonesia. For reversible alternative see SRGI2013 to SRGI2013 + INAGeoid2020 height (1) (code 9629).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','9469','EPSG','9471',0.0,'EPSG','8666','Geoid (height correction) model file','INAGEOID20.gtx',NULL,NULL,NULL,NULL,NULL,NULL,'BIG-Idn INAGeoid20',0);
INSERT INTO "usage" VALUES('EPSG','14385','grid_transformation','EPSG','9305','EPSG','1122','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9310','DHDN to ETRS89 (10)','Replaces SeTa2009 from 2018-01-15. Derived using the 2016 implementation of ETRS89 / DREF91 and DHHN2016. Coincident with the transformation of cadastral data from DHDN to ETRS89 used by LVGL at a level of 1-2 mm.','EPSG','9615','NTv2','EPSG','4314','EPSG','4258',0.01,'EPSG','8656','Latitude and longitude difference file','SeTa2016.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'LVGL-Deu SL 2016',0);
INSERT INTO "usage" VALUES('EPSG','13970','grid_transformation','EPSG','9310','EPSG','4584','EPSG','1055');
INSERT INTO "grid_transformation" VALUES('EPSG','9312','NZVD2016 height to Auckland 1946 height (2)','Derived at 260 control points. Mean offset 0.292m, standard deviation 0.029m, maximum difference from mean 0.075m. Supersedes NZVD2016 height to Auckland 1946 height (1) (code 7860) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5759',0.02,'EPSG','8732','Vertical offset file','auckht1946-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ AUCK gtx',0);
INSERT INTO "usage" VALUES('EPSG','13941','grid_transformation','EPSG','9312','EPSG','3764','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9313','NZVD2016 height to Bluff 1955 height (2)','Derived at 71 control points. Mean offset 0.273m, standard deviation 0.034m, maximum difference from mean 0.079m. Supersedes NZVD2016 height to Bluff 1955 height (1) (code 7861) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5760',0.02,'EPSG','8732','Vertical offset file','blufht1955-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ BLUF gtx',0);
INSERT INTO "usage" VALUES('EPSG','13942','grid_transformation','EPSG','9313','EPSG','3801','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9314','NZVD2016 height to Dunedin 1958 height (2)','Derived at 197 control points. Mean offset 0.326m, standard deviation 0.043m, maximum difference from mean 0.152m. Supersedes NZVD2016 height to Dunedin 1958 height (1) (code 7862) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5761',0.02,'EPSG','8732','Vertical offset file','duneht1958-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ DUNE gtx',0);
INSERT INTO "usage" VALUES('EPSG','13943','grid_transformation','EPSG','9314','EPSG','3803','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9315','NZVD2016 height to Dunedin-Bluff 1960 height (2)','Derived at 205 control points. Mean offset 0.254m, standard deviation 0.039m, maximum difference from mean 0.11m. Supersedes NZVD2016 height to Dunedin-Bluff 1960 height (1) (code 7863) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','4458',0.02,'EPSG','8732','Vertical offset file','dublht1960-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ DUBL gtx',0);
INSERT INTO "usage" VALUES('EPSG','13944','grid_transformation','EPSG','9315','EPSG','3806','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9316','NZVD2016 height to Gisborne 1926 height (2)','Derived at 274 control points. Mean offset 0.343m, standard deviation 0.025m, maximum difference from mean 0.09m. Supersedes NZVD2016 height to Gisborne 1926 height (1) (code 7864) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5762',0.02,'EPSG','8732','Vertical offset file','gisbht1926-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ GISB gtx',0);
INSERT INTO "usage" VALUES('EPSG','13945','grid_transformation','EPSG','9316','EPSG','3771','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9317','NZVD2016 height to Lyttelton 1937 height (2)','Derived at 923 control points. Mean offset 0.34m, standard deviation 0.041m, maximum difference from mean 0.149m. Supersedes NZVD2016 height to Lyttelton 1937 height (1) (code 7865) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5763',0.01,'EPSG','8732','Vertical offset file','lyttht1937-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ LYTT gtx',0);
INSERT INTO "usage" VALUES('EPSG','13946','grid_transformation','EPSG','9317','EPSG','3804','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9318','NZVD2016 height to Moturiki 1953 height (2)','Derived at 519 control points. Mean offset 0.309m, standard deviation 0.071m, maximum difference from mean 0.231m. Supersedes NZVD2016 height to Moturiki 1953 height (1) (code 7866) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5764',0.02,'EPSG','8732','Vertical offset file','motuht1953-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ MOTU gtx',0);
INSERT INTO "usage" VALUES('EPSG','13947','grid_transformation','EPSG','9318','EPSG','3768','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9319','NZVD2016 height to Napier 1962 height (2)','Derived at 207 control points. Mean offset 0.203m, standard deviation 0.034m, maximum difference from mean 0.096m. Supersedes NZVD2016 height to Napier 1962 height (1) (code 7867) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5765',0.02,'EPSG','8732','Vertical offset file','napiht1962-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ NAPI gtx',0);
INSERT INTO "usage" VALUES('EPSG','13948','grid_transformation','EPSG','9319','EPSG','3772','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9320','NZVD2016 height to Nelson 1955 height (2)','Derived at 256 control points. Mean offset 0.329m, standard deviation 0.039m, maximum difference from mean 0.114m. Supersedes NZVD2016 height to Nelson 1955 height (1) (code 7868) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5766',0.02,'EPSG','8732','Vertical offset file','nelsht1955-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ NELS gtx',0);
INSERT INTO "usage" VALUES('EPSG','13949','grid_transformation','EPSG','9320','EPSG','3802','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9321','NZVD2016 height to One Tree Point 1964 height (2)','Derived at 137 control points. Mean offset 0.077m, standard deviation 0.042m, maximum difference from mean 0.107m. Supersedes NZVD2016 height to One Tree Point 1964 height (1) (code 7869) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5767',0.01,'EPSG','8732','Vertical offset file','ontpht1964-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ ONTP gtx',0);
INSERT INTO "usage" VALUES('EPSG','13950','grid_transformation','EPSG','9321','EPSG','3762','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9322','NZVD2016 height to Stewart Island 1977 height (2)','Derived at 4 control points. Mean offset 0.299m, standard deviation 0.025m, maximum difference from mean 0.039m. Supersedes NZVD2016 height to Stewart Island 1977 height (1) (code 7870) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5772',0.18,'EPSG','8732','Vertical offset file','stisht1977-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ STWT gtx',0);
INSERT INTO "usage" VALUES('EPSG','13951','grid_transformation','EPSG','9322','EPSG','3338','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9323','NZVD2016 height to Taranaki 1970 height (2)','Derived at 125 control points. Mean offset 0.286m, standard deviation 0.026m, maximum difference from mean 0.07m. Supersedes NZVD2016 height to Taranaki 1970 height (1) (code 7871) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5769',0.02,'EPSG','8732','Vertical offset file','taraht1970-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ TARA gtx',0);
INSERT INTO "usage" VALUES('EPSG','13952','grid_transformation','EPSG','9323','EPSG','3769','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9324','NZVD2016 height to Wellington 1953 height (2)','Derived at 137 control points. Mean offset 0.408m, standard deviation 0.054m, maximum difference from mean 0.112m. Supersedes NZVD2016 height to Wellington 1953 height (1) (code 7872) after change of grid file format.','EPSG','1084','Vertical Offset by Grid Interpolation (gtx)','EPSG','7839','EPSG','5770',0.02,'EPSG','8732','Vertical offset file','wellht1953-nzvd2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ WGTN gtx',0);
INSERT INTO "usage" VALUES('EPSG','13953','grid_transformation','EPSG','9324','EPSG','3773','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9325','NZGD2000 to NZVD2009 height (2)','Defines NZVD2009 vertical datum (datum code 1039, CRS code 4440). Supersedes NZGD2000 to NZVD2009 height (1) (code 4459) after change of grid file format. For reversible alternative to this CT see NZGD2000 to NZGD2000 + NZVD2009 height (2) (code 9627).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4959','EPSG','4440',0.1,'EPSG','8666','Geoid (height correction) model file','nzgeoid2009.gtx',NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ 2009 gtx',0);
INSERT INTO "usage" VALUES('EPSG','14379','grid_transformation','EPSG','9325','EPSG','1175','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9326','NZGD2000 to NZVD2016 height (2)','Defines NZVD2016 vertical datum (datum code 1169, CRS code 7839). Supersedes NZGD2000 to NZVD2016 height (1) (code 7840) after change of grid file format. For reversible alternative to this CT see NZGD2000 to NZGD2000 + NZVD2016 height (2) (code 9628).','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4959','EPSG','7839',0.1,'EPSG','8666','Geoid (height correction) model file','nzgeoid2016.gtx',NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ 2016 gtx',0);
INSERT INTO "usage" VALUES('EPSG','14382','grid_transformation','EPSG','9326','EPSG','1175','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9327','NTF to RGF93 v1 (1)','May be emulated using NTv2 method - see tfm code 15958.','EPSG','1087','Geocentric translation by Grid Interpolation (IGN)','EPSG','4275','EPSG','4171',1.0,'EPSG','8727','Geocentric translation file','gr3df97a.txt',NULL,NULL,NULL,NULL,'EPSG','4171','IGN-Fra 1m',0);
INSERT INTO "usage" VALUES('EPSG','13956','grid_transformation','EPSG','9327','EPSG','3694','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','9328','NEA74 Noumea to RGNC91-93 (3)','Developed in July 2002 and officially adopted in August 2005. May be emulated using NTv2 method - see RGNC91-93 to NEA74 Noumea (4) (code 1295).','EPSG','1087','Geocentric translation by Grid Interpolation (IGN)','EPSG','4644','EPSG','4749',0.05,'EPSG','8727','Geocentric translation file','gr3dnc03a.mnt',NULL,NULL,NULL,NULL,'EPSG','4749','BGN-Ncl 0.05m',0);
INSERT INTO "usage" VALUES('EPSG','13957','grid_transformation','EPSG','9328','EPSG','2823','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','9329','IGN72 Grande Terre to RGNC91-93 (4)','Developed in July 2002 and officially adopted in August 2005. May be emulated using NTv2 method - see RGNC91-93 to IGN72 Grande Terre (6) (code 15962).','EPSG','1087','Geocentric translation by Grid Interpolation (IGN)','EPSG','4662','EPSG','4749',0.1,'EPSG','8727','Geocentric translation file','gr3dnc01b.mnt',NULL,NULL,NULL,NULL,'EPSG','4749','BGN-Ncl 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','13958','grid_transformation','EPSG','9329','EPSG','2822','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','9330','IGN72 Grande Terre to RGNC91-93 (5)','Developed in July 2002 and officially adopted in August 2005.','EPSG','1087','Geocentric translation by Grid Interpolation (IGN)','EPSG','4662','EPSG','4749',0.05,'EPSG','8727','Geocentric translation file','gr3dnc02b.mnt',NULL,NULL,NULL,NULL,'EPSG','4749','BGN-Ncl Noum 0.05m',0);
INSERT INTO "usage" VALUES('EPSG','13959','grid_transformation','EPSG','9330','EPSG','2823','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','9338','DHDN to ETRS89 (9)','Official transformation for the state of Baden-Württemberg. Used in ATKIS (Amtliches Topographisch-Kartographisches Informationssystem [Official Topographic and Cartographic Information System]).','EPSG','9615','NTv2','EPSG','4314','EPSG','4258',0.1,'EPSG','8656','Latitude and longitude difference file','BWTA2017.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'LGL-Deu BWTA2017',0);
INSERT INTO "usage" VALUES('EPSG','13961','grid_transformation','EPSG','9338','EPSG','4580','EPSG','1055');
INSERT INTO "grid_transformation" VALUES('EPSG','9352','RGNC91-93 to NGNC08 height (1)','Geoid model RANC08 realizes NGNC08 height (CRS code 9351) to a precision of 2-5cm. For reversible alternative to this transformation see RGNC91-93 to RGNC91-93 + NGNC08 height (1) (code 9640).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','4907','EPSG','9351',0.03,'EPSG','8666','Geoid (height correction) model file','Ranc08_Circe.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'BGN-Ncl RANC08',0);
INSERT INTO "usage" VALUES('EPSG','14497','grid_transformation','EPSG','9352','EPSG','3430','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9355','KSA-GRF17 to KSA-VRF14 height (1)','Hybrid geoid, grid resolution 0.02° lat x 0.025° lon. Accuracy ~2 cm in Eastern region and ~10-20 cm in rest of KSA. File also available in IGN2009 format. To access KSA-GEOID17 contact <NAME_EMAIL>. For reversible alternative see code 9620.','EPSG','1047','Geographic3D to GravityRelatedHeight (Gravsoft)','EPSG','9332','EPSG','9335',0.1,'EPSG','8666','Geoid (height correction) model file','KSA-GEOID17.gra',NULL,NULL,NULL,NULL,NULL,NULL,'GCS-Sau',0);
INSERT INTO "usage" VALUES('EPSG','14338','grid_transformation','EPSG','9355','EPSG','3303','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9363','Ain el Abd to KSA-GRF17 (2)','Accuracy 5-10 cm.','EPSG','1087','Geocentric translation by Grid Interpolation (IGN)','EPSG','4204','EPSG','9333',0.1,'EPSG','8727','Geocentric translation file','ARAMCO_AAA-KSAGRF_6.tac',NULL,NULL,NULL,NULL,'EPSG','9333','GCS-Sau 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','14010','grid_transformation','EPSG','9363','EPSG','3303','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','9365','ETRS89 to TPEN11-IRF (1)','In conjunction with the TPEN11-TM map projection (code 9366) applied to TPEN11-IRF (code 9364), emulates the TPEN11 Snake and TPEN11ext Snake projections. Applied to ETRS89 (as realized through the OSNet v2009 CORS) defines TPEN11-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9364',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-TPEN11-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr TPEN11 OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','13984','grid_transformation','EPSG','9365','EPSG','4583','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9369','ETRS89 to MML07-IRF (1)','In conjunction with the MML07-TM map projection (code 9370) applied to MML07-IRF (code 9372), emulates the MML07 Snake projection. Applied to ETRS89 (as realized through the OSNet v2009 CORS) defines MML07-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9372',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-MML07-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr MML07 OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','14002','grid_transformation','EPSG','9369','EPSG','4588','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9386','ETRS89 to AbInvA96_2020-IRF (1)','In conjunction with AbInvA96_2020-TM map projection (code 9385) applied to AbInvA96_2020-IRF (code 9384), emulates the AbInvA96_2020 Snake projection. Applied to ETRS89 (as realized through the OSNet v2009) defines AbInvA96_2020-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9384',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-AbInvA96_2020-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'TS-Gbr A96 OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','14063','grid_transformation','EPSG','9386','EPSG','4589','EPSG','1196');
INSERT INTO "grid_transformation" VALUES('EPSG','9408','ED50 to ETRS89 (16)','Replaces ED50 to ETRS89 (12) (code 15932).','EPSG','9615','NTv2','EPSG','4230','EPSG','4258',0.2,'EPSG','8656','Latitude and longitude difference file','PENR2009.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp v3 pen',0);
INSERT INTO "usage" VALUES('EPSG','14064','grid_transformation','EPSG','9408','EPSG','4605','EPSG','1026');
INSERT INTO "grid_transformation" VALUES('EPSG','9409','ED50 to ETRS89 (17)','Replaces ED50 to ETRS89 (12) (code 15932).','EPSG','9615','NTv2','EPSG','4230','EPSG','4258',0.2,'EPSG','8656','Latitude and longitude difference file','BALR2009.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp v3 Bal',0);
INSERT INTO "usage" VALUES('EPSG','14065','grid_transformation','EPSG','9409','EPSG','2335','EPSG','1026');
INSERT INTO "grid_transformation" VALUES('EPSG','9410','ETRS89 to Alicante height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + Alicante height (1) (code 9605).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','5782',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14272','grid_transformation','EPSG','9410','EPSG','2366','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9411','ETRS89 to Mallorca height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + Mallorca height (1) (code 9608).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9392',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14282','grid_transformation','EPSG','9411','EPSG','4602','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9412','ETRS89 to Menorca height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + Menorca height (1) (code 9609).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9393',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14285','grid_transformation','EPSG','9412','EPSG','4603','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9413','ETRS89 to Ibiza height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + Ibiza (1) (code 9607).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9394',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14278','grid_transformation','EPSG','9413','EPSG','4604','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9414','ETRS89 to Ceuta 2 height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + Ceuta 2 height (1) (code 9606).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9402',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14275','grid_transformation','EPSG','9414','EPSG','4590','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9415','REGCAN95 to Lanzarote height (1)','For reversible alternative to this transformation see REGCAN95 to REGCAN95 + Lanzarote height (1) (code 9615).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9395',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14307','grid_transformation','EPSG','9415','EPSG','4591','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9416','REGCAN95 to Fuerteventura height (1)','For reversible alternative to this transformation see REGCAN95 to REGCAN95 + Fuerteventura height (1) (code 9611).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9396',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14293','grid_transformation','EPSG','9416','EPSG','4592','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9417','REGCAN95 to Gran Canaria height (1)','For reversible alternative to this transformation see REGCAN95 to REGCAN95 + Gran Canaria height (1) (code 9612).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9397',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14298','grid_transformation','EPSG','9417','EPSG','4593','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9418','REGCAN95 to Tenerife height (1)','','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9398',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14074','grid_transformation','EPSG','9418','EPSG','4594','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9419','REGCAN95 to La Gomera height (1)','For reversible alternative to this transformation see REGCAN95 to REGCAN95 + La Gomera height (1) (code 9613).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9399',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14301','grid_transformation','EPSG','9419','EPSG','4595','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9420','REGCAN95 to La Palma height (1)','For reversible alternative to this transformation see REGCAN95 to REGCAN95 + La Palma height (1) (code 9614).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9400',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14304','grid_transformation','EPSG','9420','EPSG','4596','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9421','REGCAN95 to El Hierro height (1)','For reversible alternative to this transformation see REGCAN95 to REGCAN + El Hierro height (1) (code 9610).','EPSG','1025','Geographic3D to GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9401',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14288','grid_transformation','EPSG','9421','EPSG','4597','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9431','GHA height to EVRF2019 height (1)','Determined at 147 points, SD 0.060m. Offset: mean -0.306m, minimum -0.442m, maximum -0.219m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5778','EPSG','9389',0.12,'EPSG','8732','Vertical offset file','at_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Aut 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14089','grid_transformation','EPSG','9431','EPSG','1037','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9432','GHA height to EVRF2019 mean-tide height (1)','Determined at 147 points, SD 0.058m. Offset: mean -0.330m, minimum -0.463m, maximum -0.245m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5778','EPSG','9390',0.116,'EPSG','8732','Vertical offset file','at_2019_m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Aut 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14090','grid_transformation','EPSG','9432','EPSG','1037','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9433','Ostend height to EVRF2019 mean-tide height (1)','Determined at 39 points, SD 0.016m. Offset: mean -2.323m, minimum -2.372m, maximum -2.290m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5710','EPSG','9390',0.032,'EPSG','8732','Vertical offset file','be_2019_m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bel 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14091','grid_transformation','EPSG','9433','EPSG','1347','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9434','Ostend height to EVRF2019 height (1)','Determined at 39 points, SD 0.017m. Offset: mean -2.315m, minimum -2.364m, maximum -2.279m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5710','EPSG','9389',0.034,'EPSG','8732','Vertical offset file','be_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bel 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14092','grid_transformation','EPSG','9434','EPSG','1347','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9435','DHHN2016 height to EVRF2019 height (1)','Determined at 802 points, SD 0.010m. Offset: mean 0.013m, minimum -0.008m, maximum 0.039m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','7837','EPSG','9389',0.02,'EPSG','8732','Vertical offset file','de_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Deu 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14093','grid_transformation','EPSG','9435','EPSG','3339','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9436','DHHN2016 height to EVRF2019 mean-tide height (1)','Determined at 802 points, SD 0.003m. Offset: mean 0.007m, minimum -0.004m, maximum 0.018m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','7837','EPSG','9390',0.006,'EPSG','8732','Vertical offset file','de_2019_m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Deu 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14094','grid_transformation','EPSG','9436','EPSG','3339','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9437','Trieste height to EVRF2019 height (1)','Determined at 46 points, SD 0.016m. Offset: mean -0.548m, minimum -0.595m, maximum -0.500m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5195','EPSG','9389',0.032,'EPSG','8732','Vertical offset file','mk_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Mkd 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14095','grid_transformation','EPSG','9437','EPSG','1148','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9438','Trieste height to EVRF2019 mean-tide height (1)','Determined at 46 points, SD 0.015m. Offset: mean -0.604m, minimum -0.650m, maximum -0.558m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5195','EPSG','9390',0.03,'EPSG','8732','Vertical offset file','mk_2019_m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Mkd 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14096','grid_transformation','EPSG','9438','EPSG','1148','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9439','Cascais height to EVRF2019 height (1)','Determined at 18 points, SD 0.010m. Offset: mean -0.277m, minimum -0.323m, maximum -0.264m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5780','EPSG','9389',0.02,'EPSG','8732','Vertical offset file','pt_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Prt 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14097','grid_transformation','EPSG','9439','EPSG','1294','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9440','Cascais height to EVRF2019 mean-tide height (1)','Determined at 18 points, SD 0.007m. Offset: mean -0.343m, minimum -0.383m, maximum -0.332m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5780','EPSG','9390',0.014,'EPSG','8732','Vertical offset file','pt_2019_m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Prt 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14098','grid_transformation','EPSG','9440','EPSG','1294','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9441','SVS2010 height to EVRF2019 height (1)','Determined at 66 points, SD 0.003m. Offset: mean -0.258m, minimum -0.264m, maximum -0.250m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','8690','EPSG','9389',0.006,'EPSG','8732','Vertical offset file','si_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Svn 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14099','grid_transformation','EPSG','9441','EPSG','3307','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9442','SVS2010 height to EVRF2019 mean-tide height (1)','Determined at 66 points, SD 0.004m. Offset: mean -0.290m, minimum -0.295m, maximum -0.280m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','8690','EPSG','9390',0.008,'EPSG','8732','Vertical offset file','si_2019_m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Svn 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14100','grid_transformation','EPSG','9442','EPSG','3307','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9443','LHN95 height to EVRF2019 height (1)','Determined at 553 points, SD 0.075m. Offset: mean -0.204m, minimum -0.330m, maximum -0.130m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5729','EPSG','9389',0.15,'EPSG','8732','Vertical offset file','ch_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Che 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14101','grid_transformation','EPSG','9443','EPSG','1286','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9444','LHN95 height to EVRF2019 mean-tide height (1)','Determined at 553 points, SD 0.073m. Offset: mean -0.233m, minimum -0.353m, maximum -0.044m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5729','EPSG','9390',0.146,'EPSG','8732','Vertical offset file','ch_2019_m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Che 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14102','grid_transformation','EPSG','9444','EPSG','1286','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9445','ODN height to EVRF2019 height (1)','Determined at 35 points, SD 0.011m. Offset: mean -0.181m, minimum -0.202m, maximum -0.161m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5701','EPSG','9389',0.022,'EPSG','8732','Vertical offset file','gb_2019_z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Gbr 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14103','grid_transformation','EPSG','9445','EPSG','2792','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9454','ETRS89 to GBK19-IRF (1)','In conjunction with the GBK19-TM map projection (code 9455) applied to GBK19-IRF (code 9453), emulates the GBK19 Snake projection. Applied to ETRS89 (as realized through the OSNet v2009 CORS) defines GBK19-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9453',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-GBK19-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr GBK19 OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','14129','grid_transformation','EPSG','9454','EPSG','4607','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9461','GDA2020 to AVWS height (1)','AGQG is used to realise AVWS. Uncertainties (4-8 cm across mainland Australia) given in accompanying file AGQG_uncertainty_20191107.gsb. For reversible alternative to this transformation see GDA2020 to GDA2020 + AVWS height (1) (code 9465).','EPSG','1048','Geographic3D to GravityRelatedHeight (AUSGeoid v2)','EPSG','7843','EPSG','9458',0.1,'EPSG','8666','Geoid (height correction) model file','AGQG_20191107.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus AGQG 20191107',1);
INSERT INTO "usage" VALUES('EPSG','14141','grid_transformation','EPSG','9461','EPSG','4177','EPSG','1264');
INSERT INTO "grid_transformation" VALUES('EPSG','9465','GDA2020 to GDA2020 + AVWS height (1)','Reversible alternative to GDA2020 to AVWS height (1) (code 9461). AGQG is used to realise AVWS. Uncertainties (4-8 cm across mainland Australia) given in accompanying file AGQG_uncertainty_20191107.gsb.','EPSG','1083','Geog3D to Geog2D+GravityRelatedHeight (AUSGeoidv2)','EPSG','7843','EPSG','9462',0.1,'EPSG','8666','Geoid (height correction) model file','AGQG_20191107.gsb',NULL,NULL,NULL,NULL,'EPSG','7844','GA-Aus AGQG 20191107',1);
INSERT INTO "usage" VALUES('EPSG','14145','grid_transformation','EPSG','9465','EPSG','4177','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9466','GDA2020 to GDA2020 + AHD height (1)','Reversible alternative to GDA2020 to AHD height (1) (code 8451). Uncertainties given in accompanying file AUSGeoid2020_20180201_error.gsb','EPSG','1083','Geog3D to Geog2D+GravityRelatedHeight (AUSGeoidv2)','EPSG','7843','EPSG','9463',0.15,'EPSG','8666','Geoid (height correction) model file','AUSGeoid2020_20180201.gsb',NULL,NULL,NULL,NULL,'EPSG','7844','GA-Aus 2020',0);
INSERT INTO "usage" VALUES('EPSG','14146','grid_transformation','EPSG','9466','EPSG','4493','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9467','GDA94 to GDA94 + AHD height (1)','Reversible alternative to GDA94 to AHD height (1) (code 5656). Uses AUSGeoid09 model which uses bi-cubic interpolation; bi-linear interpolation of the grid file will give results agreeing to within 1cm 99.97% of the time.','EPSG','1083','Geog3D to Geog2D+GravityRelatedHeight (AUSGeoidv2)','EPSG','4939','EPSG','9464',0.15,'EPSG','8666','Geoid (height correction) model file','AUSGeoid09_V1.01.gsb',NULL,NULL,NULL,NULL,'EPSG','4283','GA-Aus09',0);
INSERT INTO "usage" VALUES('EPSG','14147','grid_transformation','EPSG','9467','EPSG','4493','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9483','Canada velocity grid v7','','EPSG','1070','Point motion by grid (Canada NTv2_Vel)','EPSG','8254','EPSG','8254',0.01,'EPSG','1050','Point motion velocity grid file','cvg70.cvb',NULL,NULL,NULL,NULL,NULL,NULL,'NRC-Can cvg7.0',0);
INSERT INTO "usage" VALUES('EPSG','14214','grid_transformation','EPSG','9483','EPSG','1061','EPSG','1131');
INSERT INTO "grid_transformation" VALUES('EPSG','9484','ETRS89 to NN54 height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + NN54 height (1) (code 9594).','EPSG','1047','Geographic3D to GravityRelatedHeight (Gravsoft)','EPSG','4937','EPSG','5776',0.02,'EPSG','8666','Geoid (height correction) model file','href2008a.bin',NULL,NULL,NULL,NULL,NULL,NULL,'SK-Nor 2008',0);
INSERT INTO "usage" VALUES('EPSG','14374','grid_transformation','EPSG','9484','EPSG','1352','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9485','ETRS89 to NN2000 height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + NN2000 height (1) (code 9593).','EPSG','1047','Geographic3D to GravityRelatedHeight (Gravsoft)','EPSG','4937','EPSG','5941',0.02,'EPSG','8666','Geoid (height correction) model file','HREF2018B_NN2000_EUREF89.bin',NULL,NULL,NULL,NULL,NULL,NULL,'SK-Nor 2018',0);
INSERT INTO "usage" VALUES('EPSG','14376','grid_transformation','EPSG','9485','EPSG','1352','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9496','MGI 1901 to SRB-ETRS89 (9)','','EPSG','9615','NTv2','EPSG','3906','EPSG','8685',0.03,'EPSG','8656','Latitude and longitude difference file','MGI1901_TO_SRBETRS89_NTv2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'RGZ-Srb 0.1m 2020',0);
INSERT INTO "usage" VALUES('EPSG','14226','grid_transformation','EPSG','9496','EPSG','4543','EPSG','1185');
INSERT INTO "grid_transformation" VALUES('EPSG','9550','NAD83 to NAD83(CSRS)v6 (10)','File NLCSRSV4A.GSB corrects error in file header record previously released as NLCSRSV4.GSB. No change to gridded data.','EPSG','9615','NTv2','EPSG','4269','EPSG','8252',0.1,'EPSG','8656','Latitude and longitude difference file','NLCSRSV4A.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'CGS-Can Nfl island',0);
INSERT INTO "usage" VALUES('EPSG','14831','grid_transformation','EPSG','9550','EPSG','4612','EPSG','1026');
INSERT INTO "grid_transformation" VALUES('EPSG','9553','Cascais height to EVRF2019 height (2)','Determined at 18 points, SD 0.014m. Offset: mean -0.275m, minimum -0.322m, maximum -0.262m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5780','EPSG','9389',0.028,'EPSG','8732','Vertical offset file','pt_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Prt 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14842','grid_transformation','EPSG','9553','EPSG','1294','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9554','Cascais height to EVRF2019 mean-tide height (2)','Determined at 18 points, SD 0.012m. Offset: mean -0.340m, minimum -0.383m, maximum -0.324m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5780','EPSG','9390',0.024,'EPSG','8732','Vertical offset file','pt_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Prt 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14671','grid_transformation','EPSG','9554','EPSG','1294','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9555','DHHN2016 height to EVRF2019 height (2)','Determined at 802 points, SD 0.010m. Offset: mean 0.016m, minimum -0.004m, maximum 0.052m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','7837','EPSG','9389',0.02,'EPSG','8732','Vertical offset file','de_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Deu 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14672','grid_transformation','EPSG','9555','EPSG','3339','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9556','DHHN2016 height to EVRF2019 mean-tide height (2)','Determined at 802 points, SD 0.004m. Offset: mean 0.009m, minimum -0.011m, maximum 0.028m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','7837','EPSG','9390',0.008,'EPSG','8732','Vertical offset file','de_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Deu 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14843','grid_transformation','EPSG','9556','EPSG','3339','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9557','GHA height to EVRF2019 height (2)','Determined at 150 points, SD 0.068m. Offset: mean -0.309m, minimum -0.450m, maximum -0.210m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5778','EPSG','9389',0.136,'EPSG','8732','Vertical offset file','at_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Aut 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14673','grid_transformation','EPSG','9557','EPSG','1037','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9558','GHA height to EVRF2019 mean-tide height (2)','Determined at 150 points, SD 0.065m. Offset: mean -0.333m, minimum -0.471m, maximum -0.236m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5778','EPSG','9390',0.13,'EPSG','8732','Vertical offset file','at_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Aut 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14674','grid_transformation','EPSG','9558','EPSG','1037','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9559','LHN95 height to EVRF2019 height (2)','Determined at 553 points, SD 0.073m. Offset: mean -0.216m, minimum -0.478m, maximum -0.021m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5729','EPSG','9389',0.146,'EPSG','8732','Vertical offset file','ch_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Che 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14675','grid_transformation','EPSG','9559','EPSG','1286','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9560','LHN95 height to EVRF2019 mean-tide height (2)','Determined at 553 points, SD 0.071m. Offset: mean -0.244m, minimum -0.506m, maximum -0.012m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5729','EPSG','9390',0.142,'EPSG','8732','Vertical offset file','ch_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Che 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14676','grid_transformation','EPSG','9560','EPSG','1286','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9561','ODN height to EVRF2019 height (2)','Determined at 35 points, SD 0.012m. Offset: mean -0.178m, minimum -0.199m, maximum -0.159m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5701','EPSG','9389',0.024,'EPSG','8732','Vertical offset file','gb_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Gbr 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14677','grid_transformation','EPSG','9561','EPSG','2792','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9563','Ostend height to EVRF2019 height (2)','Determined at 39 points, SD 0.021m. Offset: mean -2.312m, minimum -2.362m, maximum -2.275m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5710','EPSG','9389',0.042,'EPSG','8732','Vertical offset file','be_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bel 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14678','grid_transformation','EPSG','9563','EPSG','1347','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9564','Ostend height to EVRF2019 mean-tide height (2)','Determined at 39 points, SD 0.020m. Offset: mean -2.320m, minimum -2.370m, maximum -2.285m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5710','EPSG','9390',0.04,'EPSG','8732','Vertical offset file','be_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bel 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14679','grid_transformation','EPSG','9564','EPSG','1347','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9565','SVS2010 height to EVRF2019 height (2)','Determined at 65 points, SD 0.003m. Offset: mean -0.259m, minimum -0.265m, maximum -0.251m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','8690','EPSG','9389',0.006,'EPSG','8732','Vertical offset file','si_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Svn 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14680','grid_transformation','EPSG','9565','EPSG','3307','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9566','SVS2010 height to EVRF2019 mean-tide height (2)','Determined at 65 points, SD 0.004m. Offset: mean -0.290m, minimum -0.295m, maximum -0.280m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','8690','EPSG','9390',0.008,'EPSG','8732','Vertical offset file','si_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Svn 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14681','grid_transformation','EPSG','9566','EPSG','3307','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9567','Trieste height to EVRF2019 height (2)','Determined at 46 points, SD 0.021m. Offset: mean -0.551m, minimum -0.606m, maximum -0.490m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5195','EPSG','9389',0.042,'EPSG','8732','Vertical offset file','mk_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Mkd 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14668','grid_transformation','EPSG','9567','EPSG','1148','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9568','Trieste height to EVRF2019 mean-tide height (2)','Determined at 46 points, SD 0.022m. Offset: mean -0.606m, minimum -0.662m, maximum -0.548m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5195','EPSG','9390',0.044,'EPSG','8732','Vertical offset file','mk_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Mkd 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14667','grid_transformation','EPSG','9568','EPSG','1148','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9569','Trieste height to EVRF2019 height (3)','Determined at 10 points, SD 0.006m. Offset: mean -0.336m, minimum -0.346m, maximum -0.326m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5195','EPSG','9389',0.012,'EPSG','8732','Vertical offset file','ba_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bih 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14666','grid_transformation','EPSG','9569','EPSG','1050','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9570','Trieste height to EVRF2019 mean-tide height (3)','Determined at 10 points, SD 0.005m. Offset: mean -0.377m, minimum -0.386m, maximum -0.368m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5195','EPSG','9390',0.01,'EPSG','8732','Vertical offset file','ba_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bih 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14665','grid_transformation','EPSG','9570','EPSG','1050','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9571','Baltic 1982 height to EVRF2019 height (1)','Determined at 58 points, SD 0.024m. Offset: mean 0.228m, minimum 0.167m, maximum 0.277m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5786','EPSG','9389',0.048,'EPSG','8732','Vertical offset file','bgalt_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bgr 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14628','grid_transformation','EPSG','9571','EPSG','3224','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9572','Baltic 1982 height to EVRF2019 mean-tide height (1)','Determined at 58 points, SD 0.021m. Offset: mean 0.180m, minimum 0.123m, maximum 0.228m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5786','EPSG','9390',0.042,'EPSG','8732','Vertical offset file','bgalt_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bgr 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14781','grid_transformation','EPSG','9572','EPSG','3224','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9573','N2000 height to EVRF2019 height (1)','Determined at 191 points, SD 0.002m. Offset: mean 0.002m, minimum -0.005m, maximum 0.007m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','3900','EPSG','9389',0.004,'EPSG','8732','Vertical offset file','fi_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Fin 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14664','grid_transformation','EPSG','9573','EPSG','3333','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9574','N2000 height to EVRF2019 mean-tide height (1)','Determined at 191 points, SD 0.012m. Offset: mean 0.054m, minimum 0.034m, maximum 0.079m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','3900','EPSG','9390',0.024,'EPSG','8732','Vertical offset file','fi_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Fin 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14663','grid_transformation','EPSG','9574','EPSG','3333','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9575','NGF-IGN69 height to EVRF2019 height (1)','Determined at 1228 points, SD 0.054m. Offset: mean -0.539m, minimum -0.651m, maximum -0.380m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5720','EPSG','9389',0.108,'EPSG','8732','Vertical offset file','fr_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Fra 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14662','grid_transformation','EPSG','9575','EPSG','1326','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9576','NGF-IGN69 height to EVRF2019 mean-tide height (1)','Determined at 1228 points, SD 0.043m. Offset: mean -0.561m, minimum -0.658m, maximum -0.430m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5720','EPSG','9390',0.086,'EPSG','8732','Vertical offset file','fr_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Fra 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14682','grid_transformation','EPSG','9576','EPSG','1326','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9577','EOMA height 1980 to EVRF2019 height (1)','Determined at 35 points, SD 0.003m. Offset: mean 0.163m, minimum 0.156m, maximum 0.171m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5787','EPSG','9389',0.006,'EPSG','8732','Vertical offset file','hu_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Hun 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14844','grid_transformation','EPSG','9577','EPSG','1119','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9578','EOMA 1980 height to EVRF2019 mean-tide height (1)','Determined at 35 points, SD 0.005m. Offset: mean 0.138m, minimum 0.127m, maximum 0.149m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5787','EPSG','9390',0.01,'EPSG','8732','Vertical offset file','hu_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Hun 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14686','grid_transformation','EPSG','9578','EPSG','1119','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9579','Latvia 2000 height to EVRF2019 height (1)','Determined at 134 points, SD 0.003m. Offset: mean 0.009m, minimum 0.000m, maximum 0.019m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','7700','EPSG','9389',0.006,'EPSG','8732','Vertical offset file','lv_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Lva 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14687','grid_transformation','EPSG','9579','EPSG','3268','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9580','Latvia 2000 height to EVRF2019 mean-tide height (1)','Determined at 134 points, SD 0.004m. Offset: mean 0.031m, minimum 0.025m, maximum 0.045m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','7700','EPSG','9390',0.008,'EPSG','8732','Vertical offset file','lv_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Lva 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14780','grid_transformation','EPSG','9580','EPSG','3268','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9581','NAP height to EVRF2019 height (1)','Determined at 1095 points, SD 0.008m. Offset: mean 0.021m, minimum 0.009m, maximum 0.055m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5709','EPSG','9389',0.016,'EPSG','8732','Vertical offset file','nl_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Nld 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14689','grid_transformation','EPSG','9581','EPSG','1275','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9582','NAP height to EVRF2019 mean-tide height (1)','Determined at 1095 points, SD 0.006m. Offset: mean 0.021m, minimum 0.008m, maximum 0.047m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5709','EPSG','9390',0.012,'EPSG','8732','Vertical offset file','nl_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Nld 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14779','grid_transformation','EPSG','9582','EPSG','1275','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9583','Constanta height to EVRF2019 height (1)','Determined at 96 points, SD 0.006m. Offset: mean 0.050m, minimum 0.029m, maximum 0.063m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5781','EPSG','9389',0.12,'EPSG','8732','Vertical offset file','ro_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Rou 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14693','grid_transformation','EPSG','9583','EPSG','3295','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9584','ETRS89 to ETRS89 + Stornoway height (2)','Reversible alternative to ETRS89 to Stornoway height (2) (code 7715).','EPSG','1097','Geog3D to Geog2D+GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','9428',0.011,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,'EPSG','4258','OS-UK Heb 2015',0);
INSERT INTO "usage" VALUES('EPSG','15098','grid_transformation','EPSG','9584','EPSG','2799','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9585','ETRS89 to ETRS89 + St. Marys height (2)','Reversible alternative to ETRS89 to St. Marys height (2) (code 7716).','EPSG','1097','Geog3D to Geog2D+GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','9430',0.01,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,'EPSG','4258','OS-UK Scilly 2015',0);
INSERT INTO "usage" VALUES('EPSG','14518','grid_transformation','EPSG','9585','EPSG','2802','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9586','ETRS89 to ETRS89 + ODN Orkney height (2)','Reversible alternative to ETRS89 to ODN Orkney height (2) (code 7712).','EPSG','1097','Geog3D to Geog2D+GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','9426',0.017,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,'EPSG','4258','OS-UK Ork 2015',0);
INSERT INTO "usage" VALUES('EPSG','14516','grid_transformation','EPSG','9586','EPSG','2793','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9587','ETRS89 to ETRS89 + ODN height (2)','Reversible alternative to ETRS89 to ODN height (2) (code 7711).','EPSG','1097','Geog3D to Geog2D+GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','9424',0.008,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,'EPSG','4258','OS-UK Gbr 2015',0);
INSERT INTO "usage" VALUES('EPSG','15097','grid_transformation','EPSG','9587','EPSG','2792','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9588','ETRS89 to ETRS89 + ODN (Offshore) height (1)','Reversible alternative to ETRS89 to ODN (Offshore) height (1) (code 7713).','EPSG','1097','Geog3D to Geog2D+GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','9425',0.02,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,'EPSG','4258','OS-UK Off 2015',0);
INSERT INTO "usage" VALUES('EPSG','14511','grid_transformation','EPSG','9588','EPSG','4391','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9589','ETRS89 to ETRS89 + Lerwick height (2)','Reversible alternative to ETRS89 to Lerwick height (2) (code 7714).','EPSG','1097','Geog3D to Geog2D+GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','9427',0.018,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,'EPSG','4258','OS -UK Shet 2015',0);
INSERT INTO "usage" VALUES('EPSG','15083','grid_transformation','EPSG','9589','EPSG','2795','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9590','ETRS89 to ETRS89 + Douglas height (2)','Reversible alternative to ETRS89 to Douglas height (2) (code 7717).','EPSG','1097','Geog3D to Geog2D+GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','9429',0.03,'EPSG','8666','Geoid (height correction) model file','OSTN15_OSGM15_GB.txt',NULL,NULL,NULL,NULL,'EPSG','4258','OS-UK Man 2015',0);
INSERT INTO "usage" VALUES('EPSG','15082','grid_transformation','EPSG','9590','EPSG','2803','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9591','ETRS89 to ETRS89 + Malin Head height (2)','Reversible alternative to ETRS89 to Malin Head height (2) (code 7959).','EPSG','1096','Geog3D to Geog2D+GravityRelatedHeight (OSGM15-Ire)','EPSG','4937','EPSG','9449',0.023,'EPSG','8666','Geoid (height correction) model file','OSGM15_Malin.gri',NULL,NULL,NULL,NULL,'EPSG','4258','OS-Ire 2015',0);
INSERT INTO "usage" VALUES('EPSG','15081','grid_transformation','EPSG','9591','EPSG','1305','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9592','ETRS89 to ETRS89 + Belfast height (2)','Reversible alternative to ETRS89 to Belfast height (2) (code 7958).','EPSG','1096','Geog3D to Geog2D+GravityRelatedHeight (OSGM15-Ire)','EPSG','4937','EPSG','9450',0.014,'EPSG','8666','Geoid (height correction) model file','OSGM15_Belfast.gri',NULL,NULL,NULL,NULL,'EPSG','4258','OS-UK NI 2015',0);
INSERT INTO "usage" VALUES('EPSG','14504','grid_transformation','EPSG','9592','EPSG','2530','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9593','ETRS89 to ETRS89 + NN2000 height (1)','Reversible alternative to ETRS89 to NN2000 height (1) (code 9485).','EPSG','1093','Geog3D to Geog2D+GravityRelatedHeight (Gravsoft)','EPSG','4937','EPSG','5942',0.02,'EPSG','8666','Geoid (height correction) model file','HREF2018B_NN2000_EUREF89.bin',NULL,NULL,NULL,NULL,'EPSG','4258','SK-Nor 2018',0);
INSERT INTO "usage" VALUES('EPSG','14465','grid_transformation','EPSG','9593','EPSG','1352','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9594','ETRS89 to ETRS89 + NN54 height (1)','Reversible alternative to ETRS89 to NN54 height (1) (code 9484).','EPSG','1093','Geog3D to Geog2D+GravityRelatedHeight (Gravsoft)','EPSG','4937','EPSG','6144',0.02,'EPSG','8666','Geoid (height correction) model file','href2008a.bin',NULL,NULL,NULL,NULL,'EPSG','4258','SK-Nor 2008',0);
INSERT INTO "usage" VALUES('EPSG','14464','grid_transformation','EPSG','9594','EPSG','1352','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9595','NAD83(2011) to NAD83(2011) + NAVD88 height (3)','Reversible alternative to NAD83(2011) to NAVD88 height (3) (code 9229). Uses Geoid18 hybrid model. Replaces 12B model. See information source for further information.','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','6319','EPSG','6349',0.015,'EPSG','8666','Geoid (height correction) model file','g2018u0.bin',NULL,NULL,NULL,NULL,'EPSG','6318','NGS-US Conus 18',0);
INSERT INTO "usage" VALUES('EPSG','14853','grid_transformation','EPSG','9595','EPSG','1323','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9596','NAD83(2011) to NAD83(2011) + NAVD88 height (2)','Reversible alternative to NAD83(2011) to NAVD88 height (2) (code 6327).','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','6319','EPSG','6349',0.02,'EPSG','8666','Geoid (height correction) model file','g2012ba0.bin',NULL,NULL,NULL,NULL,'EPSG','6318','NGS-US AK',0);
INSERT INTO "usage" VALUES('EPSG','14456','grid_transformation','EPSG','9596','EPSG','1330','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9597','ETRS89 to ETRS89 + NAP height (2)','Reversible alternative to ETRS89 to NAP height (2) (code 9283).','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','4937','EPSG','9286',0.001,'EPSG','8666','Geoid (height correction) model file','nlgeo2018.gtx',NULL,NULL,NULL,NULL,'EPSG','4258','NSGI-Nld 2018',0);
INSERT INTO "usage" VALUES('EPSG','14455','grid_transformation','EPSG','9597','EPSG','1275','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9598','GR96 to GR96 + GVR2000 height (1)','Reversible alternative to GR96 to GVR2000 height (1) (code 8268). File is also available in NOAA VDatum format (ggeoid2000.gtx) and GeoTIFF format (ggeoid2000.tif). ','EPSG','1093','Geog3D to Geog2D+GravityRelatedHeight (Gravsoft)','EPSG','4909','EPSG','8349',0.1,'EPSG','8666','Geoid (height correction) model file','gr2000g.gri',NULL,NULL,NULL,NULL,'EPSG','4747','SDFE-Grl',0);
INSERT INTO "usage" VALUES('EPSG','15074','grid_transformation','EPSG','9598','EPSG','4461','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9599','GR96 to GR96 + GVR2016 height (1)','Reversible alternative to GR96 to GVR2016 height (1) (code 8269). File is also available in NOAA VDatum format (ggeoid2016.gtx) and GeoTIFF format (ggeoid2016.tif).','EPSG','1093','Geog3D to Geog2D+GravityRelatedHeight (Gravsoft)','EPSG','4909','EPSG','8350',0.1,'EPSG','8666','Geoid (height correction) model file','ggeoid16.gri',NULL,NULL,NULL,NULL,'EPSG','4747','SDFE-Grl',0);
INSERT INTO "usage" VALUES('EPSG','15075','grid_transformation','EPSG','9599','EPSG','4454','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9600','ETRS89 to ETRS89 + EVRF2000 Austria height (1)','Reversible alternative to ETRS89 to EVRF2000 Austria height (1) (code 9276). Austrian Geoid 2008. Accuracy 5cm (1 sigma). The transformation is implemented in BEV-Transformator.','EPSG','1089','Geog3D to Geog2D+GravityRelatedHeight (BEV AT)','EPSG','4937','EPSG','9500',0.05,'EPSG','8666','Geoid (height correction) model file','GEOID_GRS80_Oesterreich.csv',NULL,NULL,NULL,NULL,'EPSG','4258','BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','14428','grid_transformation','EPSG','9600','EPSG','1037','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9601','MGI to MGI + EVRF2000 Austria height (1)','Reversible alternative to MGI to EVRF2000 Austria height (1) (code 9277). Austrian Geoid 2008 (Bessel ellipsoid). Accuracy 5cm (1 sigma). The transformation is implemented in BEV-Transformator.','EPSG','1089','Geog3D to Geog2D+GravityRelatedHeight (BEV AT)','EPSG','9267','EPSG','9501',0.05,'EPSG','8666','Geoid (height correction) model file','GEOID_BESSEL_Oesterreich.csv',NULL,NULL,NULL,NULL,'EPSG','4312','BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','14430','grid_transformation','EPSG','9601','EPSG','1037','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9602','CIGD11 to CIGD11 + CBVD61 height (ft) (1)','Reversible alternative to CIGD11 to CBVD61 height (ft) (1) (code 6140). Care: source CRS heights are in metres, transformation file parameter values and target CRS heights are in feet.','EPSG','1091','Geog3D to Geog2D+GravityRelatedHeight (CI)','EPSG','6134','EPSG','9502',0.03,'EPSG','8666','Geoid (height correction) model file','CBGM0811.TXT',NULL,NULL,NULL,NULL,'EPSG','6135','LSD-Cym',0);
INSERT INTO "usage" VALUES('EPSG','15103','grid_transformation','EPSG','9602','EPSG','3207','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9603','CIGD11 to CIGD11 + GCVD54 height (ft) (1)','Reversible alternative to CIGD11 to GCVD54 height (ft) (1) (code 6138). Care: source CRS heights are in metres, transformation file parameter values and target CRS heights are in feet.','EPSG','1091','Geog3D to Geog2D+GravityRelatedHeight (CI)','EPSG','6134','EPSG','9503',0.03,'EPSG','8666','Geoid (height correction) model file','GCGM0811.TXT',NULL,NULL,NULL,NULL,'EPSG','6135','LSD-Cym',0);
INSERT INTO "usage" VALUES('EPSG','15067','grid_transformation','EPSG','9603','EPSG','3185','EPSG','1026');
INSERT INTO "grid_transformation" VALUES('EPSG','9604','CIGD11 to CIGD11 + LCVD61 height (ft) (1)','Reversible alternative to CIGD11 to LCVD61 height (ft) (1) (code 6139). Care: source CRS heights are in metres, transformation file parameter values and target CRS heights are in feet.','EPSG','1091','Geog3D to Geog2D+GravityRelatedHeight (CI)','EPSG','6134','EPSG','9504',0.03,'EPSG','8666','Geoid (height correction) model file','LCGM0811.TXT',NULL,NULL,NULL,NULL,'EPSG','6135','LSD-Cym',0);
INSERT INTO "usage" VALUES('EPSG','15069','grid_transformation','EPSG','9604','EPSG','4121','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9605','ETRS89 to ETRS89 + Alicante height (1)','Reversible alternative to ETRS89 to Alicante height (1) (code 9410).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9505',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,'EPSG','4258','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','15070','grid_transformation','EPSG','9605','EPSG','2366','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9606','ETRS89 to ETRS89 + Ceuta 2 height (1)','Reversible alternative to ETRS89 to Ceuta 2 height (1) (code 9414).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9506',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,'EPSG','4258','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14438','grid_transformation','EPSG','9606','EPSG','4590','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9607','ETRS89 to ETRS89 + Ibiza height (1)','Reversible alternative to ETRS89 to Ibiza height (1) (code 9413).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9507',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,'EPSG','4258','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14439','grid_transformation','EPSG','9607','EPSG','4604','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9608','ETRS89 to ETRS89 + Mallorca height (1)','Reversible alternative to ETRS89 to+ Mallorca height (1) (code 9411).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9508',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,'EPSG','4258','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14281','grid_transformation','EPSG','9608','EPSG','4602','EPSG','1026');
INSERT INTO "grid_transformation" VALUES('EPSG','9609','ETRS89 to ETRS89 + Menorca height (1)','Reversible alternative to ETRS89 to Menorca height (1) (code 9412).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4937','EPSG','9509',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP.txt',NULL,NULL,NULL,NULL,'EPSG','4258','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14441','grid_transformation','EPSG','9609','EPSG','4603','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9610','REGCAN95 to REGCAN95 + El Hierro height (1)','Reversible alternative to REGCAN95 to El Hierro height (1) (code 9421).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9510',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,'EPSG','4081','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','15102','grid_transformation','EPSG','9610','EPSG','4597','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9611','REGCAN95 to REGCAN95 + Fuerteventura height (1)','Reversible alternative to REGCAN95 to Fuerteventura height (1) (code 9416).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9511',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,'EPSG','4081','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14443','grid_transformation','EPSG','9611','EPSG','4592','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9612','REGCAN95 to REGCAN95 + Gran Canaria height (1)','Reversible alternative to REGCAN95 to Gran Canaria height (1) (code 9417).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9512',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,'EPSG','4081','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','15071','grid_transformation','EPSG','9612','EPSG','4593','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9613','REGCAN95 to REGCAN95 + La Gomera height (1)','Reversible alternative to REGCAN95 to La Gomera height (1) (code 9419).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9513',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,'EPSG','4081','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14445','grid_transformation','EPSG','9613','EPSG','4595','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9614','REGCAN95 to REGCAN95 + La Palma height (1)','Reversible alternative to REGCAN95 to La Palma height (1) (code 9420).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9514',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,'EPSG','4081','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14446','grid_transformation','EPSG','9614','EPSG','4596','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9615','REGCAN95 to REGCAN95 + Lanzarote height (1)','Reversible alternative to REGCAN95 to Lanzarote height (1) (code 9415).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9515',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,'EPSG','4081','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','15072','grid_transformation','EPSG','9615','EPSG','4591','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9616','REGCAN95 to REGCAN95 + Tenerife height (1)','Reversible alternative to REGCAN95 to Tenerife height (1) (code 9418).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4080','EPSG','9516',0.05,'EPSG','8666','Geoid (height correction) model file','EGM08_REDNAP_Canarias.txt',NULL,NULL,NULL,NULL,'EPSG','4081','IGN-Esp 2008',0);
INSERT INTO "usage" VALUES('EPSG','14448','grid_transformation','EPSG','9616','EPSG','4594','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9617','SHGD2015 to SHGD2015 + SHVD2015 height (1)','Reversible alternative to SHGD2015 to SHVD2015 height (1) (code 7891).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','7885','EPSG','7956',0.0,'EPSG','8666','Geoid (height correction) model file','Und_min2.5x2.5_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,'EPSG','7886','ENRD-Shn Hel',0);
INSERT INTO "usage" VALUES('EPSG','14449','grid_transformation','EPSG','9617','EPSG','3183','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9618','WGS 84 to WGS 84 + EGM2008 height (2)','Reversible alternative to WGS 84 to EGM2008 height (2) (code 3859).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4979','EPSG','9518',0.5,'EPSG','8666','Geoid (height correction) model file','Und_min1x1_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,'EPSG','4326','NGA-World',0);
INSERT INTO "usage" VALUES('EPSG','15073','grid_transformation','EPSG','9618','EPSG','1262','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9619','FEH2010 to FEH2010 + FCSVR10 height (1)','Reversible alternative to FEH2010 to FCSVR10 height (1) (code 5626).','EPSG','1093','Geog3D to Geog2D+GravityRelatedHeight (Gravsoft)','EPSG','5592','EPSG','9519',0.1,'EPSG','8666','Geoid (height correction) model file','fehmarn_geoid10.gri',NULL,NULL,NULL,NULL,'EPSG','5593','FEM-Dnk-Deu Feh',0);
INSERT INTO "usage" VALUES('EPSG','14326','grid_transformation','EPSG','9619','EPSG','3890','EPSG','1139');
INSERT INTO "grid_transformation" VALUES('EPSG','9620','KSA-GRF17 to KSA-GRF17 + KSA-VRF14 height (1)','Reversible alternative to KSA-GRF17 to KSA-VRF14 height (1) (code 9355). File also available in IGN2009 format. To access KSA-GEOID17 contact GCS by <NAME_EMAIL>. ','EPSG','1093','Geog3D to Geog2D+GravityRelatedHeight (Gravsoft)','EPSG','9332','EPSG','9520',0.1,'EPSG','8666','Geoid (height correction) model file','KSA-GEOID17.gra',NULL,NULL,NULL,NULL,'EPSG','9333','GCS-Sau',0);
INSERT INTO "usage" VALUES('EPSG','14453','grid_transformation','EPSG','9620','EPSG','3303','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9621','POSGAR 2007 to POSGAR 2007 + SRVN16 height (1)','Reversible alternative to POSGAR 2007 to SRVN16 height (1) (code 9256). Uses geoid model Ar16. See information source for more information.','EPSG','1093','Geog3D to Geog2D+GravityRelatedHeight (Gravsoft)','EPSG','5342','EPSG','9521',0.05,'EPSG','8666','Geoid (height correction) model file','GEOIDE-Ar16.gri',NULL,NULL,NULL,NULL,'EPSG','5340','IGN-Arg',0);
INSERT INTO "usage" VALUES('EPSG','15056','grid_transformation','EPSG','9621','EPSG','4573','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9622','NAD83(2011) to NAD83(2011) + PRVD02 height (2)','Reversible alternative to NAD83(2011) to PRVD02 height (2) (code 9230). Uses Geoid18 hybrid model. Replaces 12B model. See information source for further information.','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','6319','EPSG','9522',0.015,'EPSG','8666','Geoid (height correction) model file','g2018p0.bin',NULL,NULL,NULL,NULL,'EPSG','6318','NGS-Pri 18',0);
INSERT INTO "usage" VALUES('EPSG','14458','grid_transformation','EPSG','9622','EPSG','3294','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9623','NAD83(2011) to NAD83(2011) + VIVD09 height (2)','Reversible alternative to NAD83(2011) to VIVD09 height (2) (code 9231). Uses Geoid18 hybrid model. Replaces 12B model. See information source for further information.','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','6319','EPSG','9523',0.015,'EPSG','8666','Geoid (height correction) model file','g2018p0.bin',NULL,NULL,NULL,NULL,'EPSG','6318','NGS-Vir 18',0);
INSERT INTO "usage" VALUES('EPSG','14459','grid_transformation','EPSG','9623','EPSG','3330','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9624','NAD83(MA11) to NAD83(MA11) + GUVD04 height (1)','Reversible alternative to NAD83(MA11) to GUVD04 height (1) (code 7648). Uses Geoid12B hybrid model. See information source for further information.','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','6324','EPSG','9524',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bg0.bin',NULL,NULL,NULL,NULL,'EPSG','6325','NGS-Gum 12B',0);
INSERT INTO "usage" VALUES('EPSG','14460','grid_transformation','EPSG','9624','EPSG','3255','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9625','NAD83(MA11) to NAD83(MA11) + NMVD03 height (1)','Reversible alternative to NAD83(MA11) to NMVD03 height (1) (code 7649). Uses Geoid12B hybrid model. See information source for further information.','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','6324','EPSG','9525',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bg0.bin',NULL,NULL,NULL,NULL,'EPSG','6325','NGS-Mnp 12B',0);
INSERT INTO "usage" VALUES('EPSG','14461','grid_transformation','EPSG','9625','EPSG','4171','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9626','NAD83(PA11) to NAD83(PA11) + ASVD02 height (1)','Reversible alternative to NAD83(PA11) to ASVD02 height (1) (code 7650). Uses Geoid12B hybrid model. See information source for further information.','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','6321','EPSG','9526',0.02,'EPSG','8666','Geoid (height correction) model file','g2012bs0.bin',NULL,NULL,NULL,NULL,'EPSG','6322','NGS-Asm 12B',0);
INSERT INTO "usage" VALUES('EPSG','14463','grid_transformation','EPSG','9626','EPSG','2288','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9627','NZGD2000 to NZGD2000 + NZVD2009 height (2)','Reversible alternative to NZGD2000 to NZVD2009 height (2) (code 9325).','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','4959','EPSG','9527',0.1,'EPSG','8666','Geoid (height correction) model file','nzgeoid2009.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ 2009 gtx',0);
INSERT INTO "usage" VALUES('EPSG','14466','grid_transformation','EPSG','9627','EPSG','1175','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9628','NZGD2000 to NZGD2000 + NZVD2016 height (2)','Reversible alternative to NZGD2000 to NZVD2016 height (2) (code 9326).','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','4959','EPSG','9528',0.1,'EPSG','8666','Geoid (height correction) model file','nzgeoid2016.gtx',NULL,NULL,NULL,NULL,'EPSG','4167','LINZ-NZ 2016 gtx',0);
INSERT INTO "usage" VALUES('EPSG','14467','grid_transformation','EPSG','9628','EPSG','1175','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9629','SRGI2013 to SRGI2013 + INAGeoid2020 height (1)','Reversible alternative to SRGI2013 to INAGeoid2020 height (1) (code 9305).','EPSG','1088','Geog3D to Geog2D+GravityRelatedHeight (gtx)','EPSG','9469','EPSG','9529',0.0,'EPSG','8666','Geoid (height correction) model file','INAGEOID20.gtx',NULL,NULL,NULL,NULL,'EPSG','9470','BIG-Idn INAGeoid20',0);
INSERT INTO "usage" VALUES('EPSG','14468','grid_transformation','EPSG','9629','EPSG','1122','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9630','RGFG95 to RGFG95 + NGG1977 height (1)','Reversible alternative to RGFG95 to NGG1977 height (1) (code 10011).','EPSG','1094','Geog3D to Geog2D+GravityRelatedHeight (IGN1997)','EPSG','4967','EPSG','9530',998.0,'EPSG','8666','Geoid (height correction) model file','ggguy00.txt',NULL,NULL,NULL,NULL,'EPSG','4624','IGN Guf',0);
INSERT INTO "usage" VALUES('EPSG','14469','grid_transformation','EPSG','9630','EPSG','3146','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9631','RGAF09 to RGAF09 + Guadeloupe 1988 height (2)','Reversible alternative to RGAF09 to Guadeloupe 1988 height (2) (code 9133).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9531',0.05,'EPSG','8666','Geoid (height correction) model file','RAGTBT2016.mnt',NULL,NULL,NULL,NULL,'EPSG','5489','IGN Glp GT 2016',0);
INSERT INTO "usage" VALUES('EPSG','14470','grid_transformation','EPSG','9631','EPSG','2892','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9632','RGAF09 to RGAF09 + IGN 1988 LS height (2)','Reversible alternative to RGAF09 to IGN 1988 LS height (2) (code 9134).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9532',0.1,'EPSG','8666','Geoid (height correction) model file','RALS2016.mnt',NULL,NULL,NULL,NULL,'EPSG','5489','IGN Glp LSt 2016',0);
INSERT INTO "usage" VALUES('EPSG','14472','grid_transformation','EPSG','9632','EPSG','2895','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9633','RGAF09 to RGAF09 + IGN 1988 MG height (2)','Reversible alternative to RGAF09 to IGN 1988 MG height (2) (code 9135).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9533',0.1,'EPSG','8666','Geoid (height correction) model file','RAMG2016.mnt',NULL,NULL,NULL,NULL,'EPSG','5489','IGN Glp MG 2016',0);
INSERT INTO "usage" VALUES('EPSG','15076','grid_transformation','EPSG','9633','EPSG','2894','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9634','RGAF09 to RGAF09 + IGN 1988 SB height (2)','Reversible alternative to RGAF09 to IGN 1988 SB height (2) (code 9187).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9534',0.1,'EPSG','8666','Geoid (height correction) model file','gg10_sbv2.mnt',NULL,NULL,NULL,NULL,'EPSG','5489','IGN Glp StB',0);
INSERT INTO "usage" VALUES('EPSG','15077','grid_transformation','EPSG','9634','EPSG','2891','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9635','RGAF09 to RGAF09 + IGN 1988 SM height (2)','Reversible alternative to RGAF09 to IGN 1988 SM height (2) (code 9188).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9535',0.1,'EPSG','8666','Geoid (height correction) model file','gg10_smv2.mnt',NULL,NULL,NULL,NULL,'EPSG','5489','IGN Glp StM',0);
INSERT INTO "usage" VALUES('EPSG','14482','grid_transformation','EPSG','9635','EPSG','2890','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9636','RGAF09 to IGN 2008 LD height (1)','Reversible alternative to RGAF09 to IGN 2008 LD height (1) (code 9131).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9536',0.2,'EPSG','8666','Geoid (height correction) model file','RALD2016.mnt',NULL,NULL,NULL,NULL,'EPSG','5489','IGN-Glp Des',0);
INSERT INTO "usage" VALUES('EPSG','14485','grid_transformation','EPSG','9636','EPSG','2893','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9637','RGAF09 to RGAF09 + Martinique 1987 height (2)','Reversible alternative to RGAF09 to Martinique 1987 height (2) (code 9136).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','5488','EPSG','9537',0.05,'EPSG','8666','Geoid (height correction) model file','RAMART2016.mnt',NULL,NULL,NULL,NULL,'EPSG','5489','IGN Mtq 2016',0);
INSERT INTO "usage" VALUES('EPSG','14487','grid_transformation','EPSG','9637','EPSG','3276','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9638','RGF93 v2 to RGF93 v2 + NGF-IGN69 height (3)','Reversible alternative to RGF93 v2 to NGF-IGN69 height (3) (code 8885). May also be found with filename RAF18.tac.','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','9776','EPSG','9538',0.01,'EPSG','8666','Geoid (height correction) model file','RAF18.mnt',NULL,NULL,NULL,NULL,'EPSG','9777','IGN Fra 18',0);
INSERT INTO "usage" VALUES('EPSG','15078','grid_transformation','EPSG','9638','EPSG','1326','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9639','RGF93 v2 to RGF93 v2 + NGF-IGN78 height (2)','Reversible alternative to RGF93 v2 to NGF-IGN78 height (2) (code 8372).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','9776','EPSG','9539',0.05,'EPSG','8666','Geoid (height correction) model file','RAC09.mnt',NULL,NULL,NULL,NULL,'EPSG','9777','IGN Fra Cor 09',0);
INSERT INTO "usage" VALUES('EPSG','15079','grid_transformation','EPSG','9639','EPSG','1327','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9640','RGNC91-93 to RGNC91-93 + NGNC08 height (1)','Reversible alternative to RGNC91-93 to NGNC08 height (1) (code 9352).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','4907','EPSG','9540',0.03,'EPSG','8666','Geoid (height correction) model file','Ranc08_Circe.mnt',NULL,NULL,NULL,NULL,'EPSG','4749','BGN-Ncl RANC08',0);
INSERT INTO "usage" VALUES('EPSG','14496','grid_transformation','EPSG','9640','EPSG','3430','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9641','RGSPM06 to RGSPM06 + Danger 1950 height (2)','Reversible alternative to RGSPM06 to Danger 1950 height (2) (code 9228).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','4466','EPSG','9541',0.05,'EPSG','8666','Geoid (height correction) model file','RASPM2018.mnt',NULL,NULL,NULL,NULL,'EPSG','4463','IGN-SPM',0);
INSERT INTO "usage" VALUES('EPSG','15080','grid_transformation','EPSG','9641','EPSG','3299','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9642','RRAF 1991 to RRAF 1991 + IGN 2008 LD height (1)','Reversible alternative to RRAF 1991 to IGN 2008 LD height (1) (code 9132).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','4557','EPSG','9542',0.2,'EPSG','8666','Geoid (height correction) model file','RALDW842016.mnt',NULL,NULL,NULL,NULL,'EPSG','4558','IGN Glp Des',0);
INSERT INTO "usage" VALUES('EPSG','14502','grid_transformation','EPSG','9642','EPSG','2893','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9643','ITRF2005 to ITRF2005 + SA LLD height (1)','Reversible alternative to ITRF2005 to SA LLD height (1) (code 9280).','EPSG','1098','Geog3D to Geog2D+GravityRelatedHeight (SA 2010)','EPSG','7910','EPSG','9543',0.07,'EPSG','8666','Geoid (height correction) model file','SAGEOID2010.dat',NULL,NULL,NULL,NULL,'EPSG','8998','NGI-Zaf',0);
INSERT INTO "usage" VALUES('EPSG','14523','grid_transformation','EPSG','9643','EPSG','3309','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9644','NAD83(CSRS)v6 to NAD83(CSRS)v6 + CGVD2013(CGG2013a) height (1)','Reversible alternative to NAD83(CSRS)v6 to CGVD2013(CGG2013a) height (1) (code 9247).','EPSG','1090','Geog3D to Geog2D+GravityRelatedHeight (CGG 2013)','EPSG','8251','EPSG','9544',0.03,'EPSG','8666','Geoid (height correction) model file','CGG2013n83a.byn',NULL,NULL,NULL,NULL,'EPSG','8252','NRC Can CGG2013a',0);
INSERT INTO "usage" VALUES('EPSG','15099','grid_transformation','EPSG','9644','EPSG','1061','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9645','Constanta height to EVRF2019 mean-tide height (1)','Determined at 96 points, SD 0.010m. Offset: mean 0.015m, minimum -0.006m, maximum 0.040m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5781','EPSG','9390',0.02,'EPSG','8732','Vertical offset file','ro_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Rou 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14692','grid_transformation','EPSG','9645','EPSG','3295','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9646','Alicante height to EVRF2019 height (1)','Determined at 155 points, SD 0.041m. Offset: mean -0.427m, minimum -0.555m, maximum -0.355m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5782','EPSG','9389',0.082,'EPSG','8732','Vertical offset file','es_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Esp 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14696','grid_transformation','EPSG','9646','EPSG','2366','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9647','Alicante height to EVRF2019 mean-tide height (1)','Determined at 155 points, SD 0.039m. Offset: mean -0.488m, minimum -0.603m, maximum -0.426m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5782','EPSG','9390',0.078,'EPSG','8732','Vertical offset file','es_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Esp 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14854','grid_transformation','EPSG','9647','EPSG','2366','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9648','RH2000 height to EVRF2019 height (1)','Determined at 3356 points, SD 0.003m. Offset: mean -0.003m, minimum -0.014m, maximum 0.003m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5613','EPSG','9389',0.006,'EPSG','8732','Vertical offset file','se_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Swe 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14697','grid_transformation','EPSG','9648','EPSG','3313','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9649','RH2000 height to EVRF2019 mean-tide height (1)','Determined at 3356 points, SD 0.016m. Offset: mean 0.036m, minimum 0.003m, maximum 0.071m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5613','EPSG','9390',0.032,'EPSG','8732','Vertical offset file','se_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Swe 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14778','grid_transformation','EPSG','9649','EPSG','3313','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9652','Baltic 1986 height to EVRF2019 height (1)','Determined at 300 points, SD 0.010m. Offset: mean 0.178m, minimum 0.144m, maximum 0.203m. May also use CRS 9702 (ETRF2000-PL) as interpolation CRS.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9650','EPSG','9389',0.02,'EPSG','8732','Vertical offset file','pl86_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Pol 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','15035','grid_transformation','EPSG','9652','EPSG','3293','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9653','Baltic 1986 height to EVRF2019 mean-tide height (1)','Determined at 300 points, SD 0.010m. Offset: mean 0.176m, minimum 0.133m, maximum 0.201m. May also use CRS 9702 (ETRF2000-PL) as interpolation CRS.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9650','EPSG','9390',0.02,'EPSG','8732','Vertical offset file','pl86_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Pol 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','15036','grid_transformation','EPSG','9653','EPSG','3293','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9654','EVRF2007-PL height to EVRF2019 height (1)','Determined at 319 points, SD 0.003m. Offset: mean 0.012m, minimum 0.002m, maximum 0.024m. May also use CRS 9702 (ETRF2000-PL) as interpolation CRS.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9651','EPSG','9389',0.006,'EPSG','8732','Vertical offset file','pl07_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Pol 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','15037','grid_transformation','EPSG','9654','EPSG','3293','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9655','EVRF2000-PL height to EVRF2019 mean-tide height (1)','Determined at 319 points, SD 0.006m. Offset: mean 0.011m, minimum -0.006m, maximum 0.022m. May also use CRS 9702 (ETRF2000-PL) as interpolation CRS.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9651','EPSG','9390',0.012,'EPSG','8732','Vertical offset file','pl07_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Pol 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','15038','grid_transformation','EPSG','9655','EPSG','3293','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9658','ETRF2000-PL to Baltic 1986 height (1)','For reversible alternative to this transformation see ETRF2000-PL to ETRF2000-PL + Baltic 1986 height (1) (code 9659).','EPSG','1099','Geographic3D to GravityRelatedHeight (PL txt)','EPSG','9702','EPSG','9650',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-KRON86-NH.txt',NULL,NULL,NULL,NULL,NULL,NULL,'GUGiK-Pol geoid11 KRON86',1);
INSERT INTO "usage" VALUES('EPSG','15013','grid_transformation','EPSG','9658','EPSG','3293','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9659','ETRF2000-PL to ETRF2000-PL + Baltic 1986 height (1)','Reversible alternative to ETRF2000-PL to Baltic 1986 height (1) (code 9658).','EPSG','1100','Geog3D to Geog2D+GravityRelatedHeight (PL txt)','EPSG','9702','EPSG','9656',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-KRON86-NH.txt',NULL,NULL,NULL,NULL,'EPSG','9702','GUGiK-Pol geoid11 KRON86',1);
INSERT INTO "usage" VALUES('EPSG','15106','grid_transformation','EPSG','9659','EPSG','3293','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9660','ETRF2000-PL to EVRF2007-PL height (1)','For reversible alternative to this transformation see ETRF2000-PL to ETRF2000-PL + EVRF2007-PL height (1) (code 9661).','EPSG','1099','Geographic3D to GravityRelatedHeight (PL txt)','EPSG','9702','EPSG','9651',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-EVRF2007-NH.txt',NULL,NULL,NULL,NULL,NULL,NULL,'GUGiK-Pol geoid11 EVRF07',1);
INSERT INTO "usage" VALUES('EPSG','15039','grid_transformation','EPSG','9660','EPSG','3293','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9661','ETRF2000-PL to ETRF2000-PL + EVRF2007-PL height (1)','Reversible alternative to ETRF2000-PL to EVRF2007-PL height (1) (code 9660).','EPSG','1100','Geog3D to Geog2D+GravityRelatedHeight (PL txt)','EPSG','9702','EPSG','9657',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-EVRF2007-NH.txt',NULL,NULL,NULL,NULL,'EPSG','9702','GUGiK-Pol geoid11 EVRF07',1);
INSERT INTO "usage" VALUES('EPSG','15107','grid_transformation','EPSG','9661','EPSG','3293','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9662','Baltic 1986 height to EVRF2007-PL height (1)','Gives same result as differencing quasi-geoid models geoid2011-KRON86 and geoid2011-EVRF2007 (transformations 9658 and 9660).','EPSG','1101','Vertical Offset by Grid Interpolation (PL txt)','EPSG','9650','EPSG','9651',0.04,'EPSG','8732','Vertical offset file','gugik-evrf2007.txt',NULL,NULL,NULL,NULL,'EPSG','9702','GUGiK-Pol',0);
INSERT INTO "usage" VALUES('EPSG','15052','grid_transformation','EPSG','9662','EPSG','3293','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9664','EH2000 height to EVRF2019 height (1)','Determined at 367 points, SD 0.001m. Offset: mean 0.011m, minimum 0.008m, maximum 0.013m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9663','EPSG','9389',0.002,'EPSG','8732','Vertical offset file','ee_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Est 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14748','grid_transformation','EPSG','9664','EPSG','3246','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9665','EH2000 height to EVRF2019 mean-tide height (1)','Determined at 367 points, SD 0.002m. Offset: mean 0.041m, minimum 0.037m, maximum 0.045m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9663','EPSG','9390',0.004,'EPSG','8732','Vertical offset file','ee_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Est 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14757','grid_transformation','EPSG','9665','EPSG','3246','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9667','LAS07 height to EVRF2019 height (1)','Determined at 56 points, SD 0.005m. Offset: mean 0.009m, minimum 0.001m, maximum 0.018m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9666','EPSG','9389',0.01,'EPSG','8732','Vertical offset file','lt_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Ltu 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14755','grid_transformation','EPSG','9667','EPSG','3272','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9668','LAS07 height to EVRF2019 mean-tide height (1)','Determined at 56 points, SD 0.007m. Offset: mean 0.024m, minimum 0.010m, maximum 0.036m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9666','EPSG','9390',0.014,'EPSG','8732','Vertical offset file','lt_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Ltu 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14759','grid_transformation','EPSG','9668','EPSG','3272','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9670','BGS2005 height to EVRF2019 height (1)','Determined at 59 points, SD 0.018m. Offset: mean -0.002m, minimum -0.051m, maximum 0.034m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9669','EPSG','9389',0.036,'EPSG','8732','Vertical offset file','bgneu_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bgr 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14799','grid_transformation','EPSG','9670','EPSG','3224','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9671','BGS2005 height to EVRF2019 mean-tide height (1)','Determined at 59 points, SD 0.016m. Offset: mean -0.050m, minimum -0.093m, maximum -0.019m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','9669','EPSG','9390',0.032,'EPSG','8732','Vertical offset file','bgneu_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Bgr 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14798','grid_transformation','EPSG','9671','EPSG','3224','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9689','GDA94 to WGS 84 (3)','Equivalent to concatenation of CT 8447 and null CT 8450 through GDA2020. See GDA94 to WGS 84 (2) (CT code 9688) for conformal-only alternative (i.e. without distortion modelling).','EPSG','9615','NTv2','EPSG','4283','EPSG','4326',3.0,'EPSG','8656','Latitude and longitude difference file','GDA94_GDA2020_conformal_and_distortion.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICSM-Aus Conf and Dist',0);
INSERT INTO "usage" VALUES('EPSG','14965','grid_transformation','EPSG','9689','EPSG','2575','EPSG','1275');
INSERT INTO "grid_transformation" VALUES('EPSG','9691','WGS 84 to GDA2020 (4)','Equivalent to concatenation of null CT 1150 and CT 8447 through GDA94. See WGS 84 to GDA94 (3) (CT code 9690) for conformal-only alternative (i.e. without distortion modelling).','EPSG','9615','NTv2','EPSG','4326','EPSG','7844',3.0,'EPSG','8656','Latitude and longitude difference file','GDA94_GDA2020_conformal_and_distortion.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ICSM-Aus Conf and Dist',0);
INSERT INTO "usage" VALUES('EPSG','14966','grid_transformation','EPSG','9691','EPSG','2575','EPSG','1159');
INSERT INTO "grid_transformation" VALUES('EPSG','9692','GDA2020 to AVWS height (2)','AGQG is used to realise AVWS. Uncertainties (4-8 cm across mainland Australia) given in accompanying file AGQG_uncertainty_20201120.gsb. For reversible alternative to this transformation see GDA2020 to GDA2020 + AVWS height (2) (code 9693).','EPSG','1048','Geographic3D to GravityRelatedHeight (AUSGeoid v2)','EPSG','7843','EPSG','9458',0.1,'EPSG','8666','Geoid (height correction) model file','AGQG_20201120.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus AGQG 20201120',0);
INSERT INTO "usage" VALUES('EPSG','14924','grid_transformation','EPSG','9692','EPSG','4177','EPSG','1264');
INSERT INTO "grid_transformation" VALUES('EPSG','9693','GDA2020 to GDA2020 + AVWS height (2)','Reversible alternative to GDA2020 to AVWS height (1) (code 9692). AGQG is used to realise AVWS. Uncertainties (4-8 cm across mainland Australia) given in accompanying file AGQG_uncertainty_20201120.gsb.','EPSG','1083','Geog3D to Geog2D+GravityRelatedHeight (AUSGeoidv2)','EPSG','7843','EPSG','9462',0.1,'EPSG','8666','Geoid (height correction) model file','AGQG_20201120.gsb',NULL,NULL,NULL,NULL,'EPSG','7844','GA-Aus AGQG 20201120',0);
INSERT INTO "usage" VALUES('EPSG','14967','grid_transformation','EPSG','9693','EPSG','4177','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9704','WGS 84 to WGS 84 + EGM2008 height (1)','Reversible alternative to WGS 84 to EGM2008 height (1) (code 3858).','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4979','EPSG','9518',1.0,'EPSG','8666','Geoid (height correction) model file','Und_min2.5x2.5_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,'EPSG','4326','NGA-World',0);
INSERT INTO "usage" VALUES('EPSG','15096','grid_transformation','EPSG','9704','EPSG','1262','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9706','WGS 84 to WGS 84 + MSL height (1)','Reversible alternative to WGS 84 to MSL height (1) (code 8037). Parameter values are from WGS 84 to WGS 84 + EGM2008 height (2) (CT code 9618) assuming that the EGM2008 surface equals MSL surface within the accuracy of the transformation.','EPSG','1092','Geog3D to Geog2D+GravityRelatedHeight (EGM2008)','EPSG','4979','EPSG','9705',0.5,'EPSG','8666','Geoid (height correction) model file','Und_min1x1_egm2008_isw=82_WGS84_TideFree.gz',NULL,NULL,NULL,NULL,'EPSG','4326','IOGP-World',0);
INSERT INTO "usage" VALUES('EPSG','15105','grid_transformation','EPSG','9706','EPSG','1262','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9708','WGS 84 to WGS 84 + EGM96 height (1)','Reversible alternative to WGS 84 to EGM96 height (1) (code 10084).','EPSG','1103','Geog3D to Geog2D+GravityRelatedHeight (EGM)','EPSG','4979','EPSG','9707',1.0,'EPSG','8666','Geoid (height correction) model file','WW15MGH.GRD',NULL,NULL,NULL,NULL,'EPSG','4326','NGA-World',0);
INSERT INTO "usage" VALUES('EPSG','15101','grid_transformation','EPSG','9708','EPSG','1262','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9717','ETRF2000-PL to Baltic 1986 height (1)','For reversible alternative to this transformation see ETRF2000-PL to ETRF2000-PL + Baltic 1986 height (1) (code 9718).','EPSG','1099','Geographic3D to GravityRelatedHeight (PL txt)','EPSG','9701','EPSG','9650',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-KRON86-NH.txt',NULL,NULL,NULL,NULL,NULL,NULL,'GUGiK-Pol geoid11 KRON86',0);
INSERT INTO "usage" VALUES('EPSG','15236','grid_transformation','EPSG','9717','EPSG','3293','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9718','ETRF2000-PL to ETRF2000-PL + Baltic 1986 height (1)','Reversible alternative to ETRF2000-PL to Baltic 1986 height (1) (code 9717).','EPSG','1100','Geog3D to Geog2D+GravityRelatedHeight (PL txt)','EPSG','9701','EPSG','9656',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-KRON86-NH.txt',NULL,NULL,NULL,NULL,'EPSG','9702','GUGiK-Pol geoid11 KRON86',0);
INSERT INTO "usage" VALUES('EPSG','15237','grid_transformation','EPSG','9718','EPSG','3293','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9719','ETRF2000-PL to EVRF2007-PL height (1)','For reversible alternative to this transformation see ETRF2000-PL to ETRF2000-PL + EVRF2007-PL height (1) (code 9720).','EPSG','1099','Geographic3D to GravityRelatedHeight (PL txt)','EPSG','9701','EPSG','9651',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-EVRF2007-NH.txt',NULL,NULL,NULL,NULL,NULL,NULL,'GUGiK-Pol geoid11 EVRF07',0);
INSERT INTO "usage" VALUES('EPSG','15238','grid_transformation','EPSG','9719','EPSG','3293','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9720','ETRF2000-PL to ETRF2000-PL + EVRF2007-PL height (1)','Reversible alternative to ETRF2000-PL to EVRF2007-PL height (1) (code 9719).','EPSG','1100','Geog3D to Geog2D+GravityRelatedHeight (PL txt)','EPSG','9701','EPSG','9657',0.03,'EPSG','8666','Geoid (height correction) model file','gugik-geoid2011-PL-EVRF2007-NH.txt',NULL,NULL,NULL,NULL,'EPSG','9702','GUGiK-Pol geoid11 EVRF07',0);
INSERT INTO "usage" VALUES('EPSG','15239','grid_transformation','EPSG','9720','EPSG','3293','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9727','ETRS89 to Genoa 1942 height (1)','Replaces ITALGEO99. For reversible alternative to this transformation see ETRS89 to ETRS89+ Genoa 1942 height (1) (code 9729).','EPSG','1106','Geographic3D to GravityRelatedHeight (ITAL2005)','EPSG','4937','EPSG','5214',0.035,'EPSG','8666','Geoid (height correction) model file','geo_igm_mar06.grd',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita 2005 main Sic',0);
INSERT INTO "usage" VALUES('EPSG','15332','grid_transformation','EPSG','9727','EPSG','3736','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9728','ETRS89 to Cagliari 1956 height (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + Cagliari 1956 height (1) (code 9730).','EPSG','1106','Geographic3D to GravityRelatedHeight (ITAL2005)','EPSG','4937','EPSG','9722',0.035,'EPSG','8666','Geoid (height correction) model file','geo_igm_mar06.grd',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita 2005 Sardinia',0);
INSERT INTO "usage" VALUES('EPSG','15337','grid_transformation','EPSG','9728','EPSG','2339','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9729','ETRS89 to ETRS89 + Genoa 1942 height (1)','Reversible alternative to ETRS89 to Genoa 1942 height (1) (code 9727).','EPSG','1105','Geog3D to Geog2D+GravityRelatedHeight (ITAL2005)','EPSG','4937','EPSG','9723',0.035,'EPSG','8666','Geoid (height correction) model file','geo_igm_mar06.grd',NULL,NULL,NULL,NULL,'EPSG','4258','IGM-Ita 2005 main Sic',0);
INSERT INTO "usage" VALUES('EPSG','15333','grid_transformation','EPSG','9729','EPSG','3736','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9730','ETRS89 to ETRS89 + Cagliari 1956 height (1)','Reversible alternative to ETRS89 to Cagliari 1956 height (1) (code 9728).','EPSG','1105','Geog3D to Geog2D+GravityRelatedHeight (ITAL2005)','EPSG','4937','EPSG','9725',0.035,'EPSG','8666','Geoid (height correction) model file','geo_igm_mar06.grd',NULL,NULL,NULL,NULL,'EPSG','4258','IGM-Ita 2005 Sardinia',0);
INSERT INTO "usage" VALUES('EPSG','15283','grid_transformation','EPSG','9730','EPSG','2339','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9732','Monte Mario to ED50 (1)','For the reverse transformation from ED50 to Monte Mario, iteration may be avoided by using an alternative grid file (35160622_47161840_E50_R40.gsb). ','EPSG','9615','NTv2','EPSG','4265','EPSG','4230',0.1,'EPSG','8656','Latitude and longitude difference file','35160622_47161840_R40_E50.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita',0);
INSERT INTO "usage" VALUES('EPSG','15306','grid_transformation','EPSG','9732','EPSG','4619','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','9733','Monte Mario to IGM95 (4)','For the reverse transformation from IGM95 to Monte Mario, iteration may be avoided by using an alternative grid file (35160622_47161840_F89_R40.gsb). ','EPSG','9615','NTv2','EPSG','4265','EPSG','4670',0.1,'EPSG','8656','Latitude and longitude difference file','35160622_47161840_R40_F89.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita',0);
INSERT INTO "usage" VALUES('EPSG','15327','grid_transformation','EPSG','9733','EPSG','4619','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','9734','Monte Mario to RDN2008 (5)','For the reverse transformation from RDN2008 to Monte Mario, iteration may be avoided by using an alternative grid file (35160622_47161840_F00_R40.gsb). ','EPSG','9615','NTv2','EPSG','4265','EPSG','6706',0.1,'EPSG','8656','Latitude and longitude difference file','35160622_47161840_R40_F00.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita',0);
INSERT INTO "usage" VALUES('EPSG','15308','grid_transformation','EPSG','9734','EPSG','4619','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','9735','ED50 to IGM95 (1)','For the reverse transformation from IGM95 to ED50, iteration may be avoided by using an alternative grid file (35160622_47161840_F89_E50.gsb).','EPSG','9615','NTv2','EPSG','4230','EPSG','4670',0.2,'EPSG','8656','Latitude and longitude difference file','35160622_47161840_E50_F89.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita',0);
INSERT INTO "usage" VALUES('EPSG','15334','grid_transformation','EPSG','9735','EPSG','4619','EPSG','1032');
INSERT INTO "grid_transformation" VALUES('EPSG','9736','ED50 to RDN2008 (1)','For the reverse transformation from RDN2008 to ED50, iteration may be avoided by using an alternative grid file (35160622_47161840_F00_E50.gsb). ','EPSG','9615','NTv2','EPSG','4230','EPSG','6706',0.2,'EPSG','8656','Latitude and longitude difference file','35160622_47161840_E50_F00.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita',0);
INSERT INTO "usage" VALUES('EPSG','15335','grid_transformation','EPSG','9736','EPSG','4619','EPSG','1032');
INSERT INTO "grid_transformation" VALUES('EPSG','9737','IGM95 to RDN2008 (1)','For the reverse transformation from RDN2008 to IGM95, iteration may be avoided by using an alternative grid file (35160622_47161840_F00_F89.gsb). ','EPSG','9615','NTv2','EPSG','4670','EPSG','6706',0.01,'EPSG','8656','Latitude and longitude difference file','35160622_47161840_F89_F00.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGM-Ita',0);
INSERT INTO "usage" VALUES('EPSG','15336','grid_transformation','EPSG','9737','EPSG','4619','EPSG','1150');
INSERT INTO "grid_transformation" VALUES('EPSG','9740','ETRS89 to EOS21-IRF (1)','In conjunction with the EOS-TM map projection (code 9738) applied to EOS21-IRF (code 9739), emulates the EOS21 Snake projection. Applied to ETRS89 (as realized through the OSNet v2009 CORS) defines EOS21-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9739',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-EOS21-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr EOS21 OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','15342','grid_transformation','EPSG','9740','EPSG','4620','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9759','ETRS89 to ECML14_NB-IRF (1)','In conjunction with the ECML14_NB-TM map projection (code 9760) applied to ECML14_NB-IRF (code 9758), emulates the ECML14_NB Snake projection. Applied to ETRS89 (as realized through the OSNet v2009 CORS) defines ECML14_NB-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9758',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-ECML14_NB-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr ECML_NB OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','16500','grid_transformation','EPSG','9759','EPSG','4621','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9764','ETRS89 to EWR2-IRF (1)','In conjunction with the EWR2-TM map projection (code 9765) applied to EWR2-IRF (code 9763), emulates the EWR2 Snake projection. Applied to ETRS89 (as realized through the OSNet v2009 CORS) defines EWR2-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9763',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-EWR2-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr EWR2 OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','16505','grid_transformation','EPSG','9764','EPSG','4622','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9786','RGF93 v2b to NGF-IGN69 height (4)','Replaces RAF18 geoid model [RGF93 v2 to NGF-IGN69 height (3)]. Accuracy at each grid node is in the geoid model file. Recommended interpolation method is bilinear. For reversible alternative see RGF93 v2b to RGF93 v2b + NGF-IGN69 height (4) (code 9787).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','9781','EPSG','5720',0.01,'EPSG','8666','Geoid (height correction) model file','RAF18b.mnt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra 18b',0);
INSERT INTO "usage" VALUES('EPSG','16689','grid_transformation','EPSG','9786','EPSG','1326','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9787','RGF93 v2b to RGF93 v2b + NGF-IGN69 height (4)','Reversible alternative to RGF93 v2b to NGF-IGN69 height (4) (CT code 9786). Replaces RGF93 v2 to RGF93 v2 + NGF-IGN69 height (3) (CT code 9638).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','9781','EPSG','9785',0.01,'EPSG','8666','Geoid (height correction) model file','RAF18b.mnt',NULL,NULL,NULL,NULL,'EPSG','9782','IGN Fra 18b',0);
INSERT INTO "usage" VALUES('EPSG','16690','grid_transformation','EPSG','9787','EPSG','1326','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9795','NAD83 to NAD83(CSRS)v7 (1)','Also distributed through Alberta Environment and Parks Lands Division named ABCSRSV7.DAC.','EPSG','9615','NTv2','EPSG','4269','EPSG','8255',0.1,'EPSG','8656','Latitude and longitude difference file','ABCSRSV7.GSB',NULL,NULL,NULL,NULL,NULL,NULL,'NRCan-Can AB',0);
INSERT INTO "usage" VALUES('EPSG','16635','grid_transformation','EPSG','9795','EPSG','2376','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','9867','ETRS89 to MRH21-IRF (1)','In conjunction with MRH21-TM map projection (code 9868) applied to MRH21-IRF (code 9866), emulates the MRH21 Snake projection. Applied to ETRS89 (as realized through the OSNet v2009) defines MRH21-IRF hence is errorless.						','EPSG','9615','NTv2','EPSG','4258','EPSG','9866',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-MRH21-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr MHR21 OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','16943','grid_transformation','EPSG','9867','EPSG','4652','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9876','RGF93 v2b to NGF-IGN69 height (5)','Replaces RAF18b geoid model [RGF93 v2 to NGF-IGN69 height (4)]. Accuracy at each grid node is in the geoid model file. Recommended interpolation method is bilinear. For reversible alternative see RGF93 v2b to RGF93 v2b + NGF-IGN69 height (5) (code 9877).','EPSG','1073','Geographic3D to GravityRelatedHeight (IGN2009)','EPSG','9781','EPSG','5720',0.01,'EPSG','8666','Geoid (height correction) model file','RAF20.tac',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra 20',0);
INSERT INTO "usage" VALUES('EPSG','16930','grid_transformation','EPSG','9876','EPSG','1326','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','9877','RGF93 v2b to RGF93 v2b + NGF-IGN69 height (5)','Reversible alternative to RGF93 v2b to NGF-IGN69 height (5) (CT code 9876). Replaces RGF93 v2b to RGF93 v2b + NGF-IGN69 height (4) (CT code 9787).','EPSG','1095','Geog3D to Geog2D+GravityRelatedHeight (IGN2009)','EPSG','9781','EPSG','9785',0.01,'EPSG','8666','Geoid (height correction) model file','RAF20.tac',NULL,NULL,NULL,NULL,'EPSG','9782','IGN Fra 20',0);
INSERT INTO "usage" VALUES('EPSG','16932','grid_transformation','EPSG','9877','EPSG','1326','EPSG','1270');
INSERT INTO "grid_transformation" VALUES('EPSG','9878','ETRS89 to MOLDOR11-IRF (1)','In conjunction with the MOLDOR11-TM map projection (code 9879) applied to MOLDOR11-IRF (code 9871), emulates the MOLDOR11 Snake projection. Applied to ETRS89 (as realized through the OSNet v2009 CORS) defines MOLDOR11-IRF hence is errorless.','EPSG','9615','NTv2','EPSG','4258','EPSG','9871',0.0,'EPSG','8656','Latitude and longitude difference file','TN15-ETRS89-to-MOLDOR11-IRF.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'NR-Gbr MOLDOR OSNet2009',0);
INSERT INTO "usage" VALUES('EPSG','16955','grid_transformation','EPSG','9878','EPSG','4655','EPSG','1141');
INSERT INTO "grid_transformation" VALUES('EPSG','9884','ETRS89 to CD Norway depth (1)','For reversible alternative to this transformation see ETRS89 to ETRS89 + CD Norway depth (1) (code 9885). An improved model is expected in 2023.','EPSG','1109','Geographic3D to Depth (Gravsoft)','EPSG','4937','EPSG','9672',999.0,'EPSG','8666','Geoid (height correction) model file','ChartDatum_above_Ellipsoid_EUREF89_v2021a.bin',NULL,NULL,NULL,NULL,NULL,NULL,'SK-Nor 2021',0);
INSERT INTO "usage" VALUES('EPSG','16982','grid_transformation','EPSG','9884','EPSG','4656','EPSG','1277');
INSERT INTO "grid_transformation" VALUES('EPSG','9885','ETRS89 to ETRS89 + CD Norway depth (1)','Reversible alternative to ETRS89 to CD Norway depth (1) (code 9884). An improved model is expected in 2023.','EPSG','1110','Geog3D to Geog2D+Depth (Gravsoft)','EPSG','4937','EPSG','9883',999.0,'EPSG','8666','Geoid (height correction) model file','ChartDatum_above_Ellipsoid_EUREF89_v2021a.bin',NULL,NULL,NULL,NULL,'EPSG','4258','SK-Nor 2021',0);
INSERT INTO "usage" VALUES('EPSG','16984','grid_transformation','EPSG','9885','EPSG','4656','EPSG','1272');
INSERT INTO "grid_transformation" VALUES('EPSG','9886','NAD27 to NAD83(CSRS)v2 (2)','Can be taken as an approximate transformation NAD27 to WGS 84 - see code 1703.','EPSG','9615','NTv2','EPSG','4267','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','SK27-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',0);
INSERT INTO "usage" VALUES('EPSG','16978','grid_transformation','EPSG','9886','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9887','NAD83 to NAD83(CSRS)v2 (2)','Can be taken as an approximate transformation NAD83 to WGS 84 - see code 1697.','EPSG','9615','NTv2','EPSG','4269','EPSG','8237',1.5,'EPSG','8656','Latitude and longitude difference file','SK83-98.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'SK PMC-Can SK',0);
INSERT INTO "usage" VALUES('EPSG','16979','grid_transformation','EPSG','9887','EPSG','2375','EPSG','1151');
INSERT INTO "grid_transformation" VALUES('EPSG','9902','Baltic 1977 height to EVRF2019 height (1)','Determined at 154 points, SD 0.034m. Offset: mean 0.151m, minimum 0.079m, maximum 0.285m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5705','EPSG','9389',0.068,'EPSG','8732','Vertical offset file','ua_2019z.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Ukr 2019z 2021',0);
INSERT INTO "usage" VALUES('EPSG','17066','grid_transformation','EPSG','9902','EPSG','3324','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','9903','Baltic 1977 height to EVRF2019 mean-tide height (1)','Determined at 154 points, SD 0.032m. Offset: mean 0.131m, minimum 0.066m, maximum 0.268m.','EPSG','1085','Vertical Offset by Grid Interpolation (asc)','EPSG','5705','EPSG','9390',0.064,'EPSG','8732','Vertical offset file','ua_2019m.asc',NULL,NULL,NULL,NULL,'EPSG','4258','EuG-Ukr 2019m 2021',0);
INSERT INTO "usage" VALUES('EPSG','17067','grid_transformation','EPSG','9903','EPSG','3324','EPSG','1059');
INSERT INTO "grid_transformation" VALUES('EPSG','10000','RGF93 v1 to NGF-IGN69 height (1)','May be used for transformations from WGS 84 to NGF-IGN69 height. Accuracy at each 0.1 deg x 0.1 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4965','EPSG','5720',0.5,'EPSG','8666','Geoid (height correction) model file','ggf97a.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra',0);
INSERT INTO "usage" VALUES('EPSG','11001','grid_transformation','EPSG','10000','EPSG','1326','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10001','ETRS89 to NGF-IGN69 height (1)','Parameter values taken from RGF93 v1 to NGF-IGN69 height (1) (code 10000) assuming that RGF93 v1 is equivalent to ETRS89 within the accuracy of the transformation. Accuracy at each 0.1 deg x 0.1 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4937','EPSG','5720',0.5,'EPSG','8666','Geoid (height correction) model file','ggf97a.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra',0);
INSERT INTO "usage" VALUES('EPSG','11002','grid_transformation','EPSG','10001','EPSG','1326','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10002','RGF93 v1 to NGF-IGN78 height (1)','May be used for transformations from WGS 84 to NGF-IGN78 height. Accuracy at each 0.1 deg x 0.1 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4965','EPSG','5721',0.5,'EPSG','8666','Geoid (height correction) model file','ggf97a_corse.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra Cor',0);
INSERT INTO "usage" VALUES('EPSG','11003','grid_transformation','EPSG','10002','EPSG','1327','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10003','ETRS89 to NGF-IGN78 height (1)','Parameter values taken from RGF93 v1 to NGF-IGN78 height (1) (code 10002) assuming that RGF93 v1 is equivalent to ETRS89 within the accuracy of the transformation. Accuracy at each 0.1 deg x 0.1 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4937','EPSG','5721',0.5,'EPSG','8666','Geoid (height correction) model file','ggf97a_corse.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Fra Cor',0);
INSERT INTO "usage" VALUES('EPSG','11004','grid_transformation','EPSG','10003','EPSG','1327','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10004','RRAF 1991 to Martinique 1987 height (1)','May be used for transformations from WGS 84 to IGN 1987. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5756',998.0,'EPSG','8666','Geoid (height correction) model file','ggm00.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Mtq',1);
INSERT INTO "usage" VALUES('EPSG','11005','grid_transformation','EPSG','10004','EPSG','1156','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10005','RRAF 1991 to Guadeloupe 1988 height (1)','May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5757',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp GT',1);
INSERT INTO "usage" VALUES('EPSG','11006','grid_transformation','EPSG','10005','EPSG','2892','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10006','RRAF 1991 to Guadeloupe 1988 height (2)','May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5757',998.0,'EPSG','8666','Geoid (height correction) model file','ggg00_mg.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp MG',1);
INSERT INTO "usage" VALUES('EPSG','11007','grid_transformation','EPSG','10006','EPSG','2894','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10007','RRAF 1991 to Guadeloupe 1988 height (3)','May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5757',998.0,'EPSG','8666','Geoid (height correction) model file','ggg00_ls.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp LSt',1);
INSERT INTO "usage" VALUES('EPSG','11008','grid_transformation','EPSG','10007','EPSG','2895','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10008','RRAF 1991 to Guadeloupe 1988 height (4)','May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5757',998.0,'EPSG','8666','Geoid (height correction) model file','ggg00_ld.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp Des',1);
INSERT INTO "usage" VALUES('EPSG','11009','grid_transformation','EPSG','10008','EPSG','2893','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10009','RRAF 1991 to Guadeloupe 1988 height (5)','May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5757',998.0,'EPSG','8666','Geoid (height correction) model file','ggg00_sb.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StB',1);
INSERT INTO "usage" VALUES('EPSG','11010','grid_transformation','EPSG','10009','EPSG','2891','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10010','RRAF 1991 to Guadeloupe 1988 height (6)','May be used for transformations from WGS 84 to IGN 1988. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5757',998.0,'EPSG','8666','Geoid (height correction) model file','ggg00_sm.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StM',1);
INSERT INTO "usage" VALUES('EPSG','11011','grid_transformation','EPSG','10010','EPSG','2890','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10011','RGFG95 to NGG1977 height (1)','May be used for transformations from WGS 84 to NGG1977. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file. For reversible alternative to this transformation see RGFG95 to RGFG95 + NGG1977 height (1) (code 9630).','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4967','EPSG','5755',998.0,'EPSG','8666','Geoid (height correction) model file','ggguy00.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Guf',0);
INSERT INTO "usage" VALUES('EPSG','14422','grid_transformation','EPSG','10011','EPSG','3146','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10012','RGR92 to Reunion 1989 height (1)','May be used for transformations from WGS 84 to IGN 1989. Accuracy at each 0.02 deg x 0.02 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4971','EPSG','5758',0.1,'EPSG','8666','Geoid (height correction) model file','ggr99.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Reu',0);
INSERT INTO "usage" VALUES('EPSG','11013','grid_transformation','EPSG','10012','EPSG','3337','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10013','NAD83 to NAVD88 height (1)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u01.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus NW',1);
INSERT INTO "usage" VALUES('EPSG','11014','grid_transformation','EPSG','10013','EPSG','2977','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10014','NAD83 to NAVD88 height (2)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u02.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CNW',1);
INSERT INTO "usage" VALUES('EPSG','11015','grid_transformation','EPSG','10014','EPSG','2978','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10015','NAD83 to NAVD88 height (3)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u03.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CNE',1);
INSERT INTO "usage" VALUES('EPSG','11016','grid_transformation','EPSG','10015','EPSG','2979','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10016','NAD83 to NAVD88 height (4)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u04.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus NE',1);
INSERT INTO "usage" VALUES('EPSG','11017','grid_transformation','EPSG','10016','EPSG','2980','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10017','NAD83 to NAVD88 height (5)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u05.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus SW',1);
INSERT INTO "usage" VALUES('EPSG','11018','grid_transformation','EPSG','10017','EPSG','2973','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10018','NAD83 to NAVD88 height (6)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u06.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CSW',1);
INSERT INTO "usage" VALUES('EPSG','11019','grid_transformation','EPSG','10018','EPSG','2974','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10019','NAD83 to NAVD88 height (7)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u07.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus CSE',1);
INSERT INTO "usage" VALUES('EPSG','11020','grid_transformation','EPSG','10019','EPSG','2975','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10020','NAD83 to NAVD88 height (8)','Uses Geoid03 hybrid model. See information source for further information. Note: Source CRS is 2D, used in this application of the method as a pseudo-3D CRS.','EPSG','9665','Geographic3D to GravityRelatedHeight (gtx)','EPSG','4269','EPSG','5703',0.05,'EPSG','8666','Geoid (height correction) model file','g2003u08.bin',NULL,NULL,NULL,NULL,NULL,NULL,'NGS-US Conus SE',1);
INSERT INTO "usage" VALUES('EPSG','11021','grid_transformation','EPSG','10020','EPSG','2976','EPSG','1132');
INSERT INTO "grid_transformation" VALUES('EPSG','10021','ETRS89 to ODN height (1)','','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5701',0.02,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Gbr',0);
INSERT INTO "usage" VALUES('EPSG','11022','grid_transformation','EPSG','10021','EPSG','2792','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10022','ETRS89 to Belfast height (1)','May be used for transformations from WGS 84 to Belfast.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5732',0.03,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK NI',1);
INSERT INTO "usage" VALUES('EPSG','11023','grid_transformation','EPSG','10022','EPSG','2530','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10023','ETRS89 to Douglas height (1)','May be used for transformations from WGS 84 to Douglas.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5750',0.02,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Man',0);
INSERT INTO "usage" VALUES('EPSG','11024','grid_transformation','EPSG','10023','EPSG','2803','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10024','ETRS89 to Fair Isle height (1)','May be used for transformations from WGS 84 to Fair Isle.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5741',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Fair',0);
INSERT INTO "usage" VALUES('EPSG','11025','grid_transformation','EPSG','10024','EPSG','2794','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10025','ETRS89 to Flannan Isles height (1)','May be used for transformations from WGS 84 to Flannan Isles.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5748',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Flan',0);
INSERT INTO "usage" VALUES('EPSG','11026','grid_transformation','EPSG','10025','EPSG','2801','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10026','ETRS89 to Foula height (1)','May be used for transformations from WGS 84 to Foula.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5743',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Foula',0);
INSERT INTO "usage" VALUES('EPSG','11027','grid_transformation','EPSG','10026','EPSG','2796','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10027','ETRS89 to Lerwick height (1)','May be used for transformations from WGS 84 to Lerwick.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5742',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Shet',0);
INSERT INTO "usage" VALUES('EPSG','11028','grid_transformation','EPSG','10027','EPSG','2795','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10028','ETRS89 to Malin Head height (1)','May be used for transformations from WGS 84 to Malin Head.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5731',0.04,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-Ire',1);
INSERT INTO "usage" VALUES('EPSG','11029','grid_transformation','EPSG','10028','EPSG','1305','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10029','ETRS89 to ODN Orkney height (1)','','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5740',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Ork',0);
INSERT INTO "usage" VALUES('EPSG','11030','grid_transformation','EPSG','10029','EPSG','2793','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10030','ETRS89 to North Rona height (1)','May be used for transformations from WGS 84 to North Rona.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5745',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Rona',0);
INSERT INTO "usage" VALUES('EPSG','11031','grid_transformation','EPSG','10030','EPSG','2798','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10031','ETRS89 to St. Kilda height (1)','May be used for transformations from WGS 84 to St. Kilda.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5747',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Kilda',0);
INSERT INTO "usage" VALUES('EPSG','11032','grid_transformation','EPSG','10031','EPSG','2800','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10032','ETRS89 to St. Marys height (1)','May be used for transformations from WGS 84 to St. Marys.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5749',0.0,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Scilly',0);
INSERT INTO "usage" VALUES('EPSG','11033','grid_transformation','EPSG','10032','EPSG','2802','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10033','ETRS89 to Stornoway height (1)','May be used for transformations from WGS 84 to Stornoway.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5746',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Heb',0);
INSERT INTO "usage" VALUES('EPSG','11034','grid_transformation','EPSG','10033','EPSG','2799','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10034','ETRS89 to Sule Skerry height (1)','May be used for transformations from WGS 84 to Sule Skerry.','EPSG','9663','Geographic3D to GravityRelatedHeight (OSGM-GB)','EPSG','4937','EPSG','5744',0.05,'EPSG','8666','Geoid (height correction) model file','OSTN02_OSGM02_GB.txt',NULL,NULL,NULL,NULL,NULL,NULL,'OS-UK Sule',0);
INSERT INTO "usage" VALUES('EPSG','11035','grid_transformation','EPSG','10034','EPSG','2797','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10035','GDA94 to AHD height (1)','May be used for transformations from WGS 84 to AHD.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SC52_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SC52',0);
INSERT INTO "usage" VALUES('EPSG','11036','grid_transformation','EPSG','10035','EPSG','2899','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10036','GDA94 to AHD height (2)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SC53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SC53',0);
INSERT INTO "usage" VALUES('EPSG','11037','grid_transformation','EPSG','10036','EPSG','2900','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10037','GDA94 to AHD height (3)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SC54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SC54',0);
INSERT INTO "usage" VALUES('EPSG','11038','grid_transformation','EPSG','10037','EPSG','2901','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10038','GDA94 to AHD height (4)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SD51_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SD51',0);
INSERT INTO "usage" VALUES('EPSG','11039','grid_transformation','EPSG','10038','EPSG','2902','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10039','GDA94 to AHD height (5)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SD52_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SD52',0);
INSERT INTO "usage" VALUES('EPSG','11040','grid_transformation','EPSG','10039','EPSG','2903','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10040','GDA94 to AHD height (6)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SD53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SD53',0);
INSERT INTO "usage" VALUES('EPSG','11041','grid_transformation','EPSG','10040','EPSG','2904','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10041','GDA94 to AHD height (7)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SD54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SD54',0);
INSERT INTO "usage" VALUES('EPSG','11042','grid_transformation','EPSG','10041','EPSG','2905','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10042','GDA94 to AHD height (8)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SD55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SD55',0);
INSERT INTO "usage" VALUES('EPSG','11043','grid_transformation','EPSG','10042','EPSG','2906','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10043','GDA94 to AHD height (9)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SE50_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SE50',0);
INSERT INTO "usage" VALUES('EPSG','11044','grid_transformation','EPSG','10043','EPSG','2907','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10044','GDA94 to AHD height (10)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SE51_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SE51',0);
INSERT INTO "usage" VALUES('EPSG','11045','grid_transformation','EPSG','10044','EPSG','2908','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10045','GDA94 to AHD height (11)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SE52_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SE52',0);
INSERT INTO "usage" VALUES('EPSG','11046','grid_transformation','EPSG','10045','EPSG','2909','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10046','GDA94 to AHD height (12)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SE53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SE53',0);
INSERT INTO "usage" VALUES('EPSG','11047','grid_transformation','EPSG','10046','EPSG','2910','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10047','GDA94 to AHD height (13)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SE54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SE54',0);
INSERT INTO "usage" VALUES('EPSG','11048','grid_transformation','EPSG','10047','EPSG','2911','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10048','GDA94 to AHD height (14)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SE55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SE55',0);
INSERT INTO "usage" VALUES('EPSG','11049','grid_transformation','EPSG','10048','EPSG','2912','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10049','GDA94 to AHD height (15)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF49_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF49',0);
INSERT INTO "usage" VALUES('EPSG','11050','grid_transformation','EPSG','10049','EPSG','2913','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10050','GDA94 to AHD height (16)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF50_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF50',0);
INSERT INTO "usage" VALUES('EPSG','11051','grid_transformation','EPSG','10050','EPSG','2914','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10051','GDA94 to AHD height (17)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF51_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF51',0);
INSERT INTO "usage" VALUES('EPSG','11052','grid_transformation','EPSG','10051','EPSG','2915','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10052','GDA94 to AHD height (18)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF52_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF52',0);
INSERT INTO "usage" VALUES('EPSG','11053','grid_transformation','EPSG','10052','EPSG','2916','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10053','GDA94 to AHD height (19)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF53',0);
INSERT INTO "usage" VALUES('EPSG','11054','grid_transformation','EPSG','10053','EPSG','2917','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10054','GDA94 to AHD height (20)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF54',0);
INSERT INTO "usage" VALUES('EPSG','11055','grid_transformation','EPSG','10054','EPSG','2918','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10055','GDA94 to AHD height (21)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF55',0);
INSERT INTO "usage" VALUES('EPSG','11056','grid_transformation','EPSG','10055','EPSG','2919','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10056','GDA94 to AHD height (22)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SF56_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SF56',0);
INSERT INTO "usage" VALUES('EPSG','11057','grid_transformation','EPSG','10056','EPSG','2920','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10057','GDA94 to AHD height (23)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG49_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG49',0);
INSERT INTO "usage" VALUES('EPSG','11058','grid_transformation','EPSG','10057','EPSG','2921','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10058','GDA94 to AHD height (24)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG50_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG50',0);
INSERT INTO "usage" VALUES('EPSG','11059','grid_transformation','EPSG','10058','EPSG','2922','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10059','GDA94 to AHD height (25)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG51_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG51',0);
INSERT INTO "usage" VALUES('EPSG','11060','grid_transformation','EPSG','10059','EPSG','2923','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10060','GDA94 to AHD height (26)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG52_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG52',0);
INSERT INTO "usage" VALUES('EPSG','11061','grid_transformation','EPSG','10060','EPSG','2924','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10061','GDA94 to AHD height (27)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG53',0);
INSERT INTO "usage" VALUES('EPSG','11062','grid_transformation','EPSG','10061','EPSG','2925','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10062','GDA94 to AHD height (28)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG54',0);
INSERT INTO "usage" VALUES('EPSG','11063','grid_transformation','EPSG','10062','EPSG','2926','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10063','GDA94 to AHD height (29)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG55',0);
INSERT INTO "usage" VALUES('EPSG','11064','grid_transformation','EPSG','10063','EPSG','2927','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10064','GDA94 to AHD height (30)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SG56_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SG56',0);
INSERT INTO "usage" VALUES('EPSG','11065','grid_transformation','EPSG','10064','EPSG','2928','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10065','GDA94 to AHD height (31)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH49_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH49',1);
INSERT INTO "usage" VALUES('EPSG','11066','grid_transformation','EPSG','10065','EPSG','2929','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10066','GDA94 to AHD height (32)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH50_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH50',0);
INSERT INTO "usage" VALUES('EPSG','11067','grid_transformation','EPSG','10066','EPSG','2930','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10067','GDA94 to AHD height (33)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH51_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH51',0);
INSERT INTO "usage" VALUES('EPSG','11068','grid_transformation','EPSG','10067','EPSG','2931','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10068','GDA94 to AHD height (34)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH52_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH52',0);
INSERT INTO "usage" VALUES('EPSG','11069','grid_transformation','EPSG','10068','EPSG','2932','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10069','GDA94 to AHD height (35)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH53',0);
INSERT INTO "usage" VALUES('EPSG','11070','grid_transformation','EPSG','10069','EPSG','2933','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10070','GDA94 to AHD height (36)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH54',0);
INSERT INTO "usage" VALUES('EPSG','11071','grid_transformation','EPSG','10070','EPSG','2934','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10071','GDA94 to AHD height (37)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH55',0);
INSERT INTO "usage" VALUES('EPSG','11072','grid_transformation','EPSG','10071','EPSG','2935','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10072','GDA94 to AHD height (38)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SH56_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SH56',0);
INSERT INTO "usage" VALUES('EPSG','11073','grid_transformation','EPSG','10072','EPSG','2936','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10073','GDA94 to AHD height (39)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SI50_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SI50',0);
INSERT INTO "usage" VALUES('EPSG','11074','grid_transformation','EPSG','10073','EPSG','2937','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10074','GDA94 to AHD height (40)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SI51_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SI51',0);
INSERT INTO "usage" VALUES('EPSG','11075','grid_transformation','EPSG','10074','EPSG','2938','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10075','GDA94 to AHD height (41)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SI53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SI53',0);
INSERT INTO "usage" VALUES('EPSG','11076','grid_transformation','EPSG','10075','EPSG','2939','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10076','GDA94 to AHD height (42)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SI54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SI54',0);
INSERT INTO "usage" VALUES('EPSG','11077','grid_transformation','EPSG','10076','EPSG','2940','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10077','GDA94 to AHD height (43)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SI55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SI55',0);
INSERT INTO "usage" VALUES('EPSG','11078','grid_transformation','EPSG','10077','EPSG','2941','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10078','GDA94 to AHD height (44)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SI56_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SI56',0);
INSERT INTO "usage" VALUES('EPSG','11079','grid_transformation','EPSG','10078','EPSG','2942','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10079','GDA94 to AHD height (45)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SJ53_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SJ53',0);
INSERT INTO "usage" VALUES('EPSG','11080','grid_transformation','EPSG','10079','EPSG','2943','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10080','GDA94 to AHD height (46)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SJ54_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SJ54',0);
INSERT INTO "usage" VALUES('EPSG','11081','grid_transformation','EPSG','10080','EPSG','2944','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10081','GDA94 to AHD height (47)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SJ55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SJ55',0);
INSERT INTO "usage" VALUES('EPSG','11082','grid_transformation','EPSG','10081','EPSG','2945','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10082','GDA94 to AHD height (48)','May be used for transformations from WGS 84 to AHD. Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5711',0.4,'EPSG','8666','Geoid (height correction) model file','SJ56_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SJ56',0);
INSERT INTO "usage" VALUES('EPSG','11083','grid_transformation','EPSG','10082','EPSG','2946','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10083','GDA94 to AHD (Tasmania) height (1)','May be used for transformations from WGS 84 to AHD (Tasmania). Uses AusGeoid98 model.','EPSG','9662','Geographic3D to GravityRelatedHeight (AUSGeoid98)','EPSG','4939','EPSG','5712',0.4,'EPSG','8666','Geoid (height correction) model file','SK55_DAT.htm',NULL,NULL,NULL,NULL,NULL,NULL,'GA-Aus SK55',0);
INSERT INTO "usage" VALUES('EPSG','11084','grid_transformation','EPSG','10083','EPSG','2947','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','10084','WGS 84 to EGM96 height (1)','Replaces WGS 84 to EGM84 height (1) (CT 15781). Replaced by WGS 84 to EGM2008 height (1) and (2) (CTs 3858-59).  For reversible alternative see WGS 84 to WGS 84 + EGM96 height (1) (CT 9708). An executable using spherical harmonics is also available.','EPSG','9661','Geographic3D to GravityRelatedHeight (EGM)','EPSG','4979','EPSG','5773',1.0,'EPSG','8666','Geoid (height correction) model file','WW15MGH.GRD',NULL,NULL,NULL,NULL,NULL,NULL,'NGA-World',0);
INSERT INTO "usage" VALUES('EPSG','11085','grid_transformation','EPSG','10084','EPSG','1262','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','15486','CH1903 to CH1903+ (1)','For improved accuracy (0.01m) use CHENyx06 interpolation programme FINELTRA. File CHENyx06 replaced by CHENyx06a; there is a small area at the border of the data where some more real data has been introduced. swisstopo consider the change insignificant.','EPSG','9615','NTv2','EPSG','4149','EPSG','4150',0.2,'EPSG','8656','Latitude and longitude difference file','CHENyx06a.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'BfL-Che',0);
INSERT INTO "usage" VALUES('EPSG','11497','grid_transformation','EPSG','15486','EPSG','1286','EPSG','1085');
INSERT INTO "grid_transformation" VALUES('EPSG','15488','RRAF 1991 to IGN 1988 MG height (1)','May be used for transformations from WGS 84 to IGN 1988 MG. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5617',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_mg.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp MG',1);
INSERT INTO "usage" VALUES('EPSG','11499','grid_transformation','EPSG','15488','EPSG','2894','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','15489','RRAF 1991 to IGN 1988 LS height (1)','May be used for transformations from WGS 84 to IGN 1988 LS. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5616',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_ls.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp LSt',1);
INSERT INTO "usage" VALUES('EPSG','11500','grid_transformation','EPSG','15489','EPSG','2895','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','15490','RRAF 1991 to IGN 1992 LD height (1)','May be used for transformations from WGS 84 to IGN 1992 LD. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5618',0.5,'EPSG','8666','Geoid (height correction) model file','ggg00_ld.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp Des',1);
INSERT INTO "usage" VALUES('EPSG','11501','grid_transformation','EPSG','15490','EPSG','2893','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','15491','RRAF 1991 to IGN 1988 SB height (1)','May be used for transformations from WGS 84 to IGN 1988 SB. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5619',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_sb.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StB',1);
INSERT INTO "usage" VALUES('EPSG','11502','grid_transformation','EPSG','15491','EPSG','2891','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','15492','RRAF 1991 to IGN 1988 SM height (1)','May be used for transformations from WGS 84 to IGN 1988 SM. Accuracy at each 0.025 deg x 0.025 degree grid node is given within the geoid model file.','EPSG','9664','Geographic3D to GravityRelatedHeight (IGN1997)','EPSG','4973','EPSG','5620',0.2,'EPSG','8666','Geoid (height correction) model file','ggg00_sm.txt',NULL,NULL,NULL,NULL,NULL,NULL,'IGN Glp StM',1);
INSERT INTO "usage" VALUES('EPSG','11503','grid_transformation','EPSG','15492','EPSG','2890','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','15781','WGS 84 to EGM84 height (1)','File may also be found named as "EGM84.GRD". An executable using spherical harmonics is also available. Replaced by WGS 84 to EGM96 height (1) (CT code 10084).','EPSG','9661','Geographic3D to GravityRelatedHeight (EGM)','EPSG','4979','EPSG','5798',1.0,'EPSG','8666','Geoid (height correction) model file','DIRACC.DAT',NULL,NULL,NULL,NULL,NULL,NULL,'NGA-World',0);
INSERT INTO "usage" VALUES('EPSG','11792','grid_transformation','EPSG','15781','EPSG','1262','EPSG','1133');
INSERT INTO "grid_transformation" VALUES('EPSG','15785','AGD84 to WGS 84 (9)','Parameter values from AGD84 to GDA94 (5) (code 1804). Approximation assuming WGS 84 is equivalent to GDA94; ignores the low accuracy of the WGS 84 ensemble and the inconsistent application of tectonic plate motion to WGS 84 data.','EPSG','9615','NTv2','EPSG','4203','EPSG','4326',2.9,'EPSG','8656','Latitude and longitude difference file','National 84 (02.07.01).gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Aus 1m',0);
INSERT INTO "usage" VALUES('EPSG','11796','grid_transformation','EPSG','15785','EPSG','2576','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15786','AGD66 to WGS 84 (17)','Parameter values from AGD66 to GDA94 (11) (code 1803). Approximation assuming WGS 84 is equivalent to GDA94; ignores the low accuracy of the WGS 84 ensemble and the inconsistent application of tectonic plate motion to WGS 84 data.','EPSG','9615','NTv2','EPSG','4202','EPSG','4326',2.9,'EPSG','8656','Latitude and longitude difference file','A66 National (13.09.01).gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Aus 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','11797','grid_transformation','EPSG','15786','EPSG','2575','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15834','NAD83 to NAD83(HARN) (44)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 15835.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','nchpgn.las','EPSG','8658','Longitude difference file','nchpgn.los',NULL,NULL,'NGS-Usa NC',0);
INSERT INTO "usage" VALUES('EPSG','11845','grid_transformation','EPSG','15834','EPSG','1402','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','15835','NAD83 to WGS 84 (55)','Parameter files are from NAD83 to NAD83(HARN) (44) (code 15834) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','nchpgn.las','EPSG','8658','Longitude difference file','nchpgn.los',NULL,NULL,'OGP-Usa NC',0);
INSERT INTO "usage" VALUES('EPSG','11846','grid_transformation','EPSG','15835','EPSG','1402','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15836','NAD83 to NAD83(HARN) (45)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 15837.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','schpgn.las','EPSG','8658','Longitude difference file','schpgn.los',NULL,NULL,'NGS-Usa SC',0);
INSERT INTO "usage" VALUES('EPSG','11847','grid_transformation','EPSG','15836','EPSG','1409','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','15837','NAD83 to WGS 84 (56)','Parameter files are from NAD83 to NAD83(HARN) (45) (code 15836) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','schpgn.las','EPSG','8658','Longitude difference file','schpgn.los',NULL,NULL,'OGP-Usa SC',0);
INSERT INTO "usage" VALUES('EPSG','11848','grid_transformation','EPSG','15837','EPSG','1409','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15838','NAD83 to NAD83(HARN) (46)','Uses NADCON method which expects longitudes positive west; EPSG GeogCRSs NAD83 (code 4269) and NAD83(HARN) (code 4152) have longitudes positive east.  May be taken as approximate transformation NAD83-WGS 84 - see code 15839.','EPSG','9613','NADCON','EPSG','4269','EPSG','4152',0.05,'EPSG','8657','Latitude difference file','pahpgn.las','EPSG','8658','Longitude difference file','pahpgn.los',NULL,NULL,'NGS-Usa PA',0);
INSERT INTO "usage" VALUES('EPSG','11849','grid_transformation','EPSG','15838','EPSG','1407','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','15839','NAD83 to WGS 84 (57)','Parameter files are from NAD83 to NAD83(HARN) (46) (code 15838) assuming that NAD83(HARN) is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4269','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','pahpgn.las','EPSG','8658','Longitude difference file','pahpgn.los',NULL,NULL,'OGP-Usa PA',0);
INSERT INTO "usage" VALUES('EPSG','11850','grid_transformation','EPSG','15839','EPSG','1407','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15840','Old Hawaiian to WGS 84 (8)','Parameter files are from Old Hawaiian to NAD83 (1) (code 1454) assuming that NAD83 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4135','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','hawaii.las','EPSG','8658','Longitude difference file','hawaii.los',NULL,NULL,'OGP-Usa HI 2m',0);
INSERT INTO "usage" VALUES('EPSG','11851','grid_transformation','EPSG','15840','EPSG','1334','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15841','Puerto Rico to WGS 84 (4)','Parameter files are from Puerto Rico to NAD83 (1) (code 1461) assuming that NAD83 is equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9613','NADCON','EPSG','4139','EPSG','4326',2.0,'EPSG','8657','Latitude difference file','prvi.las','EPSG','8658','Longitude difference file','prvi.los',NULL,NULL,'OGP-Pri 2m',0);
INSERT INTO "usage" VALUES('EPSG','11852','grid_transformation','EPSG','15841','EPSG','3294','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15851','NAD27 to WGS 84 (79)','Parameter files are from NAD27 to NAD83 (1) (code 1241) assuming that NAD83 is equivalent to WGS 84 within the accuracy of this tfm. Uses NADCON method which expects longitudes positive west; EPSG CRS codes 4267 and 4326 have longitudes positive east.','EPSG','9613','NADCON','EPSG','4267','EPSG','4326',5.0,'EPSG','8657','Latitude difference file','conus.las','EPSG','8658','Longitude difference file','conus.los',NULL,NULL,'OGP-Usa Conus',0);
INSERT INTO "usage" VALUES('EPSG','11862','grid_transformation','EPSG','15851','EPSG','2374','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15864','NAD27 to WGS 84 (85)','Parameter files are from NAD27 to NAD83 (2) (code 1243) assuming that NAD83 is equivalent to WGS 84 within the accuracy of this tfm. Uses NADCON method which expects longitudes positive west; EPSG CRS codes 4267 and 4326 have longitudes positive east.','EPSG','9613','NADCON','EPSG','4267','EPSG','4326',5.0,'EPSG','8657','Latitude difference file','alaska.las','EPSG','8658','Longitude difference file','alaska.los',NULL,NULL,'OGP-Usa AK',0);
INSERT INTO "usage" VALUES('EPSG','11875','grid_transformation','EPSG','15864','EPSG','2373','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15895','ED50 to ETRS89 (11)','May be taken as approximate transformation ED50 to WGS 84 - see code 15907. NOTE: Parameter file is non-conformant with NTv2 specification - replaced by ED50 to ETRS89 (12) (code 15932).','EPSG','9615','NTv2','EPSG','4230','EPSG','4258',0.2,'EPSG','8656','Latitude and longitude difference file','sped2et.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp',1);
INSERT INTO "usage" VALUES('EPSG','11906','grid_transformation','EPSG','15895','EPSG','3429','EPSG','1152');
INSERT INTO "grid_transformation" VALUES('EPSG','15907','ED50 to WGS 84 (40)','Parameter values from ED50 to ETRS89 (11) (code 15895). Assumes ETRS89 and WGS 84 can be considered the same to within the accuracy of the transformation. NOTE: Parameter file is non-conformant with NTv2 specification - replaced by ED50 to WGS 84 (41).','EPSG','9615','NTv2','EPSG','4230','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','sped2et.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Esp',1);
INSERT INTO "usage" VALUES('EPSG','11918','grid_transformation','EPSG','15907','EPSG','3429','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','15932','ED50 to ETRS89 (12)','Replaces ED50 to ETRS89 (11) (code 15895) - see supersession record. May be taken as approximate transformation ED50 to WGS 84 - see code 15933.','EPSG','9615','NTv2','EPSG','4230','EPSG','4258',0.2,'EPSG','8656','Latitude and longitude difference file','SPED2ETV2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Esp v2',0);
INSERT INTO "usage" VALUES('EPSG','11943','grid_transformation','EPSG','15932','EPSG','3429','EPSG','1152');
INSERT INTO "grid_transformation" VALUES('EPSG','15933','ED50 to WGS 84 (41)','Parameter values from ED50 to ETRS89 (12) (code 15932). Assumes ETRS89 and WGS 84 can be considered the same to within the accuracy of the transformation. Replaces ED50 to WGS 84 (40) - see supersession record.','EPSG','9615','NTv2','EPSG','4230','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','SPED2ETV2.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Esp v2',0);
INSERT INTO "usage" VALUES('EPSG','11944','grid_transformation','EPSG','15933','EPSG','3429','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15940','NTF to RGF93 (2)','Emulation using NTv2 method of France Geocentric Interpolation method tfm NTF to RGF93 (1), code 1053. May be taken as approximate transformation to ETRS89 or WGS 84 - see tfm codes 15941 and 15942.','EPSG','9615','NTv2','EPSG','4275','EPSG','4171',1.0,'EPSG','8656','Latitude and longitude difference file','rgf93_ntf.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ESRI-Fra 1m emulation',1);
INSERT INTO "usage" VALUES('EPSG','11951','grid_transformation','EPSG','15940','EPSG','1326','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','15941','NTF to ETRS89 (3)','These parameter values are taken from NTF to RGR93 (2) (code 15940) as RGR93 may be considered equivalent to ETRS89 within the accuracy of the transformation. Emulation of France Geocentric Interpolation method tfm code 1054.','EPSG','9615','NTv2','EPSG','4275','EPSG','4258',1.0,'EPSG','8656','Latitude and longitude difference file','rgf93_ntf.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Fra 1m emulation',1);
INSERT INTO "usage" VALUES('EPSG','11952','grid_transformation','EPSG','15941','EPSG','1326','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','15942','NTF to WGS 84 (3)','These parameter values are taken from NTF to RGR93 (2) (code 15940) as RGR93 may be considered equivalent to WGS 84 within the accuracy of the transformation. Emulation of France Geocentric Interpolation method tfm code 15939.','EPSG','9615','NTv2','EPSG','4275','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','rgf93_ntf.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Fra 1m emulation',1);
INSERT INTO "usage" VALUES('EPSG','11953','grid_transformation','EPSG','15942','EPSG','1326','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','15944','NEA74 Noumea to RGNC91-93 (4)','Emulation using NTv2 method of tfm NEA74 Noumea to RGNC91-93 (3) (code 15943).','EPSG','9615','NTv2','EPSG','4644','EPSG','4749',0.05,'EPSG','8656','Latitude and longitude difference file','RGNC1991_NEA74Noumea.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ESRI-Ncl 0.05m',1);
INSERT INTO "usage" VALUES('EPSG','11955','grid_transformation','EPSG','15944','EPSG','2823','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','15947','IGN72 Grande Terre to RGNC91-93 (6)','Emulation using NTv2 method of tfm IGN72 Grande Terre to RGNC91-93 (4) (code 15945).','EPSG','9615','NTv2','EPSG','4662','EPSG','4749',0.1,'EPSG','8656','Latitude and longitude difference file','RGNC1991_IGN72GrandeTerre.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ESRI-Ncl 0.1m',1);
INSERT INTO "usage" VALUES('EPSG','11958','grid_transformation','EPSG','15947','EPSG','2822','EPSG','1031');
INSERT INTO "grid_transformation" VALUES('EPSG','15948','DHDN to ETRS89 (8)','Developed for ATKIS (Amtliches Topographisch-Kartographisches Informationssystem [Official Topographic and Cartographic Information System]). Provides a uniform transformation across the whole country. May be used as tfm to WGS84 - see tfm code 15949.','EPSG','9615','NTv2','EPSG','4314','EPSG','4258',0.9,'EPSG','8656','Latitude and longitude difference file','BETA2007.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'BKG-Deu BeTA2007',0);
INSERT INTO "usage" VALUES('EPSG','11959','grid_transformation','EPSG','15948','EPSG','3339','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','15949','DHDN to WGS 84 (4)','These parameter values are taken from DHDN to ETRS89 (8) (code 15948) as ETRS89 may be considered equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4314','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','BETA2007.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Deu BeTA2007',0);
INSERT INTO "usage" VALUES('EPSG','11960','grid_transformation','EPSG','15949','EPSG','3339','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15954','RD/83 to WGS 84 (1)','Parameter values from taken DHDN to ETRS89 (8) (code 15948) as RD/83 and ETRS89 may be considered equivalent to DHDN and WGS 84 respectively within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4745','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','BETA2007.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Deu BeTA2007',0);
INSERT INTO "usage" VALUES('EPSG','11965','grid_transformation','EPSG','15954','EPSG','2545','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15955','PD/83 to WGS 84 (1)','Parameter values taken from DHDN to ETRS89 (8) (code 15948) as PD/83 and ETRS89 may be considered equivalent to DHDN and WGS 84 respectively within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4746','EPSG','4326',1.0,'EPSG','8656','Latitude and longitude difference file','BETA2007.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Deu BeTA2007',0);
INSERT INTO "usage" VALUES('EPSG','11966','grid_transformation','EPSG','15955','EPSG','2544','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15958','RGF93 v1 to NTF (2)','Emulation using NTv2 method of transformation NTF to RGF93 v1 (1), code 9327. Note that grid file parameters are of opposite sign. May be taken as approximate transformation to ETRS89 or WGS 84 - see tfm codes 15959 and 15960.','EPSG','9615','NTv2','EPSG','4171','EPSG','4275',1.0,'EPSG','8656','Latitude and longitude difference file','rgf93_ntf.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ESRI-Fra 1m emulation',0);
INSERT INTO "usage" VALUES('EPSG','11969','grid_transformation','EPSG','15958','EPSG','3694','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','15959','ETRS89 to NTF (3)','These parameter values are taken from RGF93 to NTF (2) (code 15958) as RGF93 may be considered equivalent to ETRS89 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4258','EPSG','4275',1.0,'EPSG','8656','Latitude and longitude difference file','rgf93_ntf.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Fra 1m emulation',0);
INSERT INTO "usage" VALUES('EPSG','11970','grid_transformation','EPSG','15959','EPSG','3694','EPSG','1041');
INSERT INTO "grid_transformation" VALUES('EPSG','15960','WGS 84 to NTF (3)','These parameter values are taken from RGF93 to NTF (2) (code 15958) as RGF93 may be considered equivalent to WGS 84 within the accuracy of the transformation.','EPSG','9615','NTv2','EPSG','4326','EPSG','4275',1.0,'EPSG','8656','Latitude and longitude difference file','rgf93_ntf.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Fra 1m emulation',0);
INSERT INTO "usage" VALUES('EPSG','11971','grid_transformation','EPSG','15960','EPSG','3694','EPSG','1252');
INSERT INTO "grid_transformation" VALUES('EPSG','15961','RGNC91-93 to NEA74 Noumea (4)','Emulation using NTv2 method of tfm NEA74 Noumea to RGNC91-93 (3) (code 15943). Note reversal of sign of parameter values in grid file.','EPSG','9615','NTv2','EPSG','4749','EPSG','4644',0.05,'EPSG','8656','Latitude and longitude difference file','RGNC1991_IGN72GrandeTerre.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ESRI-Ncl 0.05m',1);
INSERT INTO "usage" VALUES('EPSG','11972','grid_transformation','EPSG','15961','EPSG','2823','EPSG','1027');
INSERT INTO "grid_transformation" VALUES('EPSG','15962','RGNC91-93 to IGN72 Grande Terre (6)','Emulation using NTv2 method of tfm IGN72 Grande Terre to RGNC91-93 (4) (code 9329). Note reversal sign of of parameter values in grid file.','EPSG','9615','NTv2','EPSG','4749','EPSG','4662',0.1,'EPSG','8656','Latitude and longitude difference file','RGNC1991_IGN72GrandeTerre.gsb',NULL,NULL,NULL,NULL,NULL,NULL,'ESRI-Ncl 0.1m',0);
INSERT INTO "usage" VALUES('EPSG','11973','grid_transformation','EPSG','15962','EPSG','2822','EPSG','1031');
