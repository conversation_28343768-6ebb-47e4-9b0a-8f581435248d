--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "other_transformation" VALUES('EPSG','1258','Bogota 1975 (Bogota) to Bogota 1975 (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4802','EPSG','4218',NULL,'EPSG','8602','Longitude offset',-74.04513,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGAC-Col',1);
INSERT INTO "usage" VALUES('EPSG','8179','other_transformation','EPSG','1258','EPSG','1070','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1259','Lisbon (Lisbon) to Lisbon (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4803','EPSG','4207',NULL,'EPSG','8602','Longitude offset',-9.0754862,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGC-Prt',1);
INSERT INTO "usage" VALUES('EPSG','8180','other_transformation','EPSG','1259','EPSG','1294','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1260','Makassar (Jakarta) to Makassar (1)','','EPSG','9601','Longitude rotation','EPSG','4804','EPSG','4257',0.0,'EPSG','8602','Longitude offset',106.482779,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Idn Sulawesi',0);
INSERT INTO "usage" VALUES('EPSG','8181','other_transformation','EPSG','1260','EPSG','1316','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1261','MGI (Ferro) to MGI (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4805','EPSG','4312',NULL,'EPSG','8602','Longitude offset',-17.4,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'BEV-Aut balk',1);
INSERT INTO "usage" VALUES('EPSG','8182','other_transformation','EPSG','1261','EPSG','1166','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1262','Monte Mario (Rome) to Monte Mario (1)','','EPSG','9601','Longitude rotation','EPSG','4806','EPSG','4265',0.0,'EPSG','8602','Longitude offset',12.27084,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Ita',0);
INSERT INTO "usage" VALUES('EPSG','8183','other_transformation','EPSG','1262','EPSG','3343','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1263','Padang (Jakarta) to Padang (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4808','EPSG','4280',NULL,'EPSG','8602','Longitude offset',106.482779,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Idn Sumatra',1);
INSERT INTO "usage" VALUES('EPSG','8184','other_transformation','EPSG','1263','EPSG','1355','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1264','BD50 (Brussels) to BD50 (1)','','EPSG','9601','Longitude rotation','EPSG','4809','EPSG','4215',0.0,'EPSG','8602','Longitude offset',4.220471,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Bel',0);
INSERT INTO "usage" VALUES('EPSG','8185','other_transformation','EPSG','1264','EPSG','1347','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1265','Tananarive (Paris) to Tananarive (1)','','EPSG','9601','Longitude rotation','EPSG','4810','EPSG','4297',0.0,'EPSG','8602','Longitude offset',2.5969213,'EPSG','9105',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Mdg',0);
INSERT INTO "usage" VALUES('EPSG','8186','other_transformation','EPSG','1265','EPSG','3273','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1266','Voirol 1875 (Paris) to Voirol 1875 (1)','','EPSG','9601','Longitude rotation','EPSG','4811','EPSG','4304',0.0,'EPSG','8602','Longitude offset',2.5969213,'EPSG','9105',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Dza',0);
INSERT INTO "usage" VALUES('EPSG','8187','other_transformation','EPSG','1266','EPSG','1365','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1268','Batavia (Jakarta) to Batavia (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4813','EPSG','4211',NULL,'EPSG','8602','Longitude offset',106.482779,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Idn Java',1);
INSERT INTO "usage" VALUES('EPSG','8189','other_transformation','EPSG','1268','EPSG','1285','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1269','RT38 (Stockholm) to RT38 (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4814','EPSG','4308',NULL,'EPSG','8602','Longitude offset',18.03298,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'NLS-Swe',1);
INSERT INTO "usage" VALUES('EPSG','8190','other_transformation','EPSG','1269','EPSG','1225','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1270','Greek (Athens) to Greek (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4815','EPSG','4120',NULL,'EPSG','8602','Longitude offset',23.4258815,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'NTU-Grc',1);
INSERT INTO "usage" VALUES('EPSG','8191','other_transformation','EPSG','1270','EPSG','1106','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1335','Tokyo to WGS 84 (6)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','4301','EPSG','4326',2.0,'EPSG','8601','Latitude offset',7.92,'EPSG','9108','EPSG','8602','Longitude offset',-13.88,'EPSG','9104','EPSG','8604','Geoid undulation',26.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 452141',1);
INSERT INTO "usage" VALUES('EPSG','8256','other_transformation','EPSG','1335','EPSG','2425','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1336','Tokyo + JSLD to WGS 84 (7)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',7.94,'EPSG','9104','EPSG','8602','Longitude offset',-13.97,'EPSG','9104','EPSG','8604','Geoid undulation',26.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 452142',1);
INSERT INTO "usage" VALUES('EPSG','8257','other_transformation','EPSG','1336','EPSG','2426','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1337','Tokyo + JSLD to WGS 84 (8)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.1,'EPSG','9104','EPSG','8602','Longitude offset',-13.81,'EPSG','9104','EPSG','8604','Geoid undulation',27.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 444141',1);
INSERT INTO "usage" VALUES('EPSG','8258','other_transformation','EPSG','1337','EPSG','2427','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1338','Tokyo + JSLD to WGS 84 (9)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.15,'EPSG','9104','EPSG','8602','Longitude offset',-13.95,'EPSG','9104','EPSG','8604','Geoid undulation',28.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 444142',1);
INSERT INTO "usage" VALUES('EPSG','8259','other_transformation','EPSG','1338','EPSG','2428','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1339','Tokyo + JSLD to WGS 84 (10)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.37,'EPSG','9104','EPSG','8602','Longitude offset',-13.65,'EPSG','9104','EPSG','8604','Geoid undulation',29.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440141',1);
INSERT INTO "usage" VALUES('EPSG','8260','other_transformation','EPSG','1339','EPSG','2429','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1340','Tokyo + JSLD to WGS 84 (11)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.44,'EPSG','9104','EPSG','8602','Longitude offset',-13.87,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440142',1);
INSERT INTO "usage" VALUES('EPSG','8261','other_transformation','EPSG','1340','EPSG','2430','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1341','Tokyo + JSLD to WGS 84 (12)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.61,'EPSG','9104','EPSG','8602','Longitude offset',-14.08,'EPSG','9104','EPSG','8604','Geoid undulation',30.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440143',1);
INSERT INTO "usage" VALUES('EPSG','8262','other_transformation','EPSG','1341','EPSG','2431','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1342','Tokyo + JSLD to WGS 84 (13)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.73,'EPSG','9104','EPSG','8602','Longitude offset',-14.3,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440144',1);
INSERT INTO "usage" VALUES('EPSG','8263','other_transformation','EPSG','1342','EPSG','2432','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1343','Tokyo + JSLD to WGS 84 (14)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.63,'EPSG','9104','EPSG','8602','Longitude offset',-13.49,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432141',1);
INSERT INTO "usage" VALUES('EPSG','8264','other_transformation','EPSG','1343','EPSG','2433','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1344','Tokyo + JSLD to WGS 84 (15)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.71,'EPSG','9104','EPSG','8602','Longitude offset',-13.73,'EPSG','9104','EPSG','8604','Geoid undulation',31.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432142',1);
INSERT INTO "usage" VALUES('EPSG','8265','other_transformation','EPSG','1344','EPSG','2434','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1345','Tokyo + JSLD to WGS 84 (16)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.84,'EPSG','9104','EPSG','8602','Longitude offset',-14.03,'EPSG','9104','EPSG','8604','Geoid undulation',31.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432143',1);
INSERT INTO "usage" VALUES('EPSG','8266','other_transformation','EPSG','1345','EPSG','2435','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1346','Tokyo + JSLD to WGS 84 (17)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.98,'EPSG','9104','EPSG','8602','Longitude offset',-14.33,'EPSG','9104','EPSG','8604','Geoid undulation',32.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432144',1);
INSERT INTO "usage" VALUES('EPSG','8267','other_transformation','EPSG','1346','EPSG','2436','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1347','Tokyo + JSLD to WGS 84 (18)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.1,'EPSG','9104','EPSG','8602','Longitude offset',-14.56,'EPSG','9104','EPSG','8604','Geoid undulation',32.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432145',1);
INSERT INTO "usage" VALUES('EPSG','8268','other_transformation','EPSG','1347','EPSG','2437','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1348','Tokyo + JSLD to WGS 84 (19)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.79,'EPSG','9104','EPSG','8602','Longitude offset',-13.0,'EPSG','9104','EPSG','8604','Geoid undulation',33.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424140',1);
INSERT INTO "usage" VALUES('EPSG','8269','other_transformation','EPSG','1348','EPSG','2438','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1349','Tokyo + JSLD to WGS 84 (20)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.84,'EPSG','9104','EPSG','8602','Longitude offset',-13.31,'EPSG','9104','EPSG','8604','Geoid undulation',31.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424141',1);
INSERT INTO "usage" VALUES('EPSG','8270','other_transformation','EPSG','1349','EPSG','2439','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1350','Tokyo + JSLD to WGS 84 (21)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.98,'EPSG','9104','EPSG','8602','Longitude offset',-13.59,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424142',1);
INSERT INTO "usage" VALUES('EPSG','8271','other_transformation','EPSG','1350','EPSG','2440','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1351','Tokyo + JSLD to WGS 84 (22)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.1,'EPSG','9104','EPSG','8602','Longitude offset',-13.91,'EPSG','9104','EPSG','8604','Geoid undulation',29.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424143',1);
INSERT INTO "usage" VALUES('EPSG','8272','other_transformation','EPSG','1351','EPSG','2441','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1352','Tokyo + JSLD to WGS 84 (23)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.17,'EPSG','9104','EPSG','8602','Longitude offset',-14.27,'EPSG','9104','EPSG','8604','Geoid undulation',31.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424144',1);
INSERT INTO "usage" VALUES('EPSG','8273','other_transformation','EPSG','1352','EPSG','2442','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1353','Tokyo + JSLD to WGS 84 (24)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.23,'EPSG','9104','EPSG','8602','Longitude offset',-14.52,'EPSG','9104','EPSG','8604','Geoid undulation',31.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424145',1);
INSERT INTO "usage" VALUES('EPSG','8274','other_transformation','EPSG','1353','EPSG','2443','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1354','Tokyo + JSLD to WGS 84 (25)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.9,'EPSG','9104','EPSG','8602','Longitude offset',-12.68,'EPSG','9104','EPSG','8604','Geoid undulation',34.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420139',1);
INSERT INTO "usage" VALUES('EPSG','8275','other_transformation','EPSG','1354','EPSG','2444','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1355','Tokyo + JSLD to WGS 84 (26)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',8.99,'EPSG','9104','EPSG','8602','Longitude offset',-12.8,'EPSG','9104','EPSG','8604','Geoid undulation',34.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420140',1);
INSERT INTO "usage" VALUES('EPSG','8276','other_transformation','EPSG','1355','EPSG','2445','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1356','Tokyo + JSLD to WGS 84 (27)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.0,'EPSG','9104','EPSG','8602','Longitude offset',-13.07,'EPSG','9104','EPSG','8604','Geoid undulation',31.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420141',1);
INSERT INTO "usage" VALUES('EPSG','8277','other_transformation','EPSG','1356','EPSG','2446','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1357','Tokyo + JSLD to WGS 84 (28)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.21,'EPSG','9104','EPSG','8602','Longitude offset',-13.51,'EPSG','9104','EPSG','8604','Geoid undulation',27.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420142',1);
INSERT INTO "usage" VALUES('EPSG','8278','other_transformation','EPSG','1357','EPSG','2447','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1358','Tokyo + JSLD to WGS 84 (29)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.33,'EPSG','9104','EPSG','8602','Longitude offset',-13.66,'EPSG','9104','EPSG','8604','Geoid undulation',23.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420143',1);
INSERT INTO "usage" VALUES('EPSG','8279','other_transformation','EPSG','1358','EPSG','2448','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1359','Tokyo + JSLD to WGS 84 (30)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.25,'EPSG','9104','EPSG','8602','Longitude offset',-12.72,'EPSG','9104','EPSG','8604','Geoid undulation',34.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 412140',1);
INSERT INTO "usage" VALUES('EPSG','8280','other_transformation','EPSG','1359','EPSG','2449','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1360','Tokyo + JSLD to WGS 84 (31)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.39,'EPSG','9104','EPSG','8602','Longitude offset',-12.91,'EPSG','9104','EPSG','8604','Geoid undulation',31.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 412141',1);
INSERT INTO "usage" VALUES('EPSG','8281','other_transformation','EPSG','1360','EPSG','2450','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1361','Tokyo + JSLD to WGS 84 (32)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.55,'EPSG','9104','EPSG','8602','Longitude offset',-12.63,'EPSG','9104','EPSG','8604','Geoid undulation',35.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 404140',1);
INSERT INTO "usage" VALUES('EPSG','8282','other_transformation','EPSG','1361','EPSG','2451','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1362','Tokyo + JSLD to WGS 84 (33)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.62,'EPSG','9104','EPSG','8602','Longitude offset',-12.82,'EPSG','9104','EPSG','8604','Geoid undulation',34.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 404141',1);
INSERT INTO "usage" VALUES('EPSG','8283','other_transformation','EPSG','1362','EPSG','2452','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1363','Tokyo + JSLD to WGS 84 (34)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.81,'EPSG','9104','EPSG','8602','Longitude offset',-12.29,'EPSG','9104','EPSG','8604','Geoid undulation',36.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 400139',1);
INSERT INTO "usage" VALUES('EPSG','8284','other_transformation','EPSG','1363','EPSG','2453','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1364','Tokyo + JSLD to WGS 84 (35)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.81,'EPSG','9104','EPSG','8602','Longitude offset',-12.45,'EPSG','9104','EPSG','8604','Geoid undulation',37.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 400140',1);
INSERT INTO "usage" VALUES('EPSG','8285','other_transformation','EPSG','1364','EPSG','2454','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1365','Tokyo + JSLD to WGS 84 (36)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.92,'EPSG','9104','EPSG','8602','Longitude offset',-12.79,'EPSG','9104','EPSG','8604','Geoid undulation',38.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 400141',1);
INSERT INTO "usage" VALUES('EPSG','8286','other_transformation','EPSG','1365','EPSG','2455','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1366','Tokyo + JSLD to WGS 84 (37)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',9.91,'EPSG','9104','EPSG','8602','Longitude offset',-12.21,'EPSG','9104','EPSG','8604','Geoid undulation',36.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 392139',1);
INSERT INTO "usage" VALUES('EPSG','8287','other_transformation','EPSG','1366','EPSG','2456','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1367','Tokyo + JSLD to WGS 84 (38)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.08,'EPSG','9104','EPSG','8602','Longitude offset',-12.35,'EPSG','9104','EPSG','8604','Geoid undulation',39.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 392140',1);
INSERT INTO "usage" VALUES('EPSG','8288','other_transformation','EPSG','1367','EPSG','2457','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1368','Tokyo + JSLD to WGS 84 (39)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.19,'EPSG','9104','EPSG','8602','Longitude offset',-12.74,'EPSG','9104','EPSG','8604','Geoid undulation',40.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 392141',1);
INSERT INTO "usage" VALUES('EPSG','8289','other_transformation','EPSG','1368','EPSG','2458','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1369','Tokyo + JSLD to WGS 84 (40)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.29,'EPSG','9104','EPSG','8602','Longitude offset',-12.13,'EPSG','9104','EPSG','8604','Geoid undulation',38.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 384139',1);
INSERT INTO "usage" VALUES('EPSG','8290','other_transformation','EPSG','1369','EPSG','2459','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1370','Tokyo + JSLD to WGS 84 (41)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.33,'EPSG','9104','EPSG','8602','Longitude offset',-12.27,'EPSG','9104','EPSG','8604','Geoid undulation',40.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 384140',1);
INSERT INTO "usage" VALUES('EPSG','8291','other_transformation','EPSG','1370','EPSG','2460','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1371','Tokyo + JSLD to WGS 84 (42)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.45,'EPSG','9104','EPSG','8602','Longitude offset',-12.61,'EPSG','9104','EPSG','8604','Geoid undulation',41.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 384141',1);
INSERT INTO "usage" VALUES('EPSG','8292','other_transformation','EPSG','1371','EPSG','2461','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1372','Tokyo + JSLD to WGS 84 (43)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.54,'EPSG','9104','EPSG','8602','Longitude offset',-11.96,'EPSG','9104','EPSG','8604','Geoid undulation',39.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 380139',1);
INSERT INTO "usage" VALUES('EPSG','8293','other_transformation','EPSG','1372','EPSG','2462','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1373','Tokyo + JSLD to WGS 84 (44)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.65,'EPSG','9104','EPSG','8602','Longitude offset',-12.27,'EPSG','9104','EPSG','8604','Geoid undulation',41.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 380140',1);
INSERT INTO "usage" VALUES('EPSG','8294','other_transformation','EPSG','1373','EPSG','2463','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1374','Tokyo + JSLD to WGS 84 (45)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.67,'EPSG','9104','EPSG','8602','Longitude offset',-12.5,'EPSG','9104','EPSG','8604','Geoid undulation',41.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 380141',1);
INSERT INTO "usage" VALUES('EPSG','8295','other_transformation','EPSG','1374','EPSG','2464','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1375','Tokyo + JSLD to WGS 84 (46)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.67,'EPSG','9104','EPSG','8602','Longitude offset',-10.86,'EPSG','9104','EPSG','8604','Geoid undulation',38.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372136',1);
INSERT INTO "usage" VALUES('EPSG','8296','other_transformation','EPSG','1375','EPSG','2465','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1376','Tokyo + JSLD to WGS 84 (47)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.68,'EPSG','9104','EPSG','8602','Longitude offset',-10.97,'EPSG','9104','EPSG','8604','Geoid undulation',36.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372137',1);
INSERT INTO "usage" VALUES('EPSG','8297','other_transformation','EPSG','1376','EPSG','2466','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1377','Tokyo + JSLD to WGS 84 (48)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.8,'EPSG','9104','EPSG','8602','Longitude offset',-11.53,'EPSG','9104','EPSG','8604','Geoid undulation',39.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372138',1);
INSERT INTO "usage" VALUES('EPSG','8298','other_transformation','EPSG','1377','EPSG','2467','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1378','Tokyo + JSLD to WGS 84 (49)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.8,'EPSG','9104','EPSG','8602','Longitude offset',-11.73,'EPSG','9104','EPSG','8604','Geoid undulation',40.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372139',1);
INSERT INTO "usage" VALUES('EPSG','8299','other_transformation','EPSG','1378','EPSG','2468','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1379','Tokyo + JSLD to WGS 84 (50)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.92,'EPSG','9104','EPSG','8602','Longitude offset',-12.16,'EPSG','9104','EPSG','8604','Geoid undulation',42.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372140',1);
INSERT INTO "usage" VALUES('EPSG','8300','other_transformation','EPSG','1379','EPSG','2469','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1380','Tokyo + JSLD to WGS 84 (51)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.0,'EPSG','9104','EPSG','8602','Longitude offset',-12.25,'EPSG','9104','EPSG','8604','Geoid undulation',41.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372141',1);
INSERT INTO "usage" VALUES('EPSG','8301','other_transformation','EPSG','1380','EPSG','2470','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1381','Tokyo + JSLD to WGS 84 (52)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.83,'EPSG','9104','EPSG','8602','Longitude offset',-10.77,'EPSG','9104','EPSG','8604','Geoid undulation',36.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364136',1);
INSERT INTO "usage" VALUES('EPSG','8302','other_transformation','EPSG','1381','EPSG','2471','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1382','Tokyo + JSLD to WGS 84 (53)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.95,'EPSG','9104','EPSG','8602','Longitude offset',-11.0,'EPSG','9104','EPSG','8604','Geoid undulation',38.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364137',1);
INSERT INTO "usage" VALUES('EPSG','8303','other_transformation','EPSG','1382','EPSG','2472','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1383','Tokyo + JSLD to WGS 84 (54)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',10.97,'EPSG','9104','EPSG','8602','Longitude offset',-11.34,'EPSG','9104','EPSG','8604','Geoid undulation',40.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364138',1);
INSERT INTO "usage" VALUES('EPSG','8304','other_transformation','EPSG','1383','EPSG','2473','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1384','Tokyo + JSLD to WGS 84 (55)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.04,'EPSG','9104','EPSG','8602','Longitude offset',-11.69,'EPSG','9104','EPSG','8604','Geoid undulation',43.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364139',1);
INSERT INTO "usage" VALUES('EPSG','8305','other_transformation','EPSG','1384','EPSG','2474','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1385','Tokyo + JSLD to WGS 84 (56)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.17,'EPSG','9104','EPSG','8602','Longitude offset',-12.05,'EPSG','9104','EPSG','8604','Geoid undulation',42.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364140',1);
INSERT INTO "usage" VALUES('EPSG','8306','other_transformation','EPSG','1385','EPSG','2475','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1386','Tokyo + JSLD to WGS 84 (57)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.11,'EPSG','9104','EPSG','8602','Longitude offset',-10.59,'EPSG','9104','EPSG','8604','Geoid undulation',37.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360136',1);
INSERT INTO "usage" VALUES('EPSG','8307','other_transformation','EPSG','1386','EPSG','2476','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1387','Tokyo + JSLD to WGS 84 (58)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.16,'EPSG','9104','EPSG','8602','Longitude offset',-10.97,'EPSG','9104','EPSG','8604','Geoid undulation',40.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360137',1);
INSERT INTO "usage" VALUES('EPSG','8308','other_transformation','EPSG','1387','EPSG','2477','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1388','Tokyo + JSLD to WGS 84 (59)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.29,'EPSG','9104','EPSG','8602','Longitude offset',-11.23,'EPSG','9104','EPSG','8604','Geoid undulation',42.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360138',1);
INSERT INTO "usage" VALUES('EPSG','8309','other_transformation','EPSG','1388','EPSG','2478','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1389','Tokyo + JSLD to WGS 84 (60)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.36,'EPSG','9104','EPSG','8602','Longitude offset',-11.59,'EPSG','9104','EPSG','8604','Geoid undulation',42.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360139',1);
INSERT INTO "usage" VALUES('EPSG','8310','other_transformation','EPSG','1389','EPSG','2479','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1390','Tokyo + JSLD to WGS 84 (61)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.44,'EPSG','9104','EPSG','8602','Longitude offset',-11.88,'EPSG','9104','EPSG','8604','Geoid undulation',40.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360140',1);
INSERT INTO "usage" VALUES('EPSG','8311','other_transformation','EPSG','1390','EPSG','2480','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1391','Tokyo + JSLD to WGS 84 (62)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.27,'EPSG','9104','EPSG','8602','Longitude offset',-9.31,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352132',1);
INSERT INTO "usage" VALUES('EPSG','8312','other_transformation','EPSG','1391','EPSG','2481','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1392','Tokyo + JSLD to WGS 84 (63)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.33,'EPSG','9104','EPSG','8602','Longitude offset',-9.52,'EPSG','9104','EPSG','8604','Geoid undulation',33.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352133',1);
INSERT INTO "usage" VALUES('EPSG','8313','other_transformation','EPSG','1392','EPSG','2482','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1393','Tokyo + JSLD to WGS 84 (64)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.38,'EPSG','9104','EPSG','8602','Longitude offset',-9.86,'EPSG','9104','EPSG','8604','Geoid undulation',34.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352134',1);
INSERT INTO "usage" VALUES('EPSG','8314','other_transformation','EPSG','1393','EPSG','2483','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1394','Tokyo + JSLD to WGS 84 (65)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.41,'EPSG','9104','EPSG','8602','Longitude offset',-10.14,'EPSG','9104','EPSG','8604','Geoid undulation',35.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352135',1);
INSERT INTO "usage" VALUES('EPSG','8315','other_transformation','EPSG','1394','EPSG','2484','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1395','Tokyo + JSLD to WGS 84 (66)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.39,'EPSG','9104','EPSG','8602','Longitude offset',-10.52,'EPSG','9104','EPSG','8604','Geoid undulation',37.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352136',1);
INSERT INTO "usage" VALUES('EPSG','8316','other_transformation','EPSG','1395','EPSG','2485','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1396','Tokyo + JSLD to WGS 84 (67)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.49,'EPSG','9104','EPSG','8602','Longitude offset',-10.83,'EPSG','9104','EPSG','8604','Geoid undulation',39.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352137',1);
INSERT INTO "usage" VALUES('EPSG','8317','other_transformation','EPSG','1396','EPSG','2486','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1397','Tokyo + JSLD to WGS 84 (68)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.58,'EPSG','9104','EPSG','8602','Longitude offset',-11.21,'EPSG','9104','EPSG','8604','Geoid undulation',41.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352138',1);
INSERT INTO "usage" VALUES('EPSG','8318','other_transformation','EPSG','1397','EPSG','2487','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1398','Tokyo + JSLD to WGS 84 (69)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.65,'EPSG','9104','EPSG','8602','Longitude offset',-11.53,'EPSG','9104','EPSG','8604','Geoid undulation',38.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352139',1);
INSERT INTO "usage" VALUES('EPSG','8319','other_transformation','EPSG','1398','EPSG','2488','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1399','Tokyo + JSLD to WGS 84 (70)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.72,'EPSG','9104','EPSG','8602','Longitude offset',-11.8,'EPSG','9104','EPSG','8604','Geoid undulation',34.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352140',1);
INSERT INTO "usage" VALUES('EPSG','8320','other_transformation','EPSG','1399','EPSG','2489','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1400','Tokyo + JSLD to WGS 84 (71)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.44,'EPSG','9104','EPSG','8602','Longitude offset',-9.21,'EPSG','9104','EPSG','8604','Geoid undulation',32.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344132',1);
INSERT INTO "usage" VALUES('EPSG','8321','other_transformation','EPSG','1400','EPSG','2490','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1401','Tokyo + JSLD to WGS 84 (72)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.47,'EPSG','9104','EPSG','8602','Longitude offset',-9.52,'EPSG','9104','EPSG','8604','Geoid undulation',35.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344133',1);
INSERT INTO "usage" VALUES('EPSG','8322','other_transformation','EPSG','1401','EPSG','2491','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1402','Tokyo + JSLD to WGS 84 (73)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.55,'EPSG','9104','EPSG','8602','Longitude offset',-9.8,'EPSG','9104','EPSG','8604','Geoid undulation',35.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344134',1);
INSERT INTO "usage" VALUES('EPSG','8323','other_transformation','EPSG','1402','EPSG','2492','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1403','Tokyo + JSLD to WGS 84 (74)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.61,'EPSG','9104','EPSG','8602','Longitude offset',-10.12,'EPSG','9104','EPSG','8604','Geoid undulation',35.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344135',1);
INSERT INTO "usage" VALUES('EPSG','8324','other_transformation','EPSG','1403','EPSG','2493','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1404','Tokyo + JSLD to WGS 84 (75)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.66,'EPSG','9104','EPSG','8602','Longitude offset',-10.47,'EPSG','9104','EPSG','8604','Geoid undulation',37.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344136',1);
INSERT INTO "usage" VALUES('EPSG','8325','other_transformation','EPSG','1404','EPSG','2494','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1405','Tokyo + JSLD to WGS 84 (76)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.78,'EPSG','9104','EPSG','8602','Longitude offset',-10.79,'EPSG','9104','EPSG','8604','Geoid undulation',39.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344137',1);
INSERT INTO "usage" VALUES('EPSG','8326','other_transformation','EPSG','1405','EPSG','2495','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1406','Tokyo + JSLD to WGS 84 (77)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.85,'EPSG','9104','EPSG','8602','Longitude offset',-11.13,'EPSG','9104','EPSG','8604','Geoid undulation',39.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344138',1);
INSERT INTO "usage" VALUES('EPSG','8327','other_transformation','EPSG','1406','EPSG','2496','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1407','Tokyo + JSLD to WGS 84 (78)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.9,'EPSG','9104','EPSG','8602','Longitude offset',-11.47,'EPSG','9104','EPSG','8604','Geoid undulation',36.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344139',1);
INSERT INTO "usage" VALUES('EPSG','8328','other_transformation','EPSG','1407','EPSG','2497','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1408','Tokyo + JSLD to WGS 84 (79)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.91,'EPSG','9104','EPSG','8602','Longitude offset',-11.69,'EPSG','9104','EPSG','8604','Geoid undulation',33.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344140',1);
INSERT INTO "usage" VALUES('EPSG','8329','other_transformation','EPSG','1408','EPSG','2498','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1409','Tokyo + JSLD to WGS 84 (80)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.65,'EPSG','9104','EPSG','8602','Longitude offset',-8.59,'EPSG','9104','EPSG','8604','Geoid undulation',29.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340130',1);
INSERT INTO "usage" VALUES('EPSG','8330','other_transformation','EPSG','1409','EPSG','2499','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1410','Tokyo + JSLD to WGS 84 (81)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.68,'EPSG','9104','EPSG','8602','Longitude offset',-8.8,'EPSG','9104','EPSG','8604','Geoid undulation',30.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340131',1);
INSERT INTO "usage" VALUES('EPSG','8331','other_transformation','EPSG','1410','EPSG','2500','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1411','Tokyo + JSLD to WGS 84 (82)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.73,'EPSG','9104','EPSG','8602','Longitude offset',-9.04,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340132',1);
INSERT INTO "usage" VALUES('EPSG','8332','other_transformation','EPSG','1411','EPSG','2501','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1412','Tokyo + JSLD to WGS 84 (83)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.72,'EPSG','9104','EPSG','8602','Longitude offset',-9.48,'EPSG','9104','EPSG','8604','Geoid undulation',35.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340133',1);
INSERT INTO "usage" VALUES('EPSG','8333','other_transformation','EPSG','1412','EPSG','2502','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1413','Tokyo + JSLD to WGS 84 (84)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.81,'EPSG','9104','EPSG','8602','Longitude offset',9.74,'EPSG','9104','EPSG','8604','Geoid undulation',35.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340134',1);
INSERT INTO "usage" VALUES('EPSG','8334','other_transformation','EPSG','1413','EPSG','2503','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1414','Tokyo + JSLD to WGS 84 (85)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.88,'EPSG','9104','EPSG','8602','Longitude offset',-10.1,'EPSG','9104','EPSG','8604','Geoid undulation',37.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340135',1);
INSERT INTO "usage" VALUES('EPSG','8335','other_transformation','EPSG','1414','EPSG','2504','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1415','Tokyo + JSLD to WGS 84 (86)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.91,'EPSG','9104','EPSG','8602','Longitude offset',-10.35,'EPSG','9104','EPSG','8604','Geoid undulation',37.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340136',1);
INSERT INTO "usage" VALUES('EPSG','8336','other_transformation','EPSG','1415','EPSG','2505','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1416','Tokyo + JSLD to WGS 84 (87)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.9,'EPSG','9104','EPSG','8602','Longitude offset',-10.7,'EPSG','9104','EPSG','8604','Geoid undulation',39.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340137',1);
INSERT INTO "usage" VALUES('EPSG','8337','other_transformation','EPSG','1416','EPSG','2506','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1417','Tokyo + JSLD to WGS 84 (88)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.02,'EPSG','9104','EPSG','8602','Longitude offset',-11.09,'EPSG','9104','EPSG','8604','Geoid undulation',38.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340138',1);
INSERT INTO "usage" VALUES('EPSG','8338','other_transformation','EPSG','1417','EPSG','2507','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1418','Tokyo + JSLD to WGS 84 (89)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.87,'EPSG','9104','EPSG','8602','Longitude offset',-8.23,'EPSG','9104','EPSG','8604','Geoid undulation',29.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332129',1);
INSERT INTO "usage" VALUES('EPSG','8339','other_transformation','EPSG','1418','EPSG','2508','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1419','Tokyo + JSLD to WGS 84 (90)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.84,'EPSG','9104','EPSG','8602','Longitude offset',-8.44,'EPSG','9104','EPSG','8604','Geoid undulation',30.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332130',1);
INSERT INTO "usage" VALUES('EPSG','8340','other_transformation','EPSG','1419','EPSG','2509','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1420','Tokyo + JSLD to WGS 84 (91)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.94,'EPSG','9104','EPSG','8602','Longitude offset',-8.71,'EPSG','9104','EPSG','8604','Geoid undulation',30.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332131',1);
INSERT INTO "usage" VALUES('EPSG','8341','other_transformation','EPSG','1420','EPSG','2510','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1421','Tokyo + JSLD to WGS 84 (92)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',11.99,'EPSG','9104','EPSG','8602','Longitude offset',-9.02,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332132',1);
INSERT INTO "usage" VALUES('EPSG','8342','other_transformation','EPSG','1421','EPSG','2511','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1422','Tokyo + JSLD to WGS 84 (93)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.05,'EPSG','9104','EPSG','8602','Longitude offset',-9.36,'EPSG','9104','EPSG','8604','Geoid undulation',35.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332133',1);
INSERT INTO "usage" VALUES('EPSG','8343','other_transformation','EPSG','1422','EPSG','2512','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1423','Tokyo + JSLD to WGS 84 (94)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.1,'EPSG','9104','EPSG','8602','Longitude offset',-9.64,'EPSG','9104','EPSG','8604','Geoid undulation',35.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332134',1);
INSERT INTO "usage" VALUES('EPSG','8344','other_transformation','EPSG','1423','EPSG','2513','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1424','Tokyo + JSLD to WGS 84 (95)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.1,'EPSG','9104','EPSG','8602','Longitude offset',-10.08,'EPSG','9104','EPSG','8604','Geoid undulation',37.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332135',1);
INSERT INTO "usage" VALUES('EPSG','8345','other_transformation','EPSG','1424','EPSG','2514','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1425','Tokyo + JSLD to WGS 84 (96)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.07,'EPSG','9104','EPSG','8602','Longitude offset',-10.25,'EPSG','9104','EPSG','8604','Geoid undulation',37.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332136',1);
INSERT INTO "usage" VALUES('EPSG','8346','other_transformation','EPSG','1425','EPSG','2515','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1426','Tokyo + JSLD to WGS 84 (97)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.0,'EPSG','9104','EPSG','8602','Longitude offset',-8.15,'EPSG','9104','EPSG','8604','Geoid undulation',32.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324129',1);
INSERT INTO "usage" VALUES('EPSG','8347','other_transformation','EPSG','1426','EPSG','2516','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1427','Tokyo + JSLD to WGS 84 (98)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.06,'EPSG','9104','EPSG','8602','Longitude offset',-8.38,'EPSG','9104','EPSG','8604','Geoid undulation',31.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324130',1);
INSERT INTO "usage" VALUES('EPSG','8348','other_transformation','EPSG','1427','EPSG','2517','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1428','Tokyo + JSLD to WGS 84 (99)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.17,'EPSG','9104','EPSG','8602','Longitude offset',-8.69,'EPSG','9104','EPSG','8604','Geoid undulation',30.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324131',1);
INSERT INTO "usage" VALUES('EPSG','8349','other_transformation','EPSG','1428','EPSG','2518','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1429','Tokyo + JSLD to WGS 84 (100)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.23,'EPSG','9104','EPSG','8602','Longitude offset',-8.99,'EPSG','9104','EPSG','8604','Geoid undulation',31.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324132',1);
INSERT INTO "usage" VALUES('EPSG','8350','other_transformation','EPSG','1429','EPSG','2519','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1430','Tokyo + JSLD to WGS 84 (101)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.21,'EPSG','9104','EPSG','8602','Longitude offset',-9.21,'EPSG','9104','EPSG','8604','Geoid undulation',34.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324133',1);
INSERT INTO "usage" VALUES('EPSG','8351','other_transformation','EPSG','1430','EPSG','2520','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1431','Tokyo + JSLD to WGS 84 (102)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.28,'EPSG','9104','EPSG','8602','Longitude offset',-9.6,'EPSG','9104','EPSG','8604','Geoid undulation',33.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324134',1);
INSERT INTO "usage" VALUES('EPSG','8352','other_transformation','EPSG','1431','EPSG','2521','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1432','Tokyo + JSLD to WGS 84 (103)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.28,'EPSG','9104','EPSG','8602','Longitude offset',-8.25,'EPSG','9104','EPSG','8604','Geoid undulation',31.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 320130',1);
INSERT INTO "usage" VALUES('EPSG','8353','other_transformation','EPSG','1432','EPSG','2522','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1433','Tokyo + JSLD to WGS 84 (104)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.37,'EPSG','9104','EPSG','8602','Longitude offset',-8.55,'EPSG','9104','EPSG','8604','Geoid undulation',29.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 320131',1);
INSERT INTO "usage" VALUES('EPSG','8354','other_transformation','EPSG','1433','EPSG','2523','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1434','Tokyo + JSLD to WGS 84 (105)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.53,'EPSG','9104','EPSG','8602','Longitude offset',-8.21,'EPSG','9104','EPSG','8604','Geoid undulation',31.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 320132',1);
INSERT INTO "usage" VALUES('EPSG','8355','other_transformation','EPSG','1434','EPSG','2524','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1435','Tokyo + JSLD to WGS 84 (106)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.57,'EPSG','9104','EPSG','8602','Longitude offset',-8.4,'EPSG','9104','EPSG','8604','Geoid undulation',28.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 312130',1);
INSERT INTO "usage" VALUES('EPSG','8356','other_transformation','EPSG','1435','EPSG','2525','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1436','Tokyo + JSLD to WGS 84 (107)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',12.71,'EPSG','9104','EPSG','8602','Longitude offset',-8.17,'EPSG','9104','EPSG','8604','Geoid undulation',29.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 312131',1);
INSERT INTO "usage" VALUES('EPSG','8357','other_transformation','EPSG','1436','EPSG','2526','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1447','Anguilla 1957 to WGS 84 (1)','','EPSG','9619','Geographic2D offsets','EPSG','4600','EPSG','4326',10.0,'EPSG','8601','Latitude offset',-18.0,'EPSG','9104','EPSG','8602','Longitude offset',4.4,'EPSG','9104',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'DOS-Aia',0);
INSERT INTO "usage" VALUES('EPSG','8368','other_transformation','EPSG','1447','EPSG','3214','EPSG','1024');
INSERT INTO "other_transformation" VALUES('EPSG','1466','NGO 1948 (Oslo) to NGO1948 (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4817','EPSG','4273',NULL,'EPSG','8602','Longitude offset',10.43225,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'NGO-Nor',1);
INSERT INTO "usage" VALUES('EPSG','8387','other_transformation','EPSG','1466','EPSG','1352','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1467','NTF (Paris) to NTF (Greenwich) (1)','','EPSG','9601','Longitude rotation','EPSG','4807','EPSG','4275',NULL,'EPSG','8602','Longitude offset',2.5969213,'EPSG','9105',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Fra',1);
INSERT INTO "usage" VALUES('EPSG','8388','other_transformation','EPSG','1467','EPSG','1096','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1468','NTF (Paris) to NTF (Greenwich) (2)','OGP prefers value from IGN Paris (code 1467).','EPSG','9601','Longitude rotation','EPSG','4807','EPSG','4275',NULL,'EPSG','8602','Longitude offset',2.201395,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'RGS',1);
INSERT INTO "usage" VALUES('EPSG','8389','other_transformation','EPSG','1468','EPSG','1096','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1519','Bern 1898 (Bern) to CH1903 (Greenwich)','','EPSG','9601','Longitude rotation','EPSG','4801','EPSG','4149',NULL,'EPSG','8602','Longitude offset',7.26225,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'BfL-CH',1);
INSERT INTO "usage" VALUES('EPSG','8440','other_transformation','EPSG','1519','EPSG','1286','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1755','Bogota 1975 (Bogota) to Bogota 1975 (1)','','EPSG','9601','Longitude rotation','EPSG','4802','EPSG','4218',0.0,'EPSG','8602','Longitude offset',-74.04513,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGAC-Col',0);
INSERT INTO "usage" VALUES('EPSG','8676','other_transformation','EPSG','1755','EPSG','3229','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1756','Lisbon (Lisbon) to Lisbon (1)','','EPSG','9601','Longitude rotation','EPSG','4803','EPSG','4207',0.0,'EPSG','8602','Longitude offset',-9.0754862,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGC-Prt',0);
INSERT INTO "usage" VALUES('EPSG','8677','other_transformation','EPSG','1756','EPSG','1294','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1757','MGI (Ferro) to MGI (1)','','EPSG','9601','Longitude rotation','EPSG','4805','EPSG','4312',0.0,'EPSG','8602','Longitude offset',-17.4,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'BEV-Aut balk',1);
INSERT INTO "usage" VALUES('EPSG','8678','other_transformation','EPSG','1757','EPSG','1321','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1758','Padang (Jakarta) to Padang (1)','','EPSG','9601','Longitude rotation','EPSG','4808','EPSG','4280',0.0,'EPSG','8602','Longitude offset',106.482779,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Idn Sumatra',1);
INSERT INTO "usage" VALUES('EPSG','8679','other_transformation','EPSG','1758','EPSG','1355','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1759','Batavia (Jakarta) to Batavia (1)','','EPSG','9601','Longitude rotation','EPSG','4813','EPSG','4211',0.0,'EPSG','8602','Longitude offset',106.482779,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Idn Java',0);
INSERT INTO "usage" VALUES('EPSG','8680','other_transformation','EPSG','1759','EPSG','1285','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1760','RT38 (Stockholm) to RT38 (1)','','EPSG','9601','Longitude rotation','EPSG','4814','EPSG','4308',0.0,'EPSG','8602','Longitude offset',18.03298,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'NLS-Swe',0);
INSERT INTO "usage" VALUES('EPSG','8681','other_transformation','EPSG','1760','EPSG','3313','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1761','Greek (Athens) to Greek (1)','','EPSG','9601','Longitude rotation','EPSG','4815','EPSG','4120',0.0,'EPSG','8602','Longitude offset',23.4258815,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'NTU-Grc',0);
INSERT INTO "usage" VALUES('EPSG','8682','other_transformation','EPSG','1761','EPSG','3254','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1762','NGO 1948 (Oslo) to NGO1948 (1)','','EPSG','9601','Longitude rotation','EPSG','4817','EPSG','4273',0.0,'EPSG','8602','Longitude offset',10.43225,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'NGO-Nor',0);
INSERT INTO "usage" VALUES('EPSG','8683','other_transformation','EPSG','1762','EPSG','1352','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1763','NTF (Paris) to NTF (1)','','EPSG','9601','Longitude rotation','EPSG','4807','EPSG','4275',0.0,'EPSG','8602','Longitude offset',2.5969213,'EPSG','9105',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Fra',0);
INSERT INTO "usage" VALUES('EPSG','8684','other_transformation','EPSG','1763','EPSG','3694','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1764','NTF (Paris) to NTF (2)','OGP prefers value from IGN Paris (code 1763).','EPSG','9601','Longitude rotation','EPSG','4807','EPSG','4275',0.0,'EPSG','8602','Longitude offset',2.201395,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'RGS',0);
INSERT INTO "usage" VALUES('EPSG','8685','other_transformation','EPSG','1764','EPSG','3694','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1765','Bern 1898 (Bern) to CH1903 (1)','','EPSG','9601','Longitude rotation','EPSG','4801','EPSG','4149',0.0,'EPSG','8602','Longitude offset',7.26225,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'BfL-CH',0);
INSERT INTO "usage" VALUES('EPSG','8686','other_transformation','EPSG','1765','EPSG','1286','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1827','Tokyo + JSLD to WGS 84 (6)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4326',1.0,'EPSG','8601','Latitude offset',7.92,'EPSG','9104','EPSG','8602','Longitude offset',-13.88,'EPSG','9104','EPSG','8604','Geoid undulation',26.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 452141',1);
INSERT INTO "usage" VALUES('EPSG','8748','other_transformation','EPSG','1827','EPSG','2425','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','1881','Carthage (Paris) to Carthage (1)','','EPSG','9601','Longitude rotation','EPSG','4816','EPSG','4223',0.0,'EPSG','8602','Longitude offset',2.5969213,'EPSG','9105',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Fra',0);
INSERT INTO "usage" VALUES('EPSG','8802','other_transformation','EPSG','1881','EPSG','1618','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1882','Nord Sahara 1959 (Paris) to Nord Sahara 1959 (1)','','EPSG','9601','Longitude rotation','EPSG','4819','EPSG','4307',0.0,'EPSG','8602','Longitude offset',2.5969213,'EPSG','9105',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGN-Fra',1);
INSERT INTO "usage" VALUES('EPSG','8803','other_transformation','EPSG','1882','EPSG','1026','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1883','Segara (Jakarta) to Segara (1)','','EPSG','9601','Longitude rotation','EPSG','4820','EPSG','4613',0.0,'EPSG','8602','Longitude offset',106.482779,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Idn Kal E',0);
INSERT INTO "usage" VALUES('EPSG','8804','other_transformation','EPSG','1883','EPSG','1360','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1884','S-JTSK (Ferro) to S-JTSK (1)','','EPSG','9601','Longitude rotation','EPSG','4818','EPSG','4156',0.0,'EPSG','8602','Longitude offset',-17.4,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EPSG-Cze',0);
INSERT INTO "usage" VALUES('EPSG','8805','other_transformation','EPSG','1884','EPSG','1306','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','1891','Greek to GGRS87 (1)','More accurate polynomial transformations between Greek / Hatt projection zones and GGRS87 / Greek Grid are available from the Military Geographic Service.','EPSG','9619','Geographic2D offsets','EPSG','4120','EPSG','4121',5.0,'EPSG','8601','Latitude offset',-5.86,'EPSG','9104','EPSG','8602','Longitude offset',0.28,'EPSG','9104',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'HGS-Grc',0);
INSERT INTO "usage" VALUES('EPSG','8812','other_transformation','EPSG','1891','EPSG','3254','EPSG','1045');
INSERT INTO "other_transformation" VALUES('EPSG','1991','Lisbon 1890 (Lisbon) to Lisbon 1890 (1)','','EPSG','9601','Longitude rotation','EPSG','4904','EPSG','4666',0.0,'EPSG','8602','Longitude offset',-9.0754862,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'CGC-Prt',0);
INSERT INTO "usage" VALUES('EPSG','8912','other_transformation','EPSG','1991','EPSG','1294','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','3895','MGI (Ferro) to MGI (1)','See tfm code 3913 for longitude rotation applied in former Yugoslavia.','EPSG','9601','Longitude rotation','EPSG','4805','EPSG','4312',0.0,'EPSG','8602','Longitude offset',-17.4,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'BEV-Aut',0);
INSERT INTO "usage" VALUES('EPSG','8964','other_transformation','EPSG','3895','EPSG','1037','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','3913','MGI (Ferro) to MGI 1901 (1)','Uses Albrecht 1902 value. See tfm code 3895 for longitude rotation applied in Austria.','EPSG','9601','Longitude rotation','EPSG','4805','EPSG','3906',1.0,'EPSG','8602','Longitude offset',-17.394602,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Yug',0);
INSERT INTO "usage" VALUES('EPSG','8971','other_transformation','EPSG','3913','EPSG','2370','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','4441','NZVD2009 height to One Tree Point 1964 height (1)','Accuracy 0.03m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5767',0.03,'EPSG','8603','Vertical Offset',0.06,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ ONTP',0);
INSERT INTO "usage" VALUES('EPSG','9076','other_transformation','EPSG','4441','EPSG','3762','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4442','NZVD2009 height to Auckland 1946 height (1)','Accuracy 0.05m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5759',0.05,'EPSG','8603','Vertical Offset',0.34,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ AUCK',0);
INSERT INTO "usage" VALUES('EPSG','9077','other_transformation','EPSG','4442','EPSG','3764','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4443','NZVD2009 height to Moturiki 1953 height (1)','Accuracy 0.06m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5764',0.06,'EPSG','8603','Vertical Offset',0.24,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ MOTU',0);
INSERT INTO "usage" VALUES('EPSG','9078','other_transformation','EPSG','4443','EPSG','3768','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4444','NZVD2009 height to Nelson 1955 height (1)','Accuracy 0.07m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5766',0.07,'EPSG','8603','Vertical Offset',0.29,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ NELS',0);
INSERT INTO "usage" VALUES('EPSG','9079','other_transformation','EPSG','4444','EPSG','3802','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4445','NZVD2009 height to Gisborne 1926 height (1)','Accuracy 0.02m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5762',0.02,'EPSG','8603','Vertical Offset',0.34,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ GISB',0);
INSERT INTO "usage" VALUES('EPSG','9080','other_transformation','EPSG','4445','EPSG','3771','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4446','NZVD2009 height to Napier 1962 height (1)','Accuracy 0.05m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5765',0.05,'EPSG','8603','Vertical Offset',0.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ NAPI',0);
INSERT INTO "usage" VALUES('EPSG','9081','other_transformation','EPSG','4446','EPSG','3772','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4447','NZVD2009 height to Taranaki 1970 height (1)','Accuracy 0.05m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5769',0.05,'EPSG','8603','Vertical Offset',0.32,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ TARA',0);
INSERT INTO "usage" VALUES('EPSG','9082','other_transformation','EPSG','4447','EPSG','3769','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4448','NZVD2009 height to Wellington 1953 height (1)','Accuracy 0.04m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5770',0.04,'EPSG','8603','Vertical Offset',0.44,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ WELL',0);
INSERT INTO "usage" VALUES('EPSG','9083','other_transformation','EPSG','4448','EPSG','3773','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4449','NZVD2009 height to Lyttelton 1937 height (1)','Accuracy 0.09m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5763',0.09,'EPSG','8603','Vertical Offset',0.47,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ LYTT',0);
INSERT INTO "usage" VALUES('EPSG','9084','other_transformation','EPSG','4449','EPSG','3804','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4450','NZVD2009 height to Dunedin 1958 height (1)','Accuracy 0.07m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5761',0.07,'EPSG','8603','Vertical Offset',0.49,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ DUNE',0);
INSERT INTO "usage" VALUES('EPSG','9085','other_transformation','EPSG','4450','EPSG','3803','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4451','NZVD2009 height to Bluff 1955 height (1)','Accuracy 0.05m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5760',0.05,'EPSG','8603','Vertical Offset',0.36,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ BLUF',0);
INSERT INTO "usage" VALUES('EPSG','9086','other_transformation','EPSG','4451','EPSG','3801','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4452','NZVD2009 height to Stewart Island 1977 height (1)','Accuracy 0.15m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','5772',0.15,'EPSG','8603','Vertical Offset',0.39,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ STIS',0);
INSERT INTO "usage" VALUES('EPSG','9087','other_transformation','EPSG','4452','EPSG','3338','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','4453','NZVD2009 height to Dunedin-Bluff 1960 height (1)','Accuracy 0.04m (1 sigma).','EPSG','9616','Vertical Offset','EPSG','4440','EPSG','4458',0.04,'EPSG','8603','Vertical Offset',0.38,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'LINZ-NZ DUBL',0);
INSERT INTO "usage" VALUES('EPSG','9088','other_transformation','EPSG','4453','EPSG','3806','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5133','Tokyo 1892 to Tokyo (1)','Caused by redetermination of longitude of Tokyo datum fundamental point in 1918.','EPSG','9601','Longitude rotation','EPSG','5132','EPSG','4301',0.0,'EPSG','8602','Longitude offset',10.405,'EPSG','9104',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Jpn',0);
INSERT INTO "usage" VALUES('EPSG','9240','other_transformation','EPSG','5133','EPSG','1364','EPSG','1027');
INSERT INTO "other_transformation" VALUES('EPSG','5134','Tokyo 1892 to Korean 1985 (1)','Caused by redetermination of longitude of Tokyo datum origin in 1918. Korean 1985 is based on the 1918 determination.','EPSG','9601','Longitude rotation','EPSG','5132','EPSG','4162',0.0,'EPSG','8602','Longitude offset',10.405,'EPSG','9104',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Kor',0);
INSERT INTO "usage" VALUES('EPSG','9241','other_transformation','EPSG','5134','EPSG','3266','EPSG','1027');
INSERT INTO "other_transformation" VALUES('EPSG','5238','S-JTSK/05 (Ferro) to S-JTSK/05 (1)','','EPSG','9601','Longitude rotation','EPSG','5229','EPSG','5228',0.0,'EPSG','8602','Longitude offset',-17.4,'EPSG','9110',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Cze',0);
INSERT INTO "usage" VALUES('EPSG','9310','other_transformation','EPSG','5238','EPSG','1079','EPSG','1100');
INSERT INTO "other_transformation" VALUES('EPSG','5241','S-JTSK to S-JTSK/05 (1)','S-JTSK/05 is derived from the R05 realisation of ETRS89 and constrained to be coincident with S-JTSK.','EPSG','9619','Geographic2D offsets','EPSG','4156','EPSG','5228',0.0,'EPSG','8601','Latitude offset',0.0,'EPSG','9104','EPSG','8602','Longitude offset',0.0,'EPSG','9104',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'CUZK-Cze',0);
INSERT INTO "usage" VALUES('EPSG','9313','other_transformation','EPSG','5241','EPSG','1079','EPSG','1113');
INSERT INTO "other_transformation" VALUES('EPSG','5400','Baltic height to Caspian depth (1)','Baltic datum plane is 28m above Caspian datum plane.','EPSG','9616','Vertical Offset','EPSG','5705','EPSG','5706',0.0,'EPSG','8603','Vertical Offset',-28.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'Caspian Sea',1);
INSERT INTO "usage" VALUES('EPSG','9368','other_transformation','EPSG','5400','EPSG','1291','EPSG','1060');
INSERT INTO "other_transformation" VALUES('EPSG','5401','Belfast to Malin Head','Belfast datum is 37mm above Malin Head datum.','EPSG','9616','Vertical Offset','EPSG','5732','EPSG','5731',0.01,'EPSG','8603','Vertical Offset',-0.037,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSNI-Gbr NI',1);
INSERT INTO "usage" VALUES('EPSG','9369','other_transformation','EPSG','5401','EPSG','1305','EPSG','1208');
INSERT INTO "other_transformation" VALUES('EPSG','5402','Baltic height to AIOC95 depth (1)','Baltic datum plane is 26.3m above AIOC95 datum plane.','EPSG','9616','Vertical Offset','EPSG','5705','EPSG','5734',0.0,'EPSG','8603','Vertical Offset',-26.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'AIOC95-Aze',1);
INSERT INTO "usage" VALUES('EPSG','9370','other_transformation','EPSG','5402','EPSG','2592','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5403','AIOC95 depth to Caspian depth (1)','The AIOC95 vertical reference surface is 1.7m above the Caspian vertical reference surface.','EPSG','9616','Vertical Offset','EPSG','5734','EPSG','5706',0.0,'EPSG','8603','Vertical Offset',-1.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'AIOC95-Aze',0);
INSERT INTO "usage" VALUES('EPSG','9371','other_transformation','EPSG','5403','EPSG','2592','EPSG','1060');
INSERT INTO "other_transformation" VALUES('EPSG','5404','Baltic to Black Sea (1)','Baltic datum is 0.4m above Black Sea datum.','EPSG','9616','Vertical Offset','EPSG','5705','EPSG','5735',0.0,'EPSG','8603','Vertical Offset',-0.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'Black Sea',1);
INSERT INTO "usage" VALUES('EPSG','9372','other_transformation','EPSG','5404','EPSG','1102','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5405','Hong Kong Principal height to Hong Kong Chart depth (1)','HKPD is 0.146m above chart datum.','EPSG','9616','Vertical Offset','EPSG','5738','EPSG','5739',0.0,'EPSG','8603','Vertical Offset',0.146,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'SMO-HK',1);
INSERT INTO "usage" VALUES('EPSG','9373','other_transformation','EPSG','5405','EPSG','1118','EPSG','1060');
INSERT INTO "other_transformation" VALUES('EPSG','5406','Belfast to Malin Head (1)','Belfast datum is 37mm below Malin Head datum.','EPSG','9616','Vertical Offset','EPSG','5732','EPSG','5731',0.01,'EPSG','8603','Vertical Offset',0.037,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSNI-Gbr NI',1);
INSERT INTO "usage" VALUES('EPSG','9374','other_transformation','EPSG','5406','EPSG','1305','EPSG','1208');
INSERT INTO "other_transformation" VALUES('EPSG','5407','Poolbeg to Malin Head (1)','Poolbeg datum is 2.7m below Malin Head datum. Transformations are subject to localised anomalies.','EPSG','9616','Vertical Offset','EPSG','5754','EPSG','5731',0.1,'EPSG','8603','Vertical Offset',2.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSI-Ire',1);
INSERT INTO "usage" VALUES('EPSG','9375','other_transformation','EPSG','5407','EPSG','1305','EPSG','1142');
INSERT INTO "other_transformation" VALUES('EPSG','5408','Poolbeg to Belfast (1)','Poolbeg datum is 2.7m below Belfast datum. Transformations are subject to localised anomalies.','EPSG','9616','Vertical Offset','EPSG','5754','EPSG','5732',0.1,'EPSG','8603','Vertical Offset',2.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSNI-Gbr NI',1);
INSERT INTO "usage" VALUES('EPSG','9376','other_transformation','EPSG','5408','EPSG','1305','EPSG','1208');
INSERT INTO "other_transformation" VALUES('EPSG','5412','KOC CD to Kuwait PWD (1)','Construction datum is 0.49m below PWD datum.','EPSG','9616','Vertical Offset','EPSG','5790','EPSG','5788',0.1,'EPSG','8603','Vertical Offset',0.49,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',1);
INSERT INTO "usage" VALUES('EPSG','9380','other_transformation','EPSG','5412','EPSG','1136','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5413','KOC CD height to KOC WD depth (1)','Construction Datum datum plane is 4.74m (15.55ft) below Well Datum datum plane.','EPSG','9616','Vertical Offset','EPSG','5790','EPSG','5789',0.1,'EPSG','8603','Vertical Offset',4.74,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',1);
INSERT INTO "usage" VALUES('EPSG','9381','other_transformation','EPSG','5413','EPSG','3267','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5414','KOC WD to Kuwait PWD (1)','Well datum is 4.25m above PWD datum.','EPSG','9616','Vertical Offset','EPSG','5789','EPSG','5788',0.1,'EPSG','8603','Vertical Offset',-4.25,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',1);
INSERT INTO "usage" VALUES('EPSG','9382','other_transformation','EPSG','5414','EPSG','1136','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5419','NGF IGN69 height to EVRF2000 height (1)','Determined at 8 points. RMS residual 0.005m, maximum residual 0.010m. The IGN69 vertical reference surface is below the EVRF2000 vertical reference surface.','EPSG','9616','Vertical Offset','EPSG','5720','EPSG','5730',0.1,'EPSG','8603','Vertical Offset',-0.486,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Fra',0);
INSERT INTO "usage" VALUES('EPSG','9387','other_transformation','EPSG','5419','EPSG','1326','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5425','NAP height to EVRF2000 height (1)','Determined at 757 points. RMS residual 0.002m, maximum residual 0.021m. The NAP vertical reference surface is below the EVRF2000 vertical reference surface.','EPSG','9616','Vertical Offset','EPSG','5709','EPSG','5730',0.1,'EPSG','8603','Vertical Offset',-0.005,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Nld',0);
INSERT INTO "usage" VALUES('EPSG','9393','other_transformation','EPSG','5425','EPSG','1275','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5427','Cascais height to EVRF2000 height (1)','Determined at 5 points. RMS residual 0.013m, maximum residual 0.021m. The Cascais vertical reference surface is below the EVRF2000 vertical reference surface.','EPSG','9616','Vertical Offset','EPSG','5780','EPSG','5730',0.1,'EPSG','8603','Vertical Offset',-0.315,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Prt',0);
INSERT INTO "usage" VALUES('EPSG','9395','other_transformation','EPSG','5427','EPSG','1294','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5432','N60 height to EVRF2000 height (1)','Determined at 66 points. RMS residual 0.003m, maximum residual 0.009m. The N60 vertical reference surface is above the EVRF2000 vertical reference surface.','EPSG','9616','Vertical Offset','EPSG','5717','EPSG','5730',0.1,'EPSG','8603','Vertical Offset',0.213,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Fin',0);
INSERT INTO "usage" VALUES('EPSG','9400','other_transformation','EPSG','5432','EPSG','3333','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5438','Baltic 1977 height to Caspian height (1)','Baltic 1977 vertical reference surface is 28m above Caspian vertical reference surface.','EPSG','9616','Vertical Offset','EPSG','5705','EPSG','5611',0.0,'EPSG','8603','Vertical Offset',28.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'Caspian Sea',0);
INSERT INTO "usage" VALUES('EPSG','9406','other_transformation','EPSG','5438','EPSG','1291','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5440','Baltic 1977 depth to Caspian depth (1)','The Baltic 1977 vertical reference surface is 28m above the Caspian vertical reference surface.','EPSG','9616','Vertical Offset','EPSG','5612','EPSG','5706',0.0,'EPSG','8603','Vertical Offset',-28.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'Caspian Sea',0);
INSERT INTO "usage" VALUES('EPSG','9408','other_transformation','EPSG','5440','EPSG','1291','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5441','Baltic depth to Caspian height (1)','The Baltic vertical reference surface is 28m above the Caspian vetyical reference surface.','EPSG','9616','Vertical Offset','EPSG','5612','EPSG','5611',0.0,'EPSG','8603','Vertical Offset',28.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'Caspian Sea',1);
INSERT INTO "usage" VALUES('EPSG','9409','other_transformation','EPSG','5441','EPSG','1291','EPSG','1060');
INSERT INTO "other_transformation" VALUES('EPSG','5442','Baltic height to Baltic depth (1)','','EPSG','9616','Vertical Offset','EPSG','5705','EPSG','5612',0.0,'EPSG','8603','Vertical Offset',0.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-FSU',1);
INSERT INTO "usage" VALUES('EPSG','9410','other_transformation','EPSG','5442','EPSG','1284','EPSG','1212');
INSERT INTO "other_transformation" VALUES('EPSG','5443','Baltic 1977 height to AIOC95 height (1)','Baltic 1977 vertical reference surface is 26.3m above AIOC95 surface.','EPSG','9616','Vertical Offset','EPSG','5705','EPSG','5797',0.0,'EPSG','8603','Vertical Offset',26.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'AIOC95-Aze',0);
INSERT INTO "usage" VALUES('EPSG','9411','other_transformation','EPSG','5443','EPSG','2592','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5445','Baltic 1977 depth to AIOC95 depth (1)','The Baltic 1977 vertical reference surface is 26.3m above the AIOC95 surface.','EPSG','9616','Vertical Offset','EPSG','5612','EPSG','5734',0.0,'EPSG','8603','Vertical Offset',-26.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'AIOC95-Aze',0);
INSERT INTO "usage" VALUES('EPSG','9413','other_transformation','EPSG','5445','EPSG','2592','EPSG','1060');
INSERT INTO "other_transformation" VALUES('EPSG','5446','Baltic depth to AIOC95 height (1)','Baltic datum plane is 26.3m above AIOC95 datum plane.','EPSG','9616','Vertical Offset','EPSG','5612','EPSG','5797',0.0,'EPSG','8603','Vertical Offset',26.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'AIOC95-Aze',1);
INSERT INTO "usage" VALUES('EPSG','9414','other_transformation','EPSG','5446','EPSG','2592','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5447','Baltic 1977 height to Black Sea height (1)','Baltic 1977 vertical reference surface is 0.4m above Black Sea surface.','EPSG','9616','Vertical Offset','EPSG','5705','EPSG','5735',0.0,'EPSG','8603','Vertical Offset',0.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'Black Sea',0);
INSERT INTO "usage" VALUES('EPSG','9415','other_transformation','EPSG','5447','EPSG','3251','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5448','Poolbeg height to Malin Head height (1)','Poolbeg datum plane is 2.7m below Malin Head datum plane. Transformations are subject to localised anomalies.','EPSG','9616','Vertical Offset','EPSG','5754','EPSG','5731',0.1,'EPSG','8603','Vertical Offset',-2.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSI-Ire',1);
INSERT INTO "usage" VALUES('EPSG','9416','other_transformation','EPSG','5448','EPSG','1305','EPSG','1142');
INSERT INTO "other_transformation" VALUES('EPSG','5449','Poolbeg height to Belfast height (1)','Poolbeg datum plane is 2.7m below Belfast datum plane. Transformations are subject to localised anomalies.','EPSG','9616','Vertical Offset','EPSG','5754','EPSG','5732',0.1,'EPSG','8603','Vertical Offset',-2.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSNI-Gbr NI',1);
INSERT INTO "usage" VALUES('EPSG','9417','other_transformation','EPSG','5449','EPSG','2530','EPSG','1208');
INSERT INTO "other_transformation" VALUES('EPSG','5450','KOC CD height to Kuwait PWD height (1)','The KOC CD vertical reference surface is 0.49m below the PWD surface.','EPSG','9616','Vertical Offset','EPSG','5790','EPSG','5788',0.1,'EPSG','8603','Vertical Offset',-0.49,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',0);
INSERT INTO "usage" VALUES('EPSG','9418','other_transformation','EPSG','5450','EPSG','3267','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5452','Belfast height to Malin Head height (1)','Belfast vertical reference surface is 37mm below Malin Head surface.','EPSG','9616','Vertical Offset','EPSG','5732','EPSG','5731',0.01,'EPSG','8603','Vertical Offset',-0.037,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSNI-Gbr NI',0);
INSERT INTO "usage" VALUES('EPSG','9419','other_transformation','EPSG','5452','EPSG','2530','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','5453','KOC CD height to KOC WD depth (ft) (1)','Construction Datum datum plane is 4.74m (15.55ft) below Well Datum datum plane.','EPSG','9616','Vertical Offset','EPSG','5790','EPSG','5614',0.1,'EPSG','8603','Vertical Offset',15.55,'EPSG','9002',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',1);
INSERT INTO "usage" VALUES('EPSG','9420','other_transformation','EPSG','5453','EPSG','3267','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','5454','HKPD height to HKCD depth (1)','HKPD datum plane is 0.146m above HKCD datum plane.','EPSG','9616','Vertical Offset','EPSG','5738','EPSG','5739',0.0,'EPSG','8603','Vertical Offset',-0.146,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'SMO-HK',1);
INSERT INTO "usage" VALUES('EPSG','9421','other_transformation','EPSG','5454','EPSG','1118','EPSG','1060');
INSERT INTO "other_transformation" VALUES('EPSG','5455','KOC WD depth to Kuwait PWD height (1)','Well Datum datum plane is 4.25m above PWD datum plane.','EPSG','9616','Vertical Offset','EPSG','5789','EPSG','5788',0.1,'EPSG','8603','Vertical Offset',4.25,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',1);
INSERT INTO "usage" VALUES('EPSG','9422','other_transformation','EPSG','5455','EPSG','3267','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','6699','JGD2000 (vertical) height to JGD2011 (vertical) height (1)','Excludes areas of eastern Honshu affected by 2008 Iwate-Miyagi and 2011 Tohoku earthquakes (Aomori, Iwate, Miyagi, Akita, Yamagata, Fukushima and Ibaraki prefectures).','EPSG','9616','Vertical Offset','EPSG','6694','EPSG','6695',0.01,'EPSG','8603','Vertical Offset',0.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Jpn ex E Honshu',0);
INSERT INTO "usage" VALUES('EPSG','9735','other_transformation','EPSG','6699','EPSG','4165','EPSG','1235');
INSERT INTO "other_transformation" VALUES('EPSG','7653','EGM96 height to Kumul 34 height (1)','Defines Kumul 34 heights.','EPSG','9616','Vertical Offset','EPSG','5773','EPSG','7651',0.0,'EPSG','8603','Vertical Offset',-0.87,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'QC-Png Kumul34',0);
INSERT INTO "usage" VALUES('EPSG','10195','other_transformation','EPSG','7653','EPSG','4013','EPSG','1133');
INSERT INTO "other_transformation" VALUES('EPSG','7654','EGM2008 height to Kiunga height (1)','Defines Kiunga heights.','EPSG','9616','Vertical Offset','EPSG','3855','EPSG','7652',0.0,'EPSG','8603','Vertical Offset',-3.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'QC-Png Kiunga',0);
INSERT INTO "usage" VALUES('EPSG','10196','other_transformation','EPSG','7654','EPSG','4383','EPSG','1133');
INSERT INTO "other_transformation" VALUES('EPSG','7873','EGM96 height to POM96 height (1)','Defines POM96 heights.','EPSG','9616','Vertical Offset','EPSG','5773','EPSG','7832',0.0,'EPSG','8603','Vertical Offset',-1.58,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'QC-Png Gulf-Cen',0);
INSERT INTO "usage" VALUES('EPSG','10307','other_transformation','EPSG','7873','EPSG','4425','EPSG','1133');
INSERT INTO "other_transformation" VALUES('EPSG','7874','EGM2008 height to POM08 height (1)','Defines POM08 heights.','EPSG','9616','Vertical Offset','EPSG','3855','EPSG','7841',0.0,'EPSG','8603','Vertical Offset',-0.93,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'QC-Png Gulf-Cen',0);
INSERT INTO "usage" VALUES('EPSG','10308','other_transformation','EPSG','7874','EPSG','4425','EPSG','1133');
INSERT INTO "other_transformation" VALUES('EPSG','7963','Poolbeg height (ft(Br36)) to Poolbeg height (m)','Change of unit from British foot (1936) [ft(BR36)] to metre.','EPSG','1069','Change of Vertical Unit','EPSG','5754','EPSG','7962',NULL,'EPSG','1051','Unit conversion scalar',0.3048007491,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10347','other_transformation','EPSG','7963','EPSG','1305','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','7964','Poolbeg height (m) to Malin Head height (1)','Poolbeg vertical reference surface is 2.7m below Malin Head surface. Nominal accuracy 0.1m but transformations are subject to localised anomalies.','EPSG','9616','Vertical Offset','EPSG','7962','EPSG','5731',0.1,'EPSG','8603','Vertical Offset',-2.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSI-Ire',0);
INSERT INTO "usage" VALUES('EPSG','10348','other_transformation','EPSG','7964','EPSG','1305','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','7966','Poolbeg height (m) to Belfast height (1)','Poolbeg vertical reference surface is 2.7m below Belfast surface. Nominal accuracy 0.1m but transformations are subject to localised anomalies.','EPSG','9616','Vertical Offset','EPSG','7962','EPSG','5732',0.1,'EPSG','8603','Vertical Offset',-2.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OSI-Ire',0);
INSERT INTO "usage" VALUES('EPSG','10350','other_transformation','EPSG','7966','EPSG','2530','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','7972','NGVD29 height (ftUS) to NGVD29 height (m)','Change of unit from US survey foot (ftUS) to metre. 1 ftUS = (12/39.37)m ≈ 0.304800609601219m.','EPSG','1069','Change of Vertical Unit','EPSG','5702','EPSG','7968',NULL,'EPSG','1051','Unit conversion scalar',0.304800609601219,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10355','other_transformation','EPSG','7972','EPSG','1323','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','7977','HKPD depth to HKCD depth (1)','The HKPD vertical reference surface is 0.146m above the HKCD surface.','EPSG','9616','Vertical Offset','EPSG','7976','EPSG','5739',0.0,'EPSG','8603','Vertical Offset',-0.146,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'SMO-HK',0);
INSERT INTO "usage" VALUES('EPSG','10359','other_transformation','EPSG','7977','EPSG','3335','EPSG','1060');
INSERT INTO "other_transformation" VALUES('EPSG','7978','NGVD29 height (ftUS) to NGVD29 depth (ftUS)','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5702','EPSG','6359',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10360','other_transformation','EPSG','7978','EPSG','1323','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','7980','KOC CD height to KOC WD height (1)','The KOC CD vertical reference surface is 4.74m (15.55ft) below KOC WD surface.','EPSG','9616','Vertical Offset','EPSG','5790','EPSG','7979',0.1,'EPSG','8603','Vertical Offset',-4.74,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',0);
INSERT INTO "usage" VALUES('EPSG','10361','other_transformation','EPSG','7980','EPSG','3267','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','7981','Kuwait PWD height to KOC WD height (1)','The KOC WD vertical reference surface is 4.25m above the Kuwait PWD surface.','EPSG','9616','Vertical Offset','EPSG','5788','EPSG','7979',0.1,'EPSG','8603','Vertical Offset',-4.25,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'KOC-Kwt',0);
INSERT INTO "usage" VALUES('EPSG','10362','other_transformation','EPSG','7981','EPSG','3267','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','7982','HKPD height to HKPD depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5738','EPSG','7976',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10363','other_transformation','EPSG','7982','EPSG','3334','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','7984','KOC WD height to KOC WD depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','7979','EPSG','5789',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10365','other_transformation','EPSG','7984','EPSG','3267','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','7985','KOC WD depth to KOC WD depth (ft)','Change of unit from metre to International foot (ft). 1ft = 0.3048m.','EPSG','1069','Change of Vertical Unit','EPSG','5789','EPSG','5614',NULL,'EPSG','1051','Unit conversion scalar',3.28083989501312,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10366','other_transformation','EPSG','7985','EPSG','3267','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','7988','NAVD88 height to NAVD88 height (ftUS)','Change of unit from metre to US survey foot. 1 ftUS = (12/39.37)m ≈ 0.304800609601219m.','EPSG','1069','Change of Vertical Unit','EPSG','5703','EPSG','6360',NULL,'EPSG','1051','Unit conversion scalar',3.28083333333333,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10369','other_transformation','EPSG','7988','EPSG','3664','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','7989','NAVD88 height to NAVD88 depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5703','EPSG','6357',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10370','other_transformation','EPSG','7989','EPSG','4161','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','7990','NAVD88 height (ftUS) to NAVD88 depth (ftUS)','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','6360','EPSG','6358',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10371','other_transformation','EPSG','7990','EPSG','3664','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','8038','Instantaneous Water Level height to Instantaneous Water Level depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5829','EPSG','5831',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10395','other_transformation','EPSG','8038','EPSG','1262','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','8039','MSL height to MSL depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5714','EPSG','5715',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10396','other_transformation','EPSG','8039','EPSG','1262','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','8054','MSL height to MSL height (ft)','Change of unit from metre to International foot (ft). 1ft = 0.3048m.','EPSG','1069','Change of Vertical Unit','EPSG','5714','EPSG','8050',NULL,'EPSG','1051','Unit conversion scalar',3.28083989501312,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10403','other_transformation','EPSG','8054','EPSG','1262','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','8055','MSL height to MSL height (ftUS)','Change of unit from metre to US survey foot (ftUS). 1 ftUS = (12/39.37)m ≈ 0.304800609601219m.','EPSG','1069','Change of Vertical Unit','EPSG','5714','EPSG','8052',NULL,'EPSG','1051','Unit conversion scalar',3.28083333333333,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10404','other_transformation','EPSG','8055','EPSG','1245','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','8056','MSL depth to MSL depth (ft)','Change of unit from metre to International foot (ft). 1ft = 0.3048m.','EPSG','1069','Change of Vertical Unit','EPSG','5715','EPSG','8051',NULL,'EPSG','1051','Unit conversion scalar',3.28083989501312,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10405','other_transformation','EPSG','8056','EPSG','1262','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','8057','MSL depth to MSL depth (ftUS)','Change of unit from metre to US survey foot (ftUS). 1 ftUS = (12/39.37)m ≈ 0.304800609601219m.','EPSG','1069','Change of Vertical Unit','EPSG','5715','EPSG','8053',NULL,'EPSG','1051','Unit conversion scalar',3.28083333333333,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10406','other_transformation','EPSG','8057','EPSG','1245','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','8060','Baltic 1977 height to Baltic 1977 depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5705','EPSG','5612',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10407','other_transformation','EPSG','8060','EPSG','2423','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','8229','NAVD88 height to NAVD88 height (ft)','Change of unit from metre to International foot (ft). 1ft = 0.3048. For States which have adopted International feet for their State Plane coordinate systems.','EPSG','1069','Change of Vertical Unit','EPSG','5703','EPSG','8228',NULL,'EPSG','1051','Unit conversion scalar',3.28083989501312,'EPSG','9201',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10445','other_transformation','EPSG','8229','EPSG','4464','EPSG','1101');
INSERT INTO "other_transformation" VALUES('EPSG','8354','Black Sea height to Black Sea depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5735','EPSG','5336',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10504','other_transformation','EPSG','8354','EPSG','3251','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','8355','AIOC95 height to AIOC95 depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5797','EPSG','5734',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10505','other_transformation','EPSG','8355','EPSG','2592','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','8356','Caspian height to Caspian depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','5611','EPSG','5706',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10506','other_transformation','EPSG','8356','EPSG','1291','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','8359','Baltic 1957 height to Baltic 1957 depth','Change of axis positive direction from up to down.','EPSG','1068','Height Depth Reversal','EPSG','8357','EPSG','8358',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,1);
INSERT INTO "usage" VALUES('EPSG','10507','other_transformation','EPSG','8359','EPSG','1306','EPSG','1111');
INSERT INTO "other_transformation" VALUES('EPSG','9371','Vienna height to GHA height (1)','Defines Wiener Null surface. GHA vertical reference surface is 156.68m below Wiener Null surface.','EPSG','9616','Vertical Offset','EPSG','8881','EPSG','5778',0.0,'EPSG','8603','Vertical Offset',156.68,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'BEV-Aut Wien',0);
INSERT INTO "usage" VALUES('EPSG','13986','other_transformation','EPSG','9371','EPSG','4585','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','9446','ODN height to EVRF2019 mean-tide height (1)','Determined at Channel Tunnel portal.','EPSG','9616','Vertical Offset','EPSG','5701','EPSG','9390',0.02,'EPSG','8603','Vertical Offset',-0.173,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Gbr 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14104','other_transformation','EPSG','9446','EPSG','2792','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','9447','Antalya height to EVRF2019 mean-tide height (1)','Determined at two points. No accuracy figure available. Applicable for points up to a maximum height of about 1000 m.','EPSG','9616','Vertical Offset','EPSG','5775','EPSG','9390',0.02,'EPSG','8603','Vertical Offset',-0.446,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Tur 2019m',1);
INSERT INTO "usage" VALUES('EPSG','14105','other_transformation','EPSG','9447','EPSG','3322','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','9448','Antalya height to EVRF2019 height (1)','Determined at two points. No accuracy figure available. Applicable for points up to a maximum height of about 1000 m.','EPSG','9616','Vertical Offset','EPSG','5775','EPSG','9389',0.02,'EPSG','8603','Vertical Offset',-0.392,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Tur 2019z',1);
INSERT INTO "usage" VALUES('EPSG','14106','other_transformation','EPSG','9448','EPSG','3322','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','9551','Antalya height to EVRF2019 height (2)','Determined at two points. No accuracy figure available. Applicable for points up to a maximum height of about 1000 m.','EPSG','9616','Vertical Offset','EPSG','5775','EPSG','9389',0.02,'EPSG','8603','Vertical Offset',-0.394,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Tur 2019z 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14651','other_transformation','EPSG','9551','EPSG','3322','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','9552','Antalya height to EVRF2019 mean-tide height (2)','Determined at two points. No accuracy figure available. Applicable for points up to a maximum height of about 1000 m.','EPSG','9616','Vertical Offset','EPSG','5775','EPSG','9390',0.02,'EPSG','8603','Vertical Offset',-0.448,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Tur 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14650','other_transformation','EPSG','9552','EPSG','3322','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','9562','ODN height to EVRF2019 mean-tide height (2)','Determined at Channel Tunnel portal.','EPSG','9616','Vertical Offset','EPSG','5701','EPSG','9390',0.02,'EPSG','8603','Vertical Offset',-0.17,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'EuG-Gbr 2019m 2020-09',0);
INSERT INTO "usage" VALUES('EPSG','14640','other_transformation','EPSG','9562','EPSG','2792','EPSG','1059');
INSERT INTO "other_transformation" VALUES('EPSG','9726','Genoa 1942 height to Catania 1965 height (1)','','EPSG','9616','Vertical Offset','EPSG','5214','EPSG','9721',0.01,'EPSG','8603','Vertical Offset',0.141,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'IGMI-Ita Sicily',0);
INSERT INTO "usage" VALUES('EPSG','15279','other_transformation','EPSG','9726','EPSG','2340','EPSG','1184');
INSERT INTO "other_transformation" VALUES('EPSG','10087','Jamaica 1875 / Jamaica (Old Grid) to JAD69 / Jamaica National Grid (1)','Derived by least squares fit at primary triangulation stations. Accuracy will be less outside of this network due to extrapolation.','EPSG','9624','Affine parametric transformation','EPSG','24100','EPSG','24200',1.5,'EPSG','8623','A0',82357.457,'EPSG','9001','EPSG','8624','A1',0.304794369,'EPSG','9203','EPSG','8625','A2',1.5417425e-05,'EPSG','9203','EPSG','8639','B0',28091.324,'EPSG','9001','EPSG','8640','B1',-1.5417425e-05,'EPSG','9203','EPSG','8641','B2',0.304794369,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'SD-Jam',0);
INSERT INTO "usage" VALUES('EPSG','11088','other_transformation','EPSG','10087','EPSG','3342','EPSG','1153');
INSERT INTO "other_transformation" VALUES('EPSG','10088','JAD69 / Jamaica National Grid to Jamaica 1875 / Jamaica (Old Grid) (1)','Derived by least squares fit at primary triangulation stations. Accuracy will be less outside of this network due to extrapolation.','EPSG','9624','Affine parametric transformation','EPSG','24200','EPSG','24100',1.5,'EPSG','8623','A0',-270201.96,'EPSG','9005','EPSG','8624','A1',0.00016595792,'EPSG','9203','EPSG','8625','A2',3.2809005,'EPSG','9203','EPSG','8639','B0',-92178.51,'EPSG','9005','EPSG','8640','B1',3.2809005,'EPSG','9203','EPSG','8641','B2',-0.00016595792,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'SD-Jam',1);
INSERT INTO "usage" VALUES('EPSG','11089','other_transformation','EPSG','10088','EPSG','3342','EPSG','1153');
INSERT INTO "other_transformation" VALUES('EPSG','10095','Mauritania 1999 / UTM zone 28N to WGS 84 / UTM zone 28N (1)','Parameter values consistent with the OGP Affine parametric transformation method derived by OGP from the published Helmert 2D parameter values.','EPSG','9624','Affine parametric transformation','EPSG','3103','EPSG','32628',40.0,'EPSG','8623','A0',NULL,'EPSG',NULL,'EPSG','8624','A1',NULL,'EPSG',NULL,'EPSG','8625','A2',NULL,'EPSG',NULL,'EPSG','8639','B0',NULL,'EPSG',NULL,'EPSG','8640','B1',NULL,'EPSG',NULL,'EPSG','8641','B2',NULL,'EPSG',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'MMI-Mau W',1);
INSERT INTO "usage" VALUES('EPSG','11096','other_transformation','EPSG','10095','EPSG','2971','EPSG','1249');
INSERT INTO "other_transformation" VALUES('EPSG','10096','Mauritania 1999 / UTM zone 29N to WGS 84 / UTM zone 29N (1)','Parameter values consistent with the OGP Affine parametric transformation method derived by OGP from the published Helmert 2D parameter values.','EPSG','9624','Affine parametric transformation','EPSG','3104','EPSG','32629',40.0,'EPSG','8623','A0',NULL,'EPSG',NULL,'EPSG','8624','A1',NULL,'EPSG',NULL,'EPSG','8625','A2',NULL,'EPSG',NULL,'EPSG','8639','B0',NULL,'EPSG',NULL,'EPSG','8640','B1',NULL,'EPSG',NULL,'EPSG','8641','B2',NULL,'EPSG',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'MMI-Mau C',1);
INSERT INTO "usage" VALUES('EPSG','11097','other_transformation','EPSG','10096','EPSG','2970','EPSG','1249');
INSERT INTO "other_transformation" VALUES('EPSG','10097','Mauritania 1999 / UTM zone 30N to WGS 84 / UTM zone 30N (1)','Parameter values consistent with the OGP Affine parametric transformation method derived by OGP from the published Helmert 2D parameter values.','EPSG','9624','Affine parametric transformation','EPSG','3105','EPSG','32630',40.0,'EPSG','8623','A0',NULL,'EPSG',NULL,'EPSG','8624','A1',NULL,'EPSG',NULL,'EPSG','8625','A2',NULL,'EPSG',NULL,'EPSG','8639','B0',NULL,'EPSG',NULL,'EPSG','8640','B1',NULL,'EPSG',NULL,'EPSG','8641','B2',NULL,'EPSG',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'MMI-Mau E',1);
INSERT INTO "usage" VALUES('EPSG','11098','other_transformation','EPSG','10097','EPSG','2969','EPSG','1249');
INSERT INTO "other_transformation" VALUES('EPSG','15596','Tokyo + JSLD height to WGS 84 (7)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',7.94,'EPSG','9104','EPSG','8602','Longitude offset',-13.97,'EPSG','9104','EPSG','8604','Geoid undulation',26.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 452142',0);
INSERT INTO "usage" VALUES('EPSG','11607','other_transformation','EPSG','15596','EPSG','2426','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15597','Tokyo + JSLD height to WGS 84 (8)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.1,'EPSG','9104','EPSG','8602','Longitude offset',-13.81,'EPSG','9104','EPSG','8604','Geoid undulation',27.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 444141',0);
INSERT INTO "usage" VALUES('EPSG','11608','other_transformation','EPSG','15597','EPSG','2427','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15598','Tokyo + JSLD height to WGS 84 (9)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.15,'EPSG','9104','EPSG','8602','Longitude offset',-13.95,'EPSG','9104','EPSG','8604','Geoid undulation',28.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 444142',0);
INSERT INTO "usage" VALUES('EPSG','11609','other_transformation','EPSG','15598','EPSG','2428','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15599','Tokyo + JSLD height to WGS 84 (10)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.37,'EPSG','9104','EPSG','8602','Longitude offset',-13.65,'EPSG','9104','EPSG','8604','Geoid undulation',29.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440141',0);
INSERT INTO "usage" VALUES('EPSG','11610','other_transformation','EPSG','15599','EPSG','2429','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15600','Tokyo + JSLD height to WGS 84 (11)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.44,'EPSG','9104','EPSG','8602','Longitude offset',-13.87,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440142',0);
INSERT INTO "usage" VALUES('EPSG','11611','other_transformation','EPSG','15600','EPSG','2430','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15601','Tokyo + JSLD height to WGS 84 (12)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.61,'EPSG','9104','EPSG','8602','Longitude offset',-14.08,'EPSG','9104','EPSG','8604','Geoid undulation',30.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440143',0);
INSERT INTO "usage" VALUES('EPSG','11612','other_transformation','EPSG','15601','EPSG','2431','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15602','Tokyo + JSLD height to WGS 84 (13)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.73,'EPSG','9104','EPSG','8602','Longitude offset',-14.3,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 440144',0);
INSERT INTO "usage" VALUES('EPSG','11613','other_transformation','EPSG','15602','EPSG','2432','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15603','Tokyo + JSLD height to WGS 84 (14)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.63,'EPSG','9104','EPSG','8602','Longitude offset',-13.49,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432141',0);
INSERT INTO "usage" VALUES('EPSG','11614','other_transformation','EPSG','15603','EPSG','2433','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15604','Tokyo + JSLD height to WGS 84 (15)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.71,'EPSG','9104','EPSG','8602','Longitude offset',-13.73,'EPSG','9104','EPSG','8604','Geoid undulation',31.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432142',0);
INSERT INTO "usage" VALUES('EPSG','11615','other_transformation','EPSG','15604','EPSG','2434','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15605','Tokyo + JSLD height to WGS 84 (16)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.84,'EPSG','9104','EPSG','8602','Longitude offset',-14.03,'EPSG','9104','EPSG','8604','Geoid undulation',31.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432143',0);
INSERT INTO "usage" VALUES('EPSG','11616','other_transformation','EPSG','15605','EPSG','2435','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15606','Tokyo + JSLD height to WGS 84 (17)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.98,'EPSG','9104','EPSG','8602','Longitude offset',-14.33,'EPSG','9104','EPSG','8604','Geoid undulation',32.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432144',0);
INSERT INTO "usage" VALUES('EPSG','11617','other_transformation','EPSG','15606','EPSG','2436','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15607','Tokyo + JSLD height to WGS 84 (18)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.1,'EPSG','9104','EPSG','8602','Longitude offset',-14.56,'EPSG','9104','EPSG','8604','Geoid undulation',32.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 432145',0);
INSERT INTO "usage" VALUES('EPSG','11618','other_transformation','EPSG','15607','EPSG','2437','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15608','Tokyo + JSLD height to WGS 84 (19)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.79,'EPSG','9104','EPSG','8602','Longitude offset',-13.0,'EPSG','9104','EPSG','8604','Geoid undulation',33.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424140',0);
INSERT INTO "usage" VALUES('EPSG','11619','other_transformation','EPSG','15608','EPSG','2438','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15609','Tokyo + JSLD height to WGS 84 (20)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.84,'EPSG','9104','EPSG','8602','Longitude offset',-13.31,'EPSG','9104','EPSG','8604','Geoid undulation',31.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424141',0);
INSERT INTO "usage" VALUES('EPSG','11620','other_transformation','EPSG','15609','EPSG','2439','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15610','Tokyo + JSLD height to WGS 84 (21)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.98,'EPSG','9104','EPSG','8602','Longitude offset',-13.59,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424142',0);
INSERT INTO "usage" VALUES('EPSG','11621','other_transformation','EPSG','15610','EPSG','2440','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15611','Tokyo + JSLD height to WGS 84 (22)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.1,'EPSG','9104','EPSG','8602','Longitude offset',-13.91,'EPSG','9104','EPSG','8604','Geoid undulation',29.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424143',0);
INSERT INTO "usage" VALUES('EPSG','11622','other_transformation','EPSG','15611','EPSG','2441','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15612','Tokyo + JSLD height to WGS 84 (23)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.17,'EPSG','9104','EPSG','8602','Longitude offset',-14.27,'EPSG','9104','EPSG','8604','Geoid undulation',31.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424144',0);
INSERT INTO "usage" VALUES('EPSG','11623','other_transformation','EPSG','15612','EPSG','2442','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15613','Tokyo + JSLD height to WGS 84 (24)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.23,'EPSG','9104','EPSG','8602','Longitude offset',-14.52,'EPSG','9104','EPSG','8604','Geoid undulation',31.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 424145',0);
INSERT INTO "usage" VALUES('EPSG','11624','other_transformation','EPSG','15613','EPSG','2443','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15614','Tokyo + JSLD height to WGS 84 (25)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.9,'EPSG','9104','EPSG','8602','Longitude offset',-12.68,'EPSG','9104','EPSG','8604','Geoid undulation',34.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420139',0);
INSERT INTO "usage" VALUES('EPSG','11625','other_transformation','EPSG','15614','EPSG','2444','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15615','Tokyo + JSLD height to WGS 84 (26)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',8.99,'EPSG','9104','EPSG','8602','Longitude offset',-12.8,'EPSG','9104','EPSG','8604','Geoid undulation',34.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420140',0);
INSERT INTO "usage" VALUES('EPSG','11626','other_transformation','EPSG','15615','EPSG','2445','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15616','Tokyo + JSLD height to WGS 84 (27)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.0,'EPSG','9104','EPSG','8602','Longitude offset',-13.07,'EPSG','9104','EPSG','8604','Geoid undulation',31.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420141',0);
INSERT INTO "usage" VALUES('EPSG','11627','other_transformation','EPSG','15616','EPSG','2446','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15617','Tokyo + JSLD height to WGS 84 (28)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.21,'EPSG','9104','EPSG','8602','Longitude offset',-13.51,'EPSG','9104','EPSG','8604','Geoid undulation',27.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420142',0);
INSERT INTO "usage" VALUES('EPSG','11628','other_transformation','EPSG','15617','EPSG','2447','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15618','Tokyo + JSLD height to WGS 84 (29)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.33,'EPSG','9104','EPSG','8602','Longitude offset',-13.66,'EPSG','9104','EPSG','8604','Geoid undulation',23.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 420143',0);
INSERT INTO "usage" VALUES('EPSG','11629','other_transformation','EPSG','15618','EPSG','2448','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15619','Tokyo + JSLD height to WGS 84 (30)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.25,'EPSG','9104','EPSG','8602','Longitude offset',-12.72,'EPSG','9104','EPSG','8604','Geoid undulation',34.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 412140',0);
INSERT INTO "usage" VALUES('EPSG','11630','other_transformation','EPSG','15619','EPSG','2449','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15620','Tokyo + JSLD height to WGS 84 (31)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.39,'EPSG','9104','EPSG','8602','Longitude offset',-12.91,'EPSG','9104','EPSG','8604','Geoid undulation',31.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 412141',0);
INSERT INTO "usage" VALUES('EPSG','11631','other_transformation','EPSG','15620','EPSG','2450','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15621','Tokyo + JSLD height to WGS 84 (32)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.55,'EPSG','9104','EPSG','8602','Longitude offset',-12.63,'EPSG','9104','EPSG','8604','Geoid undulation',35.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 404140',0);
INSERT INTO "usage" VALUES('EPSG','11632','other_transformation','EPSG','15621','EPSG','2451','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15622','Tokyo + JSLD height to WGS 84 (33)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.62,'EPSG','9104','EPSG','8602','Longitude offset',-12.82,'EPSG','9104','EPSG','8604','Geoid undulation',34.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 404141',0);
INSERT INTO "usage" VALUES('EPSG','11633','other_transformation','EPSG','15622','EPSG','2452','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15623','Tokyo + JSLD height to WGS 84 (34)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.81,'EPSG','9104','EPSG','8602','Longitude offset',-12.29,'EPSG','9104','EPSG','8604','Geoid undulation',36.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 400139',0);
INSERT INTO "usage" VALUES('EPSG','11634','other_transformation','EPSG','15623','EPSG','2453','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15624','Tokyo + JSLD height to WGS 84 (35)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.81,'EPSG','9104','EPSG','8602','Longitude offset',-12.45,'EPSG','9104','EPSG','8604','Geoid undulation',37.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 400140',0);
INSERT INTO "usage" VALUES('EPSG','11635','other_transformation','EPSG','15624','EPSG','2454','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15625','Tokyo + JSLD height to WGS 84 (36)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.92,'EPSG','9104','EPSG','8602','Longitude offset',-12.79,'EPSG','9104','EPSG','8604','Geoid undulation',38.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 400141',0);
INSERT INTO "usage" VALUES('EPSG','11636','other_transformation','EPSG','15625','EPSG','2455','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15626','Tokyo + JSLD height to WGS 84 (37)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',9.91,'EPSG','9104','EPSG','8602','Longitude offset',-12.21,'EPSG','9104','EPSG','8604','Geoid undulation',36.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 392139',0);
INSERT INTO "usage" VALUES('EPSG','11637','other_transformation','EPSG','15626','EPSG','2456','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15627','Tokyo + JSLD height to WGS 84 (38)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.08,'EPSG','9104','EPSG','8602','Longitude offset',-12.35,'EPSG','9104','EPSG','8604','Geoid undulation',39.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 392140',0);
INSERT INTO "usage" VALUES('EPSG','11638','other_transformation','EPSG','15627','EPSG','2457','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15628','Tokyo + JSLD height to WGS 84 (39)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.19,'EPSG','9104','EPSG','8602','Longitude offset',-12.74,'EPSG','9104','EPSG','8604','Geoid undulation',40.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 392141',0);
INSERT INTO "usage" VALUES('EPSG','11639','other_transformation','EPSG','15628','EPSG','2458','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15629','Tokyo + JSLD height to WGS 84 (40)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.29,'EPSG','9104','EPSG','8602','Longitude offset',-12.13,'EPSG','9104','EPSG','8604','Geoid undulation',38.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 384139',0);
INSERT INTO "usage" VALUES('EPSG','11640','other_transformation','EPSG','15629','EPSG','2459','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15630','Tokyo + JSLD height to WGS 84 (41)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.33,'EPSG','9104','EPSG','8602','Longitude offset',-12.27,'EPSG','9104','EPSG','8604','Geoid undulation',40.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 384140',0);
INSERT INTO "usage" VALUES('EPSG','11641','other_transformation','EPSG','15630','EPSG','2460','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15631','Tokyo + JSLD height to WGS 84 (42)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.45,'EPSG','9104','EPSG','8602','Longitude offset',-12.61,'EPSG','9104','EPSG','8604','Geoid undulation',41.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 384141',0);
INSERT INTO "usage" VALUES('EPSG','11642','other_transformation','EPSG','15631','EPSG','2461','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15632','Tokyo + JSLD height to WGS 84 (43)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.54,'EPSG','9104','EPSG','8602','Longitude offset',-11.96,'EPSG','9104','EPSG','8604','Geoid undulation',39.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 380139',0);
INSERT INTO "usage" VALUES('EPSG','11643','other_transformation','EPSG','15632','EPSG','2462','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15633','Tokyo + JSLD height to WGS 84 (44)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.65,'EPSG','9104','EPSG','8602','Longitude offset',-12.27,'EPSG','9104','EPSG','8604','Geoid undulation',41.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 380140',0);
INSERT INTO "usage" VALUES('EPSG','11644','other_transformation','EPSG','15633','EPSG','2463','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15634','Tokyo + JSLD height to WGS 84 (45)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.67,'EPSG','9104','EPSG','8602','Longitude offset',-12.5,'EPSG','9104','EPSG','8604','Geoid undulation',41.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 380141',0);
INSERT INTO "usage" VALUES('EPSG','11645','other_transformation','EPSG','15634','EPSG','2464','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15635','Tokyo + JSLD height to WGS 84 (46)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.67,'EPSG','9104','EPSG','8602','Longitude offset',-10.86,'EPSG','9104','EPSG','8604','Geoid undulation',38.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372136',0);
INSERT INTO "usage" VALUES('EPSG','11646','other_transformation','EPSG','15635','EPSG','2465','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15636','Tokyo + JSLD height to WGS 84 (47)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.68,'EPSG','9104','EPSG','8602','Longitude offset',-10.97,'EPSG','9104','EPSG','8604','Geoid undulation',36.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372137',0);
INSERT INTO "usage" VALUES('EPSG','11647','other_transformation','EPSG','15636','EPSG','2466','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15637','Tokyo + JSLD height to WGS 84 (48)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.8,'EPSG','9104','EPSG','8602','Longitude offset',-11.53,'EPSG','9104','EPSG','8604','Geoid undulation',39.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372138',0);
INSERT INTO "usage" VALUES('EPSG','11648','other_transformation','EPSG','15637','EPSG','2467','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15638','Tokyo + JSLD height to WGS 84 (49)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.8,'EPSG','9104','EPSG','8602','Longitude offset',-11.73,'EPSG','9104','EPSG','8604','Geoid undulation',40.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372139',0);
INSERT INTO "usage" VALUES('EPSG','11649','other_transformation','EPSG','15638','EPSG','2468','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15639','Tokyo + JSLD height to WGS 84 (50)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.92,'EPSG','9104','EPSG','8602','Longitude offset',-12.16,'EPSG','9104','EPSG','8604','Geoid undulation',42.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372140',0);
INSERT INTO "usage" VALUES('EPSG','11650','other_transformation','EPSG','15639','EPSG','2469','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15640','Tokyo + JSLD height to WGS 84 (51)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.0,'EPSG','9104','EPSG','8602','Longitude offset',-12.25,'EPSG','9104','EPSG','8604','Geoid undulation',41.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 372141',0);
INSERT INTO "usage" VALUES('EPSG','11651','other_transformation','EPSG','15640','EPSG','2470','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15641','Tokyo + JSLD height to WGS 84 (52)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.83,'EPSG','9104','EPSG','8602','Longitude offset',-10.77,'EPSG','9104','EPSG','8604','Geoid undulation',36.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364136',0);
INSERT INTO "usage" VALUES('EPSG','11652','other_transformation','EPSG','15641','EPSG','2471','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15642','Tokyo + JSLD height to WGS 84 (53)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.95,'EPSG','9104','EPSG','8602','Longitude offset',-11.0,'EPSG','9104','EPSG','8604','Geoid undulation',38.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364137',0);
INSERT INTO "usage" VALUES('EPSG','11653','other_transformation','EPSG','15642','EPSG','2472','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15643','Tokyo + JSLD height to WGS 84 (54)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',10.97,'EPSG','9104','EPSG','8602','Longitude offset',-11.34,'EPSG','9104','EPSG','8604','Geoid undulation',40.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364138',0);
INSERT INTO "usage" VALUES('EPSG','11654','other_transformation','EPSG','15643','EPSG','2473','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15644','Tokyo + JSLD height to WGS 84 (55)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.04,'EPSG','9104','EPSG','8602','Longitude offset',-11.69,'EPSG','9104','EPSG','8604','Geoid undulation',43.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364139',0);
INSERT INTO "usage" VALUES('EPSG','11655','other_transformation','EPSG','15644','EPSG','2474','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15645','Tokyo + JSLD height to WGS 84 (56)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.17,'EPSG','9104','EPSG','8602','Longitude offset',-12.05,'EPSG','9104','EPSG','8604','Geoid undulation',42.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 364140',0);
INSERT INTO "usage" VALUES('EPSG','11656','other_transformation','EPSG','15645','EPSG','2475','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15646','Tokyo + JSLD height to WGS 84 (57)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.11,'EPSG','9104','EPSG','8602','Longitude offset',-10.59,'EPSG','9104','EPSG','8604','Geoid undulation',37.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360136',0);
INSERT INTO "usage" VALUES('EPSG','11657','other_transformation','EPSG','15646','EPSG','2476','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15647','Tokyo + JSLD height to WGS 84 (58)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.16,'EPSG','9104','EPSG','8602','Longitude offset',-10.97,'EPSG','9104','EPSG','8604','Geoid undulation',40.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360137',0);
INSERT INTO "usage" VALUES('EPSG','11658','other_transformation','EPSG','15647','EPSG','2477','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15648','Tokyo + JSLD height to WGS 84 (59)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.29,'EPSG','9104','EPSG','8602','Longitude offset',-11.23,'EPSG','9104','EPSG','8604','Geoid undulation',42.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360138',0);
INSERT INTO "usage" VALUES('EPSG','11659','other_transformation','EPSG','15648','EPSG','2478','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15649','Tokyo + JSLD height to WGS 84 (60)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.36,'EPSG','9104','EPSG','8602','Longitude offset',-11.59,'EPSG','9104','EPSG','8604','Geoid undulation',42.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360139',0);
INSERT INTO "usage" VALUES('EPSG','11660','other_transformation','EPSG','15649','EPSG','2479','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15650','Tokyo + JSLD height to WGS 84 (61)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.44,'EPSG','9104','EPSG','8602','Longitude offset',-11.88,'EPSG','9104','EPSG','8604','Geoid undulation',40.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 360140',0);
INSERT INTO "usage" VALUES('EPSG','11661','other_transformation','EPSG','15650','EPSG','2480','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15651','Tokyo + JSLD height to WGS 84 (62)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.27,'EPSG','9104','EPSG','8602','Longitude offset',-9.31,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352132',0);
INSERT INTO "usage" VALUES('EPSG','11662','other_transformation','EPSG','15651','EPSG','2481','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15652','Tokyo + JSLD height to WGS 84 (63)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.33,'EPSG','9104','EPSG','8602','Longitude offset',-9.52,'EPSG','9104','EPSG','8604','Geoid undulation',33.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352133',0);
INSERT INTO "usage" VALUES('EPSG','11663','other_transformation','EPSG','15652','EPSG','2482','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15653','Tokyo + JSLD height to WGS 84 (64)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.38,'EPSG','9104','EPSG','8602','Longitude offset',-9.86,'EPSG','9104','EPSG','8604','Geoid undulation',34.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352134',0);
INSERT INTO "usage" VALUES('EPSG','11664','other_transformation','EPSG','15653','EPSG','2483','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15654','Tokyo + JSLD height to WGS 84 (65)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.41,'EPSG','9104','EPSG','8602','Longitude offset',-10.14,'EPSG','9104','EPSG','8604','Geoid undulation',35.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352135',0);
INSERT INTO "usage" VALUES('EPSG','11665','other_transformation','EPSG','15654','EPSG','2484','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15655','Tokyo + JSLD height to WGS 84 (66)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.39,'EPSG','9104','EPSG','8602','Longitude offset',-10.52,'EPSG','9104','EPSG','8604','Geoid undulation',37.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352136',0);
INSERT INTO "usage" VALUES('EPSG','11666','other_transformation','EPSG','15655','EPSG','2485','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15656','Tokyo + JSLD height to WGS 84 (67)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.49,'EPSG','9104','EPSG','8602','Longitude offset',-10.83,'EPSG','9104','EPSG','8604','Geoid undulation',39.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352137',0);
INSERT INTO "usage" VALUES('EPSG','11667','other_transformation','EPSG','15656','EPSG','2486','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15657','Tokyo + JSLD height to WGS 84 (68)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.58,'EPSG','9104','EPSG','8602','Longitude offset',-11.21,'EPSG','9104','EPSG','8604','Geoid undulation',41.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352138',0);
INSERT INTO "usage" VALUES('EPSG','11668','other_transformation','EPSG','15657','EPSG','2487','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15658','Tokyo + JSLD height to WGS 84 (69)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.65,'EPSG','9104','EPSG','8602','Longitude offset',-11.53,'EPSG','9104','EPSG','8604','Geoid undulation',38.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352139',0);
INSERT INTO "usage" VALUES('EPSG','11669','other_transformation','EPSG','15658','EPSG','2488','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15659','Tokyo + JSLD height to WGS 84 (70)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.72,'EPSG','9104','EPSG','8602','Longitude offset',-11.8,'EPSG','9104','EPSG','8604','Geoid undulation',34.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 352140',0);
INSERT INTO "usage" VALUES('EPSG','11670','other_transformation','EPSG','15659','EPSG','2489','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15660','Tokyo + JSLD height to WGS 84 (71)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.44,'EPSG','9104','EPSG','8602','Longitude offset',-9.21,'EPSG','9104','EPSG','8604','Geoid undulation',32.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344132',0);
INSERT INTO "usage" VALUES('EPSG','11671','other_transformation','EPSG','15660','EPSG','2490','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15661','Tokyo + JSLD height to WGS 84 (72)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.47,'EPSG','9104','EPSG','8602','Longitude offset',-9.52,'EPSG','9104','EPSG','8604','Geoid undulation',35.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344133',0);
INSERT INTO "usage" VALUES('EPSG','11672','other_transformation','EPSG','15661','EPSG','2491','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15662','Tokyo + JSLD height to WGS 84 (73)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.55,'EPSG','9104','EPSG','8602','Longitude offset',-9.8,'EPSG','9104','EPSG','8604','Geoid undulation',35.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344134',0);
INSERT INTO "usage" VALUES('EPSG','11673','other_transformation','EPSG','15662','EPSG','2492','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15663','Tokyo + JSLD height to WGS 84 (74)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.61,'EPSG','9104','EPSG','8602','Longitude offset',-10.12,'EPSG','9104','EPSG','8604','Geoid undulation',35.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344135',0);
INSERT INTO "usage" VALUES('EPSG','11674','other_transformation','EPSG','15663','EPSG','2493','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15664','Tokyo + JSLD height to WGS 84 (75)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.66,'EPSG','9104','EPSG','8602','Longitude offset',-10.47,'EPSG','9104','EPSG','8604','Geoid undulation',37.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344136',0);
INSERT INTO "usage" VALUES('EPSG','11675','other_transformation','EPSG','15664','EPSG','2494','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15665','Tokyo + JSLD height to WGS 84 (76)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.78,'EPSG','9104','EPSG','8602','Longitude offset',-10.79,'EPSG','9104','EPSG','8604','Geoid undulation',39.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344137',0);
INSERT INTO "usage" VALUES('EPSG','11676','other_transformation','EPSG','15665','EPSG','2495','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15666','Tokyo + JSLD height to WGS 84 (77)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.85,'EPSG','9104','EPSG','8602','Longitude offset',-11.13,'EPSG','9104','EPSG','8604','Geoid undulation',39.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344138',0);
INSERT INTO "usage" VALUES('EPSG','11677','other_transformation','EPSG','15666','EPSG','2496','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15667','Tokyo + JSLD height to WGS 84 (78)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.9,'EPSG','9104','EPSG','8602','Longitude offset',-11.47,'EPSG','9104','EPSG','8604','Geoid undulation',36.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344139',0);
INSERT INTO "usage" VALUES('EPSG','11678','other_transformation','EPSG','15667','EPSG','2497','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15668','Tokyo + JSLD height to WGS 84 (79)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.91,'EPSG','9104','EPSG','8602','Longitude offset',-11.69,'EPSG','9104','EPSG','8604','Geoid undulation',33.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 344140',0);
INSERT INTO "usage" VALUES('EPSG','11679','other_transformation','EPSG','15668','EPSG','2498','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15669','Tokyo + JSLD height to WGS 84 (80)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.65,'EPSG','9104','EPSG','8602','Longitude offset',-8.59,'EPSG','9104','EPSG','8604','Geoid undulation',29.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340130',0);
INSERT INTO "usage" VALUES('EPSG','11680','other_transformation','EPSG','15669','EPSG','2499','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15670','Tokyo + JSLD height to WGS 84 (81)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.68,'EPSG','9104','EPSG','8602','Longitude offset',-8.8,'EPSG','9104','EPSG','8604','Geoid undulation',30.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340131',0);
INSERT INTO "usage" VALUES('EPSG','11681','other_transformation','EPSG','15670','EPSG','2500','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15671','Tokyo + JSLD height to WGS 84 (82)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.73,'EPSG','9104','EPSG','8602','Longitude offset',-9.04,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340132',0);
INSERT INTO "usage" VALUES('EPSG','11682','other_transformation','EPSG','15671','EPSG','2501','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15672','Tokyo + JSLD height to WGS 84 (83)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.72,'EPSG','9104','EPSG','8602','Longitude offset',-9.48,'EPSG','9104','EPSG','8604','Geoid undulation',35.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340133',0);
INSERT INTO "usage" VALUES('EPSG','11683','other_transformation','EPSG','15672','EPSG','2502','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15673','Tokyo + JSLD height to WGS 84 (84)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.81,'EPSG','9104','EPSG','8602','Longitude offset',9.74,'EPSG','9104','EPSG','8604','Geoid undulation',35.8,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340134',0);
INSERT INTO "usage" VALUES('EPSG','11684','other_transformation','EPSG','15673','EPSG','2503','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15674','Tokyo + JSLD height to WGS 84 (85)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.88,'EPSG','9104','EPSG','8602','Longitude offset',-10.1,'EPSG','9104','EPSG','8604','Geoid undulation',37.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340135',0);
INSERT INTO "usage" VALUES('EPSG','11685','other_transformation','EPSG','15674','EPSG','2504','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15675','Tokyo + JSLD height to WGS 84 (86)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.91,'EPSG','9104','EPSG','8602','Longitude offset',-10.35,'EPSG','9104','EPSG','8604','Geoid undulation',37.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340136',0);
INSERT INTO "usage" VALUES('EPSG','11686','other_transformation','EPSG','15675','EPSG','2505','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15676','Tokyo + JSLD height to WGS 84 (87)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.9,'EPSG','9104','EPSG','8602','Longitude offset',-10.7,'EPSG','9104','EPSG','8604','Geoid undulation',39.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340137',0);
INSERT INTO "usage" VALUES('EPSG','11687','other_transformation','EPSG','15676','EPSG','2506','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15677','Tokyo + JSLD height to WGS 84 (88)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.02,'EPSG','9104','EPSG','8602','Longitude offset',-11.09,'EPSG','9104','EPSG','8604','Geoid undulation',38.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 340138',0);
INSERT INTO "usage" VALUES('EPSG','11688','other_transformation','EPSG','15677','EPSG','2507','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15678','Tokyo + JSLD height to WGS 84 (89)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.87,'EPSG','9104','EPSG','8602','Longitude offset',-8.23,'EPSG','9104','EPSG','8604','Geoid undulation',29.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332129',0);
INSERT INTO "usage" VALUES('EPSG','11689','other_transformation','EPSG','15678','EPSG','2508','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15679','Tokyo + JSLD height to WGS 84 (90)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.84,'EPSG','9104','EPSG','8602','Longitude offset',-8.44,'EPSG','9104','EPSG','8604','Geoid undulation',30.6,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332130',0);
INSERT INTO "usage" VALUES('EPSG','11690','other_transformation','EPSG','15679','EPSG','2509','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15680','Tokyo + JSLD height to WGS 84 (91)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.94,'EPSG','9104','EPSG','8602','Longitude offset',-8.71,'EPSG','9104','EPSG','8604','Geoid undulation',30.2,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332131',0);
INSERT INTO "usage" VALUES('EPSG','11691','other_transformation','EPSG','15680','EPSG','2510','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15681','Tokyo + JSLD height to WGS 84 (92)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',11.99,'EPSG','9104','EPSG','8602','Longitude offset',-9.02,'EPSG','9104','EPSG','8604','Geoid undulation',30.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332132',0);
INSERT INTO "usage" VALUES('EPSG','11692','other_transformation','EPSG','15681','EPSG','2511','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15682','Tokyo + JSLD height to WGS 84 (93)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.05,'EPSG','9104','EPSG','8602','Longitude offset',-9.36,'EPSG','9104','EPSG','8604','Geoid undulation',35.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332133',0);
INSERT INTO "usage" VALUES('EPSG','11693','other_transformation','EPSG','15682','EPSG','2512','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15683','Tokyo + JSLD height to WGS 84 (94)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.1,'EPSG','9104','EPSG','8602','Longitude offset',-9.64,'EPSG','9104','EPSG','8604','Geoid undulation',35.5,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332134',0);
INSERT INTO "usage" VALUES('EPSG','11694','other_transformation','EPSG','15683','EPSG','2513','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15684','Tokyo + JSLD height to WGS 84 (95)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.1,'EPSG','9104','EPSG','8602','Longitude offset',-10.08,'EPSG','9104','EPSG','8604','Geoid undulation',37.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332135',0);
INSERT INTO "usage" VALUES('EPSG','11695','other_transformation','EPSG','15684','EPSG','2514','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15685','Tokyo + JSLD height to WGS 84 (96)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.07,'EPSG','9104','EPSG','8602','Longitude offset',-10.25,'EPSG','9104','EPSG','8604','Geoid undulation',37.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 332136',0);
INSERT INTO "usage" VALUES('EPSG','11696','other_transformation','EPSG','15685','EPSG','2515','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15686','Tokyo + JSLD height to WGS 84 (97)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.0,'EPSG','9104','EPSG','8602','Longitude offset',-8.15,'EPSG','9104','EPSG','8604','Geoid undulation',32.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324129',0);
INSERT INTO "usage" VALUES('EPSG','11697','other_transformation','EPSG','15686','EPSG','2516','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15687','Tokyo + JSLD height to WGS 84 (98)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.06,'EPSG','9104','EPSG','8602','Longitude offset',-8.38,'EPSG','9104','EPSG','8604','Geoid undulation',31.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324130',0);
INSERT INTO "usage" VALUES('EPSG','11698','other_transformation','EPSG','15687','EPSG','2517','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15688','Tokyo + JSLD height to WGS 84 (99)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.17,'EPSG','9104','EPSG','8602','Longitude offset',-8.69,'EPSG','9104','EPSG','8604','Geoid undulation',30.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324131',0);
INSERT INTO "usage" VALUES('EPSG','11699','other_transformation','EPSG','15688','EPSG','2518','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15689','Tokyo + JSLD height to WGS 84 (100)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.23,'EPSG','9104','EPSG','8602','Longitude offset',-8.99,'EPSG','9104','EPSG','8604','Geoid undulation',31.7,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324132',0);
INSERT INTO "usage" VALUES('EPSG','11700','other_transformation','EPSG','15689','EPSG','2519','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15690','Tokyo + JSLD height to WGS 84 (101)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.21,'EPSG','9104','EPSG','8602','Longitude offset',-9.21,'EPSG','9104','EPSG','8604','Geoid undulation',34.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324133',0);
INSERT INTO "usage" VALUES('EPSG','11701','other_transformation','EPSG','15690','EPSG','2520','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15691','Tokyo + JSLD height to WGS 84 (102)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.28,'EPSG','9104','EPSG','8602','Longitude offset',-9.6,'EPSG','9104','EPSG','8604','Geoid undulation',33.3,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 324134',0);
INSERT INTO "usage" VALUES('EPSG','11702','other_transformation','EPSG','15691','EPSG','2521','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15692','Tokyo + JSLD height to WGS 84 (103)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.28,'EPSG','9104','EPSG','8602','Longitude offset',-8.25,'EPSG','9104','EPSG','8604','Geoid undulation',31.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 320130',0);
INSERT INTO "usage" VALUES('EPSG','11703','other_transformation','EPSG','15692','EPSG','2522','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15693','Tokyo + JSLD height to WGS 84 (104)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.37,'EPSG','9104','EPSG','8602','Longitude offset',-8.55,'EPSG','9104','EPSG','8604','Geoid undulation',29.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 320131',0);
INSERT INTO "usage" VALUES('EPSG','11704','other_transformation','EPSG','15693','EPSG','2523','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15694','Tokyo + JSLD height to WGS 84 (105)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.53,'EPSG','9104','EPSG','8602','Longitude offset',-8.21,'EPSG','9104','EPSG','8604','Geoid undulation',31.0,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 320132',0);
INSERT INTO "usage" VALUES('EPSG','11705','other_transformation','EPSG','15694','EPSG','2524','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15695','Tokyo + JSLD height to WGS 84 (106)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.57,'EPSG','9104','EPSG','8602','Longitude offset',-8.4,'EPSG','9104','EPSG','8604','Geoid undulation',28.4,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 312130',0);
INSERT INTO "usage" VALUES('EPSG','11706','other_transformation','EPSG','15695','EPSG','2525','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15696','Tokyo + JSLD height to WGS 84 (107)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',12.71,'EPSG','9104','EPSG','8602','Longitude offset',-8.17,'EPSG','9104','EPSG','8604','Geoid undulation',29.9,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 312131',0);
INSERT INTO "usage" VALUES('EPSG','11707','other_transformation','EPSG','15696','EPSG','2526','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15697','Tokyo + JSLD height to WGS 84 (6)','','EPSG','9618','Geographic2D with Height Offsets','EPSG','7414','EPSG','4979',1.0,'EPSG','8601','Latitude offset',7.92,'EPSG','9104','EPSG','8602','Longitude offset',-13.88,'EPSG','9104','EPSG','8604','Geoid undulation',26.1,'EPSG','9001',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'GSI-Jpn 452141',0);
INSERT INTO "usage" VALUES('EPSG','11708','other_transformation','EPSG','15697','EPSG','2425','EPSG','1158');
INSERT INTO "other_transformation" VALUES('EPSG','15857','IGN Astro 1960 / UTM zone 28N to Mauritania 1999 / UTM zone 28N (1)','Parameter values consistent with the OGP Affine parametric transformation method derived by OGP from the published Helmert 2D parameter values. May be used for transformations to WGS 84 - see tfm code 15861.','EPSG','9624','Affine parametric transformation','EPSG','3367','EPSG','3343',40.0,'EPSG','8623','A0',-532.876,'EPSG','9001','EPSG','8624','A1',1.00017216658401,'EPSG','9203','EPSG','8625','A2',9.029305555e-05,'EPSG','9203','EPSG','8639','B0',-34.015,'EPSG','9001','EPSG','8640','B1',-9.029305555e-05,'EPSG','9203','EPSG','8641','B2',1.00017216658401,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'MMI-Mau W',0);
INSERT INTO "usage" VALUES('EPSG','11868','other_transformation','EPSG','15857','EPSG','2971','EPSG','1249');
INSERT INTO "other_transformation" VALUES('EPSG','15858','IGN Astro 1960 / UTM zone 29N to Mauritania 1999 / UTM zone 29N (1)','Parameter values consistent with the OGP Affine parametric transformation method derived by OGP from the published Helmert 2D parameter values. May be used for transformations to WGS 84 - see tfm code 15862.','EPSG','9624','Affine parametric transformation','EPSG','3368','EPSG','3344',40.0,'EPSG','8623','A0',-409.264,'EPSG','9001','EPSG','8624','A1',1.00017432259949,'EPSG','9203','EPSG','8625','A2',9.14562824e-05,'EPSG','9203','EPSG','8639','B0',-88.803,'EPSG','9001','EPSG','8640','B1',-9.14562824e-05,'EPSG','9203','EPSG','8641','B2',1.00017432259949,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'MMI-Mau C',0);
INSERT INTO "usage" VALUES('EPSG','11869','other_transformation','EPSG','15858','EPSG','2970','EPSG','1249');
INSERT INTO "other_transformation" VALUES('EPSG','15859','IGN Astro 1960 / UTM zone 30N to Mauritania 1999 / UTM zone 30N (1)','Parameter values consistent with the OGP Affine parametric transformation method derived by OGP from the published Helmert 2D parameter values. May be used for transformations to WGS 84 - see tfm code 15863.','EPSG','9624','Affine parametric transformation','EPSG','3369','EPSG','3345',40.0,'EPSG','8623','A0',-286.351,'EPSG','9001','EPSG','8624','A1',1.0001754456884,'EPSG','9203','EPSG','8625','A2',9.270672363e-05,'EPSG','9203','EPSG','8639','B0',-146.722,'EPSG','9001','EPSG','8640','B1',-9.270672363e-05,'EPSG','9203','EPSG','8641','B2',1.0001754456884,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'MMI-Mau E',0);
INSERT INTO "usage" VALUES('EPSG','11870','other_transformation','EPSG','15859','EPSG','2969','EPSG','1249');
INSERT INTO "other_transformation" VALUES('EPSG','15861','IGN Astro 1960 / UTM zone 28N to WGS 84 / UTM zone 28N (1)','Transformation taken from IGN Astro 1960 / UTM zone 28N to Mauritania 1999 / UTM zone 28N (1) (tfm code 15857) assuming that Mauritania 1999 is equivalent to WGS 84 within the accuracy of this tfm.','EPSG','9624','Affine parametric transformation','EPSG','3367','EPSG','32628',40.0,'EPSG','8623','A0',-532.876,'EPSG','9001','EPSG','8624','A1',1.000172166584,'EPSG','9203','EPSG','8625','A2',9.029305555e-05,'EPSG','9203','EPSG','8639','B0',-34.015,'EPSG','9001','EPSG','8640','B1',-9.029305555e-05,'EPSG','9203','EPSG','8641','B2',1.000172166584,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Mau W',0);
INSERT INTO "usage" VALUES('EPSG','11872','other_transformation','EPSG','15861','EPSG','2971','EPSG','1252');
INSERT INTO "other_transformation" VALUES('EPSG','15862','IGN Astro 1960 / UTM zone 29N to WGS 84 / UTM zone 29N (1)','Transformation taken from IGN Astro 1960 / UTM zone 29N to Mauritania 1999 / UTM zone 29N (1) (tfm code 15858) assuming that Mauritania 1999 is equivalent to WGS 84 within the accuracy of this tfm.','EPSG','9624','Affine parametric transformation','EPSG','3368','EPSG','32629',1.0,'EPSG','8623','A0',-409.264,'EPSG','9001','EPSG','8624','A1',1.0001743225995,'EPSG','9203','EPSG','8625','A2',9.14562824e-05,'EPSG','9203','EPSG','8639','B0',-88.803,'EPSG','9001','EPSG','8640','B1',-9.14562824e-05,'EPSG','9203','EPSG','8641','B2',1.0001743225995,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Mau C',0);
INSERT INTO "usage" VALUES('EPSG','11873','other_transformation','EPSG','15862','EPSG','2970','EPSG','1252');
INSERT INTO "other_transformation" VALUES('EPSG','15863','IGN Astro 1960 / UTM zone 30N to WGS 84 / UTM zone 30N (1)','Transformation taken from IGN Astro 1960 / UTM zone 30N to Mauritania 1999 / UTM zone 30N (1) (tfm code 15859) assuming that Mauritania 1999 is equivalent to WGS 84 within the accuracy of this tfm.','EPSG','9624','Affine parametric transformation','EPSG','3369','EPSG','32630',1.0,'EPSG','8623','A0',-286.351,'EPSG','9001','EPSG','8624','A1',1.0001754456884,'EPSG','9203','EPSG','8625','A2',9.270672363e-05,'EPSG','9203','EPSG','8639','B0',-146.722,'EPSG','9001','EPSG','8640','B1',-9.270672363e-05,'EPSG','9203','EPSG','8641','B2',1.0001754456884,'EPSG','9203',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'OGP-Mau E',0);
INSERT INTO "usage" VALUES('EPSG','11874','other_transformation','EPSG','15863','EPSG','2969','EPSG','1252');
