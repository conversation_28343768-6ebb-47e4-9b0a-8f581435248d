--- This file has been generated by scripts/build_db.py. DO NOT EDIT !

INSERT INTO "axis" VALUES('EPSG','32773','Easting','M','east','EPSG','1024',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32774','Northing','P','north','EPSG','1024',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1062','Easting','X','North along 130°W','EPSG','1025',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1063','Northing','Y','North along 140°E','EPSG','1025',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1065','Easting','E','South along 90°E','EPSG','1026',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1066','Northing','N','South along 180°E','EPSG','1026',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1056','Easting','E','North along 90°E','EPSG','1027',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1058','Northing','N','North along 0°E','EPSG','1027',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1073','Easting','E','east','EPSG','1028',1,'EPSG','9037');
INSERT INTO "axis" VALUES('EPSG','1074','Northing','N','north','EPSG','1028',2,'EPSG','9037');
INSERT INTO "axis" VALUES('EPSG','1078','Northing','N','north','EPSG','1029',1,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','1079','Easting','E','east','EPSG','1029',2,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','1101','Northing','Y','north','EPSG','1031',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1102','Westing','X','west','EPSG','1031',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1110','Plant East','x','east','EPSG','1032',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1111','Plant North','y','north','EPSG','1032',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1112','Gravity-related height','z','up','EPSG','1032',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1428','Bin grid I','I','J-axis plus 90 degrees','EPSG','1033',1,'EPSG','1024');
INSERT INTO "axis" VALUES('EPSG','1429','Bin grid J','J','See associated operation','EPSG','1033',2,'EPSG','1024');
INSERT INTO "axis" VALUES('EPSG','1431','Bin grid I','I','J-axis minus 90 degrees','EPSG','1034',1,'EPSG','1024');
INSERT INTO "axis" VALUES('EPSG','1432','Bin grid J','J','See associated operation','EPSG','1034',2,'EPSG','1024');
INSERT INTO "axis" VALUES('EPSG','1466','Easting','X','South along 180°E','EPSG','1035',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1467','Northing','Y','South along 90°W','EPSG','1035',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1471','Easting','X','South along 57°E','EPSG','1036',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1472','Northing','Y','South along 147°E','EPSG','1036',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1476','Easting','X','South along 108°E','EPSG','1037',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1477','Northing','Y','South along 162°W','EPSG','1037',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1481','Easting','X','South along 165°W','EPSG','1038',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1482','Northing','Y','South along 75°W','EPSG','1038',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1486','Easting','E','east','EPSG','1039',1,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','1487','Northing','N','north','EPSG','1039',2,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','32855','Forward','x','forward','EPSG','1040',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32856','Starboard','y','starboard','EPSG','1040',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32857','Platform Up','z','up','EPSG','1040',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32858','Forward','x','forward','EPSG','1041',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32859','Starboard','y','starboard','EPSG','1041',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32860','Platform Down','z','down','EPSG','1041',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32861','Starboard','x','starboard','EPSG','1042',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32862','Forward','y','forward','EPSG','1042',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32863','Platform Up','z','up','EPSG','1042',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1525','Northing','N','North along 180°E','EPSG','1044',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1526','Easting','E','North along 90°W','EPSG','1044',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32864','Starboard','x','starboard','EPSG','1045',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32865','Forward','y','forward','EPSG','1045',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32866','Platform Down','z','down','EPSG','1045',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1042','Local northing','n','north','EPSG','1047',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1043','Local easting','e','east','EPSG','1047',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1049','Local northing','n','north','EPSG','1048',1,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','1050','Local easting','e','east','EPSG','1048',2,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','1','Easting','E','east','EPSG','4400',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','2','Northing','N','north','EPSG','4400',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','3','Easting','E','east','EPSG','4401',1,'EPSG','9062');
INSERT INTO "axis" VALUES('EPSG','4','Northing','N','north','EPSG','4401',2,'EPSG','9062');
INSERT INTO "axis" VALUES('EPSG','5','Easting','E','east','EPSG','4402',1,'EPSG','9042');
INSERT INTO "axis" VALUES('EPSG','6','Northing','N','north','EPSG','4402',2,'EPSG','9042');
INSERT INTO "axis" VALUES('EPSG','7','Easting','E','east','EPSG','4403',1,'EPSG','9005');
INSERT INTO "axis" VALUES('EPSG','8','Northing','N','north','EPSG','4403',2,'EPSG','9005');
INSERT INTO "axis" VALUES('EPSG','9','Easting','E','east','EPSG','4404',1,'EPSG','9094');
INSERT INTO "axis" VALUES('EPSG','10','Northing','N','north','EPSG','4404',2,'EPSG','9094');
INSERT INTO "axis" VALUES('EPSG','11','Easting','E','east','EPSG','4405',1,'EPSG','9041');
INSERT INTO "axis" VALUES('EPSG','12','Northing','N','north','EPSG','4405',2,'EPSG','9041');
INSERT INTO "axis" VALUES('EPSG','13','Easting','X','east','EPSG','4406',1,'EPSG','9036');
INSERT INTO "axis" VALUES('EPSG','14','Northing','Y','north','EPSG','4406',2,'EPSG','9036');
INSERT INTO "axis" VALUES('EPSG','15','Easting','E','east','EPSG','4407',1,'EPSG','9039');
INSERT INTO "axis" VALUES('EPSG','16','Northing','N','north','EPSG','4407',2,'EPSG','9039');
INSERT INTO "axis" VALUES('EPSG','17','Easting','E','east','EPSG','4408',1,'EPSG','9084');
INSERT INTO "axis" VALUES('EPSG','18','Northing','N','north','EPSG','4408',2,'EPSG','9084');
INSERT INTO "axis" VALUES('EPSG','19','Easting','E','east','EPSG','4409',1,'EPSG','9040');
INSERT INTO "axis" VALUES('EPSG','20','Northing','N','north','EPSG','4409',2,'EPSG','9040');
INSERT INTO "axis" VALUES('EPSG','181','Easting','E','east','EPSG','4410',1,'EPSG','9301');
INSERT INTO "axis" VALUES('EPSG','182','Northing','N','north','EPSG','4410',2,'EPSG','9301');
INSERT INTO "axis" VALUES('EPSG','32875','Easting','E','South along 90°E','EPSG','4460',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32876','Northing','N','South along 180°E','EPSG','4460',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','205','Topocentric East','U','east','EPSG','4461',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','206','Topocentric North','V','north','EPSG','4461',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','207','Topocentric height','W','up','EPSG','4461',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','193','Easting','X','South along 180°W','EPSG','4462',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','194','Northing','Y','South along 90°W','EPSG','4462',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','195','Easting','X','South along 100°E','EPSG','4463',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','196','Northing','Y','South along 170°W','EPSG','4463',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','197','Easting','X','South along 90°W','EPSG','4464',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','198','Northing','Y','South along 0°E','EPSG','4464',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','199','Easting','X','South along 50°E','EPSG','4465',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','200','Northing','Y','South along 140°E','EPSG','4465',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','201','Easting','X','South along 10°W','EPSG','4466',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','202','Northing','Y','South along 80°E','EPSG','4466',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','203','Easting','X','South along 60°W','EPSG','4467',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','204','Northing','Y','South along 30°E','EPSG','4467',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','191','Easting','X','South along 45°E','EPSG','4468',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','192','Northing','Y','South along 135°E','EPSG','4468',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','187','Easting','X','South along 90°E','EPSG','4469',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','188','Northing','Y','South along 180°E','EPSG','4469',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','185','Easting','X','North along 90°E','EPSG','4470',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','186','Northing','Y','North along 0°E','EPSG','4470',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','137','Easting','E','North along 75°W','EPSG','4471',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','138','Northing','N','North along 165°W','EPSG','4471',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','156','Easting','E','North along 60°W','EPSG','4472',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','139','Northing','N','North along 150°W','EPSG','4472',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','157','Easting','E','North along 45°W','EPSG','4473',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','140','Northing','N','North along 135°W','EPSG','4473',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','158','Easting','E','North along 15°W','EPSG','4474',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','141','Northing','N','North along 105°W','EPSG','4474',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','159','Easting','E','North along 0°E','EPSG','4475',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','142','Northing','N','North along 90°W','EPSG','4475',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','160','Easting','E','North along 15°E','EPSG','4476',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','143','Northing','N','North along 75°W','EPSG','4476',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','161','Easting','E','North along 45°E','EPSG','4477',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','144','Northing','N','North along 45°W','EPSG','4477',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','162','Easting','E','North along 60°E','EPSG','4478',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','145','Northing','N','North along 30°W','EPSG','4478',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','163','Easting','E','North along 75°E','EPSG','4479',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','146','Northing','N','North along 15°W','EPSG','4479',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','164','Easting','E','North along 105°E','EPSG','4480',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','147','Northing','N','North along 15°E','EPSG','4480',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','165','Easting','E','North along 120°E','EPSG','4481',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','148','Northing','N','North along 30°E','EPSG','4481',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','166','Easting','E','North along 135°E','EPSG','4482',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','149','Northing','N','North along 45°E','EPSG','4482',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','167','Easting','E','North along 165°E','EPSG','4483',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','150','Northing','N','North along 75°E','EPSG','4483',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','168','Easting','E','North along 180°E','EPSG','4484',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','151','Northing','N','North along 90°E','EPSG','4484',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','169','Easting','E','North along 165°W','EPSG','4485',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','152','Northing','N','North along 105°E','EPSG','4485',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','170','Easting','E','North along 135°W','EPSG','4486',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','153','Northing','N','North along 135°E','EPSG','4486',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','171','Easting','E','North along 120°W','EPSG','4487',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','154','Northing','N','North along 150°E','EPSG','4487',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','172','Easting','E','North along 105°W','EPSG','4488',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','155','Northing','N','North along 165°E','EPSG','4488',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','21','Easting','E','North along 160°E','EPSG','4489',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','22','Northing','N','North along 70°E','EPSG','4489',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','23','Easting','E','North along 90°E','EPSG','4490',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','24','Northing','N','North along 0°E','EPSG','4490',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','26','Westing','W','west','EPSG','4491',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','25','Northing','N','north','EPSG','4491',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','27','First local axis','X','North along 130°W','EPSG','4492',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','28','Second local axis','Y','North along 140°E','EPSG','4492',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','30','Northing','N','South along 180°E','EPSG','4493',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','29','Easting','E','South along 90°E','EPSG','4493',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32','Northing','N','North along 0°E','EPSG','4494',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','31','Easting','E','North along 90°E','EPSG','4494',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','33','Easting','X','east','EPSG','4495',1,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','34','Northing','Y','north','EPSG','4495',2,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','35','Easting','E(X)','east','EPSG','4496',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','36','Northing','N(Y)','north','EPSG','4496',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','37','Easting','X','east','EPSG','4497',1,'EPSG','9003');
INSERT INTO "axis" VALUES('EPSG','38','Northing','Y','north','EPSG','4497',2,'EPSG','9003');
INSERT INTO "axis" VALUES('EPSG','39','Easting','Y','east','EPSG','4498',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','40','Northing','X','north','EPSG','4498',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','41','Easting','X','east','EPSG','4499',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','42','Northing','Y','north','EPSG','4499',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','44','Northing','N','north','EPSG','4500',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','43','Easting','E','east','EPSG','4500',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','45','Northing','N','north','EPSG','4501',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','46','Westing','E','west','EPSG','4501',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','189','Northing','N','north','EPSG','4502',1,'EPSG','9005');
INSERT INTO "axis" VALUES('EPSG','190','Easting','E','east','EPSG','4502',2,'EPSG','9005');
INSERT INTO "axis" VALUES('EPSG','48','Northing','X','north','EPSG','4530',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','47','Easting','Y','east','EPSG','4530',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','50','Northing','x','north','EPSG','4531',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','49','Easting','y','east','EPSG','4531',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','52','Northing','Y','north','EPSG','4532',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','51','Easting','X','east','EPSG','4532',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','180','Northing','X','north','EPSG','4533',1,'EPSG','9098');
INSERT INTO "axis" VALUES('EPSG','179','Easting','Y','east','EPSG','4533',2,'EPSG','9098');
INSERT INTO "axis" VALUES('EPSG','183','Northing','none','north','EPSG','4534',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','184','Easting','none','east','EPSG','4534',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','63','Geodetic latitude','Lat','north','EPSG','6405',1,'EPSG','9102');
INSERT INTO "axis" VALUES('EPSG','64','Geodetic longitude','Long','east','EPSG','6405',2,'EPSG','9102');
INSERT INTO "axis" VALUES('EPSG','65','Geodetic latitude','Lat','north','EPSG','6406',1,'EPSG','9116');
INSERT INTO "axis" VALUES('EPSG','66','Geodetic longitude','Long','east','EPSG','6406',2,'EPSG','9116');
INSERT INTO "axis" VALUES('EPSG','106','Geodetic latitude','Lat','north','EPSG','6422',1,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','107','Geodetic longitude','Lon','east','EPSG','6422',2,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','115','Geocentric X','X','geocentricX','EPSG','6500',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','116','Geocentric Y','Y','geocentricY','EPSG','6500',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','117','Geocentric Z','Z','geocentricZ','EPSG','6500',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','119','Southing','X','south','EPSG','6501',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','118','Westing','Y','west','EPSG','6501',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','120','Westing','Y','west','EPSG','6502',1,'EPSG','9031');
INSERT INTO "axis" VALUES('EPSG','121','Southing','X','south','EPSG','6502',2,'EPSG','9031');
INSERT INTO "axis" VALUES('EPSG','122','Westing','Y','west','EPSG','6503',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','123','Southing','X','south','EPSG','6503',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32877','Plant North','n','northWest','EPSG','6504',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32878','Plant East','e','northEast','EPSG','6504',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32873','First local axis','n','northWest','EPSG','6505',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32874','Second local axis','e','northEast','EPSG','6505',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','128','First local axis','I','east-south-east','EPSG','6506',1,'EPSG','9205');
INSERT INTO "axis" VALUES('EPSG','129','Second local axis','J','north-north-east','EPSG','6506',2,'EPSG','9204');
INSERT INTO "axis" VALUES('EPSG','130','First local axis','X','north','EPSG','6507',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','131','Second local axis','Y','west','EPSG','6507',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','133','Bin grid J','J','north north east','EPSG','6508',1,'EPSG','9209');
INSERT INTO "axis" VALUES('EPSG','132','Bin grid I','I','east south east','EPSG','6508',2,'EPSG','9208');
INSERT INTO "axis" VALUES('EPSG','135','Southing','P','south','EPSG','6509',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','134','Westing','M','west','EPSG','6509',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32871','Plant East','x','northEast','EPSG','6510',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32872','Plant North','y','northWest','EPSG','6510',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','211','Plant East','x','east','EPSG','6512',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','212','Plant North','y','north','EPSG','6512',2,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','213','Local height','z','up','EPSG','6512',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','32890','Bin grid I','I','columnPositive','EPSG','32760',1,NULL,NULL);
INSERT INTO "axis" VALUES('EPSG','32891','Bin grid J','J','rowPositive','EPSG','32760',2,NULL,NULL);
INSERT INTO "axis" VALUES('EPSG','32894','Bin grid I','I','columnNegative','EPSG','32761',1,NULL,NULL);
INSERT INTO "axis" VALUES('EPSG','32895','Bin grid J','J','rowPositive','EPSG','32761',2,NULL,NULL);
INSERT INTO "axis" VALUES('EPSG','1082','Gravity-related height','H','up','EPSG','1030',1,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','1516','Depth','D','down','EPSG','1043',1,'EPSG','9003');
INSERT INTO "axis" VALUES('EPSG','1051','Local depth','d','down','EPSG','1049',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','1053','Local depth','d','down','EPSG','1050',1,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','53','Geodetic latitude','Lat','north','EPSG','6401',1,'EPSG','9108');
INSERT INTO "axis" VALUES('EPSG','54','Geodetic longitude','Long','east','EPSG','6401',2,'EPSG','9108');
INSERT INTO "axis" VALUES('EPSG','55','Ellipsoidal height','h','up','EPSG','6401',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','56','Geodetic latitude','Lat','north','EPSG','6402',1,'EPSG','9108');
INSERT INTO "axis" VALUES('EPSG','57','Geodetic longitude','Long','east','EPSG','6402',2,'EPSG','9108');
INSERT INTO "axis" VALUES('EPSG','58','Geodetic latitude','Lat','north','EPSG','6403',1,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','59','Geodetic longitude','Lon','east','EPSG','6403',2,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','32879','Spherical latitude','lat','north','EPSG','6404',1,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','32880','Spherical longitude','lon','east','EPSG','6404',2,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','32881','Geocentric radius','R','up','EPSG','6404',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','67','Geodetic latitude','Lat','north','EPSG','6407',1,'EPSG','9117');
INSERT INTO "axis" VALUES('EPSG','68','Geodetic longitude','Long','east','EPSG','6407',2,'EPSG','9117');
INSERT INTO "axis" VALUES('EPSG','69','Geodetic latitude','Lat','north','EPSG','6408',1,'EPSG','9115');
INSERT INTO "axis" VALUES('EPSG','70','Geodetic longitude','Long','east','EPSG','6408',2,'EPSG','9115');
INSERT INTO "axis" VALUES('EPSG','71','Geodetic latitude','Lat','north','EPSG','6409',1,'EPSG','9118');
INSERT INTO "axis" VALUES('EPSG','72','Geodetic longitude','Long','east','EPSG','6409',2,'EPSG','9118');
INSERT INTO "axis" VALUES('EPSG','73','Geodetic latitude','Lat','north','EPSG','6410',1,'EPSG','9119');
INSERT INTO "axis" VALUES('EPSG','74','Geodetic longitude','Long','east','EPSG','6410',2,'EPSG','9119');
INSERT INTO "axis" VALUES('EPSG','75','Geodetic latitude','Lat','north','EPSG','6411',1,'EPSG','9107');
INSERT INTO "axis" VALUES('EPSG','76','Geodetic longitude','Long','east','EPSG','6411',2,'EPSG','9107');
INSERT INTO "axis" VALUES('EPSG','77','Geodetic latitude','Lat','north','EPSG','6412',1,'EPSG','9120');
INSERT INTO "axis" VALUES('EPSG','78','Geodetic longitude','Long','east','EPSG','6412',2,'EPSG','9120');
INSERT INTO "axis" VALUES('EPSG','79','Geodetic latitude','Lat','north','EPSG','6413',1,'EPSG','9102');
INSERT INTO "axis" VALUES('EPSG','80','Geodetic longitude','Long','east','EPSG','6413',2,'EPSG','9102');
INSERT INTO "axis" VALUES('EPSG','81','Ellipsoidal height','h','up','EPSG','6413',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','82','Geodetic latitude','Lat','north','EPSG','6414',1,'EPSG','9116');
INSERT INTO "axis" VALUES('EPSG','83','Geodetic longitude','Long','east','EPSG','6414',2,'EPSG','9116');
INSERT INTO "axis" VALUES('EPSG','84','Ellipsoidal height','h','up','EPSG','6414',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','85','Geodetic latitude','Lat','north','EPSG','6415',1,'EPSG','9117');
INSERT INTO "axis" VALUES('EPSG','86','Geodetic longitude','Long','east','EPSG','6415',2,'EPSG','9117');
INSERT INTO "axis" VALUES('EPSG','87','Ellipsoidal height','h','up','EPSG','6415',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','88','Geodetic latitude','Lat','north','EPSG','6416',1,'EPSG','9115');
INSERT INTO "axis" VALUES('EPSG','89','Geodetic longitude','Long','east','EPSG','6416',2,'EPSG','9115');
INSERT INTO "axis" VALUES('EPSG','90','Ellipsoidal height','h','up','EPSG','6416',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','91','Geodetic latitude','Lat','north','EPSG','6417',1,'EPSG','9118');
INSERT INTO "axis" VALUES('EPSG','92','Geodetic longitude','Long','east','EPSG','6417',2,'EPSG','9118');
INSERT INTO "axis" VALUES('EPSG','93','Ellipsoidal height','h','up','EPSG','6417',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','94','Geodetic latitude','Lat','north','EPSG','6418',1,'EPSG','9119');
INSERT INTO "axis" VALUES('EPSG','95','Geodetic longitude','Long','east','EPSG','6418',2,'EPSG','9119');
INSERT INTO "axis" VALUES('EPSG','96','Ellipsoidal height','h','up','EPSG','6418',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','97','Geodetic latitude','Lat','north','EPSG','6419',1,'EPSG','9107');
INSERT INTO "axis" VALUES('EPSG','98','Geodetic longitude','Long','east','EPSG','6419',2,'EPSG','9107');
INSERT INTO "axis" VALUES('EPSG','99','Ellipsoidal height','h','up','EPSG','6419',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','100','Geodetic latitude','Lat','north','EPSG','6420',1,'EPSG','9120');
INSERT INTO "axis" VALUES('EPSG','101','Geodetic longitude','Long','east','EPSG','6420',2,'EPSG','9120');
INSERT INTO "axis" VALUES('EPSG','102','Ellipsoidal height','h','up','EPSG','6420',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','103','Geodetic latitude','Lat','north','EPSG','6421',1,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','104','Geodetic longitude','Lon','east','EPSG','6421',2,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','105','Ellipsoidal height','h','up','EPSG','6421',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','108','Geodetic latitude','Lat','north','EPSG','6423',1,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','109','Geodetic longitude','Lon','east','EPSG','6423',2,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','110','Ellipsoidal height','h','up','EPSG','6423',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','220','Geodetic longitude','Lon','east','EPSG','6424',1,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','221','Geodetic latitude','Lat','north','EPSG','6424',2,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','215','Geodetic longitude','Lon','east','EPSG','6425',1,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','216','Geodetic latitude','Lat','north','EPSG','6425',2,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','222','Geodetic longitude','Lon','east','EPSG','6426',1,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','223','Geodetic latitude','Lat','north','EPSG','6426',2,'EPSG','9122');
INSERT INTO "axis" VALUES('EPSG','224','Ellipsoidal height','h','up','EPSG','6426',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','217','Geodetic longitude','Lon','east','EPSG','6427',1,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','218','Geodetic latitude','Lat','north','EPSG','6427',2,'EPSG','9105');
INSERT INTO "axis" VALUES('EPSG','219','Ellipsoidal height','h','up','EPSG','6427',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','225','Geodetic latitude','Lat','north','EPSG','6428',1,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','226','Geodetic longitude','Lon','east','EPSG','6428',2,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','227','Geodetic longitude','Lon','east','EPSG','6429',1,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','228','Geodetic latitude','Lat','north','EPSG','6429',2,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','230','Geodetic latitude','Lat','north','EPSG','6430',1,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','231','Geodetic longitude','Lon','east','EPSG','6430',2,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','232','Ellipsoidal height','h','up','EPSG','6430',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','233','Geodetic longitude','Lon','east','EPSG','6431',1,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','234','Geodetic latitude','Lat','north','EPSG','6431',2,'EPSG','9101');
INSERT INTO "axis" VALUES('EPSG','235','Ellipsoidal height','h','up','EPSG','6431',3,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','214','Depth','D','down','EPSG','6495',1,'EPSG','9002');
INSERT INTO "axis" VALUES('EPSG','111','Gravity-related height','H','up','EPSG','6496',1,'EPSG','9095');
INSERT INTO "axis" VALUES('EPSG','112','Gravity-related height','H','up','EPSG','6497',1,'EPSG','9003');
INSERT INTO "axis" VALUES('EPSG','113','Depth','D','down','EPSG','6498',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','114','Gravity-related height','H','up','EPSG','6499',1,'EPSG','9001');
INSERT INTO "axis" VALUES('EPSG','177','Inline','I','Along receiver lines','EPSG','6511',1,'EPSG','9208');
INSERT INTO "axis" VALUES('EPSG','178','Crossline','J','Across receiver lines','EPSG','6511',2,'EPSG','9209');
