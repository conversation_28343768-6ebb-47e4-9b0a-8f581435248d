.\" Man page generated from reStructuredText.
.
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.TH "GEOD" "1" "Jan 1, 2022" "8.2.1" "PROJ"
.SH NAME
geod \- Geodesic computations
.SH SYNOPSIS
.INDENT 0.0
.INDENT 3.5
\fBgeod\fP \fI+ellps=<ellipse>\fP [\fB\-afFIlptwW\fP [args]] [\fI+opt[=arg]\fP ...] file ...
.sp
\fBinvgeod\fP \fI+ellps=<ellipse>\fP [\fB\-afFIlptwW\fP [args]] [\fI+opt[=arg]\fP ...] file ...
.UNINDENT
.UNINDENT
.SH DESCRIPTION
.sp
\fBgeod\fP (direct) and \fBinvgeod\fP (inverse) perform geodesic
(Great Circle) computations for determining latitude, longitude and back
azimuth of a terminus point given a initial point latitude, longitude,
azimuth and distance (direct) or the forward and back azimuths and distance
between an initial and terminus point latitudes and longitudes (inverse).
The results are accurate to round off for |f| < 1/50, where
f is flattening.
.sp
\fBinvgeod\fP may not be available on all platforms; in this case
use \fI\%geod \-I\fP instead.
.sp
The following command\-line options can appear in any order:
.INDENT 0.0
.TP
.B \-I
Specifies that the inverse geodesic computation is to be performed. May be
used with execution of \fBgeod\fP as an alternative to \fBinvgeod\fP execution.
.UNINDENT
.INDENT 0.0
.TP
.B \-a
Latitude and longitudes of the initial and terminal points, forward and
back azimuths and distance are output.
.UNINDENT
.INDENT 0.0
.TP
.B \-t<a>
Where \fIa\fP specifies a character employed as the first character to denote a control
line to be passed through without processing.
.UNINDENT
.INDENT 0.0
.TP
.B \-le
Gives a listing of all the ellipsoids that may be selected with the
\fI+ellps=\fP option.
.UNINDENT
.INDENT 0.0
.TP
.B \-lu
Gives a listing of all the units that may be selected with the \fI+units=\fP
option. (Default units are meters.)
.UNINDENT
.INDENT 0.0
.TP
.B \-f <format>
Where \fIformat\fP is a printf format string to control the output form of the
geographic coordinate values. The default mode is DMS.
.UNINDENT
.INDENT 0.0
.TP
.B \-F <format>
Where \fIformat\fP is a printf format string to control the output form of the distance
value. The default mode is \fB"%.3f"\fP\&.
.UNINDENT
.INDENT 0.0
.TP
.B \-w<n>
Where \fIn\fP is the number of significant fractional digits to employ for seconds
output (when the option is not specified, \fB\-w3\fP is assumed).
.UNINDENT
.INDENT 0.0
.TP
.B \-W<n>
Where \fIn\fP is the number of significant fractional digits to employ for seconds
output. When \fB\-W\fP is employed the fields will be constant width
with leading zeroes.
.UNINDENT
.INDENT 0.0
.TP
.B \-p
This option causes the azimuthal values to be output as unsigned DMS
numbers between 0 and 360 degrees. Also note \fI\%\-f\fP\&.
.UNINDENT
.sp
The \fI+opt\fP command\-line options are associated with geodetic
parameters for specifying the ellipsoidal or sphere to use.
controls. The options are processed in left to right order
from the command line. Reentry of an option is ignored with
the first occurrence assumed to be the desired value.
.sp
See the PROJ documentation for a full list of these parameters and
controls.
.sp
One or more files (processed in left to right order) specify
the source of data to be transformed. A \fB\-\fP will specify the
location of processing standard input. If no files are specified,
the input is assumed to be from stdin.
.sp
For direct determinations input data must be in latitude, longitude,
azimuth and distance order and output will be latitude,
longitude and back azimuth of the terminus point. Latitude,
longitude of the initial and terminus point are input for the
inverse mode and respective forward and back azimuth from the
initial and terminus points are output along with the distance
between the points.
.sp
Input geographic coordinates (latitude and longitude) and
azimuthal data must be in decimal degrees or DMS format and
input distance data must be in units consistent with the ellipsoid
major axis or sphere radius units. The latitude must lie
in the range [\-90d,90d]. Output geographic coordinates will be
in DMS (if the \fI\%\-f\fP switch is not employed) to 0.001" with trailing,
zero\-valued minute\-second fields deleted. Output distance
data will be in the same units as the ellipsoid or sphere
radius.
.sp
The Earth\(aqs ellipsoidal figure may be selected in the same manner
as program \fBproj\fP by using \fI+ellps=\fP, \fI+a=\fP, \fI+es=\fP, etc.
.sp
\fBgeod\fP may also be used to determine intermediate points along
either a geodesic line between two points or along an arc of
specified distance from a geographic point. In both cases an
initial point must be specified with \fI+lat_1=lat\fP and \fI+lon_1=lon\fP
parameters and either a terminus point \fI+lat_2=lat\fP and
\fI+lon_2=lon\fP or a distance and azimuth from the initial point
with \fI+S=distance\fP and \fI+A=azimuth\fP must be specified.
.sp
If points along a geodesic are to be determined then either
\fI+n_S=integer\fP specifying the number of intermediate points
and/or \fI+del_S=distance\fP specifying the incremental distance
between points must be specified.
.sp
To determine points along an arc equidistant from the initial
point both \fI+del_A=angle\fP and \fI+n_A=integer\fP must be specified
which determine the respective angular increments and number of
points to be determined.
.SH EXAMPLES
.sp
The following script determines the geodesic azimuths and distance in U.S.
statute miles from Boston, MA, to Portland, OR:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
geod +ellps=clrk66 <<EOF \-I +units=us\-mi
42d15\(aqN 71d07\(aqW 45d31\(aqN 123d41\(aqW
EOF
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
which gives the results:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
\-66d31\(aq50.141" 75d39\(aq13.083" 2587.504
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
where the first two values are the azimuth from Boston to Portland,
the back azimuth from Portland to Boston followed by the distance.
.sp
An example of forward geodesic use is to use the Boston location
and determine Portland\(aqs location by azimuth and distance:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
geod +ellps=clrk66 <<EOF +units=us\-mi
42d15\(aqN 71d07\(aqW \-66d31\(aq50.141" 2587.504
EOF
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
which gives:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
45d31\(aq0.003"N 123d40\(aq59.985"W 75d39\(aq13.094"
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
\fBNOTE:\fP
.INDENT 0.0
.INDENT 3.5
Lack of precision in the distance value compromises the
precision of the Portland location.
.UNINDENT
.UNINDENT
.SH FURTHER READING
.INDENT 0.0
.IP 1. 3
\fI\%GeographicLib\fP\&.
.IP 2. 3
C. F. F. Karney, \fI\%Algorithms for Geodesics\fP, J. Geodesy \fB87\fP(1), 43–55 (2013);
\fI\%addenda\fP\&.
.IP 3. 3
\fI\%A geodesic bibliography\fP\&.
.UNINDENT
.SH SEE ALSO
.sp
\fBproj(1)\fP, \fBcs2cs(1)\fP, \fBcct(1)\fP, \fBgie(1)\fP, \fBprojinfo(1)\fP, \fBprojsync(1)\fP
.SH BUGS
.sp
A list of known bugs can be found at \fI\%https://github.com/OSGeo/PROJ/issues\fP
where new bug reports can be submitted to.
.SH HOME PAGE
.sp
\fI\%https://proj.org/\fP
.SH AUTHOR
Charles Karney
.SH COPYRIGHT
1983-2021
.\" Generated by docutils manpage writer.
.
