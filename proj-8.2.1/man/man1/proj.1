.\" Man page generated from reStructuredText.
.
.
.nr rst2man-indent-level 0
.
.de1 rstReportMargin
\\$1 \\n[an-margin]
level \\n[rst2man-indent-level]
level margin: \\n[rst2man-indent\\n[rst2man-indent-level]]
-
\\n[rst2man-indent0]
\\n[rst2man-indent1]
\\n[rst2man-indent2]
..
.de1 INDENT
.\" .rstReportMargin pre:
. RS \\$1
. nr rst2man-indent\\n[rst2man-indent-level] \\n[an-margin]
. nr rst2man-indent-level +1
.\" .rstReportMargin post:
..
.de UNINDENT
. RE
.\" indent \\n[an-margin]
.\" old: \\n[rst2man-indent\\n[rst2man-indent-level]]
.nr rst2man-indent-level -1
.\" new: \\n[rst2man-indent\\n[rst2man-indent-level]]
.in \\n[rst2man-indent\\n[rst2man-indent-level]]u
..
.TH "PROJ" "1" "Jan 1, 2022" "8.2.1" "PROJ"
.SH NAME
proj \- Cartographic projection filter
.SH SYNOPSIS
.INDENT 0.0
.INDENT 3.5
\fBproj\fP [\fB\-beEfiIlmorsStTvVwW\fP] [args]] [\fI+opt[=arg]\fP ...] file ...
.sp
\fBinvproj\fP [\fB\-beEfiIlmorsStTvVwW\fP] [args]] [\fI+opt[=arg]\fP ...] file ...
.UNINDENT
.UNINDENT
.SH DESCRIPTION
.sp
\fBproj\fP and \fBinvproj\fP perform respective forward and inverse
conversion of cartographic data to or from cartesian data with a wide
range of selectable projection functions.
.sp
\fBinvproj\fP may not be available on all platforms; in this case
use \fI\%proj \-I\fP instead.
.sp
The following control parameters can appear in any order
.INDENT 0.0
.TP
.B \-b
Special option for binary coordinate data input and output through standard
input and standard output. Data is assumed to be in system type double
floating point words. This option is to be used when \fBproj\fP is a child process
and allows bypassing formatting operations.
.UNINDENT
.INDENT 0.0
.TP
.B \-d <n>
.UNINDENT
.sp
New in version 5.2.0: Specify the number of decimals in the output.

.INDENT 0.0
.TP
.B \-i
Selects binary input only (see \fI\%\-b\fP).
.UNINDENT
.INDENT 0.0
.TP
.B \-I
Alternate method to specify inverse projection. Redundant when used with
\fBinvproj\fP\&.
.UNINDENT
.INDENT 0.0
.TP
.B \-o
Selects binary output only (see \fI\%\-b\fP).
.UNINDENT
.INDENT 0.0
.TP
.B \-t<a>
Where \fIa\fP specifies a character employed as the first character to denote a
control line to be passed through without processing. This option
applicable to ASCII input only. (# is the default value).
.UNINDENT
.INDENT 0.0
.TP
.B \-e <string>
Where \fIstring\fP is an arbitrary string to be output if an error is detected during
data transformations. The default value is a three character string: \fB*\et*\fP\&.
Note that if the \fI\%\-b\fP, \fI\%\-i\fP or \fI\%\-o\fP options are employed, an error
is returned as HUGE_VAL value for both return values.
.UNINDENT
.INDENT 0.0
.TP
.B \-E
Causes the input coordinates to be copied to the output line prior to
printing the converted values.
.UNINDENT
.INDENT 0.0
.TP
.B \-l<[=id]>
List projection identifiers that can be selected with \fI+proj\fP\&. \fBproj \-l=id\fP
gives expanded description of projection \fIid\fP, e.g. \fBproj \-l=merc\fP\&.
.UNINDENT
.INDENT 0.0
.TP
.B \-lp
List of all projection id that can be used with the \fI+proj\fP parameter.
Equivalent to \fBproj \-l\fP\&.
.UNINDENT
.INDENT 0.0
.TP
.B \-lP
Expanded description of all projections that can be used with the \fI+proj\fP
parameter.
.UNINDENT
.INDENT 0.0
.TP
.B \-le
List of all ellipsoids that can be selected with the \fI+ellps\fP parameters.
.UNINDENT
.INDENT 0.0
.TP
.B \-lu
List of all distance units that can be selected with the \fI+units\fP parameter.
.UNINDENT
.INDENT 0.0
.TP
.B \-r
This options reverses the order of the expected input from
longitude\-latitude or x\-y to latitude\-longitude or y\-x.
.UNINDENT
.INDENT 0.0
.TP
.B \-s
This options reverses the order of the output from x\-y or longitude\-latitude
to y\-x or latitude\-longitude.
.UNINDENT
.INDENT 0.0
.TP
.B \-S
Causes estimation of meridional and parallel scale factors, area scale
factor and angular distortion, and maximum and minimum scale factors to be
listed between <> for each input point. For conformal projections meridional
and parallel scales factors will be equal and angular distortion zero. Equal
area projections will have an area factor of 1.
.UNINDENT
.INDENT 0.0
.TP
.B \-m <mult>
The cartesian data may be scaled by the \fImult\fP parameter. When processing data
in a forward projection mode the cartesian output values are multiplied by
\fImult\fP otherwise the input cartesian values are divided by \fImult\fP before inverse
projection. If the first two characters of \fImult\fP are 1/ or 1: then the
reciprocal value of \fImult\fP is employed.
.UNINDENT
.INDENT 0.0
.TP
.B \-f <format>
Where \fIformat\fP is a printf format string to control the form of the output values.
For inverse projections, the output will be in degrees when this option is
employed. The default format is \fB"%.2f"\fP for forward projection and DMS for
inverse.
.UNINDENT
.INDENT 0.0
.TP
.B \-w<n>
Where \fIn\fP is the number of significant fractional digits to employ for seconds
output (when the option is not specified, \fB\-w3\fP is assumed).
.UNINDENT
.INDENT 0.0
.TP
.B \-W<n>
Where \fIn\fP is the number of significant fractional digits to employ for seconds
output. When \fB\-W\fP is employed the fields will be constant width
with leading zeroes.
.UNINDENT
.INDENT 0.0
.TP
.B \-v
Causes a listing of cartographic control parameters tested for and used by
the program to be printed prior to input data.
.UNINDENT
.INDENT 0.0
.TP
.B \-V
This option causes an expanded annotated listing of the characteristics of
the projected point. \fI\%\-v\fP is implied with this option.
.UNINDENT
.sp
The \fI+opt\fP run\-line arguments are associated with cartographic parameters.
Additional projection control parameters may be contained in two auxiliary
control files: the first is optionally referenced with the
\fI+init=file:id\fP and the second is always processed after the name of the
projection has been established from either the run\-line or the contents of
+init file. The environment parameter \fBPROJ_LIB\fP establishes the
default directory for a file reference without an absolute path. This is
also used for supporting files like datum shift files.
.sp
One or more files (processed in left to right order) specify the source of
data to be converted. A \fB\-\fP will specify the location of processing standard
input. If no files are specified, the input is assumed to be from stdin.
For ASCII input data the two data values must be in the first two white space
separated fields and when both input and output are ASCII all trailing
portions of the input line are appended to the output line.
.sp
Input geographic data (longitude and latitude) must be in DMS or decimal degrees format and input
cartesian data must be in units consistent with the ellipsoid major axis or
sphere radius units. Output geographic coordinates will be in DMS (if the
\fB\-w\fP switch is not employed) and precise to 0.001" with trailing, zero\-valued
minute\-second fields deleted.
.SH EXAMPLE
.sp
The following script
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
proj +proj=utm +lon_0=112w +ellps=clrk66 \-r <<EOF
45d15\(aq33.1" 111.5W
45d15.551666667N \-111d30
+45.25919444444 111d30\(aq000w
EOF
.ft P
.fi
.UNINDENT
.UNINDENT
.sp
will perform UTM forward projection with a standard UTM central meridian
nearest longitude 112W. The geographic values of this example are equivalent
and meant as examples of various forms of DMS input. The x\-y output
data will appear as three lines of:
.INDENT 0.0
.INDENT 3.5
.sp
.nf
.ft C
460769.27     5011648.45
.ft P
.fi
.UNINDENT
.UNINDENT
.SH OTHER PROGRAMS
.sp
The \fBproj\fP program is limited to converting between geographic and
projected coordinates within one datum.
.sp
The \fBcs2cs\fP program operates similarly, but allows translation
between any pair of definable coordinate reference systems, including
support for datum translation.
.SH SEE ALSO
.sp
\fBcs2cs(1)\fP, \fBcct(1)\fP, \fBgeod(1)\fP, \fBgie(1)\fP, \fBprojinfo(1)\fP, \fBprojsync(1)\fP
.SH BUGS
.sp
A list of known bugs can be found at \fI\%https://github.com/OSGeo/PROJ/issues\fP
where new bug reports can be submitted to.
.SH HOME PAGE
.sp
\fI\%https://proj.org/\fP
.SH AUTHOR
Gerald I. Evenden
.SH COPYRIGHT
1983-2021
.\" Generated by docutils manpage writer.
.
