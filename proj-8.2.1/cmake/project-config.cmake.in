# Configure @PROJECT_NAME@
#
# Set
#  @PROJECT_VARIANT_NAME@_FOUND = 1
#  @PROJECT_VARIANT_NAME@_INCLUDE_DIRS = /usr/local/include
#  @PROJECT_VARIANT_NAME@_LIBRARIES = @PROJECT_VARIANT_NAME@::proj
#  @PROJECT_VARIANT_NAME@_LIBRARY_DIRS = /usr/local/lib
#  @PROJECT_VARIANT_NAME@_BINARY_DIRS = /usr/local/bin
#  @PROJECT_VARIANT_NAME@_VERSION = 4.9.1 (for example)

# Tell the user project where to find our headers and libraries
get_filename_component (_DIR ${CMAKE_CURRENT_LIST_FILE} PATH)
get_filename_component (_ROOT "${_DIR}/@PROJECT_ROOT_DIR@" ABSOLUTE)
set (@PROJECT_VARIANT_NAME@_INCLUDE_DIRS "${_ROOT}/@INCLUDEDIR@")
set (@PROJECT_VARIANT_NAME@_LIBRARY_DIRS "${_ROOT}/@LIBDIR@")
set (@PROJECT_VARIANT_NAME@_BINARY_DIRS "${_ROOT}/@BINDIR@")

set (@PROJECT_VARIANT_NAME@_LIBRARIES @PROJECT_VARIANT_NAME@::proj)
# Read in the exported definition of the library
include ("${_DIR}/@<EMAIL>")
include ("${_DIR}/@<EMAIL>")

unset (_ROOT)
unset (_DIR)

if ("@PROJECT_VARIANT_NAME@" STREQUAL "PROJ4")
  # For backward compatibility with old releases of libgeotiff
  set (@PROJECT_VARIANT_NAME@_INCLUDE_DIR
    ${@PROJECT_VARIANT_NAME@_INCLUDE_DIRS})
endif ()
