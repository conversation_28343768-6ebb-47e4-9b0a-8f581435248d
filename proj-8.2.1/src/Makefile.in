# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@



VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
EXTRA_PROGRAMS = multistresstest$(EXEEXT)
TESTS = geodtest$(EXEEXT)
check_PROGRAMS = geodtest$(EXEEXT)
bin_PROGRAMS = proj$(EXEEXT) geod$(EXEEXT) cs2cs$(EXEEXT) gie$(EXEEXT) \
	cct$(EXEEXT) projinfo$(EXEEXT) $(am__EXEEXT_1)
noinst_PROGRAMS = invproj$(EXEEXT) invgeod$(EXEEXT)
subdir = src
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/ax_cflags_warn_all.m4 \
	$(top_srcdir)/m4/ax_check_compile_flag.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx.m4 \
	$(top_srcdir)/m4/ax_cxx_compile_stdcxx_11.m4 \
	$(top_srcdir)/m4/libtool.m4 $(top_srcdir)/m4/ltoptions.m4 \
	$(top_srcdir)/m4/ltsugar.m4 $(top_srcdir)/m4/ltversion.m4 \
	$(top_srcdir)/m4/lt~obsolete.m4 $(top_srcdir)/m4/pkg.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(include_HEADERS) \
	$(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = proj_config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
@HAVE_CURL_TRUE@am__EXEEXT_1 = projsync$(EXEEXT)
am__installdirs = "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" \
	"$(DESTDIR)$(includedir)"
PROGRAMS = $(bin_PROGRAMS) $(noinst_PROGRAMS)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__uninstall_files_from_dir = { \
  test -z "$$files" \
    || { test ! -d "$$dir" && test ! -f "$$dir" && test ! -r "$$dir"; } \
    || { echo " ( cd '$$dir' && rm -f" $$files ")"; \
         $(am__cd) "$$dir" && rm -f $$files; }; \
  }
LTLIBRARIES = $(lib_LTLIBRARIES)
libproj_la_DEPENDENCIES =
am__dirstamp = $(am__leading_dot)dirstamp
am_libproj_la_OBJECTS = iso19111/static.lo iso19111/util.lo \
	iso19111/metadata.lo iso19111/common.lo iso19111/crs.lo \
	iso19111/datum.lo iso19111/coordinatesystem.lo \
	iso19111/operation/concatenatedoperation.lo \
	iso19111/operation/coordinateoperationfactory.lo \
	iso19111/operation/conversion.lo \
	iso19111/operation/esriparammappings.lo \
	iso19111/operation/oputils.lo \
	iso19111/operation/parammappings.lo \
	iso19111/operation/projbasedoperation.lo \
	iso19111/operation/singleoperation.lo \
	iso19111/operation/transformation.lo \
	iso19111/operation/vectorofvaluesparams.lo iso19111/io.lo \
	iso19111/internal.lo iso19111/factory.lo iso19111/c_api.lo \
	projections/aeqd.lo projections/adams.lo projections/gnom.lo \
	projections/laea.lo projections/mod_ster.lo \
	projections/nsper.lo projections/nzmg.lo projections/ortho.lo \
	projections/stere.lo projections/sterea.lo projections/aea.lo \
	projections/bipc.lo projections/bonne.lo projections/eqdc.lo \
	projections/isea.lo projections/ccon.lo projections/imw_p.lo \
	projections/krovak.lo projections/lcc.lo projections/poly.lo \
	projections/rpoly.lo projections/sconics.lo \
	projections/rouss.lo projections/cass.lo projections/cc.lo \
	projections/cea.lo projections/eqc.lo projections/gall.lo \
	projections/labrd.lo projections/lsat.lo \
	projections/misrsom.lo projections/merc.lo projections/mill.lo \
	projections/ocea.lo projections/omerc.lo projections/somerc.lo \
	projections/tcc.lo projections/tcea.lo projections/times.lo \
	projections/tmerc.lo projections/tobmerc.lo \
	projections/airy.lo projections/aitoff.lo \
	projections/august.lo projections/bacon.lo \
	projections/bertin1953.lo projections/chamb.lo \
	projections/hammer.lo projections/lagrng.lo \
	projections/larr.lo projections/lask.lo projections/latlong.lo \
	projections/nicol.lo projections/ob_tran.lo projections/oea.lo \
	projections/tpeqd.lo projections/vandg.lo \
	projections/vandg2.lo projections/vandg4.lo \
	projections/wag7.lo projections/lcca.lo projections/geos.lo \
	projections/boggs.lo projections/collg.lo \
	projections/comill.lo projections/crast.lo \
	projections/denoy.lo projections/eck1.lo projections/eck2.lo \
	projections/eck3.lo projections/eck4.lo projections/eck5.lo \
	projections/fahey.lo projections/fouc_s.lo \
	projections/gins8.lo projections/gstmerc.lo \
	projections/gn_sinu.lo projections/goode.lo projections/igh.lo \
	projections/igh_o.lo projections/hatano.lo \
	projections/loxim.lo projections/mbt_fps.lo \
	projections/mbtfpp.lo projections/mbtfpq.lo \
	projections/moll.lo projections/nell.lo projections/nell_h.lo \
	projections/patterson.lo projections/putp2.lo \
	projections/putp3.lo projections/putp4p.lo \
	projections/putp5.lo projections/putp6.lo projections/qsc.lo \
	projections/robin.lo projections/s2.lo projections/sch.lo \
	projections/sts.lo projections/urm5.lo projections/urmfps.lo \
	projections/wag2.lo projections/wag3.lo projections/wink1.lo \
	projections/wink2.lo projections/healpix.lo \
	projections/natearth.lo projections/natearth2.lo \
	projections/calcofi.lo projections/eqearth.lo \
	projections/col_urban.lo conversions/axisswap.lo \
	conversions/cart.lo conversions/geoc.lo conversions/geocent.lo \
	conversions/noop.lo conversions/topocentric.lo \
	conversions/set.lo conversions/unitconvert.lo \
	transformations/affine.lo transformations/deformation.lo \
	transformations/helmert.lo transformations/hgridshift.lo \
	transformations/horner.lo transformations/molodensky.lo \
	transformations/vgridshift.lo transformations/xyzgridshift.lo \
	transformations/defmodel.lo transformations/tinshift.lo \
	aasincos.lo adjlon.lo dmstor.lo auth.lo deriv.lo ell_set.lo \
	ellps.lo factors.lo fwd.lo init.lo inv.lo list.lo malloc.lo \
	mlfn.lo msfn.lo proj_mdist.lo param.lo phi2.lo pr_list.lo \
	qsfn.lo strerrno.lo tsfn.lo units.lo ctx.lo log.lo zpoly1.lo \
	rtodms.lo release.lo gauss.lo generic_inverse.lo datums.lo \
	datum_set.lo mutex.lo initcache.lo geodesic.lo strtod.lo \
	4D_api.lo pipeline.lo internal.lo wkt_parser.lo wkt1_parser.lo \
	wkt1_generated_parser.lo wkt2_parser.lo \
	wkt2_generated_parser.lo proj_json_streaming_writer.lo \
	tracing.lo grids.lo filemanager.lo networkfilemanager.lo \
	sqlite3_utils.lo
libproj_la_OBJECTS = $(am_libproj_la_OBJECTS)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
libproj_la_LINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(libproj_la_LDFLAGS) $(LDFLAGS) -o $@
am_cct_OBJECTS = apps/cct.$(OBJEXT) apps/proj_strtod.$(OBJEXT)
cct_OBJECTS = $(am_cct_OBJECTS)
cct_DEPENDENCIES = libproj.la
am_cs2cs_OBJECTS = apps/cs2cs.$(OBJEXT) apps/emess.$(OBJEXT) \
	apps/utils.$(OBJEXT)
cs2cs_OBJECTS = $(am_cs2cs_OBJECTS)
cs2cs_DEPENDENCIES = libproj.la
am_geod_OBJECTS = apps/geod.$(OBJEXT) apps/geod_set.$(OBJEXT) \
	apps/geod_interface.$(OBJEXT) apps/emess.$(OBJEXT)
geod_OBJECTS = $(am_geod_OBJECTS)
geod_DEPENDENCIES = libproj.la
am_geodtest_OBJECTS = tests/geodtest.$(OBJEXT)
geodtest_OBJECTS = $(am_geodtest_OBJECTS)
geodtest_DEPENDENCIES = libproj.la
am_gie_OBJECTS = apps/gie.$(OBJEXT) apps/proj_strtod.$(OBJEXT)
gie_OBJECTS = $(am_gie_OBJECTS)
gie_DEPENDENCIES = libproj.la
am__objects_1 = apps/geod.$(OBJEXT) apps/geod_set.$(OBJEXT) \
	apps/geod_interface.$(OBJEXT) apps/emess.$(OBJEXT)
am_invgeod_OBJECTS = $(am__objects_1)
invgeod_OBJECTS = $(am_invgeod_OBJECTS)
invgeod_DEPENDENCIES = $(geod_LDADD)
am__objects_2 = apps/proj.$(OBJEXT) apps/emess.$(OBJEXT) \
	apps/utils.$(OBJEXT)
am_invproj_OBJECTS = $(am__objects_2)
invproj_OBJECTS = $(am_invproj_OBJECTS)
invproj_DEPENDENCIES = $(proj_LDADD)
am_multistresstest_OBJECTS = tests/multistresstest.$(OBJEXT)
multistresstest_OBJECTS = $(am_multistresstest_OBJECTS)
multistresstest_DEPENDENCIES = libproj.la
am_proj_OBJECTS = apps/proj.$(OBJEXT) apps/emess.$(OBJEXT) \
	apps/utils.$(OBJEXT)
proj_OBJECTS = $(am_proj_OBJECTS)
proj_DEPENDENCIES = libproj.la
am_projinfo_OBJECTS = apps/projinfo.$(OBJEXT)
projinfo_OBJECTS = $(am_projinfo_OBJECTS)
projinfo_DEPENDENCIES = libproj.la
am__projsync_SOURCES_DIST = apps/projsync.cpp
@HAVE_CURL_TRUE@am_projsync_OBJECTS = apps/projsync.$(OBJEXT)
projsync_OBJECTS = $(am_projsync_OBJECTS)
@HAVE_CURL_TRUE@projsync_DEPENDENCIES = libproj.la
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/4D_api.Plo ./$(DEPDIR)/aasincos.Plo \
	./$(DEPDIR)/adjlon.Plo ./$(DEPDIR)/auth.Plo \
	./$(DEPDIR)/ctx.Plo ./$(DEPDIR)/datum_set.Plo \
	./$(DEPDIR)/datums.Plo ./$(DEPDIR)/deriv.Plo \
	./$(DEPDIR)/dmstor.Plo ./$(DEPDIR)/ell_set.Plo \
	./$(DEPDIR)/ellps.Plo ./$(DEPDIR)/factors.Plo \
	./$(DEPDIR)/filemanager.Plo ./$(DEPDIR)/fwd.Plo \
	./$(DEPDIR)/gauss.Plo ./$(DEPDIR)/generic_inverse.Plo \
	./$(DEPDIR)/geodesic.Plo ./$(DEPDIR)/grids.Plo \
	./$(DEPDIR)/init.Plo ./$(DEPDIR)/initcache.Plo \
	./$(DEPDIR)/internal.Plo ./$(DEPDIR)/inv.Plo \
	./$(DEPDIR)/list.Plo ./$(DEPDIR)/log.Plo \
	./$(DEPDIR)/malloc.Plo ./$(DEPDIR)/mlfn.Plo \
	./$(DEPDIR)/msfn.Plo ./$(DEPDIR)/mutex.Plo \
	./$(DEPDIR)/networkfilemanager.Plo ./$(DEPDIR)/param.Plo \
	./$(DEPDIR)/phi2.Plo ./$(DEPDIR)/pipeline.Plo \
	./$(DEPDIR)/pr_list.Plo \
	./$(DEPDIR)/proj_json_streaming_writer.Plo \
	./$(DEPDIR)/proj_mdist.Plo ./$(DEPDIR)/qsfn.Plo \
	./$(DEPDIR)/release.Plo ./$(DEPDIR)/rtodms.Plo \
	./$(DEPDIR)/sqlite3_utils.Plo ./$(DEPDIR)/strerrno.Plo \
	./$(DEPDIR)/strtod.Plo ./$(DEPDIR)/tracing.Plo \
	./$(DEPDIR)/tsfn.Plo ./$(DEPDIR)/units.Plo \
	./$(DEPDIR)/wkt1_generated_parser.Plo \
	./$(DEPDIR)/wkt1_parser.Plo \
	./$(DEPDIR)/wkt2_generated_parser.Plo \
	./$(DEPDIR)/wkt2_parser.Plo ./$(DEPDIR)/wkt_parser.Plo \
	./$(DEPDIR)/zpoly1.Plo apps/$(DEPDIR)/cct.Po \
	apps/$(DEPDIR)/cs2cs.Po apps/$(DEPDIR)/emess.Po \
	apps/$(DEPDIR)/geod.Po apps/$(DEPDIR)/geod_interface.Po \
	apps/$(DEPDIR)/geod_set.Po apps/$(DEPDIR)/gie.Po \
	apps/$(DEPDIR)/proj.Po apps/$(DEPDIR)/proj_strtod.Po \
	apps/$(DEPDIR)/projinfo.Po apps/$(DEPDIR)/projsync.Po \
	apps/$(DEPDIR)/utils.Po conversions/$(DEPDIR)/axisswap.Plo \
	conversions/$(DEPDIR)/cart.Plo conversions/$(DEPDIR)/geoc.Plo \
	conversions/$(DEPDIR)/geocent.Plo \
	conversions/$(DEPDIR)/noop.Plo conversions/$(DEPDIR)/set.Plo \
	conversions/$(DEPDIR)/topocentric.Plo \
	conversions/$(DEPDIR)/unitconvert.Plo \
	iso19111/$(DEPDIR)/c_api.Plo iso19111/$(DEPDIR)/common.Plo \
	iso19111/$(DEPDIR)/coordinatesystem.Plo \
	iso19111/$(DEPDIR)/crs.Plo iso19111/$(DEPDIR)/datum.Plo \
	iso19111/$(DEPDIR)/factory.Plo iso19111/$(DEPDIR)/internal.Plo \
	iso19111/$(DEPDIR)/io.Plo iso19111/$(DEPDIR)/metadata.Plo \
	iso19111/$(DEPDIR)/static.Plo iso19111/$(DEPDIR)/util.Plo \
	iso19111/operation/$(DEPDIR)/concatenatedoperation.Plo \
	iso19111/operation/$(DEPDIR)/conversion.Plo \
	iso19111/operation/$(DEPDIR)/coordinateoperationfactory.Plo \
	iso19111/operation/$(DEPDIR)/esriparammappings.Plo \
	iso19111/operation/$(DEPDIR)/oputils.Plo \
	iso19111/operation/$(DEPDIR)/parammappings.Plo \
	iso19111/operation/$(DEPDIR)/projbasedoperation.Plo \
	iso19111/operation/$(DEPDIR)/singleoperation.Plo \
	iso19111/operation/$(DEPDIR)/transformation.Plo \
	iso19111/operation/$(DEPDIR)/vectorofvaluesparams.Plo \
	projections/$(DEPDIR)/adams.Plo projections/$(DEPDIR)/aea.Plo \
	projections/$(DEPDIR)/aeqd.Plo projections/$(DEPDIR)/airy.Plo \
	projections/$(DEPDIR)/aitoff.Plo \
	projections/$(DEPDIR)/august.Plo \
	projections/$(DEPDIR)/bacon.Plo \
	projections/$(DEPDIR)/bertin1953.Plo \
	projections/$(DEPDIR)/bipc.Plo projections/$(DEPDIR)/boggs.Plo \
	projections/$(DEPDIR)/bonne.Plo \
	projections/$(DEPDIR)/calcofi.Plo \
	projections/$(DEPDIR)/cass.Plo projections/$(DEPDIR)/cc.Plo \
	projections/$(DEPDIR)/ccon.Plo projections/$(DEPDIR)/cea.Plo \
	projections/$(DEPDIR)/chamb.Plo \
	projections/$(DEPDIR)/col_urban.Plo \
	projections/$(DEPDIR)/collg.Plo \
	projections/$(DEPDIR)/comill.Plo \
	projections/$(DEPDIR)/crast.Plo \
	projections/$(DEPDIR)/denoy.Plo projections/$(DEPDIR)/eck1.Plo \
	projections/$(DEPDIR)/eck2.Plo projections/$(DEPDIR)/eck3.Plo \
	projections/$(DEPDIR)/eck4.Plo projections/$(DEPDIR)/eck5.Plo \
	projections/$(DEPDIR)/eqc.Plo projections/$(DEPDIR)/eqdc.Plo \
	projections/$(DEPDIR)/eqearth.Plo \
	projections/$(DEPDIR)/fahey.Plo \
	projections/$(DEPDIR)/fouc_s.Plo \
	projections/$(DEPDIR)/gall.Plo projections/$(DEPDIR)/geos.Plo \
	projections/$(DEPDIR)/gins8.Plo \
	projections/$(DEPDIR)/gn_sinu.Plo \
	projections/$(DEPDIR)/gnom.Plo projections/$(DEPDIR)/goode.Plo \
	projections/$(DEPDIR)/gstmerc.Plo \
	projections/$(DEPDIR)/hammer.Plo \
	projections/$(DEPDIR)/hatano.Plo \
	projections/$(DEPDIR)/healpix.Plo \
	projections/$(DEPDIR)/igh.Plo projections/$(DEPDIR)/igh_o.Plo \
	projections/$(DEPDIR)/imw_p.Plo projections/$(DEPDIR)/isea.Plo \
	projections/$(DEPDIR)/krovak.Plo \
	projections/$(DEPDIR)/labrd.Plo projections/$(DEPDIR)/laea.Plo \
	projections/$(DEPDIR)/lagrng.Plo \
	projections/$(DEPDIR)/larr.Plo projections/$(DEPDIR)/lask.Plo \
	projections/$(DEPDIR)/latlong.Plo \
	projections/$(DEPDIR)/lcc.Plo projections/$(DEPDIR)/lcca.Plo \
	projections/$(DEPDIR)/loxim.Plo projections/$(DEPDIR)/lsat.Plo \
	projections/$(DEPDIR)/mbt_fps.Plo \
	projections/$(DEPDIR)/mbtfpp.Plo \
	projections/$(DEPDIR)/mbtfpq.Plo \
	projections/$(DEPDIR)/merc.Plo projections/$(DEPDIR)/mill.Plo \
	projections/$(DEPDIR)/misrsom.Plo \
	projections/$(DEPDIR)/mod_ster.Plo \
	projections/$(DEPDIR)/moll.Plo \
	projections/$(DEPDIR)/natearth.Plo \
	projections/$(DEPDIR)/natearth2.Plo \
	projections/$(DEPDIR)/nell.Plo \
	projections/$(DEPDIR)/nell_h.Plo \
	projections/$(DEPDIR)/nicol.Plo \
	projections/$(DEPDIR)/nsper.Plo projections/$(DEPDIR)/nzmg.Plo \
	projections/$(DEPDIR)/ob_tran.Plo \
	projections/$(DEPDIR)/ocea.Plo projections/$(DEPDIR)/oea.Plo \
	projections/$(DEPDIR)/omerc.Plo \
	projections/$(DEPDIR)/ortho.Plo \
	projections/$(DEPDIR)/patterson.Plo \
	projections/$(DEPDIR)/poly.Plo projections/$(DEPDIR)/putp2.Plo \
	projections/$(DEPDIR)/putp3.Plo \
	projections/$(DEPDIR)/putp4p.Plo \
	projections/$(DEPDIR)/putp5.Plo \
	projections/$(DEPDIR)/putp6.Plo projections/$(DEPDIR)/qsc.Plo \
	projections/$(DEPDIR)/robin.Plo \
	projections/$(DEPDIR)/rouss.Plo \
	projections/$(DEPDIR)/rpoly.Plo projections/$(DEPDIR)/s2.Plo \
	projections/$(DEPDIR)/sch.Plo \
	projections/$(DEPDIR)/sconics.Plo \
	projections/$(DEPDIR)/somerc.Plo \
	projections/$(DEPDIR)/stere.Plo \
	projections/$(DEPDIR)/sterea.Plo projections/$(DEPDIR)/sts.Plo \
	projections/$(DEPDIR)/tcc.Plo projections/$(DEPDIR)/tcea.Plo \
	projections/$(DEPDIR)/times.Plo \
	projections/$(DEPDIR)/tmerc.Plo \
	projections/$(DEPDIR)/tobmerc.Plo \
	projections/$(DEPDIR)/tpeqd.Plo projections/$(DEPDIR)/urm5.Plo \
	projections/$(DEPDIR)/urmfps.Plo \
	projections/$(DEPDIR)/vandg.Plo \
	projections/$(DEPDIR)/vandg2.Plo \
	projections/$(DEPDIR)/vandg4.Plo \
	projections/$(DEPDIR)/wag2.Plo projections/$(DEPDIR)/wag3.Plo \
	projections/$(DEPDIR)/wag7.Plo projections/$(DEPDIR)/wink1.Plo \
	projections/$(DEPDIR)/wink2.Plo tests/$(DEPDIR)/geodtest.Po \
	tests/$(DEPDIR)/multistresstest.Po \
	transformations/$(DEPDIR)/affine.Plo \
	transformations/$(DEPDIR)/defmodel.Plo \
	transformations/$(DEPDIR)/deformation.Plo \
	transformations/$(DEPDIR)/helmert.Plo \
	transformations/$(DEPDIR)/hgridshift.Plo \
	transformations/$(DEPDIR)/horner.Plo \
	transformations/$(DEPDIR)/molodensky.Plo \
	transformations/$(DEPDIR)/tinshift.Plo \
	transformations/$(DEPDIR)/vgridshift.Plo \
	transformations/$(DEPDIR)/xyzgridshift.Plo
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
CXXCOMPILE = $(CXX) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CXXFLAGS) $(CXXFLAGS)
LTCXXCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CXX) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CXXFLAGS) $(CXXFLAGS)
AM_V_CXX = $(am__v_CXX_@AM_V@)
am__v_CXX_ = $(am__v_CXX_@AM_DEFAULT_V@)
am__v_CXX_0 = @echo "  CXX     " $@;
am__v_CXX_1 = 
CXXLD = $(CXX)
CXXLINK = $(LIBTOOL) $(AM_V_lt) --tag=CXX $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CXXLD) $(AM_CXXFLAGS) \
	$(CXXFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CXXLD = $(am__v_CXXLD_@AM_V@)
am__v_CXXLD_ = $(am__v_CXXLD_@AM_DEFAULT_V@)
am__v_CXXLD_0 = @echo "  CXXLD   " $@;
am__v_CXXLD_1 = 
SOURCES = $(libproj_la_SOURCES) $(cct_SOURCES) $(cs2cs_SOURCES) \
	$(geod_SOURCES) $(geodtest_SOURCES) $(gie_SOURCES) \
	$(invgeod_SOURCES) $(invproj_SOURCES) \
	$(multistresstest_SOURCES) $(proj_SOURCES) $(projinfo_SOURCES) \
	$(projsync_SOURCES)
DIST_SOURCES = $(libproj_la_SOURCES) $(cct_SOURCES) $(cs2cs_SOURCES) \
	$(geod_SOURCES) $(geodtest_SOURCES) $(gie_SOURCES) \
	$(invgeod_SOURCES) $(invproj_SOURCES) \
	$(multistresstest_SOURCES) $(proj_SOURCES) $(projinfo_SOURCES) \
	$(am__projsync_SOURCES_DIST)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
HEADERS = $(include_HEADERS)
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) \
	$(LISP)proj_config.h.in
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__tty_colors_dummy = \
  mgn= red= grn= lgn= blu= brg= std=; \
  am__color_tests=no
am__tty_colors = { \
  $(am__tty_colors_dummy); \
  if test "X$(AM_COLOR_TESTS)" = Xno; then \
    am__color_tests=no; \
  elif test "X$(AM_COLOR_TESTS)" = Xalways; then \
    am__color_tests=yes; \
  elif test "X$$TERM" != Xdumb && { test -t 1; } 2>/dev/null; then \
    am__color_tests=yes; \
  fi; \
  if test $$am__color_tests = yes; then \
    red='[0;31m'; \
    grn='[0;32m'; \
    lgn='[1;32m'; \
    blu='[1;34m'; \
    mgn='[0;35m'; \
    brg='[1m'; \
    std='[m'; \
  fi; \
}
am__recheck_rx = ^[ 	]*:recheck:[ 	]*
am__global_test_result_rx = ^[ 	]*:global-test-result:[ 	]*
am__copy_in_global_log_rx = ^[ 	]*:copy-in-global-log:[ 	]*
# A command that, given a newline-separated list of test names on the
# standard input, print the name of the tests that are to be re-run
# upon "make recheck".
am__list_recheck_tests = $(AWK) '{ \
  recheck = 1; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
        { \
          if ((getline line2 < ($$0 ".log")) < 0) \
	    recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[nN][Oo]/) \
        { \
          recheck = 0; \
          break; \
        } \
      else if (line ~ /$(am__recheck_rx)[yY][eE][sS]/) \
        { \
          break; \
        } \
    }; \
  if (recheck) \
    print $$0; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# A command that, given a newline-separated list of test names on the
# standard input, create the global log from their .trs and .log files.
am__create_global_log = $(AWK) ' \
function fatal(msg) \
{ \
  print "fatal: making $@: " msg | "cat >&2"; \
  exit 1; \
} \
function rst_section(header) \
{ \
  print header; \
  len = length(header); \
  for (i = 1; i <= len; i = i + 1) \
    printf "="; \
  printf "\n\n"; \
} \
{ \
  copy_in_global_log = 1; \
  global_test_result = "RUN"; \
  while ((rc = (getline line < ($$0 ".trs"))) != 0) \
    { \
      if (rc < 0) \
         fatal("failed to read from " $$0 ".trs"); \
      if (line ~ /$(am__global_test_result_rx)/) \
        { \
          sub("$(am__global_test_result_rx)", "", line); \
          sub("[ 	]*$$", "", line); \
          global_test_result = line; \
        } \
      else if (line ~ /$(am__copy_in_global_log_rx)[nN][oO]/) \
        copy_in_global_log = 0; \
    }; \
  if (copy_in_global_log) \
    { \
      rst_section(global_test_result ": " $$0); \
      while ((rc = (getline line < ($$0 ".log"))) != 0) \
      { \
        if (rc < 0) \
          fatal("failed to read from " $$0 ".log"); \
        print line; \
      }; \
      printf "\n"; \
    }; \
  close ($$0 ".trs"); \
  close ($$0 ".log"); \
}'
# Restructured Text title.
am__rst_title = { sed 's/.*/   &   /;h;s/./=/g;p;x;s/ *$$//;p;g' && echo; }
# Solaris 10 'make', and several other traditional 'make' implementations,
# pass "-e" to $(SHELL), and POSIX 2008 even requires this.  Work around it
# by disabling -e (using the XSI extension "set +e") if it's set.
am__sh_e_setup = case $$- in *e*) set +e;; esac
# Default flags passed to test drivers.
am__common_driver_flags = \
  --color-tests "$$am__color_tests" \
  --enable-hard-errors "$$am__enable_hard_errors" \
  --expect-failure "$$am__expect_failure"
# To be inserted before the command running the test.  Creates the
# directory for the log if needed.  Stores in $dir the directory
# containing $f, in $tst the test, in $log the log.  Executes the
# developer- defined test setup AM_TESTS_ENVIRONMENT (if any), and
# passes TESTS_ENVIRONMENT.  Set up options for the wrapper that
# will run the test scripts (or their associated LOG_COMPILER, if
# thy have one).
am__check_pre = \
$(am__sh_e_setup);					\
$(am__vpath_adj_setup) $(am__vpath_adj)			\
$(am__tty_colors);					\
srcdir=$(srcdir); export srcdir;			\
case "$@" in						\
  */*) am__odir=`echo "./$@" | sed 's|/[^/]*$$||'`;;	\
    *) am__odir=.;; 					\
esac;							\
test "x$$am__odir" = x"." || test -d "$$am__odir" 	\
  || $(MKDIR_P) "$$am__odir" || exit $$?;		\
if test -f "./$$f"; then dir=./;			\
elif test -f "$$f"; then dir=;				\
else dir="$(srcdir)/"; fi;				\
tst=$$dir$$f; log='$@'; 				\
if test -n '$(DISABLE_HARD_ERRORS)'; then		\
  am__enable_hard_errors=no; 				\
else							\
  am__enable_hard_errors=yes; 				\
fi; 							\
case " $(XFAIL_TESTS) " in				\
  *[\ \	]$$f[\ \	]* | *[\ \	]$$dir$$f[\ \	]*) \
    am__expect_failure=yes;;				\
  *)							\
    am__expect_failure=no;;				\
esac; 							\
$(AM_TESTS_ENVIRONMENT) $(TESTS_ENVIRONMENT)
# A shell command to get the names of the tests scripts with any registered
# extension removed (i.e., equivalently, the names of the test logs, with
# the '.log' extension removed).  The result is saved in the shell variable
# '$bases'.  This honors runtime overriding of TESTS and TEST_LOGS.  Sadly,
# we cannot use something simpler, involving e.g., "$(TEST_LOGS:.log=)",
# since that might cause problem with VPATH rewrites for suffix-less tests.
# See also 'test-harness-vpath-rewrite.sh' and 'test-trs-basic.sh'.
am__set_TESTS_bases = \
  bases='$(TEST_LOGS)'; \
  bases=`for i in $$bases; do echo $$i; done | sed 's/\.log$$//'`; \
  bases=`echo $$bases`
RECHECK_LOGS = $(TEST_LOGS)
AM_RECURSIVE_TARGETS = check recheck
TEST_SUITE_LOG = test-suite.log
TEST_EXTENSIONS = @EXEEXT@ .test
LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
LOG_COMPILE = $(LOG_COMPILER) $(AM_LOG_FLAGS) $(LOG_FLAGS)
am__set_b = \
  case '$@' in \
    */*) \
      case '$*' in \
        */*) b='$*';; \
          *) b=`echo '$@' | sed 's/\.log$$//'`; \
       esac;; \
    *) \
      b='$*';; \
  esac
am__test_logs1 = $(TESTS:=.log)
am__test_logs2 = $(am__test_logs1:@EXEEXT@.log=.log)
TEST_LOGS = $(am__test_logs2:.test.log=.log)
TEST_LOG_DRIVER = $(SHELL) $(top_srcdir)/test-driver
TEST_LOG_COMPILE = $(TEST_LOG_COMPILER) $(AM_TEST_LOG_FLAGS) \
	$(TEST_LOG_FLAGS)
am__DIST_COMMON = $(srcdir)/Makefile.in $(srcdir)/proj_config.h.in \
	$(top_srcdir)/depcomp $(top_srcdir)/test-driver
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CURL_CFLAGS = @CURL_CFLAGS@
CURL_ENABLED_FLAGS = @CURL_ENABLED_FLAGS@
CURL_LIBS = @CURL_LIBS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CXX_WFLAGS = @CXX_WFLAGS@
CYGPATH_W = @CYGPATH_W@
C_WFLAGS = @C_WFLAGS@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
EXTRA_LIBS = @EXTRA_LIBS@
FGREP = @FGREP@
FLTO_FLAG = @FLTO_FLAG@
GREP = @GREP@
GTEST_CFLAGS = @GTEST_CFLAGS@
GTEST_LIBS = @GTEST_LIBS@
HAVE_CXX11 = @HAVE_CXX11@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LD = @LD@
LDFLAGS = @LDFLAGS@
LIBCURL_CONFIG = @LIBCURL_CONFIG@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
MUTEX_SETTING = @MUTEX_SETTING@
NM = @NM@
NMEDIT = @NMEDIT@
NO_ZERO_AS_NULL_POINTER_CONSTANT_FLAG = @NO_ZERO_AS_NULL_POINTER_CONSTANT_FLAG@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PKG_CONFIG = @PKG_CONFIG@
PROJ_LIB_ENV_VAR_TRIED_LAST_FLAGS = @PROJ_LIB_ENV_VAR_TRIED_LAST_FLAGS@
RANLIB = @RANLIB@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SQLITE3_CFLAGS = @SQLITE3_CFLAGS@
SQLITE3_CHECK = @SQLITE3_CHECK@
SQLITE3_LIBS = @SQLITE3_LIBS@
STRIP = @STRIP@
THREAD_LIB = @THREAD_LIB@
TIFF_CFLAGS = @TIFF_CFLAGS@
TIFF_ENABLED_FLAGS = @TIFF_ENABLED_FLAGS@
TIFF_LIBS = @TIFF_LIBS@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
AM_CFLAGS = @C_WFLAGS@
AM_CPPFLAGS = -DPROJ_LIB=\"$(pkgdatadir)\" \
		-DMUTEX_@MUTEX_SETTING@ -I$(top_srcdir)/include @SQLITE3_CFLAGS@ @TIFF_CFLAGS@ @TIFF_ENABLED_FLAGS@ @CURL_CFLAGS@ @CURL_ENABLED_FLAGS@ @PROJ_LIB_ENV_VAR_TRIED_LAST_FLAGS@

AM_CXXFLAGS = @CXX_WFLAGS@ @FLTO_FLAG@
include_HEADERS = proj.h proj_experimental.h proj_constants.h geodesic.h \
	 proj_symbol_rename.h

EXTRA_DIST = bin_cct.cmake bin_gie.cmake bin_cs2cs.cmake \
	bin_geod.cmake bin_proj.cmake bin_projinfo.cmake \
	lib_proj.cmake \
	check_md5sum.cmake \
	generate_wkt_parser.cmake \
	CMakeLists.txt \
	bin_geodtest.cmake \
	bin_projsync.cmake \
	tests/geodtest.cpp \
	wkt1_grammar.y wkt2_grammar.y apps/emess.h apps/utils.h \
	apps/projsync.cpp

proj_SOURCES = apps/proj.cpp apps/emess.cpp apps/utils.cpp
invproj_SOURCES = $(proj_SOURCES)
projinfo_SOURCES = apps/projinfo.cpp
cs2cs_SOURCES = apps/cs2cs.cpp apps/emess.cpp apps/utils.cpp
cct_SOURCES = apps/cct.cpp apps/proj_strtod.cpp apps/proj_strtod.h apps/optargpm.h
geod_SOURCES = apps/geod.cpp apps/geod_set.cpp apps/geod_interface.cpp apps/geod_interface.h apps/emess.cpp
invgeod_SOURCES = $(geod_SOURCES)
@HAVE_CURL_TRUE@projsync_SOURCES = apps/projsync.cpp
@HAVE_CURL_TRUE@projsync_LDADD = libproj.la
@HAVE_CURL_TRUE@PROJSYNC_BIN = projsync
gie_SOURCES = apps/gie.cpp apps/proj_strtod.cpp apps/proj_strtod.h apps/optargpm.h
multistresstest_SOURCES = tests/multistresstest.cpp
geodtest_SOURCES = tests/geodtest.cpp
cct_LDADD = libproj.la
cs2cs_LDADD = libproj.la
geod_LDADD = libproj.la
invgeod_LDADD = $(geod_LDADD)
proj_LDADD = libproj.la
invproj_LDADD = $(proj_LDADD)
projinfo_LDADD = libproj.la
gie_LDADD = libproj.la
multistresstest_LDADD = libproj.la @THREAD_LIB@
geodtest_LDADD = libproj.la
lib_LTLIBRARIES = libproj.la
libproj_la_LDFLAGS = -no-undefined -version-info 24:1:2
libproj_la_LIBADD = @SQLITE3_LIBS@ @TIFF_LIBS@ @CURL_LIBS@
libproj_la_SOURCES = \
	pj_list.h proj_internal.h \
	\
	iso19111/static.cpp \
	iso19111/util.cpp \
	iso19111/metadata.cpp \
	iso19111/common.cpp \
	iso19111/crs.cpp \
	iso19111/datum.cpp \
	iso19111/coordinatesystem.cpp \
	iso19111/operation/concatenatedoperation.cpp \
	iso19111/operation/coordinateoperation_internal.hpp \
	iso19111/operation/coordinateoperation_private.hpp \
	iso19111/operation/coordinateoperationfactory.cpp \
	iso19111/operation/conversion.cpp \
	iso19111/operation/esriparammappings.hpp \
	iso19111/operation/esriparammappings.cpp \
	iso19111/operation/operationmethod_private.hpp \
	iso19111/operation/oputils.hpp \
	iso19111/operation/oputils.cpp \
	iso19111/operation/parammappings.hpp \
	iso19111/operation/parammappings.cpp \
	iso19111/operation/projbasedoperation.cpp \
	iso19111/operation/singleoperation.cpp \
	iso19111/operation/transformation.cpp \
	iso19111/operation/vectorofvaluesparams.hpp \
	iso19111/operation/vectorofvaluesparams.cpp \
	iso19111/io.cpp \
	iso19111/internal.cpp \
	iso19111/factory.cpp \
	iso19111/c_api.cpp \
	\
	projections/aeqd.cpp \
	projections/adams.cpp \
	projections/gnom.cpp \
	projections/laea.cpp \
	projections/mod_ster.cpp \
	projections/nsper.cpp \
	projections/nzmg.cpp \
	projections/ortho.cpp \
	projections/stere.cpp \
	projections/sterea.cpp \
	projections/aea.cpp \
	projections/bipc.cpp \
	projections/bonne.cpp \
	projections/eqdc.cpp \
	projections/isea.cpp \
	projections/ccon.cpp \
	projections/imw_p.cpp \
	projections/krovak.cpp \
	projections/lcc.cpp \
	projections/poly.cpp \
	projections/rpoly.cpp \
	projections/sconics.cpp \
	projections/rouss.cpp \
	projections/cass.cpp \
	projections/cc.cpp \
	projections/cea.cpp \
	projections/eqc.cpp \
	projections/gall.cpp \
	projections/labrd.cpp \
	projections/lsat.cpp \
	projections/misrsom.cpp \
	projections/merc.cpp \
	projections/mill.cpp \
	projections/ocea.cpp \
	projections/omerc.cpp \
	projections/somerc.cpp \
	projections/tcc.cpp \
	projections/tcea.cpp \
	projections/times.cpp \
	projections/tmerc.cpp \
	projections/tobmerc.cpp \
	projections/airy.cpp \
	projections/aitoff.cpp \
	projections/august.cpp \
	projections/bacon.cpp \
	projections/bertin1953.cpp \
	projections/chamb.cpp \
	projections/hammer.cpp \
	projections/lagrng.cpp \
	projections/larr.cpp \
	projections/lask.cpp \
	projections/latlong.cpp \
	projections/nicol.cpp \
	projections/ob_tran.cpp \
	projections/oea.cpp \
	projections/tpeqd.cpp \
	projections/vandg.cpp \
	projections/vandg2.cpp \
	projections/vandg4.cpp \
	projections/wag7.cpp \
	projections/lcca.cpp \
	projections/geos.cpp \
	projections/boggs.cpp \
	projections/collg.cpp \
	projections/comill.cpp \
	projections/crast.cpp \
	projections/denoy.cpp \
	projections/eck1.cpp \
	projections/eck2.cpp \
	projections/eck3.cpp \
	projections/eck4.cpp \
	projections/eck5.cpp \
	projections/fahey.cpp \
	projections/fouc_s.cpp \
	projections/gins8.cpp \
	projections/gstmerc.cpp \
	projections/gn_sinu.cpp \
	projections/goode.cpp \
	projections/igh.cpp \
	projections/igh_o.cpp \
	projections/hatano.cpp \
	projections/loxim.cpp \
	projections/mbt_fps.cpp \
	projections/mbtfpp.cpp \
	projections/mbtfpq.cpp \
	projections/moll.cpp \
	projections/nell.cpp \
	projections/nell_h.cpp \
	projections/patterson.cpp \
	projections/putp2.cpp \
	projections/putp3.cpp \
	projections/putp4p.cpp \
	projections/putp5.cpp \
	projections/putp6.cpp \
	projections/qsc.cpp \
	projections/robin.cpp \
	projections/s2.cpp \
	projections/sch.cpp \
	projections/sts.cpp \
	projections/urm5.cpp \
	projections/urmfps.cpp \
	projections/wag2.cpp \
	projections/wag3.cpp \
	projections/wink1.cpp \
	projections/wink2.cpp \
	projections/healpix.cpp \
	projections/natearth.cpp \
	projections/natearth2.cpp \
	projections/calcofi.cpp \
	projections/eqearth.cpp \
	projections/col_urban.cpp \
	\
	conversions/axisswap.cpp \
	conversions/cart.cpp \
	conversions/geoc.cpp \
	conversions/geocent.cpp \
	conversions/noop.cpp \
	conversions/topocentric.cpp \
	conversions/set.cpp \
	conversions/unitconvert.cpp \
	\
	transformations/affine.cpp \
	transformations/deformation.cpp \
	transformations/helmert.cpp \
	transformations/hgridshift.cpp \
	transformations/horner.cpp \
	transformations/molodensky.cpp \
	transformations/vgridshift.cpp \
	transformations/xyzgridshift.cpp \
	transformations/defmodel.cpp \
	transformations/defmodel.hpp \
	transformations/defmodel_exceptions.hpp \
	transformations/defmodel_impl.hpp \
	transformations/tinshift.cpp \
	transformations/tinshift.hpp \
	transformations/tinshift_exceptions.hpp \
	transformations/tinshift_impl.hpp \
	\
	aasincos.cpp adjlon.cpp \
	dmstor.cpp auth.cpp \
	deriv.cpp ell_set.cpp ellps.cpp \
	factors.cpp fwd.cpp init.cpp inv.cpp \
	list.cpp malloc.cpp mlfn.cpp mlfn.hpp msfn.cpp proj_mdist.cpp \
	param.cpp phi2.cpp pr_list.cpp \
	qsfn.cpp strerrno.cpp \
	tsfn.cpp units.cpp ctx.cpp log.cpp zpoly1.cpp rtodms.cpp \
	release.cpp gauss.cpp \
	generic_inverse.cpp \
	quadtree.hpp \
	\
	datums.cpp datum_set.cpp \
	mutex.cpp initcache.cpp geodesic.c \
	strtod.cpp \
	\
	4D_api.cpp pipeline.cpp \
	internal.cpp \
	wkt_parser.hpp wkt_parser.cpp \
	wkt1_parser.h wkt1_parser.cpp \
	wkt1_generated_parser.h wkt1_generated_parser.c \
	wkt2_parser.h wkt2_parser.cpp \
	wkt2_generated_parser.h wkt2_generated_parser.c \
	\
	proj_json_streaming_writer.hpp \
	proj_json_streaming_writer.cpp \
	\
	tracing.cpp \
	\
	grids.hpp \
	grids.cpp \
	filemanager.hpp \
	filemanager.cpp \
	networkfilemanager.cpp \
	sqlite3_utils.hpp \
	sqlite3_utils.cpp

all: proj_config.h
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
.SUFFIXES: .c .cpp .lo .log .o .obj .test .test$(EXEEXT) .trs
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu src/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu src/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

proj_config.h: stamp-h1
	@test -f $@ || rm -f stamp-h1
	@test -f $@ || $(MAKE) $(AM_MAKEFLAGS) stamp-h1

stamp-h1: $(srcdir)/proj_config.h.in $(top_builddir)/config.status
	@rm -f stamp-h1
	cd $(top_builddir) && $(SHELL) ./config.status src/proj_config.h
$(srcdir)/proj_config.h.in:  $(am__configure_deps) 
	($(am__cd) $(top_srcdir) && $(AUTOHEADER))
	rm -f stamp-h1
	touch $@

distclean-hdr:
	-rm -f proj_config.h stamp-h1
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(bindir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(bindir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	@list='$(bin_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

clean-checkPROGRAMS:
	@list='$(check_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

install-libLTLIBRARIES: $(lib_LTLIBRARIES)
	@$(NORMAL_INSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(MKDIR_P) '$(DESTDIR)$(libdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(libdir)" || exit 1; \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL) $(INSTALL_STRIP_FLAG) $$list2 "$(DESTDIR)$(libdir)"; \
	}

uninstall-libLTLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LTLIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  $(am__strip_dir) \
	  echo " $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f '$(DESTDIR)$(libdir)/$$f'"; \
	  $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=uninstall rm -f "$(DESTDIR)$(libdir)/$$f"; \
	done

clean-libLTLIBRARIES:
	-test -z "$(lib_LTLIBRARIES)" || rm -f $(lib_LTLIBRARIES)
	@list='$(lib_LTLIBRARIES)'; \
	locs=`for p in $$list; do echo $$p; done | \
	      sed 's|^[^/]*$$|.|; s|/[^/]*$$||; s|$$|/so_locations|' | \
	      sort -u`; \
	test -z "$$locs" || { \
	  echo rm -f $${locs}; \
	  rm -f $${locs}; \
	}
iso19111/$(am__dirstamp):
	@$(MKDIR_P) iso19111
	@: > iso19111/$(am__dirstamp)
iso19111/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) iso19111/$(DEPDIR)
	@: > iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/static.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/util.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/metadata.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/common.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/crs.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/datum.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/coordinatesystem.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/$(am__dirstamp):
	@$(MKDIR_P) iso19111/operation
	@: > iso19111/operation/$(am__dirstamp)
iso19111/operation/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) iso19111/operation/$(DEPDIR)
	@: > iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/concatenatedoperation.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/coordinateoperationfactory.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/conversion.lo: iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/esriparammappings.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/oputils.lo: iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/parammappings.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/projbasedoperation.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/singleoperation.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/transformation.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/operation/vectorofvaluesparams.lo:  \
	iso19111/operation/$(am__dirstamp) \
	iso19111/operation/$(DEPDIR)/$(am__dirstamp)
iso19111/io.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/internal.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/factory.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
iso19111/c_api.lo: iso19111/$(am__dirstamp) \
	iso19111/$(DEPDIR)/$(am__dirstamp)
projections/$(am__dirstamp):
	@$(MKDIR_P) projections
	@: > projections/$(am__dirstamp)
projections/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) projections/$(DEPDIR)
	@: > projections/$(DEPDIR)/$(am__dirstamp)
projections/aeqd.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/adams.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/gnom.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/laea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/mod_ster.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/nsper.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/nzmg.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/ortho.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/stere.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/sterea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/aea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/bipc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/bonne.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eqdc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/isea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/ccon.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/imw_p.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/krovak.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/lcc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/poly.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/rpoly.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/sconics.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/rouss.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/cass.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/cc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/cea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eqc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/gall.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/labrd.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/lsat.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/misrsom.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/merc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/mill.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/ocea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/omerc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/somerc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/tcc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/tcea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/times.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/tmerc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/tobmerc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/airy.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/aitoff.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/august.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/bacon.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/bertin1953.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/chamb.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/hammer.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/lagrng.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/larr.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/lask.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/latlong.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/nicol.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/ob_tran.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/oea.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/tpeqd.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/vandg.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/vandg2.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/vandg4.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/wag7.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/lcca.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/geos.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/boggs.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/collg.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/comill.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/crast.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/denoy.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eck1.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eck2.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eck3.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eck4.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eck5.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/fahey.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/fouc_s.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/gins8.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/gstmerc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/gn_sinu.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/goode.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/igh.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/igh_o.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/hatano.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/loxim.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/mbt_fps.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/mbtfpp.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/mbtfpq.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/moll.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/nell.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/nell_h.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/patterson.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/putp2.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/putp3.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/putp4p.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/putp5.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/putp6.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/qsc.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/robin.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/s2.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/sch.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/sts.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/urm5.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/urmfps.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/wag2.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/wag3.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/wink1.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/wink2.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/healpix.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/natearth.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/natearth2.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/calcofi.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/eqearth.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
projections/col_urban.lo: projections/$(am__dirstamp) \
	projections/$(DEPDIR)/$(am__dirstamp)
conversions/$(am__dirstamp):
	@$(MKDIR_P) conversions
	@: > conversions/$(am__dirstamp)
conversions/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) conversions/$(DEPDIR)
	@: > conversions/$(DEPDIR)/$(am__dirstamp)
conversions/axisswap.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
conversions/cart.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
conversions/geoc.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
conversions/geocent.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
conversions/noop.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
conversions/topocentric.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
conversions/set.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
conversions/unitconvert.lo: conversions/$(am__dirstamp) \
	conversions/$(DEPDIR)/$(am__dirstamp)
transformations/$(am__dirstamp):
	@$(MKDIR_P) transformations
	@: > transformations/$(am__dirstamp)
transformations/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) transformations/$(DEPDIR)
	@: > transformations/$(DEPDIR)/$(am__dirstamp)
transformations/affine.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/deformation.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/helmert.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/hgridshift.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/horner.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/molodensky.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/vgridshift.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/xyzgridshift.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/defmodel.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)
transformations/tinshift.lo: transformations/$(am__dirstamp) \
	transformations/$(DEPDIR)/$(am__dirstamp)

libproj.la: $(libproj_la_OBJECTS) $(libproj_la_DEPENDENCIES) $(EXTRA_libproj_la_DEPENDENCIES) 
	$(AM_V_CXXLD)$(libproj_la_LINK) -rpath $(libdir) $(libproj_la_OBJECTS) $(libproj_la_LIBADD) $(LIBS)
apps/$(am__dirstamp):
	@$(MKDIR_P) apps
	@: > apps/$(am__dirstamp)
apps/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) apps/$(DEPDIR)
	@: > apps/$(DEPDIR)/$(am__dirstamp)
apps/cct.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)
apps/proj_strtod.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)

cct$(EXEEXT): $(cct_OBJECTS) $(cct_DEPENDENCIES) $(EXTRA_cct_DEPENDENCIES) 
	@rm -f cct$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(cct_OBJECTS) $(cct_LDADD) $(LIBS)
apps/cs2cs.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)
apps/emess.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)
apps/utils.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)

cs2cs$(EXEEXT): $(cs2cs_OBJECTS) $(cs2cs_DEPENDENCIES) $(EXTRA_cs2cs_DEPENDENCIES) 
	@rm -f cs2cs$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(cs2cs_OBJECTS) $(cs2cs_LDADD) $(LIBS)
apps/geod.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)
apps/geod_set.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)
apps/geod_interface.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)

geod$(EXEEXT): $(geod_OBJECTS) $(geod_DEPENDENCIES) $(EXTRA_geod_DEPENDENCIES) 
	@rm -f geod$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(geod_OBJECTS) $(geod_LDADD) $(LIBS)
tests/$(am__dirstamp):
	@$(MKDIR_P) tests
	@: > tests/$(am__dirstamp)
tests/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) tests/$(DEPDIR)
	@: > tests/$(DEPDIR)/$(am__dirstamp)
tests/geodtest.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

geodtest$(EXEEXT): $(geodtest_OBJECTS) $(geodtest_DEPENDENCIES) $(EXTRA_geodtest_DEPENDENCIES) 
	@rm -f geodtest$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(geodtest_OBJECTS) $(geodtest_LDADD) $(LIBS)
apps/gie.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)

gie$(EXEEXT): $(gie_OBJECTS) $(gie_DEPENDENCIES) $(EXTRA_gie_DEPENDENCIES) 
	@rm -f gie$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(gie_OBJECTS) $(gie_LDADD) $(LIBS)

invgeod$(EXEEXT): $(invgeod_OBJECTS) $(invgeod_DEPENDENCIES) $(EXTRA_invgeod_DEPENDENCIES) 
	@rm -f invgeod$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(invgeod_OBJECTS) $(invgeod_LDADD) $(LIBS)
apps/proj.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)

invproj$(EXEEXT): $(invproj_OBJECTS) $(invproj_DEPENDENCIES) $(EXTRA_invproj_DEPENDENCIES) 
	@rm -f invproj$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(invproj_OBJECTS) $(invproj_LDADD) $(LIBS)
tests/multistresstest.$(OBJEXT): tests/$(am__dirstamp) \
	tests/$(DEPDIR)/$(am__dirstamp)

multistresstest$(EXEEXT): $(multistresstest_OBJECTS) $(multistresstest_DEPENDENCIES) $(EXTRA_multistresstest_DEPENDENCIES) 
	@rm -f multistresstest$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(multistresstest_OBJECTS) $(multistresstest_LDADD) $(LIBS)

proj$(EXEEXT): $(proj_OBJECTS) $(proj_DEPENDENCIES) $(EXTRA_proj_DEPENDENCIES) 
	@rm -f proj$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(proj_OBJECTS) $(proj_LDADD) $(LIBS)
apps/projinfo.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)

projinfo$(EXEEXT): $(projinfo_OBJECTS) $(projinfo_DEPENDENCIES) $(EXTRA_projinfo_DEPENDENCIES) 
	@rm -f projinfo$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(projinfo_OBJECTS) $(projinfo_LDADD) $(LIBS)
apps/projsync.$(OBJEXT): apps/$(am__dirstamp) \
	apps/$(DEPDIR)/$(am__dirstamp)

projsync$(EXEEXT): $(projsync_OBJECTS) $(projsync_DEPENDENCIES) $(EXTRA_projsync_DEPENDENCIES) 
	@rm -f projsync$(EXEEXT)
	$(AM_V_CXXLD)$(CXXLINK) $(projsync_OBJECTS) $(projsync_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f apps/*.$(OBJEXT)
	-rm -f conversions/*.$(OBJEXT)
	-rm -f conversions/*.lo
	-rm -f iso19111/*.$(OBJEXT)
	-rm -f iso19111/*.lo
	-rm -f iso19111/operation/*.$(OBJEXT)
	-rm -f iso19111/operation/*.lo
	-rm -f projections/*.$(OBJEXT)
	-rm -f projections/*.lo
	-rm -f tests/*.$(OBJEXT)
	-rm -f transformations/*.$(OBJEXT)
	-rm -f transformations/*.lo

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/4D_api.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/aasincos.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/adjlon.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/auth.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ctx.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/datum_set.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/datums.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/deriv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dmstor.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ell_set.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ellps.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/factors.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/filemanager.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fwd.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/gauss.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/generic_inverse.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/geodesic.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/grids.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/init.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/initcache.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/internal.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/inv.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/list.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/log.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/malloc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mlfn.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/msfn.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mutex.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/networkfilemanager.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/param.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/phi2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pipeline.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pr_list.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/proj_json_streaming_writer.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/proj_mdist.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/qsfn.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/release.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/rtodms.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/sqlite3_utils.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/strerrno.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/strtod.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/tracing.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/tsfn.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/units.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/wkt1_generated_parser.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/wkt1_parser.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/wkt2_generated_parser.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/wkt2_parser.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/wkt_parser.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/zpoly1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/cct.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/cs2cs.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/emess.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/geod.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/geod_interface.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/geod_set.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/gie.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/proj.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/proj_strtod.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/projinfo.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/projsync.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@apps/$(DEPDIR)/utils.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/axisswap.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/cart.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/geoc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/geocent.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/noop.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/set.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/topocentric.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@conversions/$(DEPDIR)/unitconvert.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/c_api.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/common.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/coordinatesystem.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/crs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/datum.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/factory.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/internal.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/io.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/metadata.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/static.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/$(DEPDIR)/util.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/concatenatedoperation.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/conversion.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/coordinateoperationfactory.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/esriparammappings.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/oputils.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/parammappings.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/projbasedoperation.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/singleoperation.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/transformation.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@iso19111/operation/$(DEPDIR)/vectorofvaluesparams.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/adams.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/aea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/aeqd.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/airy.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/aitoff.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/august.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/bacon.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/bertin1953.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/bipc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/boggs.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/bonne.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/calcofi.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/cass.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/cc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/ccon.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/cea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/chamb.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/col_urban.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/collg.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/comill.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/crast.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/denoy.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eck1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eck2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eck3.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eck4.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eck5.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eqc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eqdc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/eqearth.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/fahey.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/fouc_s.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/gall.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/geos.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/gins8.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/gn_sinu.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/gnom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/goode.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/gstmerc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/hammer.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/hatano.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/healpix.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/igh.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/igh_o.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/imw_p.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/isea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/krovak.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/labrd.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/laea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/lagrng.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/larr.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/lask.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/latlong.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/lcc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/lcca.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/loxim.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/lsat.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/mbt_fps.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/mbtfpp.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/mbtfpq.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/merc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/mill.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/misrsom.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/mod_ster.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/moll.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/natearth.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/natearth2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/nell.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/nell_h.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/nicol.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/nsper.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/nzmg.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/ob_tran.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/ocea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/oea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/omerc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/ortho.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/patterson.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/poly.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/putp2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/putp3.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/putp4p.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/putp5.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/putp6.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/qsc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/robin.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/rouss.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/rpoly.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/s2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/sch.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/sconics.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/somerc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/stere.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/sterea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/sts.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/tcc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/tcea.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/times.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/tmerc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/tobmerc.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/tpeqd.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/urm5.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/urmfps.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/vandg.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/vandg2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/vandg4.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/wag2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/wag3.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/wag7.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/wink1.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@projections/$(DEPDIR)/wink2.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/geodtest.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@tests/$(DEPDIR)/multistresstest.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/affine.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/defmodel.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/deformation.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/helmert.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/hgridshift.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/horner.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/molodensky.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/tinshift.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/vgridshift.Plo@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@transformations/$(DEPDIR)/xyzgridshift.Plo@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

.cpp.o:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ $<

.cpp.obj:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCXX_TRUE@	$(CXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(CXXCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.cpp.lo:
@am__fastdepCXX_TRUE@	$(AM_V_CXX)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCXX_TRUE@	$(LTCXXCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCXX_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	$(AM_V_CXX)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCXX_FALSE@	DEPDIR=$(DEPDIR) $(CXXDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCXX_FALSE@	$(AM_V_CXX@am__nodep@)$(LTCXXCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
	-rm -rf conversions/.libs conversions/_libs
	-rm -rf iso19111/.libs iso19111/_libs
	-rm -rf iso19111/operation/.libs iso19111/operation/_libs
	-rm -rf projections/.libs projections/_libs
	-rm -rf transformations/.libs transformations/_libs
install-includeHEADERS: $(include_HEADERS)
	@$(NORMAL_INSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(includedir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(includedir)" || exit 1; \
	fi; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(includedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(includedir)" || exit $$?; \
	done

uninstall-includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(include_HEADERS)'; test -n "$(includedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	dir='$(DESTDIR)$(includedir)'; $(am__uninstall_files_from_dir)

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

# Recover from deleted '.trs' file; this should ensure that
# "rm -f foo.log; make foo.trs" re-run 'foo.test', and re-create
# both 'foo.log' and 'foo.trs'.  Break the recipe in two subshells
# to avoid problems with "make -n".
.log.trs:
	rm -f $< $@
	$(MAKE) $(AM_MAKEFLAGS) $<

# Leading 'am--fnord' is there to ensure the list of targets does not
# expand to empty, as could happen e.g. with make check TESTS=''.
am--fnord $(TEST_LOGS) $(TEST_LOGS:.log=.trs): $(am__force_recheck)
am--force-recheck:
	@:

$(TEST_SUITE_LOG): $(TEST_LOGS)
	@$(am__set_TESTS_bases); \
	am__f_ok () { test -f "$$1" && test -r "$$1"; }; \
	redo_bases=`for i in $$bases; do \
	              am__f_ok $$i.trs && am__f_ok $$i.log || echo $$i; \
	            done`; \
	if test -n "$$redo_bases"; then \
	  redo_logs=`for i in $$redo_bases; do echo $$i.log; done`; \
	  redo_results=`for i in $$redo_bases; do echo $$i.trs; done`; \
	  if $(am__make_dryrun); then :; else \
	    rm -f $$redo_logs && rm -f $$redo_results || exit 1; \
	  fi; \
	fi; \
	if test -n "$$am__remaking_logs"; then \
	  echo "fatal: making $(TEST_SUITE_LOG): possible infinite" \
	       "recursion detected" >&2; \
	elif test -n "$$redo_logs"; then \
	  am__remaking_logs=yes $(MAKE) $(AM_MAKEFLAGS) $$redo_logs; \
	fi; \
	if $(am__make_dryrun); then :; else \
	  st=0;  \
	  errmsg="fatal: making $(TEST_SUITE_LOG): failed to create"; \
	  for i in $$redo_bases; do \
	    test -f $$i.trs && test -r $$i.trs \
	      || { echo "$$errmsg $$i.trs" >&2; st=1; }; \
	    test -f $$i.log && test -r $$i.log \
	      || { echo "$$errmsg $$i.log" >&2; st=1; }; \
	  done; \
	  test $$st -eq 0 || exit 1; \
	fi
	@$(am__sh_e_setup); $(am__tty_colors); $(am__set_TESTS_bases); \
	ws='[ 	]'; \
	results=`for b in $$bases; do echo $$b.trs; done`; \
	test -n "$$results" || results=/dev/null; \
	all=`  grep "^$$ws*:test-result:"           $$results | wc -l`; \
	pass=` grep "^$$ws*:test-result:$$ws*PASS"  $$results | wc -l`; \
	fail=` grep "^$$ws*:test-result:$$ws*FAIL"  $$results | wc -l`; \
	skip=` grep "^$$ws*:test-result:$$ws*SKIP"  $$results | wc -l`; \
	xfail=`grep "^$$ws*:test-result:$$ws*XFAIL" $$results | wc -l`; \
	xpass=`grep "^$$ws*:test-result:$$ws*XPASS" $$results | wc -l`; \
	error=`grep "^$$ws*:test-result:$$ws*ERROR" $$results | wc -l`; \
	if test `expr $$fail + $$xpass + $$error` -eq 0; then \
	  success=true; \
	else \
	  success=false; \
	fi; \
	br='==================='; br=$$br$$br$$br$$br; \
	result_count () \
	{ \
	    if test x"$$1" = x"--maybe-color"; then \
	      maybe_colorize=yes; \
	    elif test x"$$1" = x"--no-color"; then \
	      maybe_colorize=no; \
	    else \
	      echo "$@: invalid 'result_count' usage" >&2; exit 4; \
	    fi; \
	    shift; \
	    desc=$$1 count=$$2; \
	    if test $$maybe_colorize = yes && test $$count -gt 0; then \
	      color_start=$$3 color_end=$$std; \
	    else \
	      color_start= color_end=; \
	    fi; \
	    echo "$${color_start}# $$desc $$count$${color_end}"; \
	}; \
	create_testsuite_report () \
	{ \
	  result_count $$1 "TOTAL:" $$all   "$$brg"; \
	  result_count $$1 "PASS: " $$pass  "$$grn"; \
	  result_count $$1 "SKIP: " $$skip  "$$blu"; \
	  result_count $$1 "XFAIL:" $$xfail "$$lgn"; \
	  result_count $$1 "FAIL: " $$fail  "$$red"; \
	  result_count $$1 "XPASS:" $$xpass "$$red"; \
	  result_count $$1 "ERROR:" $$error "$$mgn"; \
	}; \
	{								\
	  echo "$(PACKAGE_STRING): $(subdir)/$(TEST_SUITE_LOG)" |	\
	    $(am__rst_title);						\
	  create_testsuite_report --no-color;				\
	  echo;								\
	  echo ".. contents:: :depth: 2";				\
	  echo;								\
	  for b in $$bases; do echo $$b; done				\
	    | $(am__create_global_log);					\
	} >$(TEST_SUITE_LOG).tmp || exit 1;				\
	mv $(TEST_SUITE_LOG).tmp $(TEST_SUITE_LOG);			\
	if $$success; then						\
	  col="$$grn";							\
	 else								\
	  col="$$red";							\
	  test x"$$VERBOSE" = x || cat $(TEST_SUITE_LOG);		\
	fi;								\
	echo "$${col}$$br$${std}"; 					\
	echo "$${col}Testsuite summary for $(PACKAGE_STRING)$${std}";	\
	echo "$${col}$$br$${std}"; 					\
	create_testsuite_report --maybe-color;				\
	echo "$$col$$br$$std";						\
	if $$success; then :; else					\
	  echo "$${col}See $(subdir)/$(TEST_SUITE_LOG)$${std}";		\
	  if test -n "$(PACKAGE_BUGREPORT)"; then			\
	    echo "$${col}Please report to $(PACKAGE_BUGREPORT)$${std}";	\
	  fi;								\
	  echo "$$col$$br$$std";					\
	fi;								\
	$$success || exit 1

check-TESTS: $(check_PROGRAMS)
	@list='$(RECHECK_LOGS)';           test -z "$$list" || rm -f $$list
	@list='$(RECHECK_LOGS:.log=.trs)'; test -z "$$list" || rm -f $$list
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	trs_list=`for i in $$bases; do echo $$i.trs; done`; \
	log_list=`echo $$log_list`; trs_list=`echo $$trs_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) TEST_LOGS="$$log_list"; \
	exit $$?;
recheck: all $(check_PROGRAMS)
	@test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)
	@set +e; $(am__set_TESTS_bases); \
	bases=`for i in $$bases; do echo $$i; done \
	         | $(am__list_recheck_tests)` || exit 1; \
	log_list=`for i in $$bases; do echo $$i.log; done`; \
	log_list=`echo $$log_list`; \
	$(MAKE) $(AM_MAKEFLAGS) $(TEST_SUITE_LOG) \
	        am__force_recheck=am--force-recheck \
	        TEST_LOGS="$$log_list"; \
	exit $$?
geodtest.log: geodtest$(EXEEXT)
	@p='geodtest$(EXEEXT)'; \
	b='geodtest'; \
	$(am__check_pre) $(LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_LOG_DRIVER_FLAGS) $(LOG_DRIVER_FLAGS) -- $(LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
.test.log:
	@p='$<'; \
	$(am__set_b); \
	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
	--log-file $$b.log --trs-file $$b.trs \
	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
	"$$tst" $(AM_TESTS_FD_REDIRECT)
@am__EXEEXT_TRUE@.test$(EXEEXT).log:
@am__EXEEXT_TRUE@	@p='$<'; \
@am__EXEEXT_TRUE@	$(am__set_b); \
@am__EXEEXT_TRUE@	$(am__check_pre) $(TEST_LOG_DRIVER) --test-name "$$f" \
@am__EXEEXT_TRUE@	--log-file $$b.log --trs-file $$b.trs \
@am__EXEEXT_TRUE@	$(am__common_driver_flags) $(AM_TEST_LOG_DRIVER_FLAGS) $(TEST_LOG_DRIVER_FLAGS) -- $(TEST_LOG_COMPILE) \
@am__EXEEXT_TRUE@	"$$tst" $(AM_TESTS_FD_REDIRECT)

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) $(check_PROGRAMS)
	$(MAKE) $(AM_MAKEFLAGS) check-TESTS
check: check-am
all-am: Makefile $(PROGRAMS) $(LTLIBRARIES) $(HEADERS) proj_config.h
install-binPROGRAMS: install-libLTLIBRARIES

installdirs:
	for dir in "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libdir)" "$(DESTDIR)$(includedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:
	-test -z "$(TEST_LOGS)" || rm -f $(TEST_LOGS)
	-test -z "$(TEST_LOGS:.log=.trs)" || rm -f $(TEST_LOGS:.log=.trs)
	-test -z "$(TEST_SUITE_LOG)" || rm -f $(TEST_SUITE_LOG)

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f apps/$(DEPDIR)/$(am__dirstamp)
	-rm -f apps/$(am__dirstamp)
	-rm -f conversions/$(DEPDIR)/$(am__dirstamp)
	-rm -f conversions/$(am__dirstamp)
	-rm -f iso19111/$(DEPDIR)/$(am__dirstamp)
	-rm -f iso19111/$(am__dirstamp)
	-rm -f iso19111/operation/$(DEPDIR)/$(am__dirstamp)
	-rm -f iso19111/operation/$(am__dirstamp)
	-rm -f projections/$(DEPDIR)/$(am__dirstamp)
	-rm -f projections/$(am__dirstamp)
	-rm -f tests/$(DEPDIR)/$(am__dirstamp)
	-rm -f tests/$(am__dirstamp)
	-rm -f transformations/$(DEPDIR)/$(am__dirstamp)
	-rm -f transformations/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-binPROGRAMS clean-checkPROGRAMS clean-generic \
	clean-libLTLIBRARIES clean-libtool clean-noinstPROGRAMS \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/4D_api.Plo
	-rm -f ./$(DEPDIR)/aasincos.Plo
	-rm -f ./$(DEPDIR)/adjlon.Plo
	-rm -f ./$(DEPDIR)/auth.Plo
	-rm -f ./$(DEPDIR)/ctx.Plo
	-rm -f ./$(DEPDIR)/datum_set.Plo
	-rm -f ./$(DEPDIR)/datums.Plo
	-rm -f ./$(DEPDIR)/deriv.Plo
	-rm -f ./$(DEPDIR)/dmstor.Plo
	-rm -f ./$(DEPDIR)/ell_set.Plo
	-rm -f ./$(DEPDIR)/ellps.Plo
	-rm -f ./$(DEPDIR)/factors.Plo
	-rm -f ./$(DEPDIR)/filemanager.Plo
	-rm -f ./$(DEPDIR)/fwd.Plo
	-rm -f ./$(DEPDIR)/gauss.Plo
	-rm -f ./$(DEPDIR)/generic_inverse.Plo
	-rm -f ./$(DEPDIR)/geodesic.Plo
	-rm -f ./$(DEPDIR)/grids.Plo
	-rm -f ./$(DEPDIR)/init.Plo
	-rm -f ./$(DEPDIR)/initcache.Plo
	-rm -f ./$(DEPDIR)/internal.Plo
	-rm -f ./$(DEPDIR)/inv.Plo
	-rm -f ./$(DEPDIR)/list.Plo
	-rm -f ./$(DEPDIR)/log.Plo
	-rm -f ./$(DEPDIR)/malloc.Plo
	-rm -f ./$(DEPDIR)/mlfn.Plo
	-rm -f ./$(DEPDIR)/msfn.Plo
	-rm -f ./$(DEPDIR)/mutex.Plo
	-rm -f ./$(DEPDIR)/networkfilemanager.Plo
	-rm -f ./$(DEPDIR)/param.Plo
	-rm -f ./$(DEPDIR)/phi2.Plo
	-rm -f ./$(DEPDIR)/pipeline.Plo
	-rm -f ./$(DEPDIR)/pr_list.Plo
	-rm -f ./$(DEPDIR)/proj_json_streaming_writer.Plo
	-rm -f ./$(DEPDIR)/proj_mdist.Plo
	-rm -f ./$(DEPDIR)/qsfn.Plo
	-rm -f ./$(DEPDIR)/release.Plo
	-rm -f ./$(DEPDIR)/rtodms.Plo
	-rm -f ./$(DEPDIR)/sqlite3_utils.Plo
	-rm -f ./$(DEPDIR)/strerrno.Plo
	-rm -f ./$(DEPDIR)/strtod.Plo
	-rm -f ./$(DEPDIR)/tracing.Plo
	-rm -f ./$(DEPDIR)/tsfn.Plo
	-rm -f ./$(DEPDIR)/units.Plo
	-rm -f ./$(DEPDIR)/wkt1_generated_parser.Plo
	-rm -f ./$(DEPDIR)/wkt1_parser.Plo
	-rm -f ./$(DEPDIR)/wkt2_generated_parser.Plo
	-rm -f ./$(DEPDIR)/wkt2_parser.Plo
	-rm -f ./$(DEPDIR)/wkt_parser.Plo
	-rm -f ./$(DEPDIR)/zpoly1.Plo
	-rm -f apps/$(DEPDIR)/cct.Po
	-rm -f apps/$(DEPDIR)/cs2cs.Po
	-rm -f apps/$(DEPDIR)/emess.Po
	-rm -f apps/$(DEPDIR)/geod.Po
	-rm -f apps/$(DEPDIR)/geod_interface.Po
	-rm -f apps/$(DEPDIR)/geod_set.Po
	-rm -f apps/$(DEPDIR)/gie.Po
	-rm -f apps/$(DEPDIR)/proj.Po
	-rm -f apps/$(DEPDIR)/proj_strtod.Po
	-rm -f apps/$(DEPDIR)/projinfo.Po
	-rm -f apps/$(DEPDIR)/projsync.Po
	-rm -f apps/$(DEPDIR)/utils.Po
	-rm -f conversions/$(DEPDIR)/axisswap.Plo
	-rm -f conversions/$(DEPDIR)/cart.Plo
	-rm -f conversions/$(DEPDIR)/geoc.Plo
	-rm -f conversions/$(DEPDIR)/geocent.Plo
	-rm -f conversions/$(DEPDIR)/noop.Plo
	-rm -f conversions/$(DEPDIR)/set.Plo
	-rm -f conversions/$(DEPDIR)/topocentric.Plo
	-rm -f conversions/$(DEPDIR)/unitconvert.Plo
	-rm -f iso19111/$(DEPDIR)/c_api.Plo
	-rm -f iso19111/$(DEPDIR)/common.Plo
	-rm -f iso19111/$(DEPDIR)/coordinatesystem.Plo
	-rm -f iso19111/$(DEPDIR)/crs.Plo
	-rm -f iso19111/$(DEPDIR)/datum.Plo
	-rm -f iso19111/$(DEPDIR)/factory.Plo
	-rm -f iso19111/$(DEPDIR)/internal.Plo
	-rm -f iso19111/$(DEPDIR)/io.Plo
	-rm -f iso19111/$(DEPDIR)/metadata.Plo
	-rm -f iso19111/$(DEPDIR)/static.Plo
	-rm -f iso19111/$(DEPDIR)/util.Plo
	-rm -f iso19111/operation/$(DEPDIR)/concatenatedoperation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/conversion.Plo
	-rm -f iso19111/operation/$(DEPDIR)/coordinateoperationfactory.Plo
	-rm -f iso19111/operation/$(DEPDIR)/esriparammappings.Plo
	-rm -f iso19111/operation/$(DEPDIR)/oputils.Plo
	-rm -f iso19111/operation/$(DEPDIR)/parammappings.Plo
	-rm -f iso19111/operation/$(DEPDIR)/projbasedoperation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/singleoperation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/transformation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/vectorofvaluesparams.Plo
	-rm -f projections/$(DEPDIR)/adams.Plo
	-rm -f projections/$(DEPDIR)/aea.Plo
	-rm -f projections/$(DEPDIR)/aeqd.Plo
	-rm -f projections/$(DEPDIR)/airy.Plo
	-rm -f projections/$(DEPDIR)/aitoff.Plo
	-rm -f projections/$(DEPDIR)/august.Plo
	-rm -f projections/$(DEPDIR)/bacon.Plo
	-rm -f projections/$(DEPDIR)/bertin1953.Plo
	-rm -f projections/$(DEPDIR)/bipc.Plo
	-rm -f projections/$(DEPDIR)/boggs.Plo
	-rm -f projections/$(DEPDIR)/bonne.Plo
	-rm -f projections/$(DEPDIR)/calcofi.Plo
	-rm -f projections/$(DEPDIR)/cass.Plo
	-rm -f projections/$(DEPDIR)/cc.Plo
	-rm -f projections/$(DEPDIR)/ccon.Plo
	-rm -f projections/$(DEPDIR)/cea.Plo
	-rm -f projections/$(DEPDIR)/chamb.Plo
	-rm -f projections/$(DEPDIR)/col_urban.Plo
	-rm -f projections/$(DEPDIR)/collg.Plo
	-rm -f projections/$(DEPDIR)/comill.Plo
	-rm -f projections/$(DEPDIR)/crast.Plo
	-rm -f projections/$(DEPDIR)/denoy.Plo
	-rm -f projections/$(DEPDIR)/eck1.Plo
	-rm -f projections/$(DEPDIR)/eck2.Plo
	-rm -f projections/$(DEPDIR)/eck3.Plo
	-rm -f projections/$(DEPDIR)/eck4.Plo
	-rm -f projections/$(DEPDIR)/eck5.Plo
	-rm -f projections/$(DEPDIR)/eqc.Plo
	-rm -f projections/$(DEPDIR)/eqdc.Plo
	-rm -f projections/$(DEPDIR)/eqearth.Plo
	-rm -f projections/$(DEPDIR)/fahey.Plo
	-rm -f projections/$(DEPDIR)/fouc_s.Plo
	-rm -f projections/$(DEPDIR)/gall.Plo
	-rm -f projections/$(DEPDIR)/geos.Plo
	-rm -f projections/$(DEPDIR)/gins8.Plo
	-rm -f projections/$(DEPDIR)/gn_sinu.Plo
	-rm -f projections/$(DEPDIR)/gnom.Plo
	-rm -f projections/$(DEPDIR)/goode.Plo
	-rm -f projections/$(DEPDIR)/gstmerc.Plo
	-rm -f projections/$(DEPDIR)/hammer.Plo
	-rm -f projections/$(DEPDIR)/hatano.Plo
	-rm -f projections/$(DEPDIR)/healpix.Plo
	-rm -f projections/$(DEPDIR)/igh.Plo
	-rm -f projections/$(DEPDIR)/igh_o.Plo
	-rm -f projections/$(DEPDIR)/imw_p.Plo
	-rm -f projections/$(DEPDIR)/isea.Plo
	-rm -f projections/$(DEPDIR)/krovak.Plo
	-rm -f projections/$(DEPDIR)/labrd.Plo
	-rm -f projections/$(DEPDIR)/laea.Plo
	-rm -f projections/$(DEPDIR)/lagrng.Plo
	-rm -f projections/$(DEPDIR)/larr.Plo
	-rm -f projections/$(DEPDIR)/lask.Plo
	-rm -f projections/$(DEPDIR)/latlong.Plo
	-rm -f projections/$(DEPDIR)/lcc.Plo
	-rm -f projections/$(DEPDIR)/lcca.Plo
	-rm -f projections/$(DEPDIR)/loxim.Plo
	-rm -f projections/$(DEPDIR)/lsat.Plo
	-rm -f projections/$(DEPDIR)/mbt_fps.Plo
	-rm -f projections/$(DEPDIR)/mbtfpp.Plo
	-rm -f projections/$(DEPDIR)/mbtfpq.Plo
	-rm -f projections/$(DEPDIR)/merc.Plo
	-rm -f projections/$(DEPDIR)/mill.Plo
	-rm -f projections/$(DEPDIR)/misrsom.Plo
	-rm -f projections/$(DEPDIR)/mod_ster.Plo
	-rm -f projections/$(DEPDIR)/moll.Plo
	-rm -f projections/$(DEPDIR)/natearth.Plo
	-rm -f projections/$(DEPDIR)/natearth2.Plo
	-rm -f projections/$(DEPDIR)/nell.Plo
	-rm -f projections/$(DEPDIR)/nell_h.Plo
	-rm -f projections/$(DEPDIR)/nicol.Plo
	-rm -f projections/$(DEPDIR)/nsper.Plo
	-rm -f projections/$(DEPDIR)/nzmg.Plo
	-rm -f projections/$(DEPDIR)/ob_tran.Plo
	-rm -f projections/$(DEPDIR)/ocea.Plo
	-rm -f projections/$(DEPDIR)/oea.Plo
	-rm -f projections/$(DEPDIR)/omerc.Plo
	-rm -f projections/$(DEPDIR)/ortho.Plo
	-rm -f projections/$(DEPDIR)/patterson.Plo
	-rm -f projections/$(DEPDIR)/poly.Plo
	-rm -f projections/$(DEPDIR)/putp2.Plo
	-rm -f projections/$(DEPDIR)/putp3.Plo
	-rm -f projections/$(DEPDIR)/putp4p.Plo
	-rm -f projections/$(DEPDIR)/putp5.Plo
	-rm -f projections/$(DEPDIR)/putp6.Plo
	-rm -f projections/$(DEPDIR)/qsc.Plo
	-rm -f projections/$(DEPDIR)/robin.Plo
	-rm -f projections/$(DEPDIR)/rouss.Plo
	-rm -f projections/$(DEPDIR)/rpoly.Plo
	-rm -f projections/$(DEPDIR)/s2.Plo
	-rm -f projections/$(DEPDIR)/sch.Plo
	-rm -f projections/$(DEPDIR)/sconics.Plo
	-rm -f projections/$(DEPDIR)/somerc.Plo
	-rm -f projections/$(DEPDIR)/stere.Plo
	-rm -f projections/$(DEPDIR)/sterea.Plo
	-rm -f projections/$(DEPDIR)/sts.Plo
	-rm -f projections/$(DEPDIR)/tcc.Plo
	-rm -f projections/$(DEPDIR)/tcea.Plo
	-rm -f projections/$(DEPDIR)/times.Plo
	-rm -f projections/$(DEPDIR)/tmerc.Plo
	-rm -f projections/$(DEPDIR)/tobmerc.Plo
	-rm -f projections/$(DEPDIR)/tpeqd.Plo
	-rm -f projections/$(DEPDIR)/urm5.Plo
	-rm -f projections/$(DEPDIR)/urmfps.Plo
	-rm -f projections/$(DEPDIR)/vandg.Plo
	-rm -f projections/$(DEPDIR)/vandg2.Plo
	-rm -f projections/$(DEPDIR)/vandg4.Plo
	-rm -f projections/$(DEPDIR)/wag2.Plo
	-rm -f projections/$(DEPDIR)/wag3.Plo
	-rm -f projections/$(DEPDIR)/wag7.Plo
	-rm -f projections/$(DEPDIR)/wink1.Plo
	-rm -f projections/$(DEPDIR)/wink2.Plo
	-rm -f tests/$(DEPDIR)/geodtest.Po
	-rm -f tests/$(DEPDIR)/multistresstest.Po
	-rm -f transformations/$(DEPDIR)/affine.Plo
	-rm -f transformations/$(DEPDIR)/defmodel.Plo
	-rm -f transformations/$(DEPDIR)/deformation.Plo
	-rm -f transformations/$(DEPDIR)/helmert.Plo
	-rm -f transformations/$(DEPDIR)/hgridshift.Plo
	-rm -f transformations/$(DEPDIR)/horner.Plo
	-rm -f transformations/$(DEPDIR)/molodensky.Plo
	-rm -f transformations/$(DEPDIR)/tinshift.Plo
	-rm -f transformations/$(DEPDIR)/vgridshift.Plo
	-rm -f transformations/$(DEPDIR)/xyzgridshift.Plo
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-hdr distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-includeHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS install-exec-local \
	install-libLTLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/4D_api.Plo
	-rm -f ./$(DEPDIR)/aasincos.Plo
	-rm -f ./$(DEPDIR)/adjlon.Plo
	-rm -f ./$(DEPDIR)/auth.Plo
	-rm -f ./$(DEPDIR)/ctx.Plo
	-rm -f ./$(DEPDIR)/datum_set.Plo
	-rm -f ./$(DEPDIR)/datums.Plo
	-rm -f ./$(DEPDIR)/deriv.Plo
	-rm -f ./$(DEPDIR)/dmstor.Plo
	-rm -f ./$(DEPDIR)/ell_set.Plo
	-rm -f ./$(DEPDIR)/ellps.Plo
	-rm -f ./$(DEPDIR)/factors.Plo
	-rm -f ./$(DEPDIR)/filemanager.Plo
	-rm -f ./$(DEPDIR)/fwd.Plo
	-rm -f ./$(DEPDIR)/gauss.Plo
	-rm -f ./$(DEPDIR)/generic_inverse.Plo
	-rm -f ./$(DEPDIR)/geodesic.Plo
	-rm -f ./$(DEPDIR)/grids.Plo
	-rm -f ./$(DEPDIR)/init.Plo
	-rm -f ./$(DEPDIR)/initcache.Plo
	-rm -f ./$(DEPDIR)/internal.Plo
	-rm -f ./$(DEPDIR)/inv.Plo
	-rm -f ./$(DEPDIR)/list.Plo
	-rm -f ./$(DEPDIR)/log.Plo
	-rm -f ./$(DEPDIR)/malloc.Plo
	-rm -f ./$(DEPDIR)/mlfn.Plo
	-rm -f ./$(DEPDIR)/msfn.Plo
	-rm -f ./$(DEPDIR)/mutex.Plo
	-rm -f ./$(DEPDIR)/networkfilemanager.Plo
	-rm -f ./$(DEPDIR)/param.Plo
	-rm -f ./$(DEPDIR)/phi2.Plo
	-rm -f ./$(DEPDIR)/pipeline.Plo
	-rm -f ./$(DEPDIR)/pr_list.Plo
	-rm -f ./$(DEPDIR)/proj_json_streaming_writer.Plo
	-rm -f ./$(DEPDIR)/proj_mdist.Plo
	-rm -f ./$(DEPDIR)/qsfn.Plo
	-rm -f ./$(DEPDIR)/release.Plo
	-rm -f ./$(DEPDIR)/rtodms.Plo
	-rm -f ./$(DEPDIR)/sqlite3_utils.Plo
	-rm -f ./$(DEPDIR)/strerrno.Plo
	-rm -f ./$(DEPDIR)/strtod.Plo
	-rm -f ./$(DEPDIR)/tracing.Plo
	-rm -f ./$(DEPDIR)/tsfn.Plo
	-rm -f ./$(DEPDIR)/units.Plo
	-rm -f ./$(DEPDIR)/wkt1_generated_parser.Plo
	-rm -f ./$(DEPDIR)/wkt1_parser.Plo
	-rm -f ./$(DEPDIR)/wkt2_generated_parser.Plo
	-rm -f ./$(DEPDIR)/wkt2_parser.Plo
	-rm -f ./$(DEPDIR)/wkt_parser.Plo
	-rm -f ./$(DEPDIR)/zpoly1.Plo
	-rm -f apps/$(DEPDIR)/cct.Po
	-rm -f apps/$(DEPDIR)/cs2cs.Po
	-rm -f apps/$(DEPDIR)/emess.Po
	-rm -f apps/$(DEPDIR)/geod.Po
	-rm -f apps/$(DEPDIR)/geod_interface.Po
	-rm -f apps/$(DEPDIR)/geod_set.Po
	-rm -f apps/$(DEPDIR)/gie.Po
	-rm -f apps/$(DEPDIR)/proj.Po
	-rm -f apps/$(DEPDIR)/proj_strtod.Po
	-rm -f apps/$(DEPDIR)/projinfo.Po
	-rm -f apps/$(DEPDIR)/projsync.Po
	-rm -f apps/$(DEPDIR)/utils.Po
	-rm -f conversions/$(DEPDIR)/axisswap.Plo
	-rm -f conversions/$(DEPDIR)/cart.Plo
	-rm -f conversions/$(DEPDIR)/geoc.Plo
	-rm -f conversions/$(DEPDIR)/geocent.Plo
	-rm -f conversions/$(DEPDIR)/noop.Plo
	-rm -f conversions/$(DEPDIR)/set.Plo
	-rm -f conversions/$(DEPDIR)/topocentric.Plo
	-rm -f conversions/$(DEPDIR)/unitconvert.Plo
	-rm -f iso19111/$(DEPDIR)/c_api.Plo
	-rm -f iso19111/$(DEPDIR)/common.Plo
	-rm -f iso19111/$(DEPDIR)/coordinatesystem.Plo
	-rm -f iso19111/$(DEPDIR)/crs.Plo
	-rm -f iso19111/$(DEPDIR)/datum.Plo
	-rm -f iso19111/$(DEPDIR)/factory.Plo
	-rm -f iso19111/$(DEPDIR)/internal.Plo
	-rm -f iso19111/$(DEPDIR)/io.Plo
	-rm -f iso19111/$(DEPDIR)/metadata.Plo
	-rm -f iso19111/$(DEPDIR)/static.Plo
	-rm -f iso19111/$(DEPDIR)/util.Plo
	-rm -f iso19111/operation/$(DEPDIR)/concatenatedoperation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/conversion.Plo
	-rm -f iso19111/operation/$(DEPDIR)/coordinateoperationfactory.Plo
	-rm -f iso19111/operation/$(DEPDIR)/esriparammappings.Plo
	-rm -f iso19111/operation/$(DEPDIR)/oputils.Plo
	-rm -f iso19111/operation/$(DEPDIR)/parammappings.Plo
	-rm -f iso19111/operation/$(DEPDIR)/projbasedoperation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/singleoperation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/transformation.Plo
	-rm -f iso19111/operation/$(DEPDIR)/vectorofvaluesparams.Plo
	-rm -f projections/$(DEPDIR)/adams.Plo
	-rm -f projections/$(DEPDIR)/aea.Plo
	-rm -f projections/$(DEPDIR)/aeqd.Plo
	-rm -f projections/$(DEPDIR)/airy.Plo
	-rm -f projections/$(DEPDIR)/aitoff.Plo
	-rm -f projections/$(DEPDIR)/august.Plo
	-rm -f projections/$(DEPDIR)/bacon.Plo
	-rm -f projections/$(DEPDIR)/bertin1953.Plo
	-rm -f projections/$(DEPDIR)/bipc.Plo
	-rm -f projections/$(DEPDIR)/boggs.Plo
	-rm -f projections/$(DEPDIR)/bonne.Plo
	-rm -f projections/$(DEPDIR)/calcofi.Plo
	-rm -f projections/$(DEPDIR)/cass.Plo
	-rm -f projections/$(DEPDIR)/cc.Plo
	-rm -f projections/$(DEPDIR)/ccon.Plo
	-rm -f projections/$(DEPDIR)/cea.Plo
	-rm -f projections/$(DEPDIR)/chamb.Plo
	-rm -f projections/$(DEPDIR)/col_urban.Plo
	-rm -f projections/$(DEPDIR)/collg.Plo
	-rm -f projections/$(DEPDIR)/comill.Plo
	-rm -f projections/$(DEPDIR)/crast.Plo
	-rm -f projections/$(DEPDIR)/denoy.Plo
	-rm -f projections/$(DEPDIR)/eck1.Plo
	-rm -f projections/$(DEPDIR)/eck2.Plo
	-rm -f projections/$(DEPDIR)/eck3.Plo
	-rm -f projections/$(DEPDIR)/eck4.Plo
	-rm -f projections/$(DEPDIR)/eck5.Plo
	-rm -f projections/$(DEPDIR)/eqc.Plo
	-rm -f projections/$(DEPDIR)/eqdc.Plo
	-rm -f projections/$(DEPDIR)/eqearth.Plo
	-rm -f projections/$(DEPDIR)/fahey.Plo
	-rm -f projections/$(DEPDIR)/fouc_s.Plo
	-rm -f projections/$(DEPDIR)/gall.Plo
	-rm -f projections/$(DEPDIR)/geos.Plo
	-rm -f projections/$(DEPDIR)/gins8.Plo
	-rm -f projections/$(DEPDIR)/gn_sinu.Plo
	-rm -f projections/$(DEPDIR)/gnom.Plo
	-rm -f projections/$(DEPDIR)/goode.Plo
	-rm -f projections/$(DEPDIR)/gstmerc.Plo
	-rm -f projections/$(DEPDIR)/hammer.Plo
	-rm -f projections/$(DEPDIR)/hatano.Plo
	-rm -f projections/$(DEPDIR)/healpix.Plo
	-rm -f projections/$(DEPDIR)/igh.Plo
	-rm -f projections/$(DEPDIR)/igh_o.Plo
	-rm -f projections/$(DEPDIR)/imw_p.Plo
	-rm -f projections/$(DEPDIR)/isea.Plo
	-rm -f projections/$(DEPDIR)/krovak.Plo
	-rm -f projections/$(DEPDIR)/labrd.Plo
	-rm -f projections/$(DEPDIR)/laea.Plo
	-rm -f projections/$(DEPDIR)/lagrng.Plo
	-rm -f projections/$(DEPDIR)/larr.Plo
	-rm -f projections/$(DEPDIR)/lask.Plo
	-rm -f projections/$(DEPDIR)/latlong.Plo
	-rm -f projections/$(DEPDIR)/lcc.Plo
	-rm -f projections/$(DEPDIR)/lcca.Plo
	-rm -f projections/$(DEPDIR)/loxim.Plo
	-rm -f projections/$(DEPDIR)/lsat.Plo
	-rm -f projections/$(DEPDIR)/mbt_fps.Plo
	-rm -f projections/$(DEPDIR)/mbtfpp.Plo
	-rm -f projections/$(DEPDIR)/mbtfpq.Plo
	-rm -f projections/$(DEPDIR)/merc.Plo
	-rm -f projections/$(DEPDIR)/mill.Plo
	-rm -f projections/$(DEPDIR)/misrsom.Plo
	-rm -f projections/$(DEPDIR)/mod_ster.Plo
	-rm -f projections/$(DEPDIR)/moll.Plo
	-rm -f projections/$(DEPDIR)/natearth.Plo
	-rm -f projections/$(DEPDIR)/natearth2.Plo
	-rm -f projections/$(DEPDIR)/nell.Plo
	-rm -f projections/$(DEPDIR)/nell_h.Plo
	-rm -f projections/$(DEPDIR)/nicol.Plo
	-rm -f projections/$(DEPDIR)/nsper.Plo
	-rm -f projections/$(DEPDIR)/nzmg.Plo
	-rm -f projections/$(DEPDIR)/ob_tran.Plo
	-rm -f projections/$(DEPDIR)/ocea.Plo
	-rm -f projections/$(DEPDIR)/oea.Plo
	-rm -f projections/$(DEPDIR)/omerc.Plo
	-rm -f projections/$(DEPDIR)/ortho.Plo
	-rm -f projections/$(DEPDIR)/patterson.Plo
	-rm -f projections/$(DEPDIR)/poly.Plo
	-rm -f projections/$(DEPDIR)/putp2.Plo
	-rm -f projections/$(DEPDIR)/putp3.Plo
	-rm -f projections/$(DEPDIR)/putp4p.Plo
	-rm -f projections/$(DEPDIR)/putp5.Plo
	-rm -f projections/$(DEPDIR)/putp6.Plo
	-rm -f projections/$(DEPDIR)/qsc.Plo
	-rm -f projections/$(DEPDIR)/robin.Plo
	-rm -f projections/$(DEPDIR)/rouss.Plo
	-rm -f projections/$(DEPDIR)/rpoly.Plo
	-rm -f projections/$(DEPDIR)/s2.Plo
	-rm -f projections/$(DEPDIR)/sch.Plo
	-rm -f projections/$(DEPDIR)/sconics.Plo
	-rm -f projections/$(DEPDIR)/somerc.Plo
	-rm -f projections/$(DEPDIR)/stere.Plo
	-rm -f projections/$(DEPDIR)/sterea.Plo
	-rm -f projections/$(DEPDIR)/sts.Plo
	-rm -f projections/$(DEPDIR)/tcc.Plo
	-rm -f projections/$(DEPDIR)/tcea.Plo
	-rm -f projections/$(DEPDIR)/times.Plo
	-rm -f projections/$(DEPDIR)/tmerc.Plo
	-rm -f projections/$(DEPDIR)/tobmerc.Plo
	-rm -f projections/$(DEPDIR)/tpeqd.Plo
	-rm -f projections/$(DEPDIR)/urm5.Plo
	-rm -f projections/$(DEPDIR)/urmfps.Plo
	-rm -f projections/$(DEPDIR)/vandg.Plo
	-rm -f projections/$(DEPDIR)/vandg2.Plo
	-rm -f projections/$(DEPDIR)/vandg4.Plo
	-rm -f projections/$(DEPDIR)/wag2.Plo
	-rm -f projections/$(DEPDIR)/wag3.Plo
	-rm -f projections/$(DEPDIR)/wag7.Plo
	-rm -f projections/$(DEPDIR)/wink1.Plo
	-rm -f projections/$(DEPDIR)/wink2.Plo
	-rm -f tests/$(DEPDIR)/geodtest.Po
	-rm -f tests/$(DEPDIR)/multistresstest.Po
	-rm -f transformations/$(DEPDIR)/affine.Plo
	-rm -f transformations/$(DEPDIR)/defmodel.Plo
	-rm -f transformations/$(DEPDIR)/deformation.Plo
	-rm -f transformations/$(DEPDIR)/helmert.Plo
	-rm -f transformations/$(DEPDIR)/hgridshift.Plo
	-rm -f transformations/$(DEPDIR)/horner.Plo
	-rm -f transformations/$(DEPDIR)/molodensky.Plo
	-rm -f transformations/$(DEPDIR)/tinshift.Plo
	-rm -f transformations/$(DEPDIR)/vgridshift.Plo
	-rm -f transformations/$(DEPDIR)/xyzgridshift.Plo
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-includeHEADERS \
	uninstall-libLTLIBRARIES

.MAKE: all check-am install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-TESTS \
	check-am clean clean-binPROGRAMS clean-checkPROGRAMS \
	clean-generic clean-libLTLIBRARIES clean-libtool \
	clean-noinstPROGRAMS cscopelist-am ctags ctags-am distclean \
	distclean-compile distclean-generic distclean-hdr \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-binPROGRAMS \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-exec-local install-html \
	install-html-am install-includeHEADERS install-info \
	install-info-am install-libLTLIBRARIES install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	recheck tags tags-am uninstall uninstall-am \
	uninstall-binPROGRAMS uninstall-includeHEADERS \
	uninstall-libLTLIBRARIES

.PRECIOUS: Makefile


# The sed hack is to please MSVC
wkt1_parser:
	bison --no-lines -d  -p pj_wkt1_ -o$(top_srcdir)/src/wkt1_generated_parser.c $(top_srcdir)/src/wkt1_grammar.y
	sed "s/\*yyssp = yystate/\*yyssp = (yytype_int16)yystate/" < $(top_srcdir)/src/wkt1_generated_parser.c | sed "s/yyerrorlab:/#if 0\nyyerrorlab:/" | sed "s/yyerrlab1:/#endif\nyyerrlab1:/" | sed "s/for (yylen = 0; yystr\[yylen\]; yylen++)/for (yylen = 0; yystr \&\& yystr\[yylen\]; yylen++)/" | sed "s/return yystpcpy (yyres, yystr) - yyres;/return (YYPTRDIFF_T)(yystpcpy (yyres, yystr) - yyres);/" | sed "s/YYPTRDIFF_T yysize = yyssp - yyss + 1;/YYPTRDIFF_T yysize = (YYPTRDIFF_T)(yyssp - yyss + 1);/" > $(top_srcdir)/src/wkt1_generated_parser.c.tmp
	mv $(top_srcdir)/src/wkt1_generated_parser.c.tmp $(top_srcdir)/src/wkt1_generated_parser.c

wkt2_parser:
	bison --no-lines -d  -p pj_wkt2_ -o$(top_srcdir)/src/wkt2_generated_parser.c $(top_srcdir)/src/wkt2_grammar.y
	sed "s/\*yyssp = yystate/\*yyssp = (yytype_int16)yystate/" < $(top_srcdir)/src/wkt2_generated_parser.c | sed "s/yyerrorlab:/#if 0\nyyerrorlab:/" | sed "s/yyerrlab1:/#endif\nyyerrlab1:/" | sed "s/for (yylen = 0; yystr\[yylen\]; yylen++)/for (yylen = 0; yystr \&\& yystr\[yylen\]; yylen++)/"| sed "s/return yystpcpy (yyres, yystr) - yyres;/return (YYPTRDIFF_T)(yystpcpy (yyres, yystr) - yyres);/" | sed "s/YYPTRDIFF_T yysize = yyssp - yyss + 1;/YYPTRDIFF_T yysize = (YYPTRDIFF_T)(yyssp - yyss + 1);/"> $(top_srcdir)/src/wkt2_generated_parser.c.tmp
	mv $(top_srcdir)/src/wkt2_generated_parser.c.tmp $(top_srcdir)/src/wkt2_generated_parser.c

install-exec-local: install-binPROGRAMS
	rm -f $(DESTDIR)$(bindir)/invproj$(EXEEXT)
	(cd $(DESTDIR)$(bindir); ln -s proj$(EXEEXT) invproj$(EXEEXT))
	rm -f $(DESTDIR)$(bindir)/invgeod$(EXEEXT)
	(cd $(DESTDIR)$(bindir); ln -s geod$(EXEEXT) invgeod$(EXEEXT))

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
