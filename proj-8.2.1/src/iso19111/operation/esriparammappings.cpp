// This file was generated by scripts/build_esri_projection_mapping.py. DO NOT
// EDIT !

/******************************************************************************
 *
 * Project:  PROJ
 * Purpose:  Mappings between ESRI projection and parameters names and WKT2
 * Author:   Even Rouault <even dot rouault at spatialys dot com>
 *
 ******************************************************************************
 * Copyright (c) 2019, Even Rouault <even dot rouault at spatialys dot com>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included
 * in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
 * OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL
 * THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
 * DEALINGS IN THE SOFTWARE.
 ****************************************************************************/

#ifndef FROM_PROJ_CPP
#define FROM_PROJ_CPP
#endif

#include "esriparammappings.hpp"
#include "proj_constants.h"

#include "proj/internal/internal.hpp"

NS_PROJ_START

using namespace internal;

namespace operation {

//! @cond Doxygen_Suppress

const ESRIParamMapping paramsESRI_Plate_Carree[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

const ESRIParamMapping paramsESRI_Equidistant_Cylindrical[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Miller_Cylindrical[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Mercator[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

const ESRIParamMapping paramsESRI_Gauss_Kruger[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

const ESRIParamMapping paramsESRI_Transverse_Mercator[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Transverse_Mercator_Complex[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Albers[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_EASTING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_EASTING_FALSE_ORIGIN, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_NORTHING_FALSE_ORIGIN, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_FALSE_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {"Standard_Parallel_2", EPSG_NAME_PARAMETER_LATITUDE_2ND_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_2ND_STD_PARALLEL, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_FALSE_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Sinusoidal[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Mollweide[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Eckert_I[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Eckert_II[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Eckert_III[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Eckert_IV[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Eckert_V[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Eckert_VI[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Gall_Stereographic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Behrmann[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", true},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "30.0", true},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Winkel_I[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Winkel_II[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Lambert_Conformal_Conic_alt1[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};
static const ESRIParamMapping paramsESRI_Lambert_Conformal_Conic_alt2[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_EASTING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_EASTING_FALSE_ORIGIN, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_NORTHING_FALSE_ORIGIN, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_FALSE_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {"Standard_Parallel_2", EPSG_NAME_PARAMETER_LATITUDE_2ND_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_2ND_STD_PARALLEL, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_FALSE_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};
static const ESRIParamMapping paramsESRI_Lambert_Conformal_Conic_alt3[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_EASTING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_EASTING_FALSE_ORIGIN, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_NORTHING_FALSE_ORIGIN, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_FALSE_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {"Standard_Parallel_2", EPSG_NAME_PARAMETER_LATITUDE_2ND_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_2ND_STD_PARALLEL, "0.0", false},
    {"Scale_Factor", nullptr, 0, "1.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_FALSE_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};
static const ESRIParamMapping paramsESRI_Lambert_Conformal_Conic_alt4[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_EASTING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_EASTING_FALSE_ORIGIN, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_NORTHING_FALSE_ORIGIN, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_FALSE_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {"Standard_Parallel_2", EPSG_NAME_PARAMETER_LATITUDE_2ND_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_2ND_STD_PARALLEL, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_ELLIPSOID_SCALE_FACTOR,
     EPSG_CODE_PARAMETER_ELLIPSOID_SCALE_FACTOR, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_FALSE_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_FALSE_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Polyconic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Quartic_Authalic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Loximuthal[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Central_Parallel", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Bonne[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping
    paramsESRI_Hotine_Oblique_Mercator_Two_Point_Natural_Origin[] = {
        {"False_Easting", EPSG_NAME_PARAMETER_EASTING_PROJECTION_CENTRE,
         EPSG_CODE_PARAMETER_EASTING_PROJECTION_CENTRE, "0.0", false},
        {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_PROJECTION_CENTRE,
         EPSG_CODE_PARAMETER_NORTHING_PROJECTION_CENTRE, "0.0", false},
        {"Latitude_Of_1st_Point", "Latitude of 1st point", 0, "0.0", false},
        {"Latitude_Of_2nd_Point", "Latitude of 2nd point", 0, "0.0", false},
        {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_INITIAL_LINE,
         EPSG_CODE_PARAMETER_SCALE_FACTOR_INITIAL_LINE, "0.0", false},
        {"Longitude_Of_1st_Point", "Longitude of 1st point", 0, "0.0", false},
        {"Longitude_Of_2nd_Point", "Longitude of 2nd point", 0, "0.0", false},
        {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
         EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
        {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Stereographic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Equidistant_Conic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {"Standard_Parallel_2", EPSG_NAME_PARAMETER_LATITUDE_2ND_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_2ND_STD_PARALLEL, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Cassini[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", nullptr, 0, "1.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Van_der_Grinten_I[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Robinson[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Two_Point_Equidistant[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Latitude_Of_1st_Point", "Latitude of 1st point", 0, "0.0", false},
    {"Latitude_Of_2nd_Point", "Latitude of 2nd point", 0, "0.0", false},
    {"Longitude_Of_1st_Point", "Longitude of 1st point", 0, "0.0", false},
    {"Longitude_Of_2nd_Point", "Longitude of 2nd point", 0, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Azimuthal_Equidistant[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Lambert_Azimuthal_Equal_Area[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Cylindrical_Equal_Area[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping
    paramsESRI_Hotine_Oblique_Mercator_Two_Point_Center[] = {
        {"False_Easting", EPSG_NAME_PARAMETER_EASTING_PROJECTION_CENTRE,
         EPSG_CODE_PARAMETER_EASTING_PROJECTION_CENTRE, "0.0", false},
        {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_PROJECTION_CENTRE,
         EPSG_CODE_PARAMETER_NORTHING_PROJECTION_CENTRE, "0.0", false},
        {"Latitude_Of_1st_Point", "Latitude of 1st point", 0, "0.0", false},
        {"Latitude_Of_2nd_Point", "Latitude of 2nd point", 0, "0.0", false},
        {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_INITIAL_LINE,
         EPSG_CODE_PARAMETER_SCALE_FACTOR_INITIAL_LINE, "0.0", false},
        {"Longitude_Of_1st_Point", "Longitude of 1st point", 0, "0.0", false},
        {"Longitude_Of_2nd_Point", "Longitude of 2nd point", 0, "0.0", false},
        {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
         EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
        {nullptr, nullptr, 0, "0.0", false}};

const ESRIParamMapping
    paramsESRI_Hotine_Oblique_Mercator_Azimuth_Natural_Origin[] = {
        {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
         EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
        {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
         EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
        {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_INITIAL_LINE,
         EPSG_CODE_PARAMETER_SCALE_FACTOR_INITIAL_LINE, "0.0", false},
        {"Azimuth", EPSG_NAME_PARAMETER_AZIMUTH_INITIAL_LINE,
         EPSG_CODE_PARAMETER_AZIMUTH_INITIAL_LINE, "0.0", false},
        {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_PROJECTION_CENTRE,
         EPSG_CODE_PARAMETER_LONGITUDE_PROJECTION_CENTRE, "0.0", false},
        {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
         EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
        {nullptr, nullptr, 0, "0.0", false}};

const ESRIParamMapping paramsESRI_Hotine_Oblique_Mercator_Azimuth_Center[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_EASTING_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_EASTING_PROJECTION_CENTRE, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_NORTHING_PROJECTION_CENTRE, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_INITIAL_LINE,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_INITIAL_LINE, "0.0", false},
    {"Azimuth", EPSG_NAME_PARAMETER_AZIMUTH_INITIAL_LINE,
     EPSG_CODE_PARAMETER_AZIMUTH_INITIAL_LINE, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LONGITUDE_PROJECTION_CENTRE, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Double_Stereographic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Krovak_alt1[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Pseudo_Standard_Parallel_1",
     EPSG_NAME_PARAMETER_LATITUDE_PSEUDO_STANDARD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_PSEUDO_STANDARD_PARALLEL, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_PSEUDO_STANDARD_PARALLEL,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_PSEUDO_STANDARD_PARALLEL, "0.0", false},
    {"Azimuth", EPSG_NAME_PARAMETER_COLATITUDE_CONE_AXIS,
     EPSG_CODE_PARAMETER_COLATITUDE_CONE_AXIS, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
    {"X_Scale", nullptr, 0, "1.0", false},
    {"Y_Scale", nullptr, 0, "1.0", false},
    {"XY_Plane_Rotation", nullptr, 0, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};
static const ESRIParamMapping paramsESRI_Krovak_alt2[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Pseudo_Standard_Parallel_1",
     EPSG_NAME_PARAMETER_LATITUDE_PSEUDO_STANDARD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_PSEUDO_STANDARD_PARALLEL, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_PSEUDO_STANDARD_PARALLEL,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_PSEUDO_STANDARD_PARALLEL, "0.0", false},
    {"Azimuth", EPSG_NAME_PARAMETER_COLATITUDE_CONE_AXIS,
     EPSG_CODE_PARAMETER_COLATITUDE_CONE_AXIS, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
    {"X_Scale", nullptr, 0, "-1.0", false},
    {"Y_Scale", nullptr, 0, "1.0", false},
    {"XY_Plane_Rotation", nullptr, 0, "90.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_New_Zealand_Map_Grid[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Longitude_Of_Origin", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Orthographic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Local[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Scale_Factor", nullptr, 0, "1.0", false},
    {"Azimuth", nullptr, 0, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Winkel_Tripel[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Aitoff[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Flat_Polar_Quartic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Craster_Parabolic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Gnomonic[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Times[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Vertical_Near_Side_Perspective[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_TOPOGRAPHIC_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_TOPOGRAPHIC_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_TOPOGRAPHIC_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_TOPOGRAPHIC_ORIGIN, "0.0", false},
    {"Height", EPSG_NAME_PARAMETER_VIEWPOINT_HEIGHT,
     EPSG_CODE_PARAMETER_VIEWPOINT_HEIGHT, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Stereographic_North_Pole[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Stereographic_South_Pole[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_STD_PARALLEL, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

const ESRIParamMapping paramsESRI_Rectified_Skew_Orthomorphic_Natural_Origin[] =
    {{"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
      EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
     {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
      EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
     {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_INITIAL_LINE,
      EPSG_CODE_PARAMETER_SCALE_FACTOR_INITIAL_LINE, "0.0", false},
     {"Azimuth", EPSG_NAME_PARAMETER_AZIMUTH_INITIAL_LINE,
      EPSG_CODE_PARAMETER_AZIMUTH_INITIAL_LINE, "0.0", false},
     {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_PROJECTION_CENTRE,
      EPSG_CODE_PARAMETER_LONGITUDE_PROJECTION_CENTRE, "0.0", false},
     {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
      EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
     {"XY_Plane_Rotation", EPSG_NAME_PARAMETER_ANGLE_RECTIFIED_TO_SKEW_GRID,
      EPSG_CODE_PARAMETER_ANGLE_RECTIFIED_TO_SKEW_GRID, "0.0", false},
     {nullptr, nullptr, 0, "0.0", false}};

const ESRIParamMapping paramsESRI_Rectified_Skew_Orthomorphic_Center[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_EASTING_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_EASTING_PROJECTION_CENTRE, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_NORTHING_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_NORTHING_PROJECTION_CENTRE, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_INITIAL_LINE,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_INITIAL_LINE, "0.0", false},
    {"Azimuth", EPSG_NAME_PARAMETER_AZIMUTH_INITIAL_LINE,
     EPSG_CODE_PARAMETER_AZIMUTH_INITIAL_LINE, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LONGITUDE_PROJECTION_CENTRE, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
    {"XY_Plane_Rotation", EPSG_NAME_PARAMETER_ANGLE_RECTIFIED_TO_SKEW_GRID,
     EPSG_CODE_PARAMETER_ANGLE_RECTIFIED_TO_SKEW_GRID, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Goode_Homolosine_alt1[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Option", nullptr, 0, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};
static const ESRIParamMapping paramsESRI_Goode_Homolosine_alt2[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Option", nullptr, 0, "1.0", false},
    {nullptr, nullptr, 0, "0.0", false}};
static const ESRIParamMapping paramsESRI_Goode_Homolosine_alt3[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Option", nullptr, 0, "2.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Equidistant_Cylindrical_Ellipsoidal[] =
    {{"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
      EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
     {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
      EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
     {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
      EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
     {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
      EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
     {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Laborde_Oblique_Mercator[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_INITIAL_LINE,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_INITIAL_LINE, "0.0", false},
    {"Azimuth", EPSG_NAME_PARAMETER_AZIMUTH_INITIAL_LINE,
     EPSG_CODE_PARAMETER_AZIMUTH_INITIAL_LINE, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LONGITUDE_PROJECTION_CENTRE, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_PROJECTION_CENTRE,
     EPSG_CODE_PARAMETER_LATITUDE_PROJECTION_CENTRE, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Gnomonic_Ellipsoidal[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Wagner_IV[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", nullptr, 0, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Wagner_V[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Wagner_VII[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Natural_Earth[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Natural_Earth_II[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Patterson[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Compact_Miller[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Geostationary_Satellite[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Height", "Satellite Height", 0, "0.0", false},
    {"Option", nullptr, 0, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Mercator_Auxiliary_Sphere[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Auxiliary_Sphere_Type", nullptr, 0, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Mercator_Variant_A[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Mercator_Variant_C[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Standard_Parallel_1", EPSG_NAME_PARAMETER_LATITUDE_1ST_STD_PARALLEL,
     EPSG_CODE_PARAMETER_LATITUDE_1ST_STD_PARALLEL, "0.0", false},
    {"Latitude_Of_Origin", nullptr, 0, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Transverse_Cylindrical_Equal_Area[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Scale_Factor", EPSG_NAME_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_SCALE_FACTOR_AT_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Origin", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_IGAC_Plano_Cartesiano[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Longitude_Of_Center", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Latitude_Of_Center", EPSG_NAME_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LATITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {"Height", EPSG_NAME_PARAMETER_PROJECTION_PLANE_ORIGIN_HEIGHT,
     EPSG_CODE_PARAMETER_PROJECTION_PLANE_ORIGIN_HEIGHT, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIParamMapping paramsESRI_Equal_Earth[] = {
    {"False_Easting", EPSG_NAME_PARAMETER_FALSE_EASTING,
     EPSG_CODE_PARAMETER_FALSE_EASTING, "0.0", false},
    {"False_Northing", EPSG_NAME_PARAMETER_FALSE_NORTHING,
     EPSG_CODE_PARAMETER_FALSE_NORTHING, "0.0", false},
    {"Central_Meridian", EPSG_NAME_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN,
     EPSG_CODE_PARAMETER_LONGITUDE_OF_NATURAL_ORIGIN, "0.0", false},
    {nullptr, nullptr, 0, "0.0", false}};

static const ESRIMethodMapping esriMappings[] = {
    {"Plate_Carree", EPSG_NAME_METHOD_EQUIDISTANT_CYLINDRICAL,
     EPSG_CODE_METHOD_EQUIDISTANT_CYLINDRICAL, paramsESRI_Plate_Carree},
    {"Plate_Carree", EPSG_NAME_METHOD_EQUIDISTANT_CYLINDRICAL_SPHERICAL,
     EPSG_CODE_METHOD_EQUIDISTANT_CYLINDRICAL_SPHERICAL,
     paramsESRI_Plate_Carree},
    {"Equidistant_Cylindrical", EPSG_NAME_METHOD_EQUIDISTANT_CYLINDRICAL,
     EPSG_CODE_METHOD_EQUIDISTANT_CYLINDRICAL,
     paramsESRI_Equidistant_Cylindrical},
    {"Miller_Cylindrical", PROJ_WKT2_NAME_METHOD_MILLER_CYLINDRICAL, 0,
     paramsESRI_Miller_Cylindrical},
    {"Mercator", EPSG_NAME_METHOD_MERCATOR_VARIANT_B,
     EPSG_CODE_METHOD_MERCATOR_VARIANT_B, paramsESRI_Mercator},
    {"Gauss_Kruger", EPSG_NAME_METHOD_TRANSVERSE_MERCATOR,
     EPSG_CODE_METHOD_TRANSVERSE_MERCATOR, paramsESRI_Gauss_Kruger},
    {"Transverse_Mercator", EPSG_NAME_METHOD_TRANSVERSE_MERCATOR,
     EPSG_CODE_METHOD_TRANSVERSE_MERCATOR, paramsESRI_Transverse_Mercator},
    {"Transverse_Mercator_Complex", EPSG_NAME_METHOD_TRANSVERSE_MERCATOR,
     EPSG_CODE_METHOD_TRANSVERSE_MERCATOR,
     paramsESRI_Transverse_Mercator_Complex},
    {"Albers", EPSG_NAME_METHOD_ALBERS_EQUAL_AREA,
     EPSG_CODE_METHOD_ALBERS_EQUAL_AREA, paramsESRI_Albers},
    {"Sinusoidal", PROJ_WKT2_NAME_METHOD_SINUSOIDAL, 0, paramsESRI_Sinusoidal},
    {"Mollweide", PROJ_WKT2_NAME_METHOD_MOLLWEIDE, 0, paramsESRI_Mollweide},
    {"Eckert_I", PROJ_WKT2_NAME_METHOD_ECKERT_I, 0, paramsESRI_Eckert_I},
    {"Eckert_II", PROJ_WKT2_NAME_METHOD_ECKERT_II, 0, paramsESRI_Eckert_II},
    {"Eckert_III", PROJ_WKT2_NAME_METHOD_ECKERT_III, 0, paramsESRI_Eckert_III},
    {"Eckert_IV", PROJ_WKT2_NAME_METHOD_ECKERT_IV, 0, paramsESRI_Eckert_IV},
    {"Eckert_V", PROJ_WKT2_NAME_METHOD_ECKERT_V, 0, paramsESRI_Eckert_V},
    {"Eckert_VI", PROJ_WKT2_NAME_METHOD_ECKERT_VI, 0, paramsESRI_Eckert_VI},
    {"Gall_Stereographic", PROJ_WKT2_NAME_METHOD_GALL_STEREOGRAPHIC, 0,
     paramsESRI_Gall_Stereographic},
    {"Behrmann", EPSG_NAME_METHOD_LAMBERT_CYLINDRICAL_EQUAL_AREA,
     EPSG_CODE_METHOD_LAMBERT_CYLINDRICAL_EQUAL_AREA, paramsESRI_Behrmann},
    {"Winkel_I", "Winkel I", 0, paramsESRI_Winkel_I},
    {"Winkel_II", "Winkel II", 0, paramsESRI_Winkel_II},
    {"Lambert_Conformal_Conic", EPSG_NAME_METHOD_LAMBERT_CONIC_CONFORMAL_1SP,
     EPSG_CODE_METHOD_LAMBERT_CONIC_CONFORMAL_1SP,
     paramsESRI_Lambert_Conformal_Conic_alt1},
    {"Lambert_Conformal_Conic", EPSG_NAME_METHOD_LAMBERT_CONIC_CONFORMAL_2SP,
     EPSG_CODE_METHOD_LAMBERT_CONIC_CONFORMAL_2SP,
     paramsESRI_Lambert_Conformal_Conic_alt2},
    {"Lambert_Conformal_Conic", EPSG_NAME_METHOD_LAMBERT_CONIC_CONFORMAL_2SP,
     EPSG_CODE_METHOD_LAMBERT_CONIC_CONFORMAL_2SP,
     paramsESRI_Lambert_Conformal_Conic_alt3},
    {"Lambert_Conformal_Conic",
     EPSG_NAME_METHOD_LAMBERT_CONIC_CONFORMAL_2SP_MICHIGAN,
     EPSG_CODE_METHOD_LAMBERT_CONIC_CONFORMAL_2SP_MICHIGAN,
     paramsESRI_Lambert_Conformal_Conic_alt4},
    {"Polyconic", EPSG_NAME_METHOD_AMERICAN_POLYCONIC,
     EPSG_CODE_METHOD_AMERICAN_POLYCONIC, paramsESRI_Polyconic},
    {"Quartic_Authalic", "Quartic Authalic", 0, paramsESRI_Quartic_Authalic},
    {"Loximuthal", "Loximuthal", 0, paramsESRI_Loximuthal},
    {"Bonne", EPSG_NAME_METHOD_BONNE, EPSG_CODE_METHOD_BONNE, paramsESRI_Bonne},
    {"Hotine_Oblique_Mercator_Two_Point_Natural_Origin",
     PROJ_WKT2_NAME_METHOD_HOTINE_OBLIQUE_MERCATOR_TWO_POINT_NATURAL_ORIGIN, 0,
     paramsESRI_Hotine_Oblique_Mercator_Two_Point_Natural_Origin},
    {"Stereographic", PROJ_WKT2_NAME_METHOD_STEREOGRAPHIC, 0,
     paramsESRI_Stereographic},
    {"Equidistant_Conic", PROJ_WKT2_NAME_METHOD_EQUIDISTANT_CONIC, 0,
     paramsESRI_Equidistant_Conic},
    {"Cassini", EPSG_NAME_METHOD_CASSINI_SOLDNER,
     EPSG_CODE_METHOD_CASSINI_SOLDNER, paramsESRI_Cassini},
    {"Van_der_Grinten_I", PROJ_WKT2_NAME_METHOD_VAN_DER_GRINTEN, 0,
     paramsESRI_Van_der_Grinten_I},
    {"Robinson", PROJ_WKT2_NAME_METHOD_ROBINSON, 0, paramsESRI_Robinson},
    {"Two_Point_Equidistant", PROJ_WKT2_NAME_METHOD_TWO_POINT_EQUIDISTANT, 0,
     paramsESRI_Two_Point_Equidistant},
    {"Azimuthal_Equidistant", EPSG_NAME_METHOD_MODIFIED_AZIMUTHAL_EQUIDISTANT,
     EPSG_CODE_METHOD_MODIFIED_AZIMUTHAL_EQUIDISTANT,
     paramsESRI_Azimuthal_Equidistant},
    {"Lambert_Azimuthal_Equal_Area",
     EPSG_NAME_METHOD_LAMBERT_AZIMUTHAL_EQUAL_AREA,
     EPSG_CODE_METHOD_LAMBERT_AZIMUTHAL_EQUAL_AREA,
     paramsESRI_Lambert_Azimuthal_Equal_Area},
    {"Cylindrical_Equal_Area", EPSG_NAME_METHOD_LAMBERT_CYLINDRICAL_EQUAL_AREA,
     EPSG_CODE_METHOD_LAMBERT_CYLINDRICAL_EQUAL_AREA,
     paramsESRI_Cylindrical_Equal_Area},
    {"Hotine_Oblique_Mercator_Two_Point_Center",
     PROJ_WKT2_NAME_METHOD_HOTINE_OBLIQUE_MERCATOR_TWO_POINT_NATURAL_ORIGIN, 0,
     paramsESRI_Hotine_Oblique_Mercator_Two_Point_Center},
    {"Hotine_Oblique_Mercator_Azimuth_Natural_Origin",
     EPSG_NAME_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_A,
     EPSG_CODE_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_A,
     paramsESRI_Hotine_Oblique_Mercator_Azimuth_Natural_Origin},
    {"Hotine_Oblique_Mercator_Azimuth_Center",
     EPSG_NAME_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_B,
     EPSG_CODE_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_B,
     paramsESRI_Hotine_Oblique_Mercator_Azimuth_Center},
    {"Double_Stereographic", EPSG_NAME_METHOD_OBLIQUE_STEREOGRAPHIC,
     EPSG_CODE_METHOD_OBLIQUE_STEREOGRAPHIC, paramsESRI_Double_Stereographic},
    {"Krovak", EPSG_NAME_METHOD_KROVAK, EPSG_CODE_METHOD_KROVAK,
     paramsESRI_Krovak_alt1},
    {"Krovak", EPSG_NAME_METHOD_KROVAK_NORTH_ORIENTED,
     EPSG_CODE_METHOD_KROVAK_NORTH_ORIENTED, paramsESRI_Krovak_alt2},
    {"New_Zealand_Map_Grid", EPSG_NAME_METHOD_NZMG, EPSG_CODE_METHOD_NZMG,
     paramsESRI_New_Zealand_Map_Grid},
    {"Orthographic", PROJ_WKT2_NAME_ORTHOGRAPHIC_SPHERICAL, 0,
     paramsESRI_Orthographic},
    {"Local", EPSG_NAME_METHOD_ORTHOGRAPHIC, EPSG_CODE_METHOD_ORTHOGRAPHIC,
     paramsESRI_Local},
    {"Winkel_Tripel", "Winkel Tripel", 0, paramsESRI_Winkel_Tripel},
    {"Aitoff", "Aitoff", 0, paramsESRI_Aitoff},
    {"Flat_Polar_Quartic", PROJ_WKT2_NAME_METHOD_FLAT_POLAR_QUARTIC, 0,
     paramsESRI_Flat_Polar_Quartic},
    {"Craster_Parabolic", "Craster Parabolic", 0, paramsESRI_Craster_Parabolic},
    {"Gnomonic", PROJ_WKT2_NAME_METHOD_GNOMONIC, 0, paramsESRI_Gnomonic},
    {"Times", PROJ_WKT2_NAME_METHOD_TIMES, 0, paramsESRI_Times},
    {"Vertical_Near_Side_Perspective", EPSG_NAME_METHOD_VERTICAL_PERSPECTIVE,
     EPSG_CODE_METHOD_VERTICAL_PERSPECTIVE,
     paramsESRI_Vertical_Near_Side_Perspective},
    {"Stereographic_North_Pole", EPSG_NAME_METHOD_POLAR_STEREOGRAPHIC_VARIANT_B,
     EPSG_CODE_METHOD_POLAR_STEREOGRAPHIC_VARIANT_B,
     paramsESRI_Stereographic_North_Pole},
    {"Stereographic_South_Pole", EPSG_NAME_METHOD_POLAR_STEREOGRAPHIC_VARIANT_B,
     EPSG_CODE_METHOD_POLAR_STEREOGRAPHIC_VARIANT_B,
     paramsESRI_Stereographic_South_Pole},
    {"Rectified_Skew_Orthomorphic_Natural_Origin",
     EPSG_NAME_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_A,
     EPSG_CODE_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_A,
     paramsESRI_Rectified_Skew_Orthomorphic_Natural_Origin},
    {"Rectified_Skew_Orthomorphic_Center",
     EPSG_NAME_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_B,
     EPSG_CODE_METHOD_HOTINE_OBLIQUE_MERCATOR_VARIANT_B,
     paramsESRI_Rectified_Skew_Orthomorphic_Center},
    {"Goode_Homolosine", PROJ_WKT2_NAME_METHOD_GOODE_HOMOLOSINE, 0,
     paramsESRI_Goode_Homolosine_alt1},
    {"Goode_Homolosine", PROJ_WKT2_NAME_METHOD_INTERRUPTED_GOODE_HOMOLOSINE, 0,
     paramsESRI_Goode_Homolosine_alt2},
    {"Goode_Homolosine",
     PROJ_WKT2_NAME_METHOD_INTERRUPTED_GOODE_HOMOLOSINE_OCEAN, 0,
     paramsESRI_Goode_Homolosine_alt3},
    {"Equidistant_Cylindrical_Ellipsoidal",
     EPSG_NAME_METHOD_EQUIDISTANT_CYLINDRICAL,
     EPSG_CODE_METHOD_EQUIDISTANT_CYLINDRICAL,
     paramsESRI_Equidistant_Cylindrical_Ellipsoidal},
    {"Laborde_Oblique_Mercator", EPSG_NAME_METHOD_LABORDE_OBLIQUE_MERCATOR,
     EPSG_CODE_METHOD_LABORDE_OBLIQUE_MERCATOR,
     paramsESRI_Laborde_Oblique_Mercator},
    {"Gnomonic_Ellipsoidal", PROJ_WKT2_NAME_METHOD_GNOMONIC, 0,
     paramsESRI_Gnomonic_Ellipsoidal},
    {"Wagner_IV", PROJ_WKT2_NAME_METHOD_WAGNER_IV, 0, paramsESRI_Wagner_IV},
    {"Wagner_V", PROJ_WKT2_NAME_METHOD_WAGNER_V, 0, paramsESRI_Wagner_V},
    {"Wagner_VII", PROJ_WKT2_NAME_METHOD_WAGNER_VII, 0, paramsESRI_Wagner_VII},
    {"Natural_Earth", PROJ_WKT2_NAME_METHOD_NATURAL_EARTH, 0,
     paramsESRI_Natural_Earth},
    {"Natural_Earth_II", PROJ_WKT2_NAME_METHOD_NATURAL_EARTH_II, 0,
     paramsESRI_Natural_Earth_II},
    {"Patterson", PROJ_WKT2_NAME_METHOD_PATTERSON, 0, paramsESRI_Patterson},
    {"Compact_Miller", PROJ_WKT2_NAME_METHOD_COMPACT_MILLER, 0,
     paramsESRI_Compact_Miller},
    {"Geostationary_Satellite",
     PROJ_WKT2_NAME_METHOD_GEOSTATIONARY_SATELLITE_SWEEP_Y, 0,
     paramsESRI_Geostationary_Satellite},
    {"Mercator_Auxiliary_Sphere",
     EPSG_NAME_METHOD_POPULAR_VISUALISATION_PSEUDO_MERCATOR,
     EPSG_CODE_METHOD_POPULAR_VISUALISATION_PSEUDO_MERCATOR,
     paramsESRI_Mercator_Auxiliary_Sphere},
    {"Mercator_Variant_A", EPSG_NAME_METHOD_MERCATOR_VARIANT_A,
     EPSG_CODE_METHOD_MERCATOR_VARIANT_A, paramsESRI_Mercator_Variant_A},
    {"Mercator_Variant_C", EPSG_NAME_METHOD_MERCATOR_VARIANT_B,
     EPSG_CODE_METHOD_MERCATOR_VARIANT_B, paramsESRI_Mercator_Variant_C},
    {"Transverse_Cylindrical_Equal_Area", "Transverse Cylindrical Equal Area",
     0, paramsESRI_Transverse_Cylindrical_Equal_Area},
    {"IGAC_Plano_Cartesiano", EPSG_NAME_METHOD_COLOMBIA_URBAN,
     EPSG_CODE_METHOD_COLOMBIA_URBAN, paramsESRI_IGAC_Plano_Cartesiano},
    {"Equal_Earth", EPSG_NAME_METHOD_EQUAL_EARTH, EPSG_CODE_METHOD_EQUAL_EARTH,
     paramsESRI_Equal_Earth},
};

// ---------------------------------------------------------------------------

const ESRIMethodMapping *getEsriMappings(size_t &nElts) {
    nElts = sizeof(esriMappings) / sizeof(esriMappings[0]);
    return esriMappings;
}

// ---------------------------------------------------------------------------

std::vector<const ESRIMethodMapping *>
getMappingsFromESRI(const std::string &esri_name) {
    std::vector<const ESRIMethodMapping *> res;
    for (const auto &mapping : esriMappings) {
        if (ci_equal(esri_name, mapping.esri_name)) {
            res.push_back(&mapping);
        }
    }
    return res;
}

//! @endcond

// ---------------------------------------------------------------------------

} // namespace operation
NS_PROJ_END
