#ifdef DO_PJ_LIST_ID
static const char PJ_LIST_H_ID[] = "@(#)pj_list.h	4.5	95/08/09	GIE	REL";
#endif
/* Full list of current projections for Tue Jan 11 12:27:04 EST 1994
**
** Copy this file and retain only appropriate lines for subset list
*/
PROJ_HEAD(adams_hemi, "Adams Hemisphere in a Square")
PROJ_HEAD(adams_ws1, "Adams World in a Square I")
PROJ_HEAD(adams_ws2, "Adams World in a Square II")
PROJ_HEAD(aea, "Albers Equal Area")
PROJ_HEAD(aeqd, "Azimuthal Equidistant")
PROJ_HEAD(affine, "Affine transformation")
PROJ_HEAD(airy, "Airy")
PROJ_HEAD(aitoff, "Aitoff")
PROJ_HEAD(alsk, "Modified Stereographic of Alaska")
PROJ_HEAD(apian, "Apian Globular I")
PROJ_HEAD(august, "August Epicycloidal")
PROJ_HEAD(axisswap, "Axis ordering")
PROJ_HEAD(bacon, "Bacon Globular")
PROJ_HEAD(bertin1953, "Bertin 1953")
PROJ_HEAD(bipc, "Bipolar conic of western hemisphere")
PROJ_HEAD(boggs, "Boggs Eumorphic")
PROJ_HEAD(bonne, "Bonne (Werner lat_1=90)")
PROJ_HEAD(calcofi, "Cal Coop Ocean Fish Invest Lines/Stations")
PROJ_HEAD(cart,    "Geodetic/cartesian conversions")
PROJ_HEAD(cass, "Cassini")
PROJ_HEAD(cc, "Central Cylindrical")
PROJ_HEAD(ccon, "Central Conic")
PROJ_HEAD(cea, "Equal Area Cylindrical")
PROJ_HEAD(chamb, "Chamberlin Trimetric")
PROJ_HEAD(collg, "Collignon")
PROJ_HEAD(col_urban, "Colombia Urban")
PROJ_HEAD(comill, "Compact Miller")
PROJ_HEAD(crast, "Craster Parabolic (Putnins P4)")
PROJ_HEAD(defmodel, "Deformation model")
PROJ_HEAD(deformation, "Kinematic grid shift")
PROJ_HEAD(denoy, "Denoyer Semi-Elliptical")
PROJ_HEAD(eck1, "Eckert I")
PROJ_HEAD(eck2, "Eckert II")
PROJ_HEAD(eck3, "Eckert III")
PROJ_HEAD(eck4, "Eckert IV")
PROJ_HEAD(eck5, "Eckert V")
PROJ_HEAD(eck6, "Eckert VI")
PROJ_HEAD(eqearth, "Equal Earth")
PROJ_HEAD(eqc, "Equidistant Cylindrical (Plate Carree)")
PROJ_HEAD(eqdc, "Equidistant Conic")
PROJ_HEAD(euler, "Euler")
PROJ_HEAD(etmerc, "Extended Transverse Mercator" )
PROJ_HEAD(fahey, "Fahey")
PROJ_HEAD(fouc, "Foucaut")
PROJ_HEAD(fouc_s, "Foucaut Sinusoidal")
PROJ_HEAD(gall, "Gall (Gall Stereographic)")
PROJ_HEAD(geoc, "Geocentric Latitude")
PROJ_HEAD(geocent, "Geocentric")
PROJ_HEAD(geogoffset, "Geographic Offset")
PROJ_HEAD(geos, "Geostationary Satellite View")
PROJ_HEAD(gins8, "Ginsburg VIII (TsNIIGAiK)")
PROJ_HEAD(gn_sinu, "General Sinusoidal Series")
PROJ_HEAD(gnom, "Gnomonic")
PROJ_HEAD(goode, "Goode Homolosine")
PROJ_HEAD(gs48, "Modified Stereographic of 48 U.S.")
PROJ_HEAD(gs50, "Modified Stereographic of 50 U.S.")
PROJ_HEAD(guyou, "Guyou")
PROJ_HEAD(hammer, "Hammer & Eckert-Greifendorff")
PROJ_HEAD(hatano, "Hatano Asymmetrical Equal Area")
PROJ_HEAD(healpix, "HEALPix")
PROJ_HEAD(rhealpix, "rHEALPix")
PROJ_HEAD(helmert,   "3- and 7-parameter Helmert shift")
PROJ_HEAD(hgridshift, "Horizontal grid shift")
PROJ_HEAD(horner,    "Horner polynomial evaluation")
PROJ_HEAD(igh,  "Interrupted Goode Homolosine")
PROJ_HEAD(igh_o,  "Interrupted Goode Homolosine Oceanic View")
PROJ_HEAD(imw_p, "International Map of the World Polyconic")
PROJ_HEAD(isea, "Icosahedral Snyder Equal Area")
PROJ_HEAD(kav5, "Kavraisky V")
PROJ_HEAD(kav7, "Kavraisky VII")
PROJ_HEAD(krovak, "Krovak")
PROJ_HEAD(labrd, "Laborde")
PROJ_HEAD(laea, "Lambert Azimuthal Equal Area")
PROJ_HEAD(lagrng, "Lagrange")
PROJ_HEAD(larr, "Larrivee")
PROJ_HEAD(lask, "Laskowski")
PROJ_HEAD(lonlat, "Lat/long (Geodetic)")
PROJ_HEAD(latlon, "Lat/long (Geodetic alias)")
PROJ_HEAD(latlong, "Lat/long (Geodetic alias)")
PROJ_HEAD(longlat, "Lat/long (Geodetic alias)")
PROJ_HEAD(lcc, "Lambert Conformal Conic")
PROJ_HEAD(lcca, "Lambert Conformal Conic Alternative")
PROJ_HEAD(leac, "Lambert Equal Area Conic")
PROJ_HEAD(lee_os, "Lee Oblated Stereographic")
PROJ_HEAD(loxim, "Loximuthal")
PROJ_HEAD(lsat, "Space oblique for LANDSAT")
PROJ_HEAD(mbt_s, "McBryde-Thomas Flat-Polar Sine")
PROJ_HEAD(mbt_fps, "McBryde-Thomas Flat-Pole Sine (No. 2)")
PROJ_HEAD(mbtfpp, "McBride-Thomas Flat-Polar Parabolic")
PROJ_HEAD(mbtfpq, "McBryde-Thomas Flat-Polar Quartic")
PROJ_HEAD(mbtfps, "McBryde-Thomas Flat-Polar Sinusoidal")
PROJ_HEAD(merc, "Mercator")
PROJ_HEAD(mil_os, "Miller Oblated Stereographic")
PROJ_HEAD(mill, "Miller Cylindrical")
PROJ_HEAD(misrsom, "Space oblique for MISR")
PROJ_HEAD(moll, "Mollweide")
PROJ_HEAD(molobadekas, "Molodensky-Badekas transform") /* implemented in PJ_helmert.c */
PROJ_HEAD(molodensky, "Molodensky transform")
PROJ_HEAD(murd1, "Murdoch I")
PROJ_HEAD(murd2, "Murdoch II")
PROJ_HEAD(murd3, "Murdoch III")
PROJ_HEAD(natearth, "Natural Earth")
PROJ_HEAD(natearth2, "Natural Earth II")
PROJ_HEAD(nell, "Nell")
PROJ_HEAD(nell_h, "Nell-Hammer")
PROJ_HEAD(nicol, "Nicolosi Globular")
PROJ_HEAD(nsper, "Near-sided perspective")
PROJ_HEAD(nzmg, "New Zealand Map Grid")
PROJ_HEAD(noop, "No operation")
PROJ_HEAD(ob_tran, "General Oblique Transformation")
PROJ_HEAD(ocea, "Oblique Cylindrical Equal Area")
PROJ_HEAD(oea, "Oblated Equal Area")
PROJ_HEAD(omerc, "Oblique Mercator")
PROJ_HEAD(ortel, "Ortelius Oval")
PROJ_HEAD(ortho, "Orthographic")
PROJ_HEAD(pconic, "Perspective Conic")
PROJ_HEAD(patterson, "Patterson Cylindrical")
PROJ_HEAD(peirce_q, "Peirce Quincuncial")
PROJ_HEAD(pipeline, "Transformation pipeline manager")
PROJ_HEAD(poly, "Polyconic (American)")
PROJ_HEAD(pop, "Retrieve coordinate value from pipeline stack")
PROJ_HEAD(push, "Save coordinate value on pipeline stack")
PROJ_HEAD(putp1, "Putnins P1")
PROJ_HEAD(putp2, "Putnins P2")
PROJ_HEAD(putp3, "Putnins P3")
PROJ_HEAD(putp3p, "Putnins P3'")
PROJ_HEAD(putp4p, "Putnins P4'")
PROJ_HEAD(putp5, "Putnins P5")
PROJ_HEAD(putp5p, "Putnins P5'")
PROJ_HEAD(putp6, "Putnins P6")
PROJ_HEAD(putp6p, "Putnins P6'")
PROJ_HEAD(qua_aut, "Quartic Authalic")
PROJ_HEAD(qsc, "Quadrilateralized Spherical Cube")
PROJ_HEAD(robin, "Robinson")
PROJ_HEAD(rouss, "Roussilhe Stereographic")
PROJ_HEAD(rpoly, "Rectangular Polyconic")
PROJ_HEAD(s2, "S2")
PROJ_HEAD(sch, "Spherical Cross-track Height")
PROJ_HEAD(set, "Set coordinate value")
PROJ_HEAD(sinu, "Sinusoidal (Sanson-Flamsteed)")
PROJ_HEAD(somerc, "Swiss. Obl. Mercator")
PROJ_HEAD(stere, "Stereographic")
PROJ_HEAD(sterea, "Oblique Stereographic Alternative")
PROJ_HEAD(gstmerc, "Gauss-Schreiber Transverse Mercator (aka Gauss-Laborde Reunion)")
PROJ_HEAD(tcc, "Transverse Central Cylindrical")
PROJ_HEAD(tcea, "Transverse Cylindrical Equal Area")
PROJ_HEAD(times, "Times Projection")
PROJ_HEAD(tinshift, "Triangulation based transformation")
PROJ_HEAD(tissot, "Tissot Conic")
PROJ_HEAD(tmerc, "Transverse Mercator")
PROJ_HEAD(tobmerc, "Tobler-Mercator")
PROJ_HEAD(topocentric, "Geocentric/Topocentric conversion")
PROJ_HEAD(tpeqd, "Two Point Equidistant")
PROJ_HEAD(tpers, "Tilted perspective")
PROJ_HEAD(unitconvert, "Unit conversion")
PROJ_HEAD(ups, "Universal Polar Stereographic")
PROJ_HEAD(urm5, "Urmaev V")
PROJ_HEAD(urmfps, "Urmaev Flat-Polar Sinusoidal")
PROJ_HEAD(utm, "Universal Transverse Mercator (UTM)")
PROJ_HEAD(vandg, "van der Grinten (I)")
PROJ_HEAD(vandg2, "van der Grinten II")
PROJ_HEAD(vandg3, "van der Grinten III")
PROJ_HEAD(vandg4, "van der Grinten IV")
PROJ_HEAD(vitk1, "Vitkovsky I")
PROJ_HEAD(vgridshift, "Vertical grid shift")
PROJ_HEAD(wag1, "Wagner I (Kavraisky VI)")
PROJ_HEAD(wag2, "Wagner II")
PROJ_HEAD(wag3, "Wagner III")
PROJ_HEAD(wag4, "Wagner IV")
PROJ_HEAD(wag5, "Wagner V")
PROJ_HEAD(wag6, "Wagner VI")
PROJ_HEAD(wag7, "Wagner VII")
PROJ_HEAD(webmerc, "Web Mercator / Pseudo Mercator")
PROJ_HEAD(weren, "Werenskiold I")
PROJ_HEAD(wink1, "Winkel I")
PROJ_HEAD(wink2, "Winkel II")
PROJ_HEAD(wintri, "Winkel Tripel")
PROJ_HEAD(xyzgridshift, "XYZ grid shift")
