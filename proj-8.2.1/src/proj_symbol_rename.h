/* This is a generated file by create_proj_symbol_rename.sh. *DO NOT EDIT MANUALLY !* */
#ifndef PROJ_SYMBOL_RENAME_H
#define PROJ_SYMBOL_RENAME_H
#define geod_direct internal_geod_direct
#define geod_directline internal_geod_directline
#define geod_gendirect internal_geod_gendirect
#define geod_gendirectline internal_geod_gendirectline
#define geod_geninverse internal_geod_geninverse
#define geod_genposition internal_geod_genposition
#define geod_gensetdistance internal_geod_gensetdistance
#define geod_init internal_geod_init
#define geod_inverse internal_geod_inverse
#define geod_inverseline internal_geod_inverseline
#define geod_lineinit internal_geod_lineinit
#define geod_polygon_addedge internal_geod_polygon_addedge
#define geod_polygon_addpoint internal_geod_polygon_addpoint
#define geod_polygonarea internal_geod_polygonarea
#define geod_polygon_clear internal_geod_polygon_clear
#define geod_polygon_compute internal_geod_polygon_compute
#define geod_polygon_init internal_geod_polygon_init
#define geod_polygon_testedge internal_geod_polygon_testedge
#define geod_polygon_testpoint internal_geod_polygon_testpoint
#define geod_position internal_geod_position
#define geod_setdistance internal_geod_setdistance
#define proj_alter_id internal_proj_alter_id
#define proj_alter_name internal_proj_alter_name
#define proj_angular_input internal_proj_angular_input
#define proj_angular_output internal_proj_angular_output
#define proj_area_create internal_proj_area_create
#define proj_area_destroy internal_proj_area_destroy
#define proj_area_set_bbox internal_proj_area_set_bbox
#define proj_as_projjson internal_proj_as_projjson
#define proj_as_proj_string internal_proj_as_proj_string
#define proj_assign_context internal_proj_assign_context
#define proj_as_wkt internal_proj_as_wkt
#define proj_celestial_body_list_destroy internal_proj_celestial_body_list_destroy
#define proj_cleanup internal_proj_cleanup
#define proj_clone internal_proj_clone
#define proj_concatoperation_get_step internal_proj_concatoperation_get_step
#define proj_concatoperation_get_step_count internal_proj_concatoperation_get_step_count
#define proj_context_clone internal_proj_context_clone
#define proj_context_create internal_proj_context_create
#define proj_context_destroy internal_proj_context_destroy
#define proj_context_errno internal_proj_context_errno
#define proj_context_errno_string internal_proj_context_errno_string
#define proj_context_get_database_metadata internal_proj_context_get_database_metadata
#define proj_context_get_database_path internal_proj_context_get_database_path
#define proj_context_get_database_structure internal_proj_context_get_database_structure
#define proj_context_get_url_endpoint internal_proj_context_get_url_endpoint
#define proj_context_get_use_proj4_init_rules internal_proj_context_get_use_proj4_init_rules
#define proj_context_get_user_writable_directory internal_proj_context_get_user_writable_directory
#define proj_context_guess_wkt_dialect internal_proj_context_guess_wkt_dialect
#define proj_context_is_network_enabled internal_proj_context_is_network_enabled
#define proj_context_set_autoclose_database internal_proj_context_set_autoclose_database
#define proj_context_set_ca_bundle_path internal_proj_context_set_ca_bundle_path
#define proj_context_set_database_path internal_proj_context_set_database_path
#define proj_context_set_enable_network internal_proj_context_set_enable_network
#define proj_context_set_fileapi internal_proj_context_set_fileapi
#define proj_context_set_file_finder internal_proj_context_set_file_finder
#define proj_context_set_network_callbacks internal_proj_context_set_network_callbacks
#define proj_context_set_search_paths internal_proj_context_set_search_paths
#define proj_context_set_sqlite3_vfs_name internal_proj_context_set_sqlite3_vfs_name
#define proj_context_set_url_endpoint internal_proj_context_set_url_endpoint
#define proj_context_use_proj4_init_rules internal_proj_context_use_proj4_init_rules
#define proj_convert_conversion_to_other_method internal_proj_convert_conversion_to_other_method
#define proj_coord internal_proj_coord
#define proj_coordoperation_create_inverse internal_proj_coordoperation_create_inverse
#define proj_coordoperation_get_accuracy internal_proj_coordoperation_get_accuracy
#define proj_coordoperation_get_grid_used internal_proj_coordoperation_get_grid_used
#define proj_coordoperation_get_grid_used_count internal_proj_coordoperation_get_grid_used_count
#define proj_coordoperation_get_method_info internal_proj_coordoperation_get_method_info
#define proj_coordoperation_get_param internal_proj_coordoperation_get_param
#define proj_coordoperation_get_param_count internal_proj_coordoperation_get_param_count
#define proj_coordoperation_get_param_index internal_proj_coordoperation_get_param_index
#define proj_coordoperation_get_towgs84_values internal_proj_coordoperation_get_towgs84_values
#define proj_coordoperation_has_ballpark_transformation internal_proj_coordoperation_has_ballpark_transformation
#define proj_coordoperation_is_instantiable internal_proj_coordoperation_is_instantiable
#define proj_create internal_proj_create
#define proj_create_argv internal_proj_create_argv
#define proj_create_cartesian_2D_cs internal_proj_create_cartesian_2D_cs
#define proj_create_compound_crs internal_proj_create_compound_crs
#define proj_create_conversion internal_proj_create_conversion
#define proj_create_conversion_albers_equal_area internal_proj_create_conversion_albers_equal_area
#define proj_create_conversion_american_polyconic internal_proj_create_conversion_american_polyconic
#define proj_create_conversion_azimuthal_equidistant internal_proj_create_conversion_azimuthal_equidistant
#define proj_create_conversion_bonne internal_proj_create_conversion_bonne
#define proj_create_conversion_cassini_soldner internal_proj_create_conversion_cassini_soldner
#define proj_create_conversion_eckert_i internal_proj_create_conversion_eckert_i
#define proj_create_conversion_eckert_ii internal_proj_create_conversion_eckert_ii
#define proj_create_conversion_eckert_iii internal_proj_create_conversion_eckert_iii
#define proj_create_conversion_eckert_iv internal_proj_create_conversion_eckert_iv
#define proj_create_conversion_eckert_v internal_proj_create_conversion_eckert_v
#define proj_create_conversion_eckert_vi internal_proj_create_conversion_eckert_vi
#define proj_create_conversion_equal_earth internal_proj_create_conversion_equal_earth
#define proj_create_conversion_equidistant_conic internal_proj_create_conversion_equidistant_conic
#define proj_create_conversion_equidistant_cylindrical internal_proj_create_conversion_equidistant_cylindrical
#define proj_create_conversion_equidistant_cylindrical_spherical internal_proj_create_conversion_equidistant_cylindrical_spherical
#define proj_create_conversion_gall internal_proj_create_conversion_gall
#define proj_create_conversion_gauss_schreiber_transverse_mercator internal_proj_create_conversion_gauss_schreiber_transverse_mercator
#define proj_create_conversion_geostationary_satellite_sweep_x internal_proj_create_conversion_geostationary_satellite_sweep_x
#define proj_create_conversion_geostationary_satellite_sweep_y internal_proj_create_conversion_geostationary_satellite_sweep_y
#define proj_create_conversion_gnomonic internal_proj_create_conversion_gnomonic
#define proj_create_conversion_goode_homolosine internal_proj_create_conversion_goode_homolosine
#define proj_create_conversion_guam_projection internal_proj_create_conversion_guam_projection
#define proj_create_conversion_hotine_oblique_mercator_two_point_natural_origin internal_proj_create_conversion_hotine_oblique_mercator_two_point_natural_origin
#define proj_create_conversion_hotine_oblique_mercator_variant_a internal_proj_create_conversion_hotine_oblique_mercator_variant_a
#define proj_create_conversion_hotine_oblique_mercator_variant_b internal_proj_create_conversion_hotine_oblique_mercator_variant_b
#define proj_create_conversion_international_map_world_polyconic internal_proj_create_conversion_international_map_world_polyconic
#define proj_create_conversion_interrupted_goode_homolosine internal_proj_create_conversion_interrupted_goode_homolosine
#define proj_create_conversion_krovak internal_proj_create_conversion_krovak
#define proj_create_conversion_krovak_north_oriented internal_proj_create_conversion_krovak_north_oriented
#define proj_create_conversion_laborde_oblique_mercator internal_proj_create_conversion_laborde_oblique_mercator
#define proj_create_conversion_lambert_azimuthal_equal_area internal_proj_create_conversion_lambert_azimuthal_equal_area
#define proj_create_conversion_lambert_conic_conformal_1sp internal_proj_create_conversion_lambert_conic_conformal_1sp
#define proj_create_conversion_lambert_conic_conformal_2sp internal_proj_create_conversion_lambert_conic_conformal_2sp
#define proj_create_conversion_lambert_conic_conformal_2sp_belgium internal_proj_create_conversion_lambert_conic_conformal_2sp_belgium
#define proj_create_conversion_lambert_conic_conformal_2sp_michigan internal_proj_create_conversion_lambert_conic_conformal_2sp_michigan
#define proj_create_conversion_lambert_cylindrical_equal_area internal_proj_create_conversion_lambert_cylindrical_equal_area
#define proj_create_conversion_lambert_cylindrical_equal_area_spherical internal_proj_create_conversion_lambert_cylindrical_equal_area_spherical
#define proj_create_conversion_mercator_variant_a internal_proj_create_conversion_mercator_variant_a
#define proj_create_conversion_mercator_variant_b internal_proj_create_conversion_mercator_variant_b
#define proj_create_conversion_miller_cylindrical internal_proj_create_conversion_miller_cylindrical
#define proj_create_conversion_mollweide internal_proj_create_conversion_mollweide
#define proj_create_conversion_new_zealand_mapping_grid internal_proj_create_conversion_new_zealand_mapping_grid
#define proj_create_conversion_oblique_stereographic internal_proj_create_conversion_oblique_stereographic
#define proj_create_conversion_orthographic internal_proj_create_conversion_orthographic
#define proj_create_conversion_polar_stereographic_variant_a internal_proj_create_conversion_polar_stereographic_variant_a
#define proj_create_conversion_polar_stereographic_variant_b internal_proj_create_conversion_polar_stereographic_variant_b
#define proj_create_conversion_pole_rotation_grib_convention internal_proj_create_conversion_pole_rotation_grib_convention
#define proj_create_conversion_pole_rotation_netcdf_cf_convention internal_proj_create_conversion_pole_rotation_netcdf_cf_convention
#define proj_create_conversion_popular_visualisation_pseudo_mercator internal_proj_create_conversion_popular_visualisation_pseudo_mercator
#define proj_create_conversion_quadrilateralized_spherical_cube internal_proj_create_conversion_quadrilateralized_spherical_cube
#define proj_create_conversion_robinson internal_proj_create_conversion_robinson
#define proj_create_conversion_sinusoidal internal_proj_create_conversion_sinusoidal
#define proj_create_conversion_spherical_cross_track_height internal_proj_create_conversion_spherical_cross_track_height
#define proj_create_conversion_stereographic internal_proj_create_conversion_stereographic
#define proj_create_conversion_transverse_mercator internal_proj_create_conversion_transverse_mercator
#define proj_create_conversion_transverse_mercator_south_oriented internal_proj_create_conversion_transverse_mercator_south_oriented
#define proj_create_conversion_tunisia_mapping_grid internal_proj_create_conversion_tunisia_mapping_grid
#define proj_create_conversion_two_point_equidistant internal_proj_create_conversion_two_point_equidistant
#define proj_create_conversion_utm internal_proj_create_conversion_utm
#define proj_create_conversion_van_der_grinten internal_proj_create_conversion_van_der_grinten
#define proj_create_conversion_vertical_perspective internal_proj_create_conversion_vertical_perspective
#define proj_create_conversion_wagner_i internal_proj_create_conversion_wagner_i
#define proj_create_conversion_wagner_ii internal_proj_create_conversion_wagner_ii
#define proj_create_conversion_wagner_iii internal_proj_create_conversion_wagner_iii
#define proj_create_conversion_wagner_iv internal_proj_create_conversion_wagner_iv
#define proj_create_conversion_wagner_v internal_proj_create_conversion_wagner_v
#define proj_create_conversion_wagner_vi internal_proj_create_conversion_wagner_vi
#define proj_create_conversion_wagner_vii internal_proj_create_conversion_wagner_vii
#define proj_create_crs_to_crs internal_proj_create_crs_to_crs
#define proj_create_crs_to_crs_from_pj internal_proj_create_crs_to_crs_from_pj
#define proj_create_cs internal_proj_create_cs
#define proj_create_derived_geographic_crs internal_proj_create_derived_geographic_crs
#define proj_create_ellipsoidal_2D_cs internal_proj_create_ellipsoidal_2D_cs
#define proj_create_ellipsoidal_3D_cs internal_proj_create_ellipsoidal_3D_cs
#define proj_create_engineering_crs internal_proj_create_engineering_crs
#define proj_create_from_database internal_proj_create_from_database
#define proj_create_from_name internal_proj_create_from_name
#define proj_create_from_wkt internal_proj_create_from_wkt
#define proj_create_geocentric_crs internal_proj_create_geocentric_crs
#define proj_create_geocentric_crs_from_datum internal_proj_create_geocentric_crs_from_datum
#define proj_create_geographic_crs internal_proj_create_geographic_crs
#define proj_create_geographic_crs_from_datum internal_proj_create_geographic_crs_from_datum
#define proj_create_operation_factory_context internal_proj_create_operation_factory_context
#define proj_create_operations internal_proj_create_operations
#define proj_create_projected_crs internal_proj_create_projected_crs
#define proj_create_transformation internal_proj_create_transformation
#define proj_create_vertical_crs internal_proj_create_vertical_crs
#define proj_create_vertical_crs_ex internal_proj_create_vertical_crs_ex
#define proj_crs_alter_cs_angular_unit internal_proj_crs_alter_cs_angular_unit
#define proj_crs_alter_cs_linear_unit internal_proj_crs_alter_cs_linear_unit
#define proj_crs_alter_geodetic_crs internal_proj_crs_alter_geodetic_crs
#define proj_crs_alter_parameters_linear_unit internal_proj_crs_alter_parameters_linear_unit
#define proj_crs_create_bound_crs internal_proj_crs_create_bound_crs
#define proj_crs_create_bound_crs_to_WGS84 internal_proj_crs_create_bound_crs_to_WGS84
#define proj_crs_create_bound_vertical_crs internal_proj_crs_create_bound_vertical_crs
#define proj_crs_create_projected_3D_crs_from_2D internal_proj_crs_create_projected_3D_crs_from_2D
#define proj_crs_demote_to_2D internal_proj_crs_demote_to_2D
#define proj_crs_get_coordinate_system internal_proj_crs_get_coordinate_system
#define proj_crs_get_coordoperation internal_proj_crs_get_coordoperation
#define proj_crs_get_datum internal_proj_crs_get_datum
#define proj_crs_get_datum_ensemble internal_proj_crs_get_datum_ensemble
#define proj_crs_get_datum_forced internal_proj_crs_get_datum_forced
#define proj_crs_get_geodetic_crs internal_proj_crs_get_geodetic_crs
#define proj_crs_get_horizontal_datum internal_proj_crs_get_horizontal_datum
#define proj_crs_get_sub_crs internal_proj_crs_get_sub_crs
#define proj_crs_info_list_destroy internal_proj_crs_info_list_destroy
#define proj_crs_is_derived internal_proj_crs_is_derived
#define proj_crs_promote_to_3D internal_proj_crs_promote_to_3D
#define proj_cs_get_axis_count internal_proj_cs_get_axis_count
#define proj_cs_get_axis_info internal_proj_cs_get_axis_info
#define proj_cs_get_type internal_proj_cs_get_type
#define proj_datum_ensemble_get_accuracy internal_proj_datum_ensemble_get_accuracy
#define proj_datum_ensemble_get_member internal_proj_datum_ensemble_get_member
#define proj_datum_ensemble_get_member_count internal_proj_datum_ensemble_get_member_count
#define proj_degree_input internal_proj_degree_input
#define proj_degree_output internal_proj_degree_output
#define proj_destroy internal_proj_destroy
#define proj_dmstor internal_proj_dmstor
#define proj_download_file internal_proj_download_file
#define proj_dynamic_datum_get_frame_reference_epoch internal_proj_dynamic_datum_get_frame_reference_epoch
#define proj_ellipsoid_get_parameters internal_proj_ellipsoid_get_parameters
#define proj_errno internal_proj_errno
#define proj_errno_reset internal_proj_errno_reset
#define proj_errno_restore internal_proj_errno_restore
#define proj_errno_set internal_proj_errno_set
#define proj_errno_string internal_proj_errno_string
#define proj_factors internal_proj_factors
#define proj_geod internal_proj_geod
#define proj_get_area_of_use internal_proj_get_area_of_use
#define proj_get_authorities_from_database internal_proj_get_authorities_from_database
#define proj_get_celestial_body_list_from_database internal_proj_get_celestial_body_list_from_database
#define proj_get_celestial_body_name internal_proj_get_celestial_body_name
#define proj_get_codes_from_database internal_proj_get_codes_from_database
#define proj_get_crs_info_list_from_database internal_proj_get_crs_info_list_from_database
#define proj_get_crs_list_parameters_create internal_proj_get_crs_list_parameters_create
#define proj_get_crs_list_parameters_destroy internal_proj_get_crs_list_parameters_destroy
#define proj_get_ellipsoid internal_proj_get_ellipsoid
#define proj_get_geoid_models_from_database internal_proj_get_geoid_models_from_database
#define proj_get_id_auth_name internal_proj_get_id_auth_name
#define proj_get_id_code internal_proj_get_id_code
#define proj_get_insert_statements internal_proj_get_insert_statements
#define proj_get_name internal_proj_get_name
#define proj_get_non_deprecated internal_proj_get_non_deprecated
#define proj_get_prime_meridian internal_proj_get_prime_meridian
#define proj_get_remarks internal_proj_get_remarks
#define proj_get_scope internal_proj_get_scope
#define proj_get_source_crs internal_proj_get_source_crs
#define proj_get_suggested_operation internal_proj_get_suggested_operation
#define proj_get_target_crs internal_proj_get_target_crs
#define proj_get_type internal_proj_get_type
#define proj_get_units_from_database internal_proj_get_units_from_database
#define proj_grid_cache_clear internal_proj_grid_cache_clear
#define proj_grid_cache_set_enable internal_proj_grid_cache_set_enable
#define proj_grid_cache_set_filename internal_proj_grid_cache_set_filename
#define proj_grid_cache_set_max_size internal_proj_grid_cache_set_max_size
#define proj_grid_cache_set_ttl internal_proj_grid_cache_set_ttl
#define proj_grid_get_info_from_database internal_proj_grid_get_info_from_database
#define proj_grid_info internal_proj_grid_info
#define proj_identify internal_proj_identify
#define proj_info internal_proj_info
#define proj_init_info internal_proj_init_info
#define proj_insert_object_session_create internal_proj_insert_object_session_create
#define proj_insert_object_session_destroy internal_proj_insert_object_session_destroy
#define proj_int_list_destroy internal_proj_int_list_destroy
#define proj_is_crs internal_proj_is_crs
#define proj_is_deprecated internal_proj_is_deprecated
#define proj_is_derived_crs internal_proj_is_derived_crs
#define proj_is_download_needed internal_proj_is_download_needed
#define proj_is_equivalent_to internal_proj_is_equivalent_to
#define proj_is_equivalent_to_with_ctx internal_proj_is_equivalent_to_with_ctx
#define proj_list_angular_units internal_proj_list_angular_units
#define proj_list_destroy internal_proj_list_destroy
#define proj_list_ellps internal_proj_list_ellps
#define proj_list_get internal_proj_list_get
#define proj_list_get_count internal_proj_list_get_count
#define proj_list_operations internal_proj_list_operations
#define proj_list_prime_meridians internal_proj_list_prime_meridians
#define proj_list_units internal_proj_list_units
#define proj_log_func internal_proj_log_func
#define proj_log_level internal_proj_log_level
#define proj_lp_dist internal_proj_lp_dist
#define proj_lpz_dist internal_proj_lpz_dist
#define proj_normalize_for_visualization internal_proj_normalize_for_visualization
#define proj_operation_factory_context_destroy internal_proj_operation_factory_context_destroy
#define proj_operation_factory_context_set_allow_ballpark_transformations internal_proj_operation_factory_context_set_allow_ballpark_transformations
#define proj_operation_factory_context_set_allowed_intermediate_crs internal_proj_operation_factory_context_set_allowed_intermediate_crs
#define proj_operation_factory_context_set_allow_use_intermediate_crs internal_proj_operation_factory_context_set_allow_use_intermediate_crs
#define proj_operation_factory_context_set_area_of_interest internal_proj_operation_factory_context_set_area_of_interest
#define proj_operation_factory_context_set_crs_extent_use internal_proj_operation_factory_context_set_crs_extent_use
#define proj_operation_factory_context_set_desired_accuracy internal_proj_operation_factory_context_set_desired_accuracy
#define proj_operation_factory_context_set_discard_superseded internal_proj_operation_factory_context_set_discard_superseded
#define proj_operation_factory_context_set_grid_availability_use internal_proj_operation_factory_context_set_grid_availability_use
#define proj_operation_factory_context_set_spatial_criterion internal_proj_operation_factory_context_set_spatial_criterion
#define proj_operation_factory_context_set_use_proj_alternative_grid_names internal_proj_operation_factory_context_set_use_proj_alternative_grid_names
#define proj_pj_info internal_proj_pj_info
#define proj_prime_meridian_get_parameters internal_proj_prime_meridian_get_parameters
#define proj_query_geodetic_crs_from_datum internal_proj_query_geodetic_crs_from_datum
#define proj_roundtrip internal_proj_roundtrip
#define proj_rtodms internal_proj_rtodms
#define proj_string_destroy internal_proj_string_destroy
#define proj_string_list_destroy internal_proj_string_list_destroy
#define proj_suggests_code_for internal_proj_suggests_code_for
#define proj_todeg internal_proj_todeg
#define proj_torad internal_proj_torad
#define proj_trans internal_proj_trans
#define proj_trans_array internal_proj_trans_array
#define proj_trans_generic internal_proj_trans_generic
#define proj_unit_list_destroy internal_proj_unit_list_destroy
#define proj_uom_get_info_from_database internal_proj_uom_get_info_from_database
#define proj_xy_dist internal_proj_xy_dist
#define proj_xyz_dist internal_proj_xyz_dist
#define pj_release internal_pj_release
#endif /* PROJ_SYMBOL_RENAME_H */
