cmake_minimum_required(VERSION 3.16)
project(map_sdk_tools
        VERSION 0.0.1
        LANGUAGES CXX
)
set(CMAKE_PREFIX_PATH "/usr/local/protobuf")
find_package(Protobuf REQUIRED)
include_directories(${Protobuf_INCLUDE_DIRS})
set(CMAKE_LIBRARY_PATH /usr/lib/x86_64-linux-gnu ${CMAKE_LIBRARY_PATH})
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(routing_hd_map_lib "routing_hd_map")



SET(PROJECT_ROOT_DIR ${PROJECT_SOURCE_DIR})
SET(THIRD_PARTY_DIR ${PROJECT_ROOT_DIR}/thirdparty)
add_subdirectory(proto)
file(GLOB_RECURSE PROTO_SRCS  ${PROJECT_ROOT_DIR}/proto/*.pb.cc)
file(GLOB_RECURSE PROTO_HDRS  ${PROJECT_ROOT_DIR}/proto/*.pb.h)
#add_subdirectory(src/tc_map)
#add_subdirectory(src/amap)
add_subdirectory(src/bd_map)
include(cmake/install.cmake)
