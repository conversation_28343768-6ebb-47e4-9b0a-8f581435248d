/*
 * File: sd_pro_data_manager.hpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-04-02
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#pragma once
#include "interface/hd_data_handler.h"

namespace gwm {
namespace bd_map {

using gwm::hdmap::RoutingMapInfo;
using gwm::hdmap::TrackList;
using gwm::interface::IHDDataHandler;

class SDProDataManager : public IHDDataHandler {
public:
  int GetRoutingMapInfo(const TrackList &track_list,
                        RoutingMapInfo &routing_map) override;

private:
};

}  // namespace bd_map
}  // namespace gwm
