/*
 * File: utm_to_latlon.cpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-21
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include <proj.h>

#include <iostream>
#include <string>
#pragma once

class UTMToLatLonConverter {
public:
  UTMToLatLonConverter(int zone, bool isNorth);

  ~UTMToLatLonConverter();

  std::pair<double, double> Convert(double easting, double northing);

private:
  PJ* utmCrs_ = nullptr;  // PROJ转换上下文
};
