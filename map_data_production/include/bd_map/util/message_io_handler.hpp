/*
 * File: message_io_handler.hpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-20
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#pragma once

#include <google/protobuf/message.h>  // 包含 protobuf 的核心功能

#include <fstream>
#include <iostream>
#include <string>

#include "util/log.hpp"

// 通用函数：将 protobuf 消息序列化并写入文件
template <typename MessageType>
bool WriteMessageToFile(const MessageType& message,
                        const std::string& filename) {
  // 将消息序列化为二进制数据
  std::string binary_data;
  if (!message.SerializeToString(&binary_data)) {
    LOG_ERROR << "Failed to serialize message." << std::endl;
    return false;
  }

  // 将二进制数据写入文件
  std::ofstream output_file(filename, std::ios::binary);
  if (!output_file) {
    LOG_ERROR << "Failed to open file for writing: " << filename << std::endl;
    return false;
  }
  output_file.write(binary_data.data(), binary_data.size());
  output_file.close();

  LOG_INFO << "Message has been written to " << filename << std::endl;
  return true;
}

// 通用函数：从文件读取二进制数据并反序列化为 protobuf 消息
template <typename MessageType>
bool ReadMessageFromFile(MessageType* message, const std::string& filename) {
  // 从二进制文件读取数据
  std::ifstream input_file(filename, std::ios::binary);
  if (!input_file) {
    LOG_ERROR << "Failed to open file for reading: " << filename << std::endl;
    return false;
  }

  std::string binary_data((std::istreambuf_iterator<char>(input_file)),
                          std::istreambuf_iterator<char>());
  input_file.close();

  // 反序列化为消息
  if (!message->ParseFromString(binary_data)) {
    LOG_ERROR << "Failed to parse message from binary data." << std::endl;
    return false;
  }

  LOG_INFO << "Message has been read from " << filename << std::endl;
  return true;
}
