#pragma once

#include <time.h>

#include <chrono>
#include <iomanip>
#include <sstream>
#include <string>

// 获取当前时间戳（秒）
inline std::uint64_t CurrentTimestampS() {
  return std::chrono::duration_cast<std::chrono::seconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count();
}

// 获取当前时间戳（毫秒）
inline std::uint64_t CurrentTimestampMS() {
  return std::chrono::duration_cast<std::chrono::milliseconds>(
             std::chrono::system_clock::now().time_since_epoch())
      .count();
}

// 获取当前时间（秒）
inline std::string DateTime(const std::string &format = "%Y-%m-%d %H:%M:%S") {
  auto now = std::chrono::system_clock::now();
  auto time_t_obj = std::chrono::system_clock::to_time_t(now);
  std::tm tm;
  localtime_r(&time_t_obj, &tm);

  std::ostringstream oss;
  oss << std::put_time(&tm, format.c_str());
  return oss.str();
}

// 获取当前时间（毫秒）
inline std::string DateTimeMs(const std::string &ms_separator = ".",
                              const std::string &format = "%Y-%m-%d %H:%M:%S") {
  auto now = std::chrono::system_clock::now();
  auto time_t_obj = std::chrono::system_clock::to_time_t(now);
  std::tm tm;
  localtime_r(&time_t_obj, &tm);

  std::ostringstream oss;
  oss << std::put_time(&tm, format.c_str());
  // 获取毫秒部分
  auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
                now.time_since_epoch()) %
            1000;
  oss << ms_separator << std::setw(3) << std::setfill('0')
      << ms.count();  // 添加毫秒部分

  return oss.str();
}

// 时间戳（秒）转时间（字符串）
inline std::string TimestampS2DateTime(
    std::uint64_t timestamp_s,
    const std::string &format = "%Y-%m-%d %H:%M:%S") {
  std::time_t time_t_obj = timestamp_s;
  std::tm tm;
  localtime_r(&time_t_obj, &tm);

  std::ostringstream oss;
  oss << std::put_time(&tm, format.c_str());
  return oss.str();
}

// 时间戳（毫秒）转时间（字符串）
inline std::string TimestampMS2DateTime(
    std::uint64_t timestamp_ms, const std::string &ms_separator = ".",
    const std::string &format = "%Y-%m-%d %H:%M:%S") {
  std::time_t time_t_obj = timestamp_ms / 1000;
  std::tm tm;
  localtime_r(&time_t_obj, &tm);

  std::ostringstream oss;
  oss << std::put_time(&tm, format.c_str());
  oss << ms_separator << std::setfill('0') << std::setw(3)
      << (timestamp_ms % 1000);
  return oss.str();
}

// 时间（字符串）转时间戳（秒）
inline std::uint64_t DateTime2TimestampS(
    const std::string &datetime,
    const std::string &format = "%Y-%m-%d %H:%M:%S") {
  struct tm tm;
  std::istringstream ss(datetime);
  ss >> std::get_time(&tm, format.c_str());
  if (ss.fail()) {
    throw std::invalid_argument("Invalid time format");
  }
  auto tp = std::chrono::system_clock::from_time_t(std::mktime(&tm));
  return std::chrono::duration_cast<std::chrono::seconds>(tp.time_since_epoch())
      .count();
}

// 时间（字符串）转时间戳（毫秒）
inline std::uint64_t DateTime2TimestampMS(
    const std::string &datetime, const std::string &ms_separator = ".",
    const std::string &format = "%Y-%m-%d %H:%M:%S") {
  // 时间字符串中的秒部分和毫秒部分
  std::string timeStr = datetime;
  int milliseconds = 0;

  // 查找是否有毫秒部分
  size_t dot_pos = timeStr.find(ms_separator);
  if (dot_pos != std::string::npos) {
    // 如果有，截取出毫秒部分
    std::string ms_str = timeStr.substr(dot_pos + 1);
    milliseconds = std::stoi(ms_str.substr(0, 3));  // 只取前三位，精确到毫秒
    timeStr = timeStr.substr(0, dot_pos);  // 去掉毫秒部分，保留到秒
  }

  struct tm tm;
  std::istringstream ss(datetime);
  ss >> std::get_time(&tm, format.c_str());
  if (ss.fail()) {
    throw std::invalid_argument("Invalid time format");
  }

  auto tp = std::chrono::system_clock::from_time_t(std::mktime(&tm));
  return std::chrono::duration_cast<std::chrono::milliseconds>(
             tp.time_since_epoch())
             .count() +
         milliseconds;
}
