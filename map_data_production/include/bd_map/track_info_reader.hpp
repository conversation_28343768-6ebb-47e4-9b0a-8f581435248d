/*
 * File: track_info_reader.hpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-28
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#pragma once

#include <string>
#include <vector>

#include "hd_map_lane.pb.h"
#include "rtk_gps.pb.h"
#include "util/csv.h"

namespace gwm {
namespace common {

using gwm::hdmap::GNSSCollection;
using gwm::hdmap::TrackList;

struct GPSRecord {
  int64_t timestamp;
  double longitude;
  double latitude;
  double altitude;
  int status;
};

struct PoseRecord {
  int64_t timestamp;
  double yaw;
};

class TrackInfoReader {
public:
  TrackInfoReader() {}
  TrackList ConvertTrackList(const std::string &global_gt_file_path,
                             const std::string &track_file_path,bool is_wgs84);

  GNSSCollection ConvertGNSSInfo(const std::string &track_file_path);

private:
  int LoadTrackFile(const std::string &track_file_path,
                    std::vector<GPSRecord> &gps_records,bool is_wgs84);

  int LoadGlobalGTFile(const std::string &global_gt_file_path,
                       std::vector<PoseRecord> &pose_records);
};

}  // namespace common
}  // namespace gwm
