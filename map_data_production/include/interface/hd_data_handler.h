/*
 * File: hd_data_handler.h
 * Description:
 * Author: qingxiansun
 * Date: 2025-04-02
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */
#pragma once

#include "proto/hd_map_lane.pb.h"

namespace gwm {
namespace interface {

using gwm::hdmap::RoutingMapInfo;
using gwm::hdmap::TrackList;

class IHDDataHandler {
public:
  virtual int GetRoutingMapInfo(const gwm::hdmap::TrackList &track_list,
                                gwm::hdmap::RoutingMapInfo &routint_map) = 0;
  virtual ~IHDDataHandler() = default;
};

}  // namespace interface
}  // namespace gwm
