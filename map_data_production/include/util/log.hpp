/*
 * File: log.hpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-06
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#pragma once

#include <string.h>

#define LOG_HEAD \
  __FUNCTION__ << "@" << strrchr(__FILE__, '/') + 1 << ":" << __LINE__ << " | "
#define LOG_CRITICAL std::cout << LOG_HEAD
#define LOG_ERROR std::cout << LOG_HEAD
#define LOG_WARN std::cout << LOG_HEAD
#define LOG_INFO std::cout << LOG_HEAD
#define LOG_DEBUG std::cout << LOG_HEAD
#define LOG_TRACE std::cout << LOG_HEAD

#define ENABLE_DebugCode
#ifdef ENABLE_DebugCode
#define DebugCode(msg) \
  {                    \
    do {               \
      msg;             \
    } while (0);       \
  }
#else
#define DebugCode(msg)
#endif

#define LOG(msg)                         \
  do {                                   \
    std::ostringstream oss;              \
    oss << msg;                          \
    std::cout << oss.str() << std::endl; \
  } while (0);
