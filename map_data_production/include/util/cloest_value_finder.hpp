/*
 * File: cloest_value_finder.hpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-28
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#pragma once
#include <algorithm>
#include <cmath>
#include <iostream>
#include <stdexcept>
#include <vector>

/**
 * @brief 通用最近值查找器，支持按结构体属性查找
 * @tparam Container 容器类型
 * @tparam T 结构体类型
 * @tparam KeyType 关键属性类型
 */
template <typename Container, typename T, typename KeyType>
struct ClosestObjectFinder {
  const Container& objects;          // 对象容器（需按key排序）
  KeyType (*keySelector)(const T&);  // 关键属性选择函数

  /**
   * @brief 构造函数
   * @param objs 对象容器（必须已按keySelector排序）
   * @param selector 关键属性选择函数
   */
  ClosestObjectFinder(const Container& objs, KeyType (*selector)(const T&))
      : objects(objs), keySelector(selector) {
    if (objects.empty()) {
      throw std::invalid_argument("Object container is empty!");
    }
  }

  /**
   * @brief 查找最接近的结构体对象
   * @param query 查询的关键值
   * @return 最接近的结构体对象
   */
  const T& findClosest(const KeyType& query) const {
    // 自定义比较函数
    auto comp = [this](const T& obj, const KeyType& val) {
      return keySelector(obj) < val;
    };

    auto it = std::lower_bound(objects.begin(), objects.end(), query, comp);

    // 处理边界情况
    if (it == objects.begin()) return *it;
    if (it == objects.end()) return *(it - 1);

    // 比较前后两个对象
    const T& prev = *(it - 1);
    const T& curr = *it;
    return (std::abs(keySelector(prev) - query) <
            std::abs(keySelector(curr) - query))
               ? prev
               : curr;
  }
};
