//
// Created by gw00366226 on 25-2-6.
//

#ifndef COORDINATES_H
#define COORDINATES_H
#include <proj.h>

#include <cmath>
#include <utility>

namespace gwm {

class Coordinates {
public:
  static Coordinates& getInstance();

  ~Coordinates();

  // void gcj02ToUtm50(double gcjLon, double gcjLat, double& easting, double&
  // northing); void wgs84ToUtm50(double lon, double lat, double& easting,
  // double& northing); void utm50ToWgs84(double easting, double northing,
  // double& lon, double& lat);
  void mercatorToLatLon(double x, double y, double& lat, double& lon);

  bool outOfChina(double lat, double lon);

  double transformLat(double x, double y);

  double transformLon(double x, double y);

  void gcj02ToWgs84(double gcjLat, double gcjLon, double& wgsLat,
                    double& wgsLon);

  std::pair<double, double> Wgs84ToGcj02(double lng, double lat);

  void wgs84ToUtm(double lat, double lon, double& utmEasting,
                  double& utmNorthing, int& utmZone);

  void gcj02ToUtm(double gcjLat, double gcjLon, double& utmEasting,
                  double& utmNorthing, int& utmZone);

  void mercatorToUtm(double x, double y, double& utmEasting,
                     double& utmNorthing, int& utmZone);

  // 计算 UTM 带号
  int calculateUTMZone(double lon);

  // 墨卡托转 UTM 坐标的函数
  void mercatorToUTM(double x, double y, double& easting, double& northing,
                     int& zone, bool& is_northern);

  // 经纬度转 UTM 坐标的函数
  void latlonToUTM(double lat, double lon, double& easting, double& northing,
                   int& zone, bool& is_northern);

  // 将角度转换为弧度
  double toRadians(double degree);
  // 将弧度转换为角度
  double toDegrees(double radians);
  // 计算两个经纬度之间的方位角
  double calculateBearing(double lat1, double lon1, double lat2, double lon2);

  bool UTMToWGS84(double utm_easting, double utm_northing, int zone,
                  bool is_northern, double& lat, double& lon);

  const double pi = 3.14159265358979324;

  // Krasovsky 1940 ellipsoid parameters
  const double a = 6378245.0;
  const double ee = 0.00669342162296594323;
  const double k0 = 0.9996;
  const double a1 = 6378137.0;           // WGS-84 ellipsoid
  const double eccSquared = 0.00669438;  // WGS-84 eccentricity squared
  const double LNG_LAT_MOD = 1e7;
  const double RAD_TO_DEG = 57.29577951308232;

private:
  Coordinates();
};

}  // namespace gwm

#endif  // COORDINATES_H
