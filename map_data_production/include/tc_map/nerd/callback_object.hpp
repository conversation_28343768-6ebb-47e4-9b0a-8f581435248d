/*
 * File: callback_object.hpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-12
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#pragma once

#include <future>

#include "nerd-api/api/map_data_engine_api.h"
#include "nerd-api/api/nerd_utils.h"

namespace tc_map {
namespace nerd {
class TileCallback : public ::nerd::api::MapDataResultCallback {
public:
  TileCallback() : f_(p_.get_future()) {}

  void OnResult(
      const ::nerd::api::RetMessage &ret, ::nerd::api::JobId job_id,
      std::unique_ptr<::nerd::api::GetMapDataBatchParam> req,
      std::unique_ptr<::nerd::api::GetMapDataBatchResult> rsp) override;
  ::nerd::api::GetMapDataBatchResult *GetResult() const { return resp_.get(); }

  void OnCanceled(::nerd::api::JobId job_id, bool success,
                  const ::nerd::api::RetMessage &ret) override {
    p_.set_value();
  }

  void Wait() { f_.wait(); }

private:
  // 仅示例用, 可以采取更合适的同步方式
  std::promise<void> p_;
  std::future<void> f_;
  std::unique_ptr<::nerd::api::GetMapDataBatchResult> resp_;
};

class RectCallback : public ::nerd::api::MapDataByRectResultCallback {
public:
  RectCallback() : f_(p_.get_future()) {}

  void OnResult(
      const ::nerd::api::RetMessage &ret, ::nerd::api::JobId job_id,
      std::unique_ptr<::nerd::api::GetMapDataByRectParam> req,
      std::unique_ptr<::nerd::api::GetMapDataByRectResult> rsp) override;
  ::nerd::api::GetMapDataByRectResult *GetResult() const { return resp_.get(); }

  void OnCanceled(::nerd::api::JobId job_id, bool success,
                  const ::nerd::api::RetMessage &ret) override {
    p_.set_value();
  }

  void Wait() { f_.wait(); }

private:
  // 仅示例用, 可以采取更合适的同步方式
  std::promise<void> p_;
  std::future<void> f_;
  std::unique_ptr<::nerd::api::GetMapDataByRectResult> resp_;
};

}  // namespace nerd
}  // namespace tc_map
