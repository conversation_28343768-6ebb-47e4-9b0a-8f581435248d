#pragma once

#include <cstdint>
#include <iostream>
#include <string>

#include "api/map_data_engine_api.h"
#include "api/nerd_utils.h"
#include "dto/iface_lane.h"
#include "nerd_api/nerd_version.h"
#include "proto/hd_map_lane.pb.h"
#include "proto/sd_map.pb.h"

namespace tc_map {
namespace nerd {

using ::nerd::api::RoadClassType;
using ::nerd::api::RoadKindTypeVec;
class TypeAdapter {
public:
  /* // 腾讯道路等级
   * ---|---
   * 00 |高速道路
   * 01 |城市高速路
   * 02 |国道
   * 03 |省道
   * 04 |县道
   * 06 |乡镇村道
   * 08 |其它道路
   * 09 |非引导道路
   * 0a |轮渡
   * 0b |步行道路
   * 0c |人渡
   * 0d |自行车专用道
   * 0e |索道
   */

  /* ROADCLASS_FREEWAY = 0;  // 高速公路
   * ROADCLASS_NATIONAL_ROAD = 1;  // 国道
   * ROADCLASS_PROVINCE_ROAD = 2;  // 省道
   * ROADCLASS_COUNTY_ROAD = 3; // 县道
   * ROADCLASS_RURAL_ROAD = 4;  // 乡公路
   * ROADCLASS_IN_COUNTY_ROAD = 5; // 县乡村内部道路
   * ROADCLASS_CITY_SPEED_WAY = 6; // 主要大街\城市快速道
   * ROADCLASS_MAIN_ROAD = 7;  // 主要道路
   * ROADCLASS_SECONDARY_ROAD = 8; // 次要道路
   * ROADCLASS_COMMON_ROAD = 9; // 普通道路
   * ROADCLASS_NON_NAVI_ROAD = 10; // 非导航道路
   * ROADCLASS_INVALID = 0xFF;  // 道路等级无效值 */
  static gwm::sdmap::Road_RoadType ConvertRoadTypeFromTencent(
      ::nerd::api::RoadClassType road_type);

  static int32_t ConvertRoadTypeToTencent(gwm::sdmap::Road_RoadType road_class);

  /* sd map info
   * FORMWAY_UNKNOWN = 0;
   * FORMWAY_DIVISED_LINK = 1;  // 上下线分离 主路
   * FORMWAY_CROSS_LINK = 2;  // 交叉点内 复杂节点内部道路
   * FORMWAY_JCT = 3; //< JCT
   * FORMWAY_ROUND_CIRCLE = 4; // 环岛
   * FORMWAY_SERVICE_ROAD = 5;  // 服务区 辅助道路
   * FORMWAY_SLIP_ROAD = 6;  // 引路 匝道
   * FORMWAY_SIDE_ROAD = 7;  // 辅路
   * FORMWAY_SLIP_JCT = 8;  // 引路 JCT
   * FORMWAY_EXIT_LINK = 9;  // 出口
   * FORMWAY_ENTRANCE_LINK = 10;  // 入口
   * FORMWAY_TURN_RIGHT_LINEA = 11;  // 右转专用道
   * FORMWAY_TURN_RIGHT_LINEB = 12;  // 右转专用道
   * FORMWAY_TURN_LEFT_LINEA = 13;  // 左转专用道
   * FORMWAY_TURN_LEFT_LINEB = 14;  // 左转专用道
   * FORMWAY_COMMON_LINK = 15;  // 普通道路
   * FORMWAY_TURN_LEFTRIGHT_LINE = 16;  // 左右转专用道
   * FORMWAY_NONMOTORIZED_DRIVEWAY = 17;  // ADF+ 专用
   * FORMWAY_FRONTDOOR_ROAD = 18;  // 门前道路
   * FORMWAY_SERVICE_SLIP_ROAD = 19;  // 服务区 + 引路
   * FORMWAY_SERVICE_JCT = 20;  // 服务区 + JCT
   * FORMWAY_SERVICE_JCT_SLIP_ROAD = 21;  // 服务区 + 引路 + JCT
   * FORMWAY_NON_VEHICLE = 22;  // 非机动车道路
   * FORMWAY_INVALID = 0xFF;  // 道路构成无效值 */

  /* tencent form way
   * kNone = 0x00; // 无属性
   * kRoundabout = 0x01; //环岛
   * kDirectionSpit = 0x02; // 上下线分离
   * kJCT = 0x03; // JCT,连接高速道路
   * kCrossInner = 0x04; // 交叉点内Link
   * kIC = 0x05; // IC,连接高速和其它不同等级道路
   * kPA = 0x06; // 停车区
   * kSA = 0x07; // 服务区
   * kBridge = 0x08; // 固定桥
   * kWalkStreet = 0x09; // 步行街
   * kSecondary = 0x0a; // 辅路
   * kRamp = 0x0b; // 匝道
   * kEnclosure = 0x0c; // 全封闭道路
   * kUndefined = 0x0d; // 未定义交通区域
   * kPoiConnect = 0x0e; // POI连接路
   * kTunnel = 0x0f; // 隧道
   * kBus = 0x11; // 公交专用道
   * kRightTurn = 0x12; // 提前右转
   * kView = 0x13; // 风景路线
   * kInnerRegion = 0x14; // 区域内道路
   * kLeftTurn = 0x15; // 提前左转
   * kUTurn = 0x16; // 调头口
   * kMainSecondaryEntrance = 0x17; // 主辅路出入口
   * kParkEntrance = 0x1a; // 停车场出入口连接路
   * kMovableBridge = 0x1b; // 移动式桥
   * kReversalLeft = 0x1e; // 借道左转
   * kMainRoad = 0x1f; // 主路
   * kFrontDoor = 0x20; // 门前路
   * kElevated = 0x21; // 高架路
   * kTruckLane = 0x22; // 货车专用道
   * kNORMAL = 0x23; // 普通
   * kTOLL = 0x24; // 收费站
   * kCONSTRUCTION = 0x25; // 建设中道路
   * kINTERSECTION = 0x26; // 十字路口
   * kHIGHWAY_PORT = 0x27; // 高速出入口
   * kHIGHWAY_CONNECTION = 0x28; // 高速连接路
   * kNONSTANDARD_ROUNDABOUT = 0x29; // 非标准环岛
   * kSPECIAL_CONNECTION = 0x2a; // 特殊连接路
   * kPARKING_OCCUPY_ROAD = 0x2b; // 包含占道停车场
   * kOWNERSHIP = 0x2c; // 私道
   * kINNER_VIRTUAL_CONNECT = 0x2d; // 区域内虚拟连接路
   * kTAXI = 0x2e; // 区域内虚拟连接路
   * kTIDE = 0x2f; // 潮汐车道
   * kSTEP_ROAD = 0x30; // 台阶路
   * kINNER_CROSS_ROAD = 0x31; // 路口面道路
   * kENTRANCE_AND_EXIT_CONNECT = 0x32; // 出入口连接路
   * kAHEAD_TURN_RIGHT = 0x33; // 提前右转(高精)
   * kCROSS_LINE_OVERPASS = 0x34; // 跨线天桥
   * kSUNKEN_ROAD = 0x35; // 下沉道路
   * kRAMP_BOTH_PASS = 0x36; // 匝道双通
   * kMOUNTAIN_ROAD = 0x37; // 山路
   * kSUNKEN_ROAD_PORT = 0x38; // 下沉道路出入口
   * kOTHER = 0x39; // 其他
   * kVIRTUAL_SOLID_CONNECTION = 0x3a; // 虚实线链接路
   * kPARKING_INTERNAL_ROAD = 0x3b; // 停车场内部道路
   */

  static gwm::sdmap::Road_FormOfWay ConvertRoadFormwayFromTencent(
      ::nerd::api::RoadKindType kind_type);

  static int32_t ConvertRoadFormwayToTencent(
      gwm::sdmap::Road_FormOfWay formway);

  /* tencent hd lane type
  enum class LaneType : uint8_t {
    kNONE,                     // 无
    kREGULAR,                  // 普通车道
    kCOMPOUND,                 // 加减速复合车道
    kLEFT_ACCELERATION,        // 左侧加速车道
    kLEFT_DECELERATION,        // 左侧减速车道
    kHOV,                      // HOV
    kSLOW,                     // 慢车道
    kSHOULDER,                 // 路肩
    kDRIVABLE_SHOULDER,        // 可行驶路肩
    kCONTROL,                  // 管制车道
    kEMERGENCY_PARKING_STRIP,  // 紧急停车带
    kBUS,                      // 公交车道
    kBICYCLE,                  // 非机动车道
    kTIDE,                     // 潮汐车道
    kDIRECTION_CHANGE,         // 可变车道
    kPARKING_ROAD,             // 停车车道
    kDRIVABLE_PARKING_ROAD,    // 可行驶停车道
    kTRUCK,                    // 货车专用道
    kTIME_LIMIT_BUS,           // 限时公交车道
    kPASSING_BAY,              // 错车道
    kREVERSAL_LEFT_TURN,       // 借道左转车道
    kTAXI,                     // 出租车车道
    kTURN_WAITING,             // 转弯待转区车道
    kDIRECTION_LOCKED,         // 定向车道
    kVIRTUAL,                  // 路口车道
    kVEHICLE_BICYCLE_MIX,      // 机非混合车道
    kMOTOR,                    // 摩托车道
    kU_TURN,                   // 掉头车道
    kTOLL,                     // 收费站车道
    kCHECK_POINT,              // 检查站车道
    kDANGEROUS_ARTICLE,        // 危险品专用车道
    kFORBIDDEN_DRIVE,          // 非行驶区域
    kTHOUGH_LANE_ZONE,         // 借道区
    kSTREET_RAILWAY,           // 有轨电车车道
    kBUS_BAY,                  // 公交港湾车道
    kSPECIAL_CAR,              // 特殊车辆专用车道
    kPEDESTRIANS,              // 人行道
    kETC,                      // ETC车道
    kEMERGENCY,                // 应急车道
    kSTRAIGHT_WAITING,         // 直行待行区车道
    kSTRAIGHT_VIRTUAL,         // 直行路口车道
    kLEFT_VIRTUAL,             // 左转路口车道
    kRIGHT_VIRTUAL,            // 右转路口车道
    kU_TURN_VIRTUAL,           // 掉头路口车道
    kHEDGING,                  // 避险车道
    kEMPTY,                    // 空车道
    kOTHER,                    // 其他车道
    kRIGHT_ACCELERATION,       // 右侧加速车道
    kRIGHT_DECELERATION,       // 右侧减速车道
    kNON_LANE,                 // 非车道
    kSLOPE,                    // 爬跑车道
    kREVERSE_NON_VEHICLE,      // 逆向非机动车道
    kEDGE                      // 边缘车道
  };
  */
  /* gwm lane type
  enum LaneType {
    UNKNOWN = 0;  // 未知类型
    HIGHWAY = 1;  // 高速公路（无行人和自行车）
    STREET = 2;  // 城市街道（有行人和自行车）
    BIDIRECTIONAL = 3;  // 双向左转车道
    SHOULDER = 4;  // 路肩
    BIKING = 5;  // 自行车道
    SIDEWALK = 6;  // 人行道
    RESTRICTED = 7;  // 限制车道
    PARKING = 8;  // 停车道
    ROADWORK = 9;  // 施工车道
    OFFRAMP = 10;  // 下匝道
    ONRAMP = 11;  // 上匝道
    BUSLANE = 12;  // 公交车道
    LEFTTURNWAITINGAREA = 13;  // 左转等待区
    PARK = 14;  // 园区车道
    ROUNDABOUT = 15;  // 环岛车道
    RIGHT_TURN_ONLY = 16;  // 只允许右转车道
    PARK_ON_LANE = 17;  // 停车车道
    DYNAMIC_LANE = 18;  // 可变车道
    WIDE_LANE = 19;  // 超宽车道
    TIDAL_LANE = 20;  // 潮汐车道
    TRANSFER_LANE = 21;  // 中转车道
  }
  */
  static gwm::hdmap::Lane_LaneType ConvertHDMapLaneTypeFromTencent(
      RoadClassType road_class, RoadKindTypeVec &road_kind_types,
      ::nerd::api::LaneType lane_type);

private:
};
}  // namespace nerd
}  // namespace tc_map
