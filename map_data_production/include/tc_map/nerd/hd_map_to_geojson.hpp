/*
 * File: hd_map_to_geojson.hpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-20
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#pragma once

#include <cmath>
#include <fstream>
#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include "proto/common/geometry.pb.h"
#include "proto/hd_map_lane.pb.h"
#include "util/json.hpp"
#include "util/utm_to_latlon.hpp"

using json = nlohmann::json;

namespace tc_map {
namespace nerd {
class HDMapToGeoJsonHandler {
public:
  HDMapToGeoJsonHandler(int zone = 50, bool isNorth = true)
      : utm_converter_(std::make_unique<UTMToLatLonConverter>(zone, isNorth)) {}
  json Point3DToGeoJSON(const gwm::common::Point3D& point, bool is_utm = true);

  json PolylineToGeoJSON(const gwm::common::Polyline& polyline);

  json LaneBoundaryToGeoJSON(const gwm::hdmap::LaneBoundary& boundary);

  json LaneToGeoJSON(const gwm::hdmap::Lane& lane);

  json LocationInfoToGeoJSON(const gwm::hdmap::LocationInfo& location);

  json RoutingMapInfoToGeoJSON(const gwm::hdmap::RoutingMapInfo& routing_map);

  json TrackListToGeoJSON(const gwm::hdmap::TrackList& track_list);

  int OutputToFile(const json& geojson, const std::string& file_path);

private:
  std::unique_ptr<UTMToLatLonConverter> utm_converter_;
};

}  // namespace nerd
}  // namespace tc_map
