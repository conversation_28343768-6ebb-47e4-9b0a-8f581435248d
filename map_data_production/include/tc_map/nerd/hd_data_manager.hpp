// Copyright 2023 Tencent Inc. All rights reserved.

#pragma once
#include <cstdint>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>

#include "cloud-atlas/tencent_cloud_atlas_typedef.h"
#include "interface/hd_data_handler.h"
#include "nerd-api/api/map_data_engine_api.h"
#include "nerd-api/api/nerd_utils.h"
#include "nerd_api/nerd_version.h"
#include "proto/hd_map_lane.pb.h"
#include "proto/sd_map.pb.h"
#include "tc_map/nerd/callback_object.hpp"

namespace tc_map {
namespace nerd {

using gwm::hdmap::Lane;
using gwm::hdmap::LaneGroup;
using gwm::hdmap::LocationInfo;
using gwm::hdmap::RoutingLaneInfo;
using gwm::hdmap::RoutingMapInfo;
using gwm::hdmap::TrackList;
using gwm::interface::IHDDataHandler;
using ::nerd::api::GeoCoordinate;
using ::nerd::api::IBoundaryConstPtr;
using ::nerd::api::ILaneConstPtr;
using ::nerd::api::RoadClassType;
using ::nerd::api::RoadKindTypeVec;

typedef std::unordered_map<const LocationInfo *, std::shared_ptr<RectCallback>>
    DataSources;

class HDDataManager : public IHDDataHandler {
public:
  HDDataManager();

  ~HDDataManager();

  int InitSDK(const std::string &data_dir, const std::string &log_dir,
              long log_expiration_hours, long max_log_store_bytes,
              long max_signle_log_file_bytes);

  int GetRoutingMapInfo(const TrackList &track_list,
                        RoutingMapInfo &routing_map) override;

private:
  std::set<::nerd::api::TileIDType> get_tile_ids(std::string txt_file);

  void getHDMapElement(::nerd::api::IHDLaneLayer *hdLayer);

  void getHDMapDataByRect(const ::nerd::api::GeoCoordinate &center_point,
                          int radius = 500);

  void getHDMapDataByTileID(::nerd::api::TileIDType tile_id);

  void getHDMapData(const ::nerd::api::GeoCoordinate &center_point, int radius,
                    ::nerd::api::TileIDType tileId);

  void getAroundTiles(::nerd::api::TileIDType id);

  int LoadDemo();

  int SetHDLaneBoundary(IBoundaryConstPtr tc_lane_boundary,
                        ::gwm::hdmap::LaneBoundary *gwm_boundary);

  double GetSpeedBySpeedType(::nerd::api::SpeedType speed_type);

  int SetHDLane(ILaneConstPtr tc_lane, RoadClassType road_class,
                RoadKindTypeVec &road_kind_types, Lane &lane);

  int GetDataSource(const LocationInfo &loc, DataSources &data_sources);

  int AddGWMLaneGroup(
      ::nerd::api::ILaneGroupConstPtr tc_lane_group,
      RoutingMapInfo &routing_map,
      std::unordered_map<uint32_t, uint32_t> &lane_id_to_indexes,
      gwm::hdmap::LaneGroup *gwm_lane_group, uint32_t &global_lane_id_index);

private:
  std::unique_ptr<::nerd::api::MapDataEngineApi> lane_api_;
};
}  // namespace nerd
}  // namespace tc_map
