// Copyright 2023 Tencent Inc. All rights reserved.

#pragma once
#include <cstdint>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>

#include "cloud-atlas/tencent_cloud_atlas_typedef.h"
#include "nerd-api/api/map_data_engine_api.h"
#include "nerd-api/api/nerd_utils.h"
#include "nerd_api/nerd_version.h"
#include "proto/sd_map.pb.h"
#include "tc_map/nerd/callback_object.hpp"
//#include "proto/amap_navi_interface.pb.h"

namespace tc_map {
namespace nerd {

class HADataManager {
public:
  HADataManager();

  ~HADataManager();

  int InitSDK(const std::string &data_dir, const std::string &log_dir,
              long log_expiration_hours, long max_log_store_bytes,
              long max_signle_log_file_bytes);

  std::set<::nerd::api::TileIDType> get_tile_ids(std::string txt_file);

  void getSDMapElement(::nerd::api::ISDLinkLayer *sdLinkLayer);

  void getHDAirData(::nerd::api::ISDLinkLayer *sdLinkLayer);

  void getHDMapElement(::nerd::api::IHDLaneLayer *hdLayer);

  void getSDMapAndHDAirDataByRect(
      const ::nerd::api::GeoCoordinate &center_point, int radius = 500);

  void getSDMapAndHDAirDataByTileID(::nerd::api::TileIDType tileId);

  void getHDMapDataByRect(const ::nerd::api::GeoCoordinate &center_point,
                          int radius = 500);

  void getHDMapDataByTileID(::nerd::api::TileIDType tile_id);

  void getSDMapAndHDAirData(const ::nerd::api::GeoCoordinate &point, int radius,
                            ::nerd::api::TileIDType tileId);

  void getHDMapData(const ::nerd::api::GeoCoordinate &center_point, int radius,
                    ::nerd::api::TileIDType tileId);

  void getAroundTiles(::nerd::api::TileIDType id);
  // 只检查文件LANE.NDS
  int LoadDemo();

  bool FillSDMapInfo(gwm::sdmap::SDMap &map_info);

  int ConvertSDMapElementToMapInfo(::nerd::api::ISDLinkLayer *sdLinkLayer,
                                   gwm::sdmap::SDMap &map_info);

  int MatchRoadLinksToSDMap(const std::vector<tca::TxLink> &matched_links,
                            gwm::sdmap::SDMap &map_info);

  int AddSDMapRoadFromTxLink(const std::vector<tca::TxLink> &matched_links,
                             gwm::sdmap::SDMap &map_info);

  int AddSDMapRoadFromLink(const ::nerd::api::ILinkConstPtr link,
                           gwm::sdmap::SDMap &map_info);

private:
  std::unique_ptr<::nerd::api::MapDataEngineApi> lane_api_;
  std::unique_ptr<::nerd::api::MapDataEngineApi> route_api_;
};
}  // namespace nerd
}  // namespace tc_map
