#pragma once

#include <unistd.h>

#include <cmath>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <string>

#include "api/map_data_engine_api.h"
#include "cloud-atlas/tencent_cloud_atlas_listener.h"
#include "cloud-atlas/tencent_cloud_atlas_provider.h"
#include "proto/sd_map.pb.h"
#include "tc_map/route_match/http_interface_impl.h"
#include "util/json.hpp"
namespace tc_map {
namespace route_match {

class RouteMatchClient {
public:
  int Init(const std::string& json_config_file_path =
               "../../conf/tencent_cloud_atlas_conf.json");

  int TestDemo(const std::string& gnss_file_path);

  int GetMatchedMapRoads(const gwm::sdmap::SDMap& source_roads,
                         std::vector<tca::TxLink>& matched_links);

private:
  // 计算两点之间的距离
  double calculateDistance(const tca::Point& p1, const tca::Point& p2);

  // 计算路线长度
  uint32_t calculatePathLength(const std::vector<tca::Point>& points);

  // 将route_points转换为Point结构体的向量
  std::vector<tca::Point> convertRoutePointsToPoints(
      const std::vector<double>& route_points_vec);

  // 读取轨迹文件
  std::vector<double> routePoint(const std::string& input_json);

  // 计算两点和正北的夹角
  float calculateBearing(double lat1, double lon1, double lat2, double lon2);

private:
  std::unique_ptr<tca::TencentCloudAtlasProvider>
      tencent_cloud_atlas_provider_ = nullptr;
  std::vector<tca::Point> output_heterogeneous_match_points_;
  // 异源匹配输入文件,据此生成轨迹文件
  std::string kGnssTrackFile_ = "./gps_track/Beijing_HNP_001.json";
  std::vector<tca::Point> tc_points_;
  std::vector<tca::OriginalLink> tc_links_;
};

class MyMatchingListener : public tca::HeterogeneousMatchingListener {
public:
  MyMatchingListener() : f_(p_.get_future()) {}
  void OnHeterogeneousMatching(const tca::MMGuideResponse& result) override;

  void Wait() { f_.wait(); }
  const tca::MMGuideResponse& GetResult() const { return *resp_; }

private:
  std::promise<void> p_;
  std::future<void> f_;
  std::unique_ptr<tca::MMGuideResponse> resp_;
};

}  // namespace route_match
}  // namespace tc_map
