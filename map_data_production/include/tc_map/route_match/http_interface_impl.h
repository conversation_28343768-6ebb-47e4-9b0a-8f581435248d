// Copyright 2020 Tencent. All rights reserved.

#ifndef TENCENTCLOUDATLASSDK_EXAMPLE_UTIL_HTTP_INTERFACE_IMPL_H_
#define TENCENTCLOUDATLASSDK_EXAMPLE_UTIL_HTTP_INTERFACE_IMPL_H_

#include <curl/curl.h>

#include <set>

#include "net/http_interface.h"
#include "util/ThreadPool.h"

namespace tc_map {
namespace route_match {

class ResponseData {
public:
  explicit ResponseData(int32_t id) : req_id(id) {}

  bool Append(char *buffer, size_t real_size) {
    data_.insert(data_.end(), buffer, buffer + real_size);
    return true;
  }

  const char *GetData() const { return data_.data(); }

  size_t GetSize() const { return data_.size(); }

  int32_t GetReqId() const { return req_id; }

private:
  std::vector<char> data_;
  int32_t req_id;
};

class DownloadCallbackParam {
public:
  CURL *curl;
  int32_t req_id;
  std::weak_ptr<mapbase::DownloadCallback> callback;
};
/**
 * 参考样例
 */
class HttpImpl : public mapbase::HttpInterface {
public:
  HttpImpl(bool conn_share = false);
  ~HttpImpl() override;

public:
  bool RequestHttpGet(int32_t req_id, const std::string &url,
                      const std::map<std::string, std::string> &headers,
                      std::weak_ptr<mapbase::HttpCallback> callback,
                      int32_t timeout_ms) override;

  bool RequestHttpPost(int32_t req_id, const std::string &url,
                       const std::map<std::string, std::string> &headers,
                       std::unique_ptr<int8_t[]> data, int32_t size,
                       std::weak_ptr<mapbase::HttpCallback> callback,
                       int32_t timeout_ms) override;

  bool Download(int32_t req_id, const std::string &url,
                const std::map<std::string, std::string> &headers,
                std::weak_ptr<mapbase::DownloadCallback> callback,
                const std::string &file_path, int32_t timeout_ms) override;

  bool CancelRequest(int32_t req_id) override;
  bool RequestHttpUpload(int32_t reqId, const std::string &url,
                         const std::map<std::string, std::string> &headers,
                         const std::string &file_path, uint64_t file_size,
                         std::weak_ptr<mapbase::HttpCallback> callback,
                         int32_t timeout_ms) override;

private:
  void DoHttpRequest(int32_t req_id, const std::string &url,
                     const std::map<std::string, std::string> &headers,
                     std::unique_ptr<int8_t[]> data, int32_t size,
                     const std::weak_ptr<mapbase::HttpCallback> &callback,
                     int32_t timeout_ms);

  void DoDownload(int32_t req_id, const std::string &url,
                  const std::map<std::string, std::string> &headers,
                  const std::weak_ptr<mapbase::DownloadCallback> &callback,
                  const std::string &file_path, int32_t timeout_ms,
                  bool conn_share);

  /**
   *	收到头数据时的处理，对buffer追加数据
   *	@param	[]
   *	@return >=0 标识读到的数据大小，<0:错误号
   *	@notes
   */
  size_t OnReceiveHeader(char *buffer, size_t size, size_t nitems,
                         void *userdata);

  /**
   *	收到数据时的处理，对buffer追加数据
   *	@param	[in]  pHttpSocket 传入的指定HttpSocket
   *	@return >=0 标识读到的数据大小，<0:错误号
   */
  size_t OnReceiveData(char *ptr, size_t size, size_t nmemb, void *userdata);

  size_t OnDownloadProgress(char *ptr, size_t size, size_t nmemb,
                            void *userdata);

  int ProgressCallback(void *clientp, curl_off_t dltotal, curl_off_t dlnow,
                       curl_off_t ultotal, curl_off_t ulnow);

  // libcurl 静态方法回调包装
  static size_t OnReceiveHeaderStatic(char *buffer, size_t size, size_t nitems,
                                      void *userdata);
  static size_t OnReceiveDataStatic(char *ptr, size_t size, size_t nmemb,
                                    void *userdata);
  static size_t OnDownloadProgressStatic(char *ptr, size_t size, size_t nmemb,
                                         void *userdata);
  static int ProgressCallbackStatic(void *clientp, curl_off_t dltotal,
                                    curl_off_t dlnow, curl_off_t ultotal,
                                    curl_off_t ulnow);

  /**
   * 初始化连接复用的curl handle
   */
  void InitConnectionShareCurl();

  /**
   * 清理连接复用的curl handle
   */
  void CleanUpConnectionShareCurl();

public:
  /**
   * 模拟弱网延迟 单位毫秒;
   */
  uint32_t weak_network_delay_ms_ = 0;

private:
  std::shared_ptr<ThreadPool> threadPoolPtr_;
  std::set<int32_t> canceled_ids_;
  std::mutex lock_;
  bool conn_share_{false};  // 是否开启connect连接复用。conn_share_ = true
                            // 时，curl handle 会在单线程中执行。
  CURL *conn_share_curl{
      nullptr};  // conn_share_ = true 时，使用共享的curl handle，单线程使用
};
}  // namespace route_match
}  // namespace tc_map

#endif  // TENCENTCLOUDATLASSDK_EXAMPLE_UTIL_HTTP_INTERFACE_IMPL_H_
