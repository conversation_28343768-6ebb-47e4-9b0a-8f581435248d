//
// Created by lqd on 23-11-27.
//

#ifndef DREAMVIEW_ZMQWRITER_H
#define DREAMVIEW_ZMQWRITER_H

#include <type_traits>
#include <mutex>
#include <memory>
#include <iostream>
#include "zmq.hpp"
#include "google/protobuf/message.h"

#define ZMQ_IPC "ipc:///tmp/zmq_ipc_ipc"
#define ZMQ_TCP "ipc:///tmp/zmq_ipc_tcp"

namespace gwm::transformer {
    class ZMQWriter {
    public:
        explicit ZMQWriter(const std::string& addr = ZMQ_TCP)
        {
            //ipc本地用bind pub
            //ipc-->tcp 用 connect pub
            _socket = zmq::socket_t(_context,zmq::socket_type::pub);
            if (addr == ZMQ_IPC)    _socket.bind(addr);
            else _socket.connect(addr);
        }

        template <typename MessageT,
                typename std::enable_if<
                        std::is_base_of<google::protobuf::Message, MessageT>::value,
                        int>::type = 0>
        void Write(const std::string&  topic,const std::shared_ptr<MessageT> &data){
            try {
                std::lock_guard<std::mutex> lock(_mutex);
                zmq::message_t _topic(topic);
                _socket.send(_topic,zmq::send_flags::sndmore);
                zmq::message_t message(data->SerializeAsString());
                _socket.send(message,zmq::send_flags::none);
            }
            catch (const zmq::error_t& e) {
                std::cerr << e.what() << std::endl;
            }
        }

        template <typename MessageT,
                typename std::enable_if<
                        std::is_same_v<std::string, MessageT>,
                        bool>::type = true>
        void Write(const std::string&  topic,const std::shared_ptr<MessageT> &data){
            try {
                std::lock_guard<std::mutex> lock(_mutex);
                zmq::message_t _topic(topic);
                _socket.send(_topic,zmq::send_flags::sndmore);
                zmq::message_t message(*data);
                _socket.send(message,zmq::send_flags::none);
            }
            catch (const zmq::error_t& e) {
                std::cerr << e.what() << std::endl;
            }
        }

        template<typename MessageT,
                typename std::enable_if<
                        std::is_same_v<std::vector<char> ,MessageT>,
                        bool>::type = true>
        void Write(const std::string&  topic,const std::shared_ptr<MessageT> &data){
            try {
                std::lock_guard<std::mutex> lock(_mutex);
                zmq::message_t _topic(topic);
                _socket.send(_topic,zmq::send_flags::sndmore);
                zmq::message_t message(data->data(),data->size());
                _socket.send(message,zmq::send_flags::none);
            }
            catch (const zmq::error_t& e) {
                std::cerr << e.what() << std::endl;
            }
        }

        template <typename MessageT,
                typename std::enable_if<
                        std::is_base_of<google::protobuf::Message, MessageT>::value,
                        int>::type = 0>
        void Write(const std::string&  topic,const MessageT &data){
            try {
                std::lock_guard<std::mutex> lock(_mutex);
                zmq::message_t _topic(topic);
                _socket.send(_topic,zmq::send_flags::sndmore);
                zmq::message_t message(data.SerializeAsString());
                _socket.send(message,zmq::send_flags::none);
            }
            catch (const zmq::error_t& e) {
                std::cerr << e.what() << std::endl;
            }
        }

        template <typename MessageT,
                typename std::enable_if<
                        std::is_same_v<std::string, MessageT>,
                        bool>::type = true>
        void Write(const std::string&  topic,const MessageT &data){
            try {
                std::lock_guard<std::mutex> lock(_mutex);
                zmq::message_t _topic(topic);
                _socket.send(_topic,zmq::send_flags::sndmore);
                zmq::message_t message(data);
                _socket.send(message,zmq::send_flags::none);
            }
            catch (const zmq::error_t& e) {
                std::cerr << e.what() << std::endl;
            }
        }

        template<typename MessageT,
                typename std::enable_if<
                        std::is_same_v<std::vector<char> ,MessageT>,
                        bool>::type = true>
        void Write(const std::string&  topic,const MessageT &data){
            try {
                std::lock_guard<std::mutex> lock(_mutex);
                zmq::message_t _topic(topic);
                _socket.send(_topic,zmq::send_flags::sndmore);
                zmq::message_t message(data.data(),data.size());
                _socket.send(message,zmq::send_flags::none);
            }
            catch (const zmq::error_t& e) {
                std::cerr << e.what() << std::endl;
            }
        }

    private:
        zmq::context_t _context;
        zmq::socket_t _socket;
        std::mutex _mutex;
    };

}
#endif //DREAMVIEW_ZMQWRITER_H
