//
// Created by lqd on 23-11-27.
//

#ifndef DREAMVIEW_ZMQREADER_H
#define DREAMVIEW_ZMQREADER_H

#include <atomic>
#include <functional>
#include <iostream>
#include <memory>
#include <mutex>
#include <thread>
#include <utility>

#include "util/log.hpp"
#include "zmq.hpp"

template <typename M0>
using CallbackFunc = std::function<void(const std::shared_ptr<M0>&)>;

#define ZMQ_IPC "ipc:///tmp/zmq_ipc_ipc"
#define ZMQ_TCP "ipc:///tmp/zmq_ipc_tcp"

namespace gwm::transformer {

template <typename MessageT>
class ZMQReader {
public:
  ZMQReader(std::string topic, const CallbackFunc<MessageT>& callbackFunc,
            const std::string& addr);
  ZMQReader(std::string topic, const CallbackFunc<MessageT>& callbackFunc);

  std::shared_ptr<MessageT> getLastMessage() {
    std::lock_guard<std::mutex> lock(_mutex);
    return _lastMessage;
  }

  void wait() { _thread.join(); }

private:
  void run();
  std::string _topic;
  CallbackFunc<MessageT> _callbackFunc;

  zmq::context_t _context;
  zmq::socket_t _socket;

  std::atomic_bool _running = false;
  std::thread _thread;
  std::shared_ptr<MessageT> _lastMessage;
  std::mutex _mutex;
};

template <typename MessageT>
void ZMQReader<MessageT>::run() {
  _lastMessage = std::make_shared<MessageT>();
  while (_running) {
    zmq::message_t topic;
    auto result = _socket.recv(topic, zmq::recv_flags::none);
    if (!topic.more()) {
      std::cerr << "no more message ,mybe some error" << std::endl;
    }
    zmq::message_t content;
    result = _socket.recv(content, zmq::recv_flags::none);
    auto message = std::make_shared<MessageT>();
    message->ParseFromArray(content.data(), content.size());
    {
      std::lock_guard<std::mutex> lock(_mutex);
      _lastMessage = message;
    }

    if (_callbackFunc) {
      _callbackFunc(message);
    }
  }
}

template <typename MessageT>
ZMQReader<MessageT>::ZMQReader(std::string topic,
                               const CallbackFunc<MessageT>& callbackFunc,
                               const std::string& addr)
    : _topic(std::move(topic)), _callbackFunc(callbackFunc) {
  _socket = zmq::socket_t(_context, zmq::socket_type::sub);
#if ZMQ_VERSION > ZMQ_MAKE_VERSION(4, 3, 2)
  _socket.set(zmq::sockopt::subscribe, _topic.c_str());
#else
  _socket.setsockopt(ZMQ_SUBSCRIBE, _topic.c_str(), _topic.size());
#endif
  _socket.connect(addr);

  _running = true;
  _thread = std::thread(&ZMQReader::run, this);
}

template <typename MessageT>
ZMQReader<MessageT>::ZMQReader(std::string topic,
                               const CallbackFunc<MessageT>& callbackFunc)
    : ZMQReader(std::move(topic), callbackFunc, ZMQ_TCP) {}
}  // namespace gwm::transformer

#endif  // DREAMVIEW_ZMQREADER_H
