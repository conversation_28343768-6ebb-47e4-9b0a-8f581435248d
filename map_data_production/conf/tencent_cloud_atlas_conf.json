{"name": "tca", "version": "1.0.2.390", "env_settings": {"_comment_test_env": "是否测试环境,Default:false", "test_env": false, "_comment_channel": "渠道号", "channel": "30008513401", "_comment_device_id": "设备唯一标识,备标识长度应该在小于等于32位", "device_id": "1234matchgwm", "_comment_https_timeout": "https请求超时时间,单位毫秒,Default:10000", "https_timeout": 10000, "_comment_retry_interval": "请求重试间隔,单位毫秒,Default:0", "retry_interval": 0, "_comment_retry_count": "请求重试次数,Default:3", "retry_count": 3, "_comment_data_root_path": "数据存放路径,包括日志,鉴权数据等,Default:./data", "data_root_path": "./data", "_comment_use_self_signed_cert": "是否使用自签名证书,Default:true", "use_self_signed_cert": false}, "log_settings": {"_comment_log_level": "0日志级别,可以为 0-verbose 1-debug 2-info 3-warning 4-error 5-fatal 6-no_log,Default:6-no_log", "tca_log_level": 0, "_comment_log_name_prefix": "日志文件名前缀,如果为空,则使用默认配置,Default:tencent_tca_,日志文件名格式: tencent_tca_20240912_202517_977.log.gz", "log_name_prefix": "tencent_tca_", "_comment_log_storage_time_limit": "日志存储时间限制,单位为天,如果小于等于0表示使用默认配置,Default:30天", "log_storage_time_limit": 30, "_comment_log_storage_total_size_limit": "日志存储大小的最大限制，单位为Byte，如果小于等于0表示使用默认配置,Default:200MB", "log_storage_total_size_limit": "200 * 1024 * 1024", "_comment_log_storage_single_size_limit": "单个日志文件大小限制，单位为Byte，如果小于等于0表示使用默认配置,Default:5MB", "log_storage_single_size_limit": "5 * 1024 * 1024"}}