#.rst:
# FindProtobuf
# ------------
#
# Locate and configure the Google Protocol Buffers library.
#
# The following variables can be set and are optional:
#
# ``PROTOBUF_SRC_ROOT_FOLDER``
#   When compiling with MSVC, if this cache variable is set
#   the protobuf-default VS project build locations
#   (vsprojects/Debug and vsprojects/Release
#   or vsprojects/x64/Debug and vsprojects/x64/Release)
#   will be searched for libraries and binaries.
# ``PROTOBUF_IMPORT_DIRS``
#   List of additional directories to be searched for
#   imported .proto files.
#
# Defines the following variables:
#
# ``PROTOBUF_FOUND``
#   Found the Google Protocol Buffers library
#   (libprotobuf & header files)
# ``PROTOBUF_INCLUDE_DIRS``
#   Include directories for Google Protocol Buffers
# ``PROTOBUF_LIBRARIES``
#   The protobuf libraries
# ``PROTOBUF_PROTOC_LIBRARIES``
#   The protoc libraries
# ``PROTOBUF_LITE_LIBRARIES``
#   The protobuf-lite libraries
#
# The following cache variables are also available to set or use:
#
# ``PROTOBUF_LIBRARY``
#   The protobuf library
# ``PROTOBUF_PROTOC_LIBRARY``
#   The protoc library
# ``PROTOBUF_INCLUDE_DIR``
#   The include directory for protocol buffers
# ``PROTOBUF_COMPILER``
#   The protoc compiler

#
# Example:
#
# .. code-block:: cmake
#
#   find_package(Protobuf REQUIRED)
#   include_directories(${PROTOBUF_INCLUDE_DIRS})
#   include_directories(${CMAKE_CURRENT_BINARY_DIR})
#   protobuf_generate_cpp(PROTO_SRCS PROTO_HDRS foo.proto)
#   add_executable(bar bar.cc PROTOSRCS{PROTO_HDRS})
#   target_link_libraries(bar ${PROTOBUF_LIBRARIES})
#
# .. note::
#   The ``protobuf_generate_cpp`` and ``protobuf_generate_python``
#   functions and :command:`add_executable` or :command:`add_library`
#   calls only work properly within the same directory.
#
# .. command:: protobuf_generate_cpp
#
#   Add custom commands to process ``.proto`` files to C++::
#
#     protobuf_generate_cpp (<SRCS> <HDRS> [<ARGN>...])
#
#   ``SRCS``
#     Variable to define with autogenerated source files
#   ``HDRS``
#     Variable to define with autogenerated header files
#   ``ARGN``
#     ``.proto`` files
#
# if(MIDDLEWARE MATCHES MDC)
#   # message(STATUS "*****************MIDDLEWARE:*******eq*******" ${MIDDLEWARE})
# endif()
#	 set(PROTOBUF_FOUND 1 CACHE INTERNAL "PROTOBUF_FOUND")
#	 set(PROTOBUF_ROOT "${3RD_ROOT}/protobuf" CACHE INTERNAL "PROTOBUF_ROOT")
#	 set(PROTOBUF_LIBRARY_DIR ${PROTOBUF_ROOT}/lib CACHE INTERNAL "PROTOBUF_LIBRARY_DIR")
#	 set(PROTOBUF_INCLUDE_DIR ${PROTOBUF_ROOT}/include CACHE INTERNAL "PROTOBUF_INCLUDE_DIR")
#	 set(PROTOBUF_COMPILER ${3RD_ROOT_PROTOC}/bin/protoc CACHE INTERNAL "PROTOBUF_PROTOC_EXECUTABLE")
#	 set(PROTOC ${PROTOBUF_PROTOC_EXECUTABLE} CACHE INTERNAL "PROTOC")
#	 include_directories(${PROTOBUF_INCLUDE_DIR})
#	 link_directories(${PROTOBUF_LIBRARY_DIR})
#	 file(GLOB LIBRARIES "${PROTOBUF_LIBRARY_DIR}/libprotobuf*.so*")
#	 set(PROTOBUF_LIBRARIES ${LIBRARIES} CACHE INTERNAL "PROTOBUF_LIBRARIES")



################################################################################
########################## define functions ####################################



# ====================  PROTOBUF_GENERATE_CPP  ==========================
function(LOCAL_PROTOBUF_GENERATE_CPP SRCS HDRS)
  if(NOT ARGN)
    message(SEND_ERROR "Error: PROTOBUF_GENERATE_CPP() called without any proto files")
    return()
  endif()

  if(PROTOBUF_GENERATE_CPP_APPEND_PATH)
    # Create an include path for each file specified
    foreach(FIL ${ARGN})
      get_filename_component(ABS_FIL ${FIL} ABSOLUTE)
      get_filename_component(ABS_PATH ${ABS_FIL} PATH)
      list(FIND _protobuf_include_path ${ABS_PATH} _contains_already)
      if(${_contains_already} EQUAL -1)
        list(APPEND _protobuf_include_path -I ${ABS_PATH})
      endif()
    endforeach()
  else()
    # assume all .proto are in project_name/proto folder
    set(_protobuf_include_path -I ${CMAKE_CURRENT_SOURCE_DIR}/proto)
  endif()
  # create project folder devel/include/project_name/proto
  # Folder where the generated headers are installed to. This should resolve to
  # devel/include/project_name/proto

  list(APPEND _protobuf_include_path -I ${CMAKE_CURRENT_SOURCE_DIR})
  message(status "current source dir:${CMAKE_CURRENT_SOURCE_DIR}")


  set(${SRCS})
  set(${HDRS})

  foreach(FIL ${ARGN})
    get_filename_component(ABS_FIL ${FIL} ABSOLUTE)
    get_filename_component(FIL_WE ${FIL} NAME_WE)
    get_filename_component(RELT_DIR ${FIL} PATH)
    get_filename_component(LPATH ${FIL} REALPATH)

    message(STATUS "GENERATED_DIR:" ${RELT_DIR})

    set(GENERATED_SRC "${RELT_DIR}/${FIL_WE}.pb.cc")
    set(GENERATED_HDR "${RELT_DIR}/${FIL_WE}.pb.h")
    message(STATUS "GENERATED_SRC:" ${GENERATED_SRC})
    message(STATUS "GENERATED_HDR:" ${GENERATED_HDR})

    list(APPEND ${SRCS} "${GENERATED_SRC}")
    list(APPEND ${HDRS} "${GENERATED_HDR}")
    set(PROTO_DIR ${CMAKE_SOURCE_DIR})
    get_filename_component(PROTO_DIR ${CMAKE_CURRENT_SOURCE_DIR} ABSOLUTE)
    #get_filename_component(PROTO_DIR ${CMAKE_CURRENT_SOURCE_DIR}/proto ABSOLUTE)

    add_custom_command(
      OUTPUT ${GENERATED_SRC} ${GENERATED_HDR}
      COMMAND protobuf::protoc --proto_path=${PROTO_DIR} --cpp_out=${PROTO_DIR} ${ABS_FIL}
      DEPENDS ${ABS_FIL} ${PROTOBUF_COMPILER}
      COMMENT "Running C++ protocol buffer compiler on ${ABS_FIL}"
      VERBATIM)
    execute_process(
      COMMAND protobuf::protoc --proto_path=${PROTO_DIR} --cpp_out=${PROTO_DIR} ${ABS_FIL}
     )
    string(REPLACE "/" ""  replace_result ${FIL})
    add_custom_target(${replace_result} ALL DEPENDS ${GENERATED_SRC} ${GENERATED_HDR})
  endforeach()
  set_source_files_properties(${${SRCS}} ${${HDRS}} PROPERTIES GENERATED TRUE)
  set(${SRCS} ${${SRCS}} PARENT_SCOPE)
  set(${HDRS} ${${HDRS}} PARENT_SCOPE)
  #install(DIRECTORY ${COMPLIED_PROJ_DIR}
  #        DESTINATION ${CATKIN_DEVEL_PREFIX}/include/
  #        FILES_MATCHING PATTERN "*.h"
  #        PATTERN ".svn" EXCLUDE
  #)
endfunction()



################################################################################
################################################################################

