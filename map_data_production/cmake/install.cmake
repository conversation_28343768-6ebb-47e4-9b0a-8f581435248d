
set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Install path prefix" FORCE)
# 设置安装路径
if(NOT CMAKE_INSTALL_PREFIX)
    set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install" CACHE PATH "Install path prefix" FORCE)
endif()

find_package(Protobuf REQUIRED)

#target_include_directories(${routing_hd_map_lib} PUBLIC
#    $<BUILD_INTERFACE:${PROJECT_ROOT_DIR}/include>
#    $<BUILD_INTERFACE:${PROJECT_ROOT_DIR}/proto>
#    $<INSTALL_INTERFACE:include>
#    $<INSTALL_INTERFACE:proto>
#)

file(GLOB NERD_API_LIBRARIES "${TENCENT_SDK_DIR}/lib/*.so*")

install(DIRECTORY ${PROJECT_ROOT_DIR}/include/ DESTINATION include)
install(DIRECTORY ${PROJECT_ROOT_DIR}/proto/ DESTINATION proto)
install(DIRECTORY ${PROJECT_ROOT_DIR}/conf/ DESTINATION conf)
install(DIRECTORY ${PROJECT_ROOT_DIR}/data/ DESTINATION data)


# 生成配置版本文件
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/${routing_hd_map_lib}-config-version.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY SameMajorVersion
)
set(PROTOBUF_OUTPUT_LIBRARIES ${PROTOBUF_LIBRARIES})

list(REMOVE_ITEM ${PROTOBUF_OUTPUT_LIBRARIES} "-lpthread")
string(REGEX REPLACE ";-lpthread" ""  PROTOBUF_OUTPUT_LIBRARIES "${PROTOBUF_OUTPUT_LIBRARIES}")
message(STATUS "output proto:${PROTOBUF_OUTPUT_LIBRARIES}")

install(FILES
    "${CMAKE_CURRENT_BINARY_DIR}/${routing_hd_map_lib}-config-version.cmake"
    DESTINATION cmake/${routing_hd_map_lib}
)

install(FILES "${PROJECT_ROOT_DIR}/tc_map_env.bash" 
              "${PROJECT_ROOT_DIR}/bd_map_env.bash"
			  "${PROJECT_ROOT_DIR}/amap_env.bash"  DESTINATION ./)

install(DIRECTORY DESTINATION ${CMAKE_INSTALL_PREFIX}/test/hd_map_data)

get_filename_component(PROTOBUF_REAL_LIB_PATH ${PROTOBUF_LIBRARY} REALPATH)
install(FILES ${PROTOBUF_REAL_LIB_PATH} DESTINATION lib)
install(FILES ${PROTOBUF_LIBRARY} DESTINATION lib)


get_filename_component(ZMQ_REAL_LIB_PATH ${ZMQ_LIBRARY} REALPATH)
install(FILES ${ZMQ_REAL_LIB_PATH} DESTINATION lib)
install(FILES ${ZMQ_LIBRARY} DESTINATION lib)


install(CODE [[
        message(STATUS "Collecting runtime dependencies...")
        
        # 获取主库路径
        #set(LIBRARY_PATH
        #    "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/librouting_hd_map.so")
        
        set(LIBRARY_PATH
            "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/bin/hd_data_convertor")
        # 获取依赖库
        include(GNUInstallDirs)
        file(GET_RUNTIME_DEPENDENCIES
            EXECUTABLES ${LIBRARY_PATH}
            RESOLVED_DEPENDENCIES_VAR RESOLVED_DEPS
            UNRESOLVED_DEPENDENCIES_VAR UNRESOLVED_DEPS
            DIRECTORIES ${ZMQ_LIBRARY}   ${PROJ_LIBRARY} ${PROTOBUF_OUTPUT_LIBRARIES} 
			${NERD_API_LIBRARIES} /usr/lib /usr/local/lib
        )
        
        # 安装依赖库
        foreach(DEP ${RESOLVED_DEPS})
            # 跳过系统核心库
            if(NOT DEP MATCHES "^(/lib|/usr/lib|/lib64|/usr/lib64)")
			  message(STATUS "Installing dependency: ${DEP}")
			  file(INSTALL
			  "${DEP}"
			  DESTINATION "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib"
			  FOLLOW_SYMLINK_CHAIN
			  )
			endif()
        endforeach()
        
        # 处理未解析的依赖
        if(UNRESOLVED_DEPS)
            message(WARNING "Unresolved dependencies: ${UNRESOLVED_DEPS}")
        endif()
]])

# 创建卸载目标
configure_file(
    "${CMAKE_CURRENT_SOURCE_DIR}/cmake_uninstall.cmake.in"
    "${CMAKE_CURRENT_BINARY_DIR}/cmake_uninstall.cmake"
    IMMEDIATE @ONLY
)

add_custom_target(uninstall
    COMMAND ${CMAKE_COMMAND} -P ${CMAKE_CURRENT_BINARY_DIR}/cmake_uninstall.cmake
)

