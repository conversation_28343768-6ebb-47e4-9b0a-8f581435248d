function(PROTOBUF_GENERATE_CPP SRCS HDRS)
    if(NOT ARGN)
        message(SEND_ERROR "Error: PROTOBUF_GENERATE_CPP() called without any proto files")
        return()
    endif()

    if(PROTOBUF_GENERATE_CPP_APPEND_PATH)
        # Create an include path for each file specified
        foreach(FIL ${ARGN})
            get_filename_component(ABS_FIL ${FIL} ABSOLUTE)
            get_filename_component(ABS_PATH ${ABS_FIL} PATH)
            list(FIND _protobuf_include_path ${ABS_PATH} _contains_already)
            if(${_contains_already} EQUAL -1)
                list(APPEND _protobuf_include_path -I ${ABS_PATH})
            endif()
        endforeach()
    else()
        # assume all .proto are in project_name/proto folder
        set(_protobuf_include_path -I ${CMAKE_CURRENT_SOURCE_DIR}/proto)
    endif()
    # create project folder devel/include/project_name/proto
    # Folder where the generated headers are installed to. This should resolve to
    # devel/include/project_name/proto

    list(APPEND _protobuf_include_path -I ${CMAKE_CURRENT_SOURCE_DIR})


    set(${SRCS})
    set(${HDRS})

    if(NOT protobuf_generate_PROTOC_EXE)
        # Default to using the CMake executable
        set(protobuf_generate_PROTOC_EXE protobuf::protoc)
    endif()

    foreach(FIL ${ARGN})
        get_filename_component(ABS_FIL ${FIL} ABSOLUTE)
        get_filename_component(FIL_WE ${FIL} NAME_WE)
        get_filename_component(RELT_DIR ${FIL} PATH)
        get_filename_component(LPATH ${FIL} REALPATH)
        string(REPLACE "${CMAKE_CURRENT_SOURCE_DIR}" "${CMAKE_BINARY_DIR}" RELT_DIR ${RELT_DIR})
        # message(STATUS "GENERATED_DIR:" ${RELT_DIR})

        set(GENERATED_SRC "${RELT_DIR}/${FIL_WE}.pb.cc")
        set(GENERATED_HDR "${RELT_DIR}/${FIL_WE}.pb.h")
        # message(STATUS "GENERATED_SRC:" ${GENERATED_SRC})
        # message(STATUS "GENERATED_HDR:" ${GENERATED_HDR})

        list(APPEND ${SRCS} "${GENERATED_SRC}")
        list(APPEND ${HDRS} "${GENERATED_HDR}")
        # set(PROTO_DIR ${CMAKE_SOURCE_DIR})
        get_filename_component(PROTO_DIR ${CMAKE_CURRENT_SOURCE_DIR} ABSOLUTE)
        get_filename_component(PROTO_OUT_DIR ${CMAKE_BINARY_DIR} ABSOLUTE)
        add_custom_command(
                OUTPUT ${GENERATED_SRC} ${GENERATED_HDR}
                COMMAND  ${protobuf_generate_PROTOC_EXE} --proto_path=${PROTO_DIR} --cpp_out=${PROTO_OUT_DIR} ${ABS_FIL}
                DEPENDS ${ABS_FIL} ${protobuf_generate_PROTOC_EXE}
                COMMENT "Running C++ protocol buffer compiler on ${ABS_FIL}"
                VERBATIM)
        execute_process(
                COMMAND ${protobuf_generate_PROTOC_EXE} --proto_path=${PROTO_DIR} --cpp_out=${PROTO_OUT_DIR} ${ABS_FIL}
        )
        string(REPLACE "/" ""  replace_result ${FIL})
        add_custom_target(${replace_result} ALL DEPENDS ${GENERATED_SRC} ${GENERATED_HDR})
    endforeach()
    set_source_files_properties(${${SRCS}} ${${HDRS}} PROPERTIES GENERATED TRUE)
    set(${SRCS} ${${SRCS}} PARENT_SCOPE)
    set(${HDRS} ${${HDRS}} PARENT_SCOPE)
    #install(DIRECTORY ${COMPLIED_PROJ_DIR}
    #        DESTINATION ${CATKIN_DEVEL_PREFIX}/include/
    #        FILES_MATCHING PATTERN "*.h"
    #        PATTERN ".svn" EXCLUDE
    #)
endfunction()
