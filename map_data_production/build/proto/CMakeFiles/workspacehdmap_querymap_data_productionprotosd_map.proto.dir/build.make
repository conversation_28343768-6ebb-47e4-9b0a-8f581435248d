# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.24

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/hdmap_query/map_data_production

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/hdmap_query/map_data_production/build

# Utility rule file for workspacehdmap_querymap_data_productionprotosd_map.proto.

# Include any custom commands dependencies for this target.
include proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/compiler_depend.make

# Include the progress variables for this target.
include proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/progress.make

proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto: /workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc
proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto: /workspace/hdmap_query/map_data_production/proto/sd_map.pb.h

/workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc: /workspace/hdmap_query/map_data_production/proto/sd_map.proto
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running C++ protocol buffer compiler on /workspace/hdmap_query/map_data_production/proto/sd_map.proto"
	cd /workspace/hdmap_query/map_data_production/build/proto && /usr/local/bin/protoc --proto_path=/workspace/hdmap_query/map_data_production/proto --cpp_out=/workspace/hdmap_query/map_data_production/proto /workspace/hdmap_query/map_data_production/proto/sd_map.proto

/workspace/hdmap_query/map_data_production/proto/sd_map.pb.h: /workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc
	@$(CMAKE_COMMAND) -E touch_nocreate /workspace/hdmap_query/map_data_production/proto/sd_map.pb.h

workspacehdmap_querymap_data_productionprotosd_map.proto: proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto
workspacehdmap_querymap_data_productionprotosd_map.proto: /workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc
workspacehdmap_querymap_data_productionprotosd_map.proto: /workspace/hdmap_query/map_data_production/proto/sd_map.pb.h
workspacehdmap_querymap_data_productionprotosd_map.proto: proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/build.make
.PHONY : workspacehdmap_querymap_data_productionprotosd_map.proto

# Rule to build all files generated by this target.
proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/build: workspacehdmap_querymap_data_productionprotosd_map.proto
.PHONY : proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/build

proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/clean:
	cd /workspace/hdmap_query/map_data_production/build/proto && $(CMAKE_COMMAND) -P CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/cmake_clean.cmake
.PHONY : proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/clean

proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/depend:
	cd /workspace/hdmap_query/map_data_production/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /workspace/hdmap_query/map_data_production /workspace/hdmap_query/map_data_production/proto /workspace/hdmap_query/map_data_production/build /workspace/hdmap_query/map_data_production/build/proto /workspace/hdmap_query/map_data_production/build/proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/depend

