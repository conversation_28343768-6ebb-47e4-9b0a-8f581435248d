# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.24

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/hdmap_query/map_data_production

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/hdmap_query/map_data_production/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /workspace/hdmap_query/map_data_production/build && $(CMAKE_COMMAND) -E cmake_progress_start /workspace/hdmap_query/map_data_production/build/CMakeFiles /workspace/hdmap_query/map_data_production/build/proto//CMakeFiles/progress.marks
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/all
	$(CMAKE_COMMAND) -E cmake_progress_start /workspace/hdmap_query/map_data_production/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /workspace/hdmap_query/map_data_production/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
proto/CMakeFiles/workspacehdmap_querymap_data_productionprotocommongeometry.proto.dir/rule:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/CMakeFiles/workspacehdmap_querymap_data_productionprotocommongeometry.proto.dir/rule
.PHONY : proto/CMakeFiles/workspacehdmap_querymap_data_productionprotocommongeometry.proto.dir/rule

# Convenience name for target.
workspacehdmap_querymap_data_productionprotocommongeometry.proto: proto/CMakeFiles/workspacehdmap_querymap_data_productionprotocommongeometry.proto.dir/rule
.PHONY : workspacehdmap_querymap_data_productionprotocommongeometry.proto

# fast build rule for target.
workspacehdmap_querymap_data_productionprotocommongeometry.proto/fast:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f proto/CMakeFiles/workspacehdmap_querymap_data_productionprotocommongeometry.proto.dir/build.make proto/CMakeFiles/workspacehdmap_querymap_data_productionprotocommongeometry.proto.dir/build
.PHONY : workspacehdmap_querymap_data_productionprotocommongeometry.proto/fast

# Convenience name for target.
proto/CMakeFiles/workspacehdmap_querymap_data_productionprotohd_map_lane.proto.dir/rule:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/CMakeFiles/workspacehdmap_querymap_data_productionprotohd_map_lane.proto.dir/rule
.PHONY : proto/CMakeFiles/workspacehdmap_querymap_data_productionprotohd_map_lane.proto.dir/rule

# Convenience name for target.
workspacehdmap_querymap_data_productionprotohd_map_lane.proto: proto/CMakeFiles/workspacehdmap_querymap_data_productionprotohd_map_lane.proto.dir/rule
.PHONY : workspacehdmap_querymap_data_productionprotohd_map_lane.proto

# fast build rule for target.
workspacehdmap_querymap_data_productionprotohd_map_lane.proto/fast:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f proto/CMakeFiles/workspacehdmap_querymap_data_productionprotohd_map_lane.proto.dir/build.make proto/CMakeFiles/workspacehdmap_querymap_data_productionprotohd_map_lane.proto.dir/build
.PHONY : workspacehdmap_querymap_data_productionprotohd_map_lane.proto/fast

# Convenience name for target.
proto/CMakeFiles/workspacehdmap_querymap_data_productionprotortk_gps.proto.dir/rule:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/CMakeFiles/workspacehdmap_querymap_data_productionprotortk_gps.proto.dir/rule
.PHONY : proto/CMakeFiles/workspacehdmap_querymap_data_productionprotortk_gps.proto.dir/rule

# Convenience name for target.
workspacehdmap_querymap_data_productionprotortk_gps.proto: proto/CMakeFiles/workspacehdmap_querymap_data_productionprotortk_gps.proto.dir/rule
.PHONY : workspacehdmap_querymap_data_productionprotortk_gps.proto

# fast build rule for target.
workspacehdmap_querymap_data_productionprotortk_gps.proto/fast:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f proto/CMakeFiles/workspacehdmap_querymap_data_productionprotortk_gps.proto.dir/build.make proto/CMakeFiles/workspacehdmap_querymap_data_productionprotortk_gps.proto.dir/build
.PHONY : workspacehdmap_querymap_data_productionprotortk_gps.proto/fast

# Convenience name for target.
proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/rule:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/rule
.PHONY : proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/rule

# Convenience name for target.
workspacehdmap_querymap_data_productionprotosd_map.proto: proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/rule
.PHONY : workspacehdmap_querymap_data_productionprotosd_map.proto

# fast build rule for target.
workspacehdmap_querymap_data_productionprotosd_map.proto/fast:
	cd /workspace/hdmap_query/map_data_production/build && $(MAKE) $(MAKESILENT) -f proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/build.make proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/build
.PHONY : workspacehdmap_querymap_data_productionprotosd_map.proto/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... workspacehdmap_querymap_data_productionprotocommongeometry.proto"
	@echo "... workspacehdmap_querymap_data_productionprotohd_map_lane.proto"
	@echo "... workspacehdmap_querymap_data_productionprotortk_gps.proto"
	@echo "... workspacehdmap_querymap_data_productionprotosd_map.proto"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /workspace/hdmap_query/map_data_production/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

