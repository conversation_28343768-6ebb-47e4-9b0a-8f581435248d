# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.24

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /usr/local/lib/python3.10/dist-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /workspace/hdmap_query/map_data_production

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /workspace/hdmap_query/map_data_production/build

# Include any dependencies generated for this target.
include src/bd_map/CMakeFiles/bd_ld_engine.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.make

# Include the progress variables for this target.
include src/bd_map/CMakeFiles/bd_ld_engine.dir/progress.make

# Include the compile flags for this target's objects.
include src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make

src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.o: /workspace/hdmap_query/map_data_production/src/bd_map/test_data.cpp
src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.o -MF CMakeFiles/bd_ld_engine.dir/test_data.cpp.o.d -o CMakeFiles/bd_ld_engine.dir/test_data.cpp.o -c /workspace/hdmap_query/map_data_production/src/bd_map/test_data.cpp

src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/test_data.cpp.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/src/bd_map/test_data.cpp > CMakeFiles/bd_ld_engine.dir/test_data.cpp.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/test_data.cpp.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/src/bd_map/test_data.cpp -o CMakeFiles/bd_ld_engine.dir/test_data.cpp.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o: /workspace/hdmap_query/map_data_production/src/bd_map/sd_pro_data_manager.cc
src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o -MF CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o.d -o CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o -c /workspace/hdmap_query/map_data_production/src/bd_map/sd_pro_data_manager.cc

src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/src/bd_map/sd_pro_data_manager.cc > CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/src/bd_map/sd_pro_data_manager.cc -o CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o: /workspace/hdmap_query/map_data_production/src/bd_map/track_info_reader.cc
src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o -MF CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o.d -o CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o -c /workspace/hdmap_query/map_data_production/src/bd_map/track_info_reader.cc

src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/src/bd_map/track_info_reader.cc > CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/src/bd_map/track_info_reader.cc -o CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.o: /workspace/hdmap_query/map_data_production/src/bd_map/cJSON.c
src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.o -MF CMakeFiles/bd_ld_engine.dir/cJSON.c.o.d -o CMakeFiles/bd_ld_engine.dir/cJSON.c.o -c /workspace/hdmap_query/map_data_production/src/bd_map/cJSON.c

src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/bd_ld_engine.dir/cJSON.c.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /workspace/hdmap_query/map_data_production/src/bd_map/cJSON.c > CMakeFiles/bd_ld_engine.dir/cJSON.c.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/bd_ld_engine.dir/cJSON.c.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /workspace/hdmap_query/map_data_production/src/bd_map/cJSON.c -o CMakeFiles/bd_ld_engine.dir/cJSON.c.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o: /workspace/hdmap_query/map_data_production/src/bd_map/hd_map_to_geojson.cc
src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o -MF CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o.d -o CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o -c /workspace/hdmap_query/map_data_production/src/bd_map/hd_map_to_geojson.cc

src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/src/bd_map/hd_map_to_geojson.cc > CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/src/bd_map/hd_map_to_geojson.cc -o CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o: /workspace/hdmap_query/map_data_production/src/bd_map/util/Coordinates.cpp
src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o -MF CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o.d -o CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o -c /workspace/hdmap_query/map_data_production/src/bd_map/util/Coordinates.cpp

src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/src/bd_map/util/Coordinates.cpp > CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/src/bd_map/util/Coordinates.cpp -o CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o: /workspace/hdmap_query/map_data_production/src/bd_map/util/utm_to_latlon.cpp
src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o -MF CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o.d -o CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o -c /workspace/hdmap_query/map_data_production/src/bd_map/util/utm_to_latlon.cpp

src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/src/bd_map/util/utm_to_latlon.cpp > CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/src/bd_map/util/utm_to_latlon.cpp -o CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o: /workspace/hdmap_query/map_data_production/proto/common/geometry.pb.cc
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o -MF CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o.d -o CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o -c /workspace/hdmap_query/map_data_production/proto/common/geometry.pb.cc

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/proto/common/geometry.pb.cc > CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/proto/common/geometry.pb.cc -o CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o: /workspace/hdmap_query/map_data_production/proto/hd_map_lane.pb.cc
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o -MF CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o.d -o CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o -c /workspace/hdmap_query/map_data_production/proto/hd_map_lane.pb.cc

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/proto/hd_map_lane.pb.cc > CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/proto/hd_map_lane.pb.cc -o CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o: /workspace/hdmap_query/map_data_production/proto/rtk_gps.pb.cc
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o -MF CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o.d -o CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o -c /workspace/hdmap_query/map_data_production/proto/rtk_gps.pb.cc

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/proto/rtk_gps.pb.cc > CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/proto/rtk_gps.pb.cc -o CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.s

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/flags.make
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o: /workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc
src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o: src/bd_map/CMakeFiles/bd_ld_engine.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o -MF CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o.d -o CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o -c /workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.i"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc > CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.i

src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.s"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /workspace/hdmap_query/map_data_production/proto/sd_map.pb.cc -o CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.s

# Object files for target bd_ld_engine
bd_ld_engine_OBJECTS = \
"CMakeFiles/bd_ld_engine.dir/test_data.cpp.o" \
"CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o" \
"CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o" \
"CMakeFiles/bd_ld_engine.dir/cJSON.c.o" \
"CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o" \
"CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o" \
"CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o" \
"CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o" \
"CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o" \
"CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o" \
"CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o"

# External object files for target bd_ld_engine
bd_ld_engine_EXTERNAL_OBJECTS =

bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/test_data.cpp.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/sd_pro_data_manager.cc.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/track_info_reader.cc.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/cJSON.c.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/hd_map_to_geojson.cc.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/util/Coordinates.cpp.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/util/utm_to_latlon.cpp.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/common/geometry.pb.cc.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/hd_map_lane.pb.cc.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/rtk_gps.pb.cc.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/__/__/proto/sd_map.pb.cc.o
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/build.make
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libime.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcurl.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libhdiffpatch.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libssl.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libcrypto.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so.2
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so.2.4.2
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcurl.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libhdiffpatch.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libhdmap-service-idl.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libime.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so.1.9.4
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so.24
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf-lite.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf-lite.so.3.14.0.0
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so.3.14.0.0
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotoc.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotoc.so.3.14.0.0
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/librttr_core.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/librttr_core.so.0.9.6
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so.1
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so.1.2.11
bin/bd_ld_engine: /usr/local/lib/libprotobuf.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libzmq.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libproj.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libime.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcurl.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libhdiffpatch.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libssl.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libcrypto.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so.2
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so.2.4.2
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libhdmap-service-idl.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so.1.9.4
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so.24
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf-lite.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf-lite.so.3.14.0.0
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so.3.14.0.0
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotoc.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotoc.so.3.14.0.0
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/librttr_core.so
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/librttr_core.so.0.9.6
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so.1
bin/bd_ld_engine: /workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so.1.2.11
bin/bd_ld_engine: /usr/local/lib/libprotobuf.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libzmq.so
bin/bd_ld_engine: /usr/lib/x86_64-linux-gnu/libproj.so
bin/bd_ld_engine: src/bd_map/CMakeFiles/bd_ld_engine.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/workspace/hdmap_query/map_data_production/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Linking CXX executable ../../bin/bd_ld_engine"
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bd_ld_engine.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/bd_map/CMakeFiles/bd_ld_engine.dir/build: bin/bd_ld_engine
.PHONY : src/bd_map/CMakeFiles/bd_ld_engine.dir/build

src/bd_map/CMakeFiles/bd_ld_engine.dir/clean:
	cd /workspace/hdmap_query/map_data_production/build/src/bd_map && $(CMAKE_COMMAND) -P CMakeFiles/bd_ld_engine.dir/cmake_clean.cmake
.PHONY : src/bd_map/CMakeFiles/bd_ld_engine.dir/clean

src/bd_map/CMakeFiles/bd_ld_engine.dir/depend:
	cd /workspace/hdmap_query/map_data_production/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /workspace/hdmap_query/map_data_production /workspace/hdmap_query/map_data_production/src/bd_map /workspace/hdmap_query/map_data_production/build /workspace/hdmap_query/map_data_production/build/src/bd_map /workspace/hdmap_query/map_data_production/build/src/bd_map/CMakeFiles/bd_ld_engine.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : src/bd_map/CMakeFiles/bd_ld_engine.dir/depend

