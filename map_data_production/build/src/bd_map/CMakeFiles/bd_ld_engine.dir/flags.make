# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.24

# compile C with /usr/bin/cc
# compile CXX with /usr/bin/c++
C_DEFINES = 

C_INCLUDES = -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include/util -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include/map_engine -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include/dtc -I/workspace/hdmap_query/map_data_production -I/workspace/hdmap_query/map_data_production/proto -I/workspace/hdmap_query/map_data_production/proto/common -I/workspace/hdmap_query/map_data_production/include -I/workspace/hdmap_query/map_data_production/include/bd_map -I/workspace/hdmap_query/map_data_production/include/bd_map/util

C_FLAGS = 

CXX_DEFINES = 

CXX_INCLUDES = -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include/util -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include/map_engine -I/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/include/dtc -I/workspace/hdmap_query/map_data_production -I/workspace/hdmap_query/map_data_production/proto -I/workspace/hdmap_query/map_data_production/proto/common -I/workspace/hdmap_query/map_data_production/include -I/workspace/hdmap_query/map_data_production/include/bd_map -I/workspace/hdmap_query/map_data_production/include/bd_map/util

CXX_FLAGS =  -pthread -std=gnu++17

