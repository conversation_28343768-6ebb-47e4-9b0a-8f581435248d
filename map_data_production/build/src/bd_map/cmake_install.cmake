# Install script for directory: /workspace/hdmap_query/map_data_production/src/bd_map

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "/workspace/hdmap_query/map_data_production/build/install")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Install shared libraries without execute permission?
if(NOT DEFINED CMAKE_INSTALL_SO_NO_EXE)
  set(CMAKE_INSTALL_SO_NO_EXE "1")
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "FALSE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "/usr/bin/objdump")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/bd_lib" TYPE FILE FILES
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so.2"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcares.so.2.4.2"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libcurl.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libhdiffpatch.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libhdmap-service-idl.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libime.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so.1.9.4"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libjsoncpp.so.24"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf-lite.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf-lite.so.3.14.0.0"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotobuf.so.3.14.0.0"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotoc.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libprotoc.so.3.14.0.0"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/librttr_core.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/librttr_core.so.0.9.6"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so.1"
    "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/lib/libz.so.1.2.11"
    )
endif()

