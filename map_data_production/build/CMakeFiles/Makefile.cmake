# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.24

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeCInformation.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeCXXInformation.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeCommonLanguageInclude.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeGenericSystem.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeInitializeConfigs.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeLanguageInformation.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakePackageConfigHelpers.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeSystemSpecificInformation.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CMakeSystemSpecificInitialize.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CheckCSourceCompiles.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CheckCXXSourceCompiles.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CheckIncludeFile.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CheckIncludeFileCXX.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/CheckLibraryExists.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Compiler/GNU-C.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Compiler/GNU-CXX.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Compiler/GNU.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/FindPackageHandleStandardArgs.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/FindPackageMessage.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/FindProtobuf.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/FindThreads.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Internal/CheckSourceCompiles.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Platform/Linux-GNU-C.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Platform/Linux-GNU-CXX.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Platform/Linux-GNU.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Platform/Linux.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/Platform/UnixPaths.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/SelectLibraryConfigurations.cmake"
  "/usr/local/lib/python3.10/dist-packages/cmake/data/share/cmake-3.24/Modules/WriteBasicConfigVersionFile.cmake"
  "/workspace/hdmap_query/map_data_production/CMakeLists.txt"
  "CMakeFiles/3.24.3/CMakeCCompiler.cmake"
  "CMakeFiles/3.24.3/CMakeCXXCompiler.cmake"
  "CMakeFiles/3.24.3/CMakeSystem.cmake"
  "/workspace/hdmap_query/map_data_production/cmake/FindProtobuf.cmake"
  "/workspace/hdmap_query/map_data_production/cmake/install.cmake"
  "/workspace/hdmap_query/map_data_production/cmake_uninstall.cmake.in"
  "/workspace/hdmap_query/map_data_production/proto/CMakeLists.txt"
  "/workspace/hdmap_query/map_data_production/src/bd_map/CMakeLists.txt"
  "/workspace/hdmap_query/map_data_production/thirdparty/baidu/x86/FindBDmap.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "routing_hd_map-config-version.cmake"
  "cmake_uninstall.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  "proto/CMakeFiles/CMakeDirectoryInformation.cmake"
  "src/bd_map/CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/uninstall.dir/DependInfo.cmake"
  "proto/CMakeFiles/workspacehdmap_querymap_data_productionprotocommongeometry.proto.dir/DependInfo.cmake"
  "proto/CMakeFiles/workspacehdmap_querymap_data_productionprotohd_map_lane.proto.dir/DependInfo.cmake"
  "proto/CMakeFiles/workspacehdmap_querymap_data_productionprotortk_gps.proto.dir/DependInfo.cmake"
  "proto/CMakeFiles/workspacehdmap_querymap_data_productionprotosd_map.proto.dir/DependInfo.cmake"
  "src/bd_map/CMakeFiles/bd_ld_engine.dir/DependInfo.cmake"
  )
