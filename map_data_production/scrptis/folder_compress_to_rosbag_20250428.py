# File: folder_compress_to_rosbag.py
# Description: 
# Author: qingxiansun
# Date: 2025-04-28
# Attention: Copyright Great Wall Technology Co.Ltd
# Attention: Please refer to COPYRIGHT.txt for complete terms of copyright


#!/usr/bin/env python
import rosbag
import rospy
from std_msgs.msg import String
from sensor_msgs.msg import Image, PointCloud2, CompressedImage
from cv_bridge import CvBridge
from sensor_msgs.msg import PointField
import json
import os
import time
import numpy as np
import cv2
import base64
import hashlib

class OptimizedRosbagConverter:
    def __init__(self):
        self.cv_bridge = CvBridge()
        self.compression = rosbag.Compression.BZ2  # 启用BZ2压缩
        self.image_compress_threshold = 512  # KB，超过此大小保持JPEG压缩
        self.metadata_only = False  # 设为True可只存储元数据

    def convert_to_bag(self, root_dir, output_bag, match_json_dir="match_json_floder"):
        """
        优化后的bag转换器，显著减小文件大小
        """
        with rosbag.Bag(output_bag, 'w', compression=self.compression) as bag:
            # 1. 优化匹配文件处理（单副本存储）
            matched_file = os.path.join(match_json_dir, "matched_file.json")
            if os.path.isfile(matched_file):
                self._add_optimized_matched_file(matched_file, bag)

            # 2. 优化configs处理
            configs_dir = os.path.join(root_dir, 'configs')
            if os.path.isdir(configs_dir):
                self._process_configs_optimized(configs_dir, bag)

            # 3. 优化数据文件夹处理
            for dir_name in self._list_data_dirs(root_dir):
                dir_path = os.path.join(root_dir, dir_name)
                data_type = self._detect_data_type(dir_path)
                
                if data_type == "image":
                    self._process_images_optimized(dir_path, dir_name, bag)
                elif data_type == "pointcloud":
                    self._process_pointcloud_optimized(dir_path, dir_name, bag)
                elif data_type == "json":
                    self._process_jsons_optimized(dir_path, dir_name, bag)

    def _add_optimized_matched_file(self, file_path, bag):
        """优化匹配文件存储（减小75%大小）"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 只存储压缩后的单副本
            msg = String()
            msg.data = json.dumps({
                'file_name': os.path.basename(file_path),
                'size_kb': len(content) / 1024,
                'md5': hashlib.md5(content).hexdigest(),
                'content': base64.b64encode(content).decode('utf-8')
            })
            
            bag.write("/match_data", msg, rospy.Time.from_sec(os.path.getmtime(file_path)))
            
        except Exception as e:
            rospy.logerr(f"Matched file error: {str(e)}")
            raise

    def _process_images_optimized(self, dir_path, topic_name, bag):
        """优化图片处理（减小2-10倍）"""
        for filename in sorted(os.listdir(dir_path)):
            if not filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                continue
                
            filepath = os.path.join(dir_path, filename)
            try:
                file_size_kb = os.path.getsize(filepath) / 1024
                
                # 大文件JPEG保持压缩格式
                if file_size_kb > self.image_compress_threshold and filename.lower().endswith(('.jpg', '.jpeg')):
                    with open(filepath, 'rb') as f:
                        jpeg_data = f.read()
                    
                    msg = CompressedImage()
                    msg.header.stamp = self._get_timestamp(filename)
                    msg.format = "jpeg"
                    msg.data = jpeg_data
                    bag.write(f"/{topic_name}/compressed", msg, msg.header.stamp)
                else:
                    # 小文件处理
                    img = cv2.imread(filepath)
                    if img is None:
                        raise ValueError("Invalid image")
                        
                    # 如果是PNG且较大，尝试压缩
                    if filename.lower().endswith('.png') and file_size_kb > 200:
                        _, compressed_img = cv2.imencode('.png', img, [cv2.IMWRITE_PNG_COMPRESSION, 9])
                        img_msg = self.cv_bridge.cv2_to_imgmsg(cv2.imdecode(compressed_img, 1), "bgr8")
                    else:
                        img_msg = self.cv_bridge.cv2_to_imgmsg(img, "bgr8")
                    
                    img_msg.header.stamp = self._get_timestamp(filename)
                    bag.write(f"/{topic_name}/image", img_msg, img_msg.header.stamp)
                    
            except Exception as e:
                rospy.logwarn(f"Skipped {filename}: {str(e)}")
                continue

    def _process_pointcloud_optimized(self, dir_path, topic_name, bag):
        """优化点云处理（减小3-5倍）"""
        try:
            import pcl
            use_pcl = True
        except ImportError:
            use_pcl = False
            rospy.logwarn("PCL not found, using basic pointcloud processing")

        for filename in sorted(os.listdir(dir_path)):
            if not filename.endswith(('.pcd', '.bin')):
                continue
                
            filepath = os.path.join(dir_path, filename)
            try:
                if use_pcl and filename.endswith('.pcd'):
                    cloud = pcl.load(filepath)
                    points = cloud.to_array()
                else:
                    # 基本处理（需要根据实际格式调整）
                    points = np.fromfile(filepath, dtype=np.float32).reshape(-1, 3)
                
                # 创建优化后的PointCloud2消息
                msg = PointCloud2()
                msg.header.stamp = self._get_timestamp(filename)
                msg.header.frame_id = "map"
                msg.height = 1
                msg.width = points.shape[0]
                msg.fields = [
                    PointField(name='x', offset=0, datatype=PointField.FLOAT32, count=1),
                    PointField(name='y', offset=4, datatype=PointField.FLOAT32, count=1),
                    PointField(name='z', offset=8, datatype=PointField.FLOAT32, count=1)
                ]
                msg.is_bigendian = False
                msg.point_step = 12  # 3x float32
                msg.row_step = msg.point_step * msg.width
                msg.data = points.tobytes()
                msg.is_dense = True
                
                bag.write(f"/{topic_name}/points", msg, msg.header.stamp)
                
            except Exception as e:
                rospy.logwarn(f"Skipped {filename}: {str(e)}")
                continue

    def _process_jsons_optimized(self, dir_path, topic_name, bag):
        """优化JSON处理（减小30-50%）"""
        for filename in sorted(os.listdir(dir_path)):
            if not filename.endswith('.json'):
                continue
                
            filepath = os.path.join(dir_path, filename)
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                msg = String()
                # 紧凑格式存储
                msg.data = json.dumps({
                    'f': filename,  # 缩写字段名
                    'd': data      # 原始数据
                }, separators=(',', ':'))  # 移除多余空格
                
                bag.write(f"/{topic_name}", msg, self._get_timestamp(filename))
                
            except Exception as e:
                rospy.logwarn(f"Skipped {filename}: {str(e)}")
                continue

    def _process_configs_optimized(self, configs_dir, bag):
        """优化配置处理"""
        for filename in sorted(os.listdir(configs_dir)):
            if not filename.endswith(('.json', '.yaml', '.yml')):
                continue
                
            filepath = os.path.join(configs_dir, filename)
            try:
                with open(filepath, 'r') as f:
                    if filename.endswith('.json'):
                        content = json.load(f)
                    else:
                        import yaml
                        content = yaml.safe_load(f)
                
                msg = String()
                msg.data = json.dumps({
                    'f': filename,
                    'c': content  # 紧凑格式
                }, separators=(',', ':'))
                
                bag.write("/configs", msg, rospy.Time.from_sec(os.path.getmtime(filepath)))
                
            except Exception as e:
                rospy.logwarn(f"Skipped {filename}: {str(e)}")
                continue

    def _list_data_dirs(self, root_dir):
        """列出有效数据目录"""
        return [d for d in os.listdir(root_dir) 
               if os.path.isdir(os.path.join(root_dir, d)) 
               and not d.startswith('.') 
               and d not in ['configs', 'match_json_floder']]

    def _detect_data_type(self, dir_path):
        """检测目录中的主要数据类型"""
        exts = [os.path.splitext(f)[1].lower() for f in os.listdir(dir_path)]
        if any(e in exts for e in ['.jpg', '.jpeg', '.png']):
            return "image"
        elif any(e in exts for e in ['.pcd', '.bin']):
            return "pointcloud"
        elif '.json' in exts:
            return "json"
        return "unknown"

    def _get_timestamp(self, filename):
        """高效时间戳提取"""
        try:
            basename = os.path.splitext(filename)[0]
            nums = [int(s) for s in basename.split('_') if s.isdigit()]
            if nums:
                sec = nums[-1] // 10**9 if nums[-1] > 1e10 else nums[-1]
                nsec = nums[-1] % 10**9 if nums[-1] > 1e10 else 0
                return rospy.Time(sec, nsec)
        except:
            pass
        return rospy.Time.from_sec(time.time())

if __name__ == '__main__':
    rospy.init_node('optimized_bag_converter')
    
    # 配置路径
    converter = OptimizedRosbagConverter()
    
    try:
        root_dir = './YR-C01-2479EE285682_20250209_122852_2_dst'
        output_bag = 'YR-C01-2479EE285682_20250209_122852_2_dst_optimized_output.bag'
        match_json_dir = './YR-C01-2479EE285682_20250209_122852_2_dst/match_json_floder'
        
        print("Starting optimized conversion...")
        start_time = time.time()
        
        converter.convert_to_bag(
            root_dir=root_dir,
            output_bag=output_bag,
            match_json_dir=match_json_dir
        )
        
        # 结果验证
        duration = time.time() - start_time
        print(f"\nConversion completed in {duration:.1f} seconds")
        
        # 显示压缩信息
        original_size = sum(os.path.getsize(os.path.join(root, f)) 
                          for root, _, files in os.walk(root_dir) 
                          for f in files) / (1024**2)
        bag_size = os.path.getsize(output_bag) / (1024**2)
        
        print(f"\nSize comparison:")
        print(f"- Original data: {original_size:.1f} MB")
        print(f"- Optimized bag: {bag_size:.1f} MB")
        print(f"- Compression ratio: {original_size/bag_size:.1f}x")
        
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        exit(1)
