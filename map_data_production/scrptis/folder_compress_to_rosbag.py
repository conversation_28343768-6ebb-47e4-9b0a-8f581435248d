# File: folder_to_rosbag.py
# Description: 
# Author: qingxiansun
# Date: 2025-04-28
# Attention: Copyright Great Wall Technology Co.Ltd
# Attention: Please refer to COPYRIGHT.txt for complete terms of copyright


#!/usr/bin/env python
import rosbag
import rospy
from std_msgs.msg import String
from sensor_msgs.msg import Image, PointCloud2, CompressedImage
from cv_bridge import CvBridge
from sensor_msgs.msg import PointField
import json
import os
import time
import numpy as np
import cv2
import base64
import hashlib
import struct
import lzma
import zlib


class RosbagConverter:
    def __init__(self):
        self.cv_bridge = CvBridge()
        self.min_time = None
        self.max_time = None
        
    def _update_time_range(self, timestamp):
        """更新全局时间范围"""
        if self.min_time is None or timestamp < self.min_time:
            self.min_time = timestamp
        if self.max_time is None or timestamp > self.max_time:
            self.max_time = timestamp
            
    def convert_to_bag(self, root_dir, output_bag, match_json_dir="match_json_floder"):
        """
        修正时间戳问题的版本
        """
        self.min_time = None
        self.max_time = None
        
        with rosbag.Bag(output_bag, 'w') as bag:
            # 1. 处理匹配文件
            matched_file_path = os.path.join(match_json_dir, "matched_file.json")
            if os.path.isfile(matched_file_path):
                self._process_matched_file(matched_file_path, bag)
            
            # 2. 处理configs
            configs_dir = os.path.join(root_dir, 'configs')
            if os.path.isdir(configs_dir):
                self._process_configs(configs_dir, bag)
            
            # 3. 处理数据文件夹
            for dir_name in sorted(os.listdir(root_dir)):
                dir_path = os.path.join(root_dir, dir_name)
                if dir_name in ['configs', 'match_json_floder']:
                    continue
                    
                if os.path.isdir(dir_path) and not dir_name.startswith('.'):
                    self._process_directory(dir_path, dir_name, bag)
            
            # 添加bag文件的全局时间信息
            if self.min_time and self.max_time:
                duration = (self.max_time - self.min_time).to_sec()
                print(f"\nFinal time range:")
                print(f"- Start: {self.min_time.to_sec()} ({self.min_time})")
                print(f"- End:   {self.max_time.to_sec()} ({self.max_time})")
                print(f"- Duration: {duration:.2f} seconds")
    
    def _process_matched_file(self, file_path, bag):
        """处理匹配文件（修正时间戳）"""
        try:
            with open(file_path, 'r') as f:
                matched_data = json.load(f)
            
            # 使用文件修改时间或当前时间
            timestamp = rospy.Time.from_sec(os.path.getmtime(file_path))
            self._update_time_range(timestamp)
            
            # 只存储一个精简版本
            msg = String()
            msg.data = json.dumps({
                'file': os.path.basename(file_path),
                'md5': hashlib.md5(open(file_path,'rb').read()).hexdigest(),
                'content': matched_data
            })
            
            bag.write("/match_data", msg, timestamp)
            
        except Exception as e:
            print(f"Error processing matched file: {str(e)}")
            raise
    
    def _process_configs(self, configs_dir, bag):
        """处理配置文件（修正时间戳）"""
        for filename in sorted(os.listdir(configs_dir)):
            if not filename.endswith(('.json', '.yaml', '.yml')):
                continue
                
            filepath = os.path.join(configs_dir, filename)
            try:
                timestamp = rospy.Time.from_sec(os.path.getmtime(filepath))
                self._update_time_range(timestamp)
                
                with open(filepath, 'r') as f:
                    content = yaml.safe_load(f) if filename.endswith(('.yaml', '.yml')) else json.load(f)
                
                msg = String()
                msg.data = json.dumps({
                    'file': filename,
                    'content': content
                })
                
                bag.write("/configs", msg, timestamp)
                
            except Exception as e:
                print(f"Skipped config {filename}: {str(e)}")
                continue
    
    def _process_directory(self, dir_path, dir_name, bag):
        """处理数据目录（修正时间戳）"""
        data_type = self._detect_data_type(dir_path)
        
        if data_type == "json":
            self._process_json_files(dir_path, dir_name, bag)
        elif data_type == "image":
            self._process_image_files(dir_path, dir_name, bag)
        elif data_type == "pointcloud":
            self._process_pointcloud_files(dir_path, dir_name, bag)
    
    def _process_json_files(self, dir_path, topic_name, bag):
        """处理JSON文件（修正时间戳）"""
        for filename in sorted(os.listdir(dir_path)):
            if not filename.endswith('.json'):
                continue
                
            try:
                timestamp = self._get_valid_timestamp(filename)
                self._update_time_range(timestamp)
                
                with open(os.path.join(dir_path, filename), 'r') as f:
                    data = json.load(f)
                
                msg = String()
                msg.data = json.dumps(data)
                bag.write(f"/{topic_name}", msg, timestamp)
                
            except Exception as e:
                print(f"Skipped {filename}: {str(e)}")
                continue
    
    def _process_image_files(self, dir_path, topic_name, bag):
        """处理图片文件（修正时间戳）"""
        for filename in sorted(os.listdir(dir_path)):
            if not filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                continue
                
            try:
                timestamp = self._get_valid_timestamp(filename)
                self._update_time_range(timestamp)
                
                filepath = os.path.join(dir_path, filename)
                if filename.lower().endswith(('.jpg', '.jpeg')):
                    # JPEG保持压缩格式
                    with open(filepath, 'rb') as f:
                        msg = CompressedImage()
                        msg.header.stamp = timestamp
                        msg.format = "jpeg"
                        msg.data = f.read()
                    bag.write(f"/{topic_name}/compressed", msg, timestamp)
                else:
                    # PNG等其他格式
                    img = cv2.imread(filepath)
                    if img is not None:
                        msg = self.cv_bridge.cv2_to_imgmsg(img, "bgr8")
                        msg.header.stamp = timestamp
                        bag.write(f"/{topic_name}/image", msg, timestamp)
                
            except Exception as e:
                print(f"Skipped {filename}: {str(e)}")
                continue
    def _process_pointcloud_files(self, dir_path, topic_name, bag):
        """处理点云数据（针对Velodyne格式优化）"""
        for filename in sorted(os.listdir(dir_path)):
            if not filename.endswith('.pcd'):
                continue
                
            filepath = os.path.join(dir_path, filename)
            try:
                # 读取点云数据
                points, intensities, rings, timestamps = self._read_velodyne_pcd(filepath)
                
                # 创建时间戳（使用点云中的最大时间戳）
                max_timestamp = max(timestamps) if len(timestamps) > 0 else 0
                timestamp = rospy.Time.from_sec(max_timestamp)
                self._update_time_range(timestamp)
                
                # 点云元数据
                meta_msg = String()
                meta_msg.data = json.dumps({
                    'file_name': filename,
                    'file_path': os.path.abspath(filepath),
                    'point_count': len(points),
                    'timestamp': max_timestamp
                })
                
                # 创建PointCloud2消息
                pc_msg = PointCloud2()
                pc_msg.header.stamp = timestamp
                pc_msg.header.frame_id = "velodyne"  # 使用Velodyne坐标系
                pc_msg.height = 1
                pc_msg.width = len(points)
                
                # 根据提供的格式设置字段
                pc_msg.fields = [
                    PointField(name='x', offset=0, datatype=PointField.FLOAT32, count=1),
                    PointField(name='y', offset=4, datatype=PointField.FLOAT32, count=1),
                    PointField(name='z', offset=8, datatype=PointField.FLOAT32, count=1),
                    PointField(name='intensity', offset=12, datatype=PointField.UINT8, count=1),
                    PointField(name='ring', offset=13, datatype=PointField.UINT8, count=1),
                    PointField(name='timestamp', offset=14, datatype=PointField.UINT16, count=1)
                ]
                
                pc_msg.is_bigendian = False
                pc_msg.point_step = 16  # 4+4+4+1+1+2 = 16 bytes
                pc_msg.row_step = pc_msg.point_step * pc_msg.width
                
                # 组装二进制数据
                data = bytearray()
                for i in range(len(points)):
                    data += struct.pack('fffBBH', 
                                      points[i][0], points[i][1], points[i][2],
                                      intensities[i], rings[i], timestamps[i])
                pc_msg.data = bytes(data)
                pc_msg.is_dense = True
                
                # 写入到bag
                bag.write(f"/{topic_name}/meta", meta_msg, pc_msg.header.stamp)
                bag.write(f"/{topic_name}/points", pc_msg, pc_msg.header.stamp)
                
            except Exception as e:
                print(f"Error processing {filepath}: {str(e)}")
                continue

    def _read_velodyne_pcd(self, filepath):
        """
        专门处理Velodyne特殊压缩格式的PCD文件
        解决"incorrect header check"错误
        """
        with open(filepath, 'rb') as f:
            # 1. 读取ASCII头部
            header = []
            while True:
                line = f.readline()
                try:
                    decoded_line = line.decode('ascii').strip()
                except UnicodeDecodeError:
                    decoded_line = str(line)
                header.append(decoded_line)
                if decoded_line.startswith('DATA binary_compressed'):
                    break
                if line == b'':
                    break

            # 2. 解析点数量
            points_count = 0
            for line in header:
                if line.startswith('POINTS'):
                    points_count = int(line.split()[1])
                    break

            if points_count == 0:
                raise ValueError("PCD文件没有点数据")

            # 3. 读取压缩头 (8字节: compressed_size + uncompressed_size)
            comp_header = f.read(8)
            if len(comp_header) != 8:
                raise ValueError("无效的压缩头长度")

            compressed_size, uncompressed_size = struct.unpack('<II', comp_header)

            # 4. 读取压缩数据
            compressed_data = f.read(compressed_size)
            if len(compressed_data) != compressed_size:
                raise ValueError("压缩数据长度不匹配")

            # 5. 特殊解压处理 - 使用原始zlib数据(不加头)
            try:
                # 方法1：使用decompressobj处理原始zlib数据
                decompressor = zlib.decompressobj(wbits=-zlib.MAX_WBITS)
                data = decompressor.decompress(compressed_data)
                data += decompressor.flush()
            except zlib.error as e:
                # 方法2：尝试添加标准zlib头
                try:
                    data = zlib.decompress(b'\x78\x9C' + compressed_data)
                except zlib.error:
                    # 方法3：最后尝试直接原始数据
                    try:
                        data = compressed_data
                        if len(data) != uncompressed_size:
                            raise ValueError("使用原始数据但大小不匹配")
                    except:
                        raise ValueError(f"解压失败: {str(e)}. 文件可能已损坏")

            # 6. 验证数据大小
            expected_size = points_count * 16  # 4+4+4+1+1+2=16字节/点
            if len(data) < expected_size:
                raise ValueError(f"数据不足: 需要{expected_size}字节, 只有{len(data)}字节")

            # 7. 使用numpy高效解析
            try:
                dtype = np.dtype([
                    ('x', '<f4'), ('y', '<f4'), ('z', '<f4'),
                    ('intensity', 'u1'), ('ring', 'u1'),
                    ('timestamp', '<u2')  # 注意是小端序
                ])
                points_arr = np.frombuffer(data[:expected_size], dtype=dtype)

                return (
                    np.column_stack((points_arr['x'], points_arr['y'], points_arr['z'])),
                    points_arr['intensity'].copy(),
                    points_arr['ring'].copy(),
                    points_arr['timestamp'].copy()
                )
            except Exception as e:
                raise ValueError(f"点数据解析失败: {str(e)}")

    def _get_valid_timestamp(self, filename):
        """确保返回有效的时间戳"""
        try:
            basename = os.path.splitext(filename)[0]
            
            # 尝试从文件名提取时间戳（假设格式为timestamp或prefix_timestamp）
            ts_str = basename.split('_')[-1] if '_' in basename else basename
            
            if ts_str.isdigit():
                sec = int(ts_str[:10])  # 取前10位作为秒
                nsec = int(ts_str[10:19]) if len(ts_str) > 10 else 0
                return rospy.Time(sec, nsec)
        except:
            pass
        
        # 默认使用当前时间
        return rospy.Time.from_sec(time.time())
    
    def _detect_data_type(self, dir_path):
        """检测数据类型"""
        exts = {os.path.splitext(f)[1].lower() for f in os.listdir(dir_path)}
        if any(e in exts for e in ['.jpg', '.jpeg', '.png']):
            return "image"
        elif any(e in exts for e in ['.pcd', '.bin']):
            return "pointcloud"
        elif '.json' in exts:
            return "json"
        return None

if __name__ == '__main__':
    rospy.init_node('rosbag_converter')
    folder_name_list = ["YR-C01-2479EE285682_20250209_122852_2_dst","YR-C01-64A111282513_20250216_135413_3_dst"]
    for folder_name in folder_name_list:
        # 配置路径
        root_dir = './%s' % folder_name
        output_bag = '%s_compressed.bag' % folder_name
        match_json_dir = './%s/match_json_floder' % folder_name
        
        if not os.path.isdir(root_dir):
            print(f"Error: Directory not found: {root_dir}")
            exit(1)
        
        try:
            converter = RosbagConverter()
            start_time = time.time()
            converter.convert_to_bag(root_dir, output_bag, match_json_dir)
            
            # 验证结果
            print("\nVerification:")
            os.system(f"rosbag info {output_bag} | grep -E 'duration|start|end|messages'")
            print(f"\nTotal processing time: {time.time()-start_time:.1f} seconds")
            
        except Exception as e:
            print(f"Fatal error: {str(e)}")
            exit(1)
