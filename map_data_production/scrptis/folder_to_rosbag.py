# File: folder_to_rosbag.py
# Description: 
# Author: qingxiansun
# Date: 2025-04-28
# Attention: Copyright Great Wall Technology Co.Ltd
# Attention: Please refer to COPYRIGHT.txt for complete terms of copyright


#!/usr/bin/env python
import rosbag
import rospy
from std_msgs.msg import String
from sensor_msgs.msg import Image, PointCloud2
from cv_bridge import CvBridge
from sensor_msgs.msg import PointField
import json
import os
import time
import numpy as np
import cv2
import base64
import hashlib
import struct
import lzma
import zlib

class RosbagConverter:
    def __init__(self):
        self.cv_bridge = CvBridge()
        self.min_time = None  # 新增：跟踪最小时间戳
        self.max_time = None  # 新增：跟踪最大时间戳
        
    def _update_time_range(self, timestamp):
        """新增方法：更新时间范围统计"""
        if self.min_time is None or timestamp < self.min_time:
            self.min_time = timestamp
        if self.max_time is None or timestamp > self.max_time:
            self.max_time = timestamp
            
    def convert_to_bag(self, root_dir, output_bag, match_json_dir="match_json_floder"):
        """
        将数据目录、配置文件夹和匹配文件合并到ROS bag中
        
        参数:
            root_dir: 包含数据子文件夹和configs文件夹的根目录
            output_bag: 输出的bag文件名
            match_json_dir: 存放matched_file.json的目录
        """
        self.min_time = None  # 重置时间统计
        self.max_time = None

        #with rosbag.Bag(output_bag, 'w', options={'chunksize': 1024 * 1024}) as bag:
        with rosbag.Bag(output_bag, 'w') as bag:
            # 1. 先处理匹配文件
            matched_file_path = os.path.join(match_json_dir, "matched_file.json")
            if os.path.isfile(matched_file_path):
                self._process_matched_file(matched_file_path, bag)
            else:
                print(f"Warning: matched_file.json not found in {match_json_dir}")
            
            # 2. 处理configs文件夹
            configs_dir = os.path.join(root_dir, 'configs')
            if os.path.isdir(configs_dir):
                self._process_configs(configs_dir, bag)
            
            # 3. 处理其他数据文件夹
            for dir_name in sorted(os.listdir(root_dir)):
                dir_path = os.path.join(root_dir, dir_name)
                
                if dir_name in ['configs', 'match_json_floder']:
                    continue  # 已经处理过
                    
                if os.path.isdir(dir_path) and not dir_name.startswith('.'):
                    self._process_directory(dir_path, dir_name, bag)

            # 添加bag文件的全局时间信息
            if self.min_time and self.max_time:
                duration = (self.max_time - self.min_time).to_sec()
                print(f"\nFinal time range:")
                print(f"- Start: {self.min_time.to_sec()} ({self.min_time})")
                print(f"- End:   {self.max_time.to_sec()} ({self.max_time})")
                print(f"- Duration: {duration:.2f} seconds")

    def _process_matched_file(self, file_path, bag):
        """处理匹配文件（修正时间戳）"""
        try:
            with open(file_path, 'r') as f:
                matched_data = json.load(f)

            # 使用文件修改时间或当前时间
            timestamp = rospy.Time.from_sec(os.path.getmtime(file_path))
            # self._update_time_range(timestamp)

            # 只存储一个精简版本
            msg = String()
            msg.data = json.dumps({
                'file': os.path.basename(file_path),
                'md5': hashlib.md5(open(file_path, 'rb').read()).hexdigest(),
                'content': matched_data
            })

            bag.write("/match_data", msg, timestamp)

        except Exception as e:
            print(f"Error processing matched file: {str(e)}")
            raise
    # def _process_matched_file(self, file_path, bag):
    #     """专门处理matched_file.json文件"""
    #     print(f"\nProcessing matched file: {file_path}")
    #
    #     try:
    #         with open(file_path, 'r') as f:
    #             matched_data = json.load(f)
    #         # 使用文件修改时间或当前时间
    #         timestamp = rospy.Time.from_sec(os.path.getmtime(file_path))
    #         self._update_time_range(timestamp)
    #         # 创建三种格式的消息：
    #         # 1. 原始JSON内容（易读格式）
    #         json_msg = String()
    #         json_msg.data = json.dumps(matched_data, indent=2)
    #
    #         # 2. 紧凑格式（用于程序处理）
    #         compact_msg = String()
    #         compact_msg.data = json.dumps(matched_data)
    #
    #         # 3. 文件附件格式（包含完整文件信息）
    #         with open(file_path, 'rb') as f:
    #             file_content = f.read()
    #
    #         attachment_msg = String()
    #         attachment_msg.data = json.dumps({
    #             'file_name': os.path.basename(file_path),
    #             'file_path': os.path.abspath(file_path),
    #             'content': base64.b64encode(file_content).decode('utf-8'),
    #             'size_bytes': len(file_content),
    #             'md5': self._calculate_md5(file_content),
    #             'timestamp': os.path.getmtime(file_path)
    #         }, indent=2)
    #
    #         # 写入到独立的topic命名空间
    #         bag.write("/match_data/original", json_msg, timestamp)
    #         bag.write("/match_data/compact", compact_msg, timestamp)
    #         bag.write("/match_data/attachment", attachment_msg, timestamp)
    #
    #         print(f"Successfully added matched file in 3 formats:")
    #         print(f"  - /match_data/original (pretty JSON)")
    #         print(f"  - /match_data/compact (minified JSON)")
    #         print(f"  - /match_data/attachment (with full file metadata)")
    #
    #     except Exception as e:
    #         print(f"Error processing matched file {file_path}: {str(e)}")
    #         raise
    
    def _calculate_md5(self, data):
        """计算数据的MD5哈希值"""
        import hashlib
        return hashlib.md5(data).hexdigest()

    def _process_configs(self, configs_dir, bag):
        """处理配置文件（修正时间戳）"""
        for filename in sorted(os.listdir(configs_dir)):
            if not filename.endswith(('.json', '.yaml', '.yml')):
                continue

            filepath = os.path.join(configs_dir, filename)
            try:
                timestamp = rospy.Time.from_sec(os.path.getmtime(filepath))
                # self._update_time_range(timestamp)

                with open(filepath, 'r') as f:
                    content = yaml.safe_load(f) if filename.endswith(('.yaml', '.yml')) else json.load(f)

                msg = String()
                msg.data = json.dumps({
                    'file': filename,
                    'content': content
                })

                bag.write("/configs", msg, timestamp)

            except Exception as e:
                print(f"Skipped config {filename}: {str(e)}")
                continue

    # def _process_configs(self, configs_dir, bag):
    #     """处理configs文件夹中的配置文件"""
    #     print(f"\nProcessing configs directory: {configs_dir}")
    #
    #     for filename in sorted(os.listdir(configs_dir)):
    #         if filename.endswith(('.json', '.yaml', '.yml')):
    #             filepath = os.path.join(configs_dir, filename)
    #
    #             try:
    #                 with open(filepath, 'r') as f:
    #                     if filename.endswith('.json'):
    #                         config_data = json.load(f)
    #                     else:  # yaml文件
    #                         import yaml
    #                         config_data = yaml.safe_load(f)
    #
    #                 msg = String()
    #                 msg.data = json.dumps({
    #                     'file_name': filename,
    #                     'file_path': os.path.abspath(filepath),
    #                     'content': config_data,
    #                     'timestamp': os.path.getmtime(filepath)
    #                 }, indent=2)
    #
    #                 timestamp = rospy.Time.from_sec(os.path.getmtime(filepath))
    #                 # self._update_time_range(timestamp)  # 新增：更新时间范围
    #                 bag.write("/configurations", msg, timestamp)
    #                 print(f"Added config file: {filename}")
    #
    #             except Exception as e:
    #                 print(f"Error processing config file {filepath}: {str(e)}")
    #                 continue
    
    def _process_directory(self, dir_path, dir_name, bag):
        """处理数据目录"""
        data_type = self._detect_data_type(dir_path)
        
        if data_type == "json":
            self._process_json_files(dir_path, dir_name, bag)
        elif data_type == "image":
            self._process_image_files(dir_path, dir_name, bag)
        elif data_type == "pointcloud":
            self._process_pointcloud_files(dir_path, dir_name, bag)
        else:
            print(f"Warning: Unknown data type in {dir_path}")
    
    def _detect_data_type(self, dir_path):
        """检测数据类型"""
        for filename in os.listdir(dir_path):
            if filename.endswith('.json'):
                return "json"
            elif filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                return "image"
            elif filename.endswith('.bin') or filename.endswith('.pcd'):
                return "pointcloud"
        return "unknown"

    def _process_json_files(self, dir_path, topic_name, bag):
        """处理JSON文件（修正时间戳）"""
        for filename in sorted(os.listdir(dir_path)):
            if not filename.endswith('.json'):
                continue

            try:
                timestamp = self._get_valid_timestamp(filename)
                self._update_time_range(timestamp)

                with open(os.path.join(dir_path, filename), 'r') as f:
                    data = json.load(f)

                msg = String()
                msg.data = json.dumps(data)
                bag.write(f"/{topic_name}", msg, timestamp)

            except Exception as e:
                print(f"Skipped {filename}: {str(e)}")
                continue
    # def _process_json_files(self, dir_path, topic_name, bag):
    #     """处理JSON数据"""
    #     for filename in sorted(os.listdir(dir_path)):
    #         if filename.endswith('.json'):
    #             filepath = os.path.join(dir_path, filename)
    #
    #             try:
    #                 with open(filepath, 'r') as f:
    #                     json_data = json.load(f)
    #                 timestamp = self._get_valid_timestamp(filename)
    #                 self._update_time_range(timestamp)
    #                 msg = String()
    #                 msg.data = json.dumps({
    #                     'file_name': filename,
    #                     'file_path': os.path.abspath(filepath),
    #                     'content': json_data,
    #                     'timestamp': self._get_timestamp(filename).to_sec()
    #                 })
    #                 bag.write(f"/{topic_name}", msg, timestamp)
    #
    #             except Exception as e:
    #                 print(f"Error processing {filepath}: {str(e)}")
    #                 continue
    
    def _process_image_files(self, dir_path, topic_name, bag):
        """处理图片数据"""
        for filename in sorted(os.listdir(dir_path)):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                filepath = os.path.join(dir_path, filename)
                
                try:
                    cv_image = cv2.imread(filepath, cv2.IMREAD_COLOR)
                    if cv_image is None:
                        raise ValueError(f"Could not read image {filepath}")

                    if cv_image is not None:
                        msg = self.cv_bridge.cv2_to_imgmsg(cv_image, "bgr8")
                        msg.header.stamp = self._get_timestamp(filename)
                        msg.header.stamp = msg.header.stamp
                        self._update_time_range(msg.header.stamp)  # 新增：更新时间范围
                        bag.write(f"/{topic_name}/image", msg, msg.header.stamp)

                    # # 添加图片元数据
                    # meta_msg = String()
                    # meta_msg.data = json.dumps({
                    #     'file_name': filename,
                    #     'file_path': os.path.abspath(filepath),
                    #     'width': cv_image.shape[1],
                    #     'height': cv_image.shape[0],
                    #     'channels': cv_image.shape[2],
                    #     'timestamp': self._get_timestamp(filename).to_sec()
                    # })
                    #
                    # # 图片数据消息
                    # img_msg = self.cv_bridge.cv2_to_imgmsg(cv_image, encoding="bgr8")
                    #
                    #
                    # # 写入到bag
                    # bag.write(f"/{topic_name}/meta", meta_msg, img_msg.header.stamp)
                    # bag.write(f"/{topic_name}/image", img_msg, img_msg.header.stamp)
                    
                except Exception as e:
                    print(f"Error processing {filepath}: {str(e)}")
                    continue

    def _process_pointcloud_files(self, dir_path, topic_name, bag):
        """处理点云数据（针对Velodyne格式优化）"""
        for filename in sorted(os.listdir(dir_path)):
            if not filename.endswith('.pcd'):
                continue

            filepath = os.path.join(dir_path, filename)
            try:
                # 读取点云数据
                points, intensities, rings, timestamps = self._read_velodyne_pcd(filepath)

                # 创建时间戳（使用点云中的最大时间戳）
                max_timestamp = max(timestamps) if len(timestamps) > 0 else 0
                timestamp = rospy.Time.from_sec(max_timestamp)
                self._update_time_range(timestamp)

                # 点云元数据
                meta_msg = String()
                meta_msg.data = json.dumps({
                    'file_name': filename,
                    'file_path': os.path.abspath(filepath),
                    'point_count': len(points),
                    'timestamp': max_timestamp
                })

                # 创建PointCloud2消息
                pc_msg = PointCloud2()
                pc_msg.header.stamp = timestamp
                pc_msg.header.frame_id = "velodyne"  # 使用Velodyne坐标系
                pc_msg.height = 1
                pc_msg.width = len(points)

                # 根据提供的格式设置字段
                pc_msg.fields = [
                    PointField(name='x', offset=0, datatype=PointField.FLOAT32, count=1),
                    PointField(name='y', offset=4, datatype=PointField.FLOAT32, count=1),
                    PointField(name='z', offset=8, datatype=PointField.FLOAT32, count=1),
                    PointField(name='intensity', offset=12, datatype=PointField.UINT8, count=1),
                    PointField(name='ring', offset=13, datatype=PointField.UINT8, count=1),
                    PointField(name='timestamp', offset=14, datatype=PointField.UINT16, count=1)
                ]

                pc_msg.is_bigendian = False
                pc_msg.point_step = 16  # 4+4+4+1+1+2 = 16 bytes
                pc_msg.row_step = pc_msg.point_step * pc_msg.width

                # 组装二进制数据
                data = bytearray()
                for i in range(len(points)):
                    data += struct.pack('fffBBH',
                                        points[i][0], points[i][1], points[i][2],
                                        intensities[i], rings[i], timestamps[i])
                pc_msg.data = bytes(data)
                pc_msg.is_dense = True

                # 写入到bag
                bag.write(f"/{topic_name}/meta", meta_msg, pc_msg.header.stamp)
                bag.write(f"/{topic_name}/points", pc_msg, pc_msg.header.stamp)

            except Exception as e:
                print(f"Error processing {filepath}: {str(e)}")
                continue

    def _read_velodyne_pcd(self, filepath):
        """
        专门处理Velodyne特殊压缩格式的PCD文件
        解决"incorrect header check"错误
        """
        with open(filepath, 'rb') as f:
            # 1. 读取ASCII头部
            header = []
            while True:
                line = f.readline()
                try:
                    decoded_line = line.decode('ascii').strip()
                except UnicodeDecodeError:
                    decoded_line = str(line)
                header.append(decoded_line)
                if decoded_line.startswith('DATA binary_compressed'):
                    break
                if line == b'':
                    break

            # 2. 解析点数量
            points_count = 0
            for line in header:
                if line.startswith('POINTS'):
                    points_count = int(line.split()[1])
                    break

            if points_count == 0:
                raise ValueError("PCD文件没有点数据")

            # 3. 读取压缩头 (8字节: compressed_size + uncompressed_size)
            comp_header = f.read(8)
            if len(comp_header) != 8:
                raise ValueError("无效的压缩头长度")

            compressed_size, uncompressed_size = struct.unpack('<II', comp_header)

            # 4. 读取压缩数据
            compressed_data = f.read(compressed_size)
            if len(compressed_data) != compressed_size:
                raise ValueError("压缩数据长度不匹配")

            # 5. 特殊解压处理 - 使用原始zlib数据(不加头)
            try:
                # 方法1：使用decompressobj处理原始zlib数据
                decompressor = zlib.decompressobj(wbits=-zlib.MAX_WBITS)
                data = decompressor.decompress(compressed_data)
                data += decompressor.flush()
            except zlib.error as e:
                # 方法2：尝试添加标准zlib头
                try:
                    data = zlib.decompress(b'\x78\x9C' + compressed_data)
                except zlib.error:
                    # 方法3：最后尝试直接原始数据
                    try:
                        data = compressed_data
                        if len(data) != uncompressed_size:
                            raise ValueError("使用原始数据但大小不匹配")
                    except:
                        raise ValueError(f"解压失败: {str(e)}. 文件可能已损坏")

            # 6. 验证数据大小
            expected_size = points_count * 16  # 4+4+4+1+1+2=16字节/点
            if len(data) < expected_size:
                raise ValueError(f"数据不足: 需要{expected_size}字节, 只有{len(data)}字节")

            # 7. 使用numpy高效解析
            try:
                dtype = np.dtype([
                    ('x', '<f4'), ('y', '<f4'), ('z', '<f4'),
                    ('intensity', 'u1'), ('ring', 'u1'),
                    ('timestamp', '<u2')  # 注意是小端序
                ])
                points_arr = np.frombuffer(data[:expected_size], dtype=dtype)

                return (
                    np.column_stack((points_arr['x'], points_arr['y'], points_arr['z'])),
                    points_arr['intensity'].copy(),
                    points_arr['ring'].copy(),
                    points_arr['timestamp'].copy()
                )
            except Exception as e:
                raise ValueError(f"点数据解析失败: {str(e)}")
    
    def _get_timestamp(self, filename):
        """从文件名获取时间戳（修正版）"""
        try:
            ts_str = os.path.splitext(filename)[0]
            
            # 新增：处理带前缀的时间戳（如"frame_1234567890"）
            if '_' in ts_str:
                ts_str = ts_str.split('_')[-1]
            
            if len(ts_str) > 10:  # 纳秒时间戳
                seconds = int(ts_str[:10])
                nanoseconds = int(ts_str[10:19])
            else:  # 普通时间戳
                seconds = int(float(ts_str))
                nanoseconds = 0
                
            return rospy.Time(seconds, nanoseconds)
        except ValueError:
            print(f"Warning: Invalid timestamp in {filename}, using current time")
            current_time = rospy.Time.from_sec(time.time())
            # self._update_time_range(current_time)  # 新增：确保即使默认时间也被记录
            return current_time

    def _get_valid_timestamp(self, filename):
        """确保返回有效的时间戳"""
        try:
            basename = os.path.splitext(filename)[0]

            # 尝试从文件名提取时间戳（假设格式为timestamp或prefix_timestamp）
            ts_str = basename.split('_')[-1] if '_' in basename else basename

            if ts_str.isdigit():
                sec = int(ts_str[:10])  # 取前10位作为秒
                nsec = int(ts_str[10:19]) if len(ts_str) > 10 else 0
                return rospy.Time(sec, nsec)
        except:
            pass

        # 默认使用当前时间
        return rospy.Time.from_sec(time.time())

if __name__ == '__main__':
    rospy.init_node('rosbag_converter', anonymous=True)

    folder_name_list = ["YR-C01-2479EE285682_20250209_122852_2_dst", "YR-C01-64A111282513_20250216_135413_3_dst"]
    for folder_name in folder_name_list:
        # 配置路径
        root_dir = './%s' % folder_name
        output_bag = '%s.bag' % folder_name
        match_json_dir = './%s/match_json_floder' % folder_name

        if not os.path.isdir(root_dir):
            print(f"Error: Directory not found: {root_dir}")
            exit(1)

        try:
            converter = RosbagConverter()
            converter.convert_to_bag(root_dir, output_bag, match_json_dir)

            # 验证结果
            print("\nFinal verification:")
            with rosbag.Bag(output_bag, 'r') as bag:
                info = bag.get_type_and_topic_info()

                # 正确获取总消息数的方法
                total_messages = 0
                matched_file_found = False

                print(f"Topics in bag:")
                for topic_name, topic_info in info.topics.items():
                    print(f"  - {topic_name}: {topic_info.message_count} messages")
                    total_messages += topic_info.message_count
                    if topic_name.startswith('/match_data'):
                        matched_file_found = True

                print(f"\nTotal messages: {total_messages}")

                if matched_file_found:
                    print("- Successfully included matched file in /match_data topics")
                else:
                    print("- Warning: Matched file not found in bag")

        except Exception as e:
            print(f"Fatal error: {str(e)}")
            exit(1)
