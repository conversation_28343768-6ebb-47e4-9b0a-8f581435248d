1. install proj-8.2.1 
  # 安装依赖项
  sudo apt update
  sudo apt install -y \
      build-essential \
      cmake \
      git \
      libsqlite3-dev \
      libtiff-dev \
      libcurl4-openssl-dev \
      pkg-config \
      sqlite3 \
      wget \
      unzip
   # 下载 PROJ 8.2.1 源码
      wget https://download.osgeo.org/proj/proj-8.2.1.tar.gz
      tar -xzf proj-8.2.1.tar.gz
      cd proj-8.2.1
   # 编译与安装
      mkdir build
      cd build
      cmake .. \
          -DCMAKE_INSTALL_PREFIX=/usr/local/proj-8.2.1 \
          -DCMAKE_BUILD_TYPE=Release \
          -DBUILD_TESTING=OFF
      make -j$(nproc)
      sudo make install

2. install protobuf_v3.14.0
  # 生成配置脚本
  ./autogen.sh

  # 配置编译选项
  ./configure --prefix=/usr/local/protobuf-3.14.0

  # 编译 (使用多核加速)
  make -j$(nproc)

  # 运行测试 (可选)
  make check

  # 安装到系统
  sudo make install

3. install zmq:
   sudo apt update
   sudo apt install libzmq3-dev
   # 验证安装
   pkg-config --modversion libzmq

4. How to compile source code? 
   Commiand: mkdir -p build; cd build; cmake ..;make; make install;

5. How to run the program demo? 
   Enter "./build/install" directory.
   source tc_map_env.bash
   ./bin/hd_data_convertor
6. How to reference HD Map Searching dynamic library?
   Enter "./build/install" directory.
   dynamic libraries: lib/librouting_hd_map.so
   header files: ./include  ./proto
7. Debug环境运行程序
   source setup.bash
   ./build/bin/hd_data_convertor

for baidu ldmap:
1,rtk_gps.txt 存放在 /map_data_production/data/bd_data/test/raw 目录，若是新的文件需替换
2,global_GT.csv  存放在 /map_data_production/data/bd_data/test/result  若是新的文件需替换
3,生成下游所需文件在 /map_data_production/data/bd_data/test/data   若是新需求，需删除之前产生的文件