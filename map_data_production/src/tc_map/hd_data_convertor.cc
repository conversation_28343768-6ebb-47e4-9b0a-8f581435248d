#include <zmq.h>

#include <chrono>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <string>
#include <thread>

#include "common/track_info_reader.hpp"
#include "nerd-api/api/map_data_engine_api.h"
#include "nerd-api/api/nerd_utils.h"
#include "nerd_api/nerd_version.h"
#include "proto/hd_map_lane.pb.h"
#include "proto/rtk_gps.pb.h"
#include "tc_map/nerd/hd_data_manager.hpp"
#include "tc_map/nerd/hd_map_to_geojson.hpp"
#include "tc_map/route_match/route_match.hpp"
#include "util/Coordinates.h"
#include "util/argparse.hpp"
#include "util/date_time.h"
#include "util/log.hpp"
#include "util/message_io_handler.hpp"
#include "zmq/ZMQWriter.hpp"

using gwm::hdmap::GNSSCollection;
using gwm::hdmap::Lane;
using gwm::hdmap::LocationInfo;
using gwm::hdmap::RoutingLaneInfo;
using gwm::hdmap::RoutingMapInfo;
using gwm::hdmap::TrackList;
using ::nerd::api::GeoCoordinate;

int GetTestTrackList(TrackList &track_list);

//
int main(int argc, char **argv) {
  argparse::ArgumentParser program(argv[0]);
  program.add_argument("-g", "--global_gt_file_path")
      .help("global gt file path, csv format, get timestamp and yaw info.")
      .default_value("./test/track_list_data/result/global_GT.csv");
  program.add_argument("-t", "--track_file_path")
      .help("gps track file path, csv format, get gps longitude latitude info.")
      .default_value("./test/track_list_data/raw/rtk_gps.txt");
  try {
    program.parse_args(argc, argv);
  } catch (const std::runtime_error &err) {
    LOG_ERROR << err.what() << std::endl;
    LOG_ERROR << program;
    return -1;
  }
  auto global_gt_file_path = program.get<std::string>("--global_gt_file_path");
  auto track_file_path = program.get<std::string>("--track_file_path");
  if (!std::filesystem::exists(global_gt_file_path) ||
      !std::filesystem::exists(track_file_path)) {
    LOG_ERROR << "File is not existsed." << std::endl;
    LOG_ERROR << global_gt_file_path << std::endl;
    LOG_ERROR << track_file_path << std::endl;
    return -1;
  }

  tc_map::nerd::HDDataManager manager;
  std::string hd_data_dir = "./data/********/HD";
  manager.InitSDK(hd_data_dir, "./log", (24 * 30), (200 * 1024 * 1024),
                  (5 * 1024 * 1024));
  RoutingMapInfo routing_map;
  TrackList track_list;
  gwm::common::TrackInfoReader track_reader;
  auto gnss_list = track_reader.ConvertGNSSInfo(track_file_path);
  // track_list = std::move(
  //    track_reader.ConvertTrackList(global_gt_file_path, track_file_path));
  GetTestTrackList(track_list);
  manager.GetRoutingMapInfo(track_list, routing_map);
  WriteMessageToFile(track_list, "./test/hd_map_data/TrackList.bin");
  WriteMessageToFile(routing_map, "./test/hd_map_data/routing_map.bin");
  WriteMessageToFile(gnss_list, "./test/hd_map_data/rtk_gps.bin");
  tc_map::nerd::HDMapToGeoJsonHandler to_json_handler;
  auto hd_map_geo_json = to_json_handler.RoutingMapInfoToGeoJSON(routing_map);
  auto track_geo_json = to_json_handler.TrackListToGeoJSON(track_list);

  std::string hd_map_geo_json_path = "./test/hd_map_data/hd_map.geojson";
  to_json_handler.OutputToFile(hd_map_geo_json, hd_map_geo_json_path);
  std::string track_geo_json_path = "./test/hd_map_data/track_list.geojson";
  to_json_handler.OutputToFile(track_geo_json, track_geo_json_path);
  return 0;
}

int GetTestTrackList(TrackList &track_list) {
  int ret = 0;
  auto coord_helper = gwm::Coordinates::getInstance();
  std::vector<GeoCoordinate> coordinates = {
      {116.5111409, 39.7593992},   {116.********, 39.********},
      {116.5114277, 39.7598598},   {116.5116692, 39.7603560},
      {116.********, 39.********}, {116.5118319, 39.7606207},
      {116.5118895, 39.7609285},   {116.5120390, 39.7612486},
      {116.5121376, 39.7614667},   {116.5121826, 39.7615985},
      {116.5122474, 39.7617593},   {116.5123305, 39.7619567}};
  double bearing = .0;
  for (std::size_t i = 0; i < coordinates.size(); i++) {
    if (i < coordinates.size() - 1) {
      bearing = coord_helper.calculateBearing(
          coordinates[i].y, coordinates[i].x, coordinates[i + 1].y,
          coordinates[i + 1].x);
    } else {
      bearing = coord_helper.calculateBearing(
          coordinates[i - 1].y, coordinates[i - 1].x, coordinates[i].y,
          coordinates[i].x);
    }
    auto &geo = coordinates[i];
    auto loc = track_list.add_locations();
    loc->mutable_point()->set_x(geo.x);
    loc->mutable_point()->set_y(geo.y);
    loc->set_radias(5);
    loc->set_heading_degree(bearing);
    loc->set_max_tolerance_angle(20);
    loc->set_time_stamp(CurrentTimestampS());
    LOG_INFO << "pos:" << geo.x << "," << geo.y << " bearing:" << bearing
             << std::endl;
  }

  return ret;
}
