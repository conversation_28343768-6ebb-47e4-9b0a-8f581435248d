// Copyright 2020 Tencent. All rights reserved.

#include <curl/curl.h>

#include <cstring>
#include <iostream>
#include <thread>

#include "tc_map/route_match/http_interface_impl.h"
#include "util/ThreadPool.h"
#include "util/log.hpp"

namespace tc_map {
namespace route_match {
// 用于static方法转成员方法调用
struct ResponseDataCB {
  HttpImpl *instance;
  ResponseData *response_data;
};

// 用于static方法转成员方法调用
struct DownloadCallbackParamCB {
  HttpImpl *instance;
  DownloadCallbackParam *download_callback_param;
};

size_t HttpImpl::OnReceiveHeaderStatic(char *buffer, size_t size, size_t nitems,
                                       void *userdata) {
  auto *cb = (struct ResponseDataCB *)(userdata);
  return cb->instance->OnReceiveHeader(buffer, size, nitems, cb->response_data);
}

size_t HttpImpl::OnReceiveDataStatic(char *ptr, size_t size, size_t nmemb,
                                     void *userdata) {
  auto *cb = (struct ResponseDataCB *)(userdata);
  return cb->instance->OnReceiveData(ptr, size, nmemb, cb->response_data);
}

size_t HttpImpl::OnDownloadProgressStatic(char *ptr, size_t size, size_t nmemb,
                                          void *userdata) {
  auto *cb = (struct DownloadCallbackParamCB *)(userdata);
  return cb->instance->OnDownloadProgress(ptr, size, nmemb,
                                          cb->download_callback_param);
}

int HttpImpl::ProgressCallbackStatic(void *clientp, curl_off_t dltotal,
                                     curl_off_t dlnow, curl_off_t ultotal,
                                     curl_off_t ulnow) {
  auto *cb = (struct DownloadCallbackParamCB *)(clientp);
  return cb->instance->ProgressCallback(cb->download_callback_param, dltotal,
                                        dlnow, ultotal, ulnow);
}

/**
 * 参考样例
 */
HttpImpl::HttpImpl(bool conn_share) {
  conn_share_ = conn_share;
  DebugCode(LOG_INFO << "HttpImpl construct"
                     << " conn_share: " << conn_share_ << std::endl);

  if (conn_share_) {
    threadPoolPtr_ = std::make_shared<ThreadPool>(
        1);  // libcurl connection reuse mode, single thread
  } else {
    threadPoolPtr_ = std::make_shared<ThreadPool>(6);
  }

  curl_global_init(CURL_GLOBAL_DEFAULT);

  if (conn_share_) {
    InitConnectionShareCurl();
  }
}

HttpImpl::~HttpImpl() {
  if (conn_share_) {
    CleanUpConnectionShareCurl();
  }
  canceled_ids_.clear();
}

bool HttpImpl::RequestHttpGet(int32_t req_id, const std::string &url,
                              const std::map<std::string, std::string> &headers,
                              std::weak_ptr<mapbase::HttpCallback> callback,
                              int32_t timeout_ms) {
  auto f = [req_id, url, headers, callback, timeout_ms, this] {
    HttpImpl::DoHttpRequest(req_id, url, headers, nullptr, 0, callback,
                            timeout_ms);
  };
  threadPoolPtr_->enqueue(f);
  return true;
}

bool HttpImpl::RequestHttpPost(
    int32_t req_id, const std::string &url,
    const std::map<std::string, std::string> &headers,
    std::unique_ptr<int8_t[]> data, int32_t size,
    std::weak_ptr<mapbase::HttpCallback> callback, int32_t timeout_ms) {
  auto f = [req_id, url, headers, dataTmp = std::move(data), size, callback,
            timeout_ms, this]() mutable {
    HttpImpl::DoHttpRequest(req_id, url, headers, std::move(dataTmp), size,
                            callback, timeout_ms);
  };
  threadPoolPtr_->enqueue(std::move(f));
  return true;
}
inline void splitString(const std::string &str, const std::string &delimiters,
                        std::vector<std::string> &tokens) {
  std::string::size_type pos, lastPos = 0;
  while ((pos = str.find_first_of(delimiters, lastPos)) != std::string::npos) {
    if (pos > lastPos) {
      tokens.push_back(str.substr(lastPos, pos - lastPos));
    }
    lastPos = pos + 1;
  }

  if (lastPos < str.length()) {
    tokens.push_back(str.substr(lastPos));
  }
}

inline void trimString(std::string &s) {
  if (s.empty()) {
    return;
  }
  s.erase(0, s.find_first_not_of(' '));
  s.erase(s.find_last_not_of(' ') + 1);
}

inline void parseResponseHeader(
    struct ResponseData *header_data,
    std::map<std::string, std::vector<std::string>> &headers) {
  std::string headerStr(header_data->GetData(), header_data->GetSize());
  std::vector<std::string> lines;
  splitString(headerStr, "\r\n", lines);
  for (auto &line : lines) {
    size_t colonPos = line.find(':');

    if (colonPos != std::string::npos) {
      std::string key = line.substr(0, colonPos);
      std::string value = line.substr(colonPos + 1);
      trimString(key);
      trimString(value);

      headers[key].push_back(value);
    }
  }
}

void HttpImpl::DoHttpRequest(
    int32_t req_id, const std::string &url,
    const std::map<std::string, std::string> &headers,
    std::unique_ptr<int8_t[]> data, int32_t size,
    const std::weak_ptr<mapbase::HttpCallback> &callback, int32_t timeout_ms) {
  CURL *curl;
  CURLcode res;

  if (conn_share_) {
    curl = conn_share_curl;
  } else {
    curl = curl_easy_init();
  }

  if (curl) {
    DebugCode(LOG_INFO << "url: " << url.c_str() << std::endl);
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

    if (conn_share_) {
      // enable TCP keep-alive for this transfer
      curl_easy_setopt(curl, CURLOPT_TCP_KEEPALIVE, 1L);
      // keep-alive idle time to 120 seconds
      curl_easy_setopt(curl, CURLOPT_TCP_KEEPIDLE, 120L);
      // interval time between keep-alive probes: 60 seconds
      curl_easy_setopt(curl, CURLOPT_TCP_KEEPINTVL, 60L);
      // 防止关闭连接
      curl_easy_setopt(curl, CURLOPT_FORBID_REUSE, 0L);
    }

    // VERBOSE all log for DEBUG
#ifndef NDEBUG
    /* libcurl verbose output */
    //    curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);

    // https://curl.se/libcurl/c/curl_global_trace.html
    // curl_global_trace("all");  // curl 8 api for test
#endif

    // 设置默认DNS服务器
    const std::string SERVERS(
        "182.254.116.116,182.254.118.118,8.8.8.8,8.8.4.4,114.114.114.114,101."
        "226.4.6,218.30.118.6,"
        "123.125.81.6,140.207.198.6,112.124.47.27,42.120.21.30,10.36.249.15,10."
        "36.249.16,10.11.56.22");
    curl_easy_setopt(curl, CURLOPT_DNS_SERVERS, SERVERS.c_str());

    curl_easy_setopt(curl, CURLOPT_DNS_CACHE_TIMEOUT, -1);

    // avoid multi-thread bug of curl: dns parse timeout among multi threads may
    // send signal to stop program
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);

    curl_easy_setopt(curl, CURLOPT_TCP_NODELAY, 1L);
    // re-location
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);

    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

    // 自签名证书功能启用时，去掉此注释，让下面的代码生效
    //    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 1L);
    //    curl_easy_setopt(curl, CURLOPT_CAINFO, "/path/to/your/cert/file");
    //    //改成证书存放的真实路径，需要绝对路径

    curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, timeout_ms);

    struct curl_slist *header_list = nullptr;
    auto it = headers.begin();
    for (; it != headers.end(); ++it) {
      std::string header = it->first + ": " + it->second;
      header_list = curl_slist_append(header_list, header.c_str());
    }
    if (conn_share_) {
      // add http keep-alive header
      header_list = curl_slist_append(header_list, "Connection: keep-alive");
      header_list =
          curl_slist_append(header_list, "Keep-Alive: timeout=60, max=120");
    }
    if (header_list) {
      curl_easy_setopt(curl, CURLOPT_HTTPHEADER, header_list);
    }
    curl_easy_setopt(curl, CURLOPT_ACCEPT_ENCODING, "gzip");
    if (data) {
      curl_easy_setopt(curl, CURLOPT_POST, 1L);
      curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.get());
      curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, size);
    } else {
      curl_easy_setopt(curl, CURLOPT_HTTPGET, 1L);
    }

    struct ResponseData chunk(req_id);
    ResponseDataCB data_cb = {this, &chunk};
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION,
                     HttpImpl::OnReceiveDataStatic);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &data_cb);  // set callback params

    struct ResponseData header_data(req_id);
    ResponseDataCB header_cb = {this, &header_data};
    curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION,
                     HttpImpl::OnReceiveHeaderStatic);
    curl_easy_setopt(curl, CURLOPT_HEADERDATA, &header_cb);

    res = curl_easy_perform(curl);

    if (res != CURLE_OK) {
      fprintf(stderr, "curl_easy_perform() failed: %s\n",
              curl_easy_strerror(res));
      std::shared_ptr<mapbase::HttpCallback> http_callback = callback.lock();
      if (http_callback) {
        int32_t error = -1;
        if (res == CURLE_OPERATION_TIMEDOUT) {
          error = 1;
        } else {
          bool need_cancel = false;
          lock_.lock();
          if (canceled_ids_.find(req_id) != canceled_ids_.end()) {
            need_cancel = true;
          }
          lock_.unlock();
          if (need_cancel) {
            error = 2;
            canceled_ids_.erase(req_id);
          }
        }
        http_callback->OnHttpError(req_id, error);
      }
    } else {
      int64_t response_code;
      curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);

      std::map<std::string, std::vector<std::string>> tmp_headers;
      parseResponseHeader(&header_data, tmp_headers);

      std::shared_ptr<mapbase::HttpCallback> http_callback = callback.lock();
      if (http_callback) {
        if (chunk.GetData() != nullptr) {
          std::unique_ptr<int8_t[]> tmp_data =
              std::make_unique<int8_t[]>(chunk.GetSize());
          memcpy(tmp_data.get(), chunk.GetData(), chunk.GetSize());

          if (weak_network_delay_ms_ > 0) {
            DebugCode(LOG_INFO << "this thread sleep_for :"
                               << weak_network_delay_ms_ << " ms" << std::endl);
            std::this_thread::sleep_for(
                std::chrono::milliseconds(weak_network_delay_ms_));
          }

          http_callback->OnHttpResponse(req_id, (int32_t)response_code,
                                        std::move(tmp_data),
                                        (int32_t)chunk.GetSize(), tmp_headers);
        } else {
          http_callback->OnHttpResponse(req_id, (int32_t)response_code, nullptr,
                                        0, tmp_headers);
        }
      }
    }

    if (header_list) {
      curl_slist_free_all(header_list);
    }

    if (!conn_share_) {
      curl_easy_cleanup(curl);
    } else {
      // curl_easy_cleanup will be called cleanup_conn_share_curl if conn_share_
      // = true
    }

    DebugCode(LOG_INFO << "request end" << std::endl);
  }
}

size_t HttpImpl::OnReceiveHeader(char *buffer, size_t size, size_t nitems,
                                 void *userdata) {
  size_t real_size = size * nitems;
  auto *mem = (struct ResponseData *)userdata;

  //  DebugCode(LOG_INFO << "OnReceiveHeader, size: " << real_size << ", header:
  //  " << buffer << std::endl);

  int32_t req_id = mem->GetReqId();
  bool need_cancel = false;
  lock_.lock();
  if (canceled_ids_.find(req_id) != canceled_ids_.end()) {
    DebugCode(LOG_INFO << "OnReceiveHeader, need cancel, req id: " << req_id
                       << std::endl);
    need_cancel = true;
  }
  lock_.unlock();

  if (need_cancel || !mem->Append(buffer, real_size)) return 0;

  return real_size;
}

size_t HttpImpl::OnReceiveData(char *ptr, size_t size, size_t nmemb,
                               void *userdata) {
  size_t real_size = size * nmemb;
  auto *mem = (struct ResponseData *)userdata;
  DebugCode(LOG_INFO << "OnReceiveData, size: " << real_size << std::endl);
  int32_t req_id = mem->GetReqId();
  bool need_cancel = false;
  lock_.lock();
  if (canceled_ids_.find(req_id) != canceled_ids_.end()) {
    DebugCode(LOG_INFO << "OnReceiveData, need cancel, req id: " << req_id
                       << std::endl);
    need_cancel = true;
  }
  lock_.unlock();

  if (need_cancel || !mem->Append(ptr, real_size)) return 0;

  return real_size;
}

size_t HttpImpl::OnDownloadProgress(char *ptr, size_t size, size_t nmemb,
                                    void *userdata) {
  size_t real_size = size * nmemb;
  auto *param = reinterpret_cast<DownloadCallbackParam *>(userdata);
  DebugCode(LOG_INFO << "OnDownloadProgress, size: " << real_size
                     << ", data: " << ptr << std::endl);
  bool need_cancel = false;
  lock_.lock();
  if (canceled_ids_.find(param->req_id) != canceled_ids_.end()) {
    DebugCode(LOG_INFO << "OnDownloadProgress, need cancel, req id: "
                       << param->req_id << std::endl);
    need_cancel = true;
  }
  lock_.unlock();

  if (!need_cancel) {
    std::shared_ptr<mapbase::DownloadCallback> http_callback =
        param->callback.lock();
    if (http_callback) {
      uint64_t response_code;
      curl_easy_getinfo(param->curl, CURLINFO_RESPONSE_CODE, &response_code);
      std::unique_ptr<int8_t[]> data = std::make_unique<int8_t[]>(real_size);
      memcpy(data.get(), ptr, real_size);
      http_callback->OnDownloadProgress(param->req_id, (int32_t)response_code,
                                        std::move(data),
                                        static_cast<int32_t>(real_size));
    }

    return real_size;
  } else {
    return 0;
  }
}

int HttpImpl::ProgressCallback(void *clientp, curl_off_t dltotal,
                               curl_off_t dlnow, curl_off_t ultotal,
                               curl_off_t ulnow) {
  auto *param = reinterpret_cast<DownloadCallbackParam *>(clientp);

  bool need_cancel = false;
  lock_.lock();
  if (canceled_ids_.find(param->req_id) != canceled_ids_.end()) {
    DebugCode(LOG_INFO << "OnReceiveHeader, need cancel, req id: "
                       << param->req_id << std::endl);
    need_cancel = true;
  }
  lock_.unlock();

  if (need_cancel) return 1;

  std::shared_ptr<mapbase::DownloadCallback> http_callback =
      param->callback.lock();
  if (http_callback) {
    int64_t response_code;
    curl_easy_getinfo(param->curl, CURLINFO_RESPONSE_CODE, &response_code);
    // 适配低版本curl，CURLINFO_SIZE_DOWNLOAD_T替换为CURLINFO_SIZE_DOWNLOAD
    double download_size;
    curl_easy_getinfo(param->curl, CURLINFO_SIZE_DOWNLOAD, &download_size);
    //    DebugCode(LOG_INFO << "ProgressCallback, dltotal: " << dltotal << ",
    //    dlnow: "
    //    << dlnow << ", code: " << response_code
    //              << ", download_size: " << download_size << ", req_id: " <<
    //              param->req_id << std::endl);
    http_callback->OnDownloadProgress(param->req_id, (int32_t)response_code,
                                      nullptr, static_cast<int>(download_size));
  }

  return 0;
}

bool HttpImpl::Download(int32_t req_id, const std::string &url,
                        const std::map<std::string, std::string> &headers,
                        std::weak_ptr<mapbase::DownloadCallback> callback,
                        const std::string &file_path, int32_t timeout_ms) {
  bool conn_share = conn_share_;
  auto f = [this, req_id, url, headers, callback, file_path, timeout_ms,
            conn_share] {
    HttpImpl::DoDownload(req_id, url, headers, callback, file_path, timeout_ms,
                         conn_share);
  };
  threadPoolPtr_->enqueue(f);
  return true;
}

void HttpImpl::DoDownload(
    int32_t req_id, const std::string &url,
    const std::map<std::string, std::string> &headers,
    const std::weak_ptr<mapbase::DownloadCallback> &callback,
    const std::string &file_path, int32_t timeout_ms, bool conn_share) {
  CURL *curl;
  CURLcode res;

  if (conn_share) {
    curl = conn_share_curl;
  } else {
    curl = curl_easy_init();
  }

  if (curl) {
    DebugCode(LOG_INFO << "url: " << url.c_str() << std::endl);
    curl_easy_setopt(curl, CURLOPT_URL, url.c_str());

    // 设置默认DNS服务器
    const std::string SERVERS(
        "182.254.116.116,182.254.118.118,8.8.8.8,8.8.4.4,114.114.114.114,101."
        "226.4.6,218.30.118.6,"
        "123.125.81.6,140.207.198.6,112.124.47.27,42.120.21.30");
    curl_easy_setopt(curl, CURLOPT_DNS_SERVERS, SERVERS.c_str());

    curl_easy_setopt(curl, CURLOPT_DNS_CACHE_TIMEOUT, -1);

    // avoid multi-thread bug of curl: dns parse timeout among multi threads may
    // send signal to stop program
    curl_easy_setopt(curl, CURLOPT_NOSIGNAL, 1L);

    curl_easy_setopt(curl, CURLOPT_TCP_NODELAY, 1L);
    // re-location
    curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);

    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

    // 自签名证书功能启用时，去掉此注释，让下面的代码生效
    //    curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 1L);
    //    curl_easy_setopt(curl, CURLOPT_CAINFO, "/path/to/your/cert/file");
    //    //改成证书存放的真实路径，需要绝对路径

    curl_easy_setopt(curl, CURLOPT_TIMEOUT_MS, timeout_ms);

    struct curl_slist *header_list = nullptr;
    auto it = headers.begin();
    for (; it != headers.end(); ++it) {
      std::string header = it->first + ": " + it->second;
      header_list = curl_slist_append(header_list, header.c_str());
    }
    if (header_list) {
      curl_easy_setopt(curl, CURLOPT_HTTPHEADER, header_list);
    }
    // curl_easy_setopt(curl, CURLOPT_ACCEPT_ENCODING, "gzip");
    curl_easy_setopt(curl, CURLOPT_HTTPGET, 1L);

    DownloadCallbackParam param;
    param.callback = callback;
    param.curl = curl;
    param.req_id = req_id;

    DownloadCallbackParamCB download_callback_param_cb = {this, &param};

    FILE *file = nullptr;
    if (file_path.empty()) {
      curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION,
                       HttpImpl::OnDownloadProgressStatic);
      curl_easy_setopt(curl, CURLOPT_WRITEDATA,
                       &download_callback_param_cb);  // set callback params
    } else {
      DebugCode(LOG_INFO << "path: " << file_path.c_str() << std::endl);
      file = fopen(file_path.c_str(), "ab+");
      if (file == nullptr) {
        if (header_list) {
          curl_slist_free_all(header_list);
        }
        curl_easy_cleanup(curl);
        std::shared_ptr<mapbase::DownloadCallback> http_callback =
            callback.lock();
        if (http_callback) {
          http_callback->OnDownloadError(req_id, 2);
        }
        return;
      }
      curl_easy_setopt(curl, CURLOPT_WRITEDATA, file);

      curl_easy_setopt(curl, CURLOPT_XFERINFOFUNCTION,
                       HttpImpl::ProgressCallbackStatic);
      curl_easy_setopt(curl, CURLOPT_XFERINFODATA, &param);
      curl_easy_setopt(curl, CURLOPT_NOPROGRESS, 0L);
    }

    struct ResponseData header_data(req_id);
    curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION,
                     HttpImpl::OnReceiveHeaderStatic);
    curl_easy_setopt(curl, CURLOPT_HEADERDATA, &header_data);

    res = curl_easy_perform(curl);

    if (file) {
      fclose(file);
    }
    if (res != CURLE_OK) {
      fprintf(stderr, "curl_easy_perform() failed: %s\n",
              curl_easy_strerror(res));
      std::shared_ptr<mapbase::DownloadCallback> http_callback =
          callback.lock();
      if (http_callback) {
        int32_t error = -1;
        if (res == CURLE_OPERATION_TIMEDOUT) {
          error = 1;
        } else if (res == CURLE_WRITE_ERROR) {
          error = 3;
        } else {
          bool need_cancel = false;
          lock_.lock();
          if (canceled_ids_.find(req_id) != canceled_ids_.end()) {
            need_cancel = true;
          }
          lock_.unlock();
          if (need_cancel) {
            error = 2;
            canceled_ids_.erase(req_id);
          }
        }
        http_callback->OnDownloadError(req_id, error);
      }
    } else {
      std::shared_ptr<mapbase::DownloadCallback> http_callback =
          callback.lock();
      if (http_callback) {
        http_callback->OnDownloadComplete(req_id);
      }
    }

    if (header_list) {
      curl_slist_free_all(header_list);
    }

    if (!conn_share) {
      curl_easy_cleanup(curl);
    } else {
      // curl_easy_cleanup will be called cleanup_conn_share_curl if conn_share_
      // = true
    }
  }
}

bool HttpImpl::CancelRequest(int32_t req_id) {
  DebugCode(LOG_INFO << "HttpImpl::CancelRequest, req id: " << req_id
                     << std::endl);
  canceled_ids_.insert(req_id);
  return true;
}

bool HttpImpl::RequestHttpUpload(
    int32_t reqId, const std::string &url,
    const std::map<std::string, std::string> &headers,
    const std::string &file_path, uint64_t file_size,
    std::weak_ptr<mapbase::HttpCallback> callback, int32_t timeout_ms) {
  return false;
}

void HttpImpl::InitConnectionShareCurl() {
  if (!conn_share_curl) {
    conn_share_curl = curl_easy_init();
  }
}

void HttpImpl::CleanUpConnectionShareCurl() {
  if (conn_share_curl) {
    curl_easy_cleanup(conn_share_curl);
  }
}

}  // namespace route_match
}  // namespace tc_map
