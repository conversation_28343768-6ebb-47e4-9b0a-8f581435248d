
#include "tc_map/nerd/TypeAdapter.hpp"
#include "tc_map/route_match/route_match.hpp"
#include "util/Coordinates.h"
#include "util/log.hpp"

namespace tc_map {
namespace route_match {
//
const double kDegreesToRadians = M_PI / 180.0;
const double kRadiansToDegrees = 180.0 / M_PI;

auto &coord = gwm::Coordinates::getInstance();
// 计算两点之间的距离
double RouteMatchClient::calculateDistance(const tca::Point &p1,
                                           const tca::Point &p2) {
  const double R = 6371000;  // 地球半径，单位：米
  double lat1 = p1.lat / coord.LNG_LAT_MOD * kDegreesToRadians;
  double lat2 = p2.lat / coord.LNG_LAT_MOD * kDegreesToRadians;
  double dLat = (p2.lat / coord.LNG_LAT_MOD - p1.lat / coord.LNG_LAT_MOD) *
                kDegreesToRadians;
  double dLng = (p2.lng / coord.LNG_LAT_MOD - p1.lng / coord.LNG_LAT_MOD) *
                kDegreesToRadians;

  double a = sin(dLat / 2) * sin(dLat / 2) +
             cos(lat1) * cos(lat2) * sin(dLng / 2) * sin(dLng / 2);
  double c = 2 * atan2(sqrt(a), sqrt(1 - a));

  return R * c;
}

// 计算路线长度
uint32_t RouteMatchClient::calculatePathLength(
    const std::vector<tca::Point> &points) {
  uint32_t length = 0;
  for (size_t i = 1; i < points.size(); ++i) {
    length +=
        static_cast<uint32_t>(calculateDistance(points[i - 1], points[i]));
  }
  return length;
}

// 将route_points转换为Point结构体的向量
std::vector<tca::Point> RouteMatchClient::convertRoutePointsToPoints(
    const std::vector<double> &route_points_vec) {
  std::vector<tca::Point> points;
  for (size_t i = 0; i < route_points_vec.size(); i += 2) {
    tca::Point point;
    point.lng = static_cast<uint32_t>(route_points_vec[i] * coord.LNG_LAT_MOD);
    point.lat =
        static_cast<uint32_t>(route_points_vec[i + 1] * coord.LNG_LAT_MOD);
    points.push_back(point);
  }
  return points;
}

// 读取轨迹文件
std::vector<double> RouteMatchClient::routePoint(
    const std::string &input_json) {
  std::ifstream json_config_file_is(input_json);
  if (!json_config_file_is.good()) {
    DebugCode(LOG_INFO << "input json file not exist : " << input_json
                       << std::endl);
    return {};
  } else {
    DebugCode(LOG_INFO << "gps track file is " << input_json << std::endl);
  }

  nlohmann::json j;
  json_config_file_is >> j;
  auto pnts = j["route_data"]["pnts"];

  std::vector<double> output{};
  for (const auto &pnt : pnts) {
    output.push_back(pnt["x"]);
    output.push_back(pnt["y"]);
  }
  if (json_config_file_is.is_open()) {
    json_config_file_is.close();
  }
  return output;
}

// 计算两点和正北的夹角
float RouteMatchClient::calculateBearing(double lat1, double lon1, double lat2,
                                         double lon2) {
  lat1 *= kDegreesToRadians;
  lon1 *= kDegreesToRadians;
  lat2 *= kDegreesToRadians;
  lon2 *= kDegreesToRadians;

  double dLon = lon2 - lon1;
  double y = sin(dLon) * cos(lat2);
  double x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(dLon);
  double bearing_rad = atan2(y, x);

  auto bearing_deg =
      static_cast<float>(fmod(bearing_rad * kRadiansToDegrees + 360.0, 360.0));
  return bearing_deg;
}

int RouteMatchClient::Init(const std::string &json_config_file_path) {
  tc_points_.reserve(15000);
  tc_links_.reserve(5000);
  std::shared_ptr<mapbase::HttpInterface> http_interface =
      std::make_shared<tc_map::route_match::HttpImpl>();
  tencent_cloud_atlas_provider_ =
      std::make_unique<tca::TencentCloudAtlasProvider>();
  auto ret = tencent_cloud_atlas_provider_->Init(json_config_file_path,
                                                 http_interface);
  if (tca::ErrorCode::Success != ret) {
    DebugCode(LOG_INFO << "error code:" << ret << std::endl);
    return -1;
  }
  return 0;
}

int RouteMatchClient::TestDemo(const std::string &gnss_file_path) {
  DebugCode(LOG_INFO << "=========demo start========" << std::endl);
  kGnssTrackFile_ = gnss_file_path;

  tca::MMRequest mm_request;
  mm_request.Reset();

  // 读取轨迹文件
  auto route_points = routePoint(kGnssTrackFile_);
  if (route_points.empty()) {
    DebugCode(LOG_INFO << "route point empty!!" << std::endl);
    return -1;
  }
  // 转换route_points为Point结构体的向量
  auto points = convertRoutePointsToPoints(route_points);
  // 计算路线长度
  uint32_t path_length = calculatePathLength(points);

  // 填充mm_request
  // mm_request.type = tca::TargetType::HD;
  mm_request.type = tca::TargetType::SD;
  mm_request.heterogeneous_hd_data_version = "**********";
  tca::NaviPath navi_path;
  navi_path.Reset();
  navi_path.path_id = "168168168";
  navi_path.length = path_length;
  navi_path.coors = points;
  mm_request.path.emplace_back(navi_path);

  auto my_matching_heterogeneous_listener =
      std::make_unique<MyMatchingListener>();
  tencent_cloud_atlas_provider_->AddHeterogeneousMatchingListener(
      my_matching_heterogeneous_listener.get());
  tencent_cloud_atlas_provider_->HeterogeneousMatchingRequest(mm_request);

  // demo sleep for MyMatchingListener callback
  int max_sleep_sec = 30;
  while (max_sleep_sec-- > 0) {
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }

  DebugCode(LOG_INFO << "=========demo end========" << std::endl);

  return 0;
}

int RouteMatchClient::GetMatchedMapRoads(
    const gwm::sdmap::SDMap &source_roads,
    std::vector<tca::TxLink> &matched_links) {
  int ret = 0;
  tca::MMRequest mm_request;
  mm_request.Reset();
  tc_points_.clear();
  tc_links_.clear();
  mm_request.type = tca::TargetType::SD;
  mm_request.heterogeneous_hd_data_version = "**********";
  tca::NaviPath navi_path;
  auto &roads = source_roads.road();
  std::cout << "All points:";
  for (auto &road : roads) {
    tc_links_.resize(tc_links_.size() + 1);
    auto &tc_link = tc_links_.back();
    auto &points = road.points();
    tc_link.coor_idx = tc_points_.size();  // 当前link在路线中形点起始位置
    tc_link.coor_num = points.size();      // 当前link在路线中形点数量
    tc_link.length = road.length();        // 道路长度，单位米
    tc_link.lane_num = road.lane_count();  // 当前link的车道数
    // tc_link.linkType = ; // 道路类型 0-普通道路 1-航道 2-隧道 3-桥梁 4-高架桥
    tc_link.roadClass = tc_map::nerd::TypeAdapter::ConvertRoadTypeToTencent(
        road.road_type());  //
    tc_link.roadType = tc_map::nerd::TypeAdapter::ConvertRoadFormwayToTencent(
        road.road_form());  //
    // tc_link.roadName = ; // 道路名
    // tc_link.is_both_direction = ; // 前link是否双方向,如果双方向,lane_num无效
    // tc_link.is_emergency_lane = ; // 当前link的车道数是否包含应急车道
    // tc_link.has_multi_out = ; // 当前link段是否有岔路 0:未知,1:是,2:否
    // tc_link.has_parallel_link = ; // 当前link段是否有平行路 0:未知,1:是,2:否
    // tc_link.has_traffic_light = ; // link沿行车方向终点是否有交通灯
    // 0:未知,1:是,2:否 tc_link.is_tool = ; // 是否收费道路 0:未知,1:是,2:否
    // tc_link.is_at_service = ; // 是否在服务区 0:未知,1:是,2:否
    // tc_link.get_inner_road = ; // 是否小区内部路 0:未知,1:是,2:否
    // tc_link.is_parking_road = ; // 是否停车厂内部路 0:未知,1:是,2:否
    // tc_link.road_slop_info = ; // 坡度信息 0:未知,1:上坡,2:平坡,3:下坡
    // tc_link.reserved_info = ; // 预留字段 map<string,string>
    for (auto src_point : points) {
      tc_points_.resize(tc_points_.size() + 1);
      auto &tc_point = tc_points_.back();
      tc_point.lng = static_cast<uint32_t>(src_point.x() * coord.LNG_LAT_MOD);
      tc_point.lat = static_cast<uint32_t>(src_point.y() * coord.LNG_LAT_MOD);
      // DebugCode(LOG_INFO << std::fixed << std::setprecision(2) <<
      // tc_point.lng << ","
      //         << tc_point.lat << ",";
      DebugCode(LOG_INFO << std::fixed << std::setprecision(8) << src_point.x()
                         << "," << src_point.y() << ",");
    }
  }
  std::cout << std::endl;
  DebugCode(LOG_INFO << "tc_points:" << tc_points_.size() << std::endl);
  uint32_t path_length = calculatePathLength(tc_points_);
  navi_path.Reset();
  navi_path.path_id = "168168168";
  navi_path.length = path_length;
  std::swap(navi_path.coors, tc_points_);
  std::swap(navi_path.links, tc_links_);
  tc_points_.clear();
  tc_links_.clear();
  mm_request.path.emplace_back(navi_path);
  DebugCode(LOG_INFO << "mm_request:" << mm_request.ToString() << std::endl);

  auto my_matching_heterogeneous_listener =
      std::make_unique<MyMatchingListener>();
  tencent_cloud_atlas_provider_->AddHeterogeneousMatchingListener(
      my_matching_heterogeneous_listener.get());
  tencent_cloud_atlas_provider_->HeterogeneousMatchingRequest(mm_request);
  my_matching_heterogeneous_listener->Wait();
  auto &result = my_matching_heterogeneous_listener->GetResult();
  auto &res = result.mm_response;
  ret = res.errcode;
  if (res.errcode == 0) {
    auto &routes = res.result;
    for (auto &route : routes) {
      if (route.path_errcode == 0) {
        auto &link_groups = route.link_groups;
        for (auto &link_group : link_groups) {
          auto &links = link_group.linkinfo;
          matched_links.insert(matched_links.end(), links.begin(), links.end());
          for (auto &link : links) {
            DebugCode(LOG_INFO
                      << "raw link_id:" << link.raw_link_id
                      << " dir:" << link.dir << "length:" << link.length
                      << " lane_number:" << link.lane_number
                      << " tpid.status:" << link.tpid.status << " tpid.tile_id:"
                      << link.tpid.tile_id << " tp link id:" << link.tpid.id
                      << " points.size:" << link.points.size() << std::endl);
            auto &points = link.points;
            DebugCode(LOG_INFO << "points: ");
            for (auto &poc : points) {
              DebugCode(LOG_INFO << poc.lng << "," << poc.lat << ",");
            }
            DebugCode(LOG_INFO << std::endl);
          }
          DebugCode(LOG_INFO << "link_group status:" << link_group.status
                             << " bindpoint_idx: " << link_group.bindpoint_idx
                             << " bindpoint_num:" << link_group.bindpoint_num
                             << " start_poi" << link_group.start_loc.lng << ","
                             << link_group.start_loc.lat
                             << " end_loc: " << link_group.end_loc.lng << ","
                             << link_group.end_loc.lat
                             << " length:" << link_group.length << std::endl);
        }
        auto &bind_points = route.bind_points;
        for (auto &bind_point : bind_points) {
          DebugCode(LOG_INFO
                    << "bind point status:" << bind_point.status
                    << " raw_link_id:" << bind_point.raw_link_id << " dir"
                    << bind_point.dir << " timestamp:" << bind_point.timestamp
                    << " point:" << bind_point.point.lng << ","
                    << bind_point.point.lat << " offset:" << bind_point.offset
                    << " distance:" << bind_point.distance << std::endl);
        }
        DebugCode(LOG_INFO << "path_errcode:" << route.path_errcode
                           << " data_version" << route.data_version
                           << " path_id" << route.path_id
                           << " msg:" << route.path_msg << std::endl);
      } else {
        LOG_ERROR << "path_errcode:" << route.path_errcode << " data_version"
                  << route.data_version << " path_id" << route.path_id
                  << " msg:" << route.path_msg << std::endl;
      }
    }
  } else {
    LOG_ERROR << "error code:" << res.errcode << " msg:" << res.msg
              << std::endl;
  }

  // DebugCode(LOG_INFO << "OnHeterogeneousMatching2,mm::" <<
  // result.mm_response.ToString()
  //         << std::endl);

  return ret;
}

void MyMatchingListener::OnHeterogeneousMatching(
    const tca::MMGuideResponse &result) {
  resp_ = std::make_unique<tca::MMGuideResponse>(std::move(result));
  const double THRESHOLD = 0.00001f;
  std::ostringstream oss;
  oss.precision(10);
  oss << "HeterogeneousMatching:";
  if (result.mm_response.result.empty()) {
    DebugCode(LOG_INFO << "mm match response failed!" << std::endl);
    p_.set_value();
    return;
  }
  p_.set_value();
}
}  // namespace route_match
}  // namespace tc_map
