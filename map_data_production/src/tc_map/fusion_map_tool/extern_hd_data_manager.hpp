// Copyright 2023 Tencent Inc. All rights reserved.

#pragma once
#include <cstdint>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <set>
#include <sstream>
#include <string>
#include <vector>

#include "cloud-atlas/tencent_cloud_atlas_typedef.h"
#include "common/header.pb.h"
#include "map/map_fusion.pb.h"
#include "nerd-api/api/map_data_engine_api.h"
#include "nerd-api/api/nerd_utils.h"
#include "nerd_api/nerd_version.h"
#include "perception/gwm_pcpt_lane.pb.h"
#include "tc_map/nerd/callback_object.hpp"
#include "util/date_time.h"
#include "util/log.hpp"
#include "utm_to_trf_converter.h"
#include "vehicle/vehicle_state.pb.h"

namespace tc_map {
namespace nerd {

struct Point3D {
  double x;
  double y;
  double z;
};

struct LocationInfo {
  Point3D point;
  double radias;
  double heading_degree;
  double max_tolerance_angle;
  double time_stamp;
};

using namespace gwm::pcpt::lane;
using namespace gwm::vehicle;
using namespace gwm::fusionmap;
using gwm::pcpt::lane::PcptLaneDetectionList;
using ::nerd::api::GeoCoordinate;
using ::nerd::api::IBoundaryConstPtr;
using ::nerd::api::ILaneConstPtr;
using ::nerd::api::RoadClassType;
using ::nerd::api::RoadKindTypeVec;

typedef std::unordered_map<const LocationInfo *, std::shared_ptr<RectCallback>>
    DataSources;

class HDDataManager {
public:
  HDDataManager();

  ~HDDataManager();

  int InitSDK(const std::string &data_dir, const std::string &log_dir,
              long log_expiration_hours, long max_log_store_bytes,
              long max_signle_log_file_bytes);

  int BuildFusionMapInfo(std::vector<LocationInfo> &track_list,
                         std::vector<MapFusionResult> &fusion_map_list);

private:
  std::set<::nerd::api::TileIDType> get_tile_ids(std::string txt_file);

  void getHDMapElement(::nerd::api::IHDLaneLayer *hdLayer);

  void getHDMapDataByRect(const ::nerd::api::GeoCoordinate &center_point,
                          int radius = 500);

  void getHDMapDataByTileID(::nerd::api::TileIDType tile_id);

  void getHDMapData(const ::nerd::api::GeoCoordinate &center_point, int radius,
                    ::nerd::api::TileIDType tileId);

  void getAroundTiles(::nerd::api::TileIDType id);

  int LoadDemo();

  int SetLaneBoundary(IBoundaryConstPtr tc_lane_boundary,
                      PcptLaneDetectionList &lane_list);

  int SetRoadBoundary(IBoundaryConstPtr tc_road_boundary,
                      PcptLaneDetectionList &lane_list);

  // double GetSpeedBySpeedType(::nerd::api::SpeedType speed_type);

  int SetPcptLaneInfo(ILaneConstPtr tc_lane, PcptLaneDetectionList &lane_list);

  int GetDataSource(const LocationInfo &loc, DataSources &data_sources);

  int FillPcptLaneDetectionList(::nerd::api::ILaneGroupConstPtr tc_lane_group,
                                PcptLaneDetectionList &lane_list);

private:
  std::unique_ptr<::nerd::api::MapDataEngineApi> lane_api_;
  std::unique_ptr<UTMToVRFConverter> vrf_converter_;
  uint32_t global_track_id_ = 0;
  uint32_t global_center_line_id = 0;
  std::set<uint32_t> lane_boundary_id_list_;
  std::set<uint32_t> road_boundary_id_list_;
  std::set<uint32_t> recommended_lane_id_list_;
};
}  // namespace nerd
}  // namespace tc_map
