/*
 * File: utm_to_trf_converter.cc
 * Description:
 * Author: qingxiansun
 * Date: 2025-04-10
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include <cmath>
#include <iostream>
#include <vector>

#include "utm_to_trf_converter.h"

// 设置车辆当前位置和姿态
void UTMToVRFConverter::setVehiclePose(const VehiclePose& pose) {
  vehicle_pose_ = pose;
}

// 将UTM点转换为VRF坐标
VRFPoint UTMToVRFConverter::convertToVRF(const UTMPoint& utm_point) const {
  // 计算相对于车辆位置的差值
  double delta_easting = utm_point.easting - vehicle_pose_.position.easting;
  double delta_northing = utm_point.northing - vehicle_pose_.position.northing;

  // 旋转到车辆坐标系
  // 注意: 这里假设yaw是从正北方向顺时针测量的角度
  double cos_yaw = cos(vehicle_pose_.yaw);
  double sin_yaw = sin(vehicle_pose_.yaw);

  VRFPoint vrf_point;
  // 车辆坐标系: x向前(车头方向)，y向左
  vrf_point.x = delta_easting * sin_yaw + delta_northing * cos_yaw;
  vrf_point.y = delta_easting * cos_yaw - delta_northing * sin_yaw;
  vrf_point.z = utm_point.altitude - vehicle_pose_.position.altitude;

  return vrf_point;
}

// 批量转换
std::vector<VRFPoint> UTMToVRFConverter::convertToVRF(
    const std::vector<UTMPoint>& utm_points) const {
  std::vector<VRFPoint> vrf_points;
  vrf_points.reserve(utm_points.size());

  for (const auto& utm_point : utm_points) {
    vrf_points.push_back(convertToVRF(utm_point));
  }

  return vrf_points;
}
