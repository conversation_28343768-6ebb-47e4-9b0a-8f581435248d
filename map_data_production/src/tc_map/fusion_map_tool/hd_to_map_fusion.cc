/*
 * File: hd_to_map_fusion.cc
 * Description:
 * Author: qingxiansun
 * Date: 2025-04-09
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include <google/protobuf/text_format.h>

#include <iostream>
#include <sstream>
#include <thread>

#include "common/header.pb.h"
#include "extern_hd_data_manager.hpp"
#include "map/map_fusion.pb.h"
#include "perception/gwm_pcpt_lane.pb.h"
#include "util/Coordinates.h"
#include "util/message_io_handler.hpp"
#include "vehicle/vehicle_state.pb.h"

using namespace std;

using namespace gwm::pcpt::lane;
using namespace gwm::vehicle;
using namespace gwm::fusionmap;
using ::nerd::api::GeoCoordinate;
using tc_map::nerd::LocationInfo;

using TrackList = std::vector<LocationInfo>;

int GetTestTrackList(TrackList& track_list);

const LaneCenterLineData* FindLaneCenterLineDataByTrackId(
    const PcptLaneCenterLineList& center_line_list, uint32_t target_track_id) {
  for (const auto& line_data : center_line_list.center_line_data()) {
    if (line_data.track_id() == target_track_id) {
      return &line_data;
    }
  }
  return nullptr;
}

const LaneDataInfo* FindLaneDataInfoByCenterLineId(
    const PcptLaneList& lane_list, uint32_t target_center_line_id) {
  for (const auto& lane_data : lane_list.lane_data()) {
    if (lane_data.lane_center_line_id() == target_center_line_id) {
      return &lane_data;
    }
  }
  return nullptr;
}

int main(int argc, char** argv) {
  int ret = 0;
  tc_map::nerd::HDDataManager manager;
  std::string hd_data_dir = "../../../data/20250228/HD";
  manager.InitSDK(hd_data_dir, "./log", (24 * 30), (200 * 1024 * 1024),
                  (5 * 1024 * 1024));
  // auto map_fusion_result = std::make_shared<MapFusionResult>();
  TrackList track_list;
  GetTestTrackList(track_list);
  std::vector<MapFusionResult> map_fusion_list;
  manager.BuildFusionMapInfo(track_list, map_fusion_list);
  std::cout << "map_fusion_list.size:" << map_fusion_list.size() << std::endl;
  std::string file_name;
  std::ostringstream oss;
  for (auto& map_fusion_result : map_fusion_list) {
    uint64_t timestamp = map_fusion_result.header().timestamp();
    oss << "./test_data/" << std::to_string(timestamp) << ".bin";
    file_name = oss.str();
    WriteMessageToFile(map_fusion_result, file_name);
    oss.clear();
    oss.str("");
  }
  std::cout << "The program has executing finish." << std::endl;
  return ret;
}

int GetTestTrackList(TrackList& track_list) {
  int ret = 0;
  auto coord_helper = gwm::Coordinates::getInstance();
  std::vector<GeoCoordinate> coordinates = {
      {116.5111409, 39.7593992},   {116.51131602, 39.75971584},
      {116.5114277, 39.7598598},   {116.5116692, 39.7603560},
      {116.51170202, 39.76048671}, {116.5118319, 39.7606207},
      {116.5118895, 39.7609285},   {116.5120390, 39.7612486},
      {116.5121376, 39.7614667},   {116.5121826, 39.7615985},
      {116.5122474, 39.7617593},   {116.5123305, 39.7619567}};
  track_list.resize(coordinates.size());
  double bearing = .0;
  std::cout << std::fixed << std::setprecision(6);
  for (std::size_t i = 0; i < coordinates.size(); i++) {
    if (i < coordinates.size() - 1) {
      bearing = coord_helper.calculateBearing(
          coordinates[i].y, coordinates[i].x, coordinates[i + 1].y,
          coordinates[i + 1].x);
    } else {
      bearing = coord_helper.calculateBearing(
          coordinates[i - 1].y, coordinates[i - 1].x, coordinates[i].y,
          coordinates[i].x);
    }
    auto& geo = coordinates[i];
    auto& loc = track_list[i];
    loc.point.x = geo.x;
    loc.point.y = geo.y;
    loc.radias = 5;
    loc.heading_degree = bearing;
    loc.max_tolerance_angle = 20;
    loc.time_stamp = CurrentTimestampMS();
    LOG_INFO << "pos:" << geo.x << "," << geo.y << " bearing:" << bearing
             << std::endl;
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
  }
  double utm_east = .0;
  double utm_north = .0;
  int utm_zone = 0;

  std::cout << "start pos list:" << std::endl;
  for (auto& pos : track_list) {
    coord_helper.gcj02ToUtm(pos.point.y, pos.point.x, utm_east, utm_north,
                            utm_zone);
    DebugCode(LOG_INFO << "pos lon:" << pos.point.x << ", lat:" << pos.point.y
                       << ",utm_east:" << utm_east << ",utm_north:" << utm_north
                       << ",utm_zone:" << utm_zone << std::endl);
  }
  std::cout << "end pos list:" << std::endl;
  return ret;
}
