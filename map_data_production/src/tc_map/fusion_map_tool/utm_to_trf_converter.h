/*
 * File: utm_to_trf_converter.h
 * Description:
 * Author: qingxiansun
 * Date: 2025-04-10
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */
#pragma once
#include <vector>

// UTM坐标点结构体
struct UTMPoint {
  double easting;   // 东向坐标
  double northing;  // 北向坐标
  double altitude;  // 高度(可选)
};

// 车辆相对坐标系点结构体
struct VRFPoint {
  double x;  // 前向(车头方向)
  double y;  // 横向(左侧为正)
  double z;  // 高度(可选)
};

// 车辆姿态信息
struct VehiclePose {
  UTMPoint position;  // 车辆当前位置(UTM)
  double yaw;  // 车辆航向角(弧度)，从正北方向顺时针计算
};

class UTMToVRFConverter {
public:
  // 设置车辆当前位置和姿态
  void setVehiclePose(const VehiclePose& pose);

  // 将UTM点转换为VRF坐标
  VRFPoint convertToVRF(const UTMPoint& utm_point) const;

  // 批量转换
  std::vector<VRFPoint> convertToVRF(
      const std::vector<UTMPoint>& utm_points) const;

private:
  VehiclePose vehicle_pose_;
};
