
cmake_minimum_required(VERSION 3.16)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
project(hd_to_map_fusion
        VERSION 0.0.1
        LANGUAGES CXX
        )

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")

set(PROJECT_ROOT_DIR "${PROJECT_SOURCE_DIR}/../../../../..")
message(STATUS "${PROJECT_SOURCE_DIR}")
set(THIRD_PARTY_DIR "${PROJECT_ROOT_DIR}/modules/map_data_production/thirdparty")
message(STATUS "${THIRD_PARTY_DIR}")

include(${THIRD_PARTY_DIR}/tencent/x86/FindTCmap.cmake)

find_package(Threads)
find_library(ZMQ_LIBRARY NAMES zmq)
find_package(Protobuf REQUIRED)
find_library(PROJ_LIBRARY proj PATHS /usr/local/lib NO_DEFAULT_PATH)  # 指定路径
if (NOT PROJ_LIBRARY)
    message(FATAL_ERROR "libproj not found in /usr/local/lib")
endif()

file(GLOB NERD_API_LIBRARIES "${TENCENT_SDK_DIR}/lib/*.so*")
set(proto_dir "${PROJECT_ROOT_DIR}/deployment/interface/proto/")

set(SOURCES
    hd_to_map_fusion.cc
	extern_hd_data_manager.cc
	utm_to_trf_converter.cc
	${PROJECT_ROOT_DIR}/modules/map_data_production/src/util/Coordinates.cpp
	${PROJECT_ROOT_DIR}/modules/map_data_production/src/tc_map/nerd/callback_object.cc
    ${proto_dir}/common/header.pb.cc
    ${proto_dir}/perception/gwm_pcpt_lane.pb.cc
    ${proto_dir}/vehicle/vehicle_state.pb.cc
    ${proto_dir}/vehicle/chassis.pb.cc
    ${proto_dir}/vehicle/body.pb.cc
    ${proto_dir}/vehicle/wheel.pb.cc
    ${proto_dir}/map/map_fusion.pb.cc
    ${proto_dir}/common/geometry.pb.cc
    ${proto_dir}/map/sd_map.pb.cc
    ${proto_dir}/perception/gwm_pcpt_marker.pb.cc
    ${proto_dir}/perception/gwm_pcpt_lane.pb.cc
    ${proto_dir}/perception/traffic_light_obj.pb.cc
#${PROJECT_ROOT_DIR}/modules/map_data_production/proto/common/geometry.pb.cc
#    ${PROJECT_ROOT_DIR}/modules/map_data_production/proto/hd_map_lane.pb.cc
)

add_executable(${PROJECT_NAME} ${SOURCES})

include_directories(${PROJECT_ROOT_DIR}/deployment/interface/proto/)
include_directories(${PROJECT_ROOT_DIR}/modules/map_data_production/include)

nerd_api_target_deps(${PROJECT_NAME})

target_link_libraries(${PROJECT_NAME} PRIVATE ${PROTOBUF_LIBRARIES} 
                      ${ZMQ_LIBRARY} ${PROJ_LIBRARY})

install(TARGETS  EXPORT hd_to_map_fusion_Targets
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
        RUNTIME DESTINATION bin
        INCLUDES DESTINATION include
)

#install(FILES ${NERD_API_LIBRARIES}  DESTINATION tc_lib)
