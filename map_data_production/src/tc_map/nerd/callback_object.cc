/*
 * File: callback_object.cc
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-12
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include "tc_map/nerd/callback_object.hpp"
#include "util/log.hpp"

namespace tc_map {
namespace nerd {
void TileCallback::OnResult(
    const ::nerd::api::RetMessage &ret, ::nerd::api::JobId job_id,
    std::unique_ptr<::nerd::api::GetMapDataBatchParam> req,
    std::unique_ptr<::nerd::api::GetMapDataBatchResult> rsp) {
  DebugCode(LOG_INFO << "ret.msg: " << ret.msg << ", ret.ret: " << ret.ret);
  if (rsp->data_cubes.empty()) {
    DebugCode(LOG_INFO << "rsp->data_cubes.empty()" << std::endl);
    DebugCode(LOG_INFO << "rsp->ret.msg: " << rsp->ret.msg
                       << ", rsp->ret.ret: " << rsp->ret.ret << std::endl);
  }
  resp_ = std::move(rsp);
  p_.set_value();
}

void RectCallback::OnResult(
    const ::nerd::api::RetMessage &ret, ::nerd::api::JobId job_id,
    std::unique_ptr<::nerd::api::GetMapDataByRectParam> req,
    std::unique_ptr<::nerd::api::GetMapDataByRectResult> rsp) {
  DebugCode(LOG_INFO << "ret.msg: " << ret.msg << ", ret.ret: " << ret.ret
                     << std::endl);
  resp_ = std::move(rsp);
  p_.set_value();
}

}  // namespace nerd
}  // namespace tc_map
