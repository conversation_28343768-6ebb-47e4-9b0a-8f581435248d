
#include "tc_map/nerd/TypeAdapter.hpp"
#include "util/log.hpp"

namespace tc_map {
namespace nerd {

/* // 腾讯道路等级
 * ---|---
 * 00 |高速道路
 * 01 |城市高速路
 * 02 |国道
 * 03 |省道
 * 04 |县道
 * 06 |乡镇村道
 * 08 |其它道路
 * 09 |非引导道路
 * 0a |轮渡
 * 0b |步行道路
 * 0c |人渡
 * 0d |自行车专用道
 * 0e |索道
 */

/* ROADCLASS_FREEWAY = 0;  // 高速公路
 * ROADCLASS_NATIONAL_ROAD = 1;  // 国道
 * ROADCLASS_PROVINCE_ROAD = 2;  // 省道
 * ROADCLASS_COUNTY_ROAD = 3; // 县道
 * ROADCLASS_RURAL_ROAD = 4;  // 乡公路
 * ROADCLASS_IN_COUNTY_ROAD = 5; // 县乡村内部道路
 * ROADCLASS_CITY_SPEED_WAY = 6; // 主要大街\城市快速道
 * ROADCLASS_MAIN_ROAD = 7;  // 主要道路
 * ROADCLASS_SECONDARY_ROAD = 8; // 次要道路
 * ROADCLASS_COMMON_ROAD = 9; // 普通道路
 * ROADCLASS_NON_NAVI_ROAD = 10; // 非导航道路
 * ROADCLASS_INVALID = 0xFF;  // 道路等级无效值 */
gwm::sdmap::Road_RoadType TypeAdapter::ConvertRoadTypeFromTencent(
    ::nerd::api::RoadClassType road_type) {
  gwm::sdmap::Road_RoadType res;
  switch (road_type) {
    case 0x0:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_FREEWAY;
      break;
    case 0x1:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_CITY_SPEED_WAY;
      break;

    case 0x2:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_NATIONAL_ROAD;
      break;
    case 0x3:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_PROVINCE_ROAD;
      break;
    case 0x4:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_COUNTY_ROAD;
      break;
    case 0x6:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_RURAL_ROAD;
      break;
    case 0x8:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_SECONDARY_ROAD;
      break;
    case 0x9:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_NON_NAVI_ROAD;
      break;
    case 0xa:
    case 0xb:
    case 0xc:
    case 0xd:
    case 0xe:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_NON_NAVI_ROAD;
      break;
    defaut:
      res = gwm::sdmap::Road_RoadType_ROADCLASS_INVALID;
      break;
  }
  return res;
}

int32_t TypeAdapter::ConvertRoadTypeToTencent(
    gwm::sdmap::Road_RoadType road_type) {
  ::nerd::api::RoadClassType res;
  switch (road_type) {
    case gwm::sdmap::Road_RoadType_ROADCLASS_FREEWAY:
      res = 0x0;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_NATIONAL_ROAD:
      res = 0x1;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_PROVINCE_ROAD:
      res = 0x2;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_COUNTY_ROAD:
      res = 0x3;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_RURAL_ROAD:
      res = 0x4;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_IN_COUNTY_ROAD:
      res = 0x5;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_CITY_SPEED_WAY:
      res = 0x6;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_MAIN_ROAD:
      res = 0x7;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_SECONDARY_ROAD:
      res = 0x8;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_COMMON_ROAD:
      res = 0x9;
      break;
    case gwm::sdmap::Road_RoadType_ROADCLASS_NON_NAVI_ROAD:
      res = 0x10;
      break;
    defaut:
      res = 0x10;
      break;
  }
  return res;
}

/* sd map info
 * FORMWAY_UNKNOWN = 0;
 * FORMWAY_DIVISED_LINK = 1;  // 上下线分离 主路
 * FORMWAY_CROSS_LINK = 2;  // 交叉点内 复杂节点内部道路
 * FORMWAY_JCT = 3; //< JCT
 * FORMWAY_ROUND_CIRCLE = 4; // 环岛
 * FORMWAY_SERVICE_ROAD = 5;  // 服务区 辅助道路
 * FORMWAY_SLIP_ROAD = 6;  // 引路 匝道
 * FORMWAY_SIDE_ROAD = 7;  // 辅路
 * FORMWAY_SLIP_JCT = 8;  // 引路 JCT
 * FORMWAY_EXIT_LINK = 9;  // 出口
 * FORMWAY_ENTRANCE_LINK = 10;  // 入口
 * FORMWAY_TURN_RIGHT_LINEA = 11;  // 右转专用道
 * FORMWAY_TURN_RIGHT_LINEB = 12;  // 右转专用道
 * FORMWAY_TURN_LEFT_LINEA = 13;  // 左转专用道
 * FORMWAY_TURN_LEFT_LINEB = 14;  // 左转专用道
 * FORMWAY_COMMON_LINK = 15;  // 普通道路
 * FORMWAY_TURN_LEFTRIGHT_LINE = 16;  // 左右转专用道
 * FORMWAY_NONMOTORIZED_DRIVEWAY = 17;  // ADF+ 专用
 * FORMWAY_FRONTDOOR_ROAD = 18;  // 门前道路
 * FORMWAY_SERVICE_SLIP_ROAD = 19;  // 服务区 + 引路
 * FORMWAY_SERVICE_JCT = 20;  // 服务区 + JCT
 * FORMWAY_SERVICE_JCT_SLIP_ROAD = 21;  // 服务区 + 引路 + JCT
 * FORMWAY_NON_VEHICLE = 22;  // 非机动车道路
 * FORMWAY_INVALID = 0xFF;  // 道路构成无效值 */

/* tencent form way
 * kNone = 0x00; // 无属性
 * kRoundabout = 0x01; //环岛
 * kDirectionSpit = 0x02; // 上下线分离
 * kJCT = 0x03; // JCT,连接高速道路
 * kCrossInner = 0x04; // 交叉点内Link
 * kIC = 0x05; // IC,连接高速和其它不同等级道路
 * kPA = 0x06; // 停车区
 * kSA = 0x07; // 服务区
 * kBridge = 0x08; // 固定桥
 * kWalkStreet = 0x09; // 步行街
 * kSecondary = 0x0a; // 辅路
 * kRamp = 0x0b; // 匝道
 * kEnclosure = 0x0c; // 全封闭道路
 * kUndefined = 0x0d; // 未定义交通区域
 * kPoiConnect = 0x0e; // POI连接路
 * kTunnel = 0x0f; // 隧道
 * kBus = 0x11; // 公交专用道
 * kRightTurn = 0x12; // 提前右转
 * kView = 0x13; // 风景路线
 * kInnerRegion = 0x14; // 区域内道路
 * kLeftTurn = 0x15; // 提前左转
 * kUTurn = 0x16; // 调头口
 * kMainSecondaryEntrance = 0x17; // 主辅路出入口
 * kParkEntrance = 0x1a; // 停车场出入口连接路
 * kMovableBridge = 0x1b; // 移动式桥
 * kReversalLeft = 0x1e; // 借道左转
 * kMainRoad = 0x1f; // 主路
 * kFrontDoor = 0x20; // 门前路
 * kElevated = 0x21; // 高架路
 * kTruckLane = 0x22; // 货车专用道
 * kNORMAL = 0x23; // 普通
 * kTOLL = 0x24; // 收费站
 * kCONSTRUCTION = 0x25; // 建设中道路
 * kINTERSECTION = 0x26; // 十字路口
 * kHIGHWAY_PORT = 0x27; // 高速出入口
 * kHIGHWAY_CONNECTION = 0x28; // 高速连接路
 * kNONSTANDARD_ROUNDABOUT = 0x29; // 非标准环岛
 * kSPECIAL_CONNECTION = 0x2a; // 特殊连接路
 * kPARKING_OCCUPY_ROAD = 0x2b; // 包含占道停车场
 * kOWNERSHIP = 0x2c; // 私道
 * kINNER_VIRTUAL_CONNECT = 0x2d; // 区域内虚拟连接路
 * kTAXI = 0x2e; // 区域内虚拟连接路
 * kTIDE = 0x2f; // 潮汐车道
 * kSTEP_ROAD = 0x30; // 台阶路
 * kINNER_CROSS_ROAD = 0x31; // 路口面道路
 * kENTRANCE_AND_EXIT_CONNECT = 0x32; // 出入口连接路
 * kAHEAD_TURN_RIGHT = 0x33; // 提前右转(高精)
 * kCROSS_LINE_OVERPASS = 0x34; // 跨线天桥
 * kSUNKEN_ROAD = 0x35; // 下沉道路
 * kRAMP_BOTH_PASS = 0x36; // 匝道双通
 * kMOUNTAIN_ROAD = 0x37; // 山路
 * kSUNKEN_ROAD_PORT = 0x38; // 下沉道路出入口
 * kOTHER = 0x39; // 其他
 * kVIRTUAL_SOLID_CONNECTION = 0x3a; // 虚实线链接路
 * kPARKING_INTERNAL_ROAD = 0x3b; // 停车场内部道路
 */

gwm::sdmap::Road_FormOfWay TypeAdapter::ConvertRoadFormwayFromTencent(
    ::nerd::api::RoadKindType kind_type) {
  gwm::sdmap::Road_FormOfWay res;
  switch (kind_type) {
    case ::nerd::api::RoadKindType::kNone:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_UNKNOWN;
      break;
    case ::nerd::api::RoadKindType::kDirectionSpit:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_CROSS_LINK;
      break;
    case ::nerd::api::RoadKindType::kJCT:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_JCT;
      break;
    case ::nerd::api::RoadKindType::kRoundabout:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_ROUND_CIRCLE;
      break;
    case ::nerd::api::RoadKindType::kSA:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_ROAD;
      break;
    case ::nerd::api::RoadKindType::kRamp:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_SLIP_ROAD;
      break;
    case ::nerd::api::RoadKindType::kSecondary:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_SIDE_ROAD;
      break;
    case ::nerd::api::RoadKindType::kHIGHWAY_PORT:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_SLIP_JCT;
      break;
    case ::nerd::api::RoadKindType::kMainSecondaryEntrance:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_EXIT_LINK;
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_ENTRANCE_LINK;
      break;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEA;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEB;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_LEFT_LINEA;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_LEFT_LINEB;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_COMMON_LINK;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_LEFTRIGHT_LINE;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_NONMOTORIZED_DRIVEWAY;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_FRONTDOOR_ROAD;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_SLIP_ROAD;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_JCT;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_JCT_SLIP_ROAD;
      // res =  gwm::sdmap::Road_FormOfWay_FORMWAY_NON_VEHICLE;
    default:
      res = gwm::sdmap::Road_FormOfWay_FORMWAY_INVALID;
      break;
  }
  return res;
}

int32_t TypeAdapter::ConvertRoadFormwayToTencent(
    gwm::sdmap::Road_FormOfWay formofway) {
  int32_t res = 0;
  switch (formofway) {
    case gwm::sdmap::Road_FormOfWay_FORMWAY_UNKNOWN:
      res = 0;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_DIVISED_LINK:
      res = 1;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_CROSS_LINK:
      res = 2;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_JCT:
      res = 3;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_ROUND_CIRCLE:
      res = 4;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_ROAD:
      res = 5;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_SLIP_ROAD:
      res = 6;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_SIDE_ROAD:
      res = 7;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_SLIP_JCT:
      res = 8;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_EXIT_LINK:
      res = 9;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_ENTRANCE_LINK:
      res = 10;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEA:
      res = 11;
      break;

    case gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEB:
      res = 12;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_LEFT_LINEA:
      res = 13;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_LEFT_LINEB:
      res = 14;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_COMMON_LINK:
      res = 15;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_TURN_LEFTRIGHT_LINE:
      res = 16;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_NONMOTORIZED_DRIVEWAY:
      res = 17;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_FRONTDOOR_ROAD:
      res = 18;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_SLIP_ROAD:
      res = 56;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_JCT:
      res = 53;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_SERVICE_JCT_SLIP_ROAD:
      res = 58;
      break;
    case gwm::sdmap::Road_FormOfWay_FORMWAY_NON_VEHICLE:
      res = 22;
      break;
    default:
      res = 0;
      break;
  }
  return res;
}

gwm::hdmap::Lane_LaneType TypeAdapter::ConvertHDMapLaneTypeFromTencent(
    RoadClassType road_class, RoadKindTypeVec &road_kind_types,
    ::nerd::api::LaneType lane_type) {
  gwm::hdmap::Lane_LaneType gwm_lane_type;
  gwm_lane_type = gwm::hdmap::Lane_LaneType_UNKNOWN;
  switch (road_class) {
    case 0x00:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_HIGHWAY;
      break;
    case 0x01:
    case 0x02:
    case 0x03:
    case 0x04:
    case 0x06:
    case 0x08:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_STREET;
      break;
  }
  for (auto road_kind : road_kind_types) {
    switch (road_kind) {
      case ::nerd::api::RoadKindType::kRoundabout:
        gwm_lane_type = gwm::hdmap::Lane_LaneType_ROUNDABOUT;
        break;
      case ::nerd::api::RoadKindType::kRamp:
        // gwm_lane_type = ;
        // 需要判断是上匝道，还是下匝道，目前还没有此判断规则
        break;
    }
  }
  // no set gwm::hdmap::Lane_LaneType_ROADWORK
  // OFFRAMP = 10;  // 下匝道  ----
  //  ONRAMP = 11;  // 上匝道  -----
  // PARK = 14;  // 园区车道
  // PARK_ON_LANE = 17;  // 停车车道
  // WIDE_LANE = 19;  // 超宽车道
  // TRANSFER_LANE = 21;  // 中转车道

  switch (lane_type) {
    case ::nerd::api::LaneType::kREVERSAL_LEFT_TURN:
    case ::nerd::api::LaneType::kLEFT_VIRTUAL:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_BIDIRECTIONAL;
      break;

    case ::nerd::api::LaneType::kDRIVABLE_SHOULDER:
    case ::nerd::api::LaneType::kSHOULDER:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_SHOULDER;
      break;

    case ::nerd::api::LaneType::kBICYCLE:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_BIKING;
      break;

    case ::nerd::api::LaneType::kPEDESTRIANS:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_SIDEWALK;
      break;

    case ::nerd::api::LaneType::kCONTROL:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_RESTRICTED;
      break;
    case ::nerd::api::LaneType::kPARKING_ROAD:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_PARKING;
      break;
    case ::nerd::api::LaneType::kBUS:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_BUSLANE;
      break;
    case ::nerd::api::LaneType::kTURN_WAITING:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_LEFTTURNWAITINGAREA;
      break;
    case ::nerd::api::LaneType::kRIGHT_VIRTUAL:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_RIGHT_TURN_ONLY;
      break;
    case ::nerd::api::LaneType::kDIRECTION_CHANGE:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_DYNAMIC_LANE;
      break;
    case ::nerd::api::LaneType::kTIDE:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_TIDAL_LANE;
      break;
    case ::nerd::api::LaneType::kEMERGENCY:
      gwm_lane_type = gwm::hdmap::Lane_LaneType_EMERGENCY;
      break;
  }
  return gwm_lane_type;
}
}  // namespace nerd
}  // namespace tc_map
