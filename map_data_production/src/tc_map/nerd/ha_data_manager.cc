// Copyright 2023 Tencent Inc. All rights reserved.

#include <cstdint>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>

#include "api/map_data_engine_api.h"
#include "api/nerd_utils.h"
#include "cloud-atlas/tencent_cloud_atlas_typedef.h"
#include "nerd_api/nerd_version.h"
#include "proto/sd_map.pb.h"
#include "tc_map/nerd/TypeAdapter.hpp"
#include "tc_map/nerd/ha_data_manager.hpp"
#include "util/Coordinates.h"
#include "util/log.hpp"

namespace tc_map {
namespace nerd {

std::set<::nerd::api::TileIDType> HADataManager::get_tile_ids(
    std::string txt_file) {
  std::set<::nerd::api::TileIDType> tile_ids;
  std::stringstream ss;
  ss << "./tile_ids/" << txt_file;
  std::string file_path = ss.str();
  std::ifstream in_file(file_path);
  std::string line;
  while (std::getline(in_file, line)) {
    if (line.empty()) {
      continue;
    }
    tile_ids.insert(static_cast<::nerd::api::TileIDType>(std::stoll(line)));
  }
  return tile_ids;
}

HADataManager::HADataManager() {}

HADataManager::~HADataManager() {}

int HADataManager::InitSDK(const std::string &data_dir,
                           const std::string &log_dir,
                           long log_expiration_hours, long max_log_store_bytes,
                           long max_signle_log_file_bytes) {
  //  std::set<::nerd::api::TileIDType> lane_tile_ids =
  //  get_tile_ids("lane.txt"); std::set<::nerd::api::TileIDType> route_tile_ids
  //  = get_tile_ids("route.txt"); DebugCode(LOG_INFO <<
  //  "FIND_TILE_ID_LANE_COUNT:" << lane_tile_ids.size() << std::endl);
  //  DebugCode(LOG_INFO << "FIND_TILE_ID_ROUTE_COUNT:" << route_tile_ids.size()
  //  << std::endl);

  // 初始化log，路径相对于可执行程序而言,当然也可以是绝对路径
  ::nerd::api::InitLogger(
      log_dir, false, "tencent_nerd_ha_",        //日志文件名前缀
      std::chrono::hours(log_expiration_hours),  //日志存储时间限制
      max_log_store_bytes,         //日志存储大小的最大限制
      max_signle_log_file_bytes);  //单个日志文件大小限制

  // 先调用InitGlobalConfig, 只需要调用一次
  ::nerd::api::GlobalConfig global_config;
  // 磁盘缓存路径
  global_config.data_root_path =
      data_dir;  //路径相对于可执行程序而言,当然也可以是绝对路径
  // 版本文件路径
  global_config.version_file_path =
      global_config
          .data_root_path;  //路径相对于可执行程序而言,当然也可以是绝对路径
  // global_config.offline_debug =
  // false;//是否开启离线调试模式，默认false。设置为true时，offline_city_code_mode无效，使用全国NDS大文件。
  global_config.offline_debug =
      true;  //是否开启离线调试模式，默认false。设置为true时，offline_city_code_mode无效，使用全国NDS大文件。
  global_config.offline_city_code_mode =
      false;  // 离线模式, false:使用9x9级Tile九宫格模式  true:使用CityCode模式
  global_config.channel =
      "POC";  //这里非常重要，需要注入腾讯专门提供的渠道号，若没有填写正确的渠道号将无法获取在线数据
  global_config.deviceId =
      "testDeviceId";  // TODO
                       // 此处需要填写每台设备的真实唯一的Id，长度不能超过32，否则会报鉴权失败
  ::nerd::api::InitGlobalConfig(global_config);

  DebugCode(LOG_INFO << "sdk detail version=" << ::nerd::GetVersionDetail(true)
                     << std::endl);
  // 创建各个building block对应的引擎
  static auto create_engine =
      [](::nerd::api::MapDataBuildingBlockID build_block_id)
      -> std::unique_ptr<::nerd::api::MapDataEngineApi> {
    ::nerd::api::APIConfig api_config;
    api_config.user_id = "building_block:" +
                         std::to_string(static_cast<int32_t>(build_block_id));
    api_config.build_block_id = build_block_id;
    api_config.preference = ::nerd::api::DataPreference::
        kOfflineOnly;  //这里只推荐在线模式，或者离线模式，其他的在线优先等模式可能存在数据版本不一致的问题
    return ::nerd::api::CreateEngine(api_config);
  };

  lane_api_ = create_engine(
      ::nerd::api::MapDataBuildingBlockID::kLANE);  //创建获取HD数据的引擎实例
  route_api_ = create_engine(
      ::nerd::api::MapDataBuildingBlockID::kROUTE);  //创建获取SD数据的引擎实例
  assert(lane_api_ && route_api_);
  return 0;
}

void HADataManager::getSDMapElement(::nerd::api::ISDLinkLayer *sdLinkLayer) {
  if (sdLinkLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    DebugCode(LOG_INFO << "sdLinkLayer is nullptr " << std::endl);
    return;
  }
  auto links = sdLinkLayer->GetLinkArr();
  DebugCode(LOG_INFO << "sdLinkLayer->GetLinkArr().size(): " << links.size()
                     << std::endl);
  for (const auto &link : links) {
    DebugCode(LOG_INFO << "link id: " << link->GetID().ToString() << std::endl);
    const auto &attr = link->GetAttribute();
    std::shared_ptr<std::vector<::nerd::api::Coordinate>> coords =
        link->GetGeometry();
    auto count = coords.get()->size();
    DebugCode(LOG_INFO << "link_id:" << link->GetID().link_id
                       << " coordiates: ");
    for (int i = 0; i < count; i++) {
      bool is_northern = false;
      auto src_point = coords->data()[i];
      auto geo_coord = src_point.ToGeo();
      double utm_east = .0;
      double utm_north = .0;
      int utm_zone = 0;
      gwm::Coordinates::getInstance().gcj02ToUtm(geo_coord.y, geo_coord.x,
                                                 utm_east, utm_north, utm_zone);
      DebugCode(LOG_INFO << std::fixed << std::setprecision(8) << geo_coord.x
                         << "," << geo_coord.y << "," << utm_east << ","
                         << utm_north << "," << utm_zone << "," << is_northern
                         << ";" << std::endl);
    }
    DebugCode(LOG_INFO << std::endl);

    auto restrictions = link->GetRestrictions();
    //获取link的拓扑
    //获取后继link列表（link列表不包含路口内link），该接口只能获取到本tile内的后继link
    //如果后继link有多条，其中有几条在另外的tile中，那么该接口只能获取到本tile内的后继link列表；如果后继link只有一条，并且这条后继link在另外的tile中，那么该接口返回空列表
    auto nextLinks = link->GetNext(attr.direction, false);
    //获取后继link的id列表（linkid列表不包含路口内linkid），若后继link在另外一个tile中，那么GetNextIDs是可以拿到后继linkid的
    auto nextLinkIds = link->GetNextIDs(attr.direction, false);

    //获取后继link列表（link列表包含路口内link），该接口只能获取到本tile内的后继link
    //如果后继link有多条，其中有几条在另外的tile中，那么该接口只能获取到本tile内的后继link列表；如果后继link只有一条，并且这条后继link在另外的tile中，那么该接口返回空列表
    auto nextLinksWithinIntersection =
        link->GetNextWithinIntersection(attr.direction, false);
    //获取后继link的id列表（linkid列表包含路口内linkid），若后继link在另外一个tile中，那么GetNextIDsWithinIntersection是可以拿到后继linkid的
    auto nextLinkIdsWithinIntersection =
        link->GetNextIDsWithinIntersection(attr.direction, false);

    //获取link的node-方法一（推荐）
    auto startNodeId = link->GetStartNodeID();
    auto startNode = sdLinkLayer->GetLinkNodeByID(startNodeId);

    auto endNodeId = link->GetEndNodeID();
    auto endNode = sdLinkLayer->GetLinkNodeByID(endNodeId);

    //获取link的node-方法二（不推荐）
    //不推荐的原因是link的node是弱引用，有的时候拿到的结果是空的
    auto startNodeV2 = link->GetStartNode();
    auto endNodeV2 = link->GetEndNode();

    // 车信信息
    auto laneInfos =
        link->GetGuidance(::nerd::api::GuidanceType::kLaneMetaInfo);
    if (!laneInfos.empty()) {
      DebugCode(LOG_INFO << "laneInfo.size: " << laneInfos.size() << std::endl);
    }
    for (auto laneInfoPtr : laneInfos) {
      auto laneInfo = std::static_pointer_cast<const ::nerd::api::LaneInfoMeta>(
          laneInfoPtr);
      if (laneInfo == nullptr) {
        continue;
      }
      auto nodeid = laneInfo->GetNodeID();  // 进入节点id
      for (const auto &topo : laneInfo->lane_topos) {
        auto arrow = topo.lane_arrow;         // 转向箭头
        auto outLinkIds = topo.out_link_ids;  // 退出线号
      }
    }
    // 收费站
    auto guidancesToll =
        link->GetGuidance(::nerd::api::GuidanceType::kTollStation);
    if (!guidancesToll.empty()) {
      DebugCode(LOG_INFO << "guidancesToll.size: " << guidancesToll.size()
                         << std::endl);
    }
    for (const auto &guidance : guidancesToll) {
      auto toll_station =
          std::static_pointer_cast<const ::nerd::api::TollStation>(guidance);
      if (toll_station == nullptr) {
        continue;
      }
      auto name = toll_station->name;
      auto type = toll_station->GetType();
      auto toll_means = toll_station->toll_means;
      auto direction = toll_station->direction;
    }
    // 点限速
    auto guidancesSpeed =
        link->GetGuidance(::nerd::api::GuidanceType::kPointSpeed);
    if (!guidancesSpeed.empty()) {
      DebugCode(LOG_INFO << "guidancesSpeed.size: " << guidancesSpeed.size()
                         << std::endl);
    }
    for (const auto &guidance : guidancesSpeed) {
      auto point_speed =
          std::static_pointer_cast<const ::nerd::api::PointSpeed>(guidance);
      if (point_speed == nullptr) {
        continue;
      }
      auto speed_type = point_speed->type;
      auto position = point_speed->coordinate;
      auto speed_limit = point_speed->speed_limit;
      auto affect_link_id = point_speed->affect_link_id;
      auto special_times = point_speed->special_times;
      auto vehicles = point_speed->vehicles;
      auto speed_scenes = point_speed->speed_scenes;
    }
    // 长实线
    auto nodeId = link->GetStartNodeID();
    auto node = sdLinkLayer->GetLinkNodeByID(nodeId);
    if (node == nullptr) {
      continue;
    }
    auto guidancesLong = node->GetGuidance(::nerd::api::GuidanceType::kLsl);
    if (!guidancesLong.empty()) {
      DebugCode(LOG_INFO << "guidancesLong.size: " << guidancesLong.size()
                         << std::endl);
    }
    for (const auto &guidance : guidancesLong) {
      auto lsl = std::static_pointer_cast<const ::nerd::api::LSL>(guidance);
      if (lsl == nullptr) {
        continue;
      }
      auto type = lsl->lsl_type;
      auto exit_links = lsl->GetPassLinkIDS();
      auto lsl_lanes = lsl->lsl_lanes;
    }
    //交通警示
    auto guidanceWarningSign =
        link->GetGuidance(::nerd::api::GuidanceType::kWarningSign);
    if (!guidanceWarningSign.empty()) {
      DebugCode(LOG_INFO << "guidanceWarningSign.size: "
                         << guidanceWarningSign.size() << std::endl);
    }
    for (const auto &guidance : guidanceWarningSign) {
      auto warningSign =
          std::static_pointer_cast<const ::nerd::api::LinkWarningSign>(
              guidance);
      auto trick_kind = warningSign->trc_kind;
      auto direction = warningSign->linkDirection;
      auto coord = warningSign->coordinate;
    }
    // Zlevel
    auto guidanceZlevel =
        link->GetGuidance(::nerd::api::GuidanceType::kZLevelGroup);
    if (!guidanceZlevel.empty()) {
      DebugCode(LOG_INFO << "guidanceZlevel.size: " << guidanceZlevel.size()
                         << std::endl);
    }
    for (const auto &guiance : guidanceZlevel) {
      auto zlevel =
          std::static_pointer_cast<const ::nerd::api::ZLevelGroup>(guiance);
      auto links = zlevel->links;
      for (auto link : links) {
        auto linkid = link.link_id;
        auto zValue = link.z;
      }
      auto coord = zlevel->coordinate;
    }
    //电子眼
    auto guidanceCamera = link->GetGuidance(::nerd::api::GuidanceType::kCamera);
    if (!guidanceCamera.empty()) {
      DebugCode(LOG_INFO << "guidanceCamera.size: " << guidanceCamera.size()
                         << std::endl);
    }
    for (const auto &guidance : guidanceCamera) {
      auto camera =
          std::static_pointer_cast<const ::nerd::api::Camera>(guidance);
      auto type = camera->type;
      auto coord = camera->coordinate;
      auto direction = camera->direction;
      auto shootPos = camera->position;
    }
  }
}

void HADataManager::getHDAirData(::nerd::api::ISDLinkLayer *sdLinkLayer) {
  if (sdLinkLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    DebugCode(LOG_INFO << "sdLinkLayer is nullptr " << std::endl);
    return;
  }
  auto links = sdLinkLayer->GetLinkArr();
  DebugCode(LOG_INFO << "sdLinkLayer->GetLinkArr().size(): " << links.size()
                     << std::endl);
  for (const auto &link : links) {
    auto stopLineIds = sdLinkLayer->GetStopLineIDsByLinkId(link->GetID());
    for (auto stopLineId : stopLineIds) {
      auto stopLine = sdLinkLayer->GetStopLineById(stopLineId);
      DebugCode(LOG_INFO << " stop line id on this link: linkId:"
                         << link->GetID().ToString() << ", stopLineId:"
                         << stopLine->GetID().ToString() << std::endl);
    }
    auto featurePointIDs =
        sdLinkLayer->GetFeaturePointIDsByLinkId(link->GetID());
    for (auto &pointId : featurePointIDs) {
      auto point = sdLinkLayer->GetFeaturePointById(pointId);
      DebugCode(LOG_INFO << " feature point id on this link: linkId="
                         << link->GetID().ToString() << ", pointId="
                         << point->GetID().ToString() << std::endl);
    }

    // link 覆盖标签
    auto &data_coverage_status = link->GetAttribute().data_coverage_status;
    if (data_coverage_status.value != 0) {
      DebugCode(LOG_INFO << " linkId " << link->GetID().ToString()
                         << " link_data_coverage_status: "
                         << (uint32_t)data_coverage_status.value << std::endl);
    }

    // link 几何矢量方向归一化标记
    auto geo_direction_normalization =
        link->GetAttribute().geo_direction_normalization;
    if (data_coverage_status.value != 0) {
      DebugCode(LOG_INFO << " linkId " << link->GetID().ToString()
                         << " geo_direction_normalization: "
                         << geo_direction_normalization << std::endl);
    }

    // link zebra ids
    auto zebraIdsInLink = sdLinkLayer->GetZebraIDsByLinkId(link->GetID());
    for (auto zebraId : zebraIdsInLink) {
      auto zebra = sdLinkLayer->GetZebraById(zebraId);
      DebugCode(LOG_INFO << "linkId=" << link->GetID().ToString()
                         << ", zebraId=" << zebra->GetID().ToString()
                         << std::endl);
    }

    // link LinkID关联的IntersectionRoadID列表
    auto intersectionRoadIDs =
        sdLinkLayer->GetIntersectionRoadIDsByLinkId(link->GetID());
    for (auto intersectionRoadId : intersectionRoadIDs) {
      DebugCode(LOG_INFO << "linkId=" << link->GetID().ToString()
                         << ", intersectionRoadId="
                         << intersectionRoadId.ToString() << std::endl);
    }
  }
  DebugCode(LOG_INFO << "================featurePointIDs================="
                     << std::endl);
  auto featurePointIDs = sdLinkLayer->GetFeaturePointIDs();
  DebugCode(LOG_INFO << "featurePointIDs.size=" << featurePointIDs.size()
                     << std::endl);
  for (auto featurePointId : featurePointIDs) {
    auto featurePoint = sdLinkLayer->GetFeaturePointById(featurePointId);
    DebugCode(LOG_INFO << "feature point id:"
                       << featurePoint->GetID().ToString() << std::endl);
    // FP GetIntersectionRoadIDsByFpId
    auto intersectionRoadIds =
        sdLinkLayer->GetIntersectionRoadIDsByFpId(featurePointId);
    for (auto intersectionRoadId : intersectionRoadIds) {
      auto intersectionRoad =
          sdLinkLayer->GetIntersectionRoadByID(intersectionRoadId);
      DebugCode(
          LOG_INFO << "feature point id:" << featurePoint->GetID().ToString()
                   << " intersectionRoadId: " << intersectionRoadId.ToString()
                   << " intersectionRoadObjId: "
                   << intersectionRoad->GetRoadID().ToString() << std::endl);
    }

    // FP GetLaneLslType
    auto fpLanes = featurePoint->GetLanes();
    for (auto &lane : fpLanes) {
      auto lslType = lane->GetLaneLslType();
      DebugCode(LOG_INFO << "feature point id:"
                         << featurePoint->GetID().ToString()
                         << " lane id: " << lane->GetID().ToString()
                         << " lslType: " << (int)lslType << std::endl);
    }
  }

  DebugCode(LOG_INFO << "================stopLineIds================="
                     << std::endl);
  auto stopLineIds = sdLinkLayer->GetStopLineIDs();
  DebugCode(LOG_INFO << "stopLineIds.size=" << stopLineIds.size() << std::endl);
  for (auto stopLineId : stopLineIds) {
    auto stopLine = sdLinkLayer->GetStopLineById(stopLineId);
    DebugCode(LOG_INFO << "stopLine id:" << stopLine->GetID().ToString()
                       << std::endl);
    if (stopLine->GetStopLineType() ==
            ::nerd::api::hdair::StopLineType::SLT_StraightWaitingAreaStopLine ||
        stopLine->GetStopLineType() ==
            ::nerd::api::hdair::StopLineType::SLT_LeftTurnWaitingAreaStopLine ||
        stopLine->GetStopLineType() == ::nerd::api::hdair::StopLineType::
                                           SLT_RightTurnWaitingAreaStopLine ||
        stopLine->GetStopLineType() == ::nerd::api::hdair::StopLineType::
                                           SLT_TurnRoundWaitingAreaStopLine) {
      auto stIntersectionRoadIds = stopLine->GetRelatedIntersectionRoadIDs();
      DebugCode(LOG_INFO << "debug_waiting_stopline_id: "
                         << stopLine->GetID().ToString() << " stopLineType: "
                         << (int)stopLine->GetStopLineType()
                         << " stIntersectionRoadIds.size: "
                         << stIntersectionRoadIds.size() << std::endl);
    }

    // GetRelatedIntersectionRoadIDs
    auto intersectionRoadIds = stopLine->GetRelatedIntersectionRoadIDs();
    for (auto intersectionRoadId : intersectionRoadIds) {
      DebugCode(LOG_INFO << "stopLine id:" << stopLine->GetID().ToString()
                         << " intersectionRoadId: "
                         << intersectionRoadId.ToString() << std::endl);
    }
  }

  // IZebra in sdLinkLayer
  auto zebraIdsInLinkLayer = sdLinkLayer->GetZebraIDs();
  for (auto zebraId : zebraIdsInLinkLayer) {
    auto zebra = sdLinkLayer->GetZebraById(zebraId);
    DebugCode(LOG_INFO << "zebraId=" << zebra->GetID().ToString() << std::endl);
    auto zebraGetDirection = zebra->GetDirection();
    auto zebraLocation = zebra->GetLocation();
    auto zebraRelatedLinkID = zebra->GetRelatedLinkID();
    auto zebraRelatedNodeID = zebra->GetRelatedNodeID();
    DebugCode(
        LOG_INFO << "zebraId=" << zebra->GetID().ToString()
                 << ", zebraGetDirection=" << (int)zebraGetDirection
                 << ", zebraLocation=" << zebraLocation
                 << ", zebraRelatedLinkID=" << zebraRelatedLinkID.ToString()
                 << ", zebraRelatedNodeID=" << zebraRelatedNodeID.ToString()
                 << std::endl);
  }

  // 路口内虚拟道路
  auto intersectionRoads = sdLinkLayer->GetIntersectionRoadArr();
  for (const auto &intersectionRoad : intersectionRoads) {
    auto intersectionRoadId = intersectionRoad->GetRoadID();
    auto relatedLinkId = intersectionRoad->GetRelatedLinkID();
    auto direction = intersectionRoad->GetDirection();
    auto crossTurnType = intersectionRoad->GetCrossTurnType();
    auto startNodeID = intersectionRoad->GetStartNodeID();
    auto endNodeID = intersectionRoad->GetEndNodeID();
    auto startFPID = intersectionRoad->GetStartFeaturePointID();
    auto endFPID = intersectionRoad->GetEndFeaturePointID();
    auto preIds =
        intersectionRoad->GetPreviousIDs(direction);  // 业务按需传入方向
    auto nextIds = intersectionRoad->GetNextIDs(direction);  // 业务按需传入方向
    auto trafficLightFlag = intersectionRoad->GetTrafficLightFlag();
    auto geometry = intersectionRoad->GetGeometry();
    auto weakRefStartNode = intersectionRoad->GetStartNode();  // 弱引用
    auto weakRefEndNode = intersectionRoad->GetEndNode();      // 弱引用
    DebugCode(LOG_INFO << "intersectionRoadId=" << intersectionRoadId.ToString()
                       << ", relatedLinkId=" << relatedLinkId.ToString()
                       << ", direction=" << (int)direction
                       << ", crossTurnType=" << (int)crossTurnType
                       << ", startNodeID=" << startNodeID.ToString()
                       << ", endNodeID=" << endNodeID.ToString()
                       << ", startFPID=" << startFPID.ToString()
                       << ", endFPID=" << endFPID.ToString() << ", preIds.size="
                       << preIds.size() << ", nextIds.size=" << nextIds.size()
                       << ", trafficLightFlag=" << trafficLightFlag
                       << ", geometry.size=" << geometry->size() << std::endl);

    auto intersectionRoadNode =
        sdLinkLayer->GetIntersectionRoadNodeByID(startNodeID);
    if (intersectionRoadNode) {
      auto intersectionRoadNodeID = intersectionRoadNode->GetNodeID();
      auto intersectionRoadNodePosition = intersectionRoadNode->GetPosition();
      auto enterIntersectionRoadIDs =
          intersectionRoadNode->GetEnterIntersectionRoadIDs();
      auto outIntersectionRoadIDs =
          intersectionRoadNode->GetOutIntersectionRoadIDs();
      DebugCode(LOG_INFO << "intersectionRoadNodeID="
                         << intersectionRoadNodeID.ToString()
                         << ", nodePostion="
                         << intersectionRoadNodePosition.ToString()
                         << ", enterIntersectionRoadIDs.size="
                         << enterIntersectionRoadIDs.size()
                         << ", outIntersectionRoadIDs.size="
                         << outIntersectionRoadIDs.size() << std::endl);
    }
  }
}

void HADataManager::getHDMapElement(::nerd::api::IHDLaneLayer *hdLayer) {
  if (hdLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    DebugCode(LOG_INFO << "hdLayer is nullptr" << std::endl);
    return;
  }
  auto lanes = hdLayer->GetLaneArr();
  DebugCode(LOG_INFO << "lanes.size = " << lanes.size() << std::endl);
  auto crossAreas = hdLayer->GetCrossAreaArr();
  DebugCode(LOG_INFO << "crossAreas.size = " << crossAreas.size() << std::endl);
  auto laneGroups = hdLayer->GetLaneGroupArr();
  DebugCode(LOG_INFO << "laneGroups.size = " << laneGroups.size() << std::endl);
}

void HADataManager::getSDMapAndHDAirDataByRect(
    const ::nerd::api::GeoCoordinate &center_point, int radius) {
  //构造矩形框范围
  ::nerd::api::Rect rect =
      ::nerd::api::utils::BuildRectByLength(center_point, radius);

  ::nerd::api::GetMapDataByRectParam l_param;
  l_param.level =
      ::nerd::api::MapLevel::kLEVEL_13;  //获取SD数据，地图缩放层级13层
  l_param.rect = rect;
  auto route_rec_callback = std::make_shared<RectCallback>();
  route_api_->AsyncGetDataByRect(l_param, route_rec_callback);
  // 等待结果
  route_rec_callback->Wait();

  auto result = route_rec_callback->GetResult();
  if (result->data_cube != nullptr) {
    auto sdLinkLayer = result->data_cube->GetSDLinkLayer();
    getSDMapElement(sdLinkLayer);
    getHDAirData(sdLinkLayer);
  }
}

void HADataManager::getSDMapAndHDAirDataByTileID(
    ::nerd::api::TileIDType tileId) {
  DebugCode(LOG_INFO << "route_tile_id: " << tileId << std::endl);
  ::nerd::api::GetMapDataBatchParam route_batch_param;
  route_batch_param.tile_id_list.insert(tileId);
  auto route_tile_callback = std::make_shared<TileCallback>();
  route_api_->AsyncGetDataByTileId(route_batch_param, route_tile_callback);
  // 等待结果
  route_tile_callback->Wait();
  auto result = route_tile_callback->GetResult();
  auto dataCubes = result->data_cubes;
  for (auto datacube : dataCubes) {
    auto sdLinkLayer = datacube->GetSDLinkLayer();
    getSDMapElement(sdLinkLayer);
    getHDAirData(sdLinkLayer);
  }
}

void HADataManager::getHDMapDataByRect(
    const ::nerd::api::GeoCoordinate &center_point, int radius) {
  //构造矩形框范围
  ::nerd::api::Rect rect =
      ::nerd::api::utils::BuildRectByLength(center_point, radius);

  ::nerd::api::GetMapDataByRectParam l_param;
  l_param.level =
      ::nerd::api::MapLevel::kLEVEL_15;  //获取HD数据，地图缩放层级15层
  l_param.rect = rect;
  auto lane_rec_callback = std::make_shared<RectCallback>();
  lane_api_->AsyncGetDataByRect(l_param, lane_rec_callback);
  // 等待结果
  lane_rec_callback->Wait();

  auto result = lane_rec_callback->GetResult();
  if (result->data_cube != nullptr) {
    auto hdLayer = result->data_cube->GetHDLaneLayer();
    getHDMapElement(hdLayer);
  }
}

void HADataManager::getHDMapDataByTileID(::nerd::api::TileIDType tile_id) {
  DebugCode(LOG_INFO << "lane_tile_id: " << tile_id << std::endl);
  // 异步获取tile数据
  ::nerd::api::GetMapDataBatchParam batch_param;
  batch_param.tile_id_list.insert(tile_id);
  auto lane_tile_callback = std::make_shared<TileCallback>();
  lane_api_->AsyncGetDataByTileId(batch_param, lane_tile_callback);
  // 等待结果
  lane_tile_callback->Wait();
  auto result = lane_tile_callback->GetResult();
  auto dataCubes = result->data_cubes;
  for (auto datacube : dataCubes) {
    auto hdLayer = datacube->GetHDLaneLayer();
    getHDMapElement(hdLayer);
  }
};

void HADataManager::getSDMapAndHDAirData(
    const ::nerd::api::GeoCoordinate &point, int radius,
    ::nerd::api::TileIDType tileId) {
  getSDMapAndHDAirDataByTileID(tileId);  //通过tile ID获取SD数据

  getSDMapAndHDAirDataByRect(point, radius);  //通过矩形框获取对应范围内的SD数据

  getAroundTiles(tileId);  //获取tile周边tile id列表
}

void HADataManager::getHDMapData(const ::nerd::api::GeoCoordinate &center_point,
                                 int radius, ::nerd::api::TileIDType tileId) {
  getHDMapDataByTileID(tileId);  //通过tile ID获取HD数据
  getHDMapDataByRect(center_point, radius);  //通过矩形框获取对应范围内的HD数据
}

void HADataManager::getAroundTiles(::nerd::api::TileIDType id) {
  DebugCode(LOG_INFO << "tile id: " << id << std::endl);
  // test extend_num <= 3
  for (int extend_num = -1; extend_num <= 3; extend_num++) {
    DebugCode(LOG_INFO << "  extend_num: " << extend_num << std::endl);
    auto aroundTiles = ::nerd::api::GetAroundTiles(id, extend_num);
    DebugCode(LOG_INFO << "  aroundTiles.size: " << aroundTiles.size()
                       << std::endl);
    for (auto tile_tuple : aroundTiles) {
      auto tile_id = std::get<0>(tile_tuple);
      auto tile_row = std::get<1>(tile_tuple);
      auto tile_col = std::get<2>(tile_tuple);
      DebugCode(LOG_INFO << "    around tile id: " << tile_id << ", row: "
                         << tile_row << ", col: " << tile_col << std::endl);
    }
  }
}

// 只检查文件LANE.NDS
int HADataManager::LoadDemo() {
  ::nerd::api::GeoCoordinate point{121.499721, 31.239846};
  int radius = 500;
  std::vector<::nerd::api::TileIDType>
      allRouteTileIds;  //离线NDS数据中包含的所有tile ID
  //读取离线NDS数据中包含的所有tile id，后面可以通过这里的tile id 读取tile 数据
  route_api_->GetAllTileIDs(
      allRouteTileIds);  //仅存在离线NDS数据和 offline_debug 为true时有效
  //获取 SD 和 HDAir 数据样例
  auto route_tile_id = ::nerd::api::utils::GetTileIdByGeoCoordinate(point, 13);
  getSDMapAndHDAirData(point, radius, route_tile_id);

  std::vector<::nerd::api::TileIDType>
      allLaneTileIds;  //离线NDS数据中包含的所有tile ID
  //读取离线NDS数据中包含的所有tile id，后面可以通过这里的tile id 读取tile 数据
  lane_api_->GetAllTileIDs(
      allLaneTileIds);  //仅存在离线NDS数据和 offline_debug 为true时有效
  //获取HD数据样例
  auto lane_tile_id = ::nerd::api::utils::GetTileIdByGeoCoordinate(point, 15);
  getHDMapData(point, radius, lane_tile_id);

  return 0;
}

bool HADataManager::FillSDMapInfo(gwm::sdmap::SDMap &map_info) {
  ::nerd::api::GeoCoordinate point{121.499721, 31.239846};
  auto sd_loc = map_info.mutable_sd_location();
  sd_loc->set_roadid(10001111212);
  sd_loc->set_offset(0);
  auto match_point = sd_loc->mutable_matchpoint();
  match_point->set_x(357126.40186386066);
  match_point->set_y(3457153.639955433);
  int radius = 500;
  //构造矩形框范围
  ::nerd::api::Rect rect = ::nerd::api::utils::BuildRectByLength(point, radius);

  ::nerd::api::GetMapDataByRectParam l_param;
  l_param.level =
      ::nerd::api::MapLevel::kLEVEL_13;  //获取SD数据，地图缩放层级13层
  l_param.rect = rect;
  auto route_rec_callback = std::make_shared<RectCallback>();
  route_api_->AsyncGetDataByRect(l_param, route_rec_callback);
  // 等待结果
  route_rec_callback->Wait();

  auto result = route_rec_callback->GetResult();
  if (result->data_cube != nullptr) {
    auto sdLinkLayer = result->data_cube->GetSDLinkLayer();
    int ret = ConvertSDMapElementToMapInfo(sdLinkLayer, map_info);
    if (ret != 0) {
      return false;
    }
  }

  return true;
}

int HADataManager::AddSDMapRoadFromLink(const ::nerd::api::ILinkConstPtr link,
                                        gwm::sdmap::SDMap &map_info) {
  int ret = 0;
  auto road = map_info.add_road();
  road->set_roadid(link->GetID().GetHashId());
  const auto &attr = link->GetAttribute();
  road->set_lane_count(attr.lane_num_sum);
  road->set_anglef(attr.start_angle);
  road->set_anglet(attr.end_angle);
  road->set_road_type(
      TypeAdapter::ConvertRoadTypeFromTencent(attr.road_class_type));
  if (attr.road_kind_types.size() > 0) {
    road->set_road_form(
        TypeAdapter::ConvertRoadFormwayFromTencent(attr.road_kind_types[0]));
  }
  road->set_length(attr.length);

  std::shared_ptr<std::vector<::nerd::api::Coordinate>> coords =
      link->GetGeometry();
  auto count = coords.get()->size();
  double utm_east = .0;
  double utm_north = .0;
  int utm_zone = 0;
  DebugCode(LOG_INFO << "link_id:" << link->GetID().link_id << " coordiates: ");
  for (int i = 0; i < count; i++) {
    auto src_point = coords->data()[i];
    auto des_point = road->add_points();
    auto geo_coord = src_point.ToGeo();
    gwm::Coordinates::getInstance().gcj02ToUtm(geo_coord.y, geo_coord.x,
                                               utm_east, utm_north, utm_zone);
    des_point->set_x(utm_east);
    des_point->set_y(utm_north);
    des_point->set_z(geo_coord.relative_z);
    DebugCode(LOG_INFO << geo_coord.x << "," << geo_coord.y << "," << utm_east
                       << "," << utm_north << "," << utm_zone << ";");
  }
  DebugCode(LOG_INFO << std::endl);
  auto connect_roads = road->mutable_connect_road();
  auto previousLinkIds = link->GetPreviousIDs(attr.direction, false);
  for (auto idtype : previousLinkIds) {
    connect_roads->add_fromroadids(idtype.GetHashId());
  }
  auto nextLinkIds = link->GetNextIDs(attr.direction, false);
  for (auto idtype : nextLinkIds) {
    connect_roads->add_toroadids(idtype.GetHashId());
  }
  // 车信信息
  auto laneInfos = link->GetGuidance(::nerd::api::GuidanceType::kLaneMetaInfo);
  if (!laneInfos.empty()) {
    DebugCode(LOG_INFO << "laneInfo.size: " << laneInfos.size() << std::endl);
  }
  for (auto laneInfoPtr : laneInfos) {
    auto laneInfo =
        std::static_pointer_cast<const ::nerd::api::LaneInfoMeta>(laneInfoPtr);
    if (laneInfo == nullptr) {
      continue;
    }
    auto nodeid = laneInfo->GetNodeID();  // 进入节点id
    for (const auto &topo : laneInfo->lane_topos) {
      auto arrow = topo.lane_arrow;  // 转向箭头
      road->add_arrowmarkings(arrow);
    }
  }

  return ret;
}

int HADataManager::ConvertSDMapElementToMapInfo(
    ::nerd::api::ISDLinkLayer *sdLinkLayer, gwm::sdmap::SDMap &map_info) {
  if (sdLinkLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    DebugCode(LOG_INFO << "sdLinkLayer is nullptr " << std::endl);
    return -1;
  }
  auto links = sdLinkLayer->GetLinkArr();
  DebugCode(LOG_INFO << "sdLinkLayer->GetLinkArr().size(): " << links.size()
                     << std::endl);
  for (const auto &link : links) {
    AddSDMapRoadFromLink(link, map_info);
  }
  return 0;
}

int HADataManager::AddSDMapRoadFromTxLink(
    const std::vector<tca::TxLink> &matched_links,
    gwm::sdmap::SDMap &map_info) {
  int ret = 0;
  std::cout << "Tencent All coordinates:";
  for (auto &tx_link : matched_links) {
    auto &tpid = tx_link.tpid;
    if (tpid.status != tca::EXISTED) {
      // LOG_WARN << "tx_link matched failure."
      //          << " raw link_id:" << tx_link.raw_link_id
      //          << " dir:" << tx_link.dir << " length:" << tx_link.length
      //          << " lane_number:" << tx_link.lane_number
      //          << " tpid.status:" << tx_link.tpid.status
      //          << " tpid.tile_id:" << tx_link.tpid.tile_id
      //          << " tp link id:" << tx_link.tpid.id
      //          << " points.size:" << tx_link.points.size() << std::endl);
      continue;
    }
    auto road = map_info.add_road();
    road->set_roadid(tx_link.raw_link_id);
    road->set_lane_count(tx_link.lane_number);
    road->set_length(tx_link.length);

    auto count = tx_link.points.size();
    double utm_east = .0;
    double utm_north = .0;
    double lon = .0, lat = .0;
    int utm_zone = 0;
    // DebugCode(LOG_INFO << "link_id:" << tx_link.raw_link_id << " coordiates:
    // ";

    auto &coord = gwm::Coordinates::getInstance();
    for (int i = 0; i < count; i++) {
      auto &src_point = tx_link.points[i];
      auto des_point = road->add_points();
      lon = src_point.lng / coord.LNG_LAT_MOD;
      lat = src_point.lat / coord.LNG_LAT_MOD;
      gwm::Coordinates::getInstance().gcj02ToUtm(lat, lon, utm_east, utm_north,
                                                 utm_zone);
      des_point->set_x(utm_east);
      des_point->set_y(utm_north);
      // DebugCode(LOG_INFO << lon << "," << lat << "," << utm_east << "," <<
      // utm_north
      //        << "," << utm_zone << ";");
    }
  }

  return ret;
}

int HADataManager::MatchRoadLinksToSDMap(
    const std::vector<tca::TxLink> &matched_links,
    gwm::sdmap::SDMap &map_info) {
  int ret = 0;
  std::vector<::nerd::api::TileIDType> allLaneTileIds;
  route_api_->GetAllTileIDs(allLaneTileIds);
  DebugCode(LOG_INFO << "All lane tile ids:" << allLaneTileIds.size()
                     << " tile id list:");
  for (auto tile_id : allLaneTileIds) {
    DebugCode(LOG_INFO << tile_id << ",");
  }
  DebugCode(LOG_INFO << std::endl);
  std::unordered_set<::nerd::api::TileIDType> all_tiles_set;
  all_tiles_set.insert(allLaneTileIds.begin(), allLaneTileIds.end());
  DebugCode(LOG_INFO << "all tiles set.size:" << all_tiles_set.size()
                     << " tile id list:");
  for (auto id : all_tiles_set) {
    DebugCode(LOG_INFO << id << ",");
  }
  DebugCode(LOG_INFO << std::endl;);
  std::set<::nerd::api::TileIDType> path_tile_ids;
  std::vector<tca::TPIDInfo> link_ids;
  for (auto &tx_link : matched_links) {
    auto &tpid = tx_link.tpid;
    if (tpid.status != tca::EXISTED) {
      LOG_WARN << "tx_link matched failure."
               << " raw link_id:" << tx_link.raw_link_id
               << " dir:" << tx_link.dir << " length:" << tx_link.length
               << " lane_number:" << tx_link.lane_number
               << " tpid.status:" << tx_link.tpid.status
               << " tpid.tile_id:" << tx_link.tpid.tile_id
               << " tp link id:" << tx_link.tpid.id
               << " points.size:" << tx_link.points.size() << std::endl;
      continue;
    }
    if (all_tiles_set.count(tx_link.tpid.tile_id) == 0) {
      LOG_WARN << "Local HA does not contain the tile id"
               << " raw link_id:" << tx_link.raw_link_id
               << " dir:" << tx_link.dir << " length:" << tx_link.length
               << " lane_number:" << tx_link.lane_number
               << " tpid.status:" << tx_link.tpid.status
               << " tpid.tile_id:" << tx_link.tpid.tile_id
               << " tp link id:" << tx_link.tpid.id
               << " points.size:" << tx_link.points.size() << std::endl;

      continue;
    }
    path_tile_ids.insert(tpid.tile_id);
    link_ids.emplace_back(tpid);
  }
  if (matched_links.size() == 0) {
    LOG_ERROR << "matched_links.size is 0" << std::endl;
    ret = -1;
    return ret;
  }
  DebugCode(LOG_INFO << "matched_links.size:" << matched_links.size()
                     << std::endl);
  auto &first_link = matched_links.front();
  auto sd_loc = map_info.mutable_sd_location();
  sd_loc->set_roadid(first_link.raw_link_id);
  sd_loc->set_offset(0);
  auto match_point = sd_loc->mutable_matchpoint();
  auto first_poc = first_link.points.front();
  auto &coord = gwm::Coordinates::getInstance();
  double lng = first_poc.lng / coord.LNG_LAT_MOD;
  double lat = first_poc.lat / coord.LNG_LAT_MOD;
  double utm_east = .0, utm_north = .0;
  int utm_zone = 0;
  coord.gcj02ToUtm(lat, lng, utm_east, utm_north, utm_zone);

  DebugCode(LOG_INFO << "head location lng,lat:" << first_poc.lng << ","
                     << first_poc.lat << "," << utm_east << "," << utm_north
                     << "," << utm_zone << std::endl);
  match_point->set_x(utm_east);
  match_point->set_y(utm_north);

  if (link_ids.empty()) {
    LOG_ERROR << "No local nds data existed." << std::endl;
    ret = -1;
    return ret;
  }

  ::nerd::api::GetMapDataBatchParam route_batch_param;
  for (auto &item : path_tile_ids) {
    route_batch_param.tile_id_list.insert(item);
  }
  auto route_tile_callback = std::make_shared<TileCallback>();
  route_api_->AsyncGetDataByTileId(route_batch_param, route_tile_callback);
  route_tile_callback->Wait();
  auto result = route_tile_callback->GetResult();
  auto dataCubes = result->data_cubes;
  DebugCode(LOG_INFO << "dataCubes.size:" << dataCubes.size() << std::endl);
  std::unordered_map<::nerd::api::TileIDType, ::nerd::api::ISDLinkLayer *>
      data_sources;
  for (auto datacube : dataCubes) {
    ::nerd::api::TileIDType tile_id = datacube->GetTileId();
    auto sdLinkLayer = datacube->GetSDLinkLayer();
    data_sources[tile_id] = sdLinkLayer;
  }
  ::nerd::api::TPIDType tpid_type;
  for (auto &tpid : link_ids) {
    auto it_sdLinkLayer = data_sources.find(tpid.tile_id);
    if (it_sdLinkLayer != data_sources.end()) {
      auto sdLinkLayer = it_sdLinkLayer->second;
      tpid_type.tile_id = tpid.tile_id;
      tpid_type.tp_id = tpid.id;
      auto link = sdLinkLayer->GetLinkByTPID(tpid_type);
      AddSDMapRoadFromLink(link, map_info);
    }
  }

  return ret;
}

}  // namespace nerd
}  // namespace tc_map
