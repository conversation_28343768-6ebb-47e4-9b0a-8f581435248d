/*
 * File: hd_data_manager.cc
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-12
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include <cassert>
#include <cstdint>
#include <fstream>
#include <future>
#include <iomanip>
#include <iostream>
#include <memory>
#include <set>
#include <sstream>
#include <string>
#include <unordered_map>

#include "cloud-atlas/tencent_cloud_atlas_typedef.h"
#include "dto/types.h"
#include "nerd-api/api/map_data_engine_api.h"
#include "nerd-api/api/nerd_utils.h"
#include "nerd_api/nerd_version.h"
#include "proto/sd_map.pb.h"
#include "tc_map/nerd/TypeAdapter.hpp"
#include "tc_map/nerd/hd_data_manager.hpp"
#include "util/Coordinates.h"
#include "util/log.hpp"

namespace tc_map {
namespace nerd {

using ::nerd::api::GeoCoordinate;

HDDataManager::HDDataManager() {}

HDDataManager::~HDDataManager() {}

int HDDataManager::InitSDK(const std::string &data_dir,
                           const std::string &log_dir,
                           long log_expiration_hours, long max_log_store_bytes,
                           long max_signle_log_file_bytes) {
  // 初始化log，路径相对于可执行程序而言,当然也可以是绝对路径
  ::nerd::api::InitLogger(
      log_dir, false, "tencent_nerd_hd_",        //日志文件名前缀
      std::chrono::hours(log_expiration_hours),  //日志存储时间限制
      max_log_store_bytes,         //日志存储大小的最大限制
      max_signle_log_file_bytes);  //单个日志文件大小限制

  // 先调用InitGlobalConfig, 只需要调用一次
  ::nerd::api::GlobalConfig global_config;
  // 磁盘缓存路径, 路径相对于可执行程序而言,当然也可以是绝对路径
  global_config.data_root_path = data_dir;
  // 版本文件路径, 路径相对于可执行程序而言,当然也可以是绝对路径
  global_config.version_file_path = global_config.data_root_path;
  // global_config.offline_debug =
  // false;
  // 是否开启离线调试模式，默认false。设置为true时，offline_city_code_mode无效，使用全国NDS大文件。
  global_config.offline_debug = true;
  // 离线模式, false:使用9x9级Tile九宫格模式  true:使用CityCode模式
  global_config.offline_city_code_mode = false;
  //这里非常重要，需要注入腾讯专门提供的渠道号，若没有填写正确的渠道号将无法获取在线数据
  global_config.channel = "POC";
  // 此处需要填写每台设备的真实唯一的Id，长度不能超过32，否则会报鉴权失败
  global_config.deviceId = "testDeviceId";
  ::nerd::api::InitGlobalConfig(global_config);

  DebugCode(LOG_INFO << "sdk detail version=" << ::nerd::GetVersionDetail(true)
                     << std::endl);
  // 创建各个building block对应的引擎
  static auto create_engine =
      [](::nerd::api::MapDataBuildingBlockID build_block_id)
      -> std::unique_ptr<::nerd::api::MapDataEngineApi> {
    ::nerd::api::APIConfig api_config;
    api_config.user_id = "building_block:" +
                         std::to_string(static_cast<int32_t>(build_block_id));
    api_config.build_block_id = build_block_id;
    api_config.preference = ::nerd::api::DataPreference::
        kOfflineOnly;  //这里只推荐在线模式，或者离线模式，其他的在线优先等模式可能存在数据版本不一致的问题
    return ::nerd::api::CreateEngine(api_config);
  };

  //创建获取HD数据的引擎实例
  lane_api_ = create_engine(::nerd::api::MapDataBuildingBlockID::kLANE);
  assert(lane_api_);
  return 0;
}

void HDDataManager::getHDMapElement(::nerd::api::IHDLaneLayer *hdLayer) {
  if (hdLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    DebugCode(LOG_INFO << "hdLayer is nullptr" << std::endl);
    return;
  }
  auto lanes = hdLayer->GetLaneArr();
  DebugCode(LOG_INFO << "lanes.size = " << lanes.size() << std::endl);
  auto crossAreas = hdLayer->GetCrossAreaArr();
  DebugCode(LOG_INFO << "crossAreas.size = " << crossAreas.size() << std::endl);
  auto laneGroups = hdLayer->GetLaneGroupArr();
  DebugCode(LOG_INFO << "laneGroups.size = " << laneGroups.size() << std::endl);
}

void HDDataManager::getHDMapDataByRect(
    const ::nerd::api::GeoCoordinate &center_point, int radius) {
  //构造矩形框范围
  ::nerd::api::Rect rect =
      ::nerd::api::utils::BuildRectByLength(center_point, radius);

  ::nerd::api::GetMapDataByRectParam l_param;
  l_param.level =
      ::nerd::api::MapLevel::kLEVEL_15;  //获取HD数据，地图缩放层级15层
  l_param.rect = rect;
  auto lane_rec_callback = std::make_shared<RectCallback>();
  lane_api_->AsyncGetDataByRect(l_param, lane_rec_callback);
  // 等待结果
  lane_rec_callback->Wait();

  auto result = lane_rec_callback->GetResult();
  if (result->data_cube != nullptr) {
    auto hdLayer = result->data_cube->GetHDLaneLayer();
    ::nerd::api::AngleDegreeType angle_type(10.0);
    ::nerd::api::AngleDegreeType max_angle_type(10.0);
    auto lanes =
        hdLayer->GetClosetLanes(center_point, 10, angle_type, max_angle_type);
    DebugCode(LOG_INFO << "HD lanes.size:" << lanes.size() << std::endl);
    for (auto &lane_pair : lanes) {
      auto &lane = lane_pair.first;
      auto left_lane = lane->GetLeftLane();
      auto right_lane = lane->GetRightLane();
      auto left_boundary = lane->GetLeftBoundary();
      auto right_boundary = lane->GetRightBoundary();
      auto &open_to_left = lane->GetOpenToLeft();
      auto &open_to_right = lane->GetOpenToRight();
      auto &lane_types = lane->GetTypes();
      auto lane_direction_mask = lane->GetDirection();
      auto max_speed_kmph = lane->GetMaxSpeedKmph();
      auto max_speed_source = lane->GetMaxSpeedSource();
      auto min_speed_kmph = lane->GetMinSpeedKmph();
      auto min_speed_source = lane->GetMinSpeedSource();
      auto &access_restrict = lane->GetAccessRestrict();
      auto &lane_position = lane->GetLanePosition();
      auto &center_geometry = lane->GetCenterGeometry();
      auto &lane_center_geometry_distance_to_begin =
          lane->GetLaneCenterGeometryDistanceToBegin();
      auto lane_length_type = lane->GetLengthCm();
      auto seq_num = lane->GetSeqNum();
      auto &surface_objects = lane->GetSurfaceObjects();
      auto lane_change_points = lane->GetLaneChangePoints();
      auto trans_type = lane->GetTransType();

      DebugCode(LOG_INFO << "lane lane_type:"
                         << static_cast<int>(lane->GetType()) << std::endl);
      auto &in_line_pos = lane_pair.second;
      auto geo_poi = in_line_pos.coordinate.ToGeo();
      DebugCode(LOG_INFO << "in_line_pos coordinate_start:"
                         << in_line_pos.coordinate_start
                         << " offset_length:" << in_line_pos.offset_length
                         << " start_length: " << in_line_pos.start_length
                         << " distance_to_line:" << in_line_pos.distance_to_line
                         << "lon,lat:" << geo_poi.x << "," << geo_poi.y
                         << std::endl);
    }
  }
}

void HDDataManager::getHDMapDataByTileID(::nerd::api::TileIDType tile_id) {
  DebugCode(LOG_INFO << "lane_tile_id: " << tile_id << std::endl);
  // 异步获取tile数据
  ::nerd::api::GetMapDataBatchParam batch_param;
  batch_param.tile_id_list.insert(tile_id);
  auto lane_tile_callback = std::make_shared<TileCallback>();
  lane_api_->AsyncGetDataByTileId(batch_param, lane_tile_callback);
  // 等待结果
  lane_tile_callback->Wait();
  auto result = lane_tile_callback->GetResult();
  auto dataCubes = result->data_cubes;
  for (auto datacube : dataCubes) {
    auto hdLayer = datacube->GetHDLaneLayer();
    getHDMapElement(hdLayer);
  }
};

void HDDataManager::getHDMapData(const ::nerd::api::GeoCoordinate &center_point,
                                 int radius, ::nerd::api::TileIDType tileId) {
  getHDMapDataByTileID(tileId);  //通过tile ID获取HD数据
  getHDMapDataByRect(center_point, radius);  //通过矩形框获取对应范围内的HD数据
}

void HDDataManager::getAroundTiles(::nerd::api::TileIDType id) {
  DebugCode(LOG_INFO << "tile id: " << id << std::endl);
  // test extend_num <= 3
  for (int extend_num = -1; extend_num <= 3; extend_num++) {
    DebugCode(LOG_INFO << "  extend_num: " << extend_num << std::endl);
    auto aroundTiles = ::nerd::api::GetAroundTiles(id, extend_num);
    DebugCode(LOG_INFO << "  aroundTiles.size: " << aroundTiles.size()
                       << std::endl);
    for (auto tile_tuple : aroundTiles) {
      auto tile_id = std::get<0>(tile_tuple);
      auto tile_row = std::get<1>(tile_tuple);
      auto tile_col = std::get<2>(tile_tuple);
      DebugCode(LOG_INFO << "    around tile id: " << tile_id << ", row: "
                         << tile_row << ", col: " << tile_col << std::endl);
    }
  }
}

// 只检查文件LANE.NDS
int HDDataManager::LoadDemo() {
  ::nerd::api::GeoCoordinate point{116.5090685, 39.7554497};
  int radius = 500;
  //离线NDS数据中包含的所有tile ID
  std::vector<::nerd::api::TileIDType> allLaneTileIds;
  //读取离线NDS数据中包含的所有tile id，后面可以通过这里的tile id 读取tile 数据
  //仅存在离线NDS数据和 offline_debug 为true时有效
  lane_api_->GetAllTileIDs(allLaneTileIds);
  DebugCode(LOG_INFO << "allLaneTileIds.size():" << allLaneTileIds.size()
                     << std::endl);
  //获取HD数据样例
  auto lane_tile_id = ::nerd::api::utils::GetTileIdByGeoCoordinate(point, 15);
  getHDMapData(point, radius, lane_tile_id);
  return 0;
}

int HDDataManager::SetHDLaneBoundary(IBoundaryConstPtr tc_lane_boundary,
                                     ::gwm::hdmap::LaneBoundary *gwm_boundary) {
  int ret = 0;
  if (tc_lane_boundary == nullptr) {
    return ret;
  }
  auto &coord_helper = gwm::Coordinates::getInstance();
  double utm_east = .0;
  double utm_north = .0;
  int utm_zone = 0;
  ::nerd::api::BoundaryIDTypeHash boundary_hash_id;
  uint32_t boundary_id = boundary_hash_id(tc_lane_boundary->GetID());
  gwm_boundary->set_id(boundary_id);
  gwm_boundary->set_length(tc_lane_boundary->GetLength());
  gwm_boundary->set_virtual_(false);
  auto gwm_boundary_polyline = gwm_boundary->mutable_boundary();
  auto &tc_boundary_geometry = tc_lane_boundary->GetBoundaryGeometry();
  for (auto &tc_coord : *tc_boundary_geometry) {
    auto tc_geo_coord = tc_coord.ToGeo();
    coord_helper.gcj02ToUtm(tc_geo_coord.y, tc_geo_coord.x, utm_east, utm_north,
                            utm_zone);
    auto gwm_point = gwm_boundary_polyline->add_point();
    gwm_point->set_x(utm_east);
    gwm_point->set_y(utm_north);

    std::cout << std::fixed << std::setprecision(6);
    DebugCode(LOG_INFO << "boundary y,x" << tc_geo_coord.y << ","
                       << tc_geo_coord.x << "utm:" << utm_east << ","
                       << utm_north << "," << utm_zone << std::endl);
  }
  auto &boundary_change_points = tc_lane_boundary->GetBoundaryChangePointsV2();
  ::nerd::api::InLinePosition in_line_position;
  for (auto boundary_set : boundary_change_points) {
    auto boundary_set_style = boundary_set.boundary_set_style_;
    auto &boundary_types = boundary_set_style->boundary_types;
    auto gwm_lane_boundary_type = gwm_boundary->add_boundary_type();
    for (auto boundary_type : boundary_types) {
      if (boundary_type == ::nerd::api::BoundaryType::VIRTUAL_LINE ||
          boundary_type == ::nerd::api::BoundaryType::EDGE_VIRTUAL_LINE ||
          boundary_type == ::nerd::api::BoundaryType::MISSING_VIRTUAL_LINE ||
          boundary_type == ::nerd::api::BoundaryType::CROSS_VIRTUAL_LINE ||
          boundary_type == ::nerd::api::BoundaryType::VIRTUAL_AUXILIARY_LINE) {
        gwm_boundary->set_virtual_(true);
      }

      switch (boundary_type) {
        case ::nerd::api::BoundaryType::CURB:
          gwm_lane_boundary_type->add_types(
              gwm::hdmap::LaneBoundaryType_Type_CURB);
          break;
        case ::nerd::api::BoundaryType::GUARDRAIL:
        case ::nerd::api::BoundaryType::WALL:
        case ::nerd::api::BoundaryType::PROTECT_NET:
        case ::nerd::api::BoundaryType::POLE:
        case ::nerd::api::BoundaryType::ISOLATION_POLE:
        case ::nerd::api::BoundaryType::CONSTRUCTION_ENCLOSURE:
        case ::nerd::api::BoundaryType::MOVABLE_GUARD_RAIL:
          gwm_lane_boundary_type->add_types(
              gwm::hdmap::LaneBoundaryType_Type_OTHER_PHYSICAL_OBSTACLE);
          break;
      }
    }
    double offset_dis = tc_lane_boundary->CalcDistanceToBegin(
        boundary_set.start_position, in_line_position);
    gwm_lane_boundary_type->set_s(offset_dis);

    auto &marking_types = boundary_set_style->marking_types;
    auto &colors = boundary_set_style->colors;
    assert(colors.size() == marking_types.size());
    for (std::size_t i = 0; i < marking_types.size(); i++) {
      auto &marking_type = marking_types[i];
      auto &color = colors[i];
      switch (marking_type) {
        case ::nerd::api::BoundaryMarkingType::kNO_DATA:
          gwm_lane_boundary_type->add_types(
              gwm::hdmap::LaneBoundaryType_Type_UNKNOWN);
          break;

        case ::nerd::api::BoundaryMarkingType::kSOLID:
          if (color == ::nerd::api::BoundaryMarkingColorType::kYELLOW) {
            gwm_lane_boundary_type->add_types(
                gwm::hdmap::LaneBoundaryType_Type_SOLID_YELLOW);
          } else if (color == ::nerd::api::BoundaryMarkingColorType::kWHITE) {
            gwm_lane_boundary_type->add_types(
                gwm::hdmap::LaneBoundaryType_Type_SOLID_WHITE);
          }
          break;
        case ::nerd::api::BoundaryMarkingType::kDASHED:
          if (color == ::nerd::api::BoundaryMarkingColorType::kYELLOW) {
            gwm_lane_boundary_type->add_types(
                gwm::hdmap::LaneBoundaryType_Type_DOTTED_YELLOW);
          } else if (color == ::nerd::api::BoundaryMarkingColorType::kWHITE) {
            gwm_lane_boundary_type->add_types(
                gwm::hdmap::LaneBoundaryType_Type_DOTTED_WHITE);
          }
          break;
      }
    }
  }
  if (gwm_boundary->boundary_type_size() > 0) {
    auto &boundary_type = gwm_boundary->boundary_type(0);
    if (boundary_type.types_size() > 0) {
      gwm_boundary->set_type(boundary_type.types(0));
    }
  }
  return ret;
}

double HDDataManager::GetSpeedBySpeedType(::nerd::api::SpeedType speed_type) {
  double speed = .0;
  switch (speed_type) {
    case 1:
      speed = 36.1111111;  // 130km/h
      break;
    case 2:
      speed = 33.3333333;  // 120km/h
      break;
    case 3:
      speed = 27.7777777;  // 100km/h
      break;
    case 4:
      speed = 25.0;  // 90km/h
      break;
    case 5:
      speed = 19.444444;  // 70km/h
      break;
    case 6:
      speed = 13.888888;  // 50km/h
      break;
    case 7:
      speed = 8.333333;  // 30km/h
      break;
    case 8:
      3.055555;  // 11km/h
      break;
  }
  return speed;
}

int HDDataManager::SetHDLane(ILaneConstPtr tc_lane, RoadClassType road_class,
                             RoadKindTypeVec &road_kind_types, Lane &gwm_lane) {
  int ret = 0;
  auto lane_id_type = tc_lane->GetID();
  ::nerd::api::LaneIDTypeHash hash_id;
  uint32_t lane_id = hash_id(lane_id_type);
  gwm_lane.set_id(lane_id);
  double len = tc_lane->GetLength();
  gwm_lane.set_length(len);
  SetHDLaneBoundary(tc_lane->GetLeftBoundary(),
                    gwm_lane.mutable_left_boundary());
  SetHDLaneBoundary(tc_lane->GetRightBoundary(),
                    gwm_lane.mutable_right_boundary());
  gwm_lane.set_left_boundary_id(gwm_lane.left_boundary().id());
  gwm_lane.set_right_boundary_id(gwm_lane.right_boundary().id());

  auto tc_lane_type = tc_lane->GetType();
  auto gwm_lane_type = TypeAdapter::ConvertHDMapLaneTypeFromTencent(
      road_class, road_kind_types, tc_lane_type);
  gwm_lane.set_type(gwm_lane_type);
  double max_speed = GetSpeedBySpeedType(tc_lane->GetMaxSpeedKmph());
  gwm_lane.set_speed_limit(max_speed);
  auto next_ids = std::move(tc_lane->GetNextIDs());
  for (auto next_id : next_ids) {
    auto next_hash_id = hash_id(next_id);
    gwm_lane.add_successor_id(next_hash_id);
  }
  auto previous_ids = std::move(tc_lane->GetPreviousIDs());
  for (auto prev_id : previous_ids) {
    auto prev_hash_id = hash_id(prev_id);
    gwm_lane.add_successor_id(prev_hash_id);
  }
  auto tc_left_lane = tc_lane->GetLeftLane();
  if (tc_left_lane != nullptr) {
    auto tc_left_lane_id = tc_left_lane->GetID();
    gwm_lane.set_left_neighbor_forward_lane_id(hash_id(tc_left_lane_id));
  }
  auto tc_right_lane = tc_lane->GetRightLane();
  if (tc_right_lane != nullptr) {
    auto tc_right_lane_id = tc_right_lane->GetID();
    gwm_lane.set_right_neighbor_forward_lane_id(hash_id(tc_right_lane_id));
  }
  gwm_lane.set_cost(gwm_lane.length());
  auto trans_type = tc_lane->GetTransType();
  switch (trans_type) {
    case ::nerd::api::TransLaneType::DISPATCH:
      // gwm_lane.set_merge_splits();
      break;
    case ::nerd::api::TransLaneType::JOINT:
      //
      break;
  }
  auto change_kind = tc_lane->GetTopologyChangeKind();
  switch (change_kind) {
    case ::nerd::api::TopologyChangeKind::kRIGHT_DETACH:
      gwm_lane.set_merge(gwm::hdmap::Lane_MergeType_FROM_LEFT);
      break;
    case ::nerd::api::TopologyChangeKind::kLEFT_JOIN:
      gwm_lane.set_merge(gwm::hdmap::Lane_MergeType_FROM_RIGHT);
      break;
    default:
      gwm_lane.set_merge(gwm::hdmap::Lane_MergeType_NONE);
      break;
  }
  gwm_lane.set_min_width(tc_lane->GetWidthMin());
  auto &center_geometry = tc_lane->GetCenterGeometry();
  auto &coord_helper = gwm::Coordinates::getInstance();
  double utm_east = .0;
  double utm_north = .0;
  double left_lane_width = .0;
  double right_lane_width = .0;
  double left_road_width = .0;
  double right_road_width = .0;
  int utm_zone = 0;
  auto gwm_centerline = gwm_lane.mutable_centerline();
  double center_offset_dis = .0;
  ::nerd::api::InLinePosition in_line_position;
  for (auto &tc_coord : *center_geometry) {
    auto tc_geo_coord = tc_coord.ToGeo();
    coord_helper.gcj02ToUtm(tc_geo_coord.y, tc_geo_coord.x, utm_east, utm_north,
                            utm_zone);
    auto gwm_point = gwm_centerline->add_point();
    gwm_point->set_x(utm_east);
    gwm_point->set_y(utm_north);
    center_offset_dis =
        tc_lane->CalcCenterDistanceToBegin(tc_geo_coord, in_line_position);
    gwm_lane.add_centerline_s(center_offset_dis);
    DebugCode(LOG_INFO << "center y,x" << tc_geo_coord.y << ","
                       << tc_geo_coord.x << "utm:" << utm_east << ","
                       << utm_north << "," << utm_zone << std::endl);
    left_lane_width =
        tc_lane->CalcDistanceToLeftBoundary(tc_geo_coord, in_line_position);
    auto left_lane_sample = gwm_lane.add_left_sample();
    left_lane_sample->set_s(in_line_position.start_length);
    left_lane_sample->set_width(left_lane_width);

    right_lane_width =
        tc_lane->CalcDistanceToRightBoundary(tc_geo_coord, in_line_position);
    auto right_lane_sample = gwm_lane.add_right_sample();
    right_lane_sample->set_s(in_line_position.start_length);
    right_lane_sample->set_width(right_lane_width);

    left_road_width =
        tc_lane->CalcDistanceToLeftRoadBoundary(tc_geo_coord, in_line_position);
    auto left_road_sample = gwm_lane.add_left_road_sample();
    left_road_sample->set_s(in_line_position.start_length);
    left_road_sample->set_width(left_road_width);

    right_road_width = tc_lane->CalcDistanceToRightRoadBoundary(
        tc_geo_coord, in_line_position);
    auto right_road_sample = gwm_lane.add_right_road_sample();
    right_road_sample->set_s(in_line_position.start_length);
    right_road_sample->set_width(right_road_width);

    auto &access_restricts = tc_lane->GetAccessRestrict();
    bool is_contain_vechile_type = false;
    for (auto access_restrict : access_restricts) {
      auto tc_limit_vehicle_types = access_restrict->GetLimitVehicleType();
      is_contain_vechile_type = false;
      for (auto vehicle_type : tc_limit_vehicle_types) {
        if (vehicle_type == ::nerd::api::VehicleType::CAR) {
          is_contain_vechile_type = true;
        } else if (vehicle_type == ::nerd::api::VehicleType::MINIVAN) {
          is_contain_vechile_type = true;
        }
      }
      if (is_contain_vechile_type) {
        auto lane_rule = gwm_lane.add_lane_rules();
        lane_rule->set_vehicle_type(gwm::hdmap::LaneRule_VehicleType_DEFAULT);
        auto trigger = lane_rule->mutable_trigger();
        trigger->set_always(true);
        auto rule = lane_rule->mutable_rule();
        max_speed = GetSpeedBySpeedType(access_restrict->GetMaxSpeedKmph());
        rule->set_speed_limit(max_speed);
      }
      // lane_rule->set_vehicle_type(gwm::hdmap::LaneRule_VehicleType_LIGHT_TRUCK);
    }
  }
  return ret;
}

int HDDataManager::AddGWMLaneGroup(
    ::nerd::api::ILaneGroupConstPtr tc_lane_group, RoutingMapInfo &routing_map,
    std::unordered_map<uint32_t, uint32_t> &lane_id_to_indexes,
    gwm::hdmap::LaneGroup *gwm_lane_group, uint32_t &global_lane_index) {
  int ret = 0;
  uint32_t lane_hash_id = 0;
  uint32_t cur_lane_index = 0;
  ::nerd::api::LaneIDTypeHash hash_id;
  auto road_class = tc_lane_group->GetRoadClass();
  auto grp_attr = tc_lane_group->GetAttribute();
  auto road_kind_types = grp_attr.road_kind_types;
  DebugCode(LOG_INFO << "closet road name:" << grp_attr.name << " road_class:"
                     << static_cast<int>(grp_attr.road_class_type)
                     << " road class2:" << static_cast<int>(road_class)
                     << " road kind types:" << grp_attr.road_kind_types.size()
                     << std::endl);
  auto tc_lane_group_id = tc_lane_group->GetID();
  DebugCode(LOG_INFO << "closet tile id:" << tc_lane_group_id.tile_id
                     << " lane group id:" << tc_lane_group_id.lane_group_id
                     << std::endl);
  auto road_boundary = tc_lane_group->GetMainBoundary();
  auto grp_lanes = tc_lane_group->GetLanes();
  DebugCode(LOG_INFO << "closet grp_lanes.size():" << grp_lanes.size()
                     << std::endl);
  for (auto it_lane = grp_lanes.rbegin(); it_lane != grp_lanes.rend();
       it_lane++) {
    auto grp_lane = *it_lane;
    auto lane_id_type = grp_lane->GetID();
    lane_hash_id = hash_id(lane_id_type);
    if (lane_id_to_indexes.count(lane_hash_id) > 0) {
      cur_lane_index = lane_id_to_indexes[lane_hash_id];
    } else {
      cur_lane_index = global_lane_index++;
      lane_id_to_indexes[lane_hash_id] = cur_lane_index;
      auto gwm_lane = routing_map.add_lanes();
      // add Lane
      ret = SetHDLane(grp_lane, road_class, road_kind_types, *gwm_lane);
      if (ret != 0) {
        LOG_ERROR << "Set gwm_lane failed."
                  << "closet grp tile_id:" << lane_id_type.tile_id
                  << " grp lane_id:" << lane_id_type.lane_id
                  << " lane  hash id:" << lane_hash_id << std::endl;
      }
    }
    auto lane_frame_info = gwm_lane_group->add_lane_frame_info();
    lane_frame_info->set_lane_id(lane_hash_id);
    lane_frame_info->set_lane_index(cur_lane_index);
    // 待增加设置推荐车道策略
    lane_frame_info->set_lane_accessibility(
        gwm::hdmap::LanePassableInfo_LaneAccessibility_RECOMMEND);
    DebugCode(LOG_INFO << "closet grp tile_id:" << lane_id_type.tile_id
                       << " grp lane_id:" << lane_id_type.lane_id
                       << " lane  hash id:" << lane_hash_id << std::endl);
  }
  return ret;
}

int HDDataManager::GetRoutingMapInfo(const TrackList &track_list,
                                     RoutingMapInfo &routing_map) {
  int ret = 0;
  auto &coord_helper = gwm::Coordinates::getInstance();
  ::nerd::api::LaneIDTypeHash hash_id;
  double utm_east = .0;
  double utm_north = .0;
  int utm_zone = 0;
  uint32_t lane_hash_id = 0;
  uint32_t closet_lane_hash_id = 0;

  DataSources data_sources;
  for (auto &location : track_list.locations()) {
    ret = GetDataSource(location, data_sources);
  }
  std::unordered_map<uint32_t, uint32_t> lane_id_to_indexes;
  uint32_t global_lane_index = 0;
  uint32_t cur_lane_index = 0;
  for (auto data_source : data_sources) {
    auto loc = data_source.first;
    GeoCoordinate coordinate = {loc->point().x(), loc->point().y()};
    auto result = data_source.second->GetResult();
    auto hd_layer = result->data_cube->GetHDLaneLayer();
    ::nerd::api::AngleDegreeType angle_type(loc->heading_degree());
    ::nerd::api::AngleDegreeType max_angle_type(loc->max_tolerance_angle());
    auto tc_closet_lanes = hd_layer->GetClosetLanes(coordinate, loc->radias(),
                                                    angle_type, max_angle_type);
    DebugCode(LOG_INFO << "HD lanes.size:" << tc_closet_lanes.size()
                       << std::endl);
    auto gwm_routing_lane_info = routing_map.add_routing_lanes();
    gwm_routing_lane_info->set_time_stamp(loc->time_stamp());
    for (auto &lane_pair : tc_closet_lanes) {
      auto &tc_lane = lane_pair.first;
      auto closet_lane_id_type = tc_lane->GetID();
      auto &in_line_position = lane_pair.second;
      closet_lane_hash_id = hash_id(closet_lane_id_type);

      // set ego lane info
      auto gwm_ego_lane_info = gwm_routing_lane_info->add_ego_lane_info();
      gwm_ego_lane_info->set_lane_id(closet_lane_hash_id);
      gwm_ego_lane_info->set_lane_index(closet_lane_hash_id);
      gwm_ego_lane_info->set_prev_coordinate_index(
          in_line_position.coordinate_start);
      gwm_ego_lane_info->set_offset_length_from_prev_point(
          in_line_position.offset_length);
      gwm_ego_lane_info->set_offset_length_from_start_point(
          in_line_position.start_length);
      gwm_ego_lane_info->set_distance_to_line(
          in_line_position.distance_to_line);
      auto project_point = gwm_ego_lane_info->mutable_projecting_point();
      auto &tc_proj_point = in_line_position.coordinate;
      coord_helper.gcj02ToUtm(tc_proj_point.y, tc_proj_point.x, utm_east,
                              utm_north, utm_zone);
      project_point->set_x(utm_east);
      project_point->set_y(utm_north);
      // end set ego lane info

      auto lane_group_id_type = tc_lane->GetAttachedLaneGroupID();
      auto cross_area = tc_lane->GetAttachedCrossArea().lock();
      DebugCode(LOG_INFO << "closet tile_id:" << closet_lane_id_type.tile_id
                         << " closet lane_id:" << closet_lane_id_type.lane_id
                         << " closet Lane group id tile_id:"
                         << lane_group_id_type.tile_id << " lane group id:"
                         << lane_group_id_type.lane_group_id << std::endl);
      DebugCode(LOG_INFO << "closet coordinate_start:"
                         << in_line_position.coordinate_start
                         << " offset_length:" << in_line_position.offset_length
                         << " start_length:" << in_line_position.start_length
                         << " distane to line:"
                         << in_line_position.distance_to_line << std::endl);
      auto weaked_lane_group = tc_lane->GetAttachedLaneGroup();
      auto tc_lane_group = weaked_lane_group.lock();
      if (tc_lane_group) {
        auto tc_pre_groups = tc_lane_group->GetPrevious();
        for (auto tc_pre_lane_group : tc_pre_groups) {
          auto gwm_pre_lane_group = gwm_routing_lane_info->add_lane_groups();
          AddGWMLaneGroup(tc_pre_lane_group, routing_map, lane_id_to_indexes,
                          gwm_pre_lane_group, global_lane_index);
        }
        auto gwm_lane_group = gwm_routing_lane_info->add_lane_groups();
        AddGWMLaneGroup(tc_lane_group, routing_map, lane_id_to_indexes,
                        gwm_lane_group, global_lane_index);

        auto tc_next_groups = tc_lane_group->GetNext();
        for (auto tc_next_lane_group : tc_next_groups) {
          auto gwm_next_lane_group = gwm_routing_lane_info->add_lane_groups();
          AddGWMLaneGroup(tc_next_lane_group, routing_map, lane_id_to_indexes,
                          gwm_next_lane_group, global_lane_index);
        }
        if (lane_id_to_indexes.count(closet_lane_hash_id) > 0) {
          gwm_ego_lane_info->set_lane_index(
              lane_id_to_indexes[closet_lane_hash_id]);
        } else {
          LOG_ERROR << "set closet lane id failed," << closet_lane_hash_id
                    << std::endl;
        }
      }
    }
  }
  return ret;
}  // namespace nerd

int HDDataManager::GetDataSource(const LocationInfo &loc,
                                 DataSources &data_sources) {
  int ret = 0;
  GeoCoordinate coordinate = {loc.point().x(), loc.point().y()};
  ::nerd::api::Rect rect = ::nerd::api::utils::BuildRectByLength(
      coordinate, loc.radias());  // unit: meter

  ::nerd::api::GetMapDataByRectParam l_param;
  l_param.level =
      ::nerd::api::MapLevel::kLEVEL_15;  //获取HD数据，地图缩放层级15层
  l_param.rect = rect;
  auto lane_rec_callback = std::make_shared<RectCallback>();
  lane_api_->AsyncGetDataByRect(l_param, lane_rec_callback);
  // 等待结果
  lane_rec_callback->Wait();

  auto result = lane_rec_callback->GetResult();
  if (result->data_cube != nullptr) {
    auto hdLayer = result->data_cube->GetHDLaneLayer();
    if (hdLayer == nullptr) {
      LOG_ERROR << "hdLayer is nullptr" << std::endl;
    } else {
      data_sources[&loc] = lane_rec_callback;
    }
  } else {
    ret = -1;
    LOG_ERROR << "Get HD Labyer failed." << std::endl;
  }
  return ret;
}

}  // namespace nerd
}  // namespace tc_map
