
cmake_minimum_required(VERSION 3.16)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
project(sd_map_demo 
        VERSION 0.0.1
        LANGUAGES CXX
        )

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")

include(${THIRD_PARTY_DIR}/tencent/x86/FindTCmap.cmake)

find_package(Threads)
find_library(ZMQ_LIBRARY NAMES zmq)
find_package(Protobuf REQUIRED)
find_library(PROJ_LIBRARY proj PATHS /usr/local/lib NO_DEFAULT_PATH)  # 指定路径
if (NOT PROJ_LIBRARY)
    message(FATAL_ERROR "libproj not found in /usr/local/lib")
endif()


file(GLOB NERD_API_LIBRARIES "${TENCENT_SDK_DIR}/lib/*.so*")

set(target sd_map_demo)
set(pub_master "PUB_NODE")
set(sub_master "SUB_NODE")
set(hd_node "hd_data_convertor")

file(GLOB PROTO_SRCS ${PROJECT_ROOT_DIR}/proto/common/*.cc  ${PROJECT_ROOT_DIR}/proto/*.cc)
file(GLOB PROTO_HDRS ${PROJECT_ROOT_DIR}/proto/common/*.cc  ${PROJECT_ROOT_DIR}/proto/*.pb.h)
message(STATUS "proto_srcs: ${PROTO_SRCS}")

add_executable(${target} ${PROJECT_ROOT_DIR}/src/main.cc  ${PROTO_SRCS} ${PROTO_HDRS})

add_executable(${pub_master} 
               ${PROJECT_ROOT_DIR}/src/pub_node.cc 
               ${PROJECT_ROOT_DIR}/src/tc_map/nerd/ha_data_manager.cc
							 ${PROJECT_ROOT_DIR}/src/tc_map/nerd/TypeAdapter.cc
							 ${PROJECT_ROOT_DIR}/src/tc_map/nerd/callback_object.cc
							 ${PROJECT_ROOT_DIR}/src/tc_map/route_match/route_match.cpp
							 ${PROJECT_ROOT_DIR}/src/tc_map/route_match/http_interface_impl.cc
							 ${PROJECT_ROOT_DIR}/src/util/Coordinates.cpp
							 ${PROTO_SRCS} ${PROTO_HDRS}
							 )

add_executable(${sub_master} ${PROJECT_ROOT_DIR}/src/sub_node.cc  ${PROTO_SRCS} ${PROTO_HDRS} )

set(hd_codes   ${PROJECT_ROOT_DIR}/src/tc_map/hd_data_convertor.cc
               ${PROJECT_ROOT_DIR}/src/tc_map/nerd/hd_data_manager.cc
			   ${PROJECT_ROOT_DIR}/src/tc_map/nerd/TypeAdapter.cc
			   ${PROJECT_ROOT_DIR}/src/tc_map/nerd/callback_object.cc
			   ${PROJECT_ROOT_DIR}/src/tc_map/nerd/hd_map_to_geojson.cc
			   ${PROJECT_ROOT_DIR}/src/common/track_info_reader.cc
			   ${PROJECT_ROOT_DIR}/src/util/Coordinates.cpp
			   ${PROJECT_ROOT_DIR}/src/util/utm_to_latlon.cpp
			   ${PROTO_SRCS} ${PROTO_HDRS})
add_executable(${hd_node} ${hd_codes})

add_library(${routing_hd_map_lib} SHARED ${hd_codes})

target_include_directories(${routing_hd_map_lib} PUBLIC
    $<BUILD_INTERFACE:${PROJECT_ROOT_DIR}/include>
    $<BUILD_INTERFACE:${PROJECT_ROOT_DIR}/proto>
    $<INSTALL_INTERFACE:include>
    $<INSTALL_INTERFACE:proto>
)


include_directories( ${PROJECT_ROOT_DIR})
include_directories( ${PROJECT_ROOT_DIR}/include/)
include_directories( ${PROJECT_ROOT_DIR}/proto/)

nerd_api_target_deps(${target})
nerd_api_target_deps(${pub_master})
nerd_api_target_deps(${sub_master})
nerd_api_target_deps(${hd_node})
nerd_api_target_deps(${routing_hd_map_lib})

message(STATUS "proto library: ${PROTOBUF_LIBRARIES}   ${Protobuf_DIR}")
message(STATUS "zmq ${ZMQ_LIBRARY}")

target_link_libraries(${target} PRIVATE curl ${PROTOBUF_LIBRARIES} 
                      ${ZMQ_LIBRARY})

target_link_libraries(${pub_master} PRIVATE curl ${PROTOBUF_LIBRARIES} 
                      ${ZMQ_LIBRARY} ${PROJ_LIBRARY} )

target_link_libraries(${sub_master} PRIVATE curl ${PROTOBUF_LIBRARIES}
                      ${ZMQ_LIBRARY})

target_link_libraries(${hd_node} PRIVATE curl ${PROTOBUF_LIBRARIES} 
                      ${ZMQ_LIBRARY} ${PROJ_LIBRARY})

target_link_libraries(${routing_hd_map_lib} PRIVATE curl ${PROTOBUF_LIBRARIES}
                      ${ZMQ_LIBRARY} ${PROJ_LIBRARY})

install(TARGETS ${routing_hd_map_lib} ${hd_node} ${pub_master} ${sub_master}  EXPORT RoutingHDMapTargets
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
        RUNTIME DESTINATION bin
        INCLUDES DESTINATION include
)

install(FILES ${NERD_API_LIBRARIES}  DESTINATION tc_lib)
