#include <iostream>
#include <unistd.h>
#include <sstream>
#include <climits>
#include <iomanip>
#include <fstream>
#include <sstream>
#include <chrono>
#include <future>
#include <thread>
#include <filesystem>
#include "map_engine.h"
#include "hd_map_lane.pb.h"
#include "track_info_reader.hpp"
#include "util/message_io_handler.hpp"
#include "util/Coordinates.h"
#include "cJSON.h"
#include "hd_map_to_geojson.hpp"
#include "util/Coordinates.h"
std::unordered_map<uint32_t, baidu::imap::LinkPtr> links_um_;
std::unordered_map<uint32_t, baidu::imap::LanePtr> lanes_um_;
gwm::hdmap::RoutingMapInfo gwm_map_;
baidu::imap::sdk::MapEnginePtr map_engine_ptr_ = nullptr;
auto& coord_helper = gwm::Coordinates::getInstance();
std::filesystem::path root_path_ = std::filesystem::current_path();
void Clear()
{
    links_um_.clear();
    lanes_um_.clear();
    // gwm_map_.Clear();
};
bool SetProtoToASCIIFile(const google::protobuf::Message &message,
                         const std::string &file_name)
{
    std::ofstream ofs(file_name);
    ofs << message.DebugString() << std::endl;
    ofs.flush();
    ofs.close();
    return true;
}

void SetBoundaryInfo(
    bool is_left, bool in_junction,
    const std::vector<baidu::imap::HdLaneBoundaryPtr> &bd_marks,
    gwm::hdmap::LaneBoundary *boundary)
{
    auto *boundary_type = boundary->add_boundary_type();
    boundary_type->set_s(0.0);

    // if (in_junction) {
    //   boundary->set_virtual_(true);
    //   boundary_type->add_types(hozon::hdmap::LaneBoundaryType::DOTTED_WHITE);
    // } else {
    int flag_all_boundary = 1; // 是否全是边沿
    baidu::imap::HdLaneBoundaryPtr bd_mark_bound;
    for (auto bd_mark : bd_marks)
    {
        if (bd_mark->get_is_road_boundary() == 0)
        {
            flag_all_boundary = 0;
            bd_mark_bound = bd_mark;
            break;
        }
    }

    if (!bd_marks.empty() && flag_all_boundary == 0)
    {
        switch (bd_mark_bound->get_type())
        {
        case baidu::imap::DMT_MARKING_NONE:
            boundary->set_virtual_(true);
            boundary_type->add_types(gwm::hdmap::LaneBoundaryType::DOTTED_WHITE);
            break;
        case baidu::imap::DMT_MARKING_UNKNOWN:
            boundary->set_virtual_(true);
            boundary_type->add_types(gwm::hdmap::LaneBoundaryType::DOTTED_WHITE);
            break;

        case baidu::imap::DMT_MARKING_LONG_DASHED_LINE:
        case baidu::imap::DMT_MARKING_DOUBLE_DASHED_LINE:
            boundary->set_virtual_(false);
            boundary_type->add_types(gwm::hdmap::LaneBoundaryType::DOTTED_WHITE);
            break;

        case baidu::imap::DMT_MARKING_VIRTUAL_FIT:
        case baidu::imap::DMT_MARKING_VIRTUAL:
            boundary->set_virtual_(true);
            boundary_type->add_types(gwm::hdmap::LaneBoundaryType::DOTTED_WHITE);
            break;

        case baidu::imap::DMT_MARKING_DOUBLE_SOLID_LINE:
        case baidu::imap::DMT_MARKING_SINGLE_SOLID_LINE:
            boundary->set_virtual_(false);
            boundary_type->add_types(gwm::hdmap::LaneBoundaryType::SOLID_WHITE);
            break;

        case baidu::imap::DMT_MARKING_RIGHT_SOLID_LINE_LEFT_DASHED_LINE:
            boundary->set_virtual_(false);
            if (is_left)
            {
                boundary_type->add_types(gwm::hdmap::LaneBoundaryType::SOLID_WHITE);
            }
            else
            {
                boundary_type->add_types(
                    gwm::hdmap::LaneBoundaryType::DOTTED_WHITE);
            }
            break;

        case baidu::imap::DMT_MARKING_LEFT_SOLID_LINE_RIGHT_DASHED_LINE:
            boundary->set_virtual_(false);
            if (is_left)
            {
                boundary_type->add_types(
                    gwm::hdmap::LaneBoundaryType::DOTTED_WHITE);
            }
            else
            {
                boundary_type->add_types(gwm::hdmap::LaneBoundaryType::SOLID_WHITE);
            }
            break;
        }
    }
    else
    {
        std::cout << "bd lane has no boundary";
    }
    // }

    boundary->set_length(bd_marks.front()->get_length() / 100.0);
    boundary->set_id(bd_marks.front()->get_id());

    double utm_east = .0, utm_north = .0;
    int utm_zone = 0;
    auto &coord = gwm::Coordinates::getInstance();
    auto *line = boundary->mutable_boundary();
    for (const auto &geo : bd_marks.front()->get_geometry())
    {
        coord.gcj02ToUtm(geo.y*1e-7, geo.x*1e-7, utm_east, utm_north, utm_zone);
        auto *point = line->add_point();
        point->set_x(utm_east);
        point->set_y(utm_north);
    }
}

void SetLaneBoundary(const baidu::imap::LanePtr &bd_lane,
                     gwm::hdmap::Lane *gwm_lane)
{
    auto *left_boundary = gwm_lane->mutable_left_boundary();
    SetBoundaryInfo(true, bd_lane->has_junction_id(),
                    bd_lane->get_left_marking_list(), left_boundary);
    auto *right_boundary = gwm_lane->mutable_right_boundary();
    SetBoundaryInfo(false, bd_lane->has_junction_id(),
                    bd_lane->get_right_marking_list(), right_boundary);
}
void SetLaneType(const baidu::imap::LaneType &bd_lane_type,
                 gwm::hdmap::Lane *lane)
{
    switch (bd_lane_type)
    {
    case baidu::imap::LAT_NORMAL:
    case baidu::imap::LAT_ENTRY:
    case baidu::imap::LAT_EXIT:
    case baidu::imap::LAT_VARIABLE_LANE:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_STREET);
        break;
    case baidu::imap::LAT_RIGHT_TURN_LANE:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_RIGHT_TURN_ONLY);
        break;

    case baidu::imap::LAT_EMERGENCY:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_PARKING);
        break;

    case baidu::imap::LAT_ON_RAMP:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_ONRAMP);
        break;
    case baidu::imap::LAT_OFF_RAMP:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_OFFRAMP);
        break;
    case baidu::imap::LAT_PARKING:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_PARKING);
        break;
    case baidu::imap::LAT_CONNECT_RAMP:
    case baidu::imap::LAT_ACCELERATE:
    case baidu::imap::LAT_DECELERATE:
    case baidu::imap::LAT_EMERGENCY_PARKING_STRIP:
    case baidu::imap::LAT_CLIMBING:
    case baidu::imap::LAT_ESCAPE:
    case baidu::imap::LAT_DEDICATED_CUSTOMS:
    case baidu::imap::LAT_VIEWING_PLATFROM:
    case baidu::imap::LAT_PARALLEL_LANE:
    case baidu::imap::LAT_DIVERSION:
        lane->set_type(
            gwm::hdmap::Lane_LaneType::Lane_LaneType_HIGHWAY);
        break;

    case baidu::imap::LAT_LEFT_TURN_AREA:
        lane->set_type(
            gwm::hdmap::Lane_LaneType::Lane_LaneType_LEFTTURNWAITINGAREA);
        break;

    case baidu::imap::LAT_NON_MOTOR_LANE:
        lane->set_type(
            gwm::hdmap::Lane_LaneType::Lane_LaneType_BIKING);
        break;
    case baidu::imap::LAT_BUS_STOP:
        lane->set_type(
            gwm::hdmap::Lane_LaneType::Lane_LaneType_BUSLANE);
        break;
    case baidu::imap::LAT_NO_ENTRY_LANE:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_RESTRICTED);
        break;
    default:
        lane->set_type(gwm::hdmap::Lane_LaneType::Lane_LaneType_UNKNOWN);
        break;
    }
}

void SetTurnType(uint16_t bd_turn_type, gwm::hdmap::Lane *lane)
{
    switch (bd_turn_type)
    {
    case baidu::imap::TURNDIR_STRAIGHT:
        lane->set_turn(gwm::hdmap::Lane_LaneTurn::Lane_LaneTurn_STRAIGHT);
        break;

    case baidu::imap::TURNDIR_LEFT:
        lane->set_turn(gwm::hdmap::Lane_LaneTurn::Lane_LaneTurn_LEFT);
        break;

    case baidu::imap::TURNDIR_RIGHT:
        lane->set_turn(gwm::hdmap::Lane_LaneTurn::Lane_LaneTurn_RIGHT);
        break;

    case baidu::imap::TURNDIR_TURN:
        lane->set_turn(gwm::hdmap::Lane_LaneTurn::Lane_LaneTurn_INVALID);
        break;
    }
}
void SetCenterPoint(const baidu::imap::LanePtr &bd_lane,
                    gwm::hdmap::Lane *lane)
{
    auto &coord = gwm::Coordinates::getInstance();
    auto *center_line = lane->mutable_centerline();
    double utm_east = .0, utm_north = .0;
    int utm_zone = 0;
    for (const auto &geo : bd_lane->get_geometry())
    {
        coord.gcj02ToUtm(geo.y*1e-7, geo.x*1e-7, utm_east, utm_north, utm_zone);
        auto *point = center_line->add_point();
        point->set_x(utm_east);
        point->set_y(utm_north);
        // point->set_z(geo.z);
    }
}
// void SetMergeType(const baidu::imap::LanePtr &bd_lane,
//                   gwm::hdmap::Lane *lane)
// {
//     auto transition_type = bd_lane->get_lane_transition();
//     // switch (transition_type)
//     // {
//     // case baidu::imap::
//     //     /* code */
//     //     break;

//     // default:
//     //     break;
//     // }
// }

void LaneBuild(
    std::vector<uint32_t> left_ids, std::vector<uint32_t> right_ids,
    const baidu::imap::LanePtr &bd_lane,
    std::unordered_map<google::protobuf::int32, gwm::hdmap::Lane> *gwm_lanes_um)
{
    gwm::hdmap::Lane gwm_lane;
    gwm_lane.set_id(bd_lane->get_laneid());

    gwm_lane.set_length(bd_lane->get_length() / 100.0);

    const auto &left_marks = bd_lane->get_left_marking_list();
    SetLaneBoundary(bd_lane, &gwm_lane);

    if (bd_lane->get_max_speed_limit() > 0)
    {
        gwm_lane.set_speed_limit(bd_lane->get_max_speed_limit() / 3.6);
    }
    else
    {
        gwm_lane.set_speed_limit(40 / 3.6);
    }
    //   auto* lane_succ_id =  gwm_lane.mutable_successor_id();
    for (const auto &pred_id : bd_lane->get_precursor_lanes_ids())
    {
        gwm_lane.add_predecessor_id(pred_id);
    }

    for (const auto &succ_id : bd_lane->get_successor_lanes_ids())
    {
        gwm_lane.add_successor_id(succ_id);
    }

    for (auto left_id : left_ids)
    {
        gwm_lane.set_left_neighbor_forward_lane_id(left_id);
    }

    for (auto right_id : right_ids)
    {
        gwm_lane.set_right_neighbor_forward_lane_id(right_id);
    }

    SetLaneType(bd_lane->get_lane_type(), &gwm_lane);

    SetTurnType(bd_lane->get_current_direction(), &gwm_lane);

    SetCenterPoint(bd_lane, &gwm_lane);
    if (!bd_lane->get_left_marking_list().empty())
    {
        gwm_lane.set_left_boundary_id(bd_lane->get_left_marking_list().front()->get_id());
    }
    if (!bd_lane->get_right_marking_list().empty())
    {
        gwm_lane.set_right_boundary_id(bd_lane->get_right_marking_list().front()->get_id());
    }
    // SetMergeType(bd_lane, &gwm_lane);
    gwm_lane.set_min_width(bd_lane->get_min_width());
    int16_t max_curvature = SHRT_MIN;
    for (const auto &value : bd_lane->get_adas_curvature())
    {
        if (value > max_curvature)
        {
            max_curvature = value;
        }
    }
    gwm_lane.set_max_curvature(max_curvature);

    (*gwm_lanes_um)[gwm_lane.id()] = gwm_lane;
}

void LaneTransition(
    std::unordered_map<google::protobuf::int32, gwm::hdmap::Lane> *gwm_lanes_um)
{
    for (const auto &bd_link : links_um_)
    {

        // auto* section = road->add_section();
        // section->mutable_id()->set_id(std::to_string(bd_link.first));
        // SetSectionType(bd_link.second->get_link_type(), section);
        // auto* section_lane_id = section->mutable_lane_id();
        auto bd_lane_ids = bd_link.second->get_laneids();
        // std::vector<baidu::imap::HdLaneBoundaryPtr> bd_left_road_boundarys;
        // std::vector<baidu::imap::HdLaneBoundaryPtr> bd_right_road_boundarys;
        for (std::size_t i = 0; i < bd_lane_ids.size(); i++)
        {
            if (lanes_um_.count(bd_lane_ids[i]) != 0)
            {
                // int search_road_boudary =
                //     INT_MAX; // -1: right; 0: right+left; 1: left; 2: no serach
                std::vector<uint32_t> left_ids;
                std::vector<uint32_t> right_ids;
                // section_lane_id->Add()->set_id(std::to_string(bd_lane_ids[i]));
                std::size_t right_i = i - 1;
                // while (right_i >= 0) {
                //   right_ids.emplace_back(bd_lane_ids[right_i--]);
                // }
                if (right_i >= 0)
                {
                    right_ids.emplace_back(bd_lane_ids[right_i]);
                }
                std::size_t left_i = i + 1;
                // while (left_i < bd_lane_ids.size()) {
                //   left_ids.emplace_back(bd_lane_ids[left_i++]);
                // }
                if (left_i < bd_lane_ids.size())
                {
                    left_ids.emplace_back(bd_lane_ids[left_i]);
                }
                LaneBuild(left_ids, right_ids, lanes_um_.at(bd_lane_ids[i]),
                          gwm_lanes_um);
            }
        }
        // SetSectionBoundarys(bd_left_road_boundarys, bd_right_road_boundarys,
        //                     section);
    }
}

bool IsLaneHasBuild(const gwm::hdmap::Lane &check_lane)
{
    if (gwm_map_.lanes().empty())
    {
        return false;
    }
    for (const auto lane : gwm_map_.lanes())
    {
        if (lane.id() == check_lane.id())
        {
            return true;
        }
    }
    return false;
}
struct MockLoc
{
    uint32_t link_id = 0;
    uint32_t lane_id = 0;
    int32_t offset = 0;
    double x = 0.0;
    double y = 0.0;
};
bool GetIndexByLaneId(const google::protobuf::int32 &id, google::protobuf::int32 &index)
{
    google::protobuf::int32 count = 0;
    for (const auto &lane : gwm_map_.lanes())
    {
        if (id == lane.id())
        {
            index = count;
            return true;
        }
        count++;
    }
    return false;
}

void BuildRoutingLane(const gwm::hdmap::LocationInfo &location)
{
    gwm::hdmap::RoutingLaneInfo routing_lanes;
    baidu::imap::GpsCoord gps;
    // gps.lon = 115.4652559;
    // gps.lat = 38.8322061;
    // double radius = 5.0;
    gps.lon = location.point().x();
    gps.lat = location.point().y();
    double radius = 5.0;
    baidu::imap::LocalMap local_map;
    uint32_t res = 0;
    res = map_engine_ptr_->get_local_map(gps, radius, local_map);
    if (res != baidu::imap::IMAP_STATUS_OK)
    {
        std::cout << "get local map   failed status: " << res << std::endl;
        return;
    }
    google::protobuf::int32 index = 0;
    for (const auto &lane_ptr : local_map.hd_lanes)
    {
        auto lane_id = lane_ptr->get_laneid();
        auto ego_lane = routing_lanes.add_ego_lane_info();
        ego_lane->set_lane_id(lane_id);
        if (GetIndexByLaneId(lane_id, index))
        {
            ego_lane->set_lane_index(index);
        }
        else
        {
            ego_lane->set_lane_index(-1);
            std::cout << "cannot find lane in gwm_map_  lane_id:" << lane_id << std::endl;
        }
    }

    routing_lanes.set_time_stamp(location.time_stamp());

    for (const auto &link : local_map.hd_links)
    {
        auto lane_group = routing_lanes.add_lane_groups();

        auto lanes_id = link->get_laneids();
        for (const auto &lane_id : lanes_id)
        {
            auto passable_lane = lane_group->add_lane_frame_info();
            passable_lane->set_lane_id(lane_id);
            if (GetIndexByLaneId(lane_id, index))
            {
                passable_lane->set_lane_index(index);
            }
            else
            {
                passable_lane->set_lane_index(-1);
                std::cout << "cannot find lane in gwm_map_  lane_id:" << lane_id << std::endl;
            }
            passable_lane->set_lane_accessibility(gwm::hdmap::LanePassableInfo_LaneAccessibility::LanePassableInfo_LaneAccessibility_RECOMMEND);
        }
    }
    gwm_map_.add_routing_lanes()->CopyFrom(routing_lanes);
}

class DemoAllInOne
{
public:
    DemoAllInOne(const std::string &path, const std::string &id) : dbpath(path), vid(id)
    {
        // do nothing
    }

    ~DemoAllInOne()
    {
        // do nothing
    }
    int init()
    {
        if (map_engine_ptr_ == nullptr)
        {
            std::string log_path = root_path_/"log/bd_log/";
            map_engine_ptr_ = baidu::imap::sdk::get_map_engine();
            baidu::imap::InitParameter init_param("202502121000004183", "", baidu::imap::LogControl(100, 100 * 1024, log_path, baidu::imap::LogControl::INFO));
            if (map_engine_ptr_->initialize(dbpath, vid, init_param) !=
                baidu::imap::IMAP_STATUS_OK)
            {
                std::cout << "Engine initialize failed!";
                if (map_engine_ptr_)
                {
                    map_engine_ptr_->finalize_map();
                }
                map_engine_ptr_ = nullptr; // 确保指针重置为nullptr
                return -1;                 // 返回错误码表示初始化失败
            }
            else{
                std::cout << "Engine initialize ok!";
            }
        }
        return 0;
    }
    void get_id_from_file(const std::string &trail_path, std::vector<uint64_t> &v)
    {
        std::string file_path = trail_path;
        if (file_path.empty())
        {
            std::cout << "empty file path" << std::endl;
            return;
        }
        std::ifstream fin;
        fin.open(file_path.c_str());
        std::string str;
        while (!fin.eof())

        {
            std::getline(fin, str);
            if (str.empty())
            {
                continue;
            }
            uint64_t data = std::atoi(str.c_str());
            v.push_back(data);
            std::cout << "raw link id: " << data << std::endl;
        }
        fin.close();
    }

    void get_mock_loc_from_file(const std::string &trail_path, std::vector<MockLoc> &v)
    {
        std::string file_path = trail_path;
        if (file_path.empty())
        {
            std::cout << "empty file path" << std::endl;
            return;
        }
        std::ifstream fin;
        fin.open(file_path.c_str());
        std::string str;
        while (!fin.eof())
        {
            std::getline(fin, str);
            if (str.empty())
            {
                continue;
            }
            std::stringstream ss(str);
            std::string item;
            MockLoc loc;
            std::vector<std::string> strs;
            while (std::getline(ss, item, ','))
            {
                strs.push_back(item);
            }
            loc.x = atof(strs[0].c_str());
            loc.y = atof(strs[1].c_str());
            if (strs.size() >= 3)
            {
                loc.link_id = atoi(strs[2].c_str());
            }
            if (strs.size() >= 4)
            {
                loc.lane_id = atoi(strs[3].c_str());
            }
            if (strs.size() >= 5)
            {
                loc.offset = atoi(strs[4].c_str());
            }
            else
            {
                loc.offset = 0;
            }
            v.push_back(loc);
        }
        fin.close();
    }

    void dump_link_info(baidu::imap::LinkPtr link)
    {
        if (nullptr == link)
        {
            std::cout << "link is null , return" << std::endl;
            return;
        }
        std::cout << "link id : " << link->get_id() << std::endl;
        std::cout << "link class : " << link->get_link_class() << std::endl;
        std::cout << "travel direction : " << link->get_travel_direction() << std::endl;
        std::cout << "lane num : " << static_cast<int32_t>(link->get_lane_num()) << std::endl;
        std::cout << "link type : " << link->get_link_type() << std::endl;
        std::cout << "length : " << link->get_length() << std::endl;
        std::cout << "speed limit: " << link->get_speed_limit() << std::endl;

        std::cout << "geometry num : " << link->get_geometry().size() << " , include:";
        for (auto geo : link->get_geometry())
        {
            std::cout << "(" << geo.x << "," << geo.y << "," << geo.z << ")" << " ";
        }
        std::cout << std::endl;

        std::cout << "adas offset num : " << link->get_adas_offset().size() << " , include:";
        for (auto off : link->get_adas_offset())
        {
            std::cout << off << " ";
        }
        std::cout << std::endl;

        std::cout << "adas curvature num : " << link->get_adas_curvature().size() << " , include:";
        for (auto cur : link->get_adas_curvature())
        {
            std::cout << cur << " ";
        }
        std::cout << std::endl;

        std::cout << "adas slope num : " << link->get_adas_slope().size() << " , include:";
        for (auto slope : link->get_adas_slope())
        {
            std::cout << slope << " ";
        }
        std::cout << std::endl;

        std::cout << "adas cross slope num : " << link->get_adas_cross_slope().size() << " , include:";
        for (auto c_slope : link->get_adas_cross_slope())
        {
            std::cout << c_slope << " ";
        }
        std::cout << std::endl;

        std::cout << "adas heading num : " << link->get_adas_heading().size() << " , include:";
        for (auto h : link->get_adas_heading())
        {
            std::cout << h << " ";
        }
        std::cout << std::endl;

        std::cout << "successor link ids : ";
        for (auto t : link->get_successor_links_ids())
        {
            std::cout << t << " ";
        }
        std::cout << std::endl;

        std::cout << "precursor link ids : ";
        for (auto t : link->get_precursor_links_ids())
        {
            std::cout << t << " ";
        }
        std::cout << std::endl;

        std::cout << "laneids: ";
        for (auto id : link->get_laneids())
        {
            std::cout << id << " ";
        }
        std::cout << std::endl;

        auto udli = link->get_up_down_layer_info();
        std::cout << "up down layer info: (roadlevel: " << udli.roadLevel << " , is top: " << (int)udli.isTop
                  << " , startOffset: " << udli.startOffset << " , endOffset: " << udli.endOffset << ")" << std::endl;

        auto in_junc = link->get_in_junction_id();
        std::cout << "in junction include: ";
        for (auto junc : in_junc)
        {
            std::cout << junc << " ";
        }
        std::cout << std::endl;

        auto out_junc = link->get_out_junction_id();
        std::cout << "out junction include: ";
        for (auto junc : out_junc)
        {
            std::cout << junc << " ";
        }
        std::cout << std::endl;

        auto pass_junc = link->get_pass_junction_id();
        std::cout << "pass junction include: ";
        for (auto junc : pass_junc)
        {
            std::cout << junc << " ";
        }
        std::cout << std::endl;

        std::cout << "source type: " << (int)link->get_source_type() << std::endl;
    }

    void dump_lane_boundary(baidu::imap::HdLaneBoundaryPtr boundary)
    {
        if (nullptr == boundary)
        {
            std::cout << "boundary is null , return" << std::endl;
            return;
        }
        std::cout << "  boundary id : " << boundary->get_id() << std::endl;
        std::cout << "  boundary type : " << boundary->get_type() << std::endl;
        std::cout << "  get_color : " << (int)boundary->get_color() << std::endl;
        std::cout << "  get_material : " << (int)boundary->get_material() << std::endl;
        std::cout << "  get_width : " << (int)boundary->get_width() << std::endl;
        std::cout << "  get_length : " << (int)boundary->get_length() << std::endl;
        std::cout << "  get_order_number : " << boundary->get_order_number() << std::endl;
        std::cout << "  get_offset : " << boundary->get_offset() << std::endl;

        auto is_road_boundary = boundary->get_is_road_boundary();
        if (is_road_boundary)
        {
            std::cout << "  get_surface_type : " << (int)boundary->get_surface_type() << std::endl;
            std::cout << "  get_road_boundary_type : " << (int)boundary->get_road_boundary_type() << std::endl;
            std::cout << "  get_road_boundary_subtype : " << (int)boundary->get_road_boundary_subtype() << std::endl;
        }
        else
        {
            std::cout << "  get_divider_type : " << (int)boundary->get_divider_type() << std::endl;
            std::cout << "  get_divider_subtype : " << (int)boundary->get_divider_subtype() << std::endl;
        }
        std::cout << "  geometry num : " << boundary->get_geometry().size() << " , include:";
        for (auto geo : boundary->get_geometry())
        {
            std::cout << "(" << geo.x << "," << geo.y << "," << geo.z << ")" << " ";
        }
        std::cout << std::endl;
    }

    void dump_lane_info(baidu::imap::LanePtr lane)
    {
        if (nullptr == lane)
        {
            std::cout << "lane is null , return" << std::endl;
            return;
        }
        // std::cout << "lane id : " << lane->get_laneid() << std::endl;
        // std::cout << "link id : " << lane->get_linkid() << std::endl;
        // std::cout << "lane sequence : " << (int)lane->get_sequence() << std::endl;
        // std::cout << "lane type : " << (int)lane->get_lane_type() << std::endl;
        // std::cout << "restricted lane type : " << (int)lane->get_restricted_lane_type() << std::endl;
        // std::cout << "lane connection type : " << (int)lane->get_lane_connection_type() << std::endl;
        // std::cout << "travel direction : " << (int)lane->get_travel_direction() << std::endl;
        // std::cout << "min speed limit : " << lane->get_min_speed_limit() << std::endl;
        // std::cout << "max speed limit : " << lane->get_max_speed_limit() << std::endl;
        // std::cout << "is under construction: " << (int)lane->is_under_construction() << std::endl;
        // std::cout << "min width : " << lane->get_min_width() << std::endl;
        // std::cout << "max width : " << lane->get_max_width() << std::endl;
        // std::cout << "average width : " << lane->get_average_width() << std::endl;
        // std::cout << "length : " << lane->get_length() << std::endl;
        // std::cout << "transition : " << lane->get_lane_transition() << std::endl;
        // std::cout << "adas offset num : " << lane->get_adas_offset().size() << " , include:";
        for (auto off : lane->get_adas_offset())
        {
            std::cout << off << " ";
        }
        std::cout << std::endl;

        // std::cout << "adas curvature num : " << lane->get_adas_curvature().size() << " , include:";
        // for(auto cur : lane->get_adas_curvature()){
        //     std::cout << cur << " ";
        // }
        // std::cout << std::endl;

        // std::cout << "adas slope num : " << lane->get_adas_slope().size() << " , include:";
        // for(auto slope : lane->get_adas_slope()){
        //     std::cout << slope << " ";
        // }
        // std::cout << std::endl;

        // std::cout << "adas cross slope num : " << lane->get_adas_cross_slope().size() << " , include:";
        // for(auto c_slope : lane->get_adas_cross_slope()){
        //     std::cout << c_slope << " ";
        // }
        // std::cout << std::endl;

        // std::cout << "adas heading num : " << lane->get_adas_heading().size() << " , include:";
        // for(auto h : lane->get_adas_heading()){
        //     std::cout << h << " ";
        // }
        // std::cout << std::endl;

        std::cout << "left marking list num : " << lane->get_left_marking_list().size() << " , include:";
        for (auto l : lane->get_left_marking_list())
        {
            std::cout << l->get_id() << " ";
        }
        std::cout << std::endl;

        std::cout << "{" << std::endl;
        for (auto l : lane->get_left_marking_list())
        {
            dump_lane_boundary(l);
            std::cout << std::endl;
        }
        std::cout << "}" << std::endl;

        std::cout << "right marking list num : " << lane->get_right_marking_list().size() << " , include:";
        for (auto r : lane->get_right_marking_list())
        {
            std::cout << r->get_id() << " ";
        }
        std::cout << std::endl;

        std::cout << "{" << std::endl;
        for (auto l : lane->get_right_marking_list())
        {
            dump_lane_boundary(l);
            std::cout << std::endl;
        }
        std::cout << "}" << std::endl;

        // std::cout << "geometry num : " << lane->get_geometry().size() << " , include:";
        for(auto geo : lane->get_geometry()){
            std::cout << "(" << geo.x << "," << geo.y << "," << geo.z << ")" << " ";
        }
        // std::cout << std::endl;

        // std::cout << "successor lane ids : ";
        // for (auto t : lane->get_successor_lanes_ids()){
        //     std::cout << t << " ";
        // }
        // std::cout << std::endl;

        // std::cout << "precursor lane ids : ";
        // for (auto t : lane->get_precursor_lanes_ids()){
        //     std::cout << t << " ";
        // }
        // std::cout << std::endl;

        // std::cout << "object ids : ";
        // for (auto t : lane->get_objids()){
        //     std::cout << t << " ";
        // }
        // std::cout << std::endl;

        auto has_pos_res = lane->get_has_pos_restrictive();
        if (has_pos_res)
        {
            std::cout << "pos restrictive: " << std::endl;
            auto pos_res = lane->get_pos_res();
            std::cout << "type: " << (int)pos_res.m_type << std::endl;
            std::cout << "restriction: ";
            for (auto res : pos_res.m_restriction)
            {
                std::cout << "(type: " << (int)res.m_type << " , vehicle type: [";
                for (auto v : res.m_vehicle)
                {
                    std::cout << (int)v << " , ";
                }
                std::cout << "] , validaty: " << res.m_validaty << std::endl;
            }
            if (pos_res.m_restriction.empty())
                std::cout << std::endl;
        }

        auto has_neg_res = lane->get_has_neg_restrictive();
        if (has_neg_res)
        {
            std::cout << "neg restrictive: " << std::endl;
            auto neg_res = lane->get_neg_res();
            std::cout << "type: " << (int)neg_res.m_type << std::endl;
            std::cout << "restriction: ";
            for (auto res : neg_res.m_restriction)
            {
                std::cout << "(type: " << (int)res.m_type << " , vehicle type: [";
                for (auto v : res.m_vehicle)
                {
                    std::cout << (int)v << " , ";
                }
                std::cout << "] , validaty: " << res.m_validaty << std::endl;
            }
            if (neg_res.m_restriction.empty())
                std::cout << std::endl;
        }

        std::cout << "plan direction : " << (int)lane->get_plan_direction() << std::endl;
        std::cout << "current direction : " << (int)lane->get_current_direction() << std::endl;

        auto has_junction = lane->has_junction_id();
        if (has_junction)
        {
            std::cout << "junction id : " << lane->get_junction_id() << std::endl;
        }

        auto left_opp_lane_info = lane->get_left_opposite_lane_info();
        std::cout << "left_opposite_lane_info include: " << std::endl;
        for (auto info : left_opp_lane_info)
        {
            std::cout << "lane id: " << info.lane_id << " , offset: " << info.offset << std::endl;
        }

        auto right_opp_lane_info = lane->get_right_opposite_lane_info();
        std::cout << "right_opposite_lane_info include: " << std::endl;
        for (auto info : right_opp_lane_info)
        {
            std::cout << "lane id: " << info.lane_id << " , offset: " << info.offset << std::endl;
        }

        std::cout << "converge type: " << (int)lane->get_converge_type() << std::endl;
        std::cout << "road boundary width: " << lane->get_road_boundary_width() << std::endl;
        std::cout << "min left road boundary distance: " << lane->get_min_left_road_boundary_distance() << std::endl;
        std::cout << "max left road boundary distance: " << lane->get_max_left_road_boundary_distance() << std::endl;
        std::cout << "min right road boundary distance: " << lane->get_min_right_road_boundary_distance() << std::endl;
        std::cout << "max right road boundary distance: " << lane->get_max_right_road_boundary_distance() << std::endl;

        auto successor_special_lanes = lane->get_successor_special_lanes();
        std::cout << "successor_special_lanes include: " << std::endl;
        for (auto lane : successor_special_lanes)
        {
            std::cout << "lane id: " << lane.laneid << " , type: " << lane.type << std::endl;
        }

        auto precursor_special_lanes = lane->get_precursor_special_lanes();
        std::cout << "precursor_special_lanes include: " << std::endl;
        for (auto lane : precursor_special_lanes)
        {
            std::cout << "lane id: " << lane.laneid << " , type: " << lane.type << std::endl;
        }
    }

    void dump_obj_info(baidu::imap::ObjectPtr obj)
    {
        if (nullptr == obj)
        {
            std::cout << "obj is null , return" << std::endl;
            return;
        }
        std::cout << "objid : " << obj->get_id() << std::endl;
        std::cout << "obj type : " << (int)obj->get_type() << std::endl;
        std::cout << "obj subtype : " << (int)obj->get_subtype() << std::endl;

        std::cout << "center : " << obj->get_center().x * 1.0 / baidu::imap::SCALE_XY
                  << ", " << obj->get_center().y * 1.0 / baidu::imap::SCALE_XY
                  << ", " << obj->get_center().z * 1.0 / baidu::imap::SCALE_H
                  << std::endl;
        std::cout << "obj height : " << obj->get_height() << std::endl;
        std::cout << "obj width : " << obj->get_width() << std::endl;
        std::cout << "obj length : " << obj->get_length() << std::endl;
        std::cout << "obj heading : " << obj->get_heading() << std::endl;
        auto has_geometry = obj->has_geometry();
        if (has_geometry)
        {
            std::cout << "geometry num : " << obj->get_geometry().size() << " , include:";
            for (auto geo : obj->get_geometry())
            {
                std::cout << "(" << std::setprecision(10) << geo.x * 1.0 / baidu::imap::SCALE_XY
                          << ", " << std::setprecision(10) << geo.y * 1.0 / baidu::imap::SCALE_XY
                          << ", " << std::setprecision(10) << geo.z * 1.0 / baidu::imap::SCALE_H
                          << ")" << " ";
            }
            std::cout << std::endl;
        }

        std::cout << "bounding box geometry num : " << obj->get_bounding_box_geometry().size() << " , include:";
        for (auto geo : obj->get_bounding_box_geometry())
        {
            std::cout << "(" << std::setprecision(10) << geo.x * 1.0 / baidu::imap::SCALE_XY
                      << ", " << std::setprecision(10) << geo.y * 1.0 / baidu::imap::SCALE_XY
                      << ", " << std::setprecision(10) << geo.z * 1.0 / baidu::imap::SCALE_H
                      << ")" << " ";
        }
        std::cout << std::endl;

        std::cout << "obj laneids : ";
        for (auto t : obj->get_laneids())
        {
            std::cout << t << " ";
        }
        std::cout << std::endl;

        std::cout << "speed limit sign: [";
        for (auto s : obj->get_speed_limit_sign())
        {
            std::cout << "(type: " << (int)s.type << " , value: " << (int)s.value << "),";
        }
        std::cout << "]" << std::endl;

        std::cout << "obj shape: " << (int)obj->get_object_shape() << std::endl;
        std::cout << "roadmark type: " << (int)obj->get_roadmark_type() << std::endl;

        std::cout << "obj ref objids: ";
        for (auto t : obj->get_ref_object_ids())
        {
            std::cout << t << " ";
        }
        std::cout << std::endl;
    }

    void dump_junction_info(baidu::imap::JunctionPtr junction)
    {
        if (nullptr == junction)
        {
            std::cout << "junction is null , return" << std::endl;
            return;
        }
        std::cout << "junction id : " << junction->get_id() << std::endl;

        std::cout << "junction type : " << (int)junction->get_type() << std::endl;

        std::cout << "geometry num : " << junction->get_geometry().size() << std::endl;
        for (auto geo : junction->get_geometry())
        {
            std::cout << "(" << geo.x << "," << geo.y << "," << geo.z << ")" << " ";
        }
        std::cout << std::endl;

        auto connections = junction->get_connections();
        std::cout << "junction connection size: " << connections.size() << std::endl;
        for (auto connection : connections)
        {
            std::cout << "from link: " << connection.from_link_id << " , to link: " << connection.to_link_id << std::endl;
            for (auto ll : connection.lanelinks)
            {
                std::cout << "from lane: " << ll.from_lane_id << " , to lane: " << ll.to_lane_id << std::endl;
            }
        }

        std::cout << "lane id: ";
        for (auto &lane : junction->get_laneids())
        {
            std::cout << lane << " ";
        }
        std::cout << std::endl;

        std::cout << "obstacles id: ";
        for (auto &obs : junction->get_obstacles())
        {
            std::cout << obs << " ";
        }
        std::cout << std::endl;
    }

    void cache_localmap_info(baidu::imap::LocalMap &local_map)
    {
        for (const auto &link : local_map.hd_links)
        {
            links_um_[link->get_id()] = link;
        }
        for (const auto &lane : local_map.hd_lanes)
        {
            // for(auto geo : lane->get_geometry()){
            //     std::cout << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>(" << geo.x << "," << geo.y << "," << geo.z << ")" << " ";
            // }
            lanes_um_[lane->get_laneid()] = lane;
        }
        // std::cout << std::endl;
        // std::cout << "object size: " << local_map.hd_objects.size() << " , include:";
        // for (auto obj : local_map.hd_objects)
        // {
        //     std::cout << obj->get_id() << ",";
        // }
        // std::cout << std::endl;
        // std::cout << "junction size: " << local_map.hd_junctions.size() << " , include:";
        // for (auto jc : local_map.hd_junctions)
        // {
        //     std::cout << jc->get_id() << ",";
        // }
        // std::cout << std::endl;
    }

    void dump_mpp(const std::vector<uint32_t> &mpp)
    {
        std::cout << "mpp size: " << mpp.size() << std::endl;
        std::cout << "mpp include : " << std::endl;
        for (auto l : mpp)
        {
            std::cout << l << ", ";
        }
        std::cout << std::endl;
    }

    void dump_mpp_info(const baidu::imap::MppInfo &mpp_info)
    {
        std::cout << "mpp_info.mpp size: " << mpp_info.mpp.size() << std::endl;
        std::cout << "mpp_info is : " << std::endl;
        std::cout << "{ " << std::endl;
        for (auto mpp : mpp_info.mpp)
        {
            std::cout << "    { ";
            for (auto mpp_seg : mpp)
            {
                std::cout << "{ ";
                for (auto l : mpp_seg)
                {
                    std::cout << l << ",";
                }
                std::cout << "}";
                std::cout << ",";
            }
            std::cout << "}" << std::endl;
        }
        std::cout << "}" << std::endl;
        std::cout << "mpp_remain_len: " << mpp_info.mpp_remain_len << std::endl;
        auto mpp_index = mpp_info.mpp_index_of_car;
        std::cout << "mpp_index: " << mpp_index.x << "," << mpp_index.y << "," << mpp_index.z << std::endl;
        std::cout << "is_reach_hd_end: " << (int)mpp_info.is_reach_hd_end << std::endl;
        std::cout << "is_reach_navi_end: " << (int)mpp_info.is_reach_navi_end << std::endl;
        std::cout << "hdroute_match_status: " << mpp_info.hdroute_match_status << std::endl;
        std::cout << "hdroute_broken_point: ("
                  << std::setprecision(10) << mpp_info.hdroute_broken_point.lon << " , "
                  << std::setprecision(10) << mpp_info.hdroute_broken_point.lat << " , "
                  << mpp_info.hdroute_broken_point.height << ")"
                  << std::endl;
    }

    void dump_noa_info(const baidu::imap::SwitchInfo &switch_info)
    {
        std::cout << "swicth_info.direction: " << (int)switch_info.direction << std::endl;
        std::cout << "switch_info.reason: " << (int)switch_info.reason << std::endl;
        std::cout << "switch_info.distance: " << switch_info.distance << std::endl;
        std::cout << "switch_info.end_distance: " << switch_info.end_distance << std::endl;
        std::cout << "switch_info.point: ("
                  << std::setprecision(10) << (double)switch_info.point.x / 10e6 << " , "
                  << std::setprecision(10) << (double)switch_info.point.y / 10e6 << " , "
                  << std::setprecision(10) << (double)switch_info.point.z / 10e6 << ")"
                  << std::endl;
        std::cout << "swicth_info.front_direction: " << (int)switch_info.front_direction << std::endl;
        std::cout << "switch_info.front_distance: " << switch_info.front_distance << std::endl;

        std::cout << "switch_info.link_id: " << switch_info.link_id << std::endl;
        std::cout << "switch_info.lane_id: " << switch_info.lane_id << std::endl;
        std::cout << "switch_info.noa_states: " << std::endl;
        for (auto noa_state : switch_info.noa_states)
        {
            std::cout << "lane_id: " << noa_state.first << ", "
                      << "weight: " << (int)noa_state.second.weight << ", "
                      << "switch_event: " << (int)noa_state.second.switch_event << ", "
                      << "event_distance: " << noa_state.second.event_distance << ", "
                      << "sequence: " << (int)noa_state.second.sequence << ", "
                      << "origin_link: " << noa_state.second.origin_link << ", "
                      << "origin_lane: " << noa_state.second.origin_lane << std::endl;
        }
    }

    void dump_guide_lanes(const std::vector<uint32_t> &guide_lanes)
    {
        std::cout << " guide_lanes size: " << guide_lanes.size() << std::endl;
        std::cout << " guide_lanes include : " << std::endl;
        for (auto l : guide_lanes)
        {
            std::cout << l << ", ";
        }
        std::cout << std::endl;
    }

    void noa_demo()
    {
        baidu::imap::GpsCoord gps;
        gps.lon = 115.4652559;
        gps.lat = 38.8322061;
        baidu::imap::LanePtr p_lane = nullptr;
        baidu::imap::LinkPtr p_link = nullptr;
        uint32_t link_id = 26707577;
        uint32_t lane_id = 261890659;
        baidu::imap::NavInfo navi_in;
        baidu::imap::NavStatus navi_out;
        navi_in.navigation_status = baidu::imap::NS_NAVIGATION;
        navi_in.matching_table_status = baidu::imap::MS_MATCHING_TABLE_OK;
        navi_in.remain_distance = 20000;
        // baidu::imap::NavSDLinkList nav_sdlinks;
        std::vector<uint64_t> sdlinkid{1568481820, 1575437655, 1575442954, 1664137439, 1664140423, 1590243600, 1574915952, 1574868496, 1625336162, 1628176151, 1629297037, 1629297036, 1646595116, 1646595154, 1631089842, 1630606885, 1701517835, 1701636440, 1628176192, 1628176193, 1628176190, 1630005304, 1630005209, 1635929269, 1701634566, 1701634619, 1630005303, 1630005283, 1595865788, 1595864746, 1594482625, 1527811779, 1594482634, 1594481084, 1564648169, 1594482861, 1594479602, 1527640562, 1594482651, 1701617658, 1701635119, 1628479841, 1628479840, 1594482681, 1623161179, 1676351144, 1631480963, 1631480960, 1594482765, 1585611200, 1527612909, 1585582124, 1594482718, 1594480765, 1564649543, 1701529713, 1701598862, 1594479833, 1594479833, 1527480173, 1578556878, 1613485244, 1594482590, 1594482691, 1594481137, 1597655343, 1597655257, 1594482912, 1613555029, 1613554854, 1527741239, 1701634903, 1701635050, 1651124533, 1651124530, 1648951013, 1701519731, 1701634900, 1594565341, 1594478836, 1595127290, 1595129331, 1593510607, 1593509139, 1576444697, 1564649957, 1527609440, 1527609454, 1697192009, 1697192007, 1613555038, 1613810667, 1613810335, 1613810756, 1578106650, 1527704637, 1667436229, 1667802138, 1667802356, 1578107489, 1613304958, 1701617670, 1701536130, 1564649450, 1564647545, 1564649448, 1607611586, 1607611566, 1621955029, 1621954943, 1527842184, 1607611551, 1607611487, 1658753121, 1676104858, 1676103553, 1658753067, 1527788945, 1571364405, 1658753108, 1690563433, 1639451752, 1639451751, 1548230123, 1542731013, 1594482731, 1646515578, 1646515577, 1701617862, 1701634649, 1646515575, 1613453178, 1613452831, 1595865773, 1595865714, 1542270730, 1542666712, 1542732126, 1527704569, 1527746360, 1609427192, 1564647519, 1542732708, 1617659538, 1617670728, 1527609666, 1701634663, 1701634481, 1578105631, 1629961196, 1630366804, 1629961907, 1595199105, 1691183953, 1691183954, 1574869205, 1574905022, 1630365872, 1630366043, 1630364959, 1527704598, 1676071230, 1527490411, 1578107272, 1578107273, 1564647267, 1594782650, 1594779656, 1542755343, 1564691469, 1691547505, 1691547505, 1691547504, 1564691218, 1594428706, 1594426754, 1701525494, 1701635225, 1613897128, 1670407621, 1670407622, 1670407624, 1670407502, 1670407626, 1613895515, 1613897136, 1613305179, 1670433310, 1670434119, 1670434122, 1670433796, 1670433790, 1664361690, 1664361656, 1613896691, 1675506534, 1675504330, 1613897113, 1613893263, 1614217264, 1614219061, 1614217869, 1614219059, 1527691858, 1617667800, 1617653603, 1667602491, 1667602276, 1593721723, 1593725587, 1593725587, 1613896548, 1613895526, 1650780729, 1613893318, 1651167548, 1651167547, 1527479900, 1622334847, 1622334977, 1613894250, 1613893225, 1613893226, 1613896618, 1564649653, 1613896595, 1613893221, 1527691724, 1549711312, 1622484698, 1622484826, 1613895504, 1542733142, 1651057999, 1651057916, 1651058002, 1701634934, 1701634769, 1636947965, 1594482684, 1613895501, 1664212779, 1664212536, 1594478465, 1594476753, 1585738307, 1609432433, 1595865726, 1660325186, 1660325185, 1613896597, 1593722531, 1593720231, 1527811696, 1527742086, 1527520141, 1659035141, 1659034953, 1578106543, 1578106544, 1542731922, 1667472130, 1667472365, 1659035140, 1698149213, 1698147603, 1609432529, 1701634782, 1701635022, 1594565037, 1617660645, 1617666395, 1660158693, 1660158695, 1660158694, 1527855090, 1578106626, 1593725572, 1593725582, 1527583534, 1585451811, 1585453646, 1542479308, 1614020739, 1614021118, 1660158701, 1660158697, 1660158702, 1697491108, 1697491109, 1585453652, 1574872986, 1574909999, 1585450100, 1677224494, 1701630522, 1701629660, 1677224880, 1617664696, 1701629665, 1701630627, 1646514048, 1704630231, 1704630231, 1704630230, 1627968051, 1627968045, 1613605357, 1613605430, 1647070110, 1647070109, 1680580268, 1680580269, 1617666716, 1613605423, 1613604871, 1542364174, 1541653906, 1527746274, 1651092751, 1651092752, 1594565153, 1660042674, 1679969434, 1679969435, 1705120798, 1705120799, 1527800261, 1564649881, 1699250557, 1699250925, 1704533074, 1704533073, 1702445528, 1689460884, 1701693437, 1701710826, 1578556887, 1695029696, 1702445538, 1702437216, 1575443581, 1706505282, 1706502379, 1705702036, 1651093167, 1699047234, 1699047279, 1699047226, 1594898642, 1527490481, 1687204417, 1688602991, 1688602912, 1527546728, 1676696118, 1702525293, 1702525297, 1703202658, 1703202657, 1692605484, 1692605485, 1669953983, 1669953982, 1677932302, 1678055219, 1613555671, 1678885164, 1678885553, 1695493728, 1695493727, 1689597781, 1689597435, 1585448512, 1585455245};
        navi_in.linkid = sdlinkid;
        navi_in.map_version = 0;
        std::vector<uint32_t> mpp;
        std::vector<uint32_t> guide_lanes;
        baidu::imap::SwitchInfo si;
        baidu::imap::MppInfo mpp_info;

        baidu::imap::sdk::MapEnginePtr p_me = baidu::imap::sdk::get_map_engine();
        baidu::imap::InitParameter init_param("202502121000004183", "", baidu::imap::LogControl(100, 100 * 1024, "", baidu::imap::LogControl::INFO));
        p_me->initialize(dbpath, vid, init_param);
        p_me->set_current_position(gps);
        usleep(1e6);
        uint32_t status = 0;
        while ((status = p_me->get_engine_status()) != baidu::imap::IMAP_STATUS_OK)
        {
            std::cout << "waiting buff loading " << std::endl;
            std::cout << "engine status: " << status << std::endl;
            usleep(1e6);
        }
        p_lane = p_me->get_lane(lane_id);
        if (p_lane == nullptr)
        {
            std::cout << "can not find lane " << lane_id << " from buffer." << std::endl;
        }
        p_link = p_me->get_link(link_id);
        if (p_link == nullptr)
        {
            std::cout << "can not find link " << link_id << " from buffer." << std::endl;
        }

        uint32_t time_out_counter = 0;
        while (true)
        {
            if (100 < time_out_counter++)
            {
                time_out_counter = 0;
                break;
            }
            p_me->get_navi_status(navi_out);
            std::cout << "wait noa status : navigation_status " << (uint32_t)navi_out.navigation_status << std::endl;
            std::cout << "wait noa status : matching_table_status " << (uint32_t)navi_out.matching_table_status << std::endl;
            baidu::imap::HadmapPilotResult pilot;
            pilot.gps = gps;
            pilot.link_id = link_id;
            pilot.lane_id = lane_id;
            p_me->set_pilot_result(pilot);

            p_me->set_navi_info(navi_in);

            if (nullptr != p_link)
            {
                std::cout << "current link is city area" << std::endl;
                std::cout << "get_mpp_info ret " << (uint32_t)p_me->get_mpp_info(mpp_info) << std::endl;
                dump_mpp_info(mpp_info);
                if (!mpp_info.mpp.empty())
                {
                    break;
                }
            }

            usleep(1e6);
        }

        sleep(1);
        p_me->finalize_map();
    }

    void engine_demo(const gwm::hdmap::LocationInfo &location)
    {
        baidu::imap::GpsCoord gps;
        gps.lon = 115.477543;
        gps.lat = 38.858243;
        // double radius = 3000;
        //std::cout << ">>>>lon: " << location.point().x()<<" >>>lat   "<<location.point().y()<<" location.radias(): "<<location.radias()<<std::endl;
        // gps.lon = location.point().x();
        // gps.lat = location.point().y();
        double radius = 500;
        baidu::imap::LocalMap local_map;
        std::string map_version;
        std::string engine_version;
        baidu::imap::LanePtr p_lane = nullptr;
        baidu::imap::LinkPtr p_link = nullptr;
        baidu::imap::ObjectPtr p_obj = nullptr;
        baidu::imap::JunctionPtr p_jc = nullptr;
        uint32_t link_id = 26707577;
        uint32_t lane_id = 261890659;
        map_engine_ptr_->set_current_position(gps);
        usleep(1e6);
        uint32_t status = 0;
        while ((status = map_engine_ptr_->get_engine_status()) != baidu::imap::IMAP_STATUS_OK)
        {
            std::cout << "waiting buff loading " << std::endl;
            std::cout << "engine status: " << status << std::endl;
            usleep(1e6);
        }

        if (map_engine_ptr_->get_map_version(map_version) == baidu::imap::IMAP_STATUS_OK)
        {
            std::cout << "map version " << map_version << std::endl;
        }
        if (map_engine_ptr_->get_engine_version(engine_version) == baidu::imap::IMAP_STATUS_OK)
        {
            std::cout << "engine version " << engine_version << std::endl;
        }

        // p_link = map_engine_ptr_->get_link(link_id);
        // if (p_link != nullptr)
        // {
        //     // dump_link_info(p_link);
        // }

        // p_lane = map_engine_ptr_->get_lane(lane_id);
        // if (p_lane != nullptr)
        // {
        //     // dump_lane_info(p_lane);
        //     if (p_lane->has_junction_id())
        //     {
        //         std::cout << lane_id << "  junction is " << p_lane->get_junction_id();
        //         auto junc = map_engine_ptr_->get_junction(p_lane->get_junction_id());
        //         if (junc)
        //         {
        //             std::cout << " junction not null " << junc->get_id() << " type " << junc->get_type() << std::endl;
        //         }
        //         else
        //         {
        //             std::cout << " junction is null " << std::endl;
        //         }

        //         for (auto iter : junc->get_laneids())
        //         {
        //             std::cout << " laneid is  " << iter << std::endl;
        //         }

        //         junc = map_engine_ptr_->get_junction(4601488);
        //         if (junc)
        //         {
        //             std::cout << " junction not null 4601488" << std::endl;
        //         }
        //         else
        //         {
        //             std::cout << " junction is null 4601488" << std::endl;
        //         }
        //     }
        // }

        uint32_t res = 0;
        res = map_engine_ptr_->get_local_map(gps, radius, local_map);
        if (res != baidu::imap::IMAP_STATUS_OK)
        {
            std::cout << "get local map   failed status: " << res << std::endl;
            return;
        }
        Clear();
        cache_localmap_info(local_map);
        std::unordered_map<google::protobuf::int32, gwm::hdmap::Lane> gwm_lanes_um;
        LaneTransition(&gwm_lanes_um);

        for (const auto &lane : gwm_lanes_um)
        {
            if (!IsLaneHasBuild(lane.second))
            {
                gwm_map_.add_lanes()->CopyFrom(lane.second);
            }
        }
        BuildRoutingLane(location);
        std::cout << "gwm_map_  lane size : " << gwm_map_.lanes_size()<< std::endl;
    }

private:
    DemoAllInOne() {}
    std::string dbpath;
    std::string vid;
};

void DumpPoses(const gwm::hdmap::TrackList&track_list,
               const std::string &output_str_path, int count) {
  // const auto &rasmap = global_rasmap_plus.ras_map();
  std::cout << "dump poses size: " << track_list.locations_size();
  // root is the root level, including "type": "FeatureCollection",
  // "feature": [{}, {}, {}, ....]
  cJSON *root = cJSON_CreateObject();
  cJSON_AddItemToObject(root, "type", cJSON_CreateString("FeatureCollection"));
  // create feature arrays: features, which includes "type": "Feature", "properties": {},
  // "geometry": {[[], [], [] ......]}
  cJSON *featuresArry = cJSON_CreateArray();
  cJSON_AddItemToObject(root, "features", featuresArry);

  for(const auto&location:track_list.locations()){
    const auto &pose = location.point();
    // create single feature, and add to feature array
    cJSON *feature = cJSON_CreateObject();
    cJSON_AddItemToArray(featuresArry, feature);
    // "type": "Feature"
    cJSON_AddItemToObject(feature, "type", cJSON_CreateString("Feature"));
    // "geometry": {[[], [], []......]}
    cJSON *geometry = cJSON_CreateObject();
    cJSON_AddItemToObject(feature, "geometry", geometry);

    // point
    cJSON_AddItemToObject(geometry, "type", cJSON_CreateString("Point"));
    cJSON *coordArry = cJSON_CreateArray();
    cJSON_AddItemToObject(geometry, "coordinates", coordArry);
    cJSON_AddItemToArray(coordArry, cJSON_CreateNumber(pose.x()));
    cJSON_AddItemToArray(coordArry, cJSON_CreateNumber(pose.y()));
  }


  // write to geojson file
  FILE *file = NULL;
  // std::string home_path = getenv("HOME");
  // file = fopen((output_path + "/rasmap_plus.geojson").c_str(), "w");
  std::string file_name = output_str_path + "/poses_" + std::to_string(count) + ".geojson";
  file = fopen(file_name.c_str(), "w");
  // file = fopen(file_name.c_str(), "w");
  if (file == NULL) {
    std::cout << "Open geojson file failed !";
    cJSON_Delete(root);
    cJSON_Delete(featuresArry);
    return;
  }
  char *cjValue = cJSON_Print(root);
  int ret = fputs(cjValue, file);
  if (ret == EOF) {
    std::cout << "write geojson file failed!";
  }

  fclose(file);
  free(cjValue);
}

int main(int argc, char *argv[])
{

    std::string dbpath = root_path_/"data/bd_data";
    std::cout << "dbpath :"<<dbpath;
    std::string vid = "TEST00120250312";
    auto global_gt_file_path = root_path_/"data/bd_data/test/result/global_GT.csv";
    auto track_file_path = root_path_/"data/bd_data/test/raw/rtk_gps.txt";
    gwm::hdmap::TrackList track_list;
    //为在QGIS中可视化用
    // gwm::hdmap::TrackList track_list_wgs84;
    // gwm::common::TrackInfoReader track_reader;
    // track_list = std::move(
    //     track_reader.ConvertTrackList(global_gt_file_path, track_file_path,false));
    // track_list_wgs84 = std::move(
    //     track_reader.ConvertTrackList(global_gt_file_path, track_file_path,true));

    // auto gnss_list = track_reader.ConvertGNSSInfo(track_file_path);
    // WriteMessageToFile(track_list, root_path_/"data/bd_data/test/data/TrackList.bin");
    // SetProtoToASCIIFile(track_list, root_path_/"data/bd_data/test/data/track_list.txt");
    // WriteMessageToFile(gnss_list, root_path_/"data/bd_data/test/data/rtk_gps.bin");
    // SetProtoToASCIIFile(gnss_list, root_path_/"data/bd_data/test/data/gnss_list.txt");
    DemoAllInOne demo(dbpath, vid);
    demo.init();
    // DumpPoses(track_list_wgs84,root_path_/"data/bd_data/test/data",1);
    
    // gwm::common::Point3D last_point;
    // for (const auto &location : track_list.locations())
    // {
        
    //     if((last_point.x() == location.point().x())&&(last_point.y() == location.point().y()))
    //     {
    //         continue;
    //     }
    //     demo.engine_demo(location);
    //     last_point.set_x(location.point().x());
    //     last_point.set_y(location.point().y());

    //     sleep(1);
    // }
    while(true){
        gwm::hdmap::LocationInfo location;
        std::cout << "+++++++++++1"<< std::endl;
        demo.engine_demo(location);
        std::cout << "+++++++++++2"<< std::endl;
        if(gwm_map_.lanes_size()>599){
            SetProtoToASCIIFile(gwm_map_, root_path_/"data/bd_data/test/gwm_map.txt");
            WriteMessageToFile(gwm_map_, root_path_/"data/bd_data/test/gwm_map.bin");
        }
        
        std::cout << "+++++++++++3"<< std::endl;
        sleep(1);
    }
    
    SetProtoToASCIIFile(gwm_map_, root_path_/"data/bd_data/test/gwm_map.txt");
    WriteMessageToFile(gwm_map_, root_path_/"data/bd_data/test/gwm_map.bin");
    // gwm::common::HDMapToGeoJsonHandler to_json_handler;
    // auto hd_map_geo_json = to_json_handler.RoutingMapInfoToGeoJSON(gwm_map_);
    // auto track_geo_json = to_json_handler.TrackListToGeoJSON(track_list);

    // std::string hd_map_geo_json_path = root_path_/"data/bd_data/test/data/hd_map.geojson";
    // to_json_handler.OutputToFile(hd_map_geo_json, hd_map_geo_json_path);
    Clear();
    map_engine_ptr_->finalize_map();
    return 0;
}
