/*
 * File: hd_map_to_geojson.cc
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-20
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include <fstream>
#include <iomanip>
#include <iostream>

#include "hd_map_to_geojson.hpp"
#include "util/Coordinates.h"
#include "util/log.hpp"

namespace gwm {
namespace common {
// 将 Point3D 转换为 GeoJSON 坐标
json HDMapToGeoJsonHandler::Point3DToGeoJSON(const gwm::common::Point3D& point,
                                             bool is_utm) {
  if (!is_utm) {
    return {point.x(), point.y()};
  }
  auto& coord_helper = gwm::Coordinates::getInstance();
  auto [lon84, lat84] = utm_converter_->Convert(point.x(), point.y());
  auto [lon, lat] = coord_helper.Wgs84ToGcj02(lon84, lat84);

  DebugCode(LOG_INFO << std::fixed << std::setprecision(6) << "lon,lat:" << lon
                     << "," << lat << " 84 lon,lat:" << lon84 << "," << lat84
                     << std::endl);
  return {lon, lat};
}

// 将 Polyline 转换为 GeoJSON LineString
json HDMapToGeoJsonHandler::PolylineToGeoJSON(
    const gwm::common::Polyline& polyline) {
  json geojson_linestring = json::array();
  for (const auto& point : polyline.point()) {
    geojson_linestring.push_back(Point3DToGeoJSON(point));
  }
  return geojson_linestring;
}

// 将 LaneBoundary 转换为 GeoJSON Feature
json HDMapToGeoJsonHandler::LaneBoundaryToGeoJSON(
    const gwm::hdmap::LaneBoundary& boundary) {
  json geojson_feature = {
      {"type", "Feature"},
      {"geometry",
       {{"type", "LineString"},
        {"coordinates", PolylineToGeoJSON(boundary.boundary())}}},
      {"properties",
       {{"id", boundary.id()},
        {"length", boundary.length()},
        {"virtual", boundary.virtual_()},
        {"crossable", boundary.crossable()},
        {"has_left_deceleration_marking",
         boundary.has_left_deceleration_marking()},
        {"has_right_deceleration_marking",
         boundary.has_right_deceleration_marking()},
        {"boundary_types", json::array()}}}};

  for (const auto& type : boundary.boundary_type()) {
    if (type.types_size() == 0) continue;
    geojson_feature["properties"]["boundary_types"].push_back(
        type.types(0));  // 假设只取第一个类型
  }

  return geojson_feature;
}

// 将 Lane 转换为 GeoJSON Feature
json HDMapToGeoJsonHandler::LaneToGeoJSON(const gwm::hdmap::Lane& lane) {
  json geojson_feature = {
      {"type", "Feature"},
      {"geometry",
       {{"type", "LineString"},
        {"coordinates", PolylineToGeoJSON(lane.centerline())}}},
      {"properties",
       {{"id", lane.id()},
        {"length", lane.length()},
        {"speed_limit", lane.speed_limit()},
        {"type", lane.type()},
        {"turn", lane.turn()},
        {"direction", lane.direction()}}}};

  return geojson_feature;
}

// 将 LocationInfo 转换为 GeoJSON Feature
json HDMapToGeoJsonHandler::LocationInfoToGeoJSON(
    const gwm::hdmap::LocationInfo& location) {
  json geojson_feature = {
      {"type", "Feature"},
      {"geometry",
       {{"type", "Point"},
        {"coordinates", Point3DToGeoJSON(location.point())}}},
      {"properties",
       {{"radius", location.radias()},
        {"heading_degree", location.heading_degree()},
        {"max_tolerance_angle", location.max_tolerance_angle()},
        {"time_stamp", location.time_stamp()}}}};

  return geojson_feature;
}

json HDMapToGeoJsonHandler::TrackListToGeoJSON(
    const gwm::hdmap::TrackList& track_list) {
  json geojson_feature_collection = {{"type", "FeatureCollection"},
                                     {"features", json::array()}};

  // 添加 TrackList 对象
  for (const auto& location : track_list.locations()) {
    geojson_feature_collection["features"].push_back(
        LocationInfoToGeoJSON(location));
  }
  return geojson_feature_collection;
}

// 将 RoutingMapInfo 和 TrackList 转换为 GeoJSON FeatureCollection
json HDMapToGeoJsonHandler::RoutingMapInfoToGeoJSON(
    const gwm::hdmap::RoutingMapInfo& routing_map) {
  json geojson_feature_collection = {{"type", "FeatureCollection"},
                                     {"features", json::array()}};

  // 添加 Lane 对象
  for (const auto& lane : routing_map.lanes()) {
    geojson_feature_collection["features"].push_back(LaneToGeoJSON(lane));
  }

  // 添加 LaneBoundary 对象
  for (const auto& lane : routing_map.lanes()) {
    if (lane.has_left_boundary()) {
      geojson_feature_collection["features"].push_back(
          LaneBoundaryToGeoJSON(lane.left_boundary()));
    }
    if (lane.has_right_boundary()) {
      geojson_feature_collection["features"].push_back(
          LaneBoundaryToGeoJSON(lane.right_boundary()));
    }
  }

  return geojson_feature_collection;
}

int HDMapToGeoJsonHandler::OutputToFile(const json& geojson,
                                        const std::string& file_path) {
  // 将 GeoJSON 数据写入文件
  std::ofstream output_file(file_path);
  if (!output_file.is_open()) {
    LOG_ERROR << "Failed to open output file hdmap.geojson" << std::endl;
    return -1;
  }
  output_file << geojson.dump(4);  // 使用缩进格式化输出
  output_file.close();
  return 0;
}

}  // namespace common
}  // namespace gwm
