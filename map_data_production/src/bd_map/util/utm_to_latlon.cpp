/*
 * File: utm_to_latlon.cpp
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-21
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include <proj.h>

#include <iostream>
#include <string>

#include "util/utm_to_latlon.hpp"

UTMToLatLonConverter::UTMToLatLonConverter(int zone, bool isNorth) {
  // 创建UTM到WGS84的转换上下文
  std::string projString = "+proj=utm +zone=" + std::to_string(zone) +
                           (isNorth ? " +north" : " +south") +
                           " +ellps=WGS84 +datum=WGS84 +units=m +no_defs";
  utmCrs_ = proj_create_crs_to_crs(
      PJ_DEFAULT_CTX, projString.c_str(),
      "+proj=longlat +ellps=WGS84 +datum=WGS84 +no_defs", nullptr);
  if (!utmCrs_) {
    throw std::runtime_error("Failed to create PROJ transformation context.");
  }
}

UTMToLatLonConverter::~UTMToLatLonConverter() {
  // 释放PROJ资源
  if (utmCrs_) {
    proj_destroy(utmCrs_);
  }
}

std::pair<double, double> UTMToLatLonConverter::Convert(double easting,
                                                        double northing) {
  // 创建坐标点
  PJ_COORD utmCoord = proj_coord(easting, northing, 0, 0);

  // 执行坐标转换
  PJ_COORD latLonCoord = proj_trans(utmCrs_, PJ_FWD, utmCoord);

  // 返回经纬度
  return {latLonCoord.lp.lam, latLonCoord.lp.phi};
}
