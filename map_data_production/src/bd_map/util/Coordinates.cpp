//
// Created by gw00366226 on 25-2-6.
//

#include <proj.h>

#include <cmath>
#include <iostream>

#include "util/Coordinates.h"
#include "util/log.hpp"

namespace gwm {
Coordinates& Coordinates::getInstance() {
  static Coordinates instance;
  return instance;
}

Coordinates::Coordinates() {}
Coordinates::~Coordinates() {}

bool Coordinates::outOfChina(double lat, double lon) {
  if (lon < 72.004 || lon > 137.8347) return true;
  if (lat < 0.8293 || lat > 55.8271) return true;
  return false;
}

double Coordinates::transformLat(double x, double y) {
  double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y +
               0.2 * sqrt(abs(x));
  ret += (20.0 * sin(6.0 * x * pi) + 20.0 * sin(2.0 * x * pi)) * 2.0 / 3.0;
  ret += (20.0 * sin(y * pi) + 40.0 * sin(y / 3.0 * pi)) * 2.0 / 3.0;
  ret += (160.0 * sin(y / 12.0 * pi) + 320 * sin(y * pi / 30.0)) * 2.0 / 3.0;
  return ret;
}

double Coordinates::transformLon(double x, double y) {
  double ret =
      300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(abs(x));
  ret += (20.0 * sin(6.0 * x * pi) + 20.0 * sin(2.0 * x * pi)) * 2.0 / 3.0;
  ret += (20.0 * sin(x * pi) + 40.0 * sin(x / 3.0 * pi)) * 2.0 / 3.0;
  ret += (150.0 * sin(x / 12.0 * pi) + 300.0 * sin(x / 30.0 * pi)) * 2.0 / 3.0;
  return ret;
}

std::pair<double, double> Coordinates::Wgs84ToGcj02(double lng, double lat) {
  if (outOfChina(lat, lng)) {
    LOG_WARN << "outofchina:" << lng << "," << lat << std::endl;
    return {lng, lat};  // 如果不在中国范围内，直接返回原坐标
  }

  double dlat = transformLat(lng - 105.0, lat - 35.0);
  double dlng = transformLon(lng - 105.0, lat - 35.0);

  double radlat = (lat / 180.0) * pi;
  double magic = std::sin(radlat);
  magic = 1 - ee * magic * magic;
  double sqrtmagic = std::sqrt(magic);

  dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * pi);
  dlng = (dlng * 180.0) / ((a / sqrtmagic) * std::cos(radlat) * pi);

  double mglat = lat + dlat;
  double mglng = lng + dlng;

  return {mglng, mglat};
}

void Coordinates::gcj02ToWgs84(double gcjLat, double gcjLon, double& wgsLat,
                               double& wgsLon) {
  if (outOfChina(gcjLat, gcjLon)) {
    wgsLat = gcjLat;
    wgsLon = gcjLon;
    return;
  }
  double dLat = transformLat(gcjLon - 105.0, gcjLat - 35.0);
  double dLon = transformLon(gcjLon - 105.0, gcjLat - 35.0);
  double radLat = gcjLat / 180.0 * pi;
  double magic = sin(radLat);
  magic = 1 - ee * magic * magic;
  double sqrtMagic = sqrt(magic);
  dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
  dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * pi);
  wgsLat = gcjLat - dLat;
  wgsLon = gcjLon - dLon;
}

void Coordinates::wgs84ToUtm(double lat, double lon, double& utmEasting,
                             double& utmNorthing, int& utmZone) {
  double latRad = lat * pi / 180.0;
  double lonRad = lon * pi / 180.0;

  int zone = static_cast<int>((lon + 180) / 6) + 1;
  double lonOrigin =
      (zone - 1) * 6 - 180 + 3;  // +3 puts origin in middle of zone
  double lonOriginRad = lonOrigin * pi / 180.0;

  double eccPrimeSquared = eccSquared / (1 - eccSquared);

  double N = a1 / sqrt(1 - eccSquared * sin(latRad) * sin(latRad));
  double T = tan(latRad) * tan(latRad);
  double C = eccPrimeSquared * cos(latRad) * cos(latRad);
  double A = cos(latRad) * (lonRad - lonOriginRad);

  double M = a1 * ((1 - eccSquared / 4 - 3 * eccSquared * eccSquared / 64 -
                    5 * eccSquared * eccSquared * eccSquared / 256) *
                       latRad -
                   (3 * eccSquared / 8 + 3 * eccSquared * eccSquared / 32 +
                    45 * eccSquared * eccSquared * eccSquared / 1024) *
                       sin(2 * latRad) +
                   (15 * eccSquared * eccSquared / 256 +
                    45 * eccSquared * eccSquared * eccSquared / 1024) *
                       sin(4 * latRad) -
                   (35 * eccSquared * eccSquared * eccSquared / 3072) *
                       sin(6 * latRad));

  utmEasting = k0 * N *
                   (A + (1 - T + C) * A * A * A / 6 +
                    (5 - 18 * T + T * T + 72 * C - 58 * eccPrimeSquared) * A *
                        A * A * A * A / 120) +
               500000.0;

  utmNorthing =
      k0 *
      (M + N * tan(latRad) *
               (A * A / 2 + (5 - T + 9 * C + 4 * C * C) * A * A * A * A / 24 +
                (61 - 58 * T + T * T + 600 * C - 330 * eccPrimeSquared) * A *
                    A * A * A * A * A / 720));

  if (lat < 0)
    utmNorthing += 10000000.0;  // 10000000 meter offset for southern hemisphere

  utmZone = zone;
}

// 定义墨卡托转经纬度的函数
void Coordinates::mercatorToLatLon(double x, double y, double& lat,
                                   double& lon) {
  // 创建转换上下文
  PJ_CONTEXT* ctx = proj_context_create();
  if (!ctx) {
    LOG_ERROR << "Failed to create PROJ context." << std::endl;
    return;
  }

  // 定义源投影（Web Mercator，EPSG:3857）和目标投影（WGS84，EPSG:4326）
  PJ* pj_mercator =
      proj_create_crs_to_crs(ctx, "EPSG:3857", "EPSG:4326", nullptr);
  if (!pj_mercator) {
    LOG_ERROR << "Failed to create projection transformation." << std::endl;
    proj_context_destroy(ctx);
    return;
  }

  // 创建一个 PJ_COORD 对象来存储输入的墨卡托坐标
  PJ_COORD coord = proj_coord(x, y, 0, 0);
  // 进行坐标转换
  PJ_COORD result = proj_trans(pj_mercator, PJ_FWD, coord);

  // 提取转换后的经纬度
  lon = result.uv.u;
  lat = result.uv.v;

  // 释放资源
  proj_destroy(pj_mercator);
  proj_context_destroy(ctx);
}
// 计算 UTM 带号
int Coordinates::calculateUTMZone(double lon) {
  return static_cast<int>((lon + 180) / 6) + 1;
}
// 墨卡托转 UTM 坐标的函数
void Coordinates::mercatorToUTM(double x, double y, double& easting,
                                double& northing, int& zone,
                                bool& is_northern) {
  // 创建转换上下文
  PJ_CONTEXT* ctx = proj_context_create();
  if (!ctx) {
    LOG_ERROR << "Failed to create PROJ context." << std::endl;
    return;
  }

  // 先将墨卡托坐标转换为经纬度坐标（EPSG:4326）
  PJ* pj_mercator_to_latlon =
      proj_create_crs_to_crs(ctx, "EPSG:3857", "EPSG:4326", nullptr);
  if (!pj_mercator_to_latlon) {
    LOG_ERROR
        << "Failed to create projection transformation from Mercator to LatLon."
        << std::endl;
    proj_context_destroy(ctx);
    return;
  }

  // 创建一个 PJ_COORD 对象来存储输入的墨卡托坐标
  PJ_COORD coord_mercator = proj_coord(x, y, 0, 0);
  // 进行坐标转换，得到经纬度坐标
  PJ_COORD coord_latlon =
      proj_trans(pj_mercator_to_latlon, PJ_FWD, coord_mercator);

  // 提取经纬度
  double lat = coord_latlon.uv.v;
  double lon = coord_latlon.uv.u;

  // 计算 UTM 带号
  zone = calculateUTMZone(lon);
  // 判断是否为北半球
  is_northern = lat >= 0;

  // 根据半球和带号确定 UTM 的 EPSG 代码
  std::string utm_epsg = is_northern ? "EPSG:326" : "EPSG:327";
  utm_epsg += std::to_string(zone);

  // 创建从经纬度到 UTM 的投影转换对象
  PJ* pj_latlon_to_utm =
      proj_create_crs_to_crs(ctx, "EPSG:4326", utm_epsg.c_str(), nullptr);
  if (!pj_latlon_to_utm) {
    LOG_ERROR
        << "Failed to create projection transformation from LatLon to UTM."
        << std::endl;
    proj_destroy(pj_mercator_to_latlon);
    proj_context_destroy(ctx);
    return;
  }

  // 进行经纬度到 UTM 的坐标转换
  PJ_COORD coord_utm = proj_trans(pj_latlon_to_utm, PJ_FWD, coord_latlon);

  // 提取 UTM 坐标
  easting = coord_utm.uv.u;
  northing = coord_utm.uv.v;

  // 释放资源
  proj_destroy(pj_mercator_to_latlon);
  proj_destroy(pj_latlon_to_utm);
  proj_context_destroy(ctx);
}

// 经纬度转 UTM 坐标的函数
void Coordinates::latlonToUTM(double lat, double lon, double& easting,
                              double& northing, int& zone, bool& is_northern) {
  // 创建转换上下文
  PJ_CONTEXT* ctx = proj_context_create();
  if (!ctx) {
    LOG_ERROR << "Failed to create PROJ context." << std::endl;
    return;
  }

  // 计算 UTM 带号
  zone = calculateUTMZone(lon);
  // 判断是否为北半球
  is_northern = lat >= 0;

  // 根据半球和带号确定 UTM 的 EPSG 代码
  std::string utm_epsg = is_northern ? "EPSG:326" : "EPSG:327";
  utm_epsg += std::to_string(zone);

  // 创建从经纬度（EPSG:4326）到 UTM 的投影转换对象
  PJ* pj_latlon_to_utm =
      proj_create_crs_to_crs(ctx, "EPSG:4326", utm_epsg.c_str(), nullptr);
  if (!pj_latlon_to_utm) {
    LOG_ERROR
        << "Failed to create projection transformation from LatLon to UTM."
        << std::endl;
    proj_context_destroy(ctx);
    return;
  }

  // 创建一个 PJ_COORD 对象来存储输入的经纬度坐标
  PJ_COORD coord_latlon = proj_coord(lon, lat, 0, 0);
  // 进行坐标转换，得到 UTM 坐标
  PJ_COORD coord_utm = proj_trans(pj_latlon_to_utm, PJ_FWD, coord_latlon);

  // 提取 UTM 坐标
  easting = coord_utm.uv.u;
  northing = coord_utm.uv.v;

  // 释放资源
  proj_destroy(pj_latlon_to_utm);
  proj_context_destroy(ctx);
}

void Coordinates::gcj02ToUtm(double gcjLat, double gcjLon, double& utmEasting,
                             double& utmNorthing, int& utmZone) {
  double wgsLat, wgsLon;
  gcj02ToWgs84(gcjLat, gcjLon, wgsLat, wgsLon);
  wgs84ToUtm(wgsLat, wgsLon, utmEasting, utmNorthing, utmZone);
}

void Coordinates::mercatorToUtm(double x, double y, double& utmEasting,
                                double& utmNorthing, int& utmZone) {
  double lon = .0, lat = .0;
  mercatorToLatLon(x, y, lat, lon);
  gcj02ToUtm(lon, lat, utmEasting, utmNorthing, utmZone);
}

// 将角度转换为弧度
double Coordinates::toRadians(double degree) { return degree * M_PI / 180.0; }

// 将弧度转换为角度
double Coordinates::toDegrees(double radians) { return radians * 180.0 / M_PI; }

// 计算两个经纬度之间的方位角
double Coordinates::calculateBearing(double lat1, double lon1, double lat2,
                                     double lon2) {
  // 将经纬度转换为弧度
  double lat1Rad = toRadians(lat1);
  double lon1Rad = toRadians(lon1);
  double lat2Rad = toRadians(lat2);
  double lon2Rad = toRadians(lon2);

  // 计算经度差
  double deltaLon = lon2Rad - lon1Rad;

  // 计算方位角
  double y = sin(deltaLon) * cos(lat2Rad);
  double x =
      cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(deltaLon);
  double bearing = atan2(y, x);

  // 将方位角转换为度数并确保在0到360度之间
  bearing = toDegrees(bearing);
  bearing = fmod((bearing + 360.0), 360.0);

  return bearing;
}

// 封装函数：UTM坐标转WGS84经纬度坐标
bool Coordinates::UTMToWGS84(double utm_easting, double utm_northing, int zone,
                             bool is_northern, double& lat, double& lon) {
  // 创建PROJ上下文
  PJ_CONTEXT* ctx = proj_context_create();
  if (!ctx) {
    std::cerr << "Failed to create PROJ context." << std::endl;
    return false;
  }

  // 构造UTM坐标系的EPSG代码
  std::string utm_crs_code = "EPSG:" + std::to_string(32600 + zone);  // 北半球
  if (!is_northern) {
    utm_crs_code = "EPSG:" + std::to_string(32700 + zone);  // 南半球
  }
  std::cout << "utm_crs_code:" << utm_crs_code << std::endl;

  // 创建UTM坐标系和WGS84坐标系
  PJ* utm_crs = proj_create(ctx, utm_crs_code.c_str());
  PJ* wgs84_crs = proj_create(ctx, "EPSG:4326");

  if (!utm_crs || !wgs84_crs) {
    std::cerr << "Failed to create CRS objects." << std::endl;
    proj_context_destroy(ctx);
    return false;
  }

  // 创建坐标转换对象
  PJ* transformation =
      proj_create_crs_to_crs_from_pj(ctx, utm_crs, wgs84_crs, nullptr, nullptr);
  if (!transformation) {
    std::cerr << "Failed to create transformation object." << std::endl;
    proj_destroy(utm_crs);
    proj_destroy(wgs84_crs);
    proj_context_destroy(ctx);
    return false;
  }

  // 将转换对象规范化为视觉化使用
  PJ* transform = proj_normalize_for_visualization(ctx, transformation);
  proj_destroy(transformation);

  if (!transform) {
    std::cerr << "Failed to normalize transformation." << std::endl;
    proj_destroy(utm_crs);
    proj_destroy(wgs84_crs);
    proj_context_destroy(ctx);
    return false;
  }

  // 输入UTM坐标
  PJ_COORD input = proj_coord(utm_easting, utm_northing, 0, 0);

  // 执行坐标转换
  PJ_COORD output = proj_trans(transform, PJ_INV,
                               input);  // PJ_INV 表示逆转换（UTM -> WGS84）

  // 获取经纬度结果
  lon = output.lp.lam;
  lat = output.lp.phi;

  // 清理资源
  proj_destroy(transform);
  proj_destroy(utm_crs);
  proj_destroy(wgs84_crs);
  proj_context_destroy(ctx);

  return true;
}

}  // namespace gwm
