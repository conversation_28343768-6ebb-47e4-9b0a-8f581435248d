cmake_minimum_required(VERSION 3.16)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -pthread")
set(CMAKE_CXX_STANDARD 17)
project(bd_ld_engine)
include(${THIRD_PARTY_DIR}/baidu/x86/FindBDmap.cmake)

find_package(Threads)
find_library(ZMQ_LIBRARY NAMES zmq)
find_package(Protobuf REQUIRED)
find_library(PROJ_LIBRARY NAMES proj)
find_library(PROJ_LIBRARY proj PATHS /usr/local/lib NO_DEFAULT_PATH)  # 指定路径
if (NOT PROJ_LIBRARY)
    message(FATAL_ERROR "libproj not found in /usr/local/lib")
endif()
include_directories( ${BDMAP_INCLUDE_DIR}
                    ${BDMAP_INCLUDE_DIR}/util
                    ${BDMAP_INCLUDE_DIR}/map_engine
                    ${BDMAP_INCLUDE_DIR}/dtc
                    )
include_directories( ${PROJECT_ROOT_DIR}
                    ${PROJECT_ROOT_DIR}/proto
                    ${PROJECT_ROOT_DIR}/proto/common
                    ${PROJECT_ROOT_DIR}/include
                    ${PROJECT_ROOT_DIR}/include/bd_map
                    ${PROJECT_ROOT_DIR}/include/bd_map/util)
add_executable(${PROJECT_NAME}  
                ./test_data.cpp 
                ./sd_pro_data_manager.cc
                ./track_info_reader.cc
                ./cJSON.c
                ./hd_map_to_geojson.cc
                ./util/Coordinates.cpp
                ./util/utm_to_latlon.cpp 
                 ${PROTO_SRCS} 
                 ${PROTO_HDR})

target_link_libraries (${PROJECT_NAME}
                       ${BDMAP_LIB_DIR}/libime.so
                       ${BDMAP_LIB_DIR}/libjsoncpp.so
                       ${BDMAP_LIB_DIR}/libprotobuf.so
                       ${BDMAP_LIB_DIR}/libcares.so
                       ${BDMAP_LIB_DIR}/libcurl.so
                       ${BDMAP_LIB_DIR}/libprotobuf.so
                       ${BDMAP_LIB_DIR}/libz.so
                       ${BDMAP_LIB_DIR}/libhdiffpatch.so
                       /usr/lib/x86_64-linux-gnu/libssl.so
                       /usr/lib/x86_64-linux-gnu/libcrypto.so
                        ${BDMAP_LIBRARIES} 
                        ${PROTOBUF_LIBRARIES} 
                        ${ZMQ_LIBRARY} 
                        ${PROJ_LIBRARY})

#set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install/bd_map" CACHE PATH "Install path prefix" FORCE)
# 设置安装路径
#if(NOT CMAKE_INSTALL_PREFIX)
#    set(CMAKE_INSTALL_PREFIX "${CMAKE_BINARY_DIR}/install/bd_map" CACHE PATH "Install path prefix" FORCE)
#endif()

# 配置RPATH设置
set(CMAKE_INSTALL_RPATH "$ORIGIN/../lib")
set(CMAKE_INSTALL_RPATH_USE_LINK_PATH TRUE)


#file(GLOB BD_API_LIBRARIES "${BAIDU_SDK_DIR}/lib/*.so*")

install(FILES ${BDMAP_LIBRARIES}  DESTINATION bd_lib)
