/*
 * File: track_info_reader.cc
 * Description:
 * Author: qingxiansun
 * Date: 2025-03-28
 * Attention: Copyright Great Wall Technology Co.Ltd
 * Attention: Please refer to COPYRIGHT.txt for complete terms of copyright
 */

#include <iomanip>
#include <set>

#include "common/track_info_reader.hpp"
#include "util/Coordinates.h"
#include "util/cloest_value_finder.hpp"
#include "util/csv.h"
#include "util/log.hpp"

namespace gwm {
namespace common {

TrackList TrackInfoReader::ConvertTrackList(
    const std::string &global_gt_file_path,
    const std::string &track_file_path) {
  TrackList track_list;
  std::vector<GPSRecord> gps_records;
  std::vector<PoseRecord> pose_records;
  LoadTrackFile(track_file_path, gps_records);
  LoadGlobalGTFile(global_gt_file_path, pose_records);
  auto timestampSelector = [](const GPSRecord &record) {
    return record.timestamp;
  };
  ClosestObjectFinder<std::vector<GPSRecord>, GPSRecord, int64_t> finder(
      gps_records, timestampSelector);

  DebugCode(LOG_INFO << "Parsed pose records size:" << pose_records.size()
                     << std::endl);
  for (const auto &item : pose_records) {
    DebugCode(LOG_INFO << std::fixed << std::setprecision(6) << "Timestamp:"
                       << item.timestamp << ", yaw:" << item.yaw << std::endl);
    auto &gps_record = finder.findClosest(item.timestamp);
    auto loc = track_list.add_locations();
    loc->mutable_point()->set_x(gps_record.longitude);
    loc->mutable_point()->set_y(gps_record.latitude);
    loc->set_radias(5);
    loc->set_heading_degree(item.yaw);
    loc->set_max_tolerance_angle(10);
    loc->set_time_stamp(static_cast<double>(gps_record.timestamp) / 1e9);

    DebugCode(LOG_INFO << std::fixed << std::setprecision(6)
                       << "Timestamp: " << gps_record.timestamp
                       << ", Lon: " << gps_record.longitude
                       << ", Lat: " << gps_record.latitude
                       << ", Alt: " << gps_record.altitude
                       << ", Status: " << gps_record.status << std::endl);
  }
  DebugCode(LOG_INFO << "track_list.location.size:"
                     << track_list.locations_size() << std::endl);

  return track_list;
}

GNSSCollection TrackInfoReader::ConvertGNSSInfo(
    const std::string &track_file_path) {
  GNSSCollection res;
  std::vector<GPSRecord> gps_records;
  LoadTrackFile(track_file_path, gps_records);
  auto &coord_helper = gwm::Coordinates::getInstance();
  double utm_easting = .0;
  double utm_northing = .0;
  int utm_zone = 0;
  for (auto &record : gps_records) {
    auto gnss = res.add_gnss();
    gnss->set_time_stamp(record.timestamp);
    coord_helper.gcj02ToUtm(record.latitude, record.longitude, utm_easting,
                            utm_northing, utm_zone);
    gnss->set_easting(utm_easting);
    gnss->set_northing(utm_northing);
    gnss->set_up(record.altitude);
    gnss->set_status(record.status);
  }

  return res;
}

int TrackInfoReader::LoadTrackFile(const std::string &track_file_path,
                                   std::vector<GPSRecord> &gps_records) {
  int ret = 0;
  io::CSVReader<5, io::trim_chars<' '>, io::no_quote_escape<' '>,
                io::throw_on_overflow>
      reader(track_file_path);
  reader.read_header(io::ignore_extra_column, "#timestamp", "longitude",
                     "latitude", "altitude", "status");

  int64_t timestamp = 0;
  double longitude = .0;
  double latitude = .0;
  double altitude = .0;
  int status = 0;
  auto &coord_helper = gwm::Coordinates::getInstance();

  while (reader.read_row(timestamp, longitude, latitude, altitude, status)) {
    auto gc_pos = coord_helper.Wgs84ToGcj02(longitude, latitude);
    gps_records.resize(gps_records.size() + 1);
    auto &record = gps_records.back();
    record.timestamp = timestamp;
    record.longitude = gc_pos.first;
    record.latitude = gc_pos.second;
    record.altitude = altitude;
    record.status = status;
    gps_records.push_back(record);
  }
  std::sort(gps_records.begin(), gps_records.end(),
            [](const GPSRecord &left, const GPSRecord &right) {
              return left.timestamp < right.timestamp;
            });

  return ret;
}

int TrackInfoReader::LoadGlobalGTFile(const std::string &global_gt_file_path,
                                      std::vector<PoseRecord> &pose_records) {
  int ret = 0;
  auto &coord_helper = gwm::Coordinates::getInstance();
  io::CSVReader<2> reader(global_gt_file_path);
  reader.read_header(io::ignore_extra_column, "stamp_sec", "yaw");
  std::set<int64_t> containers;
  double timestamp = .0;
  int64_t timestamp2 = 0;
  double yaw = .0;
  while (reader.read_row(timestamp, yaw)) {
    timestamp2 = static_cast<int64_t>(std::round(timestamp * 1e9));
    auto res = containers.insert(timestamp2);
    if (!res.second) continue;
    pose_records.resize(pose_records.size() + 1);
    auto &pose = pose_records.back();
    pose.timestamp = pose.yaw = coord_helper.toDegrees(yaw);
  }
  std::sort(pose_records.begin(), pose_records.end(),
            [](const PoseRecord &left, const PoseRecord &right) {
              return left.timestamp < right.timestamp;
            });
  return ret;
}

}  // namespace common
}  // namespace gwm
