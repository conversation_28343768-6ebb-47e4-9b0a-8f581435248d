#include <zmq.h>

#include <chrono>
#include <iomanip>
#include <iostream>
#include <string>
#include <thread>

#include "proto/sd_map.pb.h"
#include "util/log.hpp"
#include "zmq/ZMQReader.hpp"

void PrintSDMapInfo(const std::shared_ptr<gwm::sdmap::SDMap> &map_info);
//
int main(int argc, char **argv) {
  int major = 0, minor = 0, patch = 0;
  zmq_version(&major, &minor, &patch);
  gwm::sdmap::SDMap map_info;
  LOG_INFO << "Current ZMQ version is " << major << "." << minor << "." << patch
           << std::endl;
  auto func = [](const std::shared_ptr<gwm::sdmap::SDMap> &message) -> void {
    PrintSDMapInfo(message);
    // auto &header = message->header();
    // LOG_INFO << "ZQM receive message: "  << std::endl;
    // LOG_INFO << "header.seq:" << header.seq() << std::endl;
    // LOG_INFO << "header.frame_id:" << header.frame_id() << std::endl;
    // LOG_INFO << "header.publish_time:" << header.publish_stamp() <<
    // std::endl; LOG_INFO << "-------------------------------------------" <<
    // std::endl;
  };
  gwm::transformer::ZMQReader<gwm::sdmap::SDMap> reader("gwm/sdmap", func,
                                                        ZMQ_IPC);
  reader.wait();
  // while (true) {
  //	std::this_thread::sleep_for(std::chrono::seconds(1));
  //	LOG_INFO << " main process handling" << std::endl;

  //}
  LOG_INFO << "The program executing finish." << std::endl;
  return 0;
}

void PrintSDMapInfo(const std::shared_ptr<gwm::sdmap::SDMap> &map_info) {
  auto header = map_info->header();
  LOG_INFO << "header.seq:" << header.seq() << std::endl;
  LOG_INFO << "header.frame_id:" << header.frame_id() << std::endl;
  LOG_INFO << "header.publish_time:" << header.publish_stamp() << std::endl;
  auto sd_loc = map_info->sd_location();
  LOG_INFO << "sd_location.roadId: " << sd_loc.roadid() << std::endl;
  LOG_INFO << "sd_location.offset: " << sd_loc.offset() << std::endl;
  LOG_INFO << "sd_location.matchPoint x:" << sd_loc.matchpoint().x()
           << ", y: " << sd_loc.matchpoint().x()
           << " , z:" << sd_loc.matchpoint().x() << std::endl;
  auto &roads = map_info->road();
  LOG_INFO << "map_info.road size:" << map_info->road_size() << std::endl;
  for (auto &road : roads) {
    LOG_INFO << "road.road_id:" << road.roadid() << std::endl;
    LOG_INFO << "road.lane_count:" << road.lane_count() << std::endl;
    LOG_INFO << "road.angleF:" << road.anglef() << std::endl;
    LOG_INFO << "road.angleT:" << road.anglet() << std::endl;
    LOG_INFO << "road.road_type:" << road.road_type() << std::endl;
    LOG_INFO << "road.road_form:" << road.road_form() << std::endl;
    LOG_INFO << "road.length:" << road.length() << std::endl;
    auto &points = road.points();
    LOG_INFO << "points: ";
    for (auto point : points) {
      LOG_INFO << std::fixed << std::setprecision(8) << point.x() << ","
               << point.y() << ",";
    }
    LOG_INFO << std::endl;
    LOG_INFO << "arrow markings: ";
    auto &arrow_markings = road.arrowmarkings();
    for (auto arrow : arrow_markings) {
      LOG_INFO << arrow << ",";
    }
    LOG_INFO << std::endl;
    auto &connect_road = road.connect_road();
    auto &from_road_ids = connect_road.fromroadids();
    LOG_INFO << "from road ids:";
    for (auto road_id : from_road_ids) {
      LOG_INFO << road_id << ",";
    }
    LOG_INFO << std::endl;
    LOG_INFO << "to raod ids:";
    auto &to_road_ids = connect_road.toroadids();
    for (auto road_id : to_road_ids) {
      LOG_INFO << road_id << ",";
    }
    LOG_INFO << std::endl;
  }
  LOG_INFO
      << "--------------------------------------------------------------------"
      << std::endl;
}
