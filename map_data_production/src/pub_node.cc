#include <zmq.h>

#include <chrono>
#include <cmath>
#include <fstream>
#include <iomanip>
#include <iostream>
#include <limits>
#include <string>
#include <thread>

#include "api/map_data_engine_api.h"
#include "api/nerd_utils.h"
#include "nerd_api/nerd_version.h"
#include "proto/hd_map_lane.pb.h"
#include "proto/sd_map.pb.h"
#include "tc_map/nerd/ha_data_manager.hpp"
#include "tc_map/route_match/route_match.hpp"
#include "util/Coordinates.h"
#include "util/date_time.h"
#include "util/log.hpp"
#include "zmq/ZMQWriter.hpp"

bool IsZero(double x) {
  return std::abs(x) < std::numeric_limits<double>::epsilon() * 10;
}

//
void FillMapInfo(gwm::sdmap::SDMap &map_info);

bool LoadTestFile(const std::string &file_path, gwm::sdmap::SDMap &map_info);

void PrintSDMapInfo(gwm::sdmap::SDMap &map_info);

void SetLocation(gwm::sdmap::SDMap &map_info, double utm_east,
                 double utm_north);
void GetGDMapRoads(const std::string &file_path,
                   gwm::sdmap::SDMap &gd_map_info);
void FillGDShowMapRoads(const gwm::sdmap::SDMap &gd_map_info,
                        gwm::sdmap::SDMap &gd_show_map_info,
                        double &loc_utm_east, double &loc_utm_north);

int main(int argc, char **argv) {
  std::string kGnssTrackFile = "./gps_track/Beijing_HNP_001.json";
  std::string config_file = "./conf/tencent_cloud_atlas_conf.json";
  if (2 == argc) {
    kGnssTrackFile = argv[1];
  }
  tc_map::nerd::HADataManager manager;
  std::string data_dir_20241206 = "./data/20241206";  // shanghai 20241206 data
  std::string data_dir_20250211 = "./data/20250211";  // shanghai 20250211 data
  std::string data_dir_20250228_HA = "./data/20250228/HA";
  std::string data_dir_20250228_HD = "./data/20250228/HD";
  std::vector<std::string> data_dirs = {data_dir_20241206, data_dir_20250211,
                                        data_dir_20250228_HA,
                                        data_dir_20250228_HD

  };
  manager.InitSDK(data_dirs[1], "./log", (24 * 30), (200 * 1024 * 1024),
                  (5 * 1024 * 1024));
  // manager.LoadDemo();
  int major = 0, minor = 0, patch = 0;
  zmq_version(&major, &minor, &patch);
  std::string file_path = "./test/data/map0312.bin";
  // std::string file_path = "./test/data/map_new.bin";
  // std::string file_path = "./test/data/map2.bin";
  LOG_INFO << "Current ZMQ version is " << major << "." << minor << "." << patch
           << std::endl;
  gwm::transformer::ZMQWriter writer(ZMQ_IPC);
  gwm::sdmap::SDMap gd_map_roads;
  gwm::sdmap::SDMap gd_show_map_roads;
  std::vector<tca::TxLink> tx_links;
  gwm::sdmap::SDMap tc_map_roads;
  double loc_utm_east = .0, loc_utm_north = .0;
  // if (!manager.FillSDMapInfo(gd_map_roads)) {
  //       	if (!LoadTestFile(file_path, gd_map_roads)) {
  //	       	FillMapInfo(gd_map_roads);
  //	}
  //} else {
  //	FillMapInfo(gd_map_roads);
  //	LOG_INFO << "Fill map header info." << std::endl;
  //}

  tc_map::route_match::RouteMatchClient client;
  // int ret = client.GetMatchedMapRoads(gd_map_roads, tx_links);
  // if (ret != 0) {
  //  LOG_ERROR << "GetMatchedMapRoads failed" << std::endl;
  //}

  while (true) {
    gd_map_roads.Clear();
    gd_show_map_roads.Clear();
    tx_links.clear();
    tc_map_roads.Clear();
    GetGDMapRoads(file_path, gd_map_roads);
    FillGDShowMapRoads(gd_map_roads, gd_show_map_roads, loc_utm_east,
                       loc_utm_north);
    client.Init(config_file);
    int ret = client.GetMatchedMapRoads(gd_map_roads, tx_links);
    if (ret != 0) {
      LOG_ERROR << "GetMatchedMapRoads failed" << std::endl;
    }

    FillMapInfo(tc_map_roads);
    SetLocation(tc_map_roads, loc_utm_east, loc_utm_north);
    // manager.AddSDMapRoadFromTxLink(tx_links, tc_map_roads);
    manager.MatchRoadLinksToSDMap(tx_links, tc_map_roads);
    // client.TestDemo(kGnssTrackFile);

    PrintSDMapInfo(tc_map_roads);
    writer.Write<gwm::sdmap::SDMap>("gwm/sdmap", tc_map_roads);
    std::this_thread::sleep_for(std::chrono::seconds(5));
    writer.Write<gwm::sdmap::SDMap>("gwm/sdmap", gd_show_map_roads);
    std::this_thread::sleep_for(std::chrono::seconds(5));
    LOG_INFO << "Send  sd_map info success." << std::endl;
  }
  return 0;
}

void FillGDShowMapRoads(const gwm::sdmap::SDMap &gd_map_info,
                        gwm::sdmap::SDMap &gd_show_map_info,
                        double &loc_utm_east, double &loc_utm_north) {
  auto &coord = gwm::Coordinates::getInstance();
  gd_show_map_info.CopyFrom(gd_map_info);
  auto roads = gd_show_map_info.mutable_road();
  double lon = .0, lat = .0;
  double utm_east = .0, utm_north = .0;
  int utm_zone = 0;
  auto sd_loc = gd_show_map_info.mutable_sd_location();
  auto match_point = sd_loc->mutable_matchpoint();
  lon = match_point->x();
  lat = match_point->y();
  coord.gcj02ToUtm(lat, lon, utm_east, utm_north, utm_zone);
  match_point->set_x(utm_east);
  match_point->set_y(utm_north);
  loc_utm_east = utm_east;
  loc_utm_north = utm_north;
  LOG_INFO << "loc lon,lat:" << lon << "," << lat
           << " utm east north:" << utm_east << "," << utm_north << std::endl;
  for (auto &road : *roads) {
    for (auto &poi : *road.mutable_points()) {
      lon = poi.x();
      lat = poi.y();
      coord.gcj02ToUtm(lat, lon, utm_east, utm_north, utm_zone);
      poi.set_x(utm_east);
      poi.set_y(utm_north);
      if (IsZero(loc_utm_east) || IsZero(loc_utm_north)) {
        loc_utm_east = utm_east;
        loc_utm_north = utm_north;
      }
      LOG_INFO << "lon,lat:" << lon << "," << lat
               << " utm east north:" << utm_east << "," << utm_north
               << std::endl;
    }
  }
  match_point->set_x(loc_utm_east);
  match_point->set_y(loc_utm_north);
}

void GetGDMapRoads(const std::string &file_path,
                   gwm::sdmap::SDMap &gd_map_info) {
  if (!LoadTestFile(file_path, gd_map_info)) {
    FillMapInfo(gd_map_info);
  }
}

void SetLocation(gwm::sdmap::SDMap &map_info, double utm_east,
                 double utm_north) {
  auto sd_loc = map_info.mutable_sd_location();
  auto match_point = sd_loc->mutable_matchpoint();
  match_point->set_x(utm_east);
  match_point->set_y(utm_north);
}

bool LoadTestFile(const std::string &file_path, gwm::sdmap::SDMap &map_info) {
  std::fstream map_file(file_path, std::ios::in | std::ios::binary);
  if (!map_file) {
    LOG_INFO << "map file " << file_path << " is not found" << std::endl;
    return false;
  }
  if (!map_info.ParseFromIstream(&map_file)) {
    return false;
  }

  return true;
}

void FillMapInfo(gwm::sdmap::SDMap &map_info) {
  auto header = map_info.mutable_header();
  header->set_seq(10001);
  header->set_frame_id("1111");
  header->set_publish_stamp(CurrentTimestampS());
  header->set_gnss_stamp(CurrentTimestampS());
  header->set_data_stamp(CurrentTimestampS());
  auto sensor_stamp = header->mutable_sensor_stamp();
  sensor_stamp->set_lidar_stamp(CurrentTimestampS());
  sensor_stamp->set_radar_stamp(CurrentTimestampS());
  sensor_stamp->set_uss_stamp(CurrentTimestampS());
  sensor_stamp->set_chassis_stamp(CurrentTimestampS());
  sensor_stamp->set_camera_stamp(CurrentTimestampS());
  sensor_stamp->set_imuins_stamp(CurrentTimestampS());
  sensor_stamp->set_gnss_stamp(CurrentTimestampS());
}

void PrintSDMapInfo(gwm::sdmap::SDMap &map_info) {
  auto header = map_info.header();
  LOG_INFO << "header.seq:" << header.seq() << std::endl;
  LOG_INFO << "header.frame_id:" << header.frame_id() << std::endl;
  LOG_INFO << "header.publish_time:" << header.publish_stamp() << std::endl;
  auto sd_loc = map_info.sd_location();
  LOG_INFO << "sd_location.roadId: " << sd_loc.roadid() << std::endl;
  LOG_INFO << "sd_location.offset: " << sd_loc.offset() << std::endl;
  LOG_INFO << "sd_location.matchPoint x:" << sd_loc.matchpoint().x()
           << ", y: " << sd_loc.matchpoint().x()
           << " , z:" << sd_loc.matchpoint().x() << std::endl;
  auto &roads = map_info.road();
  LOG_INFO << "map_info.road size:" << map_info.road_size() << std::endl;
  for (auto &road : roads) {
    LOG_INFO << "road.road_id:" << road.roadid() << std::endl;
    LOG_INFO << "road.lane_count:" << road.lane_count() << std::endl;
    LOG_INFO << "road.angleF:" << road.anglef() << std::endl;
    LOG_INFO << "road.angleT:" << road.anglet() << std::endl;
    LOG_INFO << "road.road_type:" << road.road_type() << std::endl;
    LOG_INFO << "road.road_form:" << road.road_form() << std::endl;
    LOG_INFO << "road.length:" << road.length() << std::endl;
    auto &points = road.points();
    LOG_INFO << "points: ";
    for (auto point : points) {
      LOG_INFO << std::fixed << std::setprecision(8) << point.x() << ","
               << point.y() << ",";
    }
    LOG_INFO << std::endl;
    LOG_INFO << "arrow markings: ";
    auto &arrow_markings = road.arrowmarkings();
    for (auto arrow : arrow_markings) {
      LOG_INFO << arrow << ",";
    }
    LOG_INFO << std::endl;
    auto &connect_road = road.connect_road();
    auto &from_road_ids = connect_road.fromroadids();
    LOG_INFO << "from road ids:";
    for (auto road_id : from_road_ids) {
      LOG_INFO << road_id << ",";
    }
    LOG_INFO << std::endl;
    LOG_INFO << "to raod ids:";
    auto &to_road_ids = connect_road.toroadids();
    for (auto road_id : to_road_ids) {
      LOG_INFO << road_id << ",";
    }
    LOG_INFO << std::endl;
  }
  LOG_INFO
      << "--------------------------------------------------------------------"
      << std::endl;
}
