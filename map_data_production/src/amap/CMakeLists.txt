cmake_minimum_required(VERSION 3.16)
set(CMAKE_CXX_STANDARD 17)
project(MAPAPI_Demo)
include(${THIRD_PARTY_DIR}/amap/x86/FindAmap.cmake)
add_executable(${PROJECT_NAME}   ./demo.cpp ./sd_data_manager.cc)
include_directories( ${AMAP_INCLUDE_DIR})
target_link_libraries (${PROJECT_NAME}  ${AMAP_LIBRARIES})

include_directories( ${PROJECT_ROOT_DIR})
include_directories( ${PROJECT_ROOT_DIR}/include)
include_directories( ${PROJECT_ROOT_DIR}/proto)


install(FILES ${AMAP_LIBRARIES}  DESTINATION amap_lib)
