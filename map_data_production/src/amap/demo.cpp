/*
 * Copyright (C) 2019 - 2024 Amap Auto
 */
#include <stdio.h>

#include <iostream>

#ifdef _WIN32
#include <io.h>
#include <process.h>
#else
#include <unistd.h>
#endif

#include <cstring>
#include <vector>

#include "GHDMainService.h"  // SDK接口头文件
#include "GHDMapAPIService.h"
#include "amap/sd_data_manager.hpp"

#define ARGC_MIN_COUNT 2

#ifdef _WIN32
#define sleep_this_thread(ms) Sleep(ms);
#else
#define sleep_this_thread(ms) usleep((ms)*1000);
#endif

namespace hdmap {
namespace service {

// 诊断处理示例代码
// 定义一个class实现IHDMapDiagnosisReceiver接口
class DiagnosisImp : public IHDMapDiagnosisReceiver {
public:
  DiagnosisImp() {}

  virtual ~DiagnosisImp() {}

  // 接口到诊断信息，具体诊断类型定义参考接口定义
  virtual void receiveDiagnosis(DiagnosisState state, DiagnosisType type,
                                const char* detailMessage) {
    // 处理诊断消息

    // 示例代码，集成方主动调用清除所有诊断
    GHDDiagnosisService* service = GHDDiagnosisService::getService();
    if (service) {
      service->clearAllDiagnosis();
    }
    if (detailMessage != nullptr) {
      std::cout << "state:" << state << " type:" << type
                << " message:" << detailMessage << std::endl;
    }
  }
};

// 系统设备接口实现示例代码（必须实现）
// 定义一个class实现ISystemDevice接口
class SystemDeviceImp : public ISystemDevice {
public:
  SystemDeviceImp() {}

  ~SystemDeviceImp() {}

public:
  // 提供设备唯一识别号，与激活时提供的一致
  // UID非常重要，需要保证正确。
  virtual const char* getUid() { return "750"; }

  // 提供系统信息，包括系统名、系统版本等
  virtual SystemInfo getSystemInfo() {
    static SystemInfo info;
    info.systemName = (char*)"linux";
    info.systemVersion = (char*)"2.0.0";

    return info;
  }
  // 提供网络状态，用于网络请求相关逻辑判断，需要与设备真实网络保持一致
  virtual NetworkState getNetworkState() {
    // return NETWORK_STATE_2G;
    return NETWORK_STATE_LINE;
    // return NETWORK_STATE_WIFI;
  }

  // 提供可用磁盘空间，数据更新时需要判断剩余磁盘空间
  virtual int64_t getDiskFreeSize() {
    return 1024 * 1024 * 1024;
    // return -1;
  }

  // 设置自定义配置接口，可根据不同需求来实现具体的更新，返回格式为json格式
  // 支持的配置可参考官网-高级辅助驾驶专区【产品指南-SDK产品对接说明/9.配置项相关-2支持车企/系统方对部分配置取值进行调整】章节。
  virtual const char* getCustomConfig() {
    // 打开本地日志（日志更全，便于问题分析）
    std::cout << "getCustomCOnfig" << std::endl;
    return "{\"log.alc.local.enable\": true}";
  }

  // 出现不可恢复的错误时，会调动此接口
  virtual void appearUnrecoverableError() {
    std::cout << "appearUnrecoverableError" << std::endl;
  }
  /**
   ** IDC连接状态内容发送。此接口由SDK调用，SDK通过集成方设置的监听者实例来通知集成方IDC连接状态信息变更，通知方式为事件触发
   ** @param[in] connectionInfo
   *IDC连接状态信息，包括连接状态，断开状态及相关错误信息
   **/
  virtual void receiveIDCConnectionInfo(
      const IDCConnectionInfo& connectionInfo) {
    std::cout << "receiveIDCConnectionInfo state:" << connectionInfo.state
              << " code:" << connectionInfo.code << " message："
              << connectionInfo.message << std::endl;
  }

  /**
   *      *
   * 激活信息内容发送。此接口由SDK调用，SDK通过集成方设置的监听者实例来通知集成方激活信息变更，通知方式为事件触发。
   *           *
   *                * @param[in] info 激活信息，包括激活状态和激活范围
   *                     *
   *                          */
  virtual void receiveActivationInfo(const ActivationInfo& info) {
    std::cout << "receiveActivationInfo" << std::endl;
  }
};
// 接收MapAPI消息
// 定义一个class实现IMapAPIReceiver接口
class MapAPIReceiverImp : public IMapAPIReceiver {
public:
  MapAPIReceiverImp() {}

  virtual ~MapAPIReceiverImp() {}

public:
  /**
   *      * 接收静态数据删除信息接口
   *           * @param[in] cyclicCounter
   * 删除数据消息标识，每次播发+1，在重置后清0
   *                * @param[in] roadIds 拖尾删除的roadId
   *                     */
  virtual void receiveDeleteRoadIds(const uint32_t cyclicCounter,
                                    const std::vector<uint64_t>& roadIds) {
    std::cout << "receivedDeleteRoadIds cyclicCounter: " << cyclicCounter
              << std::endl;
    std::cout << "road_id list: ";
    for (auto road_id : roadIds) {
      std::cout << road_id << " ";
    }
    std::cout << std::endl;
  }

  /**
   *** 接收静态数据接口
   *
   ** @param[in] cyclicCounter 静态数据消息标识，每次播发+1，在重置后清0
   ** @param[in] roadPackages 静态Road数据
   *                     */
  virtual void receiveRoadPackages(
      const uint32_t cyclicCounter,
      const std::vector<RoadPackage>& roadPackages) {
    std::cout << "receiveRoadPackages cyclicCounter: " << cyclicCounter
              << std::endl;
    for (auto road : roadPackages) {
      std::cout << "road_id:" << road.roadId << std::endl;
      std::cout << "length:" << road.length << std::endl;
      std::cout << "direction:" << road.direction << std::endl;
      std::cout << "formWay:" << road.formWay << std::endl;
      std::cout << "angleF:" << road.angleF << std::endl;
      std::cout << "angleT:" << road.angleT << std::endl;
      std::cout << "linkType:" << road.linkType << std::endl;
      std::cout << "historyAverageSpeed:" << road.historyAverageSpeed
                << std::endl;
      std::cout << "hasToll:" << road.hasToll << std::endl;
      std::cout << "hasTrafficLight:" << road.hasTrafficLight << std::endl;
      std::cout << "plns:" << road.plns << std::endl;
      std::cout << "ownership:" << road.ownership << std::endl;
      std::cout << "regionCode:" << road.regionCode << std::endl;
      std::cout << "connectRoads.fromRoadIds.size():"
                << road.connectRoads.fromRoadIds.size() << std::endl;
      std::cout << "connectRoads.toRoadIds.size():"
                << road.connectRoads.toRoadIds.size() << std::endl;

      // std::vector<GEOMETRY> geometries;  ///< 道路形点
      // std::vector<PointCurvature> curvatures;  ///< 道路曲率
      // std::vector<uint8_t> arrowMarkings;  ///< 道路箭头信息
      // std::vector<Camera> cameras;  ///< 电子眼列表
      // std::vector<Facility> facilities;  ///< 道路设施列表
      // std::vector<SpeedLimitInfo> derivedSpeedLimitInfos;  ///<
      // 推导最大限速信息 ConnectRoads connectRoads;  ///<
      // 进入和退出节点关联的link std::vector<CheckPoint> checkPoints;  ///<
      // 检查站列表 std::vector<LongSolidLine> longSolidLines;  ///< 长实线
      // std::vector<BusLaneInfo> busLaneInfos;  ///< 公交车道信息
      // std::vector<BackLaneInfo> backLanes;  ///< 背景车道信息
      // std::vector<FrontLaneInfo> frontLanes;  ///< 前景车道信息
    }
  }

  /**
   ** 接收额外的导航信息接口
   **
   ** @param[in] cyclicCounter 导航路径消息标识，每次播发+1，在重置后清0
   ** @param[in] routeId 导航路径唯一标识
   ** @param[in] status  导航路径状态
   ** @param[in] route  导航路径详细信息
   **
   **/
  virtual void receiveNaviRoute(const uint32_t cyclicCounter,
                                const std::string& routeId, ROUTESTATUS status,
                                const OriginalRoute& route) {
    std::cout << "receiverNaviRoute cyclicCounter:" << cyclicCounter
              << " routeId: " << routeId << " status: " << status
              << "route.ids.size():" << route.ids.size() << std::endl;
  }

  /**
   ** 接收定位信号接口
   **
   ** @param[in] cyclicCounter 消息标识，每次播发+1
   ** @param[in] locInfos 定位点
   **
   **/
  virtual void receiveLocInfo(const uint32_t cyclicCounter,
                              const std::vector<OriginalLocInfo>& locInfos) {
    std::cout << "receiveLocInfo cyclicCounter:" << cyclicCounter
              << " locInfos:" << locInfos.size() << std::endl;
  }

  /**
   *** 接收动态数据接口
   ** @param[in] cyclicCounter 消息标识，每次播发+1，在重置后清0
   ** @param[in] dynamicDatas 动态信息数据集合
   **/
  virtual void receiveDynamicData(
      const uint32_t cyclicCounter,
      const std::vector<OriginalDynamicData>& dynamicDatas) {
    std::cout << "receiverDynamicData:" << cyclicCounter
              << " dynamicDatas.size(): " << dynamicDatas.size() << std::endl;
  }

  /**
   ** 接收删除动态数据接口
   ** @param[in] cyclicCounter 消息标识，每次播发+1，在重置后清0
   ** @param[in] dynamicDataIds 需要删除的动态信息数据ID集合
   **/
  virtual void receiveDeletedDynamicData(
      const uint32_t cyclicCounter,
      const std::vector<OriginalDynamicDataId>& dynamicDataIds) {
    std::cout << "receiveDeletedDynamicData cyclicCounter: " << cyclicCounter
              << " dynamicDataIds:" << dynamicDataIds.size() << std::endl;
  }
};
}  // namespace service
}  // namespace hdmap

void usage(const char* err = nullptr) {
  if (err) {
    std::cout << err << "\n\n";
  }

  std::cout << "Usage: demo <basicDataRootDir>\n"
            << "Requireds:\n"
            << "  basicDataRootDir: data root path (windows platform suggest "
               "use absolute path)\n"
            << "e.g.\n"
            << "  demo /data \n\n";
  exit(0);
}

void generateLocSignal(std::vector<LocSignDataT>& signDatas) {
  // 模拟定位信号，仅供参考。详细的定位信号信息以及信号要求请参考定位团队提供的说明文档
  LocSignDataT signData;
  signData.dataType = LocDataGnss_T;
  signData.gnss.isEncrypted = 1;
  signData.gnss.status = 'A';
  signData.gnss.isValid = true;
  signData.gnss.lon = 1165657080;
  signData.gnss.lat = 400329130;
  signData.gnss.sourType = 0;
  signData.gnss.course = 60;  //
  signData.gnss.speed = 120;
  signData.gnss.hdop = 0.9;
  signData.gnss.accuracy = 10;
  signData.gnss.mode = 1;
  signData.gnss.num = 9;
  signDatas.push_back(signData);

  signData.gnss.lon = 1165660372;
  signData.gnss.lat = 400330712;
  signDatas.push_back(signData);
}

// main函数
int main(int argc, char* argv[]) {
  if (argc < ARGC_MIN_COUNT) {
    usage("Invalid mode found!");
    return 0;
  }
  // 注意: 初始化时序要求，GHDMainService必须初始化完成之后，
  //       各个功能模块GHDXXXService才能使用；

  // 设置数据路径
  hdmap::service::HDMapConfig config;
  config.basicDataRootDir = argv[1];  // SDK运行根目录

  // 设置系统接口实现
  hdmap::service::SystemDeviceImp* deviceReceiver =
      new hdmap::service::SystemDeviceImp();
  if (!deviceReceiver) {
    return 0;
  }

  // 设置诊断处理对象实例（非必须）
  hdmap::service::DiagnosisImp* diagnosisReceiver =
      new hdmap::service::DiagnosisImp();
  if (!diagnosisReceiver) {
    return 0;
  }

  // 设置地图数据更新处理对象实例（非必须），只适用于HD数据，不适用于HQ数据
  hdmap::service::IDOTAReceiver* dotaReceiver = nullptr;

  // GHDMainService初始化
  if (!hdmap::service::GHDMainService::init(config, deviceReceiver,
                                            diagnosisReceiver, dotaReceiver)) {
    printf("GHDMainService init error\n");
    hdmap::service::GHDMainService::deInit();  // init和deInit方法要成对出现
    return 1;
  } else {
    printf("GHDMainService init success\n");
  }

  // 示例代码，主动调用一次网络状态更新
  hdmap::service::GHDDeviceService* deviceService =
      hdmap::service::GHDDeviceService::getService();
  if (deviceService) {
    deviceService->updateNetworkState(hdmap::service::NETWORK_STATE_4G);
  }

  // 设置MapAPI message 接收对象实例
  hdmap::service::MapAPIReceiverImp* mapapiReceiver =
      new hdmap::service::MapAPIReceiverImp();
  if (!mapapiReceiver) {
    return 0;
  }
  hdmap::service::GHDMapAPIService* service =
      hdmap::service::GHDMapAPIService::getService();
  if (service) {
    // MapAPIService启动
    service->start(mapapiReceiver);
  }

  // 定频输入定位点信息，驱动SDK工作，就可以正常收到有效的消息，仅做demo演示
  std::vector<LocSignDataT> signDatas;
  generateLocSignal(signDatas);
  int i = 0;
  while (++i < 100) {
    // 模拟定位信号，仅供参考。详细的定位信号信息以及信号要求请参考定位团队提供的说明文档
    for (auto& signData : signDatas) {
      signData.gnss.tickTime = i * 100;
      hdmap::service::GHDMainService::setLocSignal(signData);
      sleep_this_thread(100);
    }
  }
  // 为了保证demo场景能接收到SDK播发的消息，增加了sleep，实际并不需要
  sleep_this_thread(3000);
  std::cout << "finished sleep time: " << 3000 << std::endl;

  hdmap::service::GHDMainService::deInit();

  delete mapapiReceiver;
  mapapiReceiver = nullptr;
  delete diagnosisReceiver;
  diagnosisReceiver = nullptr;
  delete deviceReceiver;
  deviceReceiver = nullptr;
  return 0;
}
