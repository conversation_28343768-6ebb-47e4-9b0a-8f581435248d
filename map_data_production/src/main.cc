// Copyright 2023 Tencent Inc. All rights reserved.

#include <cstdint>
#include <fstream>
#include <future>
#include <iostream>
#include <memory>
#include <sstream>
#include <string>

#include "api/map_data_engine_api.h"
#include "api/nerd_utils.h"
#include "nerd_api/nerd_version.h"
#include "proto/sd_map.pb.h"
#include "util/log.hpp"
//#include "proto/amap_navi_interface.pb.h"

//
class TileCallback : public nerd::api::MapDataResultCallback {
public:
  TileCallback() : f_(p_.get_future()) {}

  void OnResult(
      const nerd::api::RetMessage &ret, nerd::api::JobId job_id,
      std::unique_ptr<nerd::api::GetMapDataBatchParam> req,
      std::unique_ptr<nerd::api::GetMapDataBatchResult> rsp) override {
    LOG_INFO << "ret.msg: " << ret.msg << ", ret.ret: " << ret.ret << std::endl;
    if (rsp->data_cubes.empty()) {
      LOG_INFO << "rsp->data_cubes.empty()" << std::endl;
      LOG_INFO << "rsp->ret.msg: " << rsp->ret.msg
               << ", rsp->ret.ret: " << rsp->ret.ret << std::endl;
    }
    resp_ = std::move(rsp);
    p_.set_value();
  }

  nerd::api::GetMapDataBatchResult *GetResult() const { return resp_.get(); }

  void OnCanceled(nerd::api::JobId job_id, bool success,
                  const nerd::api::RetMessage &ret) override {
    p_.set_value();
  }

  void Wait() { f_.wait(); }

private:
  // 仅示例用, 可以采取更合适的同步方式
  std::promise<void> p_;
  std::future<void> f_;
  std::unique_ptr<nerd::api::GetMapDataBatchResult> resp_;
};

std::set<nerd::api::TileIDType> get_tile_ids(std::string txt_file) {
  std::set<nerd::api::TileIDType> tile_ids;
  std::stringstream ss;
  ss << "./tile_ids/" << txt_file;
  std::string file_path = ss.str();
  std::ifstream in_file(file_path);

  std::string line;
  while (std::getline(in_file, line)) {
    if (line.empty()) {
      continue;
    }
    tile_ids.insert(static_cast<nerd::api::TileIDType>(std::stoll(line)));
  }
  return tile_ids;
}

class RectCallback : public nerd::api::MapDataByRectResultCallback {
public:
  RectCallback() : f_(p_.get_future()) {}

  void OnResult(
      const nerd::api::RetMessage &ret, nerd::api::JobId job_id,
      std::unique_ptr<nerd::api::GetMapDataByRectParam> req,
      std::unique_ptr<nerd::api::GetMapDataByRectResult> rsp) override {
    LOG_INFO << "ret.msg: " << ret.msg << ", ret.ret: " << ret.ret << std::endl;
    resp_ = std::move(rsp);
    p_.set_value();
  }

  nerd::api::GetMapDataByRectResult *GetResult() const { return resp_.get(); }

  void OnCanceled(nerd::api::JobId job_id, bool success,
                  const nerd::api::RetMessage &ret) override {
    p_.set_value();
  }

  void Wait() { f_.wait(); }

private:
  // 仅示例用, 可以采取更合适的同步方式
  std::promise<void> p_;
  std::future<void> f_;
  std::unique_ptr<nerd::api::GetMapDataByRectResult> resp_;
};

void getAroundTiles(nerd::api::TileIDType id);

void getSDMapElement(nerd::api::ISDLinkLayer *sdLinkLayer) {
  if (sdLinkLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    LOG_INFO << "sdLinkLayer is nullptr " << std::endl;
    return;
  }
  auto links = sdLinkLayer->GetLinkArr();
  LOG_INFO << "sdLinkLayer->GetLinkArr().size(): " << links.size() << std::endl;
  for (const auto &link : links) {
    LOG_INFO << "link id: " << link->GetID().ToString() << std::endl;
    const auto &attr = link->GetAttribute();
    std::shared_ptr<std::vector<nerd::api::Coordinate>> Coordinates =
        link->GetGeometry();
    auto restrictions = link->GetRestrictions();

    //获取link的拓扑
    //获取后继link列表（link列表不包含路口内link），该接口只能获取到本tile内的后继link
    //如果后继link有多条，其中有几条在另外的tile中，那么该接口只能获取到本tile内的后继link列表；如果后继link只有一条，并且这条后继link在另外的tile中，那么该接口返回空列表
    auto nextLinks = link->GetNext(attr.direction, false);
    //获取后继link的id列表（linkid列表不包含路口内linkid），若后继link在另外一个tile中，那么GetNextIDs是可以拿到后继linkid的
    auto nextLinkIds = link->GetNextIDs(attr.direction, false);

    //获取后继link列表（link列表包含路口内link），该接口只能获取到本tile内的后继link
    //如果后继link有多条，其中有几条在另外的tile中，那么该接口只能获取到本tile内的后继link列表；如果后继link只有一条，并且这条后继link在另外的tile中，那么该接口返回空列表
    auto nextLinksWithinIntersection =
        link->GetNextWithinIntersection(attr.direction, false);
    //获取后继link的id列表（linkid列表包含路口内linkid），若后继link在另外一个tile中，那么GetNextIDsWithinIntersection是可以拿到后继linkid的
    auto nextLinkIdsWithinIntersection =
        link->GetNextIDsWithinIntersection(attr.direction, false);

    //获取link的node-方法一（推荐）
    auto startNodeId = link->GetStartNodeID();
    auto startNode = sdLinkLayer->GetLinkNodeByID(startNodeId);

    auto endNodeId = link->GetEndNodeID();
    auto endNode = sdLinkLayer->GetLinkNodeByID(endNodeId);

    //获取link的node-方法二（不推荐）
    //不推荐的原因是link的node是弱引用，有的时候拿到的结果是空的
    auto startNodeV2 = link->GetStartNode();
    auto endNodeV2 = link->GetEndNode();

    // 车信信息
    auto laneInfos = link->GetGuidance(nerd::api::GuidanceType::kLaneMetaInfo);
    if (!laneInfos.empty()) {
      LOG_INFO << "laneInfo.size: " << laneInfos.size() << std::endl;
    }
    for (auto laneInfoPtr : laneInfos) {
      auto laneInfo =
          std::static_pointer_cast<const nerd::api::LaneInfoMeta>(laneInfoPtr);
      if (laneInfo == nullptr) {
        continue;
      }
      auto nodeid = laneInfo->GetNodeID();  // 进入节点id
      for (const auto &topo : laneInfo->lane_topos) {
        auto arrow = topo.lane_arrow;         // 转向箭头
        auto outLinkIds = topo.out_link_ids;  // 退出线号
      }
    }
    // 收费站
    auto guidancesToll =
        link->GetGuidance(nerd::api::GuidanceType::kTollStation);
    if (!guidancesToll.empty()) {
      LOG_INFO << "guidancesToll.size: " << guidancesToll.size() << std::endl;
    }
    for (const auto &guidance : guidancesToll) {
      auto toll_station =
          std::static_pointer_cast<const nerd::api::TollStation>(guidance);
      if (toll_station == nullptr) {
        continue;
      }
      auto name = toll_station->name;
      auto type = toll_station->GetType();
      auto toll_means = toll_station->toll_means;
      auto direction = toll_station->direction;
    }
    // 点限速
    auto guidancesSpeed =
        link->GetGuidance(nerd::api::GuidanceType::kPointSpeed);
    if (!guidancesSpeed.empty()) {
      LOG_INFO << "guidancesSpeed.size: " << guidancesSpeed.size() << std::endl;
    }
    for (const auto &guidance : guidancesSpeed) {
      auto point_speed =
          std::static_pointer_cast<const nerd::api::PointSpeed>(guidance);
      if (point_speed == nullptr) {
        continue;
      }
      auto speed_type = point_speed->type;
      auto position = point_speed->coordinate;
      auto speed_limit = point_speed->speed_limit;
      auto affect_link_id = point_speed->affect_link_id;
      auto special_times = point_speed->special_times;
      auto vehicles = point_speed->vehicles;
      auto speed_scenes = point_speed->speed_scenes;
    }
    // 长实线
    auto nodeId = link->GetStartNodeID();
    auto node = sdLinkLayer->GetLinkNodeByID(nodeId);
    if (node == nullptr) {
      continue;
    }
    auto guidancesLong = node->GetGuidance(nerd::api::GuidanceType::kLsl);
    if (!guidancesLong.empty()) {
      LOG_INFO << "guidancesLong.size: " << guidancesLong.size() << std::endl;
    }
    for (const auto &guidance : guidancesLong) {
      auto lsl = std::static_pointer_cast<const nerd::api::LSL>(guidance);
      if (lsl == nullptr) {
        continue;
      }
      auto type = lsl->lsl_type;
      auto exit_links = lsl->GetPassLinkIDS();
      auto lsl_lanes = lsl->lsl_lanes;
    }

    //交通警示
    auto guidanceWarningSign =
        link->GetGuidance(nerd::api::GuidanceType::kWarningSign);
    if (!guidanceWarningSign.empty()) {
      LOG_INFO << "guidanceWarningSign.size: " << guidanceWarningSign.size()
               << std::endl;
    }
    for (const auto &guidance : guidanceWarningSign) {
      auto warningSign =
          std::static_pointer_cast<const nerd::api::LinkWarningSign>(guidance);
      auto trick_kind = warningSign->trc_kind;
      auto direction = warningSign->linkDirection;
      auto coord = warningSign->coordinate;
    }

    // Zlevel
    auto guidanceZlevel =
        link->GetGuidance(nerd::api::GuidanceType::kZLevelGroup);
    if (!guidanceZlevel.empty()) {
      LOG_INFO << "guidanceZlevel.size: " << guidanceZlevel.size() << std::endl;
    }
    for (const auto &guiance : guidanceZlevel) {
      auto zlevel =
          std::static_pointer_cast<const nerd::api::ZLevelGroup>(guiance);
      auto links = zlevel->links;
      for (auto link : links) {
        auto linkid = link.link_id;
        auto zValue = link.z;
      }
      auto coord = zlevel->coordinate;
    }

    //电子眼
    auto guidanceCamera = link->GetGuidance(nerd::api::GuidanceType::kCamera);
    if (!guidanceCamera.empty()) {
      LOG_INFO << "guidanceCamera.size: " << guidanceCamera.size() << std::endl;
    }
    for (const auto &guidance : guidanceCamera) {
      auto camera = std::static_pointer_cast<const nerd::api::Camera>(guidance);
      auto type = camera->type;
      auto coord = camera->coordinate;
      auto direction = camera->direction;
      auto shootPos = camera->position;
    }
  }
}

void getHDAirData(nerd::api::ISDLinkLayer *sdLinkLayer) {
  if (sdLinkLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    LOG_INFO << "sdLinkLayer is nullptr " << std::endl;
    return;
  }
  auto links = sdLinkLayer->GetLinkArr();
  LOG_INFO << "sdLinkLayer->GetLinkArr().size(): " << links.size() << std::endl;
  for (const auto &link : links) {
    auto stopLineIds = sdLinkLayer->GetStopLineIDsByLinkId(link->GetID());
    for (auto stopLineId : stopLineIds) {
      auto stopLine = sdLinkLayer->GetStopLineById(stopLineId);
      LOG_INFO << " stop line id on this link: linkId:"
               << link->GetID().ToString()
               << ", stopLineId:" << stopLine->GetID().ToString() << std::endl;
    }

    auto featurePointIDs =
        sdLinkLayer->GetFeaturePointIDsByLinkId(link->GetID());
    for (auto &pointId : featurePointIDs) {
      auto point = sdLinkLayer->GetFeaturePointById(pointId);
      LOG_INFO << " feature point id on this link: linkId="
               << link->GetID().ToString()
               << ", pointId=" << point->GetID().ToString() << std::endl;
    }

    // link 覆盖标签
    auto &data_coverage_status = link->GetAttribute().data_coverage_status;
    if (data_coverage_status.value != 0) {
      LOG_INFO << " linkId " << link->GetID().ToString()
               << " link_data_coverage_status: "
               << (uint32_t)data_coverage_status.value << std::endl;
    }

    // link 几何矢量方向归一化标记
    auto geo_direction_normalization =
        link->GetAttribute().geo_direction_normalization;
    if (data_coverage_status.value != 0) {
      LOG_INFO << " linkId " << link->GetID().ToString()
               << " geo_direction_normalization: "
               << geo_direction_normalization << std::endl;
    }

    // link zebra ids
    auto zebraIdsInLink = sdLinkLayer->GetZebraIDsByLinkId(link->GetID());
    for (auto zebraId : zebraIdsInLink) {
      auto zebra = sdLinkLayer->GetZebraById(zebraId);
      LOG_INFO << "linkId=" << link->GetID().ToString()
               << ", zebraId=" << zebra->GetID().ToString() << std::endl;
    }

    // link LinkID关联的IntersectionRoadID列表
    auto intersectionRoadIDs =
        sdLinkLayer->GetIntersectionRoadIDsByLinkId(link->GetID());
    for (auto intersectionRoadId : intersectionRoadIDs) {
      LOG_INFO << "linkId=" << link->GetID().ToString()
               << ", intersectionRoadId=" << intersectionRoadId.ToString()
               << std::endl;
    }
  }
  LOG_INFO << "================featurePointIDs=================" << std::endl;
  auto featurePointIDs = sdLinkLayer->GetFeaturePointIDs();
  LOG_INFO << "featurePointIDs.size=" << featurePointIDs.size() << std::endl;
  for (auto featurePointId : featurePointIDs) {
    auto featurePoint = sdLinkLayer->GetFeaturePointById(featurePointId);
    LOG_INFO << "feature point id:" << featurePoint->GetID().ToString()
             << std::endl;

    // FP GetIntersectionRoadIDsByFpId
    auto intersectionRoadIds =
        sdLinkLayer->GetIntersectionRoadIDsByFpId(featurePointId);
    for (auto intersectionRoadId : intersectionRoadIds) {
      auto intersectionRoad =
          sdLinkLayer->GetIntersectionRoadByID(intersectionRoadId);
      LOG_INFO << "feature point id:" << featurePoint->GetID().ToString()
               << " intersectionRoadId: " << intersectionRoadId.ToString()
               << " intersectionRoadObjId: "
               << intersectionRoad->GetRoadID().ToString() << std::endl;
    }

    // FP GetLaneLslType
    auto fpLanes = featurePoint->GetLanes();
    for (auto &lane : fpLanes) {
      auto lslType = lane->GetLaneLslType();
      LOG_INFO << "feature point id:" << featurePoint->GetID().ToString()
               << " lane id: " << lane->GetID().ToString()
               << " lslType: " << (int)lslType << std::endl;
    }
  }

  LOG_INFO << "================stopLineIds=================" << std::endl;
  auto stopLineIds = sdLinkLayer->GetStopLineIDs();
  LOG_INFO << "stopLineIds.size=" << stopLineIds.size() << std::endl;
  for (auto stopLineId : stopLineIds) {
    auto stopLine = sdLinkLayer->GetStopLineById(stopLineId);
    LOG_INFO << "stopLine id:" << stopLine->GetID().ToString() << std::endl;

    if (stopLine->GetStopLineType() ==
            nerd::api::hdair::StopLineType::SLT_StraightWaitingAreaStopLine ||
        stopLine->GetStopLineType() ==
            nerd::api::hdair::StopLineType::SLT_LeftTurnWaitingAreaStopLine ||
        stopLine->GetStopLineType() ==
            nerd::api::hdair::StopLineType::SLT_RightTurnWaitingAreaStopLine ||
        stopLine->GetStopLineType() ==
            nerd::api::hdair::StopLineType::SLT_TurnRoundWaitingAreaStopLine) {
      auto stIntersectionRoadIds = stopLine->GetRelatedIntersectionRoadIDs();
      LOG_INFO << "debug_waiting_stopline_id: " << stopLine->GetID().ToString()
               << " stopLineType: " << (int)stopLine->GetStopLineType()
               << " stIntersectionRoadIds.size: "
               << stIntersectionRoadIds.size() << std::endl;
    }

    // GetRelatedIntersectionRoadIDs
    auto intersectionRoadIds = stopLine->GetRelatedIntersectionRoadIDs();
    for (auto intersectionRoadId : intersectionRoadIds) {
      LOG_INFO << "stopLine id:" << stopLine->GetID().ToString()
               << " intersectionRoadId: " << intersectionRoadId.ToString()
               << std::endl;
    }
  }

  // IZebra in sdLinkLayer
  auto zebraIdsInLinkLayer = sdLinkLayer->GetZebraIDs();
  for (auto zebraId : zebraIdsInLinkLayer) {
    auto zebra = sdLinkLayer->GetZebraById(zebraId);
    LOG_INFO << "zebraId=" << zebra->GetID().ToString() << std::endl;

    auto zebraGetDirection = zebra->GetDirection();
    auto zebraLocation = zebra->GetLocation();
    auto zebraRelatedLinkID = zebra->GetRelatedLinkID();
    auto zebraRelatedNodeID = zebra->GetRelatedNodeID();
    LOG_INFO << "zebraId=" << zebra->GetID().ToString()
             << ", zebraGetDirection=" << (int)zebraGetDirection
             << ", zebraLocation=" << zebraLocation
             << ", zebraRelatedLinkID=" << zebraRelatedLinkID.ToString()
             << ", zebraRelatedNodeID=" << zebraRelatedNodeID.ToString()
             << std::endl;
  }

  // 路口内虚拟道路
  auto intersectionRoads = sdLinkLayer->GetIntersectionRoadArr();
  for (const auto &intersectionRoad : intersectionRoads) {
    auto intersectionRoadId = intersectionRoad->GetRoadID();
    auto relatedLinkId = intersectionRoad->GetRelatedLinkID();
    auto direction = intersectionRoad->GetDirection();
    auto crossTurnType = intersectionRoad->GetCrossTurnType();
    auto startNodeID = intersectionRoad->GetStartNodeID();
    auto endNodeID = intersectionRoad->GetEndNodeID();
    auto startFPID = intersectionRoad->GetStartFeaturePointID();
    auto endFPID = intersectionRoad->GetEndFeaturePointID();
    auto preIds =
        intersectionRoad->GetPreviousIDs(direction);  // 业务按需传入方向
    auto nextIds = intersectionRoad->GetNextIDs(direction);  // 业务按需传入方向
    auto trafficLightFlag = intersectionRoad->GetTrafficLightFlag();
    auto geometry = intersectionRoad->GetGeometry();
    auto weakRefStartNode = intersectionRoad->GetStartNode();  // 弱引用
    auto weakRefEndNode = intersectionRoad->GetEndNode();      // 弱引用
    LOG_INFO << "intersectionRoadId=" << intersectionRoadId.ToString()
             << ", relatedLinkId=" << relatedLinkId.ToString()
             << ", direction=" << (int)direction
             << ", crossTurnType=" << (int)crossTurnType
             << ", startNodeID=" << startNodeID.ToString()
             << ", endNodeID=" << endNodeID.ToString()
             << ", startFPID=" << startFPID.ToString()
             << ", endFPID=" << endFPID.ToString()
             << ", preIds.size=" << preIds.size()
             << ", nextIds.size=" << nextIds.size()
             << ", trafficLightFlag=" << trafficLightFlag
             << ", geometry.size=" << geometry->size() << std::endl;

    auto intersectionRoadNode =
        sdLinkLayer->GetIntersectionRoadNodeByID(startNodeID);
    if (intersectionRoadNode) {
      auto intersectionRoadNodeID = intersectionRoadNode->GetNodeID();
      auto intersectionRoadNodePosition = intersectionRoadNode->GetPosition();
      auto enterIntersectionRoadIDs =
          intersectionRoadNode->GetEnterIntersectionRoadIDs();
      auto outIntersectionRoadIDs =
          intersectionRoadNode->GetOutIntersectionRoadIDs();
      LOG_INFO << "intersectionRoadNodeID=" << intersectionRoadNodeID.ToString()
               << ", nodePostion=" << intersectionRoadNodePosition.ToString()
               << ", enterIntersectionRoadIDs.size="
               << enterIntersectionRoadIDs.size()
               << ", outIntersectionRoadIDs.size="
               << outIntersectionRoadIDs.size() << std::endl;
    }
  }
}

void getHDMapElement(nerd::api::IHDLaneLayer *hdLayer) {
  if (hdLayer ==
      nullptr) {  //此处必须判断数据层是否为空，因为有的tile里面没有路网
    LOG_INFO << "hdLayer is nullptr" << std::endl;
    return;
  }
  auto lanes = hdLayer->GetLaneArr();
  LOG_INFO << "lanes.size = " << lanes.size() << std::endl;
  auto crossAreas = hdLayer->GetCrossAreaArr();
  LOG_INFO << "crossAreas.size = " << crossAreas.size() << std::endl;
  auto laneGroups = hdLayer->GetLaneGroupArr();
  LOG_INFO << "laneGroups.size = " << laneGroups.size() << std::endl;
}

void getSDMapAndHDAirDataByRect(
    const std::unique_ptr<nerd::api::MapDataEngineApi> &route_api) {
  //构造矩形框范围
  nerd::api::GeoCoordinate point{121.499721, 31.239846};
  nerd::api::Rect rect = nerd::api::utils::BuildRectByLength(point, 500);

  nerd::api::GetMapDataByRectParam l_param;
  l_param.level =
      nerd::api::MapLevel::kLEVEL_13;  //获取SD数据，地图缩放层级13层
  l_param.rect = rect;
  auto route_rec_callback = std::make_shared<RectCallback>();
  route_api->AsyncGetDataByRect(l_param, route_rec_callback);
  // 等待结果
  route_rec_callback->Wait();

  auto result = route_rec_callback->GetResult();
  if (result->data_cube != nullptr) {
    auto sdLinkLayer = result->data_cube->GetSDLinkLayer();
    getSDMapElement(sdLinkLayer);
    getHDAirData(sdLinkLayer);
  }
}

void getSDMapAndHDAirDataByTileID(
    const std::unique_ptr<nerd::api::MapDataEngineApi> &route_api,
    nerd::api::TileIDType tileId) {
  LOG_INFO << "route_tile_id: " << tileId << std::endl;
  nerd::api::GetMapDataBatchParam route_batch_param;
  route_batch_param.tile_id_list.insert(tileId);
  auto route_tile_callback = std::make_shared<TileCallback>();
  route_api->AsyncGetDataByTileId(route_batch_param, route_tile_callback);
  // 等待结果
  route_tile_callback->Wait();

  auto result = route_tile_callback->GetResult();
  auto dataCubes = result->data_cubes;
  for (auto datacube : dataCubes) {
    auto sdLinkLayer = datacube->GetSDLinkLayer();
    getSDMapElement(sdLinkLayer);
    getHDAirData(sdLinkLayer);
  }
}

void getHDMapDataByRect(
    const std::unique_ptr<nerd::api::MapDataEngineApi> &lane_api) {
  //构造矩形框范围
  nerd::api::GeoCoordinate point{121.499721, 31.239846};
  nerd::api::Rect rect = nerd::api::utils::BuildRectByLength(point, 500);

  nerd::api::GetMapDataByRectParam l_param;
  l_param.level =
      nerd::api::MapLevel::kLEVEL_15;  //获取HD数据，地图缩放层级15层
  l_param.rect = rect;
  auto lane_rec_callback = std::make_shared<RectCallback>();
  lane_api->AsyncGetDataByRect(l_param, lane_rec_callback);
  // 等待结果
  lane_rec_callback->Wait();

  auto result = lane_rec_callback->GetResult();
  if (result->data_cube != nullptr) {
    auto hdLayer = result->data_cube->GetHDLaneLayer();
    getHDMapElement(hdLayer);
  }
}

void getHDMapDataByTileID(
    const std::unique_ptr<nerd::api::MapDataEngineApi> &lane_api,
    nerd::api::TileIDType tile_id) {
  LOG_INFO << "lane_tile_id: " << tile_id << std::endl;
  // 异步获取tile数据
  nerd::api::GetMapDataBatchParam batch_param;
  batch_param.tile_id_list.insert(tile_id);
  auto lane_tile_callback = std::make_shared<TileCallback>();
  lane_api->AsyncGetDataByTileId(batch_param, lane_tile_callback);
  // 等待结果
  lane_tile_callback->Wait();
  auto result = lane_tile_callback->GetResult();
  auto dataCubes = result->data_cubes;
  for (auto datacube : dataCubes) {
    auto hdLayer = datacube->GetHDLaneLayer();
    getHDMapElement(hdLayer);
  }
};

void getSDMapAndHDAirData(
    const std::unique_ptr<nerd::api::MapDataEngineApi> &route_api,
    nerd::api::TileIDType tileId) {
  getSDMapAndHDAirDataByTileID(route_api, tileId);  //通过tile ID获取SD数据
  getSDMapAndHDAirDataByRect(route_api);  //通过矩形框获取对应范围内的SD数据
  getAroundTiles(tileId);                 //获取tile周边tile id列表
}

void getHDMapData(const std::unique_ptr<nerd::api::MapDataEngineApi> &lane_api,
                  nerd::api::TileIDType tileId) {
  getHDMapDataByTileID(lane_api, tileId);  //通过tile ID获取HD数据
  getHDMapDataByRect(lane_api);  //通过矩形框获取对应范围内的HD数据
}

void getAroundTiles(nerd::api::TileIDType id) {
  LOG_INFO << "tile id: " << id << std::endl;
  // test extend_num <= 3
  for (int extend_num = -1; extend_num <= 3; extend_num++) {
    LOG_INFO << "  extend_num: " << extend_num << std::endl;
    auto aroundTiles = nerd::api::GetAroundTiles(id, extend_num);
    LOG_INFO << "  aroundTiles.size: " << aroundTiles.size() << std::endl;
    for (auto tile_tuple : aroundTiles) {
      auto tile_id = std::get<0>(tile_tuple);
      auto tile_row = std::get<1>(tile_tuple);
      auto tile_col = std::get<2>(tile_tuple);
      LOG_INFO << "    around tile id: " << tile_id << ", row: " << tile_row
               << ", col: " << tile_col << std::endl;
    }
  }
}

// 只检查文件LANE.NDS
int main(int argc, char **argv) {
  //  std::set<nerd::api::TileIDType> lane_tile_ids = get_tile_ids("lane.txt");
  //  std::set<nerd::api::TileIDType> route_tile_ids =
  //  get_tile_ids("route.txt"); LOG_INFO << "FIND_TILE_ID_LANE_COUNT:" <<
  //  lane_tile_ids.size() << std::endl; LOG_INFO <<
  //  "FIND_TILE_ID_ROUTE_COUNT:" << route_tile_ids.size() << std::endl;

  // 初始化log，路径相对于可执行程序而言,当然也可以是绝对路径
  nerd::api::InitLogger("./log", false, "tencent_nerd_" /*日志文件名前缀*/,
                        std::chrono::hours(24 * 30) /*日志存储时间限制*/,
                        200 * 1024 * 1024 /*日志存储大小的最大限制*/,
                        5 * 1024 * 1024 /*单个日志文件大小限制*/);

  // 先调用InitGlobalConfig, 只需要调用一次
  nerd::api::GlobalConfig global_config;
  // 磁盘缓存路径
  global_config.data_root_path =
      "./data";  //路径相对于可执行程序而言,当然也可以是绝对路径
  // 版本文件路径
  global_config.version_file_path =
      global_config
          .data_root_path;  //路径相对于可执行程序而言,当然也可以是绝对路径
  // global_config.offline_debug =
  // false;//是否开启离线调试模式，默认false。设置为true时，offline_city_code_mode无效，使用全国NDS大文件。
  global_config.offline_debug =
      true;  //是否开启离线调试模式，默认false。设置为true时，offline_city_code_mode无效，使用全国NDS大文件。
  global_config.offline_city_code_mode =
      false;  // 离线模式, false:使用9x9级Tile九宫格模式  true:使用CityCode模式
  global_config.channel =
      "POC";  //这里非常重要，需要注入腾讯专门提供的渠道号，若没有填写正确的渠道号将无法获取在线数据
  global_config.deviceId =
      "testDeviceId";  // TODO
                       // 此处需要填写每台设备的真实唯一的Id，长度不能超过32，否则会报鉴权失败
  nerd::api::InitGlobalConfig(global_config);

  LOG_INFO << "sdk detail version=" << nerd::GetVersionDetail(true)
           << std::endl;

  // 创建各个building block对应的引擎
  static auto create_engine =
      [](nerd::api::MapDataBuildingBlockID build_block_id)
      -> std::unique_ptr<nerd::api::MapDataEngineApi> {
    nerd::api::APIConfig api_config;
    api_config.user_id = "building_block:" +
                         std::to_string(static_cast<int32_t>(build_block_id));
    api_config.build_block_id = build_block_id;
    api_config.preference = nerd::api::DataPreference::
        kOfflineOnly;  //这里只推荐在线模式，或者离线模式，其他的在线优先等模式可能存在数据版本不一致的问题
    return nerd::api::CreateEngine(api_config);
  };

  auto lane_api = create_engine(
      nerd::api::MapDataBuildingBlockID::kLANE);  //创建获取HD数据的引擎实例
  auto route_api = create_engine(
      nerd::api::MapDataBuildingBlockID::kROUTE);  //创建获取SD数据的引擎实例
  assert(lane_api && route_api);

  nerd::api::GeoCoordinate point{121.499721, 31.239846};

  std::vector<nerd::api::TileIDType>
      allRouteTileIds;  //离线NDS数据中包含的所有tile ID
  //读取离线NDS数据中包含的所有tile id，后面可以通过这里的tile id 读取tile 数据
  route_api->GetAllTileIDs(
      allRouteTileIds);  //仅存在离线NDS数据和 offline_debug 为true时有效

  //获取 SD 和 HDAir 数据样例
  auto route_tile_id = nerd::api::utils::GetTileIdByGeoCoordinate(point, 13);
  getSDMapAndHDAirData(route_api, route_tile_id);

  std::vector<nerd::api::TileIDType>
      allLaneTileIds;  //离线NDS数据中包含的所有tile ID
  //读取离线NDS数据中包含的所有tile id，后面可以通过这里的tile id 读取tile 数据
  lane_api->GetAllTileIDs(
      allLaneTileIds);  //仅存在离线NDS数据和 offline_debug 为true时有效

  //获取HD数据样例
  auto lane_tile_id = nerd::api::utils::GetTileIdByGeoCoordinate(point, 15);
  getHDMapData(lane_api, lane_tile_id);
  return 0;
}
