set(TCMAP_FOUND TRUE)
set(TCMAP_DIR ${THIRD_PARTY_DIR}/tencent/x86)
set(TCMAP_INCLUDE_DIR ${TCMAP_DIR}/include)
file(GLOB_RECURSE TCMAP_LIBRARIES  ${TCMAP_DIR}/lib/lib*.so*)

set(TENCENT_SDK_DIR "${TCMAP_DIR}")

macro(nerd_api_target_deps target)
	target_include_directories(${target} PRIVATE 
                             ${TENCENT_SDK_DIR}/include/nerd-api
                             ${TENCENT_SDK_DIR}/include/map-base 
                             ${TENCENT_SDK_DIR}/include/
                             ${TENCENT_SDK_DIR}/include/cloud-atlas)

  target_link_directories(${target} PRIVATE ${TENCENT_SDK_DIR}/lib)
  target_link_libraries(${target} PRIVATE 
                        tx_cloud_atlas 
                        crbase 
                        nerdapi
                        authMgr
                        mapbase 
                        crbase 
                        pthread
                        plog 
                        curl
                        z)
endmacro(nerd_api_target_deps)


