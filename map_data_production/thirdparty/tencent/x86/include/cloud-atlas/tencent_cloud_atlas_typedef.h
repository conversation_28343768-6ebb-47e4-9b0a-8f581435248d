// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by gangjin on 2024/5/9.
//

#ifndef TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_TYPEDEF_H_
#define TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_TYPEDEF_H_

#include <map>
#include <sstream>
#include <string>
#include <vector>

#define BEGIN_NAMESPACE_TCA namespace tca {
#define END_NAMESPACE_TCA }

/* ***************************************************************************
 *
 *  异源匹配V2.0
 *
 * ***************************************************************************/

BEGIN_NAMESPACE_TCA

enum TargetType {
  /* SD/HDAir异源匹配数据 */
  SD = 0,
  /* HD异源匹配数据 */
  HD = 1
};

struct Point {
  /* 经度， GCJ02坐标系, 放大 10^7 倍 */
  uint32_t lng;
  /* 纬度， GCJ02坐标系, 放大 10^7 倍 */
  uint32_t lat;

  Point() : lng(0), lat(0) {}

  void Reset() {
    lng = 0;
    lat = 0;
  }
};

struct OriginalLink {
  /* 道路长度，单位米 */
  int32_t length;
  /* 道路类型 0-普通道路 1-航道 2-隧道 3-桥梁 4-高架桥 */
  int32_t linkType;
  /* Link道路等级
   * 0 高速公路
   * 1 国道
   * 2 省道
   * 3 县道
   * 4 乡公路
   * 5 县乡村内部道路
   * 6 主要大街、城市快速道
   * 7 主要道路
   * 8 次要道路
   * 9 普通道路
   * 10 非导航道路 */
  int32_t roadClass;
  /* Link道路类型
   * 1 主路
   * 2 路口内部道路
   * 3 JCT道路
   * 4 环岛
   * 5 服务区
   * 6 匝道
   * 7 辅路
   * 8 匝道与JCT
   * 9 出口
   * 10 入口
   * 11 A类右转专用道
   * 12 B类右转专用道
   * 13 A类左转专用道
   * 14 B类左转专用道
   * 15 普通道路
   * 16 左右转专用道
   * 56 服务区与匝道
   * 53 服务区与JCT
   * 58 服务区与匝道以及JCT */
  int32_t roadType;
  /* 道路名 */
  std::string roadName;
  /* 当前link在路线中形点起始位置 */
  int32_t coor_idx;
  /* 当前link在路线中形点数量 */
  int32_t coor_num;
  /* 当前link是否双方向,如果双方向,lane_num无效 */
  bool is_both_direction;
  /* 当前link的车道数 */
  int32_t lane_num;
  /* 当前link的车道数是否包含应急车道 */
  bool is_emergency_lane;
  /* 当前link段是否有岔路 0:未知,1:是,2:否 */
  int32_t has_multi_out;
  /* 当前link段是否有平行路 0:未知,1:是,2:否 */
  int32_t has_parallel_link;
  /* link沿行车方向终点是否有交通灯 0:未知,1:是,2:否 */
  int32_t has_traffic_light;
  /* 是否收费道路 0:未知,1:是,2:否 */
  int32_t is_toll;
  /* 是否在服务区 0:未知,1:是,2:否 */
  int32_t is_at_service;
  /* 是否小区内部路 0:未知,1:是,2:否 */
  int32_t get_inner_road;
  /* 是否停车厂内部路 0:未知,1:是,2:否 */
  int32_t is_parking_road;
  /* 坡度信息 0:未知,1:上坡,2:平坡,3:下坡 */
  int32_t road_slop_info;
  /* 预留字段 */
  std::map<std::string, std::string> reserved_info;

  OriginalLink()
      : length(0),
        linkType(0),
        roadClass(0),
        roadType(0),
        coor_idx(0),
        coor_num(0),
        is_both_direction(false),
        lane_num(0),
        is_emergency_lane(false),
        has_multi_out(0),
        has_parallel_link(0),
        has_traffic_light(0),
        is_toll(0),
        is_at_service(0),
        get_inner_road(0),
        is_parking_road(0),
        road_slop_info(0) {
    reserved_info.clear();
  }

  void Reset() {
    length = 0;
    linkType = 0;
    roadClass = 0;
    roadType = 0;
    roadName.clear();
    coor_idx = 0;
    coor_num = 0;
    is_both_direction = false;
    lane_num = 0;
    is_emergency_lane = false;
    has_multi_out = 0;
    has_parallel_link = 0;
    has_traffic_light = 0;
    is_toll = 0;
    is_at_service = 0;
    get_inner_road = 0;
    is_parking_road = 0;
    road_slop_info = 0;
    reserved_info.clear();
  }
};

struct NaviPath {
  /* 输入的 path id */
  std::string path_id;
  /* 路线长度，单位米 */
  uint32_t length;
  /* 当前导航路线的所有坐标点 */
  std::vector<Point> coors;
  /* 绑定结果的各个link信息 */
  std::vector<OriginalLink> links;

  NaviPath() : length(0) {}

  void Reset() {
    path_id.clear();
    length = 0;
    coors.clear();
    links.clear();
  }
};

enum TPIDStatus {
  /* 已存在 */
  EXISTED = 0,
  /* 不存在 */
  NOT_EXIST = 1,
  /* 数据异常 */
  BAD_ID = 2
};

struct TPIDInfo {
  /* raw link id 对应的 tpid 绑定状态 */
  TPIDStatus status;
  /* 13级瓦片，tpid 同瓦块内唯一 */
  uint32_t tile_id;
  /* link 在瓦片内的索引值；无数据版本概念；从1开始，不存在时返回0，最大值4294967295 */
  uint32_t id;

  TPIDInfo() : status(EXISTED), tile_id(0), id(0) {}

  void Reset() {
    status = EXISTED;
    tile_id = 0;
    id = 0;
  }
};

struct TxLink {
  /* raw link id */
  uint64_t raw_link_id;
  /* raw link id 对应的 tpid */
  TPIDInfo tpid;
  /* 方向：0 顺方向， 1 逆方向 */
  uint32_t dir;
  /* raw link 的形点串 */
  std::vector<Point> points;
  /* raw link 长度, 单位厘米 */
  int32_t length;
  /* 按照通行方向给出当前link的车道数,不否包含应急车道 */
  int32_t lane_number;

  TxLink() : raw_link_id(0), dir(0), length(0), lane_number(0) {}

  void Reset() {
    raw_link_id = 0;
    tpid.Reset();
    dir = 0;
    points.clear();
    length = 0;
    lane_number = 0;
  }
};

struct BindPoint {
  /* 绑定状态：0 表示成功，非0表示匹配失败，以下字段无意义 */
  int32_t status;
  /* raw link id */
  uint64_t raw_link_id;
  /* 方向： 0 顺方向，1 逆方向 */
  uint32_t dir;
  /* 对应输入点的时间戳 */
  uint64_t timestamp;
  /* 该点对应到 raw link 上绑定点的经纬度 */
  Point point;
  /* 绑定点在link上的偏移量，取值在[0-100]之间 */
  uint32_t offset;
  /* 点到绑定link的距离, 单位为厘米 */
  uint32_t distance;

  BindPoint() : status(0), raw_link_id(0), dir(0), timestamp(0), offset(0), distance(0) {}

  void Reset() {
    status = 0;
    raw_link_id = 0;
    dir = 0;
    timestamp = 0;
    point.Reset();
    offset = 0;
    distance = 0;
  }
};

struct LinkGroup {
  /* 状态：0 表示匹配成功的区间；1 表示未匹配的区间；2 表示匹配失败的区间; 3 表示多路径匹配区间，当前 linkgroup
   * 匹配的置信度低于上一段 linkgroup */
  int32_t status;
  /* 绑定结果的各个link信息 */
  std::vector<TxLink> linkinfo;
  /* 当前 linkgroup 绑定的输入点序列位置 */
  int32_t bindpoint_idx;
  /* 当前 linkgroup 绑定的输入点数量 */
  int32_t bindpoint_num;
  /* linkgroup 起始位置 */
  Point start_loc;
  /* linkgroup 终点位置 */
  Point end_loc;
  /* linkgroup 长度，单位厘米 */
  uint32_t length;

  LinkGroup() : status(0), bindpoint_idx(-1), bindpoint_num(0), length(0) {}

  void Reset() {
    status = 0;
    linkinfo.clear();
    bindpoint_idx = -1;
    bindpoint_num = 0;
    start_loc.Reset();
    end_loc.Reset();
    length = 0;
  }
};

struct Result {
  /* 为路线层面错误
   *
   * @note:
   * path_errcode = 0, 正常匹配返回
   * path_errcode = 510010: path_errmsg：invalid key，服务各个调用方的key, 异源匹配应该不会有这个问题
   * path_errcode = 510020: path_errmsg: distance between points exceeds limit ，连续两个点距离超过1km
   * path_errcode = 510030: path_errmsg: error when retrieving link ,存在某个点周围给定半径内没有候选link，
   *             表示给定路线周围没有腾讯路网
   * path_errcode = 510040: path_errmsg: point not in China ,给定路线不在中国范围内
   * path_errcode = 510050: path_errmsg: coords too short, 路线形点数量<3(路线过短);
   * path_errcode = 510060: path_errmsg: distance between points exceeds limit: 5000, 形点间最大距离不可超过5km
   * path_errcode = 510070: path_errmsg:request navi path length exceeds limit: <最长距离阈值>，路线最长不超过<阈值>
   * path_errcode = 510080 : path_errmsg : call lbs Bind error 或者 lbs match failed,return error 或者 lbs match
   *             failed,return nothing，吸附服务异常，无返回或者吸附路线返回值为空，可能是算法服务器不可用
   * path_errcode = 510090 : path_errmsg : only path id, 在第一次返回截断 path时，非第一条 path 赋值
   */
  int32_t path_errcode;
  std::string path_msg;
  /* 输入对应的 path id */
  std::string path_id;
  /* 绑路结果果，数组中每个值为一组连续link，当绑路结果不连续时，可能会有多个 */
  std::vector<LinkGroup> link_groups;
  /* 每个输入点对应的绑路结果 */
  std::vector<BindPoint> bind_points;
  /* 数据版本 */
  std::string data_version;

  void Reset() {
    path_errcode = 0;
    path_msg.clear();
    path_id.clear();
    link_groups.clear();
    bind_points.clear();
    data_version.clear();
  }
};

struct MMRequest {
  /* 请求类型 */
  TargetType type;
  /* 异源匹配目标HD数据版本, TargetType为HD时需要填充 */
  std::string heterogeneous_hd_data_version;
  /* 最多支持三条路径 */
  std::vector<NaviPath> path;

  MMRequest() : type(SD) {}

  void Reset() {
    type = SD;
    heterogeneous_hd_data_version.clear();
    path.clear();
  }

  bool operator==(const MMRequest& other) const {
    // Check if the sizes of the path vectors are the same
    if (path.size() != other.path.size()) {
      return false;
    }

    for (size_t i = 0; i < path.size(); ++i) {
      if (path[i].path_id != other.path[i].path_id) {
        return false;
      }
    }

    return true;
  }

  bool operator!=(const MMRequest& other) const { return !(*this == other); }

  std::string ToString() const {
    std::ostringstream oss;
    oss << "MMRequest { type: " << type << ",heterogeneous_hd_data_version:" << heterogeneous_hd_data_version
        << ", paths: [";
    for (const auto& p : path) {
      oss << "NaviPath { path_id: " << p.path_id << ", length: " << p.length << ", coors: [";
      for (const auto& coord : p.coors) {
        oss << "(" << coord.lng << ", " << coord.lat << "), ";
      }
      oss << "], links: [";
      for (const auto& link : p.links) {
        oss << "OriginalLink { roadName: " << link.roadName << ", length: " << link.length
            << ", linkType: " << link.linkType << ", roadClass: " << link.roadClass << ", roadType: " << link.roadType
            << ", coor_idx: " << link.coor_idx << ", coor_num: " << link.coor_num
            << ", both_direction: " << std::boolalpha << link.is_both_direction << ", lane_num: " << link.lane_num
            << ", emergency: " << link.is_emergency_lane << ", has_multi_out: " << link.has_multi_out
            << ", has_parallel_link: " << link.has_parallel_link << ", has_traffic_light: " << link.has_traffic_light
            << ", is_toll: " << link.is_toll << ", is_at_service: " << link.is_at_service
            << ", get_inner_road: " << link.get_inner_road << ", is_parking_road: " << link.is_parking_road
            << ", road_slop_info: " << link.road_slop_info << ", reserved_info: {";
        for (const auto& reserved : link.reserved_info) {
          oss << reserved.first << ": " << reserved.second << ", ";
        }
        oss << "} }, ";
      }
      oss << "] }, ";
    }
    oss << "] }";
    return oss.str();
  }

  /**
   * @brief 获取请求中的path_ids, 用于日志打印
   * @return
   */
  std::string GetPathIds() const {
    std::ostringstream oss;
    auto path_size = path.size();
    for (size_t i = 0; i < path_size; ++i) {
      oss << path[i].path_id;
      if (i < path_size - 1) {
        oss << "_";
      }
    }
    return oss.str();
  }
};

struct MMResponse {
  /* 0正常，非0失败，失败时msg提供失败原因
   *
   * @note: 为请求层面错误
   * errcode = 0, 正常请求状态，进入路线匹配流程
   * errcode = 510001: msg: content is empty ， http 请求 body 为空
   * errcode = 510002: msg: parse content failed， 解析失败
   * errcode = 510003: msg: not support target type ，数据类型不为SD
   * errcode = 510004: msg: navipath exceeds limit num: 3，匹配路径超过3条
   * errcode = 510005: msg: all match failed 匹配结果都失败
   *
   *  其它错误码: @ErrorCode
   */
  int32_t errcode;
  /* 具体错误信息见注意事项 */
  std::string msg;
  /* 0表示全量,非0表示局部实际匹配的长度,单位米
   *
   * @note: 首次返回时,只返回截断后第一条 path的异源匹配和诱导结果,但会带上所有的 path id
   * */
  uint32_t cut_path_len;
  /* 结果 */
  std::vector<Result> result;

  MMResponse() : errcode(0), cut_path_len(0) {}

  void Reset() {
    errcode = 0;
    msg.clear();
    result.clear();
  }

  bool operator==(const MMResponse& other) const {
    // Check if the sizes of the result vectors are the same
    if (result.size() != other.result.size()) {
      return false;
    }

    for (size_t i = 0; i < result.size(); ++i) {
      if (result[i].path_id != other.result[i].path_id) {
        return false;
      }
    }
    return true;
  }

  bool operator!=(const MMResponse& other) const { return !(*this == other); }

  std::string ToString() const {
    std::ostringstream oss;
    oss.precision(10);
    oss << "MMResponse {" << std::endl
        << "  errcode: " << errcode << "," << std::endl
        << "  msg: \"" << msg << "\"," << std::endl
        << "  cut_path_len: " << cut_path_len << ",result: [";
    for (const auto& r : result) {
      oss << "Result { path_errcode: " << r.path_errcode << ", path_msg: " << r.path_msg << ", path_id: " << r.path_id
          << ", link_groups: [";
      for (const auto& lg : r.link_groups) {
        oss << "LinkGroup { link_group_status: " << lg.status << ",bindpoint_idx: " << lg.bindpoint_idx
            << ",bindpoint_num: " << lg.bindpoint_num << ",start_loc: (" << lg.start_loc.lng << "," << lg.start_loc.lat
            << "), end_loc: (" << lg.end_loc.lng << "," << lg.end_loc.lat << "), length: " << lg.length
            << ", linkinfo: [";
        for (const auto& link : lg.linkinfo) {
          oss << "TxLink { raw_link_id: " << link.raw_link_id << ", dir: " << link.dir << ", length: " << link.length
              << ", lane_num:" << link.lane_number << ", tpid: { tpid_status: " << link.tpid.status
              << ", tile_id: " << link.tpid.tile_id << ", id: " << link.tpid.id << " }, points: [";
          for (const auto& point : link.points) {
            oss << point.lng / 1e7 << ", " << point.lat / 1e7 << ",";
          }
          oss << "] }, ";
        }
        oss << "] }, ";
      }
      oss << "], bind_points: [";
      for (const auto& bp : r.bind_points) {
        oss << "BindPoint { bind_status: " << bp.status << ", raw_link_id: " << bp.raw_link_id << ", dir: " << bp.dir
            << ", timestamp: " << bp.timestamp << ", point: (" << bp.point.lng / 1e7 << ", " << bp.point.lat / 1e7
            << "), offset: " << bp.offset << ", distance: " << bp.distance << " }, ";
      }
      oss << "] }, data_version: " << r.data_version;
    }
    oss << "] }";
    return oss.str();
  }
};

struct MMGuideResponse {
  /* 异源匹配返回结果 */
  MMResponse mm_response;
};

inline bool CompareMMRequestAndResponse(const MMRequest& request, const MMResponse& response) {
  // Check if the sizes of the path vectors are the same
  if (request.path.size() != response.result.size()) {
    return false;
  }

  // Compare each path_id in the request with the corresponding path_id in the response
  for (size_t i = 0; i < request.path.size(); ++i) {
    if (request.path[i].path_id != response.result[i].path_id) {
      return false;
    }
  }

  return true;
}

enum ErrorCode {
  /**
   * 成功
   */
  Success = 0,

  /**
   * 网络超时
   */
  HttpTimeOutError = 10001,

  /**
   * http 未知错误
   */
  HttpUnknownError = 10002,

  /**
   * http 请求撤销错误
   */
  HttpCancelError = 10003,

  /**
   * 鉴权错误
   */
  AuthError = 30001,

  /*
   * 文件不存在
   * */
  JsonCfgNotExit = 40001,

  /*
   * 文件加载失败失败(损坏或者格式不正确)
   */
  JsonCfgLoadFailed = 40002,

  /*
   * 没有读写权限
   */
  NotRWPermission = 40003,

  /*
   * 日志磁盘存储空间不足
   */
  NotEnoughLogSpace = 40004,

  /*
   * 离线数据磁盘存储空间不足
   */
  NotEnoughOfflineDataSpace = 40005,

  /*
   * 参数错误
   */
  Parameter_Error = 50001,

  /*
   * 未知错误
   */
  UnknowError = 90000,
};

END_NAMESPACE_TCA

#endif  // TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_TYPEDEF_H_
