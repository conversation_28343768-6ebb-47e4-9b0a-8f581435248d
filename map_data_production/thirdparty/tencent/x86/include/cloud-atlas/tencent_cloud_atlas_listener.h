// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by gangjin on 2024/5/9.
//

#ifndef TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_LISTENER_H_
#define TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_LISTENER_H_

#include "tencent_cloud_atlas_export.h"
#include "tencent_cloud_atlas_typedef.h"
BEGIN_NAMESPACE_TCA

class TCA_EXPORT HeterogeneousMatchingListener {
 public:
  virtual ~HeterogeneousMatchingListener() = default;
  /**
   * @brief  异源匹配和诱导结果回调
   * @param  result  异源匹配结果,详见MMGuideResponse
   */
  virtual void OnHeterogeneousMatching(const MMGuideResponse &result) = 0;
};

END_NAMESPACE_TCA

#endif  // TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_LISTENER_H_
