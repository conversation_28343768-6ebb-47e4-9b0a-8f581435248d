// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by gangjin on 2024/5/9.
//

#ifndef TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_PROVIDER_H_
#define TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_PROVIDER_H_

#include "net/http_interface.h"
#include "tencent_cloud_atlas_listener.h"

BEGIN_NAMESPACE_TCA

class TencentCloudAtlasProviderImpl;

class TCA_EXPORT TencentCloudAtlasProvider {
 public:
  explicit TencentCloudAtlasProvider();
  virtual ~TencentCloudAtlasProvider();
  /**
   * @brief 初始化
   * @param[in] json_config_file_path 配置文件路径
   * @param[in] http_interface http接口
   * @param[in] device_id 设备唯一标识别,设备标识长度应该在小于等于32位,如果设置为空,则使用json_config_file_path配置文件中device_id
   * @param[in] channel 渠道号,如果设置为空,则使用json_config_file_path配置文件中channel
   * @param[in] log_name_prefix 日志文件名前缀,如果为空,则使用json_config_file_path配置文件中log_name_prefix
   * @return ErrorCode 是否初始化成功,详见@ErrorCode
   */
  ErrorCode Init(const std::string &json_config_file_path,
                 const std::shared_ptr<mapbase::HttpInterface> &http_interface, const std::string &device_id = "",
                 const std::string &channel = "", const std::string &log_name_prefix = "");

  /**
   * @brief 异源匹配请求
   * @param[in] request 请求参数, 详见MMRequest
   * @return bool 是否初始化成功
   */
  bool HeterogeneousMatchingRequest(const MMRequest &request);

  /**
   * @brief 添加异源匹配监听者
   * @param[in] listener 异源匹配监听
   * @return bool ture:成功 false:失败
   */
  bool AddHeterogeneousMatchingListener(HeterogeneousMatchingListener *listener);

  /**
   * @brief 删除异源匹配监听者
   * @param[in] listener 异源匹配监听
   * @return bool ture:成功 false:失败
   */
  bool RemoveHeterogeneousMatchingListener(HeterogeneousMatchingListener *listener);

 private:
  std::unique_ptr<TencentCloudAtlasProviderImpl> p_impl_;
};

END_NAMESPACE_TCA

#endif  // TENCENTCLOUDATLASSDK_INCLUDE_TENCENT_CLOUD_ATLAS_PROVIDER_H_
