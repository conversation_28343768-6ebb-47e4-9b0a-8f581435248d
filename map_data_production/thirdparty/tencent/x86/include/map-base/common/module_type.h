#pragma once

#define TX_AUTOLOCK2(lock,tag) tencentmap::ScopedMutex sMutex##tag((lock),__FILE__, __LINE__)
#define TX_AUTOLOCK(lock) tencentmap::ScopedMutex sMutex((lock),__FILE__, __LINE__)
#define TX_LOCK(lock) (lock).lockWithCheck(__FILE__, __LINE__, INFINITY_WAIT)
#define TX_TRYLOCK(lock) (lock).lockWithCheck(__FILE__, __LINE__, 0)
#define TX_UNLOCK(lock) (lock).unlockWithCheck(__FILE__, __LINE__)

#define TX_RLOCK(lock) (lock).get_readlock().lockWithCheck(__FILE__, __LINE__, INFINITY_WAIT)
#define TX_UNRLOCK(lock) (lock).get_readlock().unlockWithCheck(__FILE__, __LINE__)
#define TX_WLOCK(lock) (lock).get_writelock().lockWithCheck(__FILE__, __LINE__, INFINITY_WAIT)
#define TX_UNWLOCK(lock) (lock).get_writelock().unlockWithCheck(__FILE__, __LINE__)

namespace tencentmap {

enum MODULETYPE {
    kMap = 0, //底图
    kMapBiz = 1, //
    kMapLoc = 2, //定位
    kMapGuide = 3, // 诱导
    kMapCalRoad = 4, // 算路
    kMapNum
};
}
