//
// Created by <PERSON> on 2021/10/19.
//

#ifndef PANGU_MAP_BASE_INCLUDE_COMMON_AUTH_H_
#define PANGU_MAP_BASE_INCLUDE_COMMON_AUTH_H_

#include "common_define.h"
#include "global_config.h"

__NAMESPACE_MAPBASE_BEGIN__


/**
 * 地图H5鉴权：https://iwiki.woa.com/pages/viewpage.action?pageId=50797877
 *
 * 接口鉴权签名计算方式：
 *
 * 签名源串规则：
 *      install_id, nonce, reqtime, reqid参数，拼接param1=value1&param2=value2...格式串，以alphabet字母
 *      表顺序排序，且必须是http query串的转义格式。签名原串例子：
 *      install_id=xxxxxxxxx&nonce=xxxxxxxx&reqid=xxxxxxxx-xxxx-xxxx-xxxxxxxx&reqtime=159000000000
 * 签名生成规则：
 *      sign = hex(MD5(signsrc + uri + srvnonce + sharedkey) )
 *      default_sign = hex(MD5(signsrc + uri + 0 + defaultShareKey) )
 * uri部分的说明：
 *      是HTTP请求的URI部分，是默认参与签名计算的请求内容。
 *      业务客户端与服务端可以协商其他内容，内容必须是手图网关能通用支持的内容类型。
 *      可以选择需要被防篡改重放的请求内容
 * 提供给H5发起请求：
 * 其中的srvnonce，shareKey, defaultShareKey，客户端端内持有，h5通过qqmap接口计算签名
 */
class AuthRequestHeader {
 public:
  std::string req_id_;	            //建议用UUID生成	每次调用应该用不同值，重试视为新的调用	N
  std::string req_time_;	        //建议用时间戳	每次调用应该用不同值，重试视为新的调用	N
  std::string user_id_;	            //用户ID标识	能够标识用户的唯一ID，登录态传递	Y
  std::string login_ssid_;	        //登录会话id	登录会话id，登录态传递	Y
  std::string imei_;	            //用户设备号ID标识	标识作用，保持相同(若已有则忽略)	Y
  std::string qimei_;	            //下一代唯一设备ID标识(欧拉QIMEI3.0)	标识作用，保持相同(若已有则忽略)	Y
  std::string nonce_;	            //客户端启动实例随机数（原mapnonce）	用于关联端内会话，建议端内协议传递	Y
  std::string install_id_;	        //客户端安装ID（原mapinst）	用于关联端内会话，建议端内协议传递	Y
  std::string sign_;	            //签名(原mapsign)	要求接口鉴权时，传递签名	Y
  std::string default_sign_;        //默认签名（原mapdefsign）	要求接口鉴权时，传递默认签名，在会话协商未建立时使用	Y
  std::string app_version_;	        //客户端版本	手图的客户端版本号	Y
  std::string channel_;	            //渠道号 Y
  std::string brand_name_;	        //设备机型型号	确认到手机的具体型号  如 华为荣耀、iPhoneX	Y
  std::string platform_;	        //操作系统	Android 或者  iOS	Y
  std::string platform_version_;	//设备机型版本号	Android 为 6.0.1   iOS 为  10.0.0
  std::string engine_;	            //引擎，比如SDK	盘古：pangu，手图：空	Y
  std::string engine_version_;	    //引擎版本号
  const std::string &GetReqId() const {
    return req_id_;
  }
  bool operator==(const mapbase::AuthRequestHeader &rhs) const {
    return req_id_ == rhs.req_id_ &&
        req_time_ == rhs.req_time_ &&
        user_id_ == rhs.user_id_ &&
        login_ssid_ == rhs.login_ssid_ &&
        imei_ == rhs.imei_ &&
        qimei_ == rhs.qimei_ &&
        nonce_ == rhs.nonce_ &&
        install_id_ == rhs.install_id_ &&
        sign_ == rhs.sign_ &&
        default_sign_ == rhs.default_sign_ &&
        app_version_ == rhs.app_version_ &&
        channel_ == rhs.channel_ &&
        brand_name_ == rhs.brand_name_ &&
        platform_ == rhs.platform_ &&
        platform_version_ == rhs.platform_version_ &&
        engine_ == rhs.engine_ &&
        engine_version_ == rhs.engine_version_;
  }
  bool operator!=(const mapbase::AuthRequestHeader &rhs) const {
    return !(rhs == *this);
  }
};

/**
 * 鉴权工具
 */
class MAPBASE_EXPORT AuthUtil {
 public:

  /**
   * 计算规则：sign = hex(MD5(signsrc + uri + srvnonce + sharedkey) )
   * H5请求鉴权： https://iwiki.woa.com/pages/viewpage.action?pageId=50797877
   *
   * @param install_id 客户端初始化传入
   * @param nonce 本地随机数，鉴权请求参数
   * @param reqid 请求的唯一ID
   * @param reqtime 请求时间戳
   * @param uri 请求统一资源定位符; 如：athena/api/res/voice_list
   * @param srvnonce 鉴权服务下发随机数，对应SessionNegotiationResponse中server_nonce_参数
   * @param sharedkey 本地持有的默认key，盘古初始化时生成
   */
  static std::string CalcSign(const std::string &install_id, const std::string &nonce,
                              const std::string &reqid, const std::string &reqtime,
                              const std::string &uri, const std::string &srvnonce,
                              const std::string &sharedkey);

  /**
   * 计算规则：default_sign = hex(MD5(signsrc + uri + 0 + defaultShareKey) )
   * H5请求鉴权 https://iwiki.woa.com/pages/viewpage.action?pageId=50797877
   *
   * @param install_id 客户端初始化传入
   * @param nonce 本地随机数，鉴权请求参数
   * @param reqid 请求的唯一ID
   * @param reqtime 请求时间戳
   * @param uri 请求统一资源定位符; 如：athena/api/res/voice_list
   * @param sharedkey 本地持有的默认key，盘古初始化时生成
   */
  static std::string CalcDefaultSign(const std::string &install_id, const std::string &nonce,
                                     const std::string &reqid, const std::string &reqtime,
                                     const std::string &uri, const std::string &sharedkey);

  /**
   * 校验构建请求头
   *
   * @param req_id 客户端初始化传入
   * @param uri 请求统一资源定位符; 如：athena/api/res/voice_list
   * @param engine 盘古：pangu，手图：空
   */
  static void CreateAuthHeader(const int32_t &req_id, const std::string &uri, const std::string &engine,
                               std::map<std::string, std::string> *headers);
};

__NAMESPACE_MAPBASE_END__

#endif //PANGU_MAP_BASE_INCLUDE_COMMON_AUTH_H_
