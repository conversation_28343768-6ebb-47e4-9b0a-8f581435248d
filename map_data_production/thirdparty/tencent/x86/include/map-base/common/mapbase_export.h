// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef MAP_BASE_INCLUDE_COMMON_MAPBASE_EXPORT_H_
#define MAP_BASE_INCLUDE_COMMON_MAPBASE_EXPORT_H_

#if defined(_WIN32)

#if defined(mapbase_EXPORTS)
#define MAPBASE_EXPORT __declspec(dllexport)
#else
#define MAPBASE_EXPORT __declspec(dllimport)
//#define MAPBASE_EXPORT
#endif  // defined(mapbase_EXPORTS)

#else  // defined(WIN32)
#if defined(mapbase_EXPORTS)
#define MAPBASE_EXPORT __attribute__((visibility("default")))
#else
#define MAPBASE_EXPORT
#endif  // defined(mapbase_EXPORTS)
#endif

#endif  // MAP_BASE_INCLUDE_COMMON_MAPBASE_EXPORT_H_
