// Copyright 2020 Tencent. All rights reserved.

//
//
//

#ifndef MAP_BASE_INCLUDE_COMMON_COMMON_LOCATION_H_
#define MAP_BASE_INCLUDE_COMMON_COMMON_LOCATION_H_

#include "common/mapbase_export.h"
#include <iostream>

namespace mapbase {

class GeoCoordinate;
class MercatorPos;
class MercatorCentimeterPos;

/**
 * 墨卡托坐标, 平面坐标系
 */
class MAPBASE_EXPORT MercatorPos {
 public:
  explicit MercatorPos(int x = 0, int y = 0) : x_(x), y_(y) {}
  ~MercatorPos();

  GeoCoordinate GetGeoPos() const;

  MercatorCentimeterPos GetCentMercator() const;

  bool operator==(const MercatorPos& r) const { return (x_ == r.x_ && y_ == r.y_); }

  void SetX(int x) { x_ = x; }
  void SetY(int y) { y_ = y; }

  int X() const { return x_; }
  int Y() const { return y_; }

  double DistanceTo(const MercatorPos& pos) const;

 private:
  int x_;
  int y_;
};

class MAPBASE_EXPORT MercatorCentimeterPos {
 public:
  explicit MercatorCentimeterPos(int x = 0, int y = 0);
  ~MercatorCentimeterPos();
  GeoCoordinate GetGeoPos() const;

  // 厘米墨卡托转墨卡托，低精度
  MercatorPos GetMercator() const;
  // 厘米墨卡托转墨卡托，高精度
  MercatorPos GetMercatorHP() const;

  bool operator==(const MercatorCentimeterPos& r) const { return (x_ == r.x_ && y_ == r.y_); }
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const MercatorCentimeterPos& pos);

  void SetX(int x) { x_ = x; }
  void SetY(int y) { y_ = y; }

  int X() const { return x_; }
  int Y() const { return y_; }

  double DistanceTo(const MercatorCentimeterPos& pos) const;

 private:
  int x_;
  int y_;
};

/**
 * 球面坐标系，经纬度
 */
class MAPBASE_EXPORT GeoCoordinate {
 public:
  explicit GeoCoordinate(double x = 0.0, double y = 0.0) : lng_(x), lat_(y) {}

  MercatorPos GetMercator() const;
  MercatorCentimeterPos GetCentMercator() const;

  double DistanceTo(const GeoCoordinate& pos) const;

  void SetLng(double lng) { lng_ = lng; }
  void SetLat(double lat) { lat_ = lat; }
  double Lng() const { return lng_; }
  double Lat() const { return lat_; }
  bool IsValid() const { return lng_ != 0.0 || lat_ != 0.0; }

  bool operator==(const GeoCoordinate& p) const;
  bool operator!=(const GeoCoordinate& p) const;

 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const GeoCoordinate& geo_coordinate);

 protected:
  double lng_;
  double lat_;
};

/**
 * 带高程的球面坐标系，经纬度，高程单位:厘米
 */
class MAPBASE_EXPORT GeoCoordinateZ : public GeoCoordinate {
 public:
  explicit GeoCoordinateZ(double lng = 0.0, double lat = 0.0, float z = 0.0) : z_(z) {
    lng_ = lng;
    lat_ = lat;
  }
  /**
   * 设置高程
   * @param z 单位:厘米
   */
  void SetZ(float z) { z_ = z; }
  /**
   * 获取高程
   * @return 单位:厘米
   */
  float Z() const { return z_; }

  bool operator==(const GeoCoordinateZ& p) const;
  bool operator!=(const GeoCoordinateZ& p) const;
 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const GeoCoordinateZ& geo_coordinate);

 protected:
  float z_;
};

}  // namespace mapbase

#endif  // MAP_BASE_INCLUDE_COMMON_COMMON_LOCATION_H_
