// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020/5/11.
//

#ifndef MAP_BASE_INCLUDE_COMMON_GLOBAL_CONFIG_H_
#define MAP_BASE_INCLUDE_COMMON_GLOBAL_CONFIG_H_

#include <map>
#include <mutex>
#include <list>
#include <iostream>
#include "common/common_define.h"
#include "common/config_observer.h"
#include "framework/thread_annotations.h"

__NAMESPACE_MAPBASE_BEGIN__
class MAPBASE_EXPORT NetworkConfig {
 public:
  NetworkType GetNetworkType() const { return network_type_; }
  std::string GetNetworkTypeStr() const {
    switch (network_type_) {
      case NetworkType::Unknown:
        return "unknown";
      case NetworkType::Mobile2g:
        return "2g";
      case NetworkType::Mobile3g:
        return "3g";
      case NetworkType::Mobile4g:
        return "4g";
      case NetworkType::Mobile5g:
        return "5g";
      case NetworkType::Wifi:
        return "wifi";
    }
    return "unknown";
  }
  void SetNetworkType(NetworkType network_type) { network_type_ = network_type; }

 private:
  NetworkType network_type_{NetworkType::Unknown};
};

class MAPBASE_EXPORT AccountConfig {
 public:
  const std::string &GetUserId() const { return user_id_; }
  void SetUserId(const std::string &user_id) { user_id_ = user_id; }
  const std::string &GetSessionId() const { return session_id_; }
  void SetSessionId(const std::string &session_id) { session_id_ = session_id; }
  int64_t GetServerNonce() const { return server_nonce_; }
  void SetServerNonce(int64_t session_nonce) { server_nonce_ = session_nonce; }
  int64_t GetClientNonce() const { return client_nonce_; }
  void SetClientNonce(int64_t client_nonce) { client_nonce_ = client_nonce; }
  const std::string &GetDefaultKey() const { return default_key_; }
  void SetDefaultKey(const std::string &default_key) { default_key_ = default_key; }

 public:
  /**
   * 重载自定义类 运算符"<<"
   */
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const AccountConfig& config);

 private:
  std::string user_id_;
  std::string session_id_;
  int64_t server_nonce_{0};
  // client_nonce_和default_key_有对应关系，可以参考pangu-evo工程中的session_negotiation_util.cc，如果不对应，鉴权会失败
  int64_t client_nonce_{0};
  std::string default_key_{"e3657677165600c17bba5bf6079d7c70"};
};

class MAPBASE_EXPORT TruckReqParams {
 public:
  bool IsTruckEnabled() const { return enable_truck_; }
  void SetTruckEnabled(bool enable_truck) { enable_truck_ = enable_truck; }
  int GetLength() const { return length_; }
  void SetLength(int length) { length_ = length; }
  int GetWidth() const { return width_; }
  void SetWidth(int width) { width_ = width; }
  int GetHigh() const { return high_; }
  void SetHigh(int high) { high_ = high; }
  int GetLoad() const { return load_; }
  void SetLoad(int load) { load_ = load; }
  int GetTotalLoad() const { return total_load_; }
  void SetTotalLoad(int total_load) { total_load_ = total_load; }
  int GetAxload() const { return axload_; }
  void SetAxload(int axload) { axload_ = axload; }
  int GetAxcnt() const { return axcnt_; }
  void SetAxcnt(int axcnt) { axcnt_ = axcnt; }

  int GetTrailer() const { return static_cast<int>(trailer_); }
  void SetTrailer(int trailer) { trailer_ = static_cast<TruckTrailerType>(trailer); }
  int GetPlateColor() const { return static_cast<int>(plate_color_); }
  void SetPlateColor(int plate_color) { plate_color_ = static_cast<TruckPlateColor>(plate_color); }
  int GetTruckType() const { return static_cast<int>(truck_type_); }
  void SetTruckType(int truck_type) { truck_type_ = static_cast<TruckType>(truck_type); }
  int GetEnergyType() const { return static_cast<int>(energy_type_); }
  void SetEnergyType(int energy_type) { energy_type_ = static_cast<TruckEnergyType>(energy_type); }
  int GetFunctionType() const { return static_cast<int>(function_type_); }
  void SetFunctionType(int function_type) { function_type_ = static_cast<TruckFunctionType>(function_type); }
  int GetGasEmisstand() const { return static_cast<int>(gas_emisstand_); }
  void SetGasEmisstand(int gas_emisstand) { gas_emisstand_ = static_cast<EmissionStandard>(gas_emisstand); }
  int GetPass() const { return static_cast<int>(pass_); }
  void SetPass(int pass) { pass_ = static_cast<PassType>(pass); }

 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const TruckReqParams& truck_req_params);

 private:
  bool enable_truck_{false};
  int length_{0};            // 车长。单位米，协议中乘1000处理，使用整形
  int width_{0};             // 车宽。单位米，协议中乘1000处理，使用整形
  int high_{0};              // 车高。单位米，协议中乘1000处理，使用整形
  int load_{0};              // 核定载重。单位吨，协议中乘1000处理，使用整形
  int total_load_{0};        // 车辆总重。单位吨，协议中乘1000处理，使用整形
  int axload_{0};            // 轴重。单位吨，协议中乘1000处理，使用整形
  int axcnt_{0};             // 轴数
  TruckTrailerType trailer_{TruckTrailerType::TRAILER_INVALID};      // 拖挂类型
  TruckPlateColor plate_color_{TruckPlateColor::COLOR_INVALID};      // 车牌颜色
  TruckType truck_type_{TruckType::TRUCK_INVALID};                   // 车辆类型
  TruckEnergyType energy_type_{TruckEnergyType::ENERGY_INVALID};     // 能源类型
  TruckFunctionType function_type_{TruckFunctionType::FUNC_NONE};    // 功能类型
  EmissionStandard gas_emisstand_{EmissionStandard::EMISSION_NONE};  // 尾气排放标准
  PassType pass_{PassType::PASS_INVALID};                            // 有无通行证
};

class MAPBASE_EXPORT AppConfig {
 public:
    AppConfig() {
      url_.clear();
      url_.insert({UrlKey::SDK_URL_KEY, "https://pangu.map.qq.com/"});
      currency_table_.clear();
      MultiLangCurrency rmb;
      rmb.multi_lang_currency_.emplace_back("");
      rmb.multi_lang_currency_.emplace_back("元");
      rmb.multi_lang_currency_.emplace_back("元");
      rmb.multi_lang_currency_.emplace_back("¥");
      MultiLangCurrency hkd;
      hkd.multi_lang_currency_.emplace_back("");
      hkd.multi_lang_currency_.emplace_back("港元");
      hkd.multi_lang_currency_.emplace_back("港元");
      hkd.multi_lang_currency_.emplace_back("HK$");
      MultiLangCurrency mop;
      mop.multi_lang_currency_.emplace_back("");
      mop.multi_lang_currency_.emplace_back("澳门币");
      mop.multi_lang_currency_.emplace_back("澳門幣");
      mop.multi_lang_currency_.emplace_back("MOP$");
      MultiLangCurrency nt;
      nt.multi_lang_currency_.emplace_back("");
      nt.multi_lang_currency_.emplace_back("新台币");
      nt.multi_lang_currency_.emplace_back("新臺幣");
      nt.multi_lang_currency_.emplace_back("NT$");
      currency_table_.insert({Currency::RMB, rmb});
      currency_table_.insert({Currency::HKD, hkd});
      currency_table_.insert({Currency::MOP, mop});
      currency_table_.insert({Currency::NT, nt});
    }

    AppConfig(const AppConfig& config) {
      std::lock(this->inner_lock, config.inner_lock);
      // make sure both already-locked mutexes are unlocked at the end of scope
      std::lock_guard<std::mutex> lock1{this->inner_lock, std::adopt_lock};
      std::lock_guard<std::mutex> lock2{config.inner_lock, std::adopt_lock};
      app_ver_ = config.app_ver_;
      channel_ = config.channel_;
      device_name_ = config.device_name_;
      device_type_ = config.device_type_;
      imei_ = config.imei_;
      app_id_ = config.app_id_;
      os_version_ = config.os_version_;
      offline_version_ = config.offline_version_;
      sdk_version_name_ = config.sdk_version_name_;
      language_ = config.language_;
      language_type_ = config.language_type_;
      url_ = config.url_;
      sdk_version_code_ = config.sdk_version_code_;
      offline_data_path_ = config.offline_data_path_;
      platform_ = config.platform_;
      engine_ = config.engine_;
      auth_enabled_ = config.auth_enabled_;
      hwp_enabled = config.hwp_enabled ;                 // 是否开启车道级能力
      lane_guide_enabled_ = config.lane_guide_enabled_;         // 是否开启车道级导航能力
      lane_match_angle_ = config.lane_match_angle_;          // 车道级下是否用定位匹配角度
      test_url_ = config.test_url_;
      charge_percent_ = config.charge_percent_;     // 途中充电站充电至电池容量的比例,默认充到90%, E2表示法.
      charge_percent_lowest_ = config.charge_percent_lowest_; // 电池最少要剩余多少,计算耗尽时，不是用到0%，默认用到10%
      destination_lowest_power_percent_ = config.destination_lowest_power_percent_; // 到终点时，电池最少要剩余多少
      global_lowest_power_percent_ = config.global_lowest_power_percent_; // 全程最小电量百分比
      install_id_ = config.install_id_;        // App安装唯一标识, 每次安装唯一即可;
      region_ = config.region_;
      coord_sys_ = config.coord_sys_;
      currency_table_ = config.currency_table_;
    }

    AppConfig& operator = (const AppConfig& config) MAPBASE_NO_THREAD_SAFETY_ANALYSIS {
      if ( this == &config) {
        return *this;
      }

      std::lock(this->inner_lock, config.inner_lock);
      // make sure both already-locked mutexes are unlocked at the end of scope
      std::lock_guard<std::mutex> lock1{this->inner_lock, std::adopt_lock};
      std::lock_guard<std::mutex> lock2{config.inner_lock, std::adopt_lock};

      app_ver_ = config.app_ver_;
      channel_ = config.channel_;
      device_name_ = config.device_name_;
      device_type_ = config.device_type_;
      imei_ = config.imei_;
      app_id_ = config.app_id_;
      os_version_ = config.os_version_;
      offline_version_ = config.offline_version_;
      sdk_version_name_ = config.sdk_version_name_;
      language_ = config.language_;
      language_type_ = config.language_type_;
      url_ = config.url_;
      sdk_version_code_ = config.sdk_version_code_;
      offline_data_path_ = config.offline_data_path_;
      platform_ = config.platform_;
      engine_ = config.engine_;
      auth_enabled_ = config.auth_enabled_;
      hwp_enabled = config.hwp_enabled ;                 // 是否开启车道级能力
      lane_guide_enabled_ = config.lane_guide_enabled_;         // 是否开启车道级导航能力
      lane_match_angle_ = config.lane_match_angle_;          // 车道级下是否用定位匹配角度
      test_url_ = config.test_url_;
      charge_percent_ = config.charge_percent_;     // 途中充电站充电至电池容量的比例,默认充到90%, E2表示法.
      charge_percent_lowest_ = config.charge_percent_lowest_; // 电池最少要剩余多少,计算耗尽时，不是用到0%，默认用到10%
      destination_lowest_power_percent_ = config.destination_lowest_power_percent_; // 到终点时，电池最少要剩余多少
      global_lowest_power_percent_ = config.global_lowest_power_percent_; //全程最小电量百分比
      install_id_ = config.install_id_;        // App安装唯一标识, 每次安装唯一即可;
      region_ = config.region_;
      coord_sys_ = config.coord_sys_;
      currency_table_ = config.currency_table_;
      return *this;
    }
  std::string GetAppVer() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return app_ver_;
  }
  void SetAppVer(const std::string &app_ver) {
      std::lock_guard<std::mutex> guard(inner_lock);
      app_ver_ = app_ver;
  }
  std::string GetChannel() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return channel_;
  }
  void SetChannel(const std::string &channel) {
      std::lock_guard<std::mutex> guard(inner_lock);
      channel_ = channel;
  }
  std::string GetDeviceName() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return device_name_;
  }
  void SetDeviceName(const std::string &device_name) {
      std::lock_guard<std::mutex> guard(inner_lock);
      device_name_ = device_name;
  }
  std::string GetDeviceType() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return device_type_;
  }
  void SetDeviceType(const std::string &device_type) {
      std::lock_guard<std::mutex> guard(inner_lock);
      device_type_ = device_type;
  }
  std::string GetImei() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return imei_;
  }
  void SetImei(const std::string &imei) {
      std::lock_guard<std::mutex> guard(inner_lock);
      imei_ = imei;
  }
  std::string GetAppId() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return app_id_;
  }
  void SetAppId(const std::string &app_id) {
      std::lock_guard<std::mutex> guard(inner_lock);
      app_id_ = app_id;
  }
  std::string GetOsVersion() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return os_version_;
  }
  void SetOsVersion(const std::string &os_version) {
      std::lock_guard<std::mutex> guard(inner_lock);
      os_version_ = os_version;
  }
  std::string GetOfflineVersion() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return offline_version_;
  }
  void SetOfflineVersion(const std::string &offline_version) {
      std::lock_guard<std::mutex> guard(inner_lock);
      offline_version_ = offline_version;
  }
  std::string GetSdkVersionName() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return sdk_version_name_;
  }
  void SetSdkVersionName(const std::string &sdk_version_name) {
      std::lock_guard<std::mutex> guard(inner_lock);
      sdk_version_name_ = sdk_version_name;
  }
  std::string GetLanguage() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return language_;
  }
  void SetLanguage(const std::string& language) {
      std::lock_guard<std::mutex> guard(inner_lock);
      language_ = language;
      ConvertLanguageType(language);
  }
  std::map<UrlKey, std::string> GetCustomHost() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return url_;
  }
  void SetCustomHost(const std::map<UrlKey, std::string>& custom_host) {
      std::lock_guard<std::mutex> guard(inner_lock);
      for (const auto &it : custom_host) {
          url_[it.first] = it.second;
      }
  }
  LanguageType GetLanguageType() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return language_type_;
  }
  int GetSdkVersionCode() const {
      std::lock_guard<std::mutex> guard(inner_lock);
      return sdk_version_code_;
  }
  void SetSdkVersionCode(int sdk_version_code) {
    std::lock_guard<std::mutex> guard(inner_lock);
    sdk_version_code_ = sdk_version_code;
  }
  std::string GetOfflineDataPath() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return offline_data_path_;
  }
  void SetOfflineDataPath(const std::string &offline_data_path) {
    std::lock_guard<std::mutex> guard(inner_lock);
    offline_data_path_ = offline_data_path;
  }
  std::string GetPlatform() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return platform_;
  }
  void SetPlatform(const std::string &platform) {
    std::lock_guard<std::mutex> guard(inner_lock);
    platform_ = platform;
  }

  std::string GetEngine() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return engine_;
  }
  
  void SetEngine(const std::string &engine) {
    std::lock_guard<std::mutex> guard(inner_lock);
    engine_ = engine;
  }

  bool IsAuthEnabled() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return auth_enabled_;
  }

  void SetAuthEnabled(bool auth_enabled) {
    std::lock_guard<std::mutex> guard(inner_lock);
    auth_enabled_ = auth_enabled;
  }
  /**
   *
   * @return
   * @Deprecated   代替方法 IsLaneGuideEnabled
   */
  bool IsHwpEnabled() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return hwp_enabled;
  }
  /**
   *
   * @param enabled
   * @Deprecated  代替方法 SetLaneGuideEnabled
   */
  void SetHwpEnabled(bool enabled) {
    std::lock_guard<std::mutex> guard(inner_lock);
    hwp_enabled = enabled;
  }

  /**
   * 获取是否支持车道级导航能力
   * @return  true:支持; false:不支持
   */
  bool IsLaneGuideEnabled() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return lane_guide_enabled_;
  }
  /**
   * 设置是否支持车道级导航能力
   * @param enabled true:支持; false:不支持
   */
  void SetLaneGuideEnabled(bool enabled) {
    std::lock_guard<std::mutex> guard(inner_lock);
    lane_guide_enabled_ = enabled;
  }
  /**
   * 车道级下是否使用定位匹配角度
   * @return false:自由车标; ture:吸附角度
   */
  bool IsLaneMatchAngleEnabled() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return lane_match_angle_;
  }
  /**
   * 设置车道级下是否使用定位匹配角度
   * @param enabled false:自由车标; ture:吸附角度
   */
  void SetLaneMatchAngleEnabled(bool enabled) {
    std::lock_guard<std::mutex> guard(inner_lock);
    lane_match_angle_ = enabled;
  }

  std::string GetTestUrl() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return test_url_;
  }
  void SetTestUrl(const std::string &test_url) {
    std::lock_guard<std::mutex> guard(inner_lock);
    test_url_ = test_url;
  }
  /**
   * 途中充电站充电至电池容量的比例,默认充到85%, E2表示法.
   */
  int GetChargePercent() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return charge_percent_;
  }
  void SetChargePercent(int charge_percent) {
    std::lock_guard<std::mutex> guard(inner_lock);
    charge_percent_ = charge_percent;
  }

  /**
   * 到达途经点的时候，电池最少要剩余多少百分比,计算耗尽时，不是用到0%，默认用到10%
   * 该值也会影响到达终点的电量，详情请看destinationLowestPowerPercent的说明
   * @return 到达途经点的电量百分比信息
   */
  int GetChargePercentLowest() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return charge_percent_lowest_;
  }
  void SetChargePercentLowest(int charge_percent_lowest) {
    std::lock_guard<std::mutex> guard(inner_lock);
    charge_percent_lowest_ = charge_percent_lowest;
  }

  /**
   * 到达终点时，电池最少要剩余多少百分比，如果不设定，SDK中默认值为-1
   * 路线服务会做如下限定：终点剩余电量=min(max(全程最小电量global_lowest_power_percent_，终点剩余电量)，50%)
   * @param destination_lowest_power_percent 到达终点的电量百分比信息
   */
  int GetDestinationLowestPowerPercent() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return destination_lowest_power_percent_;
  }
  void SetDestinationLowestPowerPercent(int destination_lowest_power_percent) {
    std::lock_guard<std::mutex> guard(inner_lock);
    destination_lowest_power_percent_ = destination_lowest_power_percent;
  }

    /**
     * 全程最小电量百分比，用于判断路线可达性的标准、计算耗尽点，默认值5%。
     * 此值传-1时服务端处理逻辑：新能源解释性不生效，可达性按charge_percent_lowest_的值处理。
     * 该值也会影响到达终点的电量，详情请看destination_lowest_power_percent的说明
     */
    int GetGlobalLowestPowerPercent() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return global_lowest_power_percent_;
    }
    void SetGlobalLowestPowerPercent(int global_lowest_power_percent) {
    std::lock_guard<std::mutex> guard(inner_lock);
    global_lowest_power_percent_ = global_lowest_power_percent;
    }

  /**
  * 获取App安装唯一标识
  */
  std::string GetInstallId() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return install_id_;
  }
  /**
  * 设置App安装唯一标识
  */
  void SetInstallId(const std::string &install_id) {
    std::lock_guard<std::mutex> guard(inner_lock);
    install_id_ = install_id;
  }
  /**
   * 获取地区信息
   */
  int GetRegion() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return static_cast<int>(region_);
  }
  /**
   * 设置地区信息
   */
  void SetRegion(int region) {
    std::lock_guard<std::mutex> guard(inner_lock);
    region_ = static_cast<RegionType>(region);
  }
  /**
   * 获取GPS坐标系类型
   */
  int GetCoordSys() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return static_cast<int>(coord_sys_);
  }
  /**
   * 设置GPS坐标系类型
   */
  void SetCoordSys(int coord_sys) {
    std::lock_guard<std::mutex> guard(inner_lock);
    coord_sys_ = static_cast<CoordSysType>(coord_sys);
  }
  /**
   * 获取GPS坐标系类型字符串
   */
  std::string GetCoordSysStr() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    if (coord_sys_ == CoordSysType::WGS84) {
      return "wgs84";
    } else if (coord_sys_ == CoordSysType::GCJ02) {
      return "gcj02";
    } else {
      return "gcj02";
    }
  }
  /**
   * 获取币种多语言表
   */
  std::map<Currency, MultiLangCurrency> GetCurrencyTable() const {
    std::lock_guard<std::mutex> guard(inner_lock);
    return currency_table_;
  }
  /**
   * 设置币种多语言表
   */
  void SetCurrencyTable(const std::map<Currency, MultiLangCurrency>& currency_table) {
    std::lock_guard<std::mutex> guard(inner_lock);
    currency_table_.clear();
    for (const auto &it : currency_table) {
      currency_table_[it.first] = it.second;
    }
  }

 private:
  void ConvertLanguageType(const std::string& language) {
      if (language == "zh-cn") {
          language_type_ = LanguageType::LANGUAGE_CN;
      } else if (language == "zh-tw") {
          language_type_ = LanguageType::LANGUAGE_TC;
      } else if (language == "en") {
          language_type_ = LanguageType::LANGUAGE_EN;
      } else {
          language_type_ = LanguageType::LANGUAGE_CN;
      }
  }

 public:
  /**
   * 重载自定义类 运算符"<<"
   * @return
   */
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const AppConfig& config);

 private:
  std::string app_ver_;
  std::string channel_;
  std::string device_name_;
  std::string device_type_;
  std::string imei_{"default"};
  std::string app_id_;
  std::string os_version_;
  std::string offline_version_{"0"};
  std::string sdk_version_name_;
  std::string language_{"zh-cn"};
  LanguageType language_type_{LanguageType::LANGUAGE_CN};
  std::map<UrlKey, std::string> url_;
  int sdk_version_code_{2000};
  std::string offline_data_path_;
  std::string platform_{"pangu"};
  std::string engine_{"pangu"};
  bool auth_enabled_{true};
  bool hwp_enabled{true};                 // 是否开启车道级能力
  bool lane_guide_enabled_{true};         // 是否开启车道级导航能力
  bool lane_match_angle_{true};          // 车道级下是否用定位匹配角度
  std::string test_url_;
  int charge_percent_ = 85;     // 途中充电站充电至电池容量的比例,默认充到85%, E2表示法.
  int charge_percent_lowest_ = 10; // 进站充电时电池最少要剩余多少电量百分比，默认用到10%，E2表示法。
  int destination_lowest_power_percent_ = -1; // 到终点时，电池最少要剩余多少，E2表示法。
  int global_lowest_power_percent_ = 10; // 全程最小电量百分比，用于判断路线可达性的标准、计算耗尽点，默认值5%，E2表示法。
  std::string install_id_;        // App安装唯一标识, 每次安装唯一即可;
  RegionType region_{RegionType::Mainland};
  CoordSysType coord_sys_{CoordSysType::GCJ02};
  std::map<Currency, MultiLangCurrency> currency_table_;
  mutable std::mutex  inner_lock;
};

class MAPBASE_EXPORT ServiceConfig {
 private:
  ServiceConfig();

 public:
  ~ServiceConfig();
  const NetworkConfig &GetNet() const { return net_; }
  const AppConfig &GetAppConfig() const { return app_config_; }
  const AccountConfig &GetAccountConfig() const { return account_config_; }
  /**
   * 获取货车导航参数
   */
  TruckReqParams &GetTruckReqParams() { return truck_req_params_; }
  NetworkObserver *GetNetworkObserver() const;
  AccountObserver *GetAccountObserver() const;
  void AddSessionStatusObserver(const std::weak_ptr<SessionStatusObserver> &observer);
  void RemoveSessionStatusObserver(const std::weak_ptr<SessionStatusObserver> &observer);
  void NotifySessionStatusExpired();
  void NotifyNewSession(const std::string& session_id, int64_t server_nonce);

  static void Init(const AppConfig &app_config);

  static void InitTruckReqParams(const TruckReqParams &truck_req_params);

  static ServiceConfig &GetInstance();

 private:
  AppConfig app_config_;
  NetworkConfig net_;
  AccountConfig account_config_;
  TruckReqParams truck_req_params_;

  std::unique_ptr<NetworkObserver> network_observer_;
  std::unique_ptr<AccountObserver> account_observer_;
  std::list<std::weak_ptr<SessionStatusObserver>> session_status_observers_;

};

__NAMESPACE_MAPBASE_END__

#endif  // MAP_BASE_INCLUDE_COMMON_GLOBAL_CONFIG_H_
