// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020/5/26.
//

#ifndef MAP_BASE_INCLUDE_COMMON_CONFIG_OBSERVER_H_
#define MAP_BASE_INCLUDE_COMMON_CONFIG_OBSERVER_H_

#include "common/common_define.h"
#include "common/global_config.h"
__NAMESPACE_MAPBASE_BEGIN__

enum class MAPBASE_EXPORT NetworkType { Unknown, Mobile2g, Mobile3g, Mobile4g, Mobile5g, Wifi };

class MAPBASE_EXPORT NetworkObserver {
 public:
  virtual ~NetworkObserver() = default;
  /**
   * 网络类型变更
   * @param type 网络类型
   */
  virtual void OnNetworkTypeChanged(mapbase::NetworkType type) = 0;
};

class MAPBASE_EXPORT AccountObserver {
 public:
  virtual ~AccountObserver() = default;
  /**
   * 账户信息变化
   * @param user_id 用户id
   * @param default_key 预埋密钥
   * @param client_nonce 客户端随机数
   */
  virtual void OnAccountChanged(const std::string& user_id, const std::string& default_key,
                                int64_t client_nonce) = 0;
  /**
   * Session信息变化
   * @param session_id 会话id
   * @param server_nonce 服务端随机数
   */
  virtual void OnSessionChanged(const std::string& session_id, int64_t server_nonce) = 0;
};

class MAPBASE_EXPORT SessionStatusObserver {
 public:
  virtual ~SessionStatusObserver() = default;

  /**
   * 鉴权返回新的会话
   *
   * @param session_id 会话id
   * @param server_nonce 服务端随机数
   */
  virtual void OnNewSession(const std::string& session_id, int64_t server_nonce) = 0;
  /**
   * 会话过期回调
   */
  virtual void OnSessionExpired() = 0;
};

__NAMESPACE_MAPBASE_END__

#endif  // MAP_BASE_INCLUDE_COMMON_CONFIG_OBSERVER_H_
