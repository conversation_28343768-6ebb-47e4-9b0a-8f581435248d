#pragma once
#include "base_mutex.h"
#include <iostream>
#include <vector>
namespace tencentmap {

class MAPBASE_EXPORT RwLock : public BaseMutex{
    
private:
    RwLock(const RwLock&);
    RwLock& operator=(const RwLock&);
public:
    class MAPBASE_EXPORT Locker : public BaseMutex{
    protected:
        RwLock& m_lock_;
    public:
        Locker(RwLock &lock) : BaseMutex(),m_lock_(lock) {
            m_enable_deadLock_ = lock.m_enable_deadLock_;
            m_type_ = lock.m_type_;
        }
        virtual void* getMutexId() {
            return &m_lock_;
        }
        virtual void unlock() {
            m_lock_.unlock();
        }
    public:
        // 原始收集方法
        void origin_collect_items(int tid, const char *file, int line)
        {
            BaseMutex::collect_blocked_items(tid, file, line);
        }
        
        virtual void collect_blocked_items(int tid, const char *file, int line)
        {
            m_lock_.collect_all_block_items(tid, file, line);
        }
    };
    class MAPBASE_EXPORT WriteLock : public Locker
    {
        friend class RwLock;

    public:
        WriteLock(RwLock &_lock) : Locker(_lock) {}

        virtual bool timedLock(int timeout /*ms*/)
        {
            return m_lock_.writelock_timeout(timeout);
        }
    };

    // 读锁
    class MAPBASE_EXPORT ReadLock : public Locker
    {
        friend class RwLock;

    public:
        int m_for_thread_;

        ReadLock(RwLock &_lock) : Locker(_lock), m_for_thread_(0) {}

        virtual bool timedLock(int timeout /*ms*/) // true: 加锁失败, false: 加锁成功
        {
            return m_lock_.readlock_timeout(timeout);
        }

        virtual void clear_lock()
        {
            m_lock_.release_readlock();
        }
    };

public:
    RwLock(MODULETYPE type,bool enable_dead_lock);
    ~RwLock();


public:
    /**
     * 为每个线程分配一个readLock实例
     * @return
     */
    BaseMutex& get_readlock()
    {
        FlagLocker locker(m_readlocks_flag_);

        ReadLock * lock = NULL;
        uint64_t tid = getTid();
        int free_idx = -1;
        for (int i = 0, s = m_readlocks_.size(); i < s; i++) {
            if (m_readlocks_[i]->m_for_thread_ == tid) { // found
                lock = m_readlocks_[i];
                break;
            } else if (free_idx == -1 && m_readlocks_[i]->m_for_thread_ == 0) {
                // if not found, get a empty element for candidate
                free_idx = i;
            }
        }

        if (lock == NULL) {
            if (free_idx == -1) { // append m_readlocks
                lock = new ReadLock(*this);
                m_readlocks_.push_back(lock);
            } else {
                lock = m_readlocks_[free_idx];
            }

            lock->m_for_thread_ = tid;
        }

        return *lock;
    }

    /**
     * 释放readlock。读锁unlock到deep == 0时调用
     */
    void release_readlock()
    {
        FlagLocker locker(m_readlocks_flag_);

        uint64_t tid = getTid();
        for (int i = 0, s = m_readlocks_.size(); i < s; i++) {
            if (m_readlocks_[i]->m_for_thread_ == tid) { // found
                m_readlocks_[i]->m_for_thread_ = 0;
                break;
            }
        }
    }

    BaseMutex& get_writelock()
    {
        return *m_writelock_;
    }

    bool readlock_timeout(int timeout);
    bool writelock_timeout(int timeout);

    void collect_all_block_items(int tid, const char *file, int line);
    void unlock()
    {
        pthread_rwlock_unlock(&m_lock_);
    }

private:
    struct MAPBASE_EXPORT FlagLocker
    {
        std::atomic_flag& m_flag;
        FlagLocker(std::atomic_flag& flag) : m_flag(flag)
        {
            while (m_flag.test_and_set()) {
                sched_yield();
            }
        }

        ~FlagLocker()
        {
            m_flag.clear();
        }
    };

private:
    
    pthread_rwlock_t m_lock_;
    WriteLock* m_writelock_;

    std::vector<ReadLock *> m_readlocks_;
    std::atomic_flag m_readlocks_flag_ ATOMIC_FLAG_INIT;

};

}
