// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020/5/26.
//

#ifndef MAP_BASE_INCLUDE_COMMON_MAPBASE_ERROR_H_
#define MAP_BASE_INCLUDE_COMMON_MAPBASE_ERROR_H_

namespace mapbase {
namespace error_code {

const static int Ok = 0;
const static int InputParameterError = -1;
const static int JceParseError = -2;
const static int ServerResponseError = -3;

namespace async_controller {
const static int ControllerNotInitialized = -100;
const static int CancelFailFinished = -101;
const static int CallbackRemoved = -102;
const static int CancelTooMuchJobs = -103;
const static int CancelByUser = -104;
}  // namespace async_controller

}  // namespace error_code
}  // namespace mapbase

#endif  // MAP_BASE_INCLUDE_COMMON_MAPBASE_ERROR_H_
