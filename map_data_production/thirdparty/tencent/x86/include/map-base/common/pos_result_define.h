// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020/5/8.
//

#ifndef MAP_BASE_INCLUDE_COMMON_POS_RESULT_DEFINE_H_
#define MAP_BASE_INCLUDE_COMMON_POS_RESULT_DEFINE_H_

#include "common/common_define.h"
#include "common/trajectory_structure.h"
#include <iomanip>
#include <sstream>

__NAMESPACE_MAPBASE_BEGIN__

// 视觉相关
#define MAX_LANE_CNT  6    // 左/右单侧最大识别车道数
#define MAX_LINE_CNT  6

/**
* @brief 车道匹配工作模式
*/
enum class MAPBASE_EXPORT LaneMatchWorkMode : int8_t {
    Unknown = -1,                                   //未工作
    LowQuality = 0,                                 //模糊车道匹配（左中右）
    Numbered = 1,                                   //精确到当前匹配车道号
    HDMapped = 2,                                   //基于HDMap的车道匹配
    HDGuideAreaMapped = 3,                          //基于HDMap的引导面中心线吸附匹配
    HDSpecialScene = 4,                             //基于特殊场景匹配（如：收费站引导面中心线吸附匹配）
    // 10开头：传感器类问题
    Error_NoVision = -11,                           // 没有视觉信号
    Error_VisionInvalid = -12,                      // 视觉信号不可用
    Error_VisionInterrupt = -13,                    // 视觉信号中断
    Error_DrNotInit = -14,                          // dr未完成初始化
    Error_SensorBackup = -15,                       // 传感器相关备用
    Error_RtkLost = -16,                            // RTK信号丢失
    Error_RtkNotUse = -17,                          // RTK信号精度不足
    // 20开头：吸附逻辑问题
    Error_HMMAtToll = -21,                          // 收费站吸附失败
    Error_HMMAtVirtualLane = -22,                   // 虚拟车道吸附失败
    Error_HMMAtVirtualLine = -23,                   // 虚拟车道线吸附失败
    Error_HMMDisBeyondDisTh = -24,                  // 吸附距离太大
    Error_HMMBackup = -25,                          // 吸附相关备用
    Error_HMMTunnel = -26,                          // 隧道吸附失败
    Error_HMMFailWithinTimeTh = -29,                // 短时间内未知原因吸附失败
    // 30开头：SD吸附问题
    Error_SDCruiseLaneLevel = -31,                  // SD吸附位置的道路等级与HD吸附的道路等级不一致
    Error_SDBackup = -32,                           // SD相关备用
    Error_GuideAreaLost = -33,                      // 没有引导面的数据
    // 40开头：SHR问题
    Error_HMMFailInSHRLaneGroup = -41,              // SD对应的LaneGroup无法进行吸附
    Error_SHRBackup = -42,                          // SHR相关备用
    // 50开头：HD结果问题
    Error_HDResultInvalid = -51,                    // HD的结果没有实际意义，例如车道数与数据不一致
    Error_HDVisionConfidenceLow = -52,              // 视觉置信度不满足进入车道级的条件
    Error_HDBackup = -53,                           // HD相关备用
    // 60开头：nerd数据问题
    Error_NerdNoData = -61,                         // nerd-api没有读取到任何数据
    Error_NerdLackOfData = -62,                     // 长时间未知原因吸附失败,大概率为nerd-api读取的数据存在缺失,小概率为dr脱路
    Error_NerdBackup = -63,                         // Nerd相关备用
    // 70开头：道路场景问题
    Error_SceneParallelRoad = -71,			// 策略-平行路场景不进入车道级
    Error_SceneTrafficJam = -72,			// 策略-堵车到家不进入车道级
    Error_SceneDivergenceRoad = -73,		// 策略-分歧路口不进入车道级
    Error_SceneLaneLine = -74,			// 策略-车道数与视觉感知召回对应出现异常,不进入车道级
    // 未知原因
    Error_Unknown = -99,                            // 未知原因
};

/**
* @brief 车道线颜色
*/
enum LineColor {
    OtherColor = 0,          // 其他
    White = 1,               // 白色
    Yellow = 2,              // 黄色
    Orange = 3,              // 橙色
    Blue = 4,                // 蓝色
    WhiteYellow = 5,         // 左白右黄
    YellowWhite = 6          // 左黄右白
};

/**
* @brief 车道线类型
*/
enum LineType {
  OtherLine = 0,                 // 其他
  SingleSolid = 1U,              // 单实线
  SingleDashed = 1U << 1,        // 单虚线
  DoubleSolid = 1U << 2,         // 双实线
  DoubleDashed = 1U << 3,        // 双虚线
  SolidDashed = 1U << 4,         // 左实右虚
  DashedSolid = 1U << 5,         // 左虚右实
  DiversionLine = 1U << 6,       // 导流线
  Guardrail = 1U << 7,           // 防护栏
  CurbStone = 1U << 8,           // 路缘石
  ArtificialVirtual = 1U << 9,   // 人工虚拟线
  OccludeVirtual = 1U << 10,     // 遮挡虚拟线
  LonDeceleration = 1U << 11,    // 纵向减速标线
  ShortThickDashed = 1U << 12,   // 短粗虚线
  VariableGuideLane = 1U << 13,  // 可变导向车道标线
  VirtualMarking = 1U << 14,     // 虚拟标线
  RoadEdge = 1U << 15,           // 道路边缘
  ConstructionEdge = 1U << 16,   // 施工边缘
  PedestrianCross = 1U << 17     // 人行横道线
};


/**
* @brief 车道类型
*/
enum LaneType {
  NotInvestigated = 0,               // 未调查
  RegularLane = 1U,                  // 常规车道
  AccelerationLane = 1U << 1,        // 加速车道
  DecelerationLane = 1U << 2,        // 减速车道
  CompoundLane = 1U << 3,            // 复合车道：为加速和减速车道
  HOVLane = 1U << 4,                 // 多乘座车道
  SlowLane = 1U << 5,                // 慢车道
  ReversibleLane = 1U << 6,          // 潮汐车道
  BusLane = 1U << 7,                 // 公交车道
  BicycleLane = 1U << 8,             // 自行车道
  ShoulderLane = 1U << 9,            // 路肩
  EmergencyLane = 1U << 10,          // 应急车道
  EmergencyParkingStrip = 1U << 11,  // 紧急停车带
  Entrance = 1U << 12,               // 入口
  Export = 1U << 13,                 // 出口
  ParkingLane = 1U << 14,            // 停车道
  Sidewalk = 1U << 15,               // 人行道
  TurnWaitingArea = 1U << 16,        // 转弯待转区
  TollLane = 1U << 17,               // 收费站车道
  ETCLane = 1U << 18,                // ETC车道
  AvoidDangerLane = 1U << 19,        // 避险车道
  DirectionalLane = 1U << 20,        // 定向车道
  TwoWayLane = 1U << 21,             // 双向车道
  VariableLane = 1U << 22,           // 可变车道
  ReverseLane = 1U << 23,            // 逆向车道
  ExtendedLane = 1U << 24,           // 扩展车道
  OtherLane = 1U << 31               // 其他
};

enum class MAPBASE_EXPORT MatchSceneProperty{
    Null = -1,        /**< 默认 */
    SA,               /**< 服务区 */
    OilStation,       /**< 加油站 */
    Toll,             /**< 收费站 */
    Tunnel,           /**< 隧道   */
    Parking,          /**< 地下车库，高置信度 */
    ParkingMiddle     /**< 地下车库，中置信度 */
};

enum class MAPBASE_EXPORT MultiRouteMatchStatus {
  Null = -1,
  AllOnRoute,     /**< 全部吸附成功 */
  AllYaw,         /**< 所有路线偏航 */
  MainYaw,        /**< 主路线偏航  */
  CompanionYaw,   /**< 伴随路线偏航 */
  MainSlightlyYaw /**< 主路线微偏  */
};

enum class MAPBASE_EXPORT RouteMatchSceneStatus {
  Null = -1,          /**< 默认 */
  ArrivalDestination, /**< 到达目的地 */
  Start,              /**< 在起点附近 */
  SA,                 /**< 服务区 */
  OilStation,         /**< 加油站 */
  Toll,               /**< 收费站 */
  Tunnel,             /**< 隧道   */
  Parking,            /**< 停车场 */
  Boundary,           /**< 不可偏航区域面 */
  InBoundForYaw,      /**< 在禁止偏航boundary里发偏 */
};

enum class MAPBASE_EXPORT SmartLocStatus {
  SmartNone = -1,     /**< 默认，建议什么都不做 */
  SmartStart = 10001, /**< 进入智能定位 */
  SmartStop = 10002,   /**< 退出智能定位 */
  SmartRunning = 10003 /**< 网络智能定位中 */
};

enum class MAPBASE_EXPORT SmartLocType {
  SmartLocType_None = -1,     /**< 默认 */
  SmartLocType_Tunnel = 0,    /**< 隧道推算 */
  SmartLocType_Common = 1,    /**< 普通智能定位类型（即非隧道推算） */
};


//路口车道数据偏航状态类型
enum class MAPBASE_EXPORT LaneYawResult {
  LYR_Unknown = 0,             // 0默认值，正常情况
  LYR_GenerateAccompalyRoute,  // 1实线隔离，生成伴随
  LYR_YAWDetected              // 2硬隔离偏航
};

enum class MAPBASE_EXPORT SlightlyYawType {
    SYAW_NULL = 0,              // 默认值
    SYAW_NORMAL = 1,            // 正常微偏
    SYAW_BAD_GPS = 2,           // 定位质量差
    SYAW_ORIGIN = 3,            // 起点附近微偏
    SYAW_LONG_TIME = 1000       // 平行路长时间微偏
};

//定位车标显示类型
enum class MAPBASE_EXPORT PosLogoDisplayType {
    LogoDisplayType_Route = 0,     // 导航路线吸附结果
    LogoDisplayType_Road = 1,        // 巡航路网吸附结果
    LogoDisplayType_GPS = 2          // 显示原始定位结果
};

/**
 * @brief 车道线数据
 *  Model: y = c3*x^3 + c2*x^2 + c1*x + c0
 */
struct LocLine {
  LineType type;          //车道线类型
  LineColor color;        //车道线颜色
  float c[4];             // c[0~3]
  float width;            // 车道宽度，单位米(m)
  float geometryQuality;  // 几何形状可信度0~1
  float typeQuality;      // 类型可信度 0~1

  LocLine()
      : type(OtherLine), color(OtherColor), width(0.0), geometryQuality(0.0), typeQuality(0.0) {
    c[0] = 0.0;
    c[1] = 0.0;
    c[2] = 0.0;
    c[3] = 0.0;
  }
};


/**
* @brief 车道数据
*  一个车道附有左右各一条车道线
*/
struct LocLane {
    float curLaneMatchConf;     // 匹配当前车道的置信度 0~1
    LaneType type;              // 车道类型
    LocLine lines[2];           // Lines[0] 表示左侧车道线； Lines[1]表示右侧车道线

    LocLane(): curLaneMatchConf(0.0),
               type(LaneType::NotInvestigated){}
};

/**
* @brief HD车道数据
*  一个车道附有左右各一条车道线
*/
struct HDLocLane : public LocLane {
    int64_t laneID;
    HDLocLane(): LocLane(), laneID(0LL){}
};

// 公交子段类型
enum class MAPBASE_EXPORT BusSubType {
  SubRouteType_UNKNOWN = -1,   // 未知
  SubRouteType_BUS = 1,        // 公交
  SubRouteType_SUBWAY = 2,     // 地铁
  SubRouteType_CAR = 3,        // 普通驾车
  SubRouteType_WALK = 4,       // 步行
  SubRouteType_TRAIN = 11,      // 火车
  SubRouteType_CHSR = 12,       // 高铁
  SubRouteType_COACH = 20,      // 长途大巴
  SubRouteType_PLANE = 30,      // 飞机
  SubRouteType_TRANS = 99,       // 换乘接驳
  SubRouteType_BIKE = 100,       // 自行车
  SubRouteType_TAXI = 101,      // 打车
};

//定位信号来源类型
enum class PosType {
  PosType_Null = -1,
  PosType_Gps = 0,            /**< GPS定位（不包括RTK) */
  PosType_Network = 1,        /**< 网络点定位*/
  PosType_RTK = 2,            /**< RTK定位*/
  PosType_Smart = 3,          /**< 智能定位（	网络推导点n_dr、隧道推算点）*/
  PosType_DR = 4,             /**< 惯导定位（车机+IMU、手图+IMU）*/
  PosType_Vision = 5,         /**< 视觉定位 */
};

enum class PosSubType {
  PosSubType_Null = -1,
  PosSubType_Tunnel_Reckon = 0,    /**< 隧道推导点 */
  PosSubType_Network_Reckon = 1,   /**< 网络推导点 */
  PosSubType_Phone_Imu = 2,        /**< 手图+IMU */
  PosSubType_Auto_Imu = 3,         /**< 车机+IMU */
};

//吸附定位所用的传感器融合类型（降级类型）
enum class PosFusionType {
  PosFusionType_Null = 0,
  PosFusionType_Gps = 1,          /**< GPS定位 */
  PosFusionType_Network = 2,      /**< 网络点 */
  PosFusionType_Dr = 3,           /**< 惯导推导点 */
  PosFusionType_Rtk = 4,          /**< Rtk，主要手机应用 */
  PosFusionType_Vision = 5,       /**< 视觉车道级定位，不带RTK */
  PosFusionType_RTKFusion = 6,    /**< 高精融合定位（包括AD高精定位和座舱带RTK的融合定位 */
};

//车机吸附场景分类
enum class LocMappedSceneType {
  LocMappedSceneType_None = 0,          /**< 默认 */
  LocMappedSceneType_SD_CRUISE = 1,     /**< SD巡航路网吸附 */
  LocMappedSceneType_HD_NAV_LANE = 2,   /**< HD车道中心线吸附 */
  LocMappedSceneType_HD_NAV_GUIDEAREA = 3,  /**<HD引导面中心线吸附 */
  LocMappedSceneType_4K_NAV_GUIDEAREA = 4,  /**<4K引导面中心线吸附 */
};

/*
 * 吸附错误码类型
 * 可能同时存在
 */
enum class LocMappedErrorCode {
  LocMappedErrorCode_None = 0,                /**< 默认值无错误 */
  //SD导航常见错误
  // -1 - -9： SD导航或巡航吸附相关错误码
  LocMappedErrorCode_SD_NoRouteData = -1,    /**< 无路线 */

  LocMappedErrorCode_GPS = 1,                                 // GPS定位
  LocMappedErrorCode_Network = 2,                             // 网络定位
  LocMappedErrorCode_Imu = 3,                                 // Imu
  LocMappedErrorCode_Odo = 4,                                 // 轮速
  LocMappedErrorCode_Rtk = 5,                                 // Rtk
  LocMappedErrorCode_Vision = 6,                              // 视觉

  // 10开头：传感器类问题
  LocMappedErrorCode_NoVision = -11,                           // 没有视觉信号
  LocMappedErrorCode_VisionInvalid = -12,                      // 视觉信号不可用
  LocMappedErrorCode_VisionInterrupt = -13,                    // 视觉信号中断
  LocMappedErrorCode_DrNotInit = -14,                          // dr未完成初始化
  LocMappedErrorCode_SensorBackup = -15,                       // 传感器相关备用

  LocMappedErrorCode_UNCALIBRATED = -16,                       //未标定错误
  LocMappedErrorCode_SignalInterruption = -17,                 // 信号中断错误
  LocMappedErrorCode_SPDUNCALIBRATED = -18,                    // 轮速未标定
  LocMappedErrorCode_GYROSCALEUNCALIBRATED = -19,              // 陀螺刻度系数未标定

  // 20开头：吸附逻辑问题
  LocMappedErrorCode_HMMAtToll = -21,                          // 收费站吸附失败
  LocMappedErrorCode_HMMAtVirtualLane = -22,                   // 虚拟车道吸附失败
  LocMappedErrorCode_HMMAtVirtualLine = -23,                   // 虚拟车道线吸附失败
  LocMappedErrorCode_HMMDisBeyondDisTh = -24,                  // 吸附距离太大
  LocMappedErrorCode_HMMBackup = -25,                          // 吸附相关备用
  LocMappedErrorCode_HMMFailWithinTimeTh = -29,                // 短时间内未知原因吸附失败

  // 30开头：SD吸附问题
  LocMappedErrorCode_SDCruiseLaneLevel = -31,                  // SD吸附位置的道路等级与HD吸附的道路等级不一致
  LocMappedErrorCode_SDBackup = -32,                           // SD相关备用

  // 40开头：SHR问题
  LocMappedErrorCode_HMMFailInSHRLaneGroup = -41,              // SD对应的LaneGroup无法进行吸附
  LocMappedErrorCode_SHRBackup = -42,                          // SHR相关备用

  // 50开头：HD结果问题
  LocMappedErrorCode_HDResultInvalid = -51,                    // HD的结果没有实际意义，例如车道数与数据不一致
  LocMappedErrorCode_HDVisionConfidenceLow = -52,              // 视觉置信度不满足进入车道级的条件
  LocMappedErrorCode_HDBackup = -53,                           // HD相关备用

  // 60开头：nerd数据问题
  LocMappedErrorCode_NerdNoData = -61,                         // nerd-api没有读取到任何数据
  LocMappedErrorCode_NerdLackOfData = -62,                     // 长时间未知原因吸附失败,大概率为nerd-api读取的数据存在缺失,小概率为dr脱路
  LocMappedErrorCode_NerdBackup = -63,                         // Nerd相关备用

  // 70开头：道路场景问题
  LocMappedErrorCode_SceneParallelRoad = -71,                // 策略-平行路场景不进入车道级
  LocMappedErrorCode_SceneTrafficJam = -72,                  // 策略-堵车到家不进入车道级
  LocMappedErrorCode_SceneDivergenceRoad = -73,              // 策略-分歧路口不进入车道级
  LocMappedErrorCode_SceneLaneLine = -74,                    // 策略-车道数与视觉感知召回对应出现异常,不进入车道级

  // 未知原因
  LocMappedErrorCode_Unknown = -99,                            // 未知原因
};

/*
 * 定位吸附细分场景结果类
 */
class MAPBASE_EXPORT LocMappedSceneResult {
 public:
  std::vector<LocMappedSceneType> scene_types;
  std::vector<LocMappedErrorCode> error_codes;

  void Reset() {
    scene_types.clear();
    error_codes.clear();
  }
};

enum class RoadMatchStatus {
  Null = -1,   /**< 默认值 */
  OffRoute = 0,   /**< 车标脱离 */
  OnRoute = 1,     /**< 车标匹配在路上 */
};

enum class RouteSide {
  Loc_SAME_SIDE = 0X90,
  Loc_OPPOSITE_SIDE = 0X91,
  Loc_NO_CENTER_LINE = 0X92,
  Loc_UNKNOWN_SIDE = 0X93
};

/*
 * 定位内部标记的数据是否在线/离线及相关版本号
 */
class MAPBASE_EXPORT RoadMatchDataVersion {
 public:
  bool is_online_sd;
  bool is_online_hd;

  uint32_t version_sd;
  uint32_t version_hd;

  RoadMatchDataVersion()
      : is_online_sd(false),
        is_online_hd(false),
        version_sd(0),
        version_hd(0){}

  void Reset() {
    is_online_sd = false;
    is_online_hd = false;
    version_sd = 0;
    version_hd = 0;
  }

  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const RoadMatchDataVersion& info) {
    out << "{online_sd:" << static_cast<int>(info.is_online_sd)
        << ",online_hd:" << static_cast<int>(info.is_online_hd)
        << ",ver_sd:" << info.version_sd
        << ",ver_hd:" << info.version_hd
        << "}";
    return out;
  }
};

class MAPBASE_EXPORT PosPoint {
 public:
  GeoCoordinate geo_pos;                 /**< 大地坐标系 */
  MercatorCentimeterPos mercator_cm_pos; /**< 墨卡托横坐标系 */
  uint64_t timestamp;      /**< 时间戳。1970年01月01日00时00分00秒以来的秒数*/
  int32_t source_type;     /**< 信号来源。
                              - 0：GPS定位
                              - 1：网络定位(包括室内定位和基站定位) */
  float alt;               /**< 海拔，单位：米 */
  float course;            /**< 方向，北0顺时针，范围：[0-360) */
  float speed;             /**< 速度，单位：千米每小时 */
  float displaySpeed;    /**< 仪表速度，单位：千米每小时 */
  int32_t speed_available; /**< 速度可用。1：可用 0：不可用*/
  float pos_acc;           /**< 位置精度，单位：米 */

  PosPoint()
      : timestamp(0), source_type(0), alt(0), course(0), speed(0), displaySpeed(-1), speed_available(1), pos_acc(0) {}

  void Reset() {
    geo_pos = GeoCoordinate();
    mercator_cm_pos = MercatorCentimeterPos();
    timestamp = 0;
    source_type = 0;
    alt = 0;
    course = 0;
    speed = 0;
    displaySpeed = -1;
    speed_available = 1;
    pos_acc = 0;
  }
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const PosPoint& info) {
    out << "{geo:(" << info.geo_pos
        << "),mer_cm:(" << info.mercator_cm_pos
        << "),ts:" << info.timestamp
        << ",src:" << info.source_type
//        << ",course:" << info.course
//        << ",alt:" << info.alt
//        << ",pos_acc:" << info.pos_acc
        << ",speed:" << info.speed
        << ",dis_speed:" << info.displaySpeed
//        << ",speed_avl:" << info.speed_available
        << "}";
    return out;
  }
};
class MAPBASE_EXPORT LaneInfoForSearchRoute {
 public:
  LaneYawResult lane_yaw_result;     //路口车道数据偏航状态类型：0默认值，正常情况；1实线隔离，生成伴随；2硬隔离偏航
  PosPoint startPoint;               //路口车道数据起始点坐标信息
  std::vector<PosPoint> exitPoints;  //路口车道数据可通达link坐标点信息
 public:
  LaneInfoForSearchRoute() : lane_yaw_result(LaneYawResult::LYR_Unknown) {}

  void Reset() {
    lane_yaw_result = LaneYawResult::LYR_Unknown;
    startPoint.Reset();
    exitPoints.clear();
  }
};
// 下一代导航车道信息
class MAPBASE_EXPORT LaneMatchResult {
 public:
  int64_t timestamp;
  LaneMatchWorkMode workMode;                    //0模糊车道定位，1精确车道定位，2HD车道定位
  int8_t curLaneNum;                             //workMode = 0 时,通过-1，0，1表示车道左中右的模糊状态。 workMode = 1时表示当前所在车道号左数，从1开始，视觉+数据融合结果
  float curLaneNumConf;                          //置信度 0~1
  int8_t totalLaneCnt;                           //总车道数，视觉+数据融合结果
  float totalLaneCntConf;                        //置信度 0~1
  LocLane lane[MAX_LANE_CNT];                    //车道类型和车道线信息，从0开始由左向右，0|1|2|3|4|5| ，视觉+数据融合结果
  LaneInfoForSearchRoute laneInfoForSearchRoute; //车道级算路信息
 public:
  LaneMatchResult()
      : timestamp(0),
        workMode(LaneMatchWorkMode::Unknown),
        curLaneNum(0),
        curLaneNumConf(0.0),
        totalLaneCnt(0),
        totalLaneCntConf(0)
        {}
        
  void Reset() {
    timestamp = 0;
    workMode = LaneMatchWorkMode::Unknown;
    curLaneNum = 0;
    curLaneNumConf = 0;
    totalLaneCnt = 0;
    totalLaneCntConf = 0;
    laneInfoForSearchRoute.Reset();
  }

  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const LaneMatchResult& info) {
    out << "{ts:" << info.timestamp
        << ",workMode:" << static_cast<int>(info.workMode)
        << ",curLaneNum:" << static_cast<int>(info.curLaneNum)
        << ",curLaneNumConf:" << info.curLaneNumConf
        << ",totalLaneCnt:" << static_cast<int>(info.totalLaneCnt)
        << ",totalLaneCntConf:" << info.totalLaneCntConf
//        << ",laneInfoForSearchRoute:" << info.laneInfoForSearchRoute
        << "}" ;
    return out;
  }
};

class MAPBASE_EXPORT HDLaneMatchResult {
public:
    int64_t timestamp;
    LaneMatchWorkMode workMode;  //2-HD车道级定位，其他-非车道级定位
    int8_t curLaneNum;           //workMode = 0 时,通过-1，0，1表示车道左中右的模糊状态。 workMode = 1时表示当前所在车道号左数，从1开始，视觉+数据融合结果
    float curLaneNumConf;        //置信度 0~1
    float totalLaneCntConf;      //置信度 0~1
    std::vector<HDLocLane> lanes;//车道类型和车道线信息，从0开始由左向右，0|1|2|3|4|5| ，视觉+数据融合结果
    PosPoint freedPos;           //自由车标，用于展示自车在车道内移动与换道，与laneMatchedPos互斥使用
    PosPoint laneMatchedPos;     //以车道中心线匹配后的自车位置，车道内行驶时永远匹配在车道上，变换车道时此值有过渡效果
    int32_t laneMatchedIndex;    //车道匹配结果在当前lane形点上的起点的下标，返回-1时无有效下标
    double slope;                //车道匹配结果,通过nerd计算得到的相对坡度,默认值为0
    PosLogoDisplayType logo_display_type;  //HD场景车标显示类型
public:
    HDLaneMatchResult() : timestamp(0),
        workMode(LaneMatchWorkMode::Unknown),
        curLaneNum(0),
        curLaneNumConf(0.0),
        totalLaneCntConf(0), laneMatchedIndex(-1), slope(0.0),
        logo_display_type(PosLogoDisplayType::LogoDisplayType_Route){}

    void Reset() {
        timestamp = 0;
        workMode = LaneMatchWorkMode::Unknown;
        curLaneNum = 0;
        curLaneNumConf = 0;
        totalLaneCntConf = 0;
        laneMatchedIndex = -1;
        slope = 0.0;
        logo_display_type = PosLogoDisplayType::LogoDisplayType_Route;
    }

    inline static std::pair<uint32_t, uint32_t> GetLaneID(const mapbase::HDLaneMatchResult& lane_match_result) {
      int curLaneNum = lane_match_result.curLaneNum;
      int size = (int)lane_match_result.lanes.size();
      if (curLaneNum >= 1 && curLaneNum <= size) {
        auto long_lane_id = lane_match_result.lanes[curLaneNum - 1].laneID;

        uint32_t tile = 0, lane = 0;
        tile = (uint32_t)(long_lane_id >> 32);
        lane = (uint32_t)(long_lane_id & 0xffffffff);
        return std::make_pair(tile, lane);
      }
      return {};
    }

    MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const HDLaneMatchResult& hdLaneMatchResult) {
      int loc_work_mode = static_cast<int>(hdLaneMatchResult.workMode);
      std::pair<uint32_t, uint32_t> lane_id = GetLaneID(hdLaneMatchResult);
      const mapbase::GeoCoordinate& geo = hdLaneMatchResult.laneMatchedPos.geo_pos;
      const auto& p3d =  mapbase::GeoCoordinateZ(geo.Lng(), geo.Lat(), hdLaneMatchResult.laneMatchedPos.alt);

      out << ",lane_mode:" << loc_work_mode << ",lane_k:" << static_cast<int>(hdLaneMatchResult.curLaneNum)
          << ",lane_vec:" << hdLaneMatchResult.lanes.size()
          << ",lane:" << lane_id.first << "-" << lane_id.second
          << ",index:" << hdLaneMatchResult.laneMatchedIndex
          << ",slop:" << hdLaneMatchResult.slope
          << ",tm:" << hdLaneMatchResult.laneMatchedPos.timestamp
          << ",course:" << hdLaneMatchResult.laneMatchedPos.course
          << ",pos_acc:" << hdLaneMatchResult.laneMatchedPos.pos_acc
          << ",pos:" << std::setprecision(9) << p3d.Lng() << "," << p3d.Lat() << "," << p3d.Z();
      return out;
    }
};

class MAPBASE_EXPORT HDGuideAreaMatchResult {
public:
  int64_t timestamp;
  int8_t workMode;                        // 0-无吸附，3-引导面吸附
  int8_t guideAreaType;                   // -1-未吸附，0-HD引导面，1-4K引导面
  PosPoint freedPos;                      //自由车标，用于展示自车在车道内移动与换道，与laneMatchedPos互斥使用
  PosPoint laneMatchedPos;                //以车道中心线匹配后的自车位置，车道内行驶时永远匹配在引导面上
  int32_t laneMatchedIndex;               //车道匹配结果在当前lane形点上的起点的下标，返回-1时无有效下标
  std::string guideAreaMatchedRouteId;    //当前吸附上的route_id
  std::string guideAreaMatchedGuideAreaId;    //当前吸附上的guideArea_id
  bool isMain;
  PosLogoDisplayType logo_display_type;   //引导面场景车标显示类型
  HDGuideAreaMatchResult()
  : timestamp(0),
  workMode(0),
  guideAreaType(-1),
  laneMatchedIndex(0),
  isMain(false),
  logo_display_type(PosLogoDisplayType::LogoDisplayType_Route)
  {}

  void Reset() {
    timestamp = 0;
    workMode = 0;
    laneMatchedIndex = 0;
    guideAreaType = -1;
    logo_display_type = PosLogoDisplayType::LogoDisplayType_Route;
  }
};

// 记录匹配信息
class MAPBASE_EXPORT MatchPosition {
 public:
  uint64_t link_id;                /**< 路网匹配link id */
  PosPoint match_pos;              /**< 匹配位置，包含道路角度 */
  float prob;                      /**< 匹配概率，0~1 */
  float offset;                    /**< 当前自车距离link起点的纵向距离，单位m */

  MatchPosition()
      : link_id(0),
        prob(0.0),
        offset(0.0) {}

  void Reset() {
    match_pos.Reset();
    link_id = 0;
    prob = 0.0;
    offset = 0.0;
  }
};

class MAPBASE_EXPORT RoadMatchResult {
 public:
  PosPoint match_pos;              /**< 匹配位置 */
  uint64_t link_raw_id;            /**< 路网匹配原始link id */
  uint64_t link_id;                /**< 路网匹配link id */
  uint32_t matched_index;          /**< 匹配位置索引 */
  OfflineRoadFuncClass func_class; /**< 路网匹配路线等级 */
  OfflineRoadKind road_kind;       /**< 路网匹配路线类型 */
  float road_dir;                  /**< 匹配位置道路方向 */
  std::vector<MatchPosition> matchPositions; /**< 所有可能的匹配位置 */
  uint32_t road_kind_conf; /**< 路线类型置信度,目前只有0/1，1表示视觉识别出主/辅场景，
                                0表示其他情况 */
  bool is_used_matched_head;       /**< 手图专用，默认false使用手图传感器方向，true 驾车巡航路线方向 */
  RoadMatchStatus road_matched_status; /**< 巡航吸附状态 */
  RoadMatchDataVersion data_version; /**< 定位内部标记的数据在离线状态及版本号*/

  RoadMatchResult()
      : link_raw_id(0),
        link_id(0),
        matched_index(0),
        func_class(OfflineRoadFuncClass::Null),
        road_kind(OfflineRoadKind::Null),
        road_dir(0.0f),
        road_kind_conf(0),
        is_used_matched_head(false),
        road_matched_status(RoadMatchStatus::Null) {}

  void Reset() {
    match_pos.Reset();
    link_id = 0;
    matched_index = 0;
    func_class = OfflineRoadFuncClass::Null;
    road_kind = OfflineRoadKind::Null;
    road_dir = 0.0f;
    matchPositions.clear();
    road_kind_conf = 0;
    is_used_matched_head = false;
    road_matched_status = RoadMatchStatus::Null;
    data_version.Reset();
  }
};

class MAPBASE_EXPORT MatchResult {
 public:
  PosPoint match_pos;                 /**< 匹配位置 */
  std::string route_id;               /**< 导航路线id */
  std::string bus_sub_uid;            /**< 公交子段uid, interval_uid or tran_uid*/
  BusSubType sub_type;                /**< 公交子段类型*/
  int32_t matched_index;              /**< 匹配位置索引 */
  RouteMatchSceneStatus scene_status; /**< 导航匹配场景 */
  SmartLocType  smart_type;           /**< 智能定位类型 */
  SmartLocStatus smart_state;         /**< 智能定位状态 */
  int32_t destination_subtype;        /**< 终点结束场景需要回流的subtype */
  std::string yaw_info; /**< 偏航时需要回流的偏航信息，目前暂时由诱导通过callback回调给客户端回流 */
  int32_t yaw_type;     /**< 偏航的具体原因 */
  int outway_during_time; /**< 偏航时时间统计回调 */
  RouteSide routeSide;

  MatchResult()
      : matched_index(-1),
        scene_status(RouteMatchSceneStatus::Null),
        smart_type(SmartLocType::SmartLocType_None),
        smart_state(SmartLocStatus::SmartNone),
        destination_subtype(-1),
        yaw_type(0),
        outway_during_time(0),
        routeSide(RouteSide::Loc_UNKNOWN_SIDE) {}

  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const MatchResult& info) {
    out << "{route_id:" << info.route_id
        << ",match_idx:" << info.matched_index
        << ",match_pos:" << info.match_pos
        << ",scene_status:" << static_cast<int>(info.scene_status)
        << ",smart_type:" << static_cast<int>(info.smart_type)
        << ",smart_state:" << static_cast<int>(info.smart_state)
//        << ",dest_subtype:" << static_cast<int>(info.destination_subtype)
//        << ",yaw_type:" << static_cast<int>(info.yaw_type)
//        << ",yaw_info:" << info.yaw_info
        << "," << static_cast<int>(info.sub_type)
        << "," << info.bus_sub_uid
//        << ",o_time:" << info.outway_during_time
        << "}";
    return out;
  }
};

class MAPBASE_EXPORT ExtraInfo {
 public:
  int32_t gps_status;          /**< GPS状态 0：强 1：弱*/
  uint64_t gps_weak_last_time; /**< GPS状态持续时长 单位秒*/
  int32_t location_available;  /**< 位置信息是否可用 1：可用 0：不可用*/
  int32_t motion;              /**< 运动状态 0未知，1静止，2步行，3驾车，4骑车，5跑步，6倾斜*/
  double motion_confidence;    /**< 运动状态的置信度，结合motion字段使用*/
  std::string building_id;     /**< 建筑物id号，若building_id为空则为室外状态，否则为室内*/
  std::string floor_name;      /**< 楼层名称*/
  int32_t pos_interactive_type;/**< 定位提示按钮状态类型 前高位28bit表示当前提示按钮在当前路线所有按钮区间的索引值，后低位4bit表示需要触发提示的按钮值，定义如下：
                                0无提示，1在桥上，2在桥下，3在主路，4在辅路，5在对面，6桥下主路，7桥下辅路  */

  ExtraInfo() : gps_status(0), gps_weak_last_time(0), location_available(1), motion(0), motion_confidence(0.0f),
      pos_interactive_type(0) {}

  void Reset() {
    gps_status = 0;
    gps_weak_last_time = 0;
    location_available = 1;
    motion = 0;
    motion_confidence = 0.0f;
    building_id.clear();
    floor_name.clear();
    pos_interactive_type = 0;
  }
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const ExtraInfo& info) {
    out << "{gps_status:" << info.gps_status
        << ",last_time:" << info.gps_weak_last_time
        << ",type:" << info.pos_interactive_type
        << "}";
    return out;
  }

};

/**
* @brief 定位引擎工作状态
*/
enum class MAPBASE_EXPORT PosStatus : int8_t {
  Ok = 0,                      /**< 正常状态 */
  IMULost = 1,                 /**< IMU信号缺失 */
  SpeedLost = 2,               /**< 车速信号丢失 */
  Initializing = 3,            /**< 初始定位 */
  Uncalibrated = 4             /**< 惯导未标定 */
};

class MAPBASE_EXPORT MatchLocationInfo {
 public:
  std::string main_route_id;             /**< 导航主路线id */
  PosType pos_type;                      /**< 定位信号类型 */
  PosSubType pos_sub_type;               /**< 定位信号子类型 */

  PosPoint origin_pos;                   /**< 原始位置信号 */
  RoadMatchResult road_result;           /**< 惯导路网匹配结果 */
  std::vector<MatchResult> route_result; /**< 路线匹配结果 */
  MultiRouteMatchStatus match_status;    /**< 综合匹配状态 */
  MatchSceneProperty scene_property;     /**< 位置Link属性，导航/巡航场景的属性，导航优先 */
  ExtraInfo extra_info;                  /**< 吸附额外信息 */
  LaneMatchResult lane_result;           /**< 车道匹配结果 */
  PosStatus pos_status;                  /**< 定位引擎工作状态 */
  PosFusionType pos_fusion_type;         /**< 传感器融合类型（降级策略类型）*/
  LocMappedSceneResult scene_result;     /**< 吸附场景及错误状态*/
  PosLogoDisplayType logo_display_type;  /**< 导航场景车标显示类型*/
  MatchLocationInfo() : match_status(MultiRouteMatchStatus::Null), scene_property(MatchSceneProperty::Null),
      logo_display_type(PosLogoDisplayType::LogoDisplayType_Route) {}

  void Reset() {
    main_route_id.clear();
    pos_type = PosType::PosType_Null;
    pos_sub_type = PosSubType::PosSubType_Null;
    origin_pos.Reset();
    road_result.Reset();
    route_result.clear();
    match_status = MultiRouteMatchStatus::Null;
    lane_result.Reset();
    extra_info.Reset();
    pos_status = PosStatus::Ok;
    pos_fusion_type = PosFusionType::PosFusionType_Null;
    scene_result.Reset();
    logo_display_type = PosLogoDisplayType::LogoDisplayType_Route;
  }

  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const MatchLocationInfo& info) {
    out << " MatchLocationInfo:{main_id:" << info.main_route_id
        << ",status:" << static_cast<int>(info.match_status)
        << ",ld_type:" << static_cast<int>(info.logo_display_type)
		<< ",property:" << static_cast<int>(info.scene_property)
        << ",pos_type:" << static_cast<int>(info.pos_type)
        << ",pos_sub_type:" << static_cast<int>(info.pos_sub_type)
        << ",orig_pos:" << info.origin_pos
        << ",route_result list:[";
    for (const auto& result : info.route_result) {
      out << result << ",";
    }
    out << "],lane_result:" << info.lane_result
//    << ",road_result:" << info.road_result // 未重载
    << ",extra:" << info.extra_info
    << "}";
    return out;
  }
};

/**
 * @brief 高频定位回调结构体
 * @note  数据频率：30HZ
 * @see   HighFreqLocInfo
 */
class MAPBASE_EXPORT HighFreqLocInfo {
public:
  int64_t timestamp;                   // 系统开机时间，unit [ms].
  int32_t vdrStatus;                   // VDR内部的状态：-1(无GPS)，0(未初始化)，1(stable&gps)，2(stable&no gps)，3(unstable&gps)，4(unstable&no gps)
  int8_t  frequency;                   // 当前定位高频回调频率(意义:当前每秒回调次数; 单位:HZ, 范围1HZ-10HZ, 默认值10HZ)
  double longitude;                    // Logitude in WGS84, unit: [deg].
  double latitude;                     // Latitude in WGS84, unit: [deg].
  double altitude;                     // Altitude in WGS84, unit: [m].
  float yaw;                           // 北为0，东为90 (0-360], 为0时表示没有数据, unit: [deg].
  float speed;                         // unit: [m/s].
  float acc;                           // accuracy, unit: [m].
  double qw;                           // 四元数w
  double qx;                           // 四元数x
  double qy;                           // 四元数y
  double qz;                           // 四元数z
  HDLaneMatchResult hdLaneMatchResult; // HD车道级匹配结果，仅当workMode==HDMapped时有效
  std::vector<mapbase::HDGuideAreaMatchResult> hdGuideAreaMatchResult; //HD引导面吸附的结果
  std::vector<mapbase::ObstacleDescriptor> obstacle; // obstacle信息
  LocMappedSceneResult hdSceneResult;  //HD吸附场景及错误状态

  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const mapbase::HighFreqLocInfo& info) {
    out << "frequency:" << static_cast<int>(info.frequency) << ",";
    out << info.hdLaneMatchResult << ",area_match_sz:" << info.hdGuideAreaMatchResult.size();
    out << [&] {
      std::ostringstream oss;
      for (size_t k = 0; k < info.hdGuideAreaMatchResult.size(); k++) {
        const auto& record = info.hdGuideAreaMatchResult[k];
        const mapbase::GeoCoordinate& geo = record.laneMatchedPos.geo_pos;
        const auto& loc_3d = mapbase::GeoCoordinateZ(geo.Lng(), geo.Lat(), record.laneMatchedPos.alt);
        oss << ";k:" << k << ",pos:" << std::setprecision(9) << loc_3d.Lng() << "," << loc_3d.Lat() << "," << loc_3d.Z()
            << ",mode:" << static_cast<int>(record.workMode) << ",rid:" << record.guideAreaMatchedRouteId << ",main:" << record.isMain
            << ",area:" << record.guideAreaMatchedGuideAreaId << ",mat_idx:" << record.laneMatchedIndex
            << ",tm:" << record.laneMatchedPos.timestamp<<",course:"<<record.laneMatchedPos.course
            << ",pos_acc" <<record.laneMatchedPos.pos_acc;
      }
      return oss.str();
    }();
    out << ";obstacle-sz:" << info.obstacle.size();
    return out;
  }

public:
    HighFreqLocInfo() : timestamp(0), vdrStatus(-1), frequency(10), longitude(-1.0f), latitude(-1.0f), altitude(-1.0f),
        yaw(0.0f), speed(0.0f), acc(0.0f), qw(1.0f), qx(0.0f), qy(0.0f), qz(0.0f) {}

    void Reset() {
        timestamp = 0;
        vdrStatus = -1;
        longitude = -1.0f;
        latitude = -1.0f;
        altitude = -1.0f;
        yaw = 0.0f;
        speed = 0.0f;
        acc = 0.0f;
        qw = 1.0f;
        qx = 0.0f;
        qy = 0.0f;
        qz = 0.0f;
    }

};

/**
 * @brief 高精定位回调failsafe结构体
 * @note  数据频率：20HZ
 * @note  根据产品定义默认初始值为-700.0
 * @see   FailSafeInfo
 */
class MAPBASE_EXPORT FailSafeInfo {
public:
  uint8_t failsafe_loc_status;        // 0 : 定位可用 1 : 定位处在失效推算中 2 : 定位失败

  uint8_t failsafe_imu_status;        // bit0: IMU无信号
                                      // bit1: 预留字段
                                      // bit2: IMU频率异常
                                      // bit3: 加速度过大, 垂直和水平方向滤波后模长>max_acc(3g)
                                      // bit4: 角速率过大, 三个轴滤波后模长 >max_w(100°/s)
                                      // bit5: IMU未标定(依赖传感器数据该字段)
                                      // bit6: 传感器时间戳逆序

  uint8_t failsafe_gnss_status;       // bit0: GNSS 数据丢失
                                      // bit1: 预留字段(因format由域控共享内存实现)
                                      // bit2: GNSS频率异常
                                      // bit3: 任何属性出现无效数值NAN
                                      // bit4: GNSS长时间无法定位或不固定

  uint8_t failsafe_camera_status;     // bit0: 摄像头数据丢失 
                                      // bit1: 预留字段 
                                      // bit2: 摄像头频率异常
                                      // bit3:摄像头与地图数据不匹配

  uint8_t failsafe_hdmap_status;      // bit0: 获取地图失败
                                      // bit1: 预留字段
                                      // bit2: 地图许可失效或过期

  uint8_t failsafe_pulse_status;      // bit0: 车速或轮速数据丢失
                                      // bit1: 预留字段
                                      // bit2: 车速或轮速频率异常
                                      // bit3: 车速或轮速异常/超量程, 异常跳变
public:
    FailSafeInfo() : failsafe_loc_status(0), failsafe_imu_status(0), 
    failsafe_gnss_status(0), failsafe_camera_status(0), failsafe_hdmap_status(0), failsafe_pulse_status(0){}

    void Reset() {
        failsafe_loc_status = 0;
        failsafe_imu_status = 0;
        failsafe_gnss_status = 0;
        failsafe_camera_status = 0;
        failsafe_hdmap_status = 0;
        failsafe_pulse_status = 0;
    }

};

/**
 * @brief 高精定位回调中当前匹配车道信息结构体
 * @see   MatchedLaneInfo
 */
class MAPBASE_EXPORT MatchedLaneInfo {
public:
  uint64_t lane_group_id;              //**< 路网匹配lane_group_id*/
  uint64_t lane_id;                    //**< 路网匹配lane id */
  int8_t lane_seq;                     //表示当前所在车道号右数，从1开始，”0”表示无效。
  int8_t lane_seq_conf;                //置信度 0~100
  float dis_to_left_line;              //车辆原点距离左车道线的距离（车辆原点为后轴中心）
  float dis_to_right_line;             //车辆原点距离右车道线的距离（车辆原点为后轴中心）
  float yaw_to_left_line;              //与左车道线相对航向
  float yaw_to_right_line;             //与右车道线相对航向

  MatchedLaneInfo() {
    Reset();
  }
  void Reset() {
    lane_group_id = 0;
    lane_id = 0;
    lane_seq = 0;
    lane_seq_conf = 0;
    dis_to_left_line = -700.0;
    dis_to_right_line = -700.0;
    yaw_to_left_line = -700.0;
    yaw_to_right_line = -700.0;
  }
};

/**
 * @brief 高精定位回调结构体
 * @note  数据频率：20HZ
 * @see   HDLocInfo
 */
class MAPBASE_EXPORT HDLocInfo {
public:
  int64_t timestamp;                   // 系统开机时间，unit [ms].
  std::vector<MatchedLaneInfo> current_lanes;  // 当前匹配车道信息，位置在重叠车道时，所有重叠车道均输出。无匹配车道时，size为0.
  double lon_gcj;                      // Logitude in GCJ02, unit: [deg].
  double lat_gcj;                      // Latitude in GCJ02, unit: [deg].
  float yaw;                           // 北为0，东为90 (0-360], 为0时表示没有数据, unit: [deg].
  double lon_gps;                      // Logitude in GCJ02, unit: [deg].
  double lat_gps;                      // Latitude in GCJ02, unit: [deg].
  double altitude;                     // Altitude, unit: [m].
  float acc;                           // accuracy, unit: [m].
  float lateral_acc;                   // lateral accuracy, unit: [m].
  float longitudinal_acc;              // longitudinal accuracy, unit: [m].

  float speed;                         // unit: [m/s].
  float vx;                            //vx unit: [m/s].
  float vy;                            //vy unit: [m/s].
  float vz;                            //vz unit: [m/s].
  float acc_x;                         //acc_x unit: [m/s].
  float acc_y;                         //acc_y unit: [m/s].
  float acc_z;                         //acc_z unit: [m/s].
  float angular_velocity_x;            //angular_velocity_x, unit: [deg].
  float angular_velocity_y;            //angular_velocity_y, unit: [deg].
  float angular_velocity_z;            //angular_velocity_z, unit: [deg].
  FailSafeInfo fail_safe;              //FailSafeInfo 功能安全检查 
 
public:
    HDLocInfo() : timestamp(0),  
                 lon_gcj(-700.0), lat_gcj(-700.0), yaw(-700.0), lon_gps(-700.0), lat_gps(-700.0), altitude(-700.0), 
                 acc(-700.0), lateral_acc(-700.0), longitudinal_acc(-700.0), 
                 speed(-700.0), vx(-700.0), vy(-700.0), vz(-700.0), acc_x(-700.0), acc_y(-700.0), acc_z(-700.0), 
                 angular_velocity_x(-700.0), angular_velocity_y(-700.0), angular_velocity_z(-700.0){}

    void Reset() {
        timestamp = 0;
        current_lanes.clear();

        lon_gcj = -700.0;
        lat_gcj = -700.0;
        yaw = -700.0;
        lon_gps = -700.0;
        lat_gps = -700.0;
        altitude = -700.0;
        acc = -700.0;
        lateral_acc = -700.0;
        longitudinal_acc = -700.0;

        speed = -700.0;
        vx = -700.0;
        vy = -700.0;
        vz = -700.0;
        acc_x = -700.0;
        acc_y = -700.0;
        acc_z = -700.0;
        angular_velocity_x = -700.0;
        angular_velocity_y = -700.0;
        angular_velocity_z = -700.0;        
        fail_safe.Reset();
    }
};

__NAMESPACE_MAPBASE_END__

#endif  // MAP_BASE_INCLUDE_COMMON_POS_RESULT_DEFINE_H_
