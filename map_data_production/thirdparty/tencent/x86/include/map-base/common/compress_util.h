// Copyright 2020 Tencent. All rights reserved.

//
// Created by morgan<PERSON>(morgansun) on 2020/6/2.
//

#ifndef MAP_BASE_INCLUDE_COMMON_COMPRESS_UTIL_H_
#define MAP_BASE_INCLUDE_COMMON_COMPRESS_UTIL_H_
#ifdef USE_ZLIB

#include "common/common_define.h"
#include "common/mapbase_export.h"

__NAMESPACE_MAPBASE_BEGIN__

namespace compress {
/**
 * gzip压缩方法
 * @param buf 原始数据流
 * @param len 原始数据流长度
 * @param gzip_buf_len 压缩后数据流长度
 * @return 压缩后数据流，当参数错误或内存空间不足时返回nullptr
 */
MAPBASE_EXPORT std::unique_ptr<int8_t[]> GzipCompress(const int8_t *buf, int32_t len,
                                       uint32_t &gzip_buf_len);
/**
 * gzip解压方法
 * @param gzip_buf 压缩数据流
 * @param gzip_buf_len 压缩数据流长度
 * @param buf_len 解压后数据流长度
 * @return 解压后数据流，当参数错误或格式无法识别时，返回nullptr
 */
MAPBASE_EXPORT std::vector<char> GzipDecompress(const int8_t *gzip_buf, int32_t gzip_buf_len,
                                         int32_t &buf_len);
/**
 * gzip解压方法 gzip-wrapped格式
 * @param gzip_buf 压缩数据流
 * @param gzip_buf_len 压缩数据流长度
 * @param buf_len 解压后数据流长度
 * @return 解压后数据流，当参数错误或格式无法识别时，返回空unique_ptr
 */
MAPBASE_EXPORT std::unique_ptr<int8_t[]> GzipInflate(const int8_t *gzip_buf, int32_t gzip_buf_len,
										int32_t &buf_len);


}  // namespace compress

__NAMESPACE_MAPBASE_END__
#endif

#endif  // MAP_BASE_INCLUDE_COMMON_COMPRESS_UTIL_H_
