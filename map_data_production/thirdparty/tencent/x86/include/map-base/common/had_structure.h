// Copyright 2020 Tencent. All rights reserved.

//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON>(<PERSON><PERSON><PERSON><PERSON><PERSON>) on 2021/4/7.
//

#ifndef MAP_BASE_INCLUDE_COMMON_HAD_STRUCTURE_H_
#define MAP_BASE_INCLUDE_COMMON_HAD_STRUCTURE_H_

#include <vector>
#include "common/common_define.h"
#include "unordered_set"

__NAMESPACE_MAPBASE_BEGIN__
namespace HAD {
///////////////////////////////////
/// tile
//////////////////////////////////
typedef uint32_t TileID;
typedef std::vector<TileID> TileIDVec;

///////////////////////////////////
/// lane
//////////////////////////////////
/**
 * LaneGroupID全局唯一
 */
struct LaneGroupID {
  TileID tile_id{0};
  uint32_t lane_group_id{0};

  bool operator==(const LaneGroupID &other) const {
   return (tile_id == other.tile_id) && (lane_group_id == other.lane_group_id);
  }

  bool operator!=(const LaneGroupID &other) const {
    return !((*this)==other);
  }

  bool operator<(const LaneGroupID &other) const {
   return (tile_id < other.tile_id) ||
     (tile_id == other.tile_id && lane_group_id < other.lane_group_id);
  }

  bool operator>(const LaneGroupID &other) const {
   return (tile_id > other.tile_id) ||
     (tile_id == other.tile_id && lane_group_id > other.lane_group_id);
  }
};

struct HashLaneGroupID {
  size_t operator()(const LaneGroupID& obj) const {
    // 使用 std::hash<uint32_t> 对 a 和 b 进行哈希
    std::hash<uint32_t> hasher;
    size_t hash_a = hasher(obj.tile_id);
    size_t hash_b = hasher(obj.lane_group_id);
    // 结合两个哈希值
    return (hash_a) ^ (hash_b << 1);
  }
};
struct LaneID {
  TileID tile_id{0};
  uint32_t lane_id{0};

  bool operator==(const LaneID &other) const {
   return (tile_id == other.tile_id) && (lane_id == other.lane_id);
  }

  bool operator!=(const LaneID &other) const {
    return !((*this)==other);
  }

  bool operator<(const LaneID &other) const {
   return (tile_id < other.tile_id) ||
     (tile_id == other.tile_id && lane_id < other.lane_id);
  }

  bool operator>(const LaneID &other) const {
   return (tile_id > other.tile_id) ||
     (tile_id == other.tile_id && lane_id > other.lane_id);
  }

  MAPBASE_EXPORT friend std::ostream &operator<<(std::ostream &out, const LaneID &info) {
   out << "(" << info.tile_id
       << "," << info.lane_id
       << ")";
   return out;
  }
};
struct HashLaneID {
  size_t operator()(const LaneID& obj) const {
    // 使用 std::hash<uint32_t> 对 a 和 b 进行哈希
    std::hash<uint32_t> hasher;
    size_t hash_a = hasher(obj.tile_id);
    size_t hash_b = hasher(obj.lane_id);
    // 结合两个哈希值
    return (hash_a) ^ (hash_b << 1);
  }
};

///////////////////////////////////
/// boundary
//////////////////////////////////
struct BoundaryID {
  TileID tile_id{0};
  uint32_t boundary_id{0};

  bool operator==(const BoundaryID &other) const {
   return (tile_id == other.tile_id) && (boundary_id == other.boundary_id);
  }

  bool operator!=(const BoundaryID &other) const {
    return !((*this)==other);
  }

  bool operator<(const BoundaryID &other) const {
   return (tile_id < other.tile_id) ||
     (tile_id == other.tile_id && boundary_id < other.boundary_id);
  }

  bool operator>(const BoundaryID &other) const {
   return (tile_id > other.tile_id) ||
     (tile_id == other.tile_id && boundary_id > other.boundary_id);
  }
};
/**
 * 面边线结构
 */
struct BoundaryData {
  std::vector<GeoCoordinateZ> points;
  std::vector<uint32_t> break_index;
};
/**
 * 描述线上一个任意点的位置
 */
struct BoundaryPosIndex {
  int index = 0;  // 向前的第一个点的索引
  double  ratio = 0.0;  // 点位于两点之间的比率
};
/**
 * 纹理坐标索引
 */
struct TextureIndex {
  uint32_t boundary_point_index = 0;
  float u = .0; // 纹理水平坐标（0.0～1.0）
  float v = .0; // 纹理垂直坐标（0.0～1.0）
};
/**
 * 面纹理
 */
struct MAPBASE_EXPORT RoadAreaData{
  BoundaryData left_boundary;
  std::vector<TextureIndex> left_texture_change_index;

  BoundaryData right_boundary;
  std::vector<TextureIndex> right_texture_change_index;
};

struct MercatorCoord {
    double x;
    double y;
    double z;
};

struct MAPBASE_EXPORT RoadAreaProData {
  std::vector<MercatorCoord> left_boundary;
  std::vector<uint32_t> left_break_indices;
  std::vector<TextureIndex> left_texture_change_index;

  std::vector<MercatorCoord> right_boundary;
  std::vector<uint32_t> right_break_indices;
  std::vector<TextureIndex> right_texture_change_index;
};

struct AlphaCtrlItem
{
    bool repeat = false;
    float step[3] = {0, 0, 1.0}; // 0-1 delta。0:U方向，1:V方向，2:整体
    float animationTimeInSec = 0;    // 时长s
};

struct UVCtrlItem
{
    bool repeat = false;        // 是否循环播放动画
    float uvStep[2] = {0,0};    // 米,废弃不用了
    float animationTime = 0;// 时长单位：s,箭头从头到尾流动完一次需要的时间
    bool reset = false;
};

struct FadeinInfo {
    int boundary_type;         //0:左边线索引 1:右边线索引
    BoundaryPosIndex start;
    BoundaryPosIndex end;
    long long color;    // rgba 需设计调一个合适颜色值，同面纹理无关
    float animation_time = 0;    //渐现动画时长
    float fade_distance;// 颜色过渡的距离,单位米
};

enum RelationShipType
{
   kCrossLane,  // 在标线下
   kInLane      // 在标线上
};

struct LaneGradual {
    float ratio_; // 范围0-1, 在道路lanegroup中的占比
    float gradual_value_; // 范围0-1，整条渐变数据中的占比,当值为1时，标识不透明，为0时标识要进行透明操作
};

struct LaneFishBoneData { // 鱼骨数据
    LaneGradual start_;
    LaneGradual end_;
};

}
__NAMESPACE_MAPBASE_END__

#endif  // MAP_BASE_INCLUDE_COMMON_HAD_STRUCTURE_H_
