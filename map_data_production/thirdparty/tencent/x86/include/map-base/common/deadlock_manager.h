#pragma once

#include "base_mutex.h"
#include "mutex.h"
#include "message_reporter.h"
#include "node.h"
#include <vector>

namespace tencentmap {

class MAPBASE_EXPORT DeadlockManager
{
public:
    DeadlockManager();
    ~DeadlockManager();
    
    // 获取锁失败时，添加阻塞信息
    void add(void* lock, uint64_t blocked_thread,
             uint64_t owner_thread,
             const char *owner_file,
             int owner_line,
             const char *blocked_file,
             int blocked_line);
    
    // 加锁成功时，移除相关阻塞信息
    void remove(uint64_t blocked_thread);
    
    // 加锁失败时，探测m_blocked_mutexes可能的死锁
    void detect(uint64_t blocked_thread,
                std::vector<std::vector<Node> > &deadlock_links);
    
    
    void setReporter(std::shared_ptr<MessageReporter> reporter) {m_reporter_ = reporter; };
    
    void reportMsg(const std::string& msg);
    
    void setLockDeepWarning(int deep) {
        m_lock_deep_warning_ = deep;
    };
    
    int getLockDeepWarning() {
        return m_lock_deep_warning_;
    }
    
    void setLockTimeoutWarning(int timeout) {
        m_lock_timeout_warning_ = timeout;
    };
    
    int getLockTimeoutWarning() {
        return m_lock_timeout_warning_;
    }
private:
    
    Node *find_next_jump(Node *from_mutex);
    
private:
    std::vector<Node> m_blocked_mutexes_; // 阻塞住线程的所有锁信息
    int m_lock_deep_warning_{5}; // 锁深度报警
    int m_lock_timeout_warning_{500}; // 锁超时报警，单位ms
    
    std::shared_ptr<MessageReporter> m_reporter_;
};
}
