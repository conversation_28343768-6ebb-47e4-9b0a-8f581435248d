//
// Created by cuipanpan on 2023/10/12.
//

#pragma once
#include "mapbase_export.h"
#include "message_reporter.h"
#include "deadlock_scheduler.h"
#include "module_type.h"
#include <set>
#include "MapLogger.h"
namespace tencentmap {

class MAPBASE_EXPORT MessageReporterImpl : public tencentmap::MessageReporter{
public:
    virtual void report(const std::string& msg){};
    virtual void writeToFile(const std::string& msg){
        xerror2("DEADLOCK %s",msg.c_str());
    };
};
MAPBASE_EXPORT void InitDeadlockConfig() {
    tencentmap::DeadLockScheduler* sched = tencentmap::DeadLockScheduler::getInstance();
    if (sched == NULL) return;
    std::shared_ptr<MessageReporterImpl> report = std::make_shared<MessageReporterImpl>();
    std::set<tencentmap::MODULETYPE> modules;
    modules.insert(tencentmap::MODULETYPE::kMap);
    sched->setCheckModules(modules, true, 5, 1000, std::dynamic_pointer_cast<tencentmap::MessageReporter>(report));
}
}
