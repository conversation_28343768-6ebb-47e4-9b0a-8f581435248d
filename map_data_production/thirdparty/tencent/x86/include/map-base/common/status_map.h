// Copyright 2020 Tencent. All rights reserved.

//
// Created by morgan<PERSON>(morgansun) on 2020/6/30.
//

#ifndef MAP_BASE_INCLUDE_COMMON_STATUS_MAP_H_
#define MAP_BASE_INCLUDE_COMMON_STATUS_MAP_H_

#include <atomic>
#include <map>
#include "common/mapbase_export.h"

namespace mapbase {

class MAPBASE_EXPORT BaseStatus {
 public:
  /**
   * 如果存在竞态，则只会有一个返回成功
   * @param value 设置的状态值
   * @return 是否设置成功
   */
  bool SetValue(int value) {
    int v = value_.load(std::memory_order_acquire);
    return value_.compare_exchange_strong(v, value, std::memory_order_release);
  }

  bool CheckAndSet(int expect, int value) {
    return value_.compare_exchange_strong(expect, value, std::memory_order_release);
  }

 private:
  std::atomic<int> value_{0};
};

class MAPBASE_EXPORT StatusMap {
 public:
  /**
   * 注册状态对
   * @param status 状态名,key
   * @param default_value 默认的状态值
   * @warning 该方法不是线程安全的
   */
  void Register(const std::string& status, int default_value);
  /**
   * 设置状态
   * @param status 状态名,key
   * @param value 默认的状态值
   * @return
   */
  void SetStatus(const std::string& status, int value);
  /**
   * 检查并设置状态
   * @param status 状态名，key
   * @param expect
   * @param value
   * @return
   */
  bool CheckAndSet(const std::string& status, int expect, int value);
  /**
   * 检查状态
   * @param status 状态名,key
   * @param expect 期望的状态
   * @return
   */
  bool Check(const std::string& status, int expect);

 private:
  std::map<std::string, BaseStatus> status_map_;
};

}  // namespace mapbase

#endif  // MAP_BASE_INCLUDE_COMMON_STATUS_MAP_H_
