#pragma once
#include "mapbase_export.h"
#include "module_type.h"
#include "message_reporter.h"
#include "node.h"
#include <memory>
#include <mutex>
#include <set>
#include <map>
namespace tencentmap {

class DeadlockManager;

class MAPBASE_EXPORT DeadLockScheduler {
public:
    static DeadLockScheduler* getInstance();
    void setCheckModules(std::set<MODULETYPE>& modules,bool enableDeadLock,int deepWarning, int timeoutWarning, std::shared_ptr<MessageReporter> report); // 设置一起检测的模块集合
    DeadLockScheduler();
    // 下面接口内部使用
    // 获取锁失败时，添加阻塞信息
    void add(void* lock, uint64_t blocked_thread,
             uint64_t owner_thread,
             const char *owner_file,
             int owner_line,
             const char *blocked_file,
             int blocked_line);
    
    // 加锁成功时，移除相关阻塞信息
    void remove(MODULETYPE type, uint64_t blocked_thread);
    
    // 加锁失败时，探测m_blocked_mutexes可能的死锁
    void detect(MODULETYPE type, uint64_t blocked_thread,
                std::vector<std::vector<Node> > &deadlock_links);
    
    bool isEnabled(MODULETYPE type);
    
    void reportMsg(MODULETYPE type, const std::string& msg);
    
    void setLockDeepWarning(MODULETYPE type,int deep);
    int getLockDeepWarning(MODULETYPE type);
    
    void setLockTimeoutWarning(MODULETYPE type,int timeout);
    int getLockTimeoutWarning(MODULETYPE type);
    
    std::shared_ptr<DeadlockManager> getDeadlockManagerByType(MODULETYPE type);
    
private:
    static DeadLockScheduler* m_deadlock_scheduler_;
    static pthread_mutex_t m_lock_;
    std::map<MODULETYPE, std::shared_ptr<DeadlockManager>> m_managers_;
    bool m_deadlock_enable_[kMapNum];
};
}
