#pragma once
#include "mapbase_export.h"
#include "common/deadlock_scheduler.h"
#include <string>
#include <pthread.h>
#include <thread>
#include "module_type.h"
#include <sys/syscall.h>
#include <unistd.h>
#define INFINITY_WAIT -1
namespace tencentmap {

inline uint64_t getTid() {
    uint64_t ret = 0;
#if defined(__linux__) || defined(__ANDROID__)
    ret = syscall(SYS_gettid);
#elif defined(__APPLE__)
    pthread_threadid_np(NULL, &ret);
#endif
    return ret;
}

class MAPBASE_EXPORT BaseMutex {
protected:
    BaseMutex();
public:
    virtual ~BaseMutex();
    virtual bool timedLock(int timeout = INFINITY_WAIT){ return true; };
    virtual void unlock(){};
    virtual void collect_blocked_items(int tid, const char* file,int line);
    virtual void* getMutexId() {
        return this;
    }
    
    MODULETYPE getModuleType() {
        return m_type_;
    }
    
    // 外部都通过这里加锁
    bool lockWithCheck(const char* file, int line, int timeout);
    
    // 外部都通过这里解锁
    void unlockWithCheck(const char* file, int line);
    
    void releaseLockData(const char* file, int line);
    void recordLockData(const char* file, int line);
    
protected:
    bool m_is_locked_{false}; // 是否被加锁
    bool m_is_recursive_{false};// 是否是递归锁
    uint64_t m_owner_thread_; // 当前锁的持有线程
    int m_lock_deep_{0}; // 锁的深度
    const char* m_file_; // 加锁的文件
    int m_line_; // 加锁文件的行号
    bool m_enable_deadLock_{false}; // 当前锁是否参与死锁检测
    uint64_t m_enter_time_; // 获取锁的时间点
    
    MODULETYPE m_type_{kMapNum}; // 锁属于的模块
    
};

}
