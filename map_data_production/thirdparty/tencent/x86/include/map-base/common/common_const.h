// Copyright 2020 Tencent. All rights reserved.

//
// Created by morgan<PERSON>(morgansun) on 2020-02-15.
//

#ifndef MAP_BASE_INCLUDE_COMMON_COMMON_CONST_H_
#define MAP_BASE_INCLUDE_COMMON_COMMON_CONST_H_

#include "common/mapbase_export.h"
#include <vector>
namespace mapbase {

/**
 * 网络模式
 * 该模式与`NetworkPolicy`不同，为实际执行模式
 */
enum class MAPBASE_EXPORT NetworkMode {
  /**
   * 在线模式
   */
  Online,
  /**
   * 离线模式
   */
  Offline
};

/**
 * 网络策略类型
 */
enum class MAPBASE_EXPORT NetworkPolicy {
  /**
   * 在线优先模式
   * 首选在线模式，失败后重试离线模式
   */
  OnlinePriority,
  /**
   * 离线优先模式
   * 首选离线模式，失败后重试在线模式
   */
  OfflinePriority,
  /**
   * 仅在线模式
   * 仅使用在线模式，失败后返回
   */
  OnlineOnly,
  /**
   * 仅离线模式
   * 首选离线模式，失败后返回
   */
  OfflineOnly,
};

/**
 * 离线路网道路等级
 */
enum class MAPBASE_EXPORT OfflineRoadFuncClass {
  Null = -1,          /**< 无效值 */
  Highway = 0,        /**< 高速路 */
  CitySpeedWay = 1,   /**< 都市高速路 */
  NationalRoad = 2,   /**< 国道 */
  ProvincialRoad = 3, /**< 省道 */
  CountyRoad = 4,     /**< 县道 */
  TownshipRoad = 5,   /**< 乡镇道路 */
  Pedestrain = 8,     /**< 行人道路、人渡、人行道 */
  OtherRoad = 9,      /**< 其它道路 */
  Ferry = 10,         /**< 轮渡 */
  Level9Road = 11     /**< 九级路 */
};

/** 在线路网道路等级，来自数据规格 */
enum class MAPBASE_EXPORT OnlineRoadFuncClass {
  Null = -1,          /**< 无效值 */
  HighWay = 0x00,     /**< 高速 */
  FastWay = 0x01,     /**< 城市快速路 */
  NationWay = 0x02,   /**< 国道 */
  ProvinceWay = 0x03, /**< 省道 */
  CountryWay = 0x04,  /**< 县道 */
  TownWay = 0x06,     /**< 乡村公路 */
  OtherWay = 0x08,    /**< 其它道路 */
  NineWay = 0x09,     /**< 九级道路（非引导道路） */
  FerryWay = 0x0a,    /**< 轮渡 */
  FootWay = 0x0b,     /**< 行人道路（步行道路） */
  TravellerWay = 0x0c,/**< 人渡 */
  Bicycle = 0x0d,     /**< 自行车专用路 */
  RopeWay = 0x0e,     /**< 索道 */
};

/** 离线道路属性 */
enum class MAPBASE_EXPORT OfflineRoadKind {
  Null = -1,       /**< 无效值 */
  Roundabout = 0,  /**< 环岛 */
  Normal = 1,      /**< 普通道路 */
  Separate = 2,    /**< 上下线分离道路 */
  JCT = 3,         /**< 高速之间的连接道路 */
  Inner = 4,       /**< 内部路 */
  Ramp = 5,        /**< 匝道 */
  PA = 6,          /**< 停车区 */
  SA = 7,          /**< 服务区 */
  RightTurn = 8,   /**< 起提前右转弯作用的道路 */
  LeftTurn = 9,    /**< 起提前左转弯作用的道路 */
  Uturn = 10,      /**< 调头路 */
  IC = 11,         /**< 连接高速和其它不同等级道路之间的道路 */
  MainSideIC = 12, /**< 辅主辅路出入口 */
  Tunnel = 13,     /**< 隧道 */
  Elevated = 14,   /**< 高架路 */
  SideRoad = 15,   /**< 辅路 */
  Bridge = 16      /**< 桥梁 */
};

/** 道路属性 */
enum class MAPBASE_EXPORT OnlineRoadKind {
  None = 0x01,                 /**< 普通道路 */
  OneWay = 0x02,               /**< 单向通行道路 */
  CrossInnerLink = 0x04,       /**< 交叉点内道路 */
  IC = 0x05,                   /**< IC */
  JCT = 0x03,                  /**< JCT */
  Tunnel = 0x0f,               /**< 隧道 */
  ServiceArea = 0x07,          /**< 服务区 */
  Parking = 0x06,              /**< 停车场 */
  SecondRoad = 0x0a,           /**< 辅路 */
  Roundabout = 0x00,           /**< 环岛 */
  Bridge = 0x08,               /**< 桥梁 */
  WalkStreet = 0x09,           /**< 步行街 */
  Ramp = 0x0b,                 /**< 匝道 */
  GuidrailRoad = 0x0c,         /**< 全封闭道路 */
  UnknownArea = 0x0d,          /**< 未定义交通区域 */
  POIConnection = 0x0e,        /**< POI连接路 */
  BusWay = 0x11,               /**< 公交专用道 */
  RightTurnRoad = 0x12,        /**< 提前右转 */
  ViewRoad = 0x13,             /**< 风景线路 */
  InnerRoad = 0x14,            /**< 内部道路 */
  LeftTurnRoad = 0x15,         /**< 提前左转 */
  TurnBackRoad = 0x16,         /**< 掉头口 */
  MainSecondConnection = 0x17, /**< 主辅路出入口 */
  ParkingConnection = 0x19,    /**< 停车位引导路 */
  VirtualConnection = 0x18,    /**< 虚拟连接路 */
  ParkingEntrance = 0x1a,      /**< 停车场出入口连接路 */
  MobileBridge = 0x1b          /**< 移动式桥 */
};

/** 节点POI类型 */
enum class MAPBASE_EXPORT NodePOIType {
  Invalid = 0x00000000, /**< 无效 */
  Park = 0x00000001,    /**< 停车场 */
  InArea = 0x00000002,  /**< 小区 */
  SA = 0x00000004,      /**< 服务区 */
  PA = 0x00000008,      /**< 停车区 */
};

/** 路况类型 (取值跟底图路况颜色值一致) */
enum class MAPBASE_EXPORT RoadCondState {
  Clear = 0,      /**< 道路畅通 */
  Slow = 1,       /**< 道路缓行 */
  Block = 2,      /**< 道路拥堵 */
  None = 3,       /**< 无路况 */
  HeavyBlock = 4, /**< 道路严重拥堵 */
};

enum class MAPBASE_EXPORT ForbiddenYawBoundType {
  Invalid, /**< 无效类型 */
  Yaw,     /**< 偏航算路Bound请求结果 */
  SA,      /**< 在线算路下发,服务区 */
  Toll,    /**< 在线算路下发，收费站 */
};

enum class MAPBASE_EXPORT RoadHintType {
  None = 0,
  Elevator = 1,                /**< 在高架上 */
  UnderElevator = 2,           /**< 在高架下 */
  MainRoad = 3,                /**< 在主路 */
  SecondRoad = 4,              /**< 在辅路 */
  ReverseDirection = 5,        /**< 反向道路 */
  UnderElevatorMainRoad = 6,   /**< 在高架下主路 */
  UnderElevatorSecondRoad = 7, /**< 在高架下辅路 */
};

enum class MAPBASE_EXPORT RouteCondType {
  None = 0, /**< 未知 */
  Clear,    /**< 畅通 */
  Slow,     /**< 缓行 */
  Jam,      /**< 拥堵 */
  HeavyJam  /**< 严重拥堵 */
};

/**
 * 路口放大图类型(优先级：实景图>矢量>4K>模式图>DM)
 */
enum class MAPBASE_EXPORT GuidePictureType {
  None = 0,
  HighwayOutScene = 11,         //高速出口实景图
  HighwayInScene = 12,          //高速入口实景图
  IntersectionScene = 13,       //普通路口实景图
  HighwayConfusedPattern = 14,  //高速道路分歧模式图
  RoadConfusedPattern = 15,     //普通道路分歧模式图
  DM = 20,
  FourK = 130100,  // 4k放大图
  Vector = 20000   // 矢量放大图
};

/**
 * 当 对应routeguiidance::RouteGuidanceAccessoryPoint type 为 WarningSigns 时
 * 对应routeguiidance::RouteGuidanceAccessoryPoint sub_type字段
 */
enum class MAPBASE_EXPORT RouteGuidanceWarningType {
  None = 0,                     // 无效值
  SharpLeftRoad = 1,            // 向左急弯路
  SharpRightRoad = 2,           // 向右急弯路
  ReverseRoadLeft = 3,          // 反向弯路（左）!
  ContinuousDetour = 4,         // 连续弯路
  UpSteepSlope = 5,             // 上陡坡
  DownSteepSlope = 6,           // 下陡坡
  BothNarrow = 7,               // 两侧变窄
  RightNarrow = 8,              // 右侧变窄
  LeftNarrow = 9,               // 左侧变窄
  NarrowBridge = 10,            // 窄桥
  OppositeRoad = 11,            // 双向交通 !
  MindChildren = 12,            // 注意儿童
  MindLivestock = 13,           // 注意牲畜 !
  MindRock = 14,                // 注意落石
  MindRockEx = 15,              // 注意落石
  MindCrosswind = 16,           // 注意横风 !
  SlipperyRoad = 17,            // 易滑
  AlongMountain = 18,           // 傍山险路 !
  AlongMountainEx = 19,         // 傍山险路 !
  DamRoad = 20,                 // 堤坝路   !
  DamRoadEx = 21,               // 堤坝路   !
  Village = 22,                 // 村庄
  CamelBackBridge = 23,         // 驼峰桥
  UnevenRoad = 24,              // 路面不平
  WaterRoad = 25,               // 过水路面  !
  RailwayCrossingManaged = 26,  // 有人看守铁路道口
  RailwayCrossingWild = 27,     // 无人看守铁路道口
  AccidentProneSection = 28,    // 事故易发路段
  Detour = 29,                  // 左右绕行 !
  DetourLeft = 30,              // 左侧绕行 !
  DetourRight = 31,             // 右侧绕行 !
  Caution = 32,                 // 注意危险 !
  NoOvertake = 33,              // 禁止超车
  AllowOvertake = 34,           // 解除禁止超车
  Honking = 35,                 // 鸣喇叭
  ContinuousDownSlope = 36,     // 连续下坡
  TextWarning = 37,             // 文字性警示标牌，现场为文字提示，且无法归类到国标危险信息标牌中 !
  MindLeftMerge = 38,              // 注意左侧合流
  MindRightMerge = 39,             // 注意右侧合流
  StopGiveway = 40,                // 停车让行 !
  EncounterGiveway = 41,           // 会车让行 !
  SlowdownGiveway = 42,            // 减速让行 !
  TurnOnLightInTunnel = 43,        // 隧道开灯
  TidalLane = 44,                  // 潮汐车道 !
  ProtrudingRoad = 45,             // 路面高凸 !
  SinkingRoad = 46,                // 路面低洼 !
  ReverseRoadRight = 47,           // 反向弯路（右） !
  TrafficAccidentBlackSpots = 48,  // 事故多发
  LaneSlideSlop = 49,              // 滑坡
  DirtRoad = 50,                   // 泥石路
  GroundSink = 51,                 // 地面塌方
  Collapse = 52,                   // 塌方
  QuarantineStation = 98,          // 检疫站
  Zebra = 99                       // 斑马线
};

/**
 * 诱导电子眼类型：和数据规格映射表<br/>
 * https://git.woa.com/map/GuideCompile/blob/master/guide_block_compile2/conf/camera_type_shoutu.res<br/>
 * 当 对应routeguiidance::RouteGuidanceAccessoryPoint type 为 Camera时<br/>
 * 对应routeguiidance::RouteGuidanceAccessoryPoint sub_type字段
 */
enum class MAPBASE_EXPORT GuidanceCameraType {
  None = 0,                 /**<无类型*/
  RedLight = 1,             /**<闯红灯照相*/
  ElectronicMonitoring = 2, /**<电子监控*/
  FixedSpeedTraps = 3,      /**<固定测速点*/
  MobileSpeedZone = 4,      /**<移动测速区*/
  BusOnlyWay = 5,           /**< 公交*/
  OneWay = 6,               /**< 单行*/
  EmergencyWay = 7,         /**< 应急*/
  NoneMotorWay = 8,         /**< 非机动车*/
  QujianEnter = 9,          /**<区间测速进入点*/
  QujianExit = 10,          /**<区间测速退出点*/
  HOV = 16,                 /**<多乘员专用道监控.*/
  LaLian = 17,              /**<拉链通行道路监控.*/
  TailNumber = 21,          /**<尾号限行，*/
  GoToBeijing = 22,         /**<外地车进京监控,*/
  IllegalBlow = 23,         /**<违法鸣笛摄像头*/
  BusStation = 24,          /**<公交车站摄像头*/
  ForbiddenTure = 30,       /**<禁止左右转*/
  ForbiddenLine = 31,       /**<违反禁止标线*/
  ForbiddenParking = 32,    /**<违章停车*/
  LowestSpeed = 33,         /**<超低速*/
  AlterableSpeed = 34,      /**<可变限速*/
  LaneSpeed = 35,           /**<分车道限速*/
  VehicelTypeSpeed = 36,    /**<分车种限速*/
  LaneOccupy = 37,          /**<违章占车道*/
  Crossing = 38,            /**<违章过路口*/
  ForbiddenSign = 39,       /**<违反禁令标志*/
  ForbiddenLight = 40,      /**<违规用灯*/
  LifeBelt = 41,            /**<不系安全带*/
  ForbiddenCall = 42,       /**<开车打手机*/
  LimitLine = 43,           /**<环保限行*/
  PedestrainFirst = 44,     /**<礼让行人*/
  AnnualInpection = 45,     /**<车辆未按规定年检*/
  VehicelExhaust = 46,      /**<车辆尾气超标*/
  Traffic = 47,             /**<路况监控*/
  Entrance = 48,            /**<出入口摄像头*/
  ForbiddenUTurn = 49,      /**<禁止掉头摄像头*/
  EtcToll = 50,             /**<etc收费电子眼*/
  NotFollowGuideLane  = 51, /**<不按导向车道行驶*/
  TrafficFlowMonitor   = 52, /**<流量监控（车流量）*/
  KeepSafeDistance = 53,     /**保持安全距离*/
  IllegalChangeLane = 54     /**违法变道*/
};
/**
 * 对应routeguiidance::RouteGuidanceAccessoryPoint type字段
 */
enum class MAPBASE_EXPORT GuidanceTipType {
  None = 0,
  GasStation = 1,
  SeatingArea = 2,
  Camera = 4,
  TollStation = 5,
  JunctionPoint = 6,
  TunnelEntrance = 7,
  ForceGuidance = 8,
  RoundaboutExit = 9,
  WarningSigns = 10,
  Straight = 11,
  ICJCT = 12,
  InnerRoad = 13,
  SpecialGuidance = WarningSigns + 10,
};

enum class MAPBASE_EXPORT GuidanceEnlargeMapType {
  None = 0,
  HighwayOutScene = 11,        /**< 高速出口实景图 */
  HighwayInScene = 12,         /**< 高速入口实景图 */
  IntersectionScene = 13,      /**< 普通路口实景图 */
  HighwayConfusedPattern = 14, /**< 高速道路分歧模式图 */
  NormalConfusedPattern = 15,  /**< 普通道路分歧模式图 */
  DM = 20,
  FourK = 130100, /**<  4K放大图 */
  Vector = 20000  /**<  矢量放大图 */
};

enum class MAPBASE_EXPORT CompanionRouteType {
  None = 0,                /**< 非法类型 */
  RecommendAvoidLimit = 1, /**< 推荐换路，避免限行 */
  RecommendAvoidJam = 2,   /**< 推荐换路，避免拥堵 */
  SilenceFast = 3,         /**< 伴随更新，新路线较快 */
  SilenceSlow = 4          /**< 伴随更新，新路线较慢 */
};

enum class MAPBASE_EXPORT RoutePlanLimitStatus {
    LimitStatusNoLimit = 0, /**< 不限行 */
    LimitStatusThroughLimitArea = 1, /**< 途径有限行规定的城市 */
    LimitStatusMayAvoidLimit = 2, /**< 可以避开限行区域 */
    LimitStatusLimitAreaAvoided = 3,  /**< 已经避开限行区域 */
    LimitStatusLimitAreaCannotAvoided = 4, /**< 无法避开限行区域 */
};

enum class MAPBASE_EXPORT LanguageType {
    LANGUAGE_NONE, /**< 未知类型 */
    LANGUAGE_CN,   /**< 中文简体 */
    LANGUAGE_TC,   /**< 中文繁体 */
    LANGUAGE_EN,   /**< 英文 */
    LANGUAGE_PT    /**< 葡萄牙语 */
};

/**
 * 导航地图场景类型
 * 暂时用于区分传统导航/车道级导航，仅用于控制自动比例尺类型
 */
enum class MAPBASE_EXPORT MapSceneType {
  MapScene_SD            = 0, // 传统导航地图
  MapScene_HD            = 1, // 高精导航地图
  MapScene_HD_TP         = 1, // 高精导航地图，tp线（已废弃）
  MapScene_HD_GUIDE_AREA = 2, // 高精引导面导航（已废弃）
};

/**
 * 驾驶模式类型
 * (待明确)
 */
enum class MAPBASE_EXPORT DriveModeType {
  DriveMode_None    = 0,     // 人工驾驶模式
  DriveMode_HWP     = 1,      // 自动驾驶模式
  DriveMode_LCC     = 2,     // LCC驾驶模式
  DriveMode_ACC     = 3,     // ACC驾驶模式
};

enum class MAPBASE_EXPORT UrlKey {
    SDK_URL_KEY = 0,           /**< SDK内部使用的后台服务 */
    OFFLINE_DATA_DOWNLOAD_URL_KEY = 1          /**< 离线数据下载服务 */
};

enum class RegionType {
  Mainland = 0, /**< 大陆 */
  Hongkong, /**< 香港 */
  Taiwan,   /**< 台湾 */
  Macao     /**< 澳门 */
};

enum class TruckPlateColor {
  COLOR_INVALID = 0, /**< 无效 */
  COLOR_BLUE = 1,    /**< 蓝色 */
  COLOR_YELLOW = 2,  /**< 黄色 */
  COLOR_BLACK = 3,   /**< 黑色 */
  COLOR_WHITE = 4,   /**< 白色 */
  COLOR_GREEN = 5,   /**< 绿色 */
  COLOR_YELLOW_GREEN = 6  /**< 黄绿色 */
};

enum class TruckType {
  TRUCK_INVALID = 0,  /**< 无效 */
  TRUCK_MINI = 1,     /**< 微型 */
  TRUCK_LIGHT = 2,    /**< 轻型 */
  TRUCK_MEDIUM = 3,   /**< 中型 */
  TRUCK_HEAVY = 4     /**< 重型 */
};

enum class TruckTrailerType {
  TRAILER_INVALID = 0,      /**< 无效 */
  TRAILER_FLATBED = 1,      /**< 平板 */
  TRAILER_CONTAINER = 2,    /**< 箱货 */
  TRAILER_SEMITRAILER = 3,  /**< 半挂 */
  TRAILER_FULL = 4          /**< 全挂 */
};

enum class TruckEnergyType {
  ENERGY_INVALID = 0,    /**< 无效 */
  ENERGY_DIESEL = 1,     /**< 柴油 */
  ENERGY_HYBRID = 2,     /**< 混合能源 */
  ENERGY_ELECTRIC = 3,   /**< 电动 */
  ENERGY_GAS = 4         /**< 天然气 */
};

enum class TruckFunctionType {
  FUNC_NONE = 0,   /**< 无 */
  FUNC_DANGER = 1  /**< 危险品 */
};

enum class EmissionStandard {
  EMISSION_NONE = 0,  /**< 无 */
  EMISSION_I = 1,     /**< 国I */
  EMISSION_II = 2,    /**< 国II */
  EMISSION_III = 3,   /**< 国III */
  EMISSION_IV = 4,    /**< 国IV */
  EMISSION_V = 5,     /**< 国V */
  EMISSION_VI = 6     /**< 国VI */
};

enum class PassType {
  PASS_INVALID = 0,  /**< 无需考虑通行证 */
  PASS_HAVE = 1,     /**< 有通行证 */
  PASS_NO = 2,       /**< 无通行证 */
  PASS_PRE = 3       /**< 预约通行证 */
};

enum class MAPBASE_EXPORT UgsTtsMode {
  UGS_TMC_DEFAULT = 0,              //默认播报模式, 熟路开关打开
  UGS_TMC_AUTO_FAMILIAR_OFF = 1,    //关闭自动熟路开关
};

enum class CoordSysType {
    GCJ02 = 0, /**< 国测局坐标系 */
    WGS84, /**< 地球坐标系 */
};

enum class MAPBASE_EXPORT Currency {
  RMB = 0,   // 人民币
  HKD = 1,   // 港元
  MOP = 2,   // 澳门币
  NT = 3,    // 新台币
};
/**
 * 映射离线道路等级到在线
 * @param road_class MAPBASE_EXPORT
 * @return
 */
OnlineRoadFuncClass RoadClassMatch(OfflineRoadFuncClass road_class MAPBASE_EXPORT);
/**
 * 计算整形代表的道路类型
 * 服务器在下发道路等级时，会采用1<<N|1<<M的方式下发
 * @param kind
 * @return
 */
std::vector<OnlineRoadKind> GetOnlineRoadKind(int kind);
/**
 * 映射离线道路类型到在线
 * @param road_kind
 * @return
 */
OnlineRoadKind RoadKindMatch(OfflineRoadKind road_kind);

}  // namespace mapbase
enum class MAPBASE_EXPORT HDResStatus {
    HD_RES_MISMATCH_SERVER      = -1,       //未支持高精功能的服务
    HD_RES_SUCCESS              = 0,        //成功
    HD_RES_REQ_NOT_REQUIRED     = 1,        //请求不需要高精数据，未携带高精请求参数
    HD_RES_FILTER_BY_FIR_RULE   = 2,        //一级云控过滤
    HD_RES_FILTER_BY_SEC_RULE   = 3,        //二级云控过滤
    HD_RES_DATA_MISS            = 4,        //无高精数据/SHR错误导致路线上无HD数据
    HD_RES_TP_DATA_DISCON       = 5,        //数据区域不连续
    HD_RES_TP_TOPO_LOWQUA       = 6,        //数据拓扑关系差
    HD_RES_BUILD_TIMEOUT        = 7,        //数据生成计算超时
};

#endif  // MAP_BASE_INCLUDE_COMMON_COMMON_CONST_H_
