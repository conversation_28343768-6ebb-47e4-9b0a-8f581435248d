// Copyright 2020 Tencent. All rights reserved.

//
// Created by xiashuangrong on 2020/6/18.
//

#ifndef MAP_BASE_INCLUDE_COMMON_OFFLINE_DATA_OBSERVER_H_
#define MAP_BASE_INCLUDE_COMMON_OFFLINE_DATA_OBSERVER_H_

#include "common/mapbase_export.h"

#include <string>

namespace mapbase {

class MAPBASE_EXPORT OfflineDataObserver {
 public:
  enum class DataType { Render, Poi, Route, Index };
  enum class UpdateType { Add, Delete, Update };

 public:
  virtual ~OfflineDataObserver() {}

 public:
  /**
   * 开始更新某城市数据
   * @param district     城市
   * @param data_type    数据类型
   * @param update_type  变更类型
   */
  virtual void OnBeforeDataUpdate(const std::string &district, DataType data_type,
                                  UpdateType update_type) = 0;

  /**
   * 结束更新某城市数据
   * @param district     城市
   * @param data_type    数据类型
   * @param update_type  变更类型
   */
  virtual void OnAfterDataUpdate(const std::string &district, DataType data_type,
                                 UpdateType update_type) = 0;

  /**
   * 开始数据目录变更
   * @param data_path  新数据目录
   */
  virtual void OnBeforeDataPathChanged(const std::string &data_path) = 0;

  /**
   * 结束数据目录变更
   * @param data_path  新数据目录，如果目录切换失败返回原来的目录
   */
  virtual void OnAfterDataPathChanged(const std::string &data_path) = 0;
};

}  // namespace mapbase

#endif  // MAP_BASE_INCLUDE_COMMON_OFFLINE_DATA_OBSERVER_H_
