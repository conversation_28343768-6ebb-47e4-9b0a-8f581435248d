#pragma once

#include "message_reporter.h"
#include <vector>

namespace tencentmap {

class MAPBASE_EXPORT Node{
public:
    Node() :
    base_lock_(NULL),
    owner_thread_(0),
    owner_file_(NULL),
    owner_line_(0),
    blocked_thread_(0),
    blocked_file_(NULL),
    blocked_line_(0){}
    
    Node(const Node & other) :
    base_lock_(other.base_lock_),
    owner_thread_(other.owner_thread_),
    blocked_thread_(other.blocked_thread_),
    owner_file_(other.owner_file_),
    owner_line_(other.owner_line_),
    blocked_file_(other.blocked_file_),
    blocked_line_(other.blocked_line_){}
    
    Node(void * checkable_lock,
                 uint64_t owner_thread,
                 uint64_t blocked_thread,
                 const char *owner_file,
                 int owner_line,
                 const char *blocked_file,
                 int blocked_line) :
    base_lock_(checkable_lock),
    owner_thread_(owner_thread),
    blocked_thread_(blocked_thread),
    owner_file_(owner_file),
    owner_line_(owner_line),
    blocked_file_(blocked_file),
    blocked_line_(blocked_line) {}
    
public:
    void* base_lock_; // 具体锁
    uint64_t owner_thread_; // 持有锁的线程
    uint64_t blocked_thread_; // 等待锁的线程
    const char* owner_file_; // 获取锁的文件
    int owner_line_; // 获取锁的行号
    const char* blocked_file_; // 等待锁的文件
    int blocked_line_; // 等待锁的行号
};
}
