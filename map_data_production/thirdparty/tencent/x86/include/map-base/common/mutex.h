#pragma once
#include "base_mutex.h"
#include "module_type.h"
#include <iostream>
namespace tencentmap {

class MAPBASE_EXPORT InternalMutex : public BaseMutex{
public:
    explicit InternalMutex(int mutex_type);

    virtual ~InternalMutex();
    
public:
    // true: 加锁失败, false: 加锁成功
    virtual bool timedLock(int timeout /*ms*/);
    
    virtual void unlock();

    pthread_mutex_t* getHandle() {
        return &m_mutex_;
    }
private:
    pthread_mutex_t m_mutex_;
};

class MAPBASE_EXPORT MapMutex : public InternalMutex{
private:
    MapMutex(const MapMutex&);
    MapMutex& operator=(const MapMutex&);
public:
    MapMutex(MODULETYPE type,bool enable_dead_lock);
};

class MAPBASE_EXPORT RecursiveMutex : public InternalMutex{
private:
    RecursiveMutex(const RecursiveMutex&);
    RecursiveMutex& operator=(const RecursiveMutex&);
public:
    RecursiveMutex(MODULETYPE type,bool enable_dead_lock);
};

}
