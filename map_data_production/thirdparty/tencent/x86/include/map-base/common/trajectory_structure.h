//
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/4/12.
//

#ifndef MAPBASE_INCLUDE_COMMON_COMMON_PERCEPTION_H_
#define MAPBASE_INCLUDE_COMMON_COMMON_PERCEPTION_H_

#include "common/common_define.h"
#include "common/had_structure.h"

__NAMESPACE_MAPBASE_BEGIN__

/**
 * 障碍物类型
 */
enum class MAPBASE_EXPORT ObstacleType {
  ObsType_None             = 0,   // 无效值或默认类型
  ObsType_Car              = 1,   // 小汽车
  ObsType_Bus              = 2,   // 客车(公共汽车)
  ObsType_Truck            = 3,   // 大卡车
  ObsType_SmallTruck       = 4,   // 小货车
  ObsType_PoliceCar        = 5,   // 特殊车辆:警车
  ObsType_Ambulance        = 6,   // 特殊车辆:救护车
  ObsType_FireCar          = 7,   // 特殊车辆:消防车
  ObsType_Pedestrian       = 8,   // 行人
  ObsType_Cyclist          = 9,   //骑车人
  ObsType_ConeBarrel       = 10,  // 锥桶
  ObsType_CrashBarrel      = 11,  // 防撞桶
  ObsType_GeLiDun          = 12,  //隔离墩
  ObsType_GeLiZhu          = 13,  //隔离柱
  ObsType_CustomizeOffset  = 5000 // 自定义障碍物类型ID起点
};

/**
 * 障碍物状态
 */
enum class MAPBASE_EXPORT ObstacleStatus {
  ObsStatus_Normal       = 0,    // 正常状态
  ObsStatus_Closest      = 1,    // 最近状态
  ObsStatus_Dangerous    = 2,    // 危险状态
  ObsStatus_Warning      = 3     // 警告状态
};

/**
 * 方位定义
 */
enum class MAPBASE_EXPORT Direction {
  Direction_None               = 0,      // 无效值
  Direction_LeftTop            = 1,      // 左前方
  Direction_Top                = 2,      //   前方
  Direction_RightTop           = 3,      // 右前方
  Direction_Right              = 4,      // 右边
  Direction_RightBottom        = 5,      // 右后方
  Direction_Bottom             = 6,      //   后方
  Direction_LeftBottom         = 7,      // 左后方
  Direction_Left               = 8,      // 左边
};

/**
 * 球面坐标系，经纬度
 */
class MAPBASE_EXPORT ObstacleDescriptor {
 public:
    ObstacleDescriptor() {
    }
 public:
 int32_t                  uid = 0;              // 唯一表示
 uint16_t                 type = 0;             // 类型, 参考ObstacleType
 uint16_t                 status = 0;           // 状态, 参考ObstacleStatus
 float                    yaw = 0.0f;           // 方位角/偏航角度
 float                    pitch = 0.0f;         // 俯仰角(坡度信息)
 double                   dimension[3];         // 障碍物长宽高，0:长，1:宽，2:高
 double                   offset_velocity[3];   // 相对自车速度
 double                   offset_position[3];   // 相对自车位置
 double                   global_position[3];   // 绝对地理坐标(原始)
 double                   matched_position[3];  // 吸附地理坐标
 HAD::LaneID              lane_id;              // 车道ID
 int32_t                  lane_matched_index = 0;// 车道匹配结果在当前lane形点上的起点的下标
 int8_t                   lane_num   = -1;      // 车道号
 int8_t                   confidence = 0;       // 置信度 0~100
 bool                     is_nearest = false;
 bool                     is_danger = false;
 int                      corner_status = 0;
};

/**
 * 变道类型，用于渲染
 */
enum TPChangeLaneType {
  /* 普通变道 */
  kTPLaneChangeTypeNormal = 1,
  /* 紧急变道，过了推荐变道位置后的提示 */
  kTPLaneChangeTypeUrgent = 2,
  /* 小折角，非变道类型，比如汇流、分流等处的车道 */
  kTPLaneChangeTypeSmallCorner = 3
};
/**
 * 变道来源
 */
enum TPChangeLaneSource {
  /** 变道指引提供的变道信息 */
  kTPLaneChangeSourceTip = 1,
  /** 转弯等变道信息 */
  kTPLaneChangeSourceGuidance,
  /** 公交车道禁行时的变道 */
  kTPChangeLaneSourceBus,
  /** 应急车道变道 */
  kTPChangeLaneSourceEmergency,
  /** 非机动车道变道 */
  kTPChangeLaneSourceBicycle
};

/**
 * 车道中心线上的某个位置，比如：
 * 起始位置 { id, 0, 0}
 * 终止位置 { id, point_number - 1, 0}
 */
struct MAPBASE_EXPORT TPLanePos {
    /** 车道ID */
    HAD::LaneID lane_id;
    /* 在中心线形点上的索引 */
    int index{0};
    /* 在形点间的相对位置 [0, 1.0f] */
    float ratio{0};
};

/**
 * 车道段 [start_pos, end_pos]
 */
struct MAPBASE_EXPORT TPLaneSegment {
    /** 起点位置 */
    TPLanePos start_pos;
    /** 终点位置 */
    TPLanePos end_pos;
};

/**
 * 车道变化信息，用于渲染
 */
struct MAPBASE_EXPORT TPChangeLaneInfo {
    /* 变化位置开始索引 */
    int start_index;
    /* 变化位置结束索引 */
    int end_index;
    /* 车道变化个数 */
    int lane_change_num;
    /* 变道类型 */
    TPChangeLaneType change_lane_type;
    /* 变道来源 */
    TPChangeLaneSource change_lane_source;
};
/**
 * 特殊车道信息
 */
struct MAPBASE_EXPORT TPSpecialLaneInfo {
    /* 变道来源
     * 公交车道、应急车道和非机动车道时有效
     */
    TPChangeLaneSource change_lane_source;
    /* 特殊车道信息 */
    std::vector<TPLaneSegment> special_lanes;
    /* 自车位置在车道中心线上的投影位置 */
    TPLanePos car_pos;
};
/**
 * 车道级车标信息
 */
class MAPBASE_EXPORT HDPosPoint {
  public:
    GeoCoordinateZ geo_pos;         /**< 大地坐标系 */
    bool is_relative_altitude = false; /**是否相对高程*/
    uint64_t timestamp = 0;         /**< 时间戳。1970年01月01日00时00分00秒以来的秒数*/
    float speed = 0;                /** 速度，单位：千米每小时 */
    float course = 0;               /**< 方向，北0顺时针，范围：[0-360) */
    float pitch  = 0;               /** 俯仰角(坡度信息) */
    float accuracy = 0;             /**< 位置精度，单位：米 */
    HAD::LaneID  lane_id;           /** 车道ID */
    uint32_t lane_matched_index = 0;/** 车道匹配结果在当前lane形点上的起点的下标，对于中心线吸附结果则为相对中心线点串索引 */
    uint8_t  dangerous_direction= 0;/** 危险障碍物的方位信息 */
    std::string route_id;           /** 中心线吸附结果对应route_id */
    std::string guide_area_id;      /** 中心线吸附结果对应引导面area_id */
    int loc_work_mode = -1;         /** 定位吸附模式 -1 无效 2 车道级吸附 3 中心线吸附*/

    friend std::ostream& operator<<(std::ostream& out, const HDPosPoint& pos) {
      out << " HDPos:{route_id:" << pos.route_id
          << ",work_mode:" << pos.loc_work_mode
          << ",lane_id:" << pos.lane_id
          << ",geo_pos:" << pos.geo_pos
          << ",lane_m_idx:" << pos.lane_matched_index
          << ",time:" << pos.timestamp
          << ",ga_id:" << pos.guide_area_id
          << ",is_r_a:" << pos.is_relative_altitude
          << ",speed:" << pos.speed
          << ",course:" << pos.course
          << ",acc:" << pos.accuracy
          << ",pitch:" << pos.pitch
          << ",danger_direct:" << pos.dangerous_direction;
      return out;
    }
};
/**
 * 车道级引导线信息
 */
struct MAPBASE_EXPORT TPGuideLineInfo {
    /**sd路线id*/
    std::string route_id;
    /** 车标数据信息 */
    HDPosPoint                    locator_info;
    /** 车道级引导线点串 */
    std::vector<GeoCoordinateZ>   guide_polyline;
    /**
     * 车道级引导线点串对应的LaneID列表
     * 列表空代表坐标串可以直接用于渲染
     * 列表不空代表需要结合LaneID进行处理(点串与ID串应该完全匹配)
     */
    std::vector<HAD::LaneID>      lane_id_vector;
    /** 车道级引导线变道信息 */
    std::vector<TPChangeLaneInfo> change_lane_info;
    /** 特殊车道信息 */
    TPSpecialLaneInfo             special_lane_info;
    /**引导线上关键点（起点，终点，变道点）对应sd路线上的投影位置，有数据时，长度>=2*/
    std::vector<std::pair<int,mapbase::RoutePos>> tp_2_sd_route_pos;
    /** 形状点是否近似与之前返回数据; 相近时渲染可以做插值，否则放弃动画插值 */
    bool is_similar_to_last_one = true;
    /** 引导线长度 */
    float length = 0.0f;
};
/**
 * 转向墙信息
 */
struct MAPBASE_EXPORT TurnWallInfo {
  std::vector<GeoCoordinateZ>    wall_polyline;  // 转向墙信息
};
/**
 * 目标车道信息
 */
struct MAPBASE_EXPORT TurnLaneInfo {
  std::vector<GeoCoordinateZ>    target_polyline;  // 目标车道线
  GeoCoordinateZ                 target_position;  // 目标车道点
};
/**
 * 高精坐标点数据结构
 */
class MAPBASE_EXPORT HADPoint {
  public:
    GeoCoordinate geo_pos;          /**< 大地坐标系 */
    int lane_matched_index = -1;    /** geo_pos在当前lane形点上的起点的下标,-1为无效值 */
    HAD::LaneID  lane_id;           /** 车道ID */
};
/**
 * 高精坐标线数据结构
 */
class MAPBASE_EXPORT HADLine {
  public:
    std::vector<HADPoint>    line_points;
};


__NAMESPACE_MAPBASE_END__

#endif  // MAPBASE_INCLUDE_COMMON_COMMON_PERCEPTION_H_
