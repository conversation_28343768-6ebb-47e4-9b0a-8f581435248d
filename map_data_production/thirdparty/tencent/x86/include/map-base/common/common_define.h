// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020-02-03.
//

#ifndef MAP_BASE_INCLUDE_COMMON_COMMON_DEFINE_H_
#define MAP_BASE_INCLUDE_COMMON_COMMON_DEFINE_H_

#include <cmath>
#include <cstdint>
#include <functional>
#include <iostream>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "common/common_const.h"
#include "common/common_location.h"
#include "common/mapbase_export.h"

#define __NAMESPACE_MAPBASE_BEGIN__ namespace mapbase {
#define __NAMESPACE_MAPBASE_END__ }

#define Nullable

__NAMESPACE_MAPBASE_BEGIN__
/**
 * 详细错误信息
 */
class MAPBASE_EXPORT ErrorInfo {
 public:
  ErrorInfo() : network_policy_code_(0), online_error_code_(0), offline_error_code_(0) {}
  ErrorInfo(int online_error_code, int offline_error_code);
  ErrorInfo(int network_policy_code, int online_error_code, int offline_error_code);

  ErrorInfo & operator= ( const ErrorInfo & ) = default;
  ErrorInfo(const ErrorInfo& ret_message) = default;

  /**
  * 网络策略状态码定义
  *
  * <p>
  *  模块分配code范围：
  *    公共模块（0~499）
  *    检索模块（600~999）
  *        601： 仅在线-在线成功
  *        602： 仅在线-在线失败
  *        603： 仅离线-离线成功
  *        604： 仅离线-离线失败
  *        605： 在线优先-在线成功
  *        606： 在线优先-在线失败_离线成功
  *        607： 在线优先-在线、离线均失败
  *        608： 离线优先-离线成功
  *        609： 离线优先-离线失败_在线成功
  *        610： 离线优先-离线、在线均失败
  *    算路模块（1000~2000）
  *       1001： 仅在线-在线成功
  *       1002： 仅在线-在线失败
  *       1003： 仅离线-离线成功
  *       1004： 仅离线-离线失败
  *       1005： 在线优先-在线成功
  *       1006： 在线优先-在线失败_离线成功
  *       1007： 离线优先-在线、离线均失败
  *       1008： 离线优先-离线成功
  *       1009： 离线优先-离线失败_在线成功
  *       1010： 离线优先-离线、在线均失败
  * </p>
  */
  int network_policy_code_{0};
  /**
  * 在线错误码
  * 错误类型
  *
  * <p>
  * 在线算路错误：-100 - 1000
  *   0：默认值
  *   -1：20公里无路错误
  *   -10：坐标错误
  * 离线算路引擎错误：1001 - 2000
  *     1101: 离线算路错误-（OlErrorFailed）
  *     1102: 离线算路错误-无法绑定起点（OlErrorFailedToBindStart）
  *     1103: 离线算路错误-无法绑定终点（OlErrorFailedToBindDestination）
  *     1104: 离线算路错误-未能找到路线（OlErrorFailedToFindRoute）
  *     1105: 离线算路错误-用户已取消（OlErrorUserCancelled）
  *     1106: 离线算路错误-内存不足（OlErrorOutOfMemory）
  *     1107: 离线算路错误-路点太近（OlErrorWaypointTooClose）
  *     1108: 离线算路错误-路由版本不匹配（OlErrorRouteVersionNotMatch）
  *     1109: 离线算路错误-步行太久（OlErrorWalkTooLong）
  *     1110: 离线算路错误-无效上下文（OlErrorInvalidContext）
  *     1111: 离线算路错误-无效参数（OlErrorInvalidParameter）
  *     1112: 离线算路错误-返回范围（OlErrorReturnBounds）
  *     1113: 离线算路错误-无法获取边界（OlErrorFailedToGetBounds）
  *     1114: 离线算路错误-绑定失败（OlErrorFailedToBind）
  *     1115: 离线算路错误-未实现（OlErrorNotImplemented）
  *     1116: 离线算路错误-途径点过多（OlErrorWaypointTooMuch）
  *     1117: 离线算路错误-途径点绑定失败（OlErrorFailedToBindWaypoint）
  *     2000: 未知错误
  * 离线检索引擎错误: 2001 - 3000 暂未提供错误码
  *       3000: 未知错误
  * 其他模块待补充
  * 融合在线算路错误码和错误POI索引
  *  1300-1320 20公里无路错误 + error_poi_index
  *  1320-1340 坐标错误 + error_poi_index
  * </p>
  */
  int online_error_code_{0};
  /**
  * 离线错误码
  * 错误POI索引
  *
  * <p>
  *   0：默认值
  *   1：起点
  *   2：终点
  *   3：途经点
  *   4：途经点
  *   N：途经点
  * </p>
  */
  int offline_error_code_{0};
};
/**
 * 通用的错误信息
 */
class MAPBASE_EXPORT RetMessage {
 public:
  RetMessage(int _ret, const std::string& _msg) : ret_(_ret), msg_(_msg) {};
  RetMessage() : ret_(0), msg_("ok") {};
  RetMessage & operator= ( const RetMessage & ) = default;
  RetMessage(const RetMessage& ret_message) = default;

  int ret_{0};
  std::string msg_;
  ErrorInfo sub_error_info_; // 错误详情
};
MAPBASE_EXPORT std::ostream& operator<<(std::ostream& os, const RetMessage& ret_message);

/**
 * 当前路线未来用时信息
 */
class MAPBASE_EXPORT TrafficStatusResponse {
 public:
  TrafficStatusResponse() = default;
  TrafficStatusResponse(int _ret, const std::string& _msg, int _eta);
  TrafficStatusResponse & operator= ( const TrafficStatusResponse & );
  TrafficStatusResponse(const TrafficStatusResponse& ret_message) = default;

  int ret_{0};
  std::string msg_;
  int eta_{0}; // 当前算路未来用时eta
};

/** 矩形范围 */
template <class T = GeoCoordinate>
class MAPBASE_EXPORT Rect {
 public:
  T left_bottom;
  T right_top;
};

class MAPBASE_EXPORT RoutePos : public GeoCoordinateZ {
 public:
  RoutePos();
  RoutePos(const GeoCoordinate& pos, uint32_t coor_start, uint32_t offset);
  RoutePos(const GeoCoordinateZ& pos, uint32_t coor_start, uint32_t offset);
  MAPBASE_EXPORT friend std::ostream& operator<<(std::ostream& out, const RoutePos& pos);
  uint32_t coor_start;    // 点在路线点串的起始索引，例如该点在索引分别是30、31的俩形状点中间，则coor_start为30
  uint32_t offset_length; // 点相对 coor_start 对应形状点的偏离距离，单位米
};

class MAPBASE_EXPORT RouteEvent {
 public:
  GeoCoordinateZ start_point; // 起始位置，坐标系：Gcj02
  GeoCoordinateZ end_point;   // 结束位置，坐标系：Gcj02
  int coor_start{0};         // 路况所在路线点串的起始索引下标，例如：路线共有100个点，该路况范围在[30,40]，则coorStart为30
  int coor_end{0};           // 路况所在路线点串的结束索引下标，例如：路线共有100个点，该路况范围在[30,40]，则coorEnd为40
  int shape_type{0};         // 事件形态定义，0-点事件 1-线事件
  int speed_kmph{0};         // 预计通过平均速度，单位：kmph
  int passtime{0};           // 预计通过时间，单位：秒
  int length{0};             // 路况事件长度，单位：米
  RouteCondType road_cond{RouteCondType::None};   // 拥堵状态，如：畅通、拥堵等
  friend std::ostream& operator<<(std::ostream& out, const RouteEvent& obj) {
    out << " RouteEvent{"
        << "start:[" << obj.coor_start << "," << obj.start_point
        << "],end:[" << obj.coor_end << "," << obj.end_point
        << "],shape_type:" << obj.shape_type
        << ",speed:" << obj.speed_kmph
        << ",time:" << obj.passtime
        << ",len:" << obj.length
        << ",cond:" << static_cast<int>(obj.road_cond)
        << "}";
    return out;
  }
};

class MAPBASE_EXPORT LinkInfo {
 public:
  long link_id;      // link id
  RoutePos link_pos; // link 坐标信息，z为相对高程
  friend std::ostream& operator<<(std::ostream& out, const LinkInfo& obj) {
    out << " LinkInfo{"
    << "],link_id:[" << obj.link_id << ",link_pos:"
    << obj.link_pos
    << "}";
    return out;
  }
};

class MAPBASE_EXPORT RouteUpdateStatus {
 public:
  std::string route_id_;         // 路线ID
  int route_traffic_status_{0};  // 获取路况状态 1:成功 0:未请求 <0:失败
  int eta_status_{0};            // 获取ETA状态 1:成功 0:未请求 <0:失败
  int hd_refresh_idx_{-1};       // 行中动态事件刷新次数，由服务端下发，客户端请求填充上次成功请求时的值，服务端用于判断客户端是否成功接收到上次请求
};

// 到达途经点的剩余时间和剩余距离信息
class MAPBASE_EXPORT ViaRemainInfo {
public:
  GeoCoordinateZ pos_; // 途经点位置
  RoutePos route_pos;  // 途经点路线位置
  int remain_time_{0}; // 到达途经点的剩余时间，单位秒
  int remain_dist_{0}; // 到达途经点的剩余距离，单位米
  float remain_energy_{0};//当前点到终点或者各途经点到达下一个途经点时的剩余电量
  int charge_time_{-1};   // 该途经点的充电时长，单位分钟，-1表示该途经点充电时间无法计算。（注：行中刷路况及伴随不提供充电时间）
  int remain_power_percent_in_{0}; // 进入充电桩时的电量,[0-100]
  int remain_power_percent_out_{0}; // 离开充电桩时的电量,[0-100]
  bool energy_info_valid_{true};  // 仅C++内部使用，仅表明如下四个变量remain_energy_, charge_time_，remain_power_percent_in_，remain_power_percent_out_是否有效
};

// 新能源信息
class MAPBASE_EXPORT EnergyConsumeInfo{
public:
  GeoCoordinateZ run_out_pos_; // 电量耗尽时的坐标x,单位与cache中保持一致(不可达时有效)
  int arrival_status_{-1};    // 0:可达 1:不可达 2:基本可达 -1:此数据无效
//  float consume_energy_{0};   //路线累计能耗
  float remain_energy_{0};//当前点到终点或者各途经点到达下一个途经点时的剩余电量
  GeoCoordinateZ no_charge_max_pos_;  // 不可达情况下，最远能到达的坐标，经纬度*10^6
  long no_charge_max_dist_{0};  // 不考虑充电时最远到达的距离
  int no_charge_max_eta_{0};    // 不考虑充电最远到达点的eta
  float no_charge_remain_power_{0.0};  // 不考虑充电时最远到达点的剩余电量，不可达时percent*battery
  int no_charge_arrival_status_{-1}; // // 不考虑充电，可达状态。0:可达 1:不可达(当前电量>总电量*最小百分比) 2:不可达(当前电量<=总电量*最小百分比)
};

// 步行信息
class MAPBASE_EXPORT WalkTip {
public:
  int coor_start_; // 步行设施起点位置
  int coor_num_; // 步行设施坐标点个数
  std::string type_; // 类型，o天桥，u地下通道 c 人行横道
};
class MAPBASE_EXPORT WalkMarker {
public:
  int distance_; // 单位: 米
  double longitude_; // marker位置
  double latitude_; // marker位置
  int coor_start_; // marker位置对应的点串索引值
};
// 步行每路线解释性，坐标起止索引表示
class MAPBASE_EXPORT GreenTravelRouteLink {
public:
  int type_;
  int coors_start_;  // 路线上起始坐标索引
  int coors_end_;    // 路线上终止坐标索引
};
// 全局解释性，用link坐标串表示
class MAPBASE_EXPORT GreenTravelGlobalLink {
public:
  std::string name_;      // link名称，暂未用
  std::vector<GeoCoordinate> coors_;     // 坐标串
  std::string card_text_; // 点击marker后的卡片文案（部分），由服务下发，可能为空，此字段暂不使用，使用：WalkExplainTips.card_text
};
class MAPBASE_EXPORT WalkExplainTips {
public:
  int tips_type_;     // 0: 全局tips；1：路线级别tips(点击不同路线，tips不一样)
  std::string title_;     // tips文案标题，由服务下发
  std::string content_;   // tips文案内容，由服务下发
  std::string card_text_; // 点击marker后的卡片文案（部分），由服务下发，可能为空
};
class MAPBASE_EXPORT GreenTravelRouteExplain {
public:
  std::string route_id_;            // link串在哪条路线上，可能不在3方案中
  int type_;                        // 与驾车type对应（无法避开的交限、天桥、地下通道、地铁通道、台阶、轮渡、第三方调用等）
  std::vector<GreenTravelGlobalLink> global_links_; // 全局解释性集合
  int global_links_length_;         // 全局解释性links的长度(cm)
  WalkExplainTips tips_;
  std::vector<std::string> detail_ids_;       // 具体原因id，如限行的具体限行规则id
  std::string exp_route_id_;                // 要解释的路线id，一定在3方案中，用户点哪条，对应哪条的解释
};

class MAPBASE_EXPORT MultiLangCurrency {
public:
    std::vector<std::string> multi_lang_currency_;       // 多语言币种，按简体中文、繁体中文、英文顺序填充
};

class MAPBASE_EXPORT NdmDistanceDuration {
public:
  RetMessage ret_message_;   // 错误信息
  long origin_id_;           // 起点序号
  long dest_id_;             // 终点序号
  int distance_;             // 距离
  int duration_;             // eta
  int light_cnt_;            // 红绿灯数量
  int turn_cnt_;             // 转弯数量
  int toll_;                 // 收费信息
  float energy_cost_;       // 能耗信息
  float energy_remain_;     // 剩余电量
};

class MAPBASE_EXPORT OlRouteExplain {
 public:
  std::vector<int> type_list_;       // 多路线离线解释性类型
};
__NAMESPACE_MAPBASE_END__

#endif  // MAP_BASE_INCLUDE_COMMON_COMMON_DEFINE_H_
