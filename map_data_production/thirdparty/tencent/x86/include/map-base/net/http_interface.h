// Copyright 2020 Tencent. All rights reserved.

#ifndef MAP_BASE_INCLUDE_NET_HTTP_INTERFACE_H_
#define MAP_BASE_INCLUDE_NET_HTTP_INTERFACE_H_

#include "common/mapbase_export.h"

#include <atomic>
#include <cstdint>
#include <map>
#include <memory>
#include <string>
#include <vector>

namespace mapbase {

class MAPBASE_EXPORT HttpCallback {
 public:
  virtual ~HttpCallback() = default;

 public:
  /**
   * @brief HTTP数据回传接口
   *
   * @param req_id     HTTP请求 ID
   * @param http_code  HTTP返回码, 200, 404, 500
   * @param data       HTTP报文数据
   * @param size       HTTP报文数据长度
   * @return 处理成功返回true
   */
  virtual bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                              int32_t size) = 0;

  /**
   * @brief HTTP数据回传接口
   *
   * @param req_id     HTTP请求 ID
   * @param http_code  HTTP返回码, 200, 404, 500
   * @param data       HTTP报文数据
   * @param size       HTTP报文数据长度
   * @param headers    HTTP Headers
   * @return 处理成功返回true
   */
  virtual bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                              int32_t size, const std::map<std::string, std::vector<std::string>> &headers) {
    return OnHttpResponse(req_id, http_code, std::move(data), size);
  }

  /**
   * @brief HTTP数据回传接口
   *
   * @param req_id     HTTP请求 ID
   * @param http_code  HTTP返回码, 200, 404, 500
   * @param data       HTTP报文数据
   * @param size       HTTP报文数据长度
   * @param headers    HTTP Headers
   * @return 处理成功返回true
   */
  virtual bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                              int32_t size, const std::map<std::string, std::string> &headers) {
    std::map<std::string, std::vector<std::string>> headers_map;
    for (const auto &kv : headers) {
      headers_map.insert({kv.first, {kv.second}});
    }
    return OnHttpResponse(req_id, http_code, std::move(data), size, headers_map);
  }

  /**
   * @brief HTTP数据回传出错接口
   *
   * @param req_id     HTTP请求 ID
   * @param error      出错码,-1:未知网络错误, 1:超时，2:取消，3:其他
   * @return 处理成功返回true
   */
  virtual bool OnHttpError(int32_t req_id, int32_t error) = 0;
};

class MAPBASE_EXPORT DownloadCallback {
 public:
  virtual ~DownloadCallback() = default;

 public:
  /**
   * @brief 下载数据进度接口
   *
   * @param req_id     HTTP请求 ID
   * @param http_code  HTTP返回码, 200, 404, 500
   * @param data       HTTP报文数据
   * @param size       HTTP报文数据长度
   * @return 处理成功返回true
   */
  virtual bool OnDownloadProgress(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                                  int32_t size) = 0;

  /**
   * @brief 下载数据回传出错接口
   *
   * @param req_id     HTTP请求 ID
   * @param error      出错码,-1:未知网络错误, 1:超时，2:取消，3:其他
   * @return 处理成功返回true
   */
  virtual bool OnDownloadError(int32_t req_id, int32_t error) = 0;

  /**
   * @brief 下载结束
   *
   *  @param req_id     HTTP请求 ID
   * @return 处理成功返回true
   */
  virtual bool OnDownloadComplete(int32_t req_id) = 0;
};

class MAPBASE_EXPORT HttpResponse {
 public:
  int32_t status;  // 状态码，0：成功，-1：未知网络错误, 1：超时，2：取消，3：其他
  int32_t http_code;         // HTTP返回码, 200, 404, 500
  std::vector<int8_t> data;  // HTTP响应数据

  HttpResponse() : status(-1), http_code(0) {}
};

class MAPBASE_EXPORT HttpInterface {
 public:
  HttpInterface() : inner_id_{1} {}
  virtual ~HttpInterface() = default;

 public:
  int32_t generateRequestId() { return inner_id_.fetch_add(1); }

  static std::map<std::string, std::string> CreateAuthHeader(int32_t req_id, const std::string &uri);
  /**
   * @brief Get请求
   * @param[in] req_id      请求ID
   * @param[in] url         url
   * @param[in] headers     HTTP头
   * @param[in] callback    请求回调
   * @param[in] timeout_ms  超时设置
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool RequestHttpGet(int32_t req_id, const std::string &url,
                              const std::map<std::string, std::string> &headers,
                              std::weak_ptr<HttpCallback> callback, int32_t timeout_ms = 10000) = 0;

  /**
   * @brief Post请求
   * @param[in] reqId      请求ID
   * @param[in] url         url
   * @param[in] headers     HTTP头
   * @param[in] data        发送数据
   * @param[in] size        数据大小
   * @param[in] callback    请求回调
   * @param[in] timeout_ms  超时设置
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool RequestHttpPost(int32_t reqId, const std::string &url,
                               const std::map<std::string, std::string> &headers,
                               std::unique_ptr<int8_t[]> data, int32_t size,
                               std::weak_ptr<HttpCallback> callback,
                               int32_t timeout_ms = 10000) = 0;
  /**
   * @brief Post请求
   * @param[in] reqId      请求ID
   * @param[in] url         url
   * @param[in] headers     HTTP头
   * @param[in] file_path   上传文件路径
   * @param[in] file_size   上传文件大小
   * @param[in] callback    请求回调
   * @param[in] timeout_ms  超时设置
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool RequestHttpUpload(int32_t reqId, const std::string &url,
                                 const std::map<std::string, std::string> &headers,
                                 const std::string &file_path, uint64_t file_size,
                                 std::weak_ptr<HttpCallback> callback,
                                 int32_t timeout_ms = 10000) = 0;

  /**
     * @brief Post请求
     * @param[in] reqId      请求ID
     * @param[in] url         url
     * @param[in] headers     HTTP头
     * @param[in] formdata    请求表单数据
     * @param[in] file_path   上传文件路径
     * @param[in] file_size   上传文件大小
     * @param[in] file_key    文件对应表单描述字段
     * @param[in] callback    请求回调
     * @param[in] timeout_ms  超时设置
     * @retval true 成功
     * @retval false 失败
     */
    virtual bool RequestHttpUploadEx(int32_t reqId, const std::string &url,
                                   const std::map<std::string, std::string> &headers,
                                   const std::map<std::string, std::string> &formdata,
                                   const std::string &file_path, uint64_t file_size,
                                   const std::string &file_key,
                                   std::weak_ptr<HttpCallback> callback,
                                   int32_t timeout_ms = 10000){return false;};

  /**
   * @brief 下载请求
   * @param[in] req_id      请求ID
   * @param[in] url         url
   * @param[in] headers     HTTP头
   * @param[in] callback    请求回调
   * @param[in] file_path   保存文件路径
   * @param[in] timeout_ms  超时设置
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool Download(int32_t req_id, const std::string &url,
                        const std::map<std::string, std::string> &headers,
                        std::weak_ptr<DownloadCallback> callback, const std::string &file_path,
                        int32_t timeout_ms = 300000) = 0;

  /**
   * @brief 取消请求
   * @param[in] req_id    请求ID
   * @retval true 成功
   * @retval false 失败
   */
  virtual bool CancelRequest(int32_t req_id) = 0;

 private:
  std::atomic_int inner_id_;
};
}  // namespace mapbase

#endif  // MAP_BASE_INCLUDE_NET_HTTP_INTERFACE_H_
