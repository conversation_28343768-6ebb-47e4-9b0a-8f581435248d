// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020/6/29.
//

#ifndef MAP_BASE_INCLUDE_FRAMEWORK_ASYNC_OBJECT_H_
#define MAP_BASE_INCLUDE_FRAMEWORK_ASYNC_OBJECT_H_

#ifndef USE_SIMPLE_THREAD
#include "base/single_thread_task_runner.h"
#include "base/synchronization/waitable_event.h"
#include "base/task/cancelable_task_tracker.h"
#include "base/task/post_task.h"
#include "base/task/task_traits.h"

#include <base/atomic_sequence_num.h>
#include <base/location.h>
#else
#include "simple_queue/async_task_runner.h"
namespace base = mapbase;
#endif
#include <memory>

namespace mapbase {

#ifdef DEBUG_FRAMEWORK

static int DEBUG_ERROR_NOT_EXIST_JOBID = 1;
static int DEBUG_ERROR_INVALID_JOBID = 2;
static int DEBUG_CANCEL_FINISHED_1 = 3;
static int DEBUG_CANCEL_FINISHED_2 = 4;
static int DEBUG_ERROR_JOBID_RUN_OUT = 5;
static int DEBUG_CANCEL_ALL_FINISHED = 6;
static int DEBUG_REQUEST_HANDLE_FINISHED = 7;

#define FRAME_LOG(v) LOG(v)

#else

#define FRAME_LOG(v) LOG_IF(v, false)

#endif

template <typename T>
class AsyncObject {
 public:
  explicit AsyncObject(scoped_refptr<base::SequencedTaskRunner> runner, T* thiz) {
    if (runner == nullptr) {
      main_runner_ = base::CreateSequencedTaskRunner(
          {base::ThreadPool(), base::TaskPriority::USER_VISIBLE,
           base::TaskShutdownBehavior::BLOCK_SHUTDOWN, base::MayBlock()});
    } else {
      main_runner_ = runner;
    }
    weak_ptr_factory_ = new base::WeakPtrFactory<T>(thiz);
    main_runner_->PostTask(FROM_HERE,
                           base::BindOnce(&AsyncObject::Init, weak_ptr_factory_->GetWeakPtr()));
  }

  virtual ~AsyncObject() = default;

  class Deleter {
   public:
    void operator()(AsyncObject<T>* thiz) {
      FRAME_LOG(INFO) << "Deleter is working...";
      std::shared_ptr<AsyncObject<T>> ptr(thiz);
      thiz->AsyncRelease(std::move(ptr));
    }
  };
  /**
   * 析构过程
   */
  virtual void ReleaseProcess() {}

 private:
  /**
   * 异步构造
   */
  void Init() {
    FRAME_LOG(INFO) << "AsyncTaskContainer::Init";
    cancelable_tracker_ = new base::CancelableTaskTracker;
  }
  /**
   * 异步释放
   */
  void AsyncRelease(std::shared_ptr<AsyncObject> shared_this) {
    if (!main_runner_->RunsTasksInCurrentSequence()) {
      main_runner_->PostTask(
          FROM_HERE, base::BindOnce(&AsyncObject::AsyncRelease,
                                    weak_ptr_factory_->GetWeakPtr(),
                                    std::move(shared_this)));
      return;
    }
    FRAME_LOG(INFO) << "AsyncRelease in progress " << this;
    if (cancelable_tracker_) {
      cancelable_tracker_->TryCancelAll();
      // 停止所有的调度任务
      delete cancelable_tracker_;
      cancelable_tracker_ = nullptr;
      // 将自身的引用标记为无效
      weak_ptr_factory_->InvalidateWeakPtrs();
    }
    ReleaseProcess();
    delete weak_ptr_factory_;
    weak_ptr_factory_ = nullptr;
    FRAME_LOG(INFO) << "AsyncTaskContainer::Release";
  }

 protected:
  scoped_refptr<base::SequencedTaskRunner> main_runner_;
  base::WeakPtrFactory<T>* weak_ptr_factory_;
  base::CancelableTaskTracker* cancelable_tracker_{nullptr};
};

}  // namespace mapbase
#endif  // MAP_BASE_INCLUDE_FRAMEWORK_ASYNC_OBJECT_H_
