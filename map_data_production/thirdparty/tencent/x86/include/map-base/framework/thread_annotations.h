// Copyright (c) 2012 The LevelDB Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file. See the AUTHORS file for names of contributors.

#pragma once

// Use Clan<PERSON>'s thread safety analysis annotations when available. In other
// environments, the macros receive empty definitions.
// Usage documentation: https://clang.llvm.org/docs/ThreadSafetyAnalysis.html

#if defined(__clang__)
#define MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(x) __attribute__((x))
#else
#define MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(x)  // no-op
#endif

#define MAPBASE_CAPABILITY(x) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(capability(x))

#define MAPBASE_SCOPED_CAPABILITY MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(scoped_lockable)

#define MAPBASE_GUARDED_BY(x) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(guarded_by(x))

#define MAPBASE_PT_GUARDED_BY(x) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(pt_guarded_by(x))

#define MAPBASE_ACQUIRED_BEFORE(...) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(acquired_before(__VA_ARGS__))

#define MAPBASE_ACQUIRED_AFTER(...) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(acquired_after(__VA_ARGS__))

#define MAPBASE_REQUIRES(...) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(requires_capability(__VA_ARGS__))

#define MAPBASE_REQUIRES_SHARED(...) \
  MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(requires_shared_capability(__VA_ARGS__))

#define MAPBASE_ACQUIRE(...) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(acquire_capability(__VA_ARGS__))

#define MAPBASE_ACQUIRE_SHARED(...) \
  MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(acquire_shared_capability(__VA_ARGS__))

#define MAPBASE_RELEASE(...) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(release_capability(__VA_ARGS__))

#define MAPBASE_RELEASE_SHARED(...) \
  MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(release_shared_capability(__VA_ARGS__))

#define MAPBASE_TRY_ACQUIRE(...) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(try_acquire_capability(__VA_ARGS__))

#define MAPBASE_TRY_ACQUIRE_SHARED(...) \
  MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(try_acquire_shared_capability(__VA_ARGS__))

#define MAPBASE_EXCLUDES(...) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(locks_excluded(__VA_ARGS__))

#define MAPBASE_ASSERT_CAPABILITY(x) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(assert_capability(x))

#define MAPBASE_ASSERT_SHARED_CAPABILITY(x) \
  MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(assert_shared_capability(x))

#define MAPBASE_RETURN_CAPABILITY(x) MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(lock_returned(x))

#define MAPBASE_NO_THREAD_SAFETY_ANALYSIS MAPBASE_THREAD_ANNOTATION_ATTRIBUTE__(no_thread_safety_analysis)
