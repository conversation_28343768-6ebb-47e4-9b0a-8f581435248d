// Copyright 2024 Tencent. All rights reserved.
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/1/25.
//
// Use Clang's thread safety analysis annotations when available. In other
// environments, the macros receive empty definitions.
// Usage documentation: https://clang.llvm.org/docs/ThreadSafetyAnalysis.html

#pragma once

#include <memory>
#include <mutex>
#include <shared_mutex>

#include "common/common_define.h"
#include "common/mapbase_export.h"
#include "framework/thread_annotations.h"

__NAMESPACE_MAPBASE_BEGIN__
class MAPBASE_EXPORT MAPBASE_CAPABILITY("mutex") Mutex {
 public:
  Mutex() = default;
  ~Mutex() = default;

  Mutex(const Mutex&) = delete;
  Mutex& operator=(const Mutex&) = delete;
  void Lock() MAPBASE_ACQUIRE() { mutex_.lock(); }
  void Unlock() MAPBASE_RELEASE() { mutex_.unlock(); }
  bool TryLock() MAPBASE_TRY_ACQUIRE(true) { return mutex_.try_lock(); }
  std::mutex& NativeHandle() { return mutex_; }

 private:
  std::mutex mutex_;
  template <class T>
  friend class UniqueLock;
  template <class T>
  friend class SharedLock;
};

#ifdef __cpp_lib_shared_mutex
using shared_mutex = std::shared_mutex;
#else
using shared_mutex = std::shared_timed_mutex;
#endif

class MAPBASE_EXPORT MAPBASE_CAPABILITY("mutex") SharedMutex {
 public:
  SharedMutex() = default;
  ~SharedMutex() = default;

  SharedMutex(const SharedMutex&) = delete;
  SharedMutex& operator=(const SharedMutex&) = delete;
  void Lock() MAPBASE_ACQUIRE() { mutex_.lock(); }
  void Unlock() MAPBASE_RELEASE() { mutex_.unlock(); }
  void LockShared() MAPBASE_ACQUIRE_SHARED() { mutex_.lock_shared(); }
  void UnlockShared() MAPBASE_RELEASE_SHARED() { mutex_.unlock_shared(); }
  bool TryLock() MAPBASE_TRY_ACQUIRE(true) { return mutex_.try_lock(); }
  bool TryLockShared() MAPBASE_TRY_ACQUIRE_SHARED(true) { return mutex_.try_lock_shared(); }
  shared_mutex& NativeHandle() { return mutex_; }

 private:
  shared_mutex mutex_;
  template <class T>
  friend class UniqueLock;
  template <class T>
  friend class SharedLock;
};

template <class T>
class MAPBASE_EXPORT MAPBASE_SCOPED_CAPABILITY LockGuard {
 public:
  explicit LockGuard(T& m) MAPBASE_ACQUIRE(m) : mutex_(m) { mutex_.Lock(); }
  ~LockGuard() MAPBASE_RELEASE() { mutex_.Unlock(); }

  LockGuard(const LockGuard&) = delete;
  LockGuard& operator=(const LockGuard&) = delete;

 private:
  T& mutex_;
};

template <class T>
class MAPBASE_EXPORT MAPBASE_SCOPED_CAPABILITY UniqueLock {
 public:
  explicit UniqueLock(T& m) MAPBASE_ACQUIRE(m) : unique_lock_(m.NativeHandle()) {}
  ~UniqueLock() MAPBASE_RELEASE() = default;

  void Lock() MAPBASE_ACQUIRE() { unique_lock_.lock(); }
  void Unlock() MAPBASE_RELEASE() { unique_lock_.unlock(); }
  bool TryLock() MAPBASE_TRY_ACQUIRE(true) { return unique_lock_.try_lock(); }
  bool TryLockShared() MAPBASE_TRY_ACQUIRE_SHARED(true) { return unique_lock_.try_lock_shared(); }
  std::unique_lock<decltype(T().mutex_)>& NativeHandle() { return unique_lock_; }

  UniqueLock(const UniqueLock&) = delete;
  UniqueLock& operator=(const UniqueLock&) = delete;

 private:
  std::unique_lock<decltype(T().mutex_)> unique_lock_;
};

template <class T>
class MAPBASE_EXPORT MAPBASE_SCOPED_CAPABILITY SharedLock {
 public:
  explicit SharedLock(T& m) MAPBASE_ACQUIRE_SHARED(m) : shared_lock_(m.NativeHandle()) {}
  ~SharedLock() MAPBASE_RELEASE_SHARED() = default;

  void Lock() MAPBASE_ACQUIRE_SHARED() { shared_lock_.lock(); }
  void Unlock() MAPBASE_RELEASE_SHARED() { shared_lock_.unlock(); }
  bool TryLock() MAPBASE_TRY_ACQUIRE(true) { return shared_lock_.try_lock(); }
  std::shared_lock<decltype(T().mutex_)>& NativeHandle() { return shared_lock_; }

  SharedLock(const SharedLock&) = delete;
  SharedLock& operator=(const SharedLock&) = delete;

 private:
  std::shared_lock<decltype(T().mutex_)> shared_lock_;
};

__NAMESPACE_MAPBASE_END__