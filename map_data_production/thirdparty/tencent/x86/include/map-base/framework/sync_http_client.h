// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020/5/18.
//

#ifndef MAP_BASE_INCLUDE_FRAMEWORK_SYNC_HTTP_CLIENT_H_
#define MAP_BASE_INCLUDE_FRAMEWORK_SYNC_HTTP_CLIENT_H_

#include "algorithm/enctrypt_util.h"
#include "common/compress_util.h"
#include "net/http_interface.h"

#ifndef USE_SIMPLE_THREAD
#include <base/single_thread_task_runner.h>
#include <base/synchronization/waitable_event.h>
#include <base/task/post_task.h>
#else
#include "simple_queue/async_task_runner.h"
namespace base = mapbase;
#endif
#include <memory>
#include <utility>

namespace mapbase {

class SyncHttpClient {
 public:
  class HttpResponse {
   private:
    HttpResponse(const HttpResponse& response) {}

   public:
    HttpResponse() { waitable_event_ = std::make_shared<base::WaitableEvent>(); }
    ~HttpResponse() { waitable_event_->Signal(); }

    void Cancel() {
      http_code = -1;
      Notify();
    }

    void WaitUntilResponse(uint32_t timeout_ms) {
      if (waitable_event_->IsSignaled()) return;
      waitable_event_->TimedWait(base::TimeDelta::FromMilliseconds(timeout_ms));
    }

    void Notify() { waitable_event_->Signal(); }

    bool IsWaiting() { return !waitable_event_->IsSignaled(); }

    base::WaitableEvent* GetEvent() { return waitable_event_.get(); }

    int32_t http_code{0};
    std::unique_ptr<int8_t[]> data;
    int32_t size{0};
    std::map<std::string, std::vector<std::string>> headers;

   private:
    std::shared_ptr<base::WaitableEvent> waitable_event_;
  };

  class CallbackWrapper : public HttpCallback {
   public:
    explicit CallbackWrapper(bool enable_encrypt = true) : enable_encrypt_(enable_encrypt) {
      response_ = std::make_unique<HttpResponse>();
    }

    void Cancel(int32_t req_id) {
      response_->Cancel();
      if (task_runner_ && !task_runner_->HasOneRef()) {
        task_runner_->PostTask(FROM_HERE, base::BindOnce(std::move(task_), req_id));
      }
    }

    bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                        int32_t size) override {
      std::map<std::string, std::vector<std::string>> headers;
      return OnHttpResponse(req_id, http_code, std::move(data), size, headers);
    }

    bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                        int32_t size,
                        const std::map<std::string, std::vector<std::string>>& headers) override {
      if (!response_->IsWaiting()) return false;
      response_->http_code = http_code;
      // 如果不是16字节对齐，那肯定不是aes256加密的数据
      if (enable_encrypt_ && http_code == 200 && size > 0 &&
          (size % mapbase::AES256_BLOCK_BYTE_SIZE == 0)) {
        // 这里复制一份数据为了在解密失败时，不影响原始数据走非加密流程
        std::unique_ptr<int8_t[]> en_data = std::make_unique<int8_t[]>(size);
        std::memcpy(en_data.get(), data.get(), size);

        size_t real_len = 0;
        int ret = mapbase::DecryptData((uint8_t*)en_data.get(), size, &real_len);
        if (ret > 0 && real_len == (size_t)ret && real_len < (size_t)size) {
#ifdef USE_ZLIB
          // gzip的magic number https://www.ietf.org/rfc/rfc1952.txt
          if ((real_len > 2) && ((uint8_t)en_data[0] == 0x1f) && ((uint8_t)en_data[1] == 0x8b)) {
            int unzip_len = 0;
            std::unique_ptr<int8_t[]> unzip_data =
                mapbase::compress::GzipInflate(en_data.get(), real_len, unzip_len);
            if (unzip_data && unzip_len > 0) {
              response_->size = unzip_len;
              response_->data = std::move(unzip_data);
            }
          } else {
#endif
            response_->size = real_len;
            response_->data = std::move(en_data);
#ifdef USE_ZLIB
          }
#endif
        } else {
          response_->size = size;
          response_->data = std::move(data);
        }
      } else {
        response_->size = size;
        response_->data = std::move(data);
      }
      response_->headers = headers;
      response_->Notify();
      if (task_runner_ && !task_runner_->HasOneRef()) {
        task_runner_->PostTask(FROM_HERE, base::BindOnce(std::move(task_), req_id));
      }
      return true;
    }

    bool OnHttpResponse(int32_t req_id, int32_t http_code, std::unique_ptr<int8_t[]> data,
                        int32_t size, const std::map<std::string, std::string>& headers) override {
      decltype(response_->headers) resp_headers;
      for (const auto& pair : headers) {
        std::vector<std::string> values = {pair.second};
        resp_headers.insert({pair.first, std::move(values)});
      }
      return OnHttpResponse(req_id, http_code, std::move(data), size, resp_headers);
    }

    bool OnHttpError(int32_t req_id, int32_t error) override {
      if (!response_->IsWaiting()) return false;
      response_->http_code = error;
      response_->Notify();
      if (task_runner_ && !task_runner_->HasOneRef()) {
        task_runner_->PostTask(FROM_HERE, base::BindOnce(std::move(task_), req_id));
      }
      return true;
    }

    HttpResponse* GetResponse() { return response_.get(); }

   private:
    std::unique_ptr<HttpResponse> response_;
    scoped_refptr<base::SequencedTaskRunner> task_runner_;
//    std::function<void(int32_t)> task_;
    base::OnceCallback<void(int32_t)> task_;
    bool enable_encrypt_;

    friend class SyncHttpClient;
  };

  explicit SyncHttpClient(int32_t req_id, std::shared_ptr<HttpInterface> http,
                          bool enable_encrypt = true)
      : req_id_(req_id), http_client_(std::move(http)) {
    callback_ = std::make_shared<CallbackWrapper>(enable_encrypt);
  }

  bool DoGetRequest(const std::string& url, const std::map<std::string, std::string>& headers,
                    int32_t timeout_ms = 10000, bool enable_encrypt = true) {
    // 需要先bind再发起请求，如果不在意返回结果，也给task_runner_和task_一个值吧
    DCHECK(callback_->task_runner_ != nullptr);
    DCHECK(!callback_->task_.is_null());

    if (have_requested_) return false;
    have_requested_ = true;
    std::string en_url = enable_encrypt ? mapbase::EncryptUrlParam(url) : url;
    return http_client_->RequestHttpGet(req_id_, en_url, headers, callback_, timeout_ms);
  }

  bool DoPostRequest(const std::string& url, const std::map<std::string, std::string>& headers,
                     std::unique_ptr<int8_t[]> data, int32_t size, int32_t timeout_ms = 10000,
                     bool enable_encrypt = true) {
    // 需要先bind再发起请求，如果不在意返回结果，也给task_runner_和task_一个值吧
    DCHECK(callback_->task_runner_ != nullptr);
    DCHECK(!callback_->task_.is_null());

    if (have_requested_) return false;
    have_requested_ = true;
    if (enable_encrypt) {
      std::map<std::string, std::string> en_headers(headers);
      en_headers.insert({"encrypt-mode", "1"});
#ifdef USE_ZLIB
      en_headers.insert({"encrypt-version", "2"});
      en_headers.insert({"map-compress", "gzip"});
#endif
      size_t en_size = size + AesPkcs7PaddingLen(size);
      std::unique_ptr<int8_t[]> en_data = std::make_unique<int8_t[]>(en_size);
      std::memcpy(en_data.get(), data.get(), size);
      mapbase::EncryptData((uint8_t*)en_data.get(), size, en_size);
      return http_client_->RequestHttpPost(req_id_, url, en_headers, std::move(en_data), en_size,
                                           callback_, timeout_ms);
    }
    return http_client_->RequestHttpPost(req_id_, url, headers, std::move(data), size, callback_,
                                         timeout_ms);
  }

  bool Cancel(int32_t req_id) {
    if (req_id != req_id_) return false;
    if (callback_ != nullptr) {
      http_client_->CancelRequest(req_id_);
      callback_->Cancel(req_id);
      callback_.reset();
      return true;
    }
    return false;
  }

  void BindAsyncTask(scoped_refptr<base::SequencedTaskRunner> task_runner, base::OnceCallback<void(int32_t)> closure) {
    callback_->task_runner_ = std::move(task_runner);
    callback_->task_ = std::move(closure);
  }

  int32_t GetReqId() const { return req_id_; }

  void WaitUntilResponse(uint32_t timeout_ms) {
    callback_->GetResponse()->WaitUntilResponse(timeout_ms);
  }

  base::WaitableEvent* GetEvent() {
    if (callback_) return callback_->GetResponse()->GetEvent();
    return nullptr;
  }

  HttpResponse* GetResponse() {
    if (callback_) return callback_->GetResponse();
    return nullptr;
  }

  bool IsCanceled() const { return callback_ == nullptr; }

 private:
  int32_t req_id_{-1};
  bool have_requested_{false};  // 是否已经发送过请求了，避免重复请求
  std::shared_ptr<HttpInterface> http_client_;
  std::shared_ptr<CallbackWrapper> callback_;
};

}  // namespace mapbase

#endif  // MAP_BASE_INCLUDE_FRAMEWORK_SYNC_HTTP_CLIENT_H_
