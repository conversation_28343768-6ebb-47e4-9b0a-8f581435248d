// Copyright 2020 Tencent. All rights reserved.

//
// Created by mor<PERSON><PERSON>(morgansun) on 2020/5/7.
//

#ifndef MAP_BASE_INCLUDE_FRAMEWORK_ASYNC_CONTROLLER_H_
#define MAP_BASE_INCLUDE_FRAMEWORK_ASYNC_CONTROLLER_H_

#include <memory>
#include <shared_mutex>
#include <utility>
#include <map>
#include <cassert>

#include "common/common_define.h"
#include "common/mapbase_error.h"
#include "framework/async_callback.h"
#include "framework/async_object.h"
#include "framework/mapbase_mutex.h"

namespace mapbase {

namespace CallbackStatus {
constexpr int Normal = 0;
constexpr int Canceled = 1;
constexpr int Working = 2;
constexpr int Died = 3;
}  // namespace CallbackStatus

/**
 * 异步回调基类定义
 * @tparam Req 请求参数
 * @tparam Rsp 返回参数
 */
template <typename Req, typename Rsp>
class AsyncCallbackWrapper {
 public:
  explicit AsyncCallbackWrapper(JobId job_id, std::weak_ptr<AsyncCallback<Req, Rsp>> callback)
      : job_id_(job_id), callback_(callback) {}
  /**
   * 当异步回调销毁时，标记为取消
   * 同时为防止死锁，析构时一定会释放锁资源
   */
  ~AsyncCallbackWrapper() {
    if (callback_status_ == CallbackStatus::Died) {
      return;
    }
    Cancel(job_id_, RetMessage(error_code::async_controller::CallbackRemoved, "call back removed"));
  }
  /**
   * 取消回调
   */
  void Cancel(JobId job_id, mapbase::RetMessage&& ret) {
    if (callback_status_ != CallbackStatus::Normal) {
      auto callback_ptr = callback_.lock();
      if (callback_ptr) {
        callback_ptr->OnCanceled(
            job_id, false,
            RetMessage(error_code::async_controller::CancelFailFinished, "job finished"));
      }
      return;
    }
    int callback_status_normal = CallbackStatus::Normal;
    if (callback_status_.compare_exchange_strong(callback_status_normal,
                                                 CallbackStatus::Canceled)) {
      auto callback_ptr = callback_.lock();
      if (callback_ptr) {
        callback_ptr->OnCanceled(job_id, true, ret);
      }
      callback_.reset();
    }
  }
  /**
   * 触发回调，内部会根据callback是否可用决定是否发出回调
   * @param job_id
   * @param ret
   * @param req
   * @param rsp
   */
  void SendCallback(JobId job_id, const RetMessage& ret, std::unique_ptr<Req> req,
                    std::unique_ptr<Rsp> rsp) {
    FRAME_LOG(INFO) << "SendCallback begin: " << job_id;
    auto callback_ptr = GetCallback();
    if (callback_ptr) {
      FRAME_LOG(INFO) << "SendCallback context: " << job_id;
      callback_ptr->OnResult(ret, job_id, std::move(req), std::move(rsp));
      callback_status_ = CallbackStatus::Died;
    }
    callback_ptr.reset();
  }
  /**
   * 锁定回调指针，当回调对象存在多个回调方法时，可以通过该方法获取回调对象
   * @return 如果回调对象已经失效则返回nullptr
   */
  std::shared_ptr<AsyncCallback<Req, Rsp>> GetCallback() {
    auto callback_ptr = callback_.lock();
    if (!callback_ptr) return nullptr;
    int callback_status_normal = CallbackStatus::Normal;
    if (!callback_status_.compare_exchange_strong(callback_status_normal, CallbackStatus::Working))
      return nullptr;
    return callback_ptr;
  }

 private:
  JobId job_id_{0};
  std::atomic_int callback_status_{CallbackStatus::Normal};
  std::weak_ptr<AsyncCallback<Req, Rsp>> callback_;
};

/**
 * 异步任务的上下文状态
 * @tparam Req
 * @tparam Rsp
 */
template <class Req, class Rsp>
class AsyncContext {
 public:
  /**
   * @param job_id   任务id
   * @param req      请求参数
   * @param callback 回调对象
   */
  AsyncContext(JobId job_id, std::unique_ptr<Req> req,
               std::unique_ptr<AsyncCallbackWrapper<Req, Rsp>> callback) {
    job_id_ = job_id;
    req_ = std::move(req);
    callback_ = std::move(callback);
  }

  /**
   * 析构函数
   * 当一个上下文析构时，需要阻塞式的将回调标记为不可用状态，Context在运行过程中
   * 是通过shared_ptr进行管理，持有方：
   *    Controller
   *    框架使用方
   *    调用方
   * 当出现析构时，即说明这三者都已经不使用该上下文，此时可以安全释放
   */
  virtual ~AsyncContext() {}

  /**
   * 获取JobId
   * @return
   */
  JobId GetJobId() const { return job_id_; }
  /**
   * 获取请求参数
   * @return
   */
  const Req& GetRequest() const { return *req_; }

  /**
   * 设置返回数据
   * @param ret 错误码
   * @param response 返回数据
   */
  void SetResponse(mapbase::RetMessage&& ret, std::unique_ptr<Rsp> response) {
    ret_ = ret;
    response_ = std::move(response);
    response_set_ = true;
  }
  /**
   * 获取错误码
   * @return
   */
  const mapbase::RetMessage& GetRetMessage() const { return ret_; }
  /**
   * 是否已经有返回数据
   * @return
   */
  bool IsResponseSet() const { return response_set_; }
  /**
   * 获取返回数据
   * @return
   */
  std::unique_ptr<Rsp> GetResponse() { return std::move(response_); }
  /**
   * 标记为取消状态
   */
  void Cancel(RetMessage&& ret_message) {
    if (callback_) {
      callback_->Cancel(job_id_, std::move(ret_message));
    }
  }
  /**
   * 触发回调
   */
  void SendCallback() {
    if (callback_) {
      callback_->SendCallback(job_id_, ret_, std::move(req_), std::move(response_));
      callback_.reset();
    }
  }
  /**
   * 获取回调对象，当回调对象存在多个方法时使用该方法获取回调指针
   * 当回调已失效时返回nullptr
   * @return
   */
  std::shared_ptr<AsyncCallback<Req, Rsp>> GetCallback() { return callback_->GetCallback(); }

 private:
  JobId job_id_;
  std::unique_ptr<Req> req_;

  // return data
  mapbase::RetMessage ret_{0, ""};
  std::unique_ptr<Rsp> response_;

  std::unique_ptr<AsyncCallbackWrapper<Req, Rsp>> callback_;  // 用户注册的回调

  bool response_set_{false};
};

template <class Req, class Rsp>
class AsyncController : public AsyncObject<AsyncController<Req, Rsp>> {
 protected:
  explicit AsyncController(scoped_refptr<base::SequencedTaskRunner> runner)
      : AsyncObject<AsyncController<Req, Rsp>>(runner, this) {}

 public:
  virtual ~AsyncController() { FRAME_LOG(INFO) << "AsyncTaskContainer::~AsyncTaskContainer"; }

  /**
   * 设置jobId的合法区间，从而保证不同类型任务的id是唯一的
   * @param min [0,max(int32_t))
   * @param max [min, max(int32_t)]
   */
  bool SetJobIdZone(int32_t min, int32_t max) {
    if (min >= max) return false;
    if (min < 0 || max < 0) return false;
    min_sequence_number_ = min;
    max_sequence_number_ = max;
    return true;
  }

  /**
   * 接收异步请求
   * @param req
   * @param callback
   * @return -1 参数为空，无法执行任务
   *         -2 队列已经停止运行，该任务无法执行
   *         其他为合法id
   */
  template <class CT = AsyncContext<Req, Rsp>>
  JobId OnRequest(std::unique_ptr<Req> req, std::weak_ptr<AsyncCallback<Req, Rsp>> callback) {
    if (req == nullptr) {
      auto shared_callback = callback.lock();
      if (shared_callback) {
        shared_callback->OnCanceled(
            -1, true,
            RetMessage(error_code::InputParameterError, "request param should not be empty"));
      }
      return -1;
    }
    if (callback.lock() == nullptr) {
      return -1;
    }
    JobId job_id = NextJobId();
    auto callback_wrapper = std::make_unique<AsyncCallbackWrapper<Req, Rsp>>(job_id, callback);
    std::unique_ptr<AsyncContext<Req, Rsp>> context(
        new CT(job_id, std::move(req), std::move(callback_wrapper)));
    bool success = this->main_runner_->PostTask(
        FROM_HERE, base::BindOnce(&AsyncController<Req, Rsp>::HandleRequest, this->GetWeakPtr(),
                                  std::move(context)));
    if (!success) {
      return -2;
    }
    return job_id;
  }

  /**
   * 取消所有已执行的任务
   */
  void CancelAll() {
    if (!this->main_runner_->RunsTasksInCurrentSequence()) {
      this->main_runner_->PostTask(
          FROM_HERE, base::BindOnce(&AsyncController<Req, Rsp>::CancelAll, GetWeakPtr()));
      return;
    }
    this->cancelable_tracker_->TryCancelAll();
    for (auto it : processing_context_list_) {
      std::shared_ptr<AsyncContext<Req, Rsp>> context = it.second;
      CancelWaitingTask(context->GetJobId());
      context->Cancel(RetMessage(error_code::async_controller::CancelByUser, "use cancel all"));
    }
    processing_context_list_.clear();
    id_map_.clear();
    CancelAllProcess();
#ifdef DEBUG_FRAMEWORK
    SendDebugMessage(RetMessage(DEBUG_CANCEL_ALL_FINISHED, "all job has been canceled"));
#endif
  }
  /**
   * 取消指定正在执行任务
   * @param job_id
   */
  void Cancel(JobId job_id) {
    if (job_id < min_sequence_number_ || job_id > max_sequence_number_) {
#ifdef DEBUG_FRAMEWORK
      SendDebugMessage(RetMessage(DEBUG_ERROR_INVALID_JOBID, "jobid is invalid"));
#endif
      return;
    }
    if (!this->main_runner_->RunsTasksInCurrentSequence()) {
      this->main_runner_->PostTask(
          FROM_HERE, base::BindOnce(&AsyncController<Req, Rsp>::Cancel, GetWeakPtr(), job_id));
      return;
    }
    auto it = processing_context_list_.find(job_id);
    if (it == processing_context_list_.end()) {
      // task may not arranged to sequence in current
      auto task_it = id_map_.find(job_id);
      if (task_it == id_map_.end()) {
#ifdef DEBUG_FRAMEWORK
        SendDebugMessage(RetMessage(DEBUG_ERROR_NOT_EXIST_JOBID, "jobid is not exist"));
#endif
        return;
      }
      // CancelWaitingTask(job_id);
      id_map_.erase(task_it);
#ifdef DEBUG_FRAMEWORK
      SendDebugMessage(RetMessage(DEBUG_CANCEL_FINISHED_1, "cancel job successed"));
#endif
      return;
    }
    std::shared_ptr<AsyncContext<Req, Rsp>> context = it->second;
    context->Cancel(RetMessage(error_code::async_controller::CancelByUser, "user canceled"));
    id_map_.erase(job_id);
    // CancelWaitingTask(job_id);
    processing_context_list_.erase(it);
    CancelProcess(job_id);
#ifdef DEBUG_FRAMEWORK
    SendDebugMessage(RetMessage(DEBUG_CANCEL_FINISHED_2, "cancel job successed"));
#endif
  }

#ifdef DEBUG_FRAMEWORK
  int GetProcessingContextListSize() const { return processing_context_list_.size(); }

  int GetIdMapSize() const { return id_map_.size(); }

  void WaitDebugInfo(int debug_msg) MAPBASE_EXCLUDES(ret_message_mutex_) {
    auto ret = GetDebugMessage();
    while (ret.ret_ != debug_msg) {
      debug_event_.Wait();
      debug_event_.Reset();
      ret = GetDebugMessage();
    }
  }

  void SendDebugMessage(RetMessage&& ret) MAPBASE_EXCLUDES(ret_message_mutex_) {
    LockGuard<Mutex> lock(ret_message_mutex_);
    ret_message_ = ret;
    debug_event_.Signal();
  }

  RetMessage GetDebugMessage() const MAPBASE_EXCLUDES(ret_message_mutex_) {
    LockGuard<Mutex> lock(ret_message_mutex_);
    return ret_message_;
  }
#endif

 protected:
  /**
   * 请求逻辑, 业务代码部分
   * @param context
   */
  virtual void ProcessRequest(std::shared_ptr<AsyncContext<Req, Rsp>> context) {
    context->SetResponse(RetMessage{-1, "not implement"}, nullptr);
  }
  /**
   * 取消所有任务(只实现，不直接使用，请使用CancelAll方法)
   */
  virtual void CancelAllProcess() {}
  /**
   * 取消指定任务(只实现，不直接使用，请使用Cancel方法)
   * @param job_id
   */
  virtual void CancelProcess(JobId job_id) {}
  /**
   * 定制的销毁逻辑
   * @return
   */
  virtual void ReleaseProcess() {}
  /**
   * 当前任务延期处理
   * @param context
   * @param delta
   */
  void DelayThisTask(std::shared_ptr<AsyncContext<Req, Rsp>> context,
                     const base::TimeDelta& delta) {
    this->main_runner_->PostDelayedTask(
        FROM_HERE,
        base::BindOnce(&AsyncController<Req, Rsp>::ProcessRequestWithWatch, GetWeakPtr(),
                       std::move(context)),
        delta);
  }

 private:
  /**
   * 分配一个新的JobId
   * @return
   */
  JobId NextJobId() {
    JobId id = sequence_number_.GetNext();
    id += min_sequence_number_;
    id %= (max_sequence_number_ + 1);
    if (id < min_sequence_number_) id = min_sequence_number_;
    return id;
  }
  /**
   * 保存job_id和task_id的映射关系
   * @param job_id
   * @param task_id
   */
  void updateIdRelation(JobId job_id, base::CancelableTaskTracker::TaskId task_id) {
    id_map_[job_id] = task_id;
  }
  /**
   * 处理业务，并将该异步任务上下文写入缓存
   * @param context
   */
  void ProcessRequestWithWatch(std::shared_ptr<AsyncContext<Req, Rsp>> shared_context) {
    DCHECK(this->main_runner_->RunsTasksInCurrentSequence())
        << "ProcessRequestWithWatch shout only run on task runner";
    JobId job_id = shared_context->GetJobId();
    auto it = id_map_.find(job_id);
    if (it == id_map_.end()) {
      // job has been marked as canceled
      shared_context->Cancel(
          RetMessage(error_code::async_controller::CancelByUser, "user canceled"));
      return;
    }
    processing_context_list_.insert({shared_context->GetJobId(), shared_context});
    ProcessRequest(shared_context);
    if (shared_context->IsResponseSet()) {
      processing_context_list_.erase(job_id);
      id_map_.erase(job_id);
    }
#ifdef DEBUG_FRAMEWORK
    SendDebugMessage(RetMessage(DEBUG_REQUEST_HANDLE_FINISHED, "request finished"));
#endif
  }

  /**
   * 异步请求的处理函数
   * @param context
   */
  void HandleRequest(std::unique_ptr<AsyncContext<Req, Rsp>> context) {
    if (!IsAvailable()) return;
    if (!this->main_runner_->RunsTasksInCurrentSequence()) {
      bool success = this->main_runner_->PostTask(
          FROM_HERE, base::BindOnce(&AsyncController<Req, Rsp>::HandleRequest, GetWeakPtr(),
                                    std::move(context)));
      LOG_IF(WARNING, !success) << "RouteServiceImpl::HandleRouteRequest post error";
      return;
    }
    auto job_id = context->GetJobId();
    if (id_map_.find(job_id) != id_map_.end()) {
      // job id has run out, cancel job
      context->Cancel(
          RetMessage(error_code::async_controller::CancelTooMuchJobs, "too much job created"));
#ifdef DEBUG_FRAMEWORK
      SendDebugMessage(RetMessage(DEBUG_ERROR_JOBID_RUN_OUT, "job id run out"));
#endif
      return;
    }
    updateIdRelation(job_id, -1);
    FRAME_LOG(INFO) << "HandleRequest";
    assert(this->main_runner_->RunsTasksInCurrentSequence());

    std::shared_ptr<AsyncContext<Req, Rsp>> shared_context(context.release());
    auto request_task = base::BindOnce(&AsyncController<Req, Rsp>::ProcessRequestWithWatch,
                                       GetWeakPtr(), shared_context);
    auto task_id = this->cancelable_tracker_->PostTask(this->main_runner_.get(), FROM_HERE,
                                                       std::move(request_task));

    updateIdRelation(job_id, task_id);
  }

  /**
   * 获取this的弱引用
   * @return
   */
  base::WeakPtr<AsyncController<Req, Rsp>> GetWeakPtr() {
    return this->weak_ptr_factory_->GetWeakPtr();
  }
  /**
   * 判断当前controller是否可用
   * @return
   */
  bool IsAvailable() const { return this->cancelable_tracker_ != nullptr; }

  /**
   * 取消一个尚未调度的任务，无法保证成功
   * @param job_id
   */
  void CancelWaitingTask(JobId job_id) {
    if (!IsAvailable()) return;
    auto it = id_map_.find(job_id);
    if (it == id_map_.end()) return;
    // 如果这里取消接下来的任务，会导致用户收不到取消的回调
    auto task_id = it->second;
    if (task_id >= 0) this->cancelable_tracker_->TryCancel(task_id);
    id_map_.erase(it);
  }

 protected:
  std::shared_ptr<AsyncContext<Req, Rsp>> GetContext(JobId id) {
    DCHECK(this->main_runner_->RunsTasksInCurrentSequence()) << "GetContext should run in runner";
    auto it = processing_context_list_.find(id);
    if (it == processing_context_list_.end()) {
      return nullptr;
    }
    return it->second;
  }

  void FinishJob(JobId job_id) {
    if (!IsAvailable()) return;
    if (!this->main_runner_->RunsTasksInCurrentSequence()) {
      bool success = this->main_runner_->PostTask(
          FROM_HERE, base::BindOnce(&AsyncController<Req, Rsp>::FinishJob, GetWeakPtr(), job_id));
      LOG_IF(WARNING, !success) << "RouteServiceImpl::OnJobFinished post error";
      return;
    }
    processing_context_list_.erase(job_id);
    id_map_.erase(job_id);
  }

  std::map<JobId, std::shared_ptr<AsyncContext<Req, Rsp>>> processing_context_list_;

 private:
  int32_t min_sequence_number_{1};
  int32_t max_sequence_number_{std::numeric_limits<int32_t>::max()};
  base::AtomicSequenceNumber sequence_number_;

  typedef std::map<JobId, base::CancelableTaskTracker::TaskId> IdMap;
  IdMap id_map_;

  base::WaitableEvent init_event_;

#ifdef DEBUG_FRAMEWORK
  mutable Mutex ret_message_mutex_;
  RetMessage ret_message_ MAPBASE_GUARDED_BY(ret_message_mutex_) {0, ""};
  base::WaitableEvent debug_event_;
#endif
};

template <typename T>
std::unique_ptr<T, typename T::Deleter> CreateController(
    scoped_refptr<base::SequencedTaskRunner> runner = nullptr) {
  return std::unique_ptr<T, typename T::Deleter>(new T(runner), typename T::Deleter());
}

template <typename T, typename... Args>
std::unique_ptr<T, typename T::Deleter> CreateController(
    scoped_refptr<base::SequencedTaskRunner> runner, Args... args) {
  return std::unique_ptr<T, typename T::Deleter>(new T(runner, args...), typename T::Deleter());
}

}  // namespace mapbase

#endif  // MAP_BASE_INCLUDE_FRAMEWORK_ASYNC_CONTROLLER_H_
