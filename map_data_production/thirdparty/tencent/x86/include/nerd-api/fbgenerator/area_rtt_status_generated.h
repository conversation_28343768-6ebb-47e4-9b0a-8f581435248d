// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_AREARTTSTATUS_AREARTTSTATUS_H_
#define FLATBUFFERS_GENERATED_AREARTTSTATUS_AREARTTSTATUS_H_

#include "flatbuffers/flatbuffers.h"

namespace AreaRttStatus {

struct StatusData;
struct StatusDataBuilder;
struct StatusDataT;

struct RoadStatus;
struct RoadStatusBuilder;
struct RoadStatusT;

struct RttTileData;
struct RttTileDataBuilder;
struct RttTileDataT;

struct RttResponse;
struct RttResponseBuilder;
struct RttResponseT;

struct StatusDataT : public flatbuffers::NativeTable {
  typedef StatusData TableType;
  int8_t status = 0;
  uint16_t radio = 0;
};

struct StatusData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef StatusDataT NativeTableType;
  typedef StatusDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_STATUS = 4,
    VT_RADIO = 6
  };
  int8_t status() const {
    return GetField<int8_t>(VT_STATUS, 0);
  }
  uint16_t radio() const {
    return GetField<uint16_t>(VT_RADIO, 0);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_STATUS) &&
           VerifyField<uint16_t>(verifier, VT_RADIO) &&
           verifier.EndTable();
  }
  StatusDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(StatusDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<StatusData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const StatusDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct StatusDataBuilder {
  typedef StatusData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_status(int8_t status) {
    fbb_.AddElement<int8_t>(StatusData::VT_STATUS, status, 0);
  }
  void add_radio(uint16_t radio) {
    fbb_.AddElement<uint16_t>(StatusData::VT_RADIO, radio, 0);
  }
  explicit StatusDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<StatusData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<StatusData>(end);
    return o;
  }
};

inline flatbuffers::Offset<StatusData> CreateStatusData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t status = 0,
    uint16_t radio = 0) {
  StatusDataBuilder builder_(_fbb);
  builder_.add_radio(radio);
  builder_.add_status(status);
  return builder_.Finish();
}

flatbuffers::Offset<StatusData> CreateStatusData(flatbuffers::FlatBufferBuilder &_fbb, const StatusDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RoadStatusT : public flatbuffers::NativeTable {
  typedef RoadStatus TableType;
  uint32_t id = 0;
  std::vector<std::unique_ptr<AreaRttStatus::StatusDataT>> status{};
};

struct RoadStatus FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RoadStatusT NativeTableType;
  typedef RoadStatusBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_STATUS = 6
  };
  uint32_t id() const {
    return GetField<uint32_t>(VT_ID, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::StatusData>> *status() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::StatusData>> *>(VT_STATUS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_ID) &&
           VerifyOffset(verifier, VT_STATUS) &&
           verifier.VerifyVector(status()) &&
           verifier.VerifyVectorOfTables(status()) &&
           verifier.EndTable();
  }
  RoadStatusT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RoadStatusT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RoadStatus> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadStatusT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RoadStatusBuilder {
  typedef RoadStatus Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_id(uint32_t id) {
    fbb_.AddElement<uint32_t>(RoadStatus::VT_ID, id, 0);
  }
  void add_status(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::StatusData>>> status) {
    fbb_.AddOffset(RoadStatus::VT_STATUS, status);
  }
  explicit RoadStatusBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RoadStatus> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RoadStatus>(end);
    return o;
  }
};

inline flatbuffers::Offset<RoadStatus> CreateRoadStatus(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t id = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::StatusData>>> status = 0) {
  RoadStatusBuilder builder_(_fbb);
  builder_.add_status(status);
  builder_.add_id(id);
  return builder_.Finish();
}

inline flatbuffers::Offset<RoadStatus> CreateRoadStatusDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t id = 0,
    const std::vector<flatbuffers::Offset<AreaRttStatus::StatusData>> *status = nullptr) {
  auto status__ = status ? _fbb.CreateVector<flatbuffers::Offset<AreaRttStatus::StatusData>>(*status) : 0;
  return AreaRttStatus::CreateRoadStatus(
      _fbb,
      id,
      status__);
}

flatbuffers::Offset<RoadStatus> CreateRoadStatus(flatbuffers::FlatBufferBuilder &_fbb, const RoadStatusT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RttTileDataT : public flatbuffers::NativeTable {
  typedef RttTileData TableType;
  int8_t code = 0;
  uint32_t tileid = 0;
  uint32_t time_stamp = 0;
  uint32_t interval = 0;
  std::vector<std::unique_ptr<AreaRttStatus::RoadStatusT>> road_rtts{};
};

struct RttTileData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RttTileDataT NativeTableType;
  typedef RttTileDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CODE = 4,
    VT_TILEID = 6,
    VT_TIME_STAMP = 8,
    VT_INTERVAL = 10,
    VT_ROAD_RTTS = 12
  };
  int8_t code() const {
    return GetField<int8_t>(VT_CODE, 0);
  }
  uint32_t tileid() const {
    return GetField<uint32_t>(VT_TILEID, 0);
  }
  uint32_t time_stamp() const {
    return GetField<uint32_t>(VT_TIME_STAMP, 0);
  }
  uint32_t interval() const {
    return GetField<uint32_t>(VT_INTERVAL, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RoadStatus>> *road_rtts() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RoadStatus>> *>(VT_ROAD_RTTS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_CODE) &&
           VerifyField<uint32_t>(verifier, VT_TILEID) &&
           VerifyField<uint32_t>(verifier, VT_TIME_STAMP) &&
           VerifyField<uint32_t>(verifier, VT_INTERVAL) &&
           VerifyOffset(verifier, VT_ROAD_RTTS) &&
           verifier.VerifyVector(road_rtts()) &&
           verifier.VerifyVectorOfTables(road_rtts()) &&
           verifier.EndTable();
  }
  RttTileDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RttTileDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RttTileData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RttTileDataBuilder {
  typedef RttTileData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_code(int8_t code) {
    fbb_.AddElement<int8_t>(RttTileData::VT_CODE, code, 0);
  }
  void add_tileid(uint32_t tileid) {
    fbb_.AddElement<uint32_t>(RttTileData::VT_TILEID, tileid, 0);
  }
  void add_time_stamp(uint32_t time_stamp) {
    fbb_.AddElement<uint32_t>(RttTileData::VT_TIME_STAMP, time_stamp, 0);
  }
  void add_interval(uint32_t interval) {
    fbb_.AddElement<uint32_t>(RttTileData::VT_INTERVAL, interval, 0);
  }
  void add_road_rtts(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RoadStatus>>> road_rtts) {
    fbb_.AddOffset(RttTileData::VT_ROAD_RTTS, road_rtts);
  }
  explicit RttTileDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RttTileData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RttTileData>(end);
    return o;
  }
};

inline flatbuffers::Offset<RttTileData> CreateRttTileData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t code = 0,
    uint32_t tileid = 0,
    uint32_t time_stamp = 0,
    uint32_t interval = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RoadStatus>>> road_rtts = 0) {
  RttTileDataBuilder builder_(_fbb);
  builder_.add_road_rtts(road_rtts);
  builder_.add_interval(interval);
  builder_.add_time_stamp(time_stamp);
  builder_.add_tileid(tileid);
  builder_.add_code(code);
  return builder_.Finish();
}

inline flatbuffers::Offset<RttTileData> CreateRttTileDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t code = 0,
    uint32_t tileid = 0,
    uint32_t time_stamp = 0,
    uint32_t interval = 0,
    const std::vector<flatbuffers::Offset<AreaRttStatus::RoadStatus>> *road_rtts = nullptr) {
  auto road_rtts__ = road_rtts ? _fbb.CreateVector<flatbuffers::Offset<AreaRttStatus::RoadStatus>>(*road_rtts) : 0;
  return AreaRttStatus::CreateRttTileData(
      _fbb,
      code,
      tileid,
      time_stamp,
      interval,
      road_rtts__);
}

flatbuffers::Offset<RttTileData> CreateRttTileData(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RttResponseT : public flatbuffers::NativeTable {
  typedef RttResponse TableType;
  uint32_t version = 0;
  std::vector<std::unique_ptr<AreaRttStatus::RttTileDataT>> tiles{};
};

struct RttResponse FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RttResponseT NativeTableType;
  typedef RttResponseBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VERSION = 4,
    VT_TILES = 6
  };
  uint32_t version() const {
    return GetField<uint32_t>(VT_VERSION, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RttTileData>> *tiles() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RttTileData>> *>(VT_TILES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_VERSION) &&
           VerifyOffset(verifier, VT_TILES) &&
           verifier.VerifyVector(tiles()) &&
           verifier.VerifyVectorOfTables(tiles()) &&
           verifier.EndTable();
  }
  RttResponseT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RttResponseT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RttResponse> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RttResponseBuilder {
  typedef RttResponse Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_version(uint32_t version) {
    fbb_.AddElement<uint32_t>(RttResponse::VT_VERSION, version, 0);
  }
  void add_tiles(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RttTileData>>> tiles) {
    fbb_.AddOffset(RttResponse::VT_TILES, tiles);
  }
  explicit RttResponseBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RttResponse> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RttResponse>(end);
    return o;
  }
};

inline flatbuffers::Offset<RttResponse> CreateRttResponse(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t version = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<AreaRttStatus::RttTileData>>> tiles = 0) {
  RttResponseBuilder builder_(_fbb);
  builder_.add_tiles(tiles);
  builder_.add_version(version);
  return builder_.Finish();
}

inline flatbuffers::Offset<RttResponse> CreateRttResponseDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t version = 0,
    const std::vector<flatbuffers::Offset<AreaRttStatus::RttTileData>> *tiles = nullptr) {
  auto tiles__ = tiles ? _fbb.CreateVector<flatbuffers::Offset<AreaRttStatus::RttTileData>>(*tiles) : 0;
  return AreaRttStatus::CreateRttResponse(
      _fbb,
      version,
      tiles__);
}

flatbuffers::Offset<RttResponse> CreateRttResponse(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline StatusDataT *StatusData::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<StatusDataT>(new StatusDataT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void StatusData::UnPackTo(StatusDataT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = status(); _o->status = _e; }
  { auto _e = radio(); _o->radio = _e; }
}

inline flatbuffers::Offset<StatusData> StatusData::Pack(flatbuffers::FlatBufferBuilder &_fbb, const StatusDataT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateStatusData(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<StatusData> CreateStatusData(flatbuffers::FlatBufferBuilder &_fbb, const StatusDataT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const StatusDataT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _status = _o->status;
  auto _radio = _o->radio;
  return AreaRttStatus::CreateStatusData(
      _fbb,
      _status,
      _radio);
}

inline RoadStatusT *RoadStatus::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RoadStatusT>(new RoadStatusT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RoadStatus::UnPackTo(RoadStatusT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = id(); _o->id = _e; }
  { auto _e = status(); if (_e) { _o->status.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->status[_i] = std::unique_ptr<AreaRttStatus::StatusDataT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<RoadStatus> RoadStatus::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadStatusT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRoadStatus(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RoadStatus> CreateRoadStatus(flatbuffers::FlatBufferBuilder &_fbb, const RoadStatusT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RoadStatusT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _id = _o->id;
  auto _status = _o->status.size() ? _fbb.CreateVector<flatbuffers::Offset<AreaRttStatus::StatusData>> (_o->status.size(), [](size_t i, _VectorArgs *__va) { return CreateStatusData(*__va->__fbb, __va->__o->status[i].get(), __va->__rehasher); }, &_va ) : 0;
  return AreaRttStatus::CreateRoadStatus(
      _fbb,
      _id,
      _status);
}

inline RttTileDataT *RttTileData::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RttTileDataT>(new RttTileDataT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RttTileData::UnPackTo(RttTileDataT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = code(); _o->code = _e; }
  { auto _e = tileid(); _o->tileid = _e; }
  { auto _e = time_stamp(); _o->time_stamp = _e; }
  { auto _e = interval(); _o->interval = _e; }
  { auto _e = road_rtts(); if (_e) { _o->road_rtts.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->road_rtts[_i] = std::unique_ptr<AreaRttStatus::RoadStatusT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<RttTileData> RttTileData::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRttTileData(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RttTileData> CreateRttTileData(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RttTileDataT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _code = _o->code;
  auto _tileid = _o->tileid;
  auto _time_stamp = _o->time_stamp;
  auto _interval = _o->interval;
  auto _road_rtts = _o->road_rtts.size() ? _fbb.CreateVector<flatbuffers::Offset<AreaRttStatus::RoadStatus>> (_o->road_rtts.size(), [](size_t i, _VectorArgs *__va) { return CreateRoadStatus(*__va->__fbb, __va->__o->road_rtts[i].get(), __va->__rehasher); }, &_va ) : 0;
  return AreaRttStatus::CreateRttTileData(
      _fbb,
      _code,
      _tileid,
      _time_stamp,
      _interval,
      _road_rtts);
}

inline RttResponseT *RttResponse::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RttResponseT>(new RttResponseT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RttResponse::UnPackTo(RttResponseT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = version(); _o->version = _e; }
  { auto _e = tiles(); if (_e) { _o->tiles.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->tiles[_i] = std::unique_ptr<AreaRttStatus::RttTileDataT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<RttResponse> RttResponse::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRttResponse(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RttResponse> CreateRttResponse(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RttResponseT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _version = _o->version;
  auto _tiles = _o->tiles.size() ? _fbb.CreateVector<flatbuffers::Offset<AreaRttStatus::RttTileData>> (_o->tiles.size(), [](size_t i, _VectorArgs *__va) { return CreateRttTileData(*__va->__fbb, __va->__o->tiles[i].get(), __va->__rehasher); }, &_va ) : 0;
  return AreaRttStatus::CreateRttResponse(
      _fbb,
      _version,
      _tiles);
}

inline const AreaRttStatus::RttResponse *GetRttResponse(const void *buf) {
  return flatbuffers::GetRoot<AreaRttStatus::RttResponse>(buf);
}

inline const AreaRttStatus::RttResponse *GetSizePrefixedRttResponse(const void *buf) {
  return flatbuffers::GetSizePrefixedRoot<AreaRttStatus::RttResponse>(buf);
}

inline bool VerifyRttResponseBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<AreaRttStatus::RttResponse>(nullptr);
}

inline bool VerifySizePrefixedRttResponseBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<AreaRttStatus::RttResponse>(nullptr);
}

inline void FinishRttResponseBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<AreaRttStatus::RttResponse> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedRttResponseBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<AreaRttStatus::RttResponse> root) {
  fbb.FinishSizePrefixed(root);
}

inline std::unique_ptr<AreaRttStatus::RttResponseT> UnPackRttResponse(
    const void *buf,
    const flatbuffers::resolver_function_t *res = nullptr) {
  return std::unique_ptr<AreaRttStatus::RttResponseT>(GetRttResponse(buf)->UnPack(res));
}

inline std::unique_ptr<AreaRttStatus::RttResponseT> UnPackSizePrefixedRttResponse(
    const void *buf,
    const flatbuffers::resolver_function_t *res = nullptr) {
  return std::unique_ptr<AreaRttStatus::RttResponseT>(GetSizePrefixedRttResponse(buf)->UnPack(res));
}

}  // namespace AreaRttStatus

#endif  // FLATBUFFERS_GENERATED_AREARTTSTATUS_AREARTTSTATUS_H_
