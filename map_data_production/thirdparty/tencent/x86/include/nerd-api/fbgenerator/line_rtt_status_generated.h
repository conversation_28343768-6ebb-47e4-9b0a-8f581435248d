// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_LINERTTSTATUS_LINERTTSTATUS_H_
#define FLATBUFFERS_GENERATED_LINERTTSTATUS_LINERTTSTATUS_H_

#include "flatbuffers/flatbuffers.h"

namespace LineRttStatus {

struct Point;

struct RelativePoint;

struct RoadInfo;
struct RoadInfoBuilder;
struct RoadInfoT;

struct LayerData;
struct LayerDataBuilder;
struct LayerDataT;

struct RttTileData;
struct RttTileDataBuilder;
struct RttTileDataT;

struct RttResponse;
struct RttResponseBuilder;
struct RttResponseT;

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(4) Point FLATBUFFERS_FINAL_CLASS {
 private:
  uint32_t x_;
  uint32_t y_;
  int32_t z_;

 public:
  Point()
      : x_(0),
        y_(0),
        z_(0) {
  }
  Point(uint32_t _x, uint32_t _y, int32_t _z)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)),
        z_(flatbuffers::EndianScalar(_z)) {
  }
  uint32_t x() const {
    return flatbuffers::EndianScalar(x_);
  }
  uint32_t y() const {
    return flatbuffers::EndianScalar(y_);
  }
  int32_t z() const {
    return flatbuffers::EndianScalar(z_);
  }
};
FLATBUFFERS_STRUCT_END(Point, 12);

FLATBUFFERS_MANUALLY_ALIGNED_STRUCT(2) RelativePoint FLATBUFFERS_FINAL_CLASS {
 private:
  uint16_t x_;
  uint16_t y_;

 public:
  RelativePoint()
      : x_(0),
        y_(0) {
  }
  RelativePoint(uint16_t _x, uint16_t _y)
      : x_(flatbuffers::EndianScalar(_x)),
        y_(flatbuffers::EndianScalar(_y)) {
  }
  uint16_t x() const {
    return flatbuffers::EndianScalar(x_);
  }
  uint16_t y() const {
    return flatbuffers::EndianScalar(y_);
  }
};
FLATBUFFERS_STRUCT_END(RelativePoint, 4);

struct RoadInfoT : public flatbuffers::NativeTable {
  typedef RoadInfo TableType;
  std::vector<LineRttStatus::Point> points{};
  std::vector<LineRttStatus::RelativePoint> relative_points{};
  std::vector<int16_t> points_z{};
};

struct RoadInfo FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RoadInfoT NativeTableType;
  typedef RoadInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_POINTS = 4,
    VT_RELATIVE_POINTS = 6,
    VT_POINTS_Z = 8
  };
  const flatbuffers::Vector<const LineRttStatus::Point *> *points() const {
    return GetPointer<const flatbuffers::Vector<const LineRttStatus::Point *> *>(VT_POINTS);
  }
  const flatbuffers::Vector<const LineRttStatus::RelativePoint *> *relative_points() const {
    return GetPointer<const flatbuffers::Vector<const LineRttStatus::RelativePoint *> *>(VT_RELATIVE_POINTS);
  }
  const flatbuffers::Vector<int16_t> *points_z() const {
    return GetPointer<const flatbuffers::Vector<int16_t> *>(VT_POINTS_Z);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_POINTS) &&
           verifier.VerifyVector(points()) &&
           VerifyOffset(verifier, VT_RELATIVE_POINTS) &&
           verifier.VerifyVector(relative_points()) &&
           VerifyOffset(verifier, VT_POINTS_Z) &&
           verifier.VerifyVector(points_z()) &&
           verifier.EndTable();
  }
  RoadInfoT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RoadInfoT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RoadInfo> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RoadInfoBuilder {
  typedef RoadInfo Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_points(flatbuffers::Offset<flatbuffers::Vector<const LineRttStatus::Point *>> points) {
    fbb_.AddOffset(RoadInfo::VT_POINTS, points);
  }
  void add_relative_points(flatbuffers::Offset<flatbuffers::Vector<const LineRttStatus::RelativePoint *>> relative_points) {
    fbb_.AddOffset(RoadInfo::VT_RELATIVE_POINTS, relative_points);
  }
  void add_points_z(flatbuffers::Offset<flatbuffers::Vector<int16_t>> points_z) {
    fbb_.AddOffset(RoadInfo::VT_POINTS_Z, points_z);
  }
  explicit RoadInfoBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RoadInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RoadInfo>(end);
    return o;
  }
};

inline flatbuffers::Offset<RoadInfo> CreateRoadInfo(
    flatbuffers::FlatBufferBuilder &_fbb,
    flatbuffers::Offset<flatbuffers::Vector<const LineRttStatus::Point *>> points = 0,
    flatbuffers::Offset<flatbuffers::Vector<const LineRttStatus::RelativePoint *>> relative_points = 0,
    flatbuffers::Offset<flatbuffers::Vector<int16_t>> points_z = 0) {
  RoadInfoBuilder builder_(_fbb);
  builder_.add_points_z(points_z);
  builder_.add_relative_points(relative_points);
  builder_.add_points(points);
  return builder_.Finish();
}

inline flatbuffers::Offset<RoadInfo> CreateRoadInfoDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<LineRttStatus::Point> *points = nullptr,
    const std::vector<LineRttStatus::RelativePoint> *relative_points = nullptr,
    const std::vector<int16_t> *points_z = nullptr) {
  auto points__ = points ? _fbb.CreateVectorOfStructs<LineRttStatus::Point>(*points) : 0;
  auto relative_points__ = relative_points ? _fbb.CreateVectorOfStructs<LineRttStatus::RelativePoint>(*relative_points) : 0;
  auto points_z__ = points_z ? _fbb.CreateVector<int16_t>(*points_z) : 0;
  return LineRttStatus::CreateRoadInfo(
      _fbb,
      points__,
      relative_points__,
      points_z__);
}

flatbuffers::Offset<RoadInfo> CreateRoadInfo(flatbuffers::FlatBufferBuilder &_fbb, const RoadInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct LayerDataT : public flatbuffers::NativeTable {
  typedef LayerData TableType;
  uint32_t layer_type = 0;
  uint32_t priority = 0;
  uint32_t extra_priority = 0;
  int8_t class_code = 0;
  std::vector<std::unique_ptr<LineRttStatus::RoadInfoT>> roads{};
};

struct LayerData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef LayerDataT NativeTableType;
  typedef LayerDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_LAYER_TYPE = 4,
    VT_PRIORITY = 6,
    VT_EXTRA_PRIORITY = 8,
    VT_CLASS_CODE = 10,
    VT_ROADS = 12
  };
  uint32_t layer_type() const {
    return GetField<uint32_t>(VT_LAYER_TYPE, 0);
  }
  uint32_t priority() const {
    return GetField<uint32_t>(VT_PRIORITY, 0);
  }
  uint32_t extra_priority() const {
    return GetField<uint32_t>(VT_EXTRA_PRIORITY, 0);
  }
  int8_t class_code() const {
    return GetField<int8_t>(VT_CLASS_CODE, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RoadInfo>> *roads() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RoadInfo>> *>(VT_ROADS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_LAYER_TYPE) &&
           VerifyField<uint32_t>(verifier, VT_PRIORITY) &&
           VerifyField<uint32_t>(verifier, VT_EXTRA_PRIORITY) &&
           VerifyField<int8_t>(verifier, VT_CLASS_CODE) &&
           VerifyOffset(verifier, VT_ROADS) &&
           verifier.VerifyVector(roads()) &&
           verifier.VerifyVectorOfTables(roads()) &&
           verifier.EndTable();
  }
  LayerDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(LayerDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<LayerData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const LayerDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct LayerDataBuilder {
  typedef LayerData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_layer_type(uint32_t layer_type) {
    fbb_.AddElement<uint32_t>(LayerData::VT_LAYER_TYPE, layer_type, 0);
  }
  void add_priority(uint32_t priority) {
    fbb_.AddElement<uint32_t>(LayerData::VT_PRIORITY, priority, 0);
  }
  void add_extra_priority(uint32_t extra_priority) {
    fbb_.AddElement<uint32_t>(LayerData::VT_EXTRA_PRIORITY, extra_priority, 0);
  }
  void add_class_code(int8_t class_code) {
    fbb_.AddElement<int8_t>(LayerData::VT_CLASS_CODE, class_code, 0);
  }
  void add_roads(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RoadInfo>>> roads) {
    fbb_.AddOffset(LayerData::VT_ROADS, roads);
  }
  explicit LayerDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<LayerData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<LayerData>(end);
    return o;
  }
};

inline flatbuffers::Offset<LayerData> CreateLayerData(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t layer_type = 0,
    uint32_t priority = 0,
    uint32_t extra_priority = 0,
    int8_t class_code = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RoadInfo>>> roads = 0) {
  LayerDataBuilder builder_(_fbb);
  builder_.add_roads(roads);
  builder_.add_extra_priority(extra_priority);
  builder_.add_priority(priority);
  builder_.add_layer_type(layer_type);
  builder_.add_class_code(class_code);
  return builder_.Finish();
}

inline flatbuffers::Offset<LayerData> CreateLayerDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t layer_type = 0,
    uint32_t priority = 0,
    uint32_t extra_priority = 0,
    int8_t class_code = 0,
    const std::vector<flatbuffers::Offset<LineRttStatus::RoadInfo>> *roads = nullptr) {
  auto roads__ = roads ? _fbb.CreateVector<flatbuffers::Offset<LineRttStatus::RoadInfo>>(*roads) : 0;
  return LineRttStatus::CreateLayerData(
      _fbb,
      layer_type,
      priority,
      extra_priority,
      class_code,
      roads__);
}

flatbuffers::Offset<LayerData> CreateLayerData(flatbuffers::FlatBufferBuilder &_fbb, const LayerDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RttTileDataT : public flatbuffers::NativeTable {
  typedef RttTileData TableType;
  int8_t code = 0;
  uint32_t tileid = 0;
  uint32_t time_stamp = 0;
  uint32_t interval = 0;
  uint16_t unit = 0;
  std::unique_ptr<LineRttStatus::Point> sw_conner{};
  std::vector<std::unique_ptr<LineRttStatus::LayerDataT>> layers{};
};

struct RttTileData FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RttTileDataT NativeTableType;
  typedef RttTileDataBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_CODE = 4,
    VT_TILEID = 6,
    VT_TIME_STAMP = 8,
    VT_INTERVAL = 10,
    VT_UNIT = 12,
    VT_SW_CONNER = 14,
    VT_LAYERS = 16
  };
  int8_t code() const {
    return GetField<int8_t>(VT_CODE, 0);
  }
  uint32_t tileid() const {
    return GetField<uint32_t>(VT_TILEID, 0);
  }
  uint32_t time_stamp() const {
    return GetField<uint32_t>(VT_TIME_STAMP, 0);
  }
  uint32_t interval() const {
    return GetField<uint32_t>(VT_INTERVAL, 0);
  }
  uint16_t unit() const {
    return GetField<uint16_t>(VT_UNIT, 0);
  }
  const LineRttStatus::Point *sw_conner() const {
    return GetStruct<const LineRttStatus::Point *>(VT_SW_CONNER);
  }
  const flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::LayerData>> *layers() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::LayerData>> *>(VT_LAYERS);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_CODE) &&
           VerifyField<uint32_t>(verifier, VT_TILEID) &&
           VerifyField<uint32_t>(verifier, VT_TIME_STAMP) &&
           VerifyField<uint32_t>(verifier, VT_INTERVAL) &&
           VerifyField<uint16_t>(verifier, VT_UNIT) &&
           VerifyField<LineRttStatus::Point>(verifier, VT_SW_CONNER) &&
           VerifyOffset(verifier, VT_LAYERS) &&
           verifier.VerifyVector(layers()) &&
           verifier.VerifyVectorOfTables(layers()) &&
           verifier.EndTable();
  }
  RttTileDataT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RttTileDataT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RttTileData> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RttTileDataBuilder {
  typedef RttTileData Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_code(int8_t code) {
    fbb_.AddElement<int8_t>(RttTileData::VT_CODE, code, 0);
  }
  void add_tileid(uint32_t tileid) {
    fbb_.AddElement<uint32_t>(RttTileData::VT_TILEID, tileid, 0);
  }
  void add_time_stamp(uint32_t time_stamp) {
    fbb_.AddElement<uint32_t>(RttTileData::VT_TIME_STAMP, time_stamp, 0);
  }
  void add_interval(uint32_t interval) {
    fbb_.AddElement<uint32_t>(RttTileData::VT_INTERVAL, interval, 0);
  }
  void add_unit(uint16_t unit) {
    fbb_.AddElement<uint16_t>(RttTileData::VT_UNIT, unit, 0);
  }
  void add_sw_conner(const LineRttStatus::Point *sw_conner) {
    fbb_.AddStruct(RttTileData::VT_SW_CONNER, sw_conner);
  }
  void add_layers(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::LayerData>>> layers) {
    fbb_.AddOffset(RttTileData::VT_LAYERS, layers);
  }
  explicit RttTileDataBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RttTileData> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RttTileData>(end);
    return o;
  }
};

inline flatbuffers::Offset<RttTileData> CreateRttTileData(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t code = 0,
    uint32_t tileid = 0,
    uint32_t time_stamp = 0,
    uint32_t interval = 0,
    uint16_t unit = 0,
    const LineRttStatus::Point *sw_conner = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::LayerData>>> layers = 0) {
  RttTileDataBuilder builder_(_fbb);
  builder_.add_layers(layers);
  builder_.add_sw_conner(sw_conner);
  builder_.add_interval(interval);
  builder_.add_time_stamp(time_stamp);
  builder_.add_tileid(tileid);
  builder_.add_unit(unit);
  builder_.add_code(code);
  return builder_.Finish();
}

inline flatbuffers::Offset<RttTileData> CreateRttTileDataDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t code = 0,
    uint32_t tileid = 0,
    uint32_t time_stamp = 0,
    uint32_t interval = 0,
    uint16_t unit = 0,
    const LineRttStatus::Point *sw_conner = 0,
    const std::vector<flatbuffers::Offset<LineRttStatus::LayerData>> *layers = nullptr) {
  auto layers__ = layers ? _fbb.CreateVector<flatbuffers::Offset<LineRttStatus::LayerData>>(*layers) : 0;
  return LineRttStatus::CreateRttTileData(
      _fbb,
      code,
      tileid,
      time_stamp,
      interval,
      unit,
      sw_conner,
      layers__);
}

flatbuffers::Offset<RttTileData> CreateRttTileData(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

struct RttResponseT : public flatbuffers::NativeTable {
  typedef RttResponse TableType;
  int8_t zoom = 0;
  uint32_t version = 0;
  std::vector<std::unique_ptr<LineRttStatus::RttTileDataT>> tiles{};
};

struct RttResponse FLATBUFFERS_FINAL_CLASS : private flatbuffers::Table {
  typedef RttResponseT NativeTableType;
  typedef RttResponseBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ZOOM = 4,
    VT_VERSION = 6,
    VT_TILES = 8
  };
  int8_t zoom() const {
    return GetField<int8_t>(VT_ZOOM, 0);
  }
  uint32_t version() const {
    return GetField<uint32_t>(VT_VERSION, 0);
  }
  const flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RttTileData>> *tiles() const {
    return GetPointer<const flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RttTileData>> *>(VT_TILES);
  }
  bool Verify(flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_ZOOM) &&
           VerifyField<uint32_t>(verifier, VT_VERSION) &&
           VerifyOffset(verifier, VT_TILES) &&
           verifier.VerifyVector(tiles()) &&
           verifier.VerifyVectorOfTables(tiles()) &&
           verifier.EndTable();
  }
  RttResponseT *UnPack(const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  void UnPackTo(RttResponseT *_o, const flatbuffers::resolver_function_t *_resolver = nullptr) const;
  static flatbuffers::Offset<RttResponse> Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);
};

struct RttResponseBuilder {
  typedef RttResponse Table;
  flatbuffers::FlatBufferBuilder &fbb_;
  flatbuffers::uoffset_t start_;
  void add_zoom(int8_t zoom) {
    fbb_.AddElement<int8_t>(RttResponse::VT_ZOOM, zoom, 0);
  }
  void add_version(uint32_t version) {
    fbb_.AddElement<uint32_t>(RttResponse::VT_VERSION, version, 0);
  }
  void add_tiles(flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RttTileData>>> tiles) {
    fbb_.AddOffset(RttResponse::VT_TILES, tiles);
  }
  explicit RttResponseBuilder(flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  flatbuffers::Offset<RttResponse> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = flatbuffers::Offset<RttResponse>(end);
    return o;
  }
};

inline flatbuffers::Offset<RttResponse> CreateRttResponse(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t zoom = 0,
    uint32_t version = 0,
    flatbuffers::Offset<flatbuffers::Vector<flatbuffers::Offset<LineRttStatus::RttTileData>>> tiles = 0) {
  RttResponseBuilder builder_(_fbb);
  builder_.add_tiles(tiles);
  builder_.add_version(version);
  builder_.add_zoom(zoom);
  return builder_.Finish();
}

inline flatbuffers::Offset<RttResponse> CreateRttResponseDirect(
    flatbuffers::FlatBufferBuilder &_fbb,
    int8_t zoom = 0,
    uint32_t version = 0,
    const std::vector<flatbuffers::Offset<LineRttStatus::RttTileData>> *tiles = nullptr) {
  auto tiles__ = tiles ? _fbb.CreateVector<flatbuffers::Offset<LineRttStatus::RttTileData>>(*tiles) : 0;
  return LineRttStatus::CreateRttResponse(
      _fbb,
      zoom,
      version,
      tiles__);
}

flatbuffers::Offset<RttResponse> CreateRttResponse(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher = nullptr);

inline RoadInfoT *RoadInfo::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RoadInfoT>(new RoadInfoT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RoadInfo::UnPackTo(RoadInfoT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = points(); if (_e) { _o->points.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->points[_i] = *_e->Get(_i); } } }
  { auto _e = relative_points(); if (_e) { _o->relative_points.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->relative_points[_i] = *_e->Get(_i); } } }
  { auto _e = points_z(); if (_e) { _o->points_z.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->points_z[_i] = _e->Get(_i); } } }
}

inline flatbuffers::Offset<RoadInfo> RoadInfo::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RoadInfoT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRoadInfo(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RoadInfo> CreateRoadInfo(flatbuffers::FlatBufferBuilder &_fbb, const RoadInfoT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RoadInfoT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _points = _o->points.size() ? _fbb.CreateVectorOfStructs(_o->points) : 0;
  auto _relative_points = _o->relative_points.size() ? _fbb.CreateVectorOfStructs(_o->relative_points) : 0;
  auto _points_z = _o->points_z.size() ? _fbb.CreateVector(_o->points_z) : 0;
  return LineRttStatus::CreateRoadInfo(
      _fbb,
      _points,
      _relative_points,
      _points_z);
}

inline LayerDataT *LayerData::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<LayerDataT>(new LayerDataT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void LayerData::UnPackTo(LayerDataT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = layer_type(); _o->layer_type = _e; }
  { auto _e = priority(); _o->priority = _e; }
  { auto _e = extra_priority(); _o->extra_priority = _e; }
  { auto _e = class_code(); _o->class_code = _e; }
  { auto _e = roads(); if (_e) { _o->roads.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->roads[_i] = std::unique_ptr<LineRttStatus::RoadInfoT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<LayerData> LayerData::Pack(flatbuffers::FlatBufferBuilder &_fbb, const LayerDataT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateLayerData(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<LayerData> CreateLayerData(flatbuffers::FlatBufferBuilder &_fbb, const LayerDataT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const LayerDataT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _layer_type = _o->layer_type;
  auto _priority = _o->priority;
  auto _extra_priority = _o->extra_priority;
  auto _class_code = _o->class_code;
  auto _roads = _o->roads.size() ? _fbb.CreateVector<flatbuffers::Offset<LineRttStatus::RoadInfo>> (_o->roads.size(), [](size_t i, _VectorArgs *__va) { return CreateRoadInfo(*__va->__fbb, __va->__o->roads[i].get(), __va->__rehasher); }, &_va ) : 0;
  return LineRttStatus::CreateLayerData(
      _fbb,
      _layer_type,
      _priority,
      _extra_priority,
      _class_code,
      _roads);
}

inline RttTileDataT *RttTileData::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RttTileDataT>(new RttTileDataT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RttTileData::UnPackTo(RttTileDataT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = code(); _o->code = _e; }
  { auto _e = tileid(); _o->tileid = _e; }
  { auto _e = time_stamp(); _o->time_stamp = _e; }
  { auto _e = interval(); _o->interval = _e; }
  { auto _e = unit(); _o->unit = _e; }
  { auto _e = sw_conner(); if (_e) _o->sw_conner = std::unique_ptr<LineRttStatus::Point>(new LineRttStatus::Point(*_e)); }
  { auto _e = layers(); if (_e) { _o->layers.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->layers[_i] = std::unique_ptr<LineRttStatus::LayerDataT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<RttTileData> RttTileData::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRttTileData(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RttTileData> CreateRttTileData(flatbuffers::FlatBufferBuilder &_fbb, const RttTileDataT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RttTileDataT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _code = _o->code;
  auto _tileid = _o->tileid;
  auto _time_stamp = _o->time_stamp;
  auto _interval = _o->interval;
  auto _unit = _o->unit;
  auto _sw_conner = _o->sw_conner ? _o->sw_conner.get() : 0;
  auto _layers = _o->layers.size() ? _fbb.CreateVector<flatbuffers::Offset<LineRttStatus::LayerData>> (_o->layers.size(), [](size_t i, _VectorArgs *__va) { return CreateLayerData(*__va->__fbb, __va->__o->layers[i].get(), __va->__rehasher); }, &_va ) : 0;
  return LineRttStatus::CreateRttTileData(
      _fbb,
      _code,
      _tileid,
      _time_stamp,
      _interval,
      _unit,
      _sw_conner,
      _layers);
}

inline RttResponseT *RttResponse::UnPack(const flatbuffers::resolver_function_t *_resolver) const {
  auto _o = std::unique_ptr<RttResponseT>(new RttResponseT());
  UnPackTo(_o.get(), _resolver);
  return _o.release();
}

inline void RttResponse::UnPackTo(RttResponseT *_o, const flatbuffers::resolver_function_t *_resolver) const {
  (void)_o;
  (void)_resolver;
  { auto _e = zoom(); _o->zoom = _e; }
  { auto _e = version(); _o->version = _e; }
  { auto _e = tiles(); if (_e) { _o->tiles.resize(_e->size()); for (flatbuffers::uoffset_t _i = 0; _i < _e->size(); _i++) { _o->tiles[_i] = std::unique_ptr<LineRttStatus::RttTileDataT>(_e->Get(_i)->UnPack(_resolver)); } } }
}

inline flatbuffers::Offset<RttResponse> RttResponse::Pack(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT* _o, const flatbuffers::rehasher_function_t *_rehasher) {
  return CreateRttResponse(_fbb, _o, _rehasher);
}

inline flatbuffers::Offset<RttResponse> CreateRttResponse(flatbuffers::FlatBufferBuilder &_fbb, const RttResponseT *_o, const flatbuffers::rehasher_function_t *_rehasher) {
  (void)_rehasher;
  (void)_o;
  struct _VectorArgs { flatbuffers::FlatBufferBuilder *__fbb; const RttResponseT* __o; const flatbuffers::rehasher_function_t *__rehasher; } _va = { &_fbb, _o, _rehasher}; (void)_va;
  auto _zoom = _o->zoom;
  auto _version = _o->version;
  auto _tiles = _o->tiles.size() ? _fbb.CreateVector<flatbuffers::Offset<LineRttStatus::RttTileData>> (_o->tiles.size(), [](size_t i, _VectorArgs *__va) { return CreateRttTileData(*__va->__fbb, __va->__o->tiles[i].get(), __va->__rehasher); }, &_va ) : 0;
  return LineRttStatus::CreateRttResponse(
      _fbb,
      _zoom,
      _version,
      _tiles);
}

inline const LineRttStatus::RttResponse *GetRttResponse(const void *buf) {
  return flatbuffers::GetRoot<LineRttStatus::RttResponse>(buf);
}

inline const LineRttStatus::RttResponse *GetSizePrefixedRttResponse(const void *buf) {
  return flatbuffers::GetSizePrefixedRoot<LineRttStatus::RttResponse>(buf);
}

inline bool VerifyRttResponseBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<LineRttStatus::RttResponse>(nullptr);
}

inline bool VerifySizePrefixedRttResponseBuffer(
    flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<LineRttStatus::RttResponse>(nullptr);
}

inline void FinishRttResponseBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<LineRttStatus::RttResponse> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedRttResponseBuffer(
    flatbuffers::FlatBufferBuilder &fbb,
    flatbuffers::Offset<LineRttStatus::RttResponse> root) {
  fbb.FinishSizePrefixed(root);
}

inline std::unique_ptr<LineRttStatus::RttResponseT> UnPackRttResponse(
    const void *buf,
    const flatbuffers::resolver_function_t *res = nullptr) {
  return std::unique_ptr<LineRttStatus::RttResponseT>(GetRttResponse(buf)->UnPack(res));
}

inline std::unique_ptr<LineRttStatus::RttResponseT> UnPackSizePrefixedRttResponse(
    const void *buf,
    const flatbuffers::resolver_function_t *res = nullptr) {
  return std::unique_ptr<LineRttStatus::RttResponseT>(GetSizePrefixedRttResponse(buf)->UnPack(res));
}

}  // namespace LineRttStatus

#endif  // FLATBUFFERS_GENERATED_LINERTTSTATUS_LINERTTSTATUS_H_
