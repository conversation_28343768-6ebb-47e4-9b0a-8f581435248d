// Generated by FindMBS.cmake::mbs_generate_version_flags, do not edit.

#ifndef NERD_API_INCLUDE_VERSION_FLAGS_H_
#define NERD_API_INCLUDE_VERSION_FLAGS_H_

//
// Informations for version
//

// Module Name
#define NERD_API_MODULE_NAME "nerdapi"
// Version
#define NERD_API_VERSION "0.1.0.0"
// Full Version 
#define NERD_API_VERSION_FULL "3.00.00_20250224_01"

//
// Informations for git repository
//

// Branch name
#define NERD_API_BRANCH_NAME "Unknown"
// Last commit hash
#define NERD_API_COMMIT_HASH "a7f458578893023838277c5eaa2cb3c200691de2"
// Last commit datetime
#define NERD_API_COMMIT_DATETIME "2025-02-13 03:27:55"
// Genarate this file's datetime
#define NERD_API_BUILD_DATETIME "2025-02-24 19:42:45"
// Uncommit changes
#define NERD_API_HAS_LOCAL_CHANGE 0
// Pangu version, empty if current build process is not in the pangu shell
#define NERD_API_PANGU_VERSION ""
// Pangu brand, for example:"wecar", "sosomap", "opensdk", or ""
#define NERD_API_PANGU_BRAND ""



#endif // NERD_API_INCLUDE_VERSION_FLAGS_H_
