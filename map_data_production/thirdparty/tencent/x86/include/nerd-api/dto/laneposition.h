/*
 * Copyright (c) 2021 Tencent Inc. All rights reserved.
 * @version: 1.0
 * @Author: wallstwang
 * @Date: 2021-03-17 16:02:00
 * @LastEditors: wallstwang
 * @LastEditTime: 2021-03-18 18:06:24
 */

#pragma once

#include <vector>
#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * @brief 坡度曲率ID的Hash值
 */
class LanePositionIDTypeHash {
 public:
  size_t operator()(const LanePositionIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.lane_pos_id);
  }
};

/**
 * @brief 坡度曲率数据
 */
struct LanePosition {
  /**
   * ID
   */
  LanePositionIDType id{0, 0};
  /**
   * 车道中心点的索引
   */
  SeqNumType seq_num{0};
  /**
   * 取值范围，[-90.000, 90.000]，单位 度
   * 正数为内向倾斜
   * 负数为外向倾斜
   */
  SlopeType h_slope{0};
  /**
   * 取值范围，[-90.000, 90.000]，单位 度
   * 正数为上坡
   * 负数为下坡
   */
  SlopeType v_slope{0};
  /**
   * 取值范围[-1.000000, 1.000000]，
   * 左偏为正，右偏为负
   */
  CurvatureType curvature{0};
  /**
   * 航向, 车辆行驶方向与正北方向的顺时针夹角
   * 取值范围(0,360]
   * 单位：度
   */
  AngleDegreeType heading;
  /**
   * 车道宽度
   * 单位: 厘米
   */
  LaneWidthType width{0};
};

}  // namespace api
}  // namespace nerd
