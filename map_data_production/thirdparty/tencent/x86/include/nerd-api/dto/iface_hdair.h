//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/3/12.
//

#pragma once

#include "types.h"
#include "dto/iface_link.h"

/**
 * 该文件和命名空间下定义了 HDAirToB 的所有数据结构和接口;
 * 备注: 由于编译HDAir ToB和ToC的依赖和编译过程不同, 类型很难对其, 使用方也不同,
 *       因此ToB的结构在新命名空间下重新定义了;
 */
namespace nerd {
namespace api {
namespace hdair {

/**
 * HDAirToB 车道类型定义
 */
enum class LaneType : uint8_t {
  NONE                    = 0,  // 无
  REGULAR                 = 1,  // 普通车道
  COMPOUND                = 2,  // 加减速复合车道
  LEFT_ACCELERATION       = 3,  // 左侧加速车道
  LEFT_DECELERATION       = 4,  // 左侧减速车道
  HOV                     = 5,  // HOV
  SLOW                    = 6,  // 慢车道
  SHOULDER                = 7,  // 路肩
  DRIVABLE_SHOULDER       = 8,  // 可行驶路肩
  CONTROL                 = 9,  // 管制车道
  EMERGENCY_PARKING_STRIP = 10, // 紧急停车带
  BUS                     = 11, // 公交车道
  BICYCLE                 = 12, // 非机动车道
  TIDE                    = 13, // 潮汐车道
  DIRECTION_CHANGE        = 14, // 可变车道
  PARKING_ROAD            = 15, // 停车车道
  DRIVABLE_PARKING_ROAD   = 16, // 可行驶停车道
  TRUCK                   = 17, // 货车专用道
  TIME_LIMIT_BUS          = 18, // 限时公交车道
  PASSING_BAY             = 19, // 错车道
  REVERSAL_LEFT_TURN      = 20, // 借道左转车道
  TAXI                    = 21, // 出租车车道
  TURN_LEFT_WAITING       = 22, // 转弯待转区车道
  DIRECTION_LOCKED        = 23, // 定向车道
                                 //
                                 //
                                 //
  STRAIGHT_WAITING        = 27, //
  VEHICLE_BICYCLE_MIX     = 28, // 机非混合车道
  MOTOR                   = 29, // 摩托车道
                                 //
  TOLL                    = 31, // 收费站车道
  CHECK_POINT             = 32, // 检查站车道
  DANGEROUS_ARTICLE       = 33, // 危险品专用车道
  FORBIDDEN_DRIVE         = 34, // 非行驶区域
  THOUGH_LANE_ZONE        = 35, // 借道区
  STREET_RAILWAY          = 36, // 有轨电车车道
  BUS_BAY                 = 37, // 公交港湾车道
  SPECIAL_CAR             = 38, // 特殊车辆专用车道
  PEDESTRIANS             = 39, // 人行道
  EMERGENCY               = 40, // 应急车道
  HEDGING                 = 41, // 避险车道
  BUS_LANE_WITH_TIME      = 42, // 时间段公交车道
  EMPTY                   = 43, // 空车道
  RIGHT_ACCELERATION      = 44, // 右侧加速车道
  RIGHT_DECELERATION      = 45, // 右侧减速车道
  SLOPE                   = 46, // 爬跑车道
  REVERSE_NON_VEHICLE     = 47, // 逆向非机动车道
  EDGE                    = 48, // 边缘车道
  OTHER                   = 99, // 其他车道
};

/**
 * HDAirToB 车道箭头类型定义
 */
enum class LaneArrowType : uint8_t {
  LaneArrowTypeStraightAhead,                             // 正前方直行
  LaneArrowTypeTurnRightFront30,                          // 右斜前（顺时针正北方向30°）
  LaneArrowTypeTurnRightFront60,                          // 右斜前（顺时针正北方向60°，已废弃）
  LaneArrowTypeTurnRight90,                               // 90度右转
  LaneArrowTypeTurnRightDown30,                           // 右斜下（顺时针正东方向30°，已废弃）
  LaneArrowTypeTurnRightDown60,                           // 右斜下（顺时针正东方向60°，已废弃）
  LaneArrowTypeUTurnLeft,                                 // 左掉头
  LaneArrowTypeTurnLeftDown30,                            // 左斜下（顺时针正南方向30°，已废弃）
  LaneArrowTypeTurnLeftDown60,                            // 左斜下（顺时针正南方向60°，已废弃）
  LaneArrowTypeTurnLeft90,                                // 90度左转
  LaneArrowTypeTurnLeftFront30,                           // 左斜前（顺时针正西方向30°，已废弃）
  LaneArrowTypeTurnLeftFront60,                           // 左斜前（顺时针正西方向60°）
  LaneArrowTypeUTurnRight,                                // 右掉头
  LaneArrowTypeUnknown,                                   // 待确认（已废弃）
  LaneArrowTypeBlank                                      // 空
};

/**
 * HDAirToB 车道拓扑变化类型
 */
enum class LaneChangeType : uint8_t {
  LeftTurnExpandingLane   = 1,                            // 左向扩展车道
  RightTurnExpandingLane  = 2,                            // 右向扩展车道
  LeftTurnMergingLane     = 3,                            // 左向汇入车道
  RightTurnMergingLane    = 4,                            // 右向汇入车道
  BothDirectionExpandingLane  = 5,                        // 双向扩展车道
  BothDirectionMergingLane    = 6,                        // 双向汇入车道
  OtherLane                   = 7,                        // 其他
  NotApplicable               = 99,                       // 不适用
};

/**
 * @brief 车道限制类型
 */
enum class LaneRestrictionType : uint8_t {
  /**
   * 无
   */
  kNone = 0,
  /**
   * 禁止通行
   */
  kFORBIDDEN,
  /**
   * 公交专用道
   */
  kBUS,
  /**
   * 多成员车道
   */
  kHOV,
  /**
   * 潮汐车道
   */
  kTIDE,
  /**
   * 其他
   */
  kOTHER = 0xFF,
};

/**
 * HDAirToB 车道边类型定义
 */
enum class BorderType : uint8_t {
  LaneBorderTypeUnknown = 0,                              // 未调查
  LaneBorderDashLine,                                     // 普通虚线
  LaneBorderShortThickDashLine,                           // 短粗虚线
  LaneBorderTurnWaitingLine,                              // 待转区标线
  LaneBorderNormalSolidLine,                              // 普通实线
  LaneBorderDiversionZoneLine,                            // 导流区线
  LaneBorderParkingZoneLine,                              // 停车位标线
  LaneBorderThickSolidLine,                               // 粗实线
  LaneBorderLeftDashRightSolidLine,                       // 左虚右实(通过4K数组进行还原，问题：是否0左1右)
  LaneBorderLeftSolidRightDashLine,                       // 左实右虚(通过4K数组进行还原)
  LaneBorderDoubleSolidLine,                              // 双实线(通过4K数组进行还原：双普通实线)
  LaneBorderDoubleDashLine,                               // 双虚线(通过4K数组进行还原：双普通虚线)
  LaneBorderOtherLine,                                    // 其它线
  LaneBorderRoadEdgeLine,                                 // 道路边缘（来源4K border type的道路边缘）
  LaneBorderPhysicalBarrier,                              // 物理隔离 (来源4K border type，如下：道路设施边界、路缘石、隔离桩、护栏、墙、防护网、施工围挡、移动护栏)
  LaneBorderVirtualLine,                                  // 虚拟线
  LaneBorderNone                                          // 无
};

/**
 * HDAirToB 车道边颜色类型定义
 */
enum class BorderColorType : uint8_t {
  LaneBorderColorUnknown = 0,                             // 未调查
  LaneBorderColorWhite,                                   // 白色
  LaneBorderColorYellow,                                  // 黄色
  LaneBorderColorBlue,                                    // 蓝色
  LaneBorderColorOrange,                                  // 橙色
  LaneBorderColorOther,                                   // 其它
  LaneBorderColorRed,                                     // 红色
  LaneBorderColorGreen,                                   // 绿色
  LaneBorderColorLeftYellowRightWhite,                    // 左黄右白
  LaneBorderColorLeftWhiteRightYellow,                    // 左白右黄
  LaneBorderColorNone                                     // 无
};

typedef std::vector<BorderType> BorderTypeVec;
typedef std::vector<BorderColorType> BorderColorVec;

/**
 * HDAirToB 车道长实线类型定义
 */
enum class LaneLslType : uint8_t {
  NONE        = 0,  // 无长实线
  LEFT_LSL    = 1,  // 左侧长实线
  RIGHT_LSL   = 2,  // 右侧长实线
  BOTH_LSL    = 3,  // 两侧长实线
  UNKNOWN     = 4   // 未调查
};

/**
 * HDAirToB 车道变化信息
 */
struct LaneChangeInfo {
  /**
   * 车道边类型列表
   */
  BorderTypeVec border_type_vec;
  /**
   * 车道边颜色类型列表
   */
  BorderColorVec border_color_vec;
  /**
   * 变化位置相对Lane起点的开始位置比例
   */
  float start_offset{0.0};
  /**
   * 变化位置相对Lane起点的结束位置比例
   */
  float end_offset{1.0};
};
typedef std::vector<LaneChangeInfo> LaneChangeInfoVec;

/**
 * HDAirToB 车道交限车辆类型
 */
enum class VehicleType : uint8_t {
  NONE,                           // 未知
  CAR,                            // 小客车
  LOCAL_TRUCK,                    // 本地货车
  NONLOCAL_TRUCK,                 // 外地货车
  AMBULANCE,                      // 急救车
  TAXI,                           // 出租车
  BUS,                            // 公交车
  WALKER,                         // 步行者
  BICYCLE,                        // 自行车
  ELECTRIC_BICYCLE,               // 电动自行车
  MOTORCYCLE,                     // 摩托车
  MULTI_PASSENGER,                // 多人乘坐车辆
  WITH_TRAILER,                   // 拖挂车
  DANGEROUS,                      // 危险品车辆
  TRANSIT,                        // 过境车辆
  MINIVAN,                        // 小型货车（电动三轮车，拖拉机，三轮汽车）
  ALL,                            // 全部车辆
  LARGE_BUS,                      // 大型客车
  LARGE_TRUCK,                    // 大型货车
  OTHER,                          // 其它机动车辆
  ELECTRIC_CAR,                   // 电动小汽车
  LIGHT_TRUCK,                    // 轻型货车
  MINI_TRUCK,                     // 微型货车
  MEDIUM_TRUCK,                   // 中型货车
  HEAVY_TRUCK,                    // 重型货车
  EXTERNAL,                       // 外部车辆
};

/**
 * HDAirToB 车道交限特殊时间类型
 */
enum class SpecialTimeType : uint8_t {
  NONE = 0,
  WORKDAY,                        // 工作日
  NOT_WORKDAY,                    // 非工作日
  HOLIDAY,                        // 节假日
  HOLIDAY_EXCEPT,                 // 节假日除外
  SEASON_SPRING,                  // 春季
  SEASON_SUMMER,                  // 夏季
  SEASON_AUTUMN,                  // 秋季
  SEASON_WINTER,                  // 冬季
  SEASON_RAIN,                    // 雨季
  SEASON_EXCEPT_RAIN,             // 雨季除外
  SEASON_DRY,                     // 干季
  SEASON_EXCEPT_DRY,              // 干季除外
  SEASON_COLD,                    // 寒假
  SEASON_EXCEPT_COLD,             // 寒假除外
  SEASON_HOT,                     // 暑假
  SEASON_EXCEPT_HOT,              // 暑假除外
  PEAK_OFF_SEASON,                // 淡季
  PEAK_EXCEPT_OFF_SEASON,         // 淡季除外
  PEAK_PEAK_SEASON,               // 旺季
  PEAK_EXCEPT_PEAK_SEASON,        // 旺季除外
  COMMUTE_MORNING_PEAK,           // 早高峰
  COMMUTE_EXCEPT_MORNING_PEAK,    // 早高峰除外
  COMMUTE_EVENING_PEAK,           // 晚高峰
  COMMUTE_EXCEPT_EVENING_PEAK,    // 晚高峰除外
  SCHOOL_LEAVING_SCHOOL,          // 放学期间
  SCHOOL_EXCEPT_LEAVING_SCHOOL,   // 放学期间除外
  CONSTRUCTION_DURING,            // 施工期间
  CONSTRUCTION_EXCEPT,            // 施工期间除外
  WEATHER_RAIN,                   // 雨
  WEATHER_SNOW,                   // 雪
  WEATHER_FOG,                    // 雾
  WEATHER_HAIL,                   // 冰雹
  WEATHER_WIND,                   // 风
};

/**
 * HDAirToB 定义特征点变化类型枚举
 */
enum class FeaturePointType : uint8_t {
  LaneTypeChange,                                         // 车道类型变化
  LaneCountChange,                                        // 车道数变化
  DataBoundaryStart,                                      // 数据边界开始
  DataBoundaryEnd,                                        // 数据边界结束
  LaneContinuityPoint,                                    // 车道连续点
  ExchangeAreaStart,                                      // 交换区起点
  ExchangeAreaEnd,                                        // 交换区终点
  RegularIntersectionEntrance,                            // 普通路口进入点
  RegularIntersectionExit,                                // 普通路口退出点
  UTurnIntersectionEntrance,                              // 掉头路口进入点
  UTurnIntersectionExit,                                  // 掉头路口退出点
  WideLaneEntrance,                                       // 宽车道进入点
  WideLaneExit,                                           // 宽车道退出点
  TollBoothEntrance,                                      // 收费站进入点
  TollBoothExit,                                          // 收费站退出点
  ExchangeAreaInteriorPoint,                              // 交换区内点
  WideLaneInteriorPoint,                                  // 宽车道内点
  TollBoothInteriorPoint,                                 // 收费站内点
  MarkingLineChangePoint,                                 // 标线变化点
  TIntersectionEntrancePoint                              // T型路口进入点
};

/**
 * HDAirToB 定义停止线类型枚举
 */
enum class StopLineType : uint8_t {
  SLT_Unknown = 0,                          // 未分类
  SLT_StopLine,                             // 停止线
  SLT_YieldLine,                            // 停车让行线
  SLT_DecelerationLine,                     // 减速让行线
  SLT_VirtualStopLine,                      // 虚拟停止线
  SLT_SlowLine,                             // 慢行线
  SLT_LeftTurnWaitingAreaStopLine,          // 左转待转区停止线
  SLT_StraightWaitingAreaStopLine,          // 直行待行区停止线
  SLT_Other,                                // 其他
  SLT_TideLaneStopLine,                     // 潮汐车道停止线
  SLT_ReverseStopLine,                      // 逆向停止线
  SLT_OtherWaitingAreaStopLine,             // 其他待转区停止线
  SLT_RightTurnWaitingAreaStopLine,         // 右转待转区停止线
  SLT_TurnRoundWaitingAreaStopLine          // 调头待转区停止线
};

/**
 * Lane(车道) ID定义
 */
using LaneIDType = FeatureIDType;
/**
 * LaneGroup(车道组) ID定义
 */
using LaneGroupIDType = FeatureIDType;
/**
 * LaneRestriction(车道交限) ID定义
 */
using LaneRestrictionIDType = FeatureIDType;
/**
 * FeaturePoint(要素特征点) ID定义
 */
using FeaturePointIDType = FeatureIDType;
/**
 * StopLine(停止线要素定义) ID 定义
 */
using StopLineIDType = FeatureIDType;
/**
 * IntersectionRoad(路口虚拟道路要素定义) ID 定义
 */
using IntersectionRoadID = FeatureIDType;
/**
 * IntersectionRoadNode(路口虚拟道路节点要素定义) ID 定义
 */
using IntersectionRoadNodeID = FeatureIDType;

/**
 * @brief 限行条件
 */
class NERD_EXPORT IRestriction {
 public:
  /**
   * @brief 获取ID
   * @return 交规ID
   */
  virtual LaneRestrictionIDType GetID() const = 0;
  /**
   * @brief 限制车辆类型
   * @return 如果没有则为空
   */
  virtual const std::vector<VehicleType>& GetLimitVehicleType() const = 0;
  /**
   * @brief 获取该交限时间类型限制
   * @return 时间限制
   */
  virtual const std::vector<SpecialTimeType>& GetSpecialTimeType() const = 0;
  /**
   * @brief 该交限起作用的时间段信息
   * @return 时间段描述字符
   */
  virtual const std::string& GetRestrictTimePeriod() const = 0;
  /**
   * @brief 限制方向
   * @return 顺方向/逆方向
   */
  virtual LaneGroupDirection GetDirection() const = 0;
  /**
   * @brief 车道限制类型
   * @return 车道限制类型
   */
  virtual LaneRestrictionType GetLaneRestrictionType() const = 0;
  /**
   * @brief 沿道路行驶方向，除10000表示[0,1]，例如30%~80%的限制范围位置区间，开始位置记为3000
   * @return 作用开始位置
   */
  virtual uint16_t GetRangeStart() const = 0;
  /**
   * @brief 沿道路行驶方向，除10000表示[0,1]，例如30%~80%的限制范围位置区间，结束位置记为8000
   * @return 作用结束位置
   */
  virtual uint16_t GetRangeEnd() const = 0;
};

/**
 * @brief Lane接口定义
 * SD地图中的Lane对象
 * 备注: HDAirToB 规格数据中有效
 */
class NERD_EXPORT ILane {
 public:
  /**
   * 获取该元素对应的ID, 通过索引动态生成;
   * @return 要素ID
   */
  virtual LaneIDType GetID() const = 0;
  /**
   * 获得该元素的车道组ID
   * @return
   */
  virtual LaneGroupIDType GetLaneGroupID() const = 0;
  /**
   * 获得该元素所属的FeaturePoint ID
   * @deprecated 一个Lane可能跨越多个FeaturePoint对象, 因此可能会有多个FeaturePoint ID, 替代接口 GetFeaturePointIDs;
   * @return
   */
  virtual FeaturePointIDType GetFeaturePointID() const = 0;
  /**
   * 获得该元素所属的FeaturePoint ID列表
   * @return
   */
  virtual std::vector<FeaturePointIDType> GetFeaturePointIDs() const = 0;
  /**
   * 获取该元素的车道通行方向
   * @return 方向信息
   */
  virtual LaneGroupDirection GetDirection() const = 0;
  /**
   * 获取该元素车道通行状态
   * @return 通行状态
   */
  virtual ConstructionType GetConstructionType() const = 0;
  /**
   * @brief 获取该车道的类型列表
   * @return 车道类型列表
   */
  virtual const std::vector<LaneType>& GetLaneTypes() const = 0;
  /**
   * 获取该车道的变化类型
   */
  virtual LaneChangeType GetLaneChangeType() const = 0;
  /**
   * 获取车道宽度, 单位:米
   */
  virtual float GetLaneWidth() const = 0;
  /**
   * 获取车道开始位置的宽度, 单位:米
   * @return
   */
  virtual float GetLaneStartWidth() const = 0;
  /**
   * 获取车道结束位置的宽度, 单位:米
   * @return
   */
  virtual float GetLaneEndWidth() const = 0;
  /**
   * @brief 车道编号
   * 备注: 从左到右，从1开始
   * @return 车道编号
   */
  virtual SeqNumType GetSeqNum() const = 0;
  /**
   * @brief 车道箭头方向，{@link LaneArrowType}
   * @return 返回车道箭头类型列表
   */
  virtual const std::vector<LaneArrowType>& GetLaneArrowTypes() const = 0;
  /**
   * @brief 当前车道的通行限制条件
   * @return 如果没有，数组为空
   */
  virtual const std::vector<std::shared_ptr<IRestriction>>& GetAccessRestrict() const = 0;
  /**
   * 获取左右边的标线类型;
   * @param is_left 是否左侧
   * @return BorderTypeVec
   */
  virtual BorderTypeVec GetBorderTypes(const bool is_left) const = 0;
  /**
   * 获取左右边的颜色类型
   * @param is_left 是否左侧
   * @return BorderColorVec
   */
  virtual BorderColorVec GetBorderColors(const bool is_left) const = 0;
  /**
   * 获取左右边的车道变化信息列表
   * @param is_left 是否左侧
   * @return LaneChangeInfoVec
   */
  virtual LaneChangeInfoVec GetLaneChangeInfos(const bool is_left) const = 0;
  /**
   * @brief 获取下游LaneID
   * @return 下游LaneID集合
   */
  virtual std::vector<LaneIDType> GetNextIDs() const = 0;
  /**
   * @brief 获取上游LaneID
   * @return 上游LaneID集合
   */
  virtual std::vector<LaneIDType> GetPreviousIDs() const = 0;
  /**
   * 获取lsl类型
   * @return
   */
  virtual LaneLslType GetLaneLslType() const = 0;
};

/**
 * @brief FeaturePoint基类
 * SD地图中的FeaturePoint对象
 * 备注: HDAirToB规格数据中有效
 */
class NERD_EXPORT IFeaturePoint {
 public:
  /**
   * 获取该元素对应的ID, 通过索引动态生成;
   * @return 要素ID
   */
  virtual FeaturePointIDType GetID() const = 0;
  /**
   * 获取变化点类型类别
   */
  virtual const std::vector<FeaturePointType>& GetTypes() const = 0;
  /**
   * 获取方向(双向/正向逆向信息)
   */
  virtual LinkDirection GetDirection() const = 0;
  /**
   * 获取FeaturePoint位置坐标
   */
  virtual Coordinate GetLocation() const = 0;
  /**
   * 获取对应的LinkID信息
   * @return
   */
  virtual LinkIDType GetLinkID() const = 0;
  /**
   * 获取投影比例, 值域: [0.0, 1.0]
   * 备注: 投影到link的比例，从link起点开始计算，如45%位置记录为0.45
   * @return 比例
   */
  virtual float GetProjectPercent() const = 0;
  /**
   * 获取该点包含的车道ID列表
   * @return 车道ID列表
   */
  virtual const std::vector<LaneIDType>& GetLaneIDs() const = 0;
  /**
   * 获取该点包含的车道信息
   * @return 车道对象列表
   */
  virtual const std::vector<std::shared_ptr<ILane>>& GetLanes() const = 0;
  /**
   * 获取该FeaturePoint关联的前序FeaturePointID列表
   * @return FeaturePointID列表
   */
  virtual std::vector<FeaturePointIDType> GetPreviousIDs() const = 0;
  /**
   * 获取该FeaturePoint关联的后继FeaturePointID列表
   * @return FeaturePointID列表
   */
  virtual std::vector<FeaturePointIDType> GetNextIDs() const = 0;
};

/**
 * @brief StopLine基类
 * SD地图中的StopLine对象
 * 备注: HDAirToB规格数据中有效
 */
class NERD_EXPORT IStopLine {
 public:
  /**
   * 获取该元素对应的ID, 通过索引动态生成;
   * @return 要素ID
   */
  virtual StopLineIDType GetID() const = 0;
  /**
   * 获取停止线的类型
   * @return
   */
  virtual StopLineType GetStopLineType() const = 0;
  /**
   * 获取停止线的位置坐标
   * @return 坐标
   */
  virtual Coordinate GetLocation() const = 0;
  /**
   * 获取关联的NodeID信息
   * @return NodeID
   */
  virtual NodeIDType GetRelatedNodeID() const = 0;
  /**
   * 获取关联的LaneID信息
   * @return LaneID列表
   */
  virtual std::vector<LaneIDType> GetRelatedLaneIDs() const = 0;
  /**
   * 获取关联的IntersectionRoadID信息
   * @return IntersectionRoadID列表
   */
  virtual std::vector<IntersectionRoadID> GetRelatedIntersectionRoadIDs() const = 0;
};

/**
 * @brief 斑马线
 * 备注: HDAirToB规格数据中有效
 */
class NERD_EXPORT IZebra {
 public:
  /**
   * @brief 获取ID
   * @return FeatureIDType
   */
  virtual FeatureIDType GetID() const = 0;
  /**
   * 获取方向(双向/正向逆向信息)
   */
  virtual LaneGroupDirection GetDirection() const = 0;
  /**
   * 获取停止线的位置坐标
   * @return 坐标
   */
  virtual Coordinate GetLocation() const = 0;
  /**
   * 获取关联的NodeID信息
   * @return NodeID
   */
  virtual NodeIDType GetRelatedNodeID() const = 0;
  /**
   * @brief 获取当前Road的LinkID
   * @return LinkIDType
   */
  virtual LinkIDType GetRelatedLinkID() const = 0;
};

/**
 * 路口车道转向信息
 */
enum class CrossTurnType : uint8_t {
  NONE = 0,           /** 不应用 */
  STRAIGHT = 1,       /** 直行 */
  LEFT = 2,           /** 左转 */
  RIGHT = 3,          /** 右转 */
  LEFT_TURN = 4,      /** 左掉头 */
  RIGHT_TURN = 5      /** 左掉头 */
};

/**
 * 路口虚拟道路节点
 */
class NERD_EXPORT IIntersectionRoadNode {
 public:
  /**
   * @brief 获取NodeID
   * @return NodeID
   */
  virtual IntersectionRoadNodeID GetNodeID() const = 0;
  /**
   * @brief Link Node坐标
   * @return Node坐标
   */
  virtual Coordinate GetPosition() const = 0;
  /**
   * @brief 获取进入的所有IntersectionRoadID集合
   * @return 进入的IntersectionRoadID集合
   */
  virtual std::vector<IntersectionRoadID> GetEnterIntersectionRoadIDs() const = 0;
  /**
   * @brief 获取出度的所有IntersectionRoadID集合
   * @return 出度的IntersectionRoadID集合
   */
  virtual std::vector<IntersectionRoadID> GetOutIntersectionRoadIDs() const = 0;
};

/**
 * 路口虚拟道路
 */
class NERD_EXPORT IIntersectionRoad {
 public:
  /**
   * @brief 获取NodeID
   * @return NodeID
   */
  virtual IntersectionRoadID GetRoadID() const = 0;
  /**
   * 获取方向(双向/正向逆向信息)
   */
  virtual LinkDirection GetDirection() const = 0;
  /**
   * 获取路口的转向信息
   * @return 路口标识
   */
  virtual CrossTurnType GetCrossTurnType() const = 0;
  /**
   * 获取红绿灯标识
   * @return 有红绿灯返回 true，否则返回 false
   */
  virtual bool GetTrafficLightFlag() const = 0;
  /**
   * @brief Road几何形状
   * @return 数据坐标系
   */
  virtual const std::shared_ptr<std::vector<Coordinate>> GetGeometry() const = 0;
  /**
   * @brief 当前Road起点的NodeID
   * @return IntersectionRoadNodeID
   */
  virtual IntersectionRoadNodeID GetStartNodeID() const = 0;
  /**
   * @brief 当前Road终点的NodeID
   * @return IntersectionRoadNodeID
   */
  virtual IntersectionRoadNodeID GetEndNodeID() const = 0;
  /**
   * @brief 获取起点Node
   * @return
   */
  virtual std::weak_ptr<IIntersectionRoadNode> GetStartNode() const = 0;
  /**
   * @brief 获取终点Node
   * @return
   */
  virtual std::weak_ptr<IIntersectionRoadNode> GetEndNode() const = 0;
  /**
   * @brief 当前Road起点的FeaturePointID
   * 备注: 若返回ID为{0,0}代表没有对应的数据;
   * @return FeaturePointIDType
   */
  virtual FeaturePointIDType GetStartFeaturePointID() const = 0;
  /**
   * @brief 当前Road终点的FeaturePointID
   * 备注: 若返回ID为{0,0}代表没有对应的数据;
   * @return FeaturePointIDType
   */
  virtual FeaturePointIDType GetEndFeaturePointID() const = 0;
  /**
   * @brief 获取当前Road的LinkID
   * 备注: 若返回ID为{0,0}代表没有对应的数据;
   * @return LinkIDType
   */
  virtual LinkIDType GetRelatedLinkID() const = 0;
  /**
   * @brief 获取IntersectionRoad的下游IntersectionRoadID
   * @param direction 搜索方向,只能是正向或逆向
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IntersectionRoadID> GetNextIDs(LinkDirection direction) const = 0;
  /**
   * @brief 获取IntersectionRoad的上游IntersectionRoadID
   * @param direction 搜索方向,只能是正向或逆向
   * @return 如果方向错误或不存在返回空数组
   */
  virtual std::vector<IntersectionRoadID> GetPreviousIDs(LinkDirection direction) const = 0;
};

}  // namespace hdair
}  // namespace api
}  // namespace nerd