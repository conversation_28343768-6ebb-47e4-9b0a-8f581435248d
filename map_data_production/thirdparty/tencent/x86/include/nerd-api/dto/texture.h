// Copyright 2022 Tencent Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <vector>

#include "dto/types.h"

namespace nerd {
namespace api {

// 纹理类型
enum class TextureMapUsageType : uint8_t {
  // 纹理贴图
  kTextureType = 0,
  // 法线贴图
  kNormalType = 1,
  // 凹凸贴图
  kBumpType = 2,
};

// 图片格式
enum class ImageFormat : bool {
  kPNG = 0,
  kJPG = 1,
};

// 纹理信息
struct TextureMap {
  // ID, 版本, 标签
  PackedIDType packed_id;
  // 纹理类型
  TextureMapUsageType texture_map_usage_type;
  //  图片格式
  ImageFormat texture_image_format;
  // 图片数据
  std::vector<uint8_t> image;
};

}  // namespace api
}  // namespace nerd
