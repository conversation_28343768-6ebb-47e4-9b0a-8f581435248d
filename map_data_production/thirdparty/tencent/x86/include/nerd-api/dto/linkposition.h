// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by da<PERSON><PERSON><PERSON> on 2022/7/21.
//

#pragma once

#include <vector>
#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * @brief 坡度曲率ID的Hash值
 */
class LinkPositionIDTypeHash {
 public:
  size_t operator()(const LanePositionIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.lane_pos_id);
  }
};

/**
 * @brief 坡度曲率数据
 */
struct LinkPosition {
  /**
   * Link形状点的索引
   */
  SeqNumType seq_num{0};
  /**
   * 横向坡度分级，分级标准0.1度
   * ......
   * -2：[-0.2°, -0.1°)
   * -1：[-0.1°, 0)
   * 0：[0°, 0.1°)
   * 1：[0.1°, 0.2°)
   * 2：[0.2°, 0.3°)
   * ......
   * 正数向左倾斜
   * 负数向右倾斜
   */
  SlopeType h_slope{0};
  /**
   * 纵向坡度分级，分级标准0.1度，同横向坡度
   * 正数为上坡
   * 负数为下坡
   */
  SlopeType v_slope{0};
  /**
   * 曲率分级。分级标准10^(-5) 1/m
   * ......
   * -154：[-0.00154, -0.00153)
   * ......
   * -2：[-0.00002, -0.00001)
   * -1：[-0.00001, 0)
   * 0：[0, 0.00001)
   * 1：[0.00001, 0.00002)
   * 2：[0.00002, 0.00003)
   * ......
   * 154：[0.00154, 0.00155)
   * ......
   */
  CurvatureType curvature{0};
  /**
   * 航向, 车辆行驶方向与正北方向的顺时针夹角
   * 取值范围[0,360)
   * 单位：度
   */
  AngleDegreeType heading{0};
};

struct LinkCurvature {
  /**
   * link起点开始的偏移量
   * 单位：米
   */
  uint32_t offset{0};
  /**
   * 曲率分级。分级标准10^(-5) 1/m
   * ......
   * -154：[-0.00154, -0.00153)
   * ......
   * -2：[-0.00002, -0.00001)
   * -1：[-0.00001, 0)
   * 0：[0, 0.00001)
   * 1：[0.00001, 0.00002)
   * 2：[0.00002, 0.00003)
   * ......
   * 154：[0.00154, 0.00155)
   * ......
   */
  int32_t curvature{0};
};

struct LinkSlope {
  /**
   * link起点开始的偏移量
   * 单位：米
   */
  uint32_t offset{0};
  /**
   * 坡度分级，分级标准0.1度
   * ......
   * -2：[-0.2°, -0.1°)
   * -1：[-0.1°, 0)
   * 0：[0°, 0.1°)
   * 1：[0.1°, 0.2°)
   * 2：[0.2°, 0.3°)
   * ......
   */
  SlopeType slope{0};
};

struct LinkHeading {
  /**
   * link起点开始的偏移量
   * 单位：米
   */
  uint32_t offset{0};
  /**
   * 航向, 车辆行驶方向与正北方向的顺时针夹角
   * 取值范围[0,360)
   * 单位：度
   */
  AngleDegreeType heading;
};

struct LinkOffsetPosition {
  /**
   * 纵向坡度列表,正数上坡，负数下坡
   */
  std::shared_ptr<std::vector<LinkSlope>> link_v_slope_list{nullptr};
  /**
   * 横向坡度列表,正数向左倾斜，负数向右倾斜
   */
  std::shared_ptr<std::vector<LinkSlope>> link_h_slope_list{nullptr};
  /**
   * 曲率列表
   */
  std::shared_ptr<std::vector<LinkCurvature>> link_curvature_list{nullptr};
  /**
   * 航向列表
   */
  std::shared_ptr<std::vector<LinkHeading>> link_heading_list{nullptr};
};

}  // namespace api
}  // namespace nerd
