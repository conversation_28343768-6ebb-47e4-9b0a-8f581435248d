// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by wallst on 2021/6/15.
//

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>
#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * @brief 覆盖面父类
 */
struct IOverlap {
  virtual ~IOverlap() = default;
  OverlapIDType id{0};
  OverlapType overlap_type;

  /**
   * 获取TileID
   * @return TileID
   */
  TileIDType GetTileId() const { return id.tile_id; }

  /**
   * @brief 构建overlap id
   * 非线程安全
   */
  static OverlapIDType IDBuilder(uint32_t tile_id, uint32_t seq) { return {tile_id, seq}; }

  /**
   * @brief 获取tile id
   */
  static TileIDType GetTileIDByOverlapID(OverlapIDType overlap_id) { return overlap_id.tile_id; }

  /**
   * @brief 获取overlap在tile中的索引
   */
  static uint32_t GetSeqByOverlapID(OverlapIDType overlap_id) { return overlap_id.overlap_id; }

  /**
   * @brief 字符串格式输出id, tile_id:seq_num
   */
  std::string IDToString() const { return id.ToString(); }
};
typedef std::shared_ptr<IOverlap> IOverlapPtr;
typedef std::shared_ptr<const IOverlap> IOverlapConstPtr;

/**
 * @brief 车道相关overlap
 */
struct LaneOverlap : public IOverlap {
  union AttachedObjectID {
    SurfaceIDType surface_id;       ///< 关联的surface object id
    LaneIDType lane_id;             ///< 关联的lane id, 只有是虚拟车道之间的overlap关系会有这个值
    CrossAreaIDType cross_area_id;  ///< 关联的cross area id, 车道与cross area形成overlap关系

    AttachedObjectID() {}
    ~AttachedObjectID() {}
  } object_id;

  LaneIDType relative_lane_id;  ///< 当前lane id，即观察视角的lane
  double start_length;          ///< surface object起点距离车道中心线起点距离，单位米
  double end_length;            ///< surface object终点距离车道中心线起点距离，单位米
};
typedef std::shared_ptr<LaneOverlap> LaneOverlapPtr;

/**
 * @brief 覆盖对象集合类
 */
class NERD_EXPORT OverlapCollect {
 private:
  std::unordered_map<OverlapType, std::vector<IOverlapConstPtr>> overlaps_;

 public:
  /**
   * @brief 设置覆盖面对象
   * @param[in] overlaps 覆盖面对象集合
   */
  void SetOverlaps(std::unordered_map<OverlapType, std::vector<IOverlapConstPtr>> &&overlaps) {
    this->overlaps_ = overlaps;
  }

  /**
   * 插入一个overlap
   * @param type
   * @param obj
   */
  void AddOverlap(OverlapType type, const IOverlapConstPtr &obj) {
    const auto &iter_objs = this->overlaps_.find(type);
    if (iter_objs == this->overlaps_.end()) {
      this->overlaps_.insert({type, {}});
    }
    this->overlaps_.at(type).emplace_back(obj);
  }

  const std::unordered_map<OverlapType, std::vector<IOverlapConstPtr>> &GetOverlapMap() const { return overlaps_; }

  /**
   * 批量插入Overlap
   */
  void AddOverlaps(const std::unordered_map<OverlapType, std::vector<IOverlapConstPtr>> &overlaps) {
    for (auto const &obj : overlaps) {
      for (auto const &o : obj.second) {
        AddOverlap(obj.first, o);
      }
    }
  }

  /**
   * @brief 是否存在覆盖面对象
   * @return 是否存在覆盖面对象
   */
  bool IsExistOverlaps() const { return !overlaps_.empty(); }

  /**
   * @brief 获取指定类型覆盖面对象
   * @return 覆盖面对象
   */
  template <class T>
  std::vector<std::shared_ptr<const T>> GetOverlapByType(OverlapType type) const {
    std::vector<std::shared_ptr<const T>> objects;
    auto it = overlaps_.find(type);
    if (it == overlaps_.end()) {
      return objects;
    }
    objects.reserve(it->second.size());
    for (const auto &obj : it->second) {
      objects.push_back(std::static_pointer_cast<const T>(obj));
    }
    return objects;
  }

  /**
   * @brief 获取车道相关overlap
   * @return overlap集合
   */
  std::vector<std::shared_ptr<const LaneOverlap>> GetLaneOverlapsByType(OverlapType type) const {
    return GetOverlapByType<LaneOverlap>(type);
  }

  /**
   * 获取所有的Overlap
   * @return overlap列表
   */
  std::vector<IOverlapConstPtr> GetAllOverlaps() const {
    std::vector<IOverlapConstPtr> all;
    for (auto const &obj : overlaps_) {
      all.insert(all.end(), obj.second.begin(), obj.second.end());
    }
    return all;
  }

  /**
   * 获取包含的Overlap类型
   * @return 类型列表
   */
  std::vector<OverlapType> GetOverlapTypes() const {
    std::vector<OverlapType> types;
    types.reserve(overlaps_.size());
    for (const auto &it : overlaps_) {
      types.push_back(it.first);
    }
    return types;
  }
};

}  // namespace api
}  // namespace nerd
