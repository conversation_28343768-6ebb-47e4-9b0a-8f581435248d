/*
 * Copyright 2021 Tencent Copyright 2021 Tencent Inc.  All rights reserved.
 * @version: 1.0
 * @Author: wallstwang
 * @Date: 2021-03-17 16:06:01
 * @LastEditors: wallstwang
 * @LastEditTime: 2021-03-21 21:00:55
 */

#pragma once

#include <cmath>
#include <cstdint>
#include <iomanip>
#include <map>
#include <memory>
#include <sstream>
#include <string>
#include <utility>
#include <vector>

#include "nerd_export.h"

namespace nerd {
namespace api {

class ILane;
typedef std::shared_ptr<ILane> ILanePtr;
typedef std::shared_ptr<const ILane> ILaneConstPtr;
typedef std::weak_ptr<ILane> ILaneWeakPtr;
typedef std::weak_ptr<const ILane> ILaneConstWeakPtr;
class IBoundary;
typedef std::shared_ptr<IBoundary> IBoundaryPtr;
typedef std::shared_ptr<const IBoundary> IBoundaryConstPtr;
struct TrafficRoadStatus;

#define RADIANS_TO_DEGREES(rad) ((rad / M_PI) * 180)

///////////////////////////////////
/// tile
//////////////////////////////////
typedef uint32_t TileIDType;
typedef std::vector<TileIDType> TileIDTypeVec;
typedef uint32_t VersionIDType;

/**
 * 瓦片数据的实际版本和全局版本
 */
class VersionIDRange {
 public:
  /**
   * 瓦片实际的数据版本
   */
  VersionIDType data_ver_{0};
  /**
   * BuildingBlock全局版本号，即LCV(last confirmed version)
   */
  VersionIDType lcv_ver_{0};

  std::string toString() const {
    std::stringstream ss;
    ss << "[" << data_ver_ << "," << lcv_ver_ << "]";
    return ss.str();
  }
};

///////////////////////////////////
/// lane position
//////////////////////////////////
typedef uint8_t SeqNumType;
typedef int16_t SlopeType;
typedef float CurvatureType;

class EnuRadType;
class AngleDegreeType {
 public:
  /**
   * 单位：角度，范围：[0, 360)
   */
  double angle{0};

  AngleDegreeType() { angle = 0; }

  explicit AngleDegreeType(double degree) { angle = degree; }

  EnuRadType ToEnuRadType() const;

  double operator+(const AngleDegreeType &other) const { return angle + other.angle; }

  double operator-(const AngleDegreeType &other) const { return angle - other.angle; }

  bool operator<(const AngleDegreeType &other) const { return angle < other.angle; }

  bool operator>(const AngleDegreeType &other) const { return angle > other.angle; }
};

class EnuRadType {
 public:
  /**
   * 单位：弧度，范围：[-π, π]
   */
  double angle{0};

  EnuRadType() { angle = 0; }

  explicit EnuRadType(double rad) { angle = rad; }

  double operator+(const EnuRadType &other) const { return angle + other.angle; }

  double operator-(const EnuRadType &other) const { return angle - other.angle; }

  AngleDegreeType ToAngelDegreeType() const {
    if (angle >= M_PI / 2 && angle <= M_PI) {
      return AngleDegreeType(-(180 * angle / M_PI) + 450);
    } else if (angle >= -M_PI && angle < M_PI / 2) {
      return AngleDegreeType(-(180 * angle / M_PI) + 90);
    } else {
      return AngleDegreeType(0);
    }
  }
};

/**
 * 单位:KMPH
 */
typedef int32_t SpeedType;

/**
 * 速度等级：
 * 0 没有数据
 * 1 >130 km/h
 * 2 (100 km/h, 130 km/h]
 * 3 (90 km/h, 100 km/h]
 * 4 (70 km/h, 90 km/h]
 * 5 (50 km/h, 70 km/h]
 * 6 (30 km/h, 50 km/h]
 * 7 [11 km/h, 30 km/h]
 * 8 <11 km/h
 */
typedef uint8_t SpeedClassType;

///////////////////////////////////
/// surface object
//////////////////////////////////

/**
 * @brief 交通标志类型
 */
enum class TrafficSignType : uint16_t {
  /**
   * 未分类, 所有类型未分类都用该值表示
   */
  kOTHER_UNDEFINED = 0,

  ///////// 其它类型 ///////////

  /**
   * 天气
   */
  kOTHER_WEATHER = 100,
  /**
   * 车种
   */
  kOTHER_VEHICLE_TYPE = 200,
  /**
   * 距离
   */
  kOTHER_DISTANCE = 300,
  /**
   * 其它
   */
  kOTHER_OTHER = 900,

  ///////// 警告标志 ///////////

  /**
   * 建议限速
   */
  kWARN_SUGGEST_SPEED = 101,
  /**
   * 向左急弯路
   */
  kWARN_TURN_LEFT,
  /**
   * 向右急弯路
   */
  kWARN_TURN_RIGHT,
  /**
   * 反向弯路
   */
  kWARN_REVERSAL_TURN,
  /**
   * 连续弯路
   */
  kWARN_CONTINUOUS_TURN,
  /**
   * 上陡坡
   */
  kWARN_SLOPE_UP,
  /**
   * 下陡坡
   */
  kWARN_SLOPE_DOWN,
  /**
   * 连续下坡
   */
  kWARN_CONTINUOUS_SLOPE_DOWN,
  /**
   * 两侧变窄
   */
  kWARN_NARROW_IN_BOTH,
  /**
   * 右侧变窄
   */
  kWARN_NARROW_IN_RIGHT,
  /**
   * 左侧变窄
   */
  kWARN_NARROW_IN_LEFT,
  /**
   * 窄桥
   */
  kWARN_NARROW_BRIDGE,
  /**
   * 双向交通
   */
  kWARN_BIDIRECTION_TRAFFIC,
  /**
   * 注意行人
   */
  kWARN_PEDESTRIAN,
  /**
   * 注意儿童
   */
  kWARN_CHILDREN,
  /**
   * 注意落石（右侧）
   */
  kWARN_ROCK_FALL_IN_RIGHT,
  /**
   * 注意落石（左侧）
   */
  kWARN_ROCK_FALL_IN_LEFT,
  /**
   * 注意横风
   */
  kWARN_CROSSWIND,
  /**
   * 易滑
   */
  kWARN_SLIPPERY,
  /**
   * 傍山险路（右侧）
   */
  kWARN_MOUNTAIN_ROAD_IN_RIGHT,
  /**
   * 傍山险路（左侧）
   */
  kWARN_MOUNTAIN_ROAD_IN_LEFT,
  /**
   * 堤坝路（右侧）
   */
  kWARN_DAM_ROAD_IN_RIGHT,
  /**
   * 堤坝路（左侧）
   */
  kWARN_DAM_ROAD_IN_LEFT,
  /**
   * 村庄
   */
  kWARN_VILLAGE,
  /**
   * 隧道
   */
  kWARN_TUNNEL,
  /**
   * 渡口
   */
  kWARN_FERRY,
  /**
   * 驼峰桥
   */
  kWARN_HUMP_BACK_BRIDGE,
  /**
   * 路面不平
   */
  kWARN_UNEVEN_ROAD,
  /**
   * 路面高突
   */
  kWARN_HUMP_ROAD,
  /**
   * 路面低洼
   */
  kWARN_LOW_LYING_ROAD,
  /**
   * 过水路面
   */
  kWARN_WATER_ON_ROAD,
  /**
   * 有人看守铁路道口
   */
  kWARN_GAURDED_RAILWAY,
  /**
   * 无人看守铁路道口
   */
  kWARN_UNGAURDED_RAILWAY,
  /**
   * 注意非机动车
   */
  kWARN_CARE_FOR_NONMORTOR,
  /**
   * 事故易发路段
   */
  kWARN_ACCIDENT_PRONE_SECTION,
  /**
   * 慢行
   */
  kWARN_DRIVE_SLOWLY,
  /**
   * 左右绕行
   */
  kWARN_CIRCUITOUS_IN_LEFT_RIGHT,
  /**
   * 左侧绕行
   */
  kWARN_CIRCUITOUS_IN_LEFT,
  /**
   * 右侧绕行
   */
  kWARN_CIRCUITOUS_IN_RIGHT,
  /**
   * 注意危险
   */
  kWARN_CARE_FOR_DANGER,
  /**
   * 施工
   */
  kWARN_CONDUCTION,
  /**
   * 隧道开车灯
   */
  kWARN_TURN_ON_LIGHT_IN_TUNNEL,
  /**
   * 注意潮汐车道
   */
  kWARN_REVERSAL_LANE,
  /**
   * 注意保持车距
   */
  kWARN_KEEP_DISTANCE,
  /**
   * 注意分离式道路(十字平面交叉)
   */
  kWARN_DIVISED_ROAD_CROSS,
  /**
   * 注意分离式道路(丁字平面交叉)
   */
  kWARN_DIVISED_ROAD_T,
  /**
   * 左侧合流
   */
  kWARN_CONFLUENCE_IN_LEFT,
  /**
   * 右侧合流
   */
  kWARN_CONFLUENCE_IN_RIGHT,
  /**
   * 避险车道
   */
  kWARN_AVOID_DANGER_LANE,
  /**
   * 注意路面结冰
   */
  kWARN_ICE_ON_ROAD,
  /**
   * 注意前方车辆排队
   */
  kWARN_CARS_IN_QUEUE,
  kWARN_CRISSCROSS,                  /** 十字交叉 */
  kWARN_CONTINUOUS_CROSS,            /** 连续交叉 */
  kWARN_LEFT_BACKWARD_CROSS,         /** 左后交叉 */
  kWARN_RIGHT_BACKWARD_CROSS,        /** 右后交叉 */
  kWARN_LEFT_FORWARD_CROSS,          /** 左前交叉 */
  kWARN_RIGHT_FORWARD_CROSS,         /** 右前交叉 */
  kWARN_LONGITUDINAL_T_JUNCTION,     /** 纵向丁字 */
  kWARN_TRANSVERSE_LEFT_T_JUNCTION,  /** 横向左丁字 */
  kWARN_TRANSVERSE_RIGHT_T_JUNCTION, /** 横向右丁字 */
  kWARN_ROUNDABOUT,                  /** 环岛 */
  kWARN_Y_CROSS,                     /** Y形交叉 */
  kWARN_REVERSAL_TURN_LEFT,          /** 反向弯路(左) */
  kWARN_REVERSAL_TURN_RIGHT,         /** 反向弯路(右) */
  kWARN_LIVESTOCK,                   /** 注意牲畜 */
  kWARN_WILDLIFE,                    /** 注意野生动物 */
  kWARN_TRAFFIC_LIGHT,               /** 注意信号灯 */
  kWARN_DISABLED,                    /** 注意残疾人 */
  kWARN_RAIN_SNOW,                   /** 注意雨雪天 */
  kWARN_FOG,                         /** 注意雾天 */
  kWARN_BAD_WEATHER,                 /** 注意不利气象条件 */
  kWARN_PROPOSE_LIMIT_SPEED,         /** 建议限速 */
  kWARN_ROAD_RAILWAY_INTERSECTION,   /** 铁路与道路交叉 */
  kWARN_RAILWAY_CROSS_50,            /** 铁路道口距离警示(50) */
  kWARN_RAILWAY_CROSS_100,           /** 铁路道口距离警示(100) */
  kWARN_RAILWAY_CROSS_150,           /** 铁路道口距离警示(150) */
  kWARN_BLACK_SPOT,                  /** 注意意外黑点 */
  kWARN_MOVABLE_BRIDGE,              /** 移动式桥 */
  kWARN_LANDSLIDE,                   /** 滑坡 */
  kWARN_MUD_ROCK_FLOW,               /** 泥石流 */
  kWARN_SOIL_SINK,                   /** 地面塌陷 */
  kWARN_COLLAPSE,                    /** 塌方 */
                                     /**
                                      * 其它
                                      */
  kWARN_OTHER = 199,

  ///////// 禁令标志 ///////////

  /**
   * 限制速度
   */
  kBAN_SPEED_LIMIT = 201,
  /**
   * 解除限制速度
   */
  kBAN_SPEED_LIMIT_END,
  /**
   * 区域限制速度
   */
  kBAN_SPEED_LIMIT_ZONE,
  /**
   * 区域限制速度解除
   */
  kBAN_SPEED_LIMIT_ZONE_END,
  /**
   * 停车让行
   */
  kBAN_GIVE_WAY_STOP,
  /**
   * 减速让行
   */
  kBAN_GIVE_WAY_SLOW_DOWN,
  /**
   * 会车让行
   */
  kBAN_GIVE_WAY_WHEN_MEETING,
  /**
   * 禁止通行
   */
  kBAN_NOT_ACCESSIBLE,
  /**
   * 禁止驶入
   */
  kBAN_DRIVE_IN,
  /**
   * 禁止机动车驶入
   */
  kBAN_MOTOR_DRIVE_IN,
  /**
   * 禁止向左转向
   */
  kBAN_TURN_LEFT,
  /**
   * 禁止向右转向
   */
  kBAN_TURN_RIGHT,
  /**
   * 禁止直行
   */
  kBAN_GO_STRAIGHT,
  /**
   * 禁止向左向右转向
   */
  kBAN_TURN_LEFT_AND_RIGHT,
  /**
   * 禁止直行与向左转弯
   */
  kBAN_STRAIGHT_AND_LEFT,
  /**
   * 禁止直行与向右转弯
   */
  kBAN_STRAIGHT_AND_RIGHT,
  /**
   * 禁止掉头
   */
  kBAN_TURN_ROUND,
  /**
   * 禁止超车
   */
  kBAN_OVERTAKE,
  /**
   * 解除禁止超车
   */
  kBAN_OVERTAKE_END,
  /**
   * 禁止停车
   */
  kBAN_PARKING,
  /**
   * 禁止长时停车
   */
  kBAN_PARKING_FOR_LONG_TIME,
  /**
   * 限制宽度
   */
  kBAN_WIDTH_LIMIT,
  /**
   * 限制高度
   */
  kBAN_HEIGHT_LIMIT,
  /**
   * 限制质量
   */
  kBAN_WEIGHT_LIMIT,
  /**
   * 停车检查
   */
  kBAN_STOP_AND_CHECK,
  /**
   * 区域禁止停车
   */
  kBAN_NON_PARKING_AREA,
  /**
   * 区域禁止停车解除
   */
  kBAN_NON_PARKING_AREA_END,
  kBAN_TRUCK_IN,                    /** 禁止载货汽车驶入 */
  kBAN_ELECTRIC_TRICYCLE_IN,        /** 禁止电动三轮车驶入 */
  kBAN_LARGE_BUS_IN,                /** 禁止大型客车驶入 */
  kBAN_MINI_BUS_IN,                 /** 禁止小型客车驶入 */
  kBAN_TRAILER_SEMITRAILER_IN,      /** 禁止挂车半挂车驶入 */
  kBAN_TRACTOR_IN,                  /** 禁止拖拉机驶入 */
  kBAN_TRICYCLE_LOWSPEED_TRUCK_IN,  /** 禁止三轮骑车,低速货车驶入 */
  kBAN_MOTORCYCLE_IN,               /** 禁止摩托车驶入 */
  kBAN_SOME_TWO_VEHICLE_IN,         /** 禁止某两种车驶入 */
  kBAN_NON_MOTOR_VEHICLE_IN,        /** 禁止非机动车驶入 */
  kBAN_ANIMAL_POWERD_VEHICLE_IN,    /** 禁止畜力车驶入 */
  kBAN_HUMAN_PASSENGER_TRICYCLE_IN, /** 禁止人力客运三轮车驶入 */
  kBAN_HUMAN_TRUCK_TRICYCLE_IN,     /** 禁止人力货运三轮车驶入 */
  kBAN_HUMAN_VEHICLE_IN,            /** 禁止人力车驶入 */
  kBAN_WALKER_IN,                   /** 禁止行人进入 */
  kBAN_TRANS_DANGEROUS_VEHICLE_IN,  /** 禁止运输危险品车辆驶入 */
  kBAN_HONK,                        /** 禁止鸣喇叭 */
  kBAN_WEIGHT_ON_AXLE_LIMIT,        /** 限制轴重 */
  kBAN_RIGHT_U_TURN,                /** 禁止右掉头 */
  kBAN_LEFT_AND_U_TURN,             /** 禁止左转与掉头 */
                                    /**
                                     * 其它
                                     */
  kBAN_OTHER = 299,

  ///////// 指示标志 ///////////

  /**
   * 直行
   */
  kTIP_STRAIGHT = 301,
  /**
   * 向左转弯
   */
  kTIP_TURN_LEFT,
  /**
   * 向右转弯
   */
  kTIP_TURN_RIGHT,
  /**
   * 直行和向左转弯
   */
  kTIP_STRAIGHT_AND_LEFT,
  /**
   * 直行和向右转弯
   */
  kTIP_STRAIGHT_AND_RIGHT,
  /**
   * 向左和向右转弯
   */
  kTIP_LEFT_AND_RIGHT,
  /**
   * 靠右侧道路行驶
   */
  kTIP_ALONG_THE_RIGHT_SIDE,
  /**
   * 靠左侧道路行驶
   */
  kTIP_ALONG_THE_LEFT_SIDE,
  /**
   * 立体交叉直行和左转弯行驶
   */
  kTIP_STRAIGHT_AND_LEFT_3D,
  /**
   * 立体交叉直行和右转弯行驶
   */
  kTIP_STRAIGHT_AND_RIGHT_3D,
  /**
   * 环岛行驶
   */
  kTIP_ROUNDABOUT,
  /**
   * 向右单行路
   */
  kTIP_RIGHT_SINGLE_DIRECTION,
  /**
   * 向左单行路
   */
  kTIP_LEFT_SINGLE_DIRECTION,
  /**
   * 直行单行路
   */
  kTIP_STRAIGHT_SINGLE_DIRECTION,
  /**
   * 步行
   */
  kTIP_WALK_STREET,
  /**
   * 路口优先通行
   */
  kTIP_CROSS_FIRST,
  /**
   * 会车先行
   */
  kTIP_MEETING_FIRST,
  /**
   * 人行横道
   */
  kTIP_CROSSWALK,
  /**
   * 右转车道
   */
  kTIP_RIGHT_LANE,
  /**
   * 左转车道
   */
  kTIP_LEFT_LANE,
  /**
   * 直行车道
   */
  kTIP_STRAIGHT_LANE,
  /**
   * 直行和右转合用车道
   */
  kTIP_STRAIGHT_AND_RIGHT_LANE,
  /**
   * 直行合左转合用车道
   */
  kTIP_STRAIGHT_AND_LEFT_LANE,
  /**
   * 掉头车道
   */
  kTIP_TURN_ROUND_LANE,
  /**
   * 掉头和左转合用车道
   */
  kTIP_TURN_ROUND_AND_LEFT_LANE,
  /**
   * 公交线路专用车道
   */
  kTIP_BUS_LANE,
  /**
   * 快速公交系统专用车道
   */
  kTIP_PUBLIC_TRANSPORTATION_SYSTEM_LANE,
  /**
   * 机动车车道
   */
  kTIP_MOTOR_LANE,
  /**
   * 非机动车车道
   */
  kTIP_NONMOTOR_LANE,
  /**
   * 多成员车辆专用车道
   */
  kTIP_MULTI_PASSENGERG_VEHICLE_LANE,
  /**
   * 停车位
   */
  kTIP_PARKING_LOT,
  /**
   * 允许掉头
   */
  kTIP_ALLOW_TO_TURN_ROUND,
  /**
   * 分向行驶车道
   */
  kTIP_MULTI_DIRECTION_LANE,
  kTIP_MOTOR_VEHICLE_DRIVE,     /** 机动车行驶 */
  kTIP_MOTOR_VEHICLE_LANE,      /** 机动车车道 */
  kTIP_NON_MOTOR_VEHICLE_DRIVE, /** 非机动车行驶 */
  kTIP_NON_MOTOR_VEHICLE_LANE,  /** 非机动车车道 */
  kTIP_NOT_ACCESSIBLE,          /** 此路不通 */
  kTIP_LESS_LANES,              /** 车道数变少 */
  kTIP_MORE_LANES,              /** 车道数变多 */
  kTIP_START_POINT,             /** 起点 */
  kTIP_END_PRE_HINT,            /** 终点预告 */
  kTIP_END_NOTICE,              /** 终点提示 */
  kTIP_END_POINT,               /** 终点 */
  kTIP_TIDE_LANE,               /** 潮汐车道 */
  kTIP_U_TURN_PERMITTED,        /** 允许调头 */
  kTIP_HONK,                    /** 鸣喇叭 */
                                /**
                                 * 其它
                                 */
  kTIP_OTHER = 399,

  ///////// 指路标志 ///////////

  /**
   * 交叉路口预告
   */
  kLEAD_CROSS_PREHINT = 401,
  /**
   * 交叉路口告知
   */
  kLEAD_CROSS_HINT,
  /**
   * 此路不通
   */
  kLEAD_NOT_ACCESSIBLE,
  /**
   * 车道数量变少
   */
  kLEAD_LESS_LANE,
  /**
   * 车道数量增加
   */
  kLEAD_MORE_LANE,
  /**
   * 起点
   */
  kLEAD_START_POINT,
  /**
   * 终点预告
   */
  kLEAD_END_POINT_PREHINT,
  /**
   * 终点提示
   */
  kLEAD_END_POINT_HINT,
  /**
   * 终点
   */
  kLEAD_END_POINT,
  kLEAD_NATIONAL_EXPRESSWAY,     /** 国家级高速 */
  kLEAD_PROVINCIAL_EXPRESSWAY,   /** 省级高速 */
  kLEAD_NATIONAL_ROAD_NUMBER,    /** 国道编号 */
  kLEAD_PROVINCIAL_ROAD_NUMBER,  /** 省道编号 */
  kLEAD_COUNTY_ROAD_NUMBER,      /** 县道编号 */
  kLEAD_TOWNSHIP_ROAD_NUMBER,    /** 乡道编号 */
  kLEAD_SPECIAL_ROAD_NUMBER,     /** 专用道路编号 */
  kLEAD_CHINESE_ROAD_NAME,       /** 中文道路名 */
  kLEAD_EXIT_NUMBER,             /** 出口编号 */
  kLEAD_SERVICE_AND_FACILITY,    /** 服务区及设施 */
  kLEAD_TOLL_STATION,            /** 收费站 */
  kLEAD_SCENIC_SPOT,             /** 景区 */
  kLEAD_TRANSPORTATION_HUB,      /** 交通枢纽 */
  kLEAD_PLACE_NAME,              /** 地名 */
  kLEAD_EXIT,                    /** 出口 */
  kLEAD_ENTRANCE,                /** 入口 */
  kLEAD_MILEPOST,                /** 里程碑 */
  kLEAD_100M_PLATE,              /** 百米牌 */
  kLEAD_ETC_GANTRY_NUMBER_PLATE, /** ETC门架号牌 */
  kLEAD_GENERAL_ROAD_NAME_PLATE, /** 通用道路名牌 */
  /**
   * 其它
   */
  kLEAD_OTHER = 499,

  ///////// 旅游区标志 ///////////
  kTRAVEL_OTHER, /** 其他 */

  ///////// 作业区标志 ///////////
  kOPERATION_OTHER, /** 其他 */

  ///////// 复合型标志 ///////////
  kCOMPOSITE_OTHER, /** 其他 */

  ///////// 辅助标志 ///////////
  /**
   * 时间
   */
  kASSIST_TIME = 801,
  /**
   * 车种
   */
  kASSIST_VEHICLE_TYPE,
  /**
   * 方向
   */
  kASSIST_DIRECTION,
  /**
   * 区域或距离
   */
  kASSIST_ZONE_OR_DISTANCE,
  /**
   * 警告或禁令了理由
   */
  kASSIST_REASON,
  /**
   * 组合辅助标志
   */
  kASSIST_COMBINED_SIGN,
  kASSIST_WEATHER,           /** 天气 */
  kASSIST_DISTANCE_PRE_HINT, /** 预告距离 */
  kASSIST_WORKING_DISTANCE,  /** 作用距离 */
  kASSIST_AREA,              /** 区域 */
  kASSIST_WARN_BAN_REASON,   /** 警告或禁令理由 */
  /**
   * 其它
   */
  kASSIST_OTHER = 899,

  ///////// 可变信息标志 ///////////
  /**
   * 可变限速标志
   */
  kVARIABLE_SPEED_LIMIT = 901,
  /**
   * 其它可变信息标志
   */
  kVARIABLE_OTHER,

  ///////// 告示标志 ///////////
  kNOTICE_INTERVAL_VELOCITY_MEASUREMENT_PRE_HINT,     /** 区间测速预告 */
  kNOTICE_INTERVAL_VELOCITY_MEASUREMENT_START_POINT,  /** 区间测速起点 */
  kNOTICE_INTERVAL_VELOCITY_MEASUREMENT_END_POINT,    /** 区间测速终点 */
  kNOTICE_INTERVAL_VELOCITY_MEASUREMENT_MIDDLE_POINT, /** 区间测速中间点 */
  kNOTICE_INTERVAL_VELOCITY_MEASUREMENT_ROAD_SECTION, /** 区间测速路段 */
  kNOTICE_INTERVAL_VELOCITY_MEASUREMENT_HINT,         /** 区间测速告知 */
  kNOTICE_MOVABLE_INTERVAL_VELOCITY_MEASUREMENT_HINT, /** 流动测速告知 */
};

enum class SpecialTimeType : uint8_t {
  kNONE = 0,
  /**
   * 工作日
   */
  kWORKDAY,
  /**
   * 工作日除外
   */
  kNOT_WORKDAY,
  /**
   * 节假日除外
   */
  kNOT_HOLIDAY,
  /**
   * 节假日
   */
  kHOLIDAY,
  /**
   * 冰、冰雹
   */
  kICE,
  /**
   * 晴天
   */
  kSUNNY,
  /**
   * 风
   */
  kWIND,
  /**
   * 雾，雾霾
   */
  kFOG,
  /**
   * 雪
   */
  kSNOW,
  /**
   * 雨
   */
  kRAIN,
  /**
   * 施工期间除外
   */
  kNOT_WORKING,
  /**
   * 施工期间
   */
  kWORKING,
  /**
   * 放学期间除外
   */
  kNOT_SCHOOL_OVER,
  /**
   * 放学期间
   */
  kSCHOOL_OVER,
  /**
   * 晚高峰除外
   */
  kNOT_EVENING_PEAK,
  /**
   * 晚高峰
   */
  kEVENING_PEAK,
  /**
   * 早高峰除外
   */
  kNOT_MORNING_PEAK,
  /**
   * 早高峰
   */
  kMORNING_PEAK,
  /**
   * 旺季除外
   */
  kNOT_PEAK_SEASON,
  /**
   * 旺季
   */
  kPEAK_SEASON,
  /**
   * 淡季除外
   */
  kNOT_LOW_SEASON,
  /**
   * 淡季
   */
  kLOW_SEASON,
  /**
   * 暑假除外
   */
  kNOT_SUMMER_VACATION,
  /**
   * 暑假
   */
  kSUMMER_VACATION,
  /**
   * 寒假除外
   */
  kNOT_WINTER_VACATION,
  /**
   * 寒假
   */
  kWINTER_VACATION,
  /**
   * 干季除外
   */
  kNOT_DRY_SEASON,
  /**
   * 干季
   */
  kDRY_SEASON,
  /**
   * 雨季除外
   */
  kNOT_RAINY_SEASON,
  /**
   * 雨季
   */
  kRAINY_SEASON,
  /**
   * 冬季
   */
  kWINTER,
  /**
   * 秋季
   */
  kAUTUMN,
  /**
   * v
   */
  kSUMMER,
  /**
   * 春季
   */
  kSPRING
};

///////////////////////////////////
/// lane group
//////////////////////////////////
typedef int32_t LaneGroupIndexType;

/**
 * @brief LaneGroupID全局唯一
 */
typedef struct LaneGroupIDType {
  LaneGroupIDType() : LaneGroupIDType(0, 0) {}

  LaneGroupIDType(TileIDType _tile_id, uint32_t _lane_group_id) : tile_id(_tile_id), lane_group_id(_lane_group_id) {}
  /**
   * 瓦片id
   */
  TileIDType tile_id;
  /**
   * 瓦片内唯一
   */
  uint32_t lane_group_id;

  bool operator==(const LaneGroupIDType &other) const {
    return (tile_id == other.tile_id) && (lane_group_id == other.lane_group_id);
  }

  bool operator!=(const LaneGroupIDType &other) const {
    return !((tile_id == other.tile_id) && (lane_group_id == other.lane_group_id));
  }

  bool operator<(const LaneGroupIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && lane_group_id < other.lane_group_id);
  }

  bool operator>(const LaneGroupIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && lane_group_id > other.lane_group_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << lane_group_id;
    return ss.str();
  }
} LaneGroupIDType;

/**
 * @brief TPID(Tile Permanent ID) 基于tile的要素永久化
 */
typedef struct TPIDType {
  /**
   * 瓦片id
   */
  TileIDType tile_id = 0;
  /**
   * 瓦片内唯一
   */
  uint32_t tp_id = 0;

  TPIDType() {}
  TPIDType(TileIDType tileId, uint32_t tpId) : tile_id(tileId), tp_id(tpId) {}
  TPIDType(TPIDType const &other) {
    tile_id = other.tile_id;
    tp_id = other.tp_id;
  }

  bool operator<(const TPIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && tp_id < other.tp_id);
  }
  bool operator>(const TPIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && tp_id > other.tp_id);
  }
  bool operator==(const TPIDType &other) const {
    return (tile_id == other.tile_id) && (tp_id == other.tp_id);
  }
  bool operator!=(const TPIDType &other) const {
    return !((tile_id == other.tile_id) && (tp_id == other.tp_id));
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << tp_id;
    return ss.str();
  }
} TPIDType;

class TPIDTypeHash {
 public:
  size_t operator()(const TPIDType &id) const {
    return std::hash<uint64_t>{}((static_cast<uint64_t>(id.tile_id)) << 32u | id.tp_id);
  }
};

/**
 * @brief CrossAreaID全局唯一
 */
typedef struct CrossAreaIDType {
  CrossAreaIDType() : CrossAreaIDType(0, 0) {}

  CrossAreaIDType(TileIDType _tile_id, uint32_t _cross_area_id) : tile_id(_tile_id), cross_area_id(_cross_area_id) {}
  /**
   * 瓦片id
   */
  TileIDType tile_id;
  /**
   * 瓦片内唯一
   */
  uint32_t cross_area_id;

  bool operator==(const CrossAreaIDType &other) const {
    return (tile_id == other.tile_id) && (cross_area_id == other.cross_area_id);
  }

  bool operator!=(const CrossAreaIDType &other) const {
    return !((tile_id == other.tile_id) && (cross_area_id == other.cross_area_id));
  }

  bool operator<(const CrossAreaIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && cross_area_id < other.cross_area_id);
  }

  bool operator>(const CrossAreaIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && cross_area_id > other.cross_area_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << cross_area_id;
    return ss.str();
  }
} CrossAreaIDType;

class CrossAreaIDTypeHash {
 public:
  size_t operator()(const CrossAreaIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.cross_area_id);
  }
};
/**
 * @brief lane group id的Hash计算值
 */
class LaneGroupIDTypeHash {
 public:
  size_t operator()(const LaneGroupIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.lane_group_id);
  }
};

typedef struct LaneGroupIDPair {
 public:
  LaneGroupIDType first;
  LaneGroupIDType second;
} LaneGroupIDPair;

struct LaneGroupIDPairComp {
  bool operator()(const LaneGroupIDPair &first, const LaneGroupIDPair &second) const {
    return (first.first < second.first) || (first.first == second.first && first.second < second.second);
  }
};
///////////////////////////////////
/// lane
//////////////////////////////////
typedef int32_t LaneIndexType;
typedef uint32_t LaneLengthType;
typedef uint16_t LaneWidthType;

/**
 * @brief Lane ID
 */
typedef struct LaneIDType {
  LaneIDType(TileIDType tile, uint32_t lane) : tile_id(tile), lane_id(lane) {}

  LaneIDType() = default;

  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内唯一
   */
  uint32_t lane_id{0};

  bool operator==(const LaneIDType &other) const { return (tile_id == other.tile_id) && (lane_id == other.lane_id); }

  bool operator!=(const LaneIDType &other) const { return !((tile_id == other.tile_id) && (lane_id == other.lane_id)); }

  bool operator<(const LaneIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && lane_id < other.lane_id);
  }

  bool operator>(const LaneIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && lane_id > other.lane_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << lane_id;
    return ss.str();
  }
} LaneIDType;

/**
 * @brief lane id的Hash值
 */
class LaneIDTypeHash {
 public:
  size_t operator()(const LaneIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.lane_id);
  }
};

class LaneIDTypeComparor {
 public:
  bool operator()(const LaneIDType &id1, const LaneIDType &id2) const {
    if (id1.tile_id != id2.tile_id) {
      return id1.tile_id < id2.tile_id;
    }
    return id1.lane_id < id2.lane_id;
  }
};

typedef struct LaneIDPair {
 public:
  LaneIDType first;
  LaneIDType second;

  bool operator==(const LaneIDPair &other) const { return (first == other.first) && (second == other.second); }

  bool operator<(const LaneIDPair &other) const {
    return (first < other.first) || (first == other.first && second < other.second);
  }

  bool operator>(const LaneIDPair &other) const {
    return (first > other.first) || (first == other.first && second > other.second);
  }
} LaneIDPair;

struct LaneIDPairComp {
  bool operator()(const LaneIDPair &first, const LaneIDPair &second) const {
    return (first.first < second.first) || (first.first == second.first && first.second < second.second);
  }
};

class LaneIDPairHash {
 public:
  size_t operator()(const LaneIDPair &id) const {
    return std::hash<size_t>()(LaneIDTypeHash()(id.first)) ^ std::hash<size_t>()(LaneIDTypeHash()(id.second));
  }
};

///////////////////////////////////
/// boundary
//////////////////////////////////
typedef int32_t BoundaryIndexType;
typedef std::vector<BoundaryIndexType> BoundaryIndexTypeVec;
typedef int32_t CoordIndexType;

/**
 * @brief Boundary ID
 */
typedef struct BoundaryIDType {
  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内唯一
   */
  uint32_t boundary_id{0};

  bool operator==(const BoundaryIDType &other) const {
    return (tile_id == other.tile_id) && (boundary_id == other.boundary_id);
  }

  bool operator!=(const BoundaryIDType &other) const { return !operator==(other); }

  bool operator<(const BoundaryIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && boundary_id < other.boundary_id);
  }

  bool operator>(const BoundaryIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && boundary_id > other.boundary_id);
  }

  std::string ToString() const { return "boundary_id:" + std::to_string(tile_id) + ":" + std::to_string(boundary_id); }
} BoundaryIDType;

/**
 * @brief BoundarySet ID
 */
typedef struct BoundarySetIDType {
  BoundaryIDType boundary_id;
  uint32_t idx{0};
  bool cross_tile{false};
} BoundarySetIDType;

class BoundaryIDTypeHash {
 public:
  size_t operator()(const BoundaryIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.boundary_id);
  }
};

typedef struct BoundaryIDPair {
 public:
  BoundaryIDType first;
  BoundaryIDType second;
} BoundaryIDPair;

struct BoundaryIDPairComp {
  bool operator()(const BoundaryIDPair &first, const BoundaryIDPair &second) const {
    return (first.first < second.first) || (first.first == second.first && first.second < second.second);
  }
};

typedef uint32_t BoundaryLengthType;
typedef uint16_t BoundaryWidthType;

/**
 * @brief Boundary数据来源
 */
enum class BoundaryDataSourceType : uint8_t {
  /**
   * 无
   */
  kNONE = 0,
  /**
   * 高精自采
   */
  kHD_SELF_COLLECTION = 1,
  /**
   * 高精众包
   */
  kHD_CROWD_SOURCING = 2,
  /**
   * 低精众包
   */
  kSD_CROWD_SOURCING = 3,
  /**
   * 情报
   */
  kINTELLIGENCE = 4,
  /**
   * 影像
   */
  kIMAGE = 5,
  /**
   * 高分影像
   */
  kHIGH_RESOLUTION_IMAGE = 6,
  /**
   * 4k生成
   */
  k4K = 7,
};

///////////////////////////////////
/// access_restrict
//////////////////////////////////
union TimeLimitMaskType {
  struct {
    uint8_t holiday : 1;
    uint8_t holiday_except : 1;

    uint8_t season_spring : 1;
    uint8_t season_summer : 1;
    uint8_t season_autumn : 1;
    uint8_t season_winter : 1;
    uint8_t season_rain : 1;
    uint8_t season_except_rain : 1;
    uint8_t season_dry : 1;
    uint8_t season_except_dry : 1;
    uint8_t season_cold : 1;
    uint8_t season_except_cold : 1;
    uint8_t season_hot : 1;
    uint8_t season_except_hot : 1;

    uint8_t peak_off_season : 1;
    uint8_t peak_except_off_season : 1;
    uint8_t peak_peak_season : 1;
    uint8_t peak_except_peak_season : 1;

    uint8_t commute_morning_peak : 1;
    uint8_t commute_except_morning_peak : 1;
    uint8_t commute_evening_peak : 1;
    uint8_t commute_except_evening_peak : 1;

    uint8_t school_leaving_school : 1;
    uint8_t school_except_leaving_school : 1;

    uint8_t construction_during : 1;
    uint8_t construction_except : 1;

    uint8_t weather_rain : 1;
    uint8_t weather_snow : 1;
    uint8_t weather_fog : 1;
    uint8_t weather_hail : 1;
    uint8_t weather_wind : 1;

    uint8_t reserved : 1;
  } mask;
  uint32_t value;

  explicit TimeLimitMaskType(uint32_t value) { this->value = value; }
};
typedef float ConditionSpeedType;

/**
 * @brief 交规ID
 */
typedef struct AccessRestrictIDType {
  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内唯一
   */
  uint32_t access_restrict_id{0};

  bool operator==(const AccessRestrictIDType &other) const {
    return (tile_id == other.tile_id) && (access_restrict_id == other.access_restrict_id);
  }

  bool operator<(const AccessRestrictIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && access_restrict_id < other.access_restrict_id);
  }

  bool operator>(const AccessRestrictIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && access_restrict_id > other.access_restrict_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << access_restrict_id;
    return ss.str();
  }
} AccessRestrictIDType;

class AccessRestrictIDTypeHash {
 public:
  size_t operator()(const AccessRestrictIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.access_restrict_id);
  }
};

/**
 * @brief 机动车类型
 */
enum class VehicleType : uint8_t {
  NONE = 0,              // 未知
  CAR = 1,               // 小客车
  LOCAL_TRUCK = 2,       // 本地货车
  NONLOCAL_TRUCK = 3,    // 外地货车
  AMBULANCE = 4,         // 急救车
  TAXI = 5,              // 出租车
  BUS = 6,               // 公交车
  WALKER = 7,            // 步行者
  BICYCLE = 8,           // 自行车
  ELECTRIC_BICYCLE = 9,  // 电动自行车
  MOTORCYCLE = 10,       // 摩托车
  MULTI_PASSENGER = 11,  // 多人乘坐车辆
  WITH_TRAILER = 12,     // 拖挂车
  DANGEROUS = 13,        // 危险品车辆
  TRANSIT = 14,          // 过境车辆
  MINIVAN = 15,          // 小型货车（电动三轮车，拖拉机，三轮汽车）
  ALL = 16,              // 全部车辆
  LARGE_BUS = 17,        // 大型客车
  LARGE_TRUCK = 18,      // 大型货车
  OTHER = 19,            // 其它机动车辆
  ELECTRIC_CAR = 20,     // 电动小汽车
  LIGHT_TRUCK = 21,      // 轻型货车
  MINI_TRUCK = 22,       // 微型货车
  MEDIUM_TRUCK = 23,     // 中型货车
  HEAVY_TRUCK = 24,      // 重型货车
  EXTERNAL = 25,         // 外部车辆
};

///////////////////////////////////
/// link
//////////////////////////////////
typedef struct LinkIDType {
  LinkIDType(TileIDType tile, uint32_t link) : tile_id(tile), link_id(link) {}

  LinkIDType() = default;

  explicit LinkIDType(uint64_t id) {
    tile_id = id >> 32;
    link_id = static_cast<uint32_t>(id);
  }

  uint64_t GetHashId() const {
    uint64_t id = tile_id;
    return id << 32 | link_id;
  }

  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内link的索引
   */
  uint32_t link_id{0};

  bool operator==(const LinkIDType &other) const { return (tile_id == other.tile_id) && (link_id == other.link_id); }

  bool operator!=(const LinkIDType &other) const { return !((tile_id == other.tile_id) && (link_id == other.link_id)); }

  bool operator<(const LinkIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && link_id < other.link_id);
  }

  bool operator>(const LinkIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && link_id > other.link_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << link_id;
    return ss.str();
  }
} LinkIDType;

class LinkIDTypeHash {
 public:
  size_t operator()(const LinkIDType &id) const {
    return std::hash<uint64_t>{}((static_cast<uint64_t>(id.tile_id)) << 32u | id.link_id);
  }
};

typedef int32_t LinkLengthType;
typedef uint8_t LinkDirectionType;
typedef int32_t LinkIndexType;
typedef uint32_t AdminCode;
/**
 * @brief Link ID pair
 */
typedef struct LinkIDPair {
 public:
  LinkIDType first;
  LinkIDType second;
} LinkIDPair;

struct LinkIDPairComp {
  bool operator()(const LinkIDPair &first, const LinkIDPair &second) const {
    return (first.first < second.first) || (first.first == second.first && first.second < second.second);
  }
};

/**
 * @brief link方向
 */
enum class LinkDirection : uint8_t {
  kUNKNOWN = 0,
  /**
   * 顺向，逆向
   */
  kBOTH_DIRECTION = 1,
  /**
   * 顺向
   */
  kSTART_TO_END,
  /**
   * 逆向
   */
  kEND_TO_START
};

///////////////////////////////////
/// node
//////////////////////////////////
/**
 * @brief NodeID tile内唯一
 */
typedef struct NodeIDType {
  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内唯一
   */
  uint32_t node_id{0};

  NodeIDType() = default;
  ~NodeIDType() = default;
  bool operator==(const NodeIDType &other) const { return (tile_id == other.tile_id) && (node_id == other.node_id); }

  bool operator<(const NodeIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && node_id < other.node_id);
  }

  bool operator>(const NodeIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && node_id > other.node_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << node_id;
    return ss.str();
  }
} NodeIDType;

class NodeIDTypeHash {
 public:
  size_t operator()(const NodeIDType &id) const { return 14514 * id.tile_id + 19 * id.node_id; }
};

/**
 * @brief 地图数据类型
 */
enum class MapDataBuildingBlockID : uint8_t {
  /**
   * 无
   */
  kNONE = 0,
  /**
   * 道路数据层
   */
  kROUTE = 1,
  /**
   * 高精数据层
   */
  kLANE = 2,
  /**
   * 基础渲染数据层
   */
  kBMD = 3,
  /**
   * 共享数据，只诱导服务端使用
   * */
  kSHARED = 4,
  /**
   * OBJECT3D数据层
   * */
  kOBJECTS_3D = 6,
  /**
   * POI全局索引数据层
   */
  kPOIGLOBAL = 7,
  /**
   * POI索引数据层
   */
  kFTS = 8,
  /**
   * BMD 4K+高精
   */
  kBMD_LANE = 12,
  /**
   * 编译错误信息
   * 备注:
   * 1.废弃类型, 外部不要再使用;
   * 2. 一张图合入主线后一个月移除
   */
  kDebugLayer = 13,
  /**
   * 渲染室内图
   */
  kBMD_INDOOR = 14,
  /**
   * 纯4k图层
   * 备注:
   * 1. 一张图下不再需要该类型, 4K和Lane数据统一在kLANE中, 请不要再使用;
   * 2. 一张图合入主线后一个月移除所有4K代码;
   */
  kLANE_4K = 15,
};
std::ostream &operator<<(std::ostream &out, const MapDataBuildingBlockID &buildingBlockId);

/**
 * @brief 地图语言类型
 */
enum class MapDataLanguageType : uint8_t {
  /**
   * 默认简体中文
   */
  kLANGUAGE_Default = 0,
  /**
   * 简体中文
   */
  kLANGUAGE_CN = 1,
  /**
   * 繁体中文
   */
  kLANGUAGE_TC = 2,
  /**
   * 英文
   */
  kLANGUAGE_EN = 3,
  /**
   * 葡萄牙文
   */
  kLANGUAGE_PT = 4
};
std::ostream &operator<<(std::ostream &out, const MapDataLanguageType &languageType);

/**
 * @brief 地图缩放层级
 */
enum class MapLevel : int8_t {
  kLEVEL_NONE = -1,  ///< 无
  kLEVEL_0 = 0,
  kLEVEL_1 = 1,
  kLEVEL_2 = 2,
  kLEVEL_3 = 3,
  kLEVEL_4 = 4,
  kLEVEL_5 = 5,
  kLEVEL_6 = 6,
  kLEVEL_7 = 7,
  kLEVEL_8 = 8,
  kLEVEL_9 = 9,
  kLEVEL_10 = 10,
  kLEVEL_11 = 11,
  kLEVEL_12 = 12,
  kLEVEL_13 = 13,
  kLEVEL_14 = 14,
  kLEVEL_15 = 15,
};

/**
 * @brief 标精和高精映射关系模式
 * */
enum class SDLinkToHDMode {
  kALL = 0,      /// 双向映射关系
  kFORWARD = 1,  /// 顺向映射关系
  kBACKWARD = 2  /// 逆向映射关系
};

/**
 * @brief geometry id
 */
typedef struct GeometryIDType {
  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内唯一
   */
  uint32_t geo_id{0};

  bool operator==(const GeometryIDType &other) const { return (tile_id == other.tile_id) && (geo_id == other.geo_id); }

  bool operator<(const GeometryIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && geo_id < other.geo_id);
  }

  bool operator>(const GeometryIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && geo_id > other.geo_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << geo_id;
    return ss.str();
  }
} GeometryIDType;

class GeometryIDTypeHash {
 public:
  size_t operator()(const GeometryIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.geo_id);
  }
};

/**
 * @brief GeoCoordinate LLA球面坐标系,如：WGS84, GCJ02都属于LLA坐标系
 */
typedef struct GeoCoordinate {
 public:
  GeoCoordinate() = default;
  GeoCoordinate(double x, double y) : x(x), y(y) {}
  GeoCoordinate(double x, double y, double z) : x(x), y(y), relative_z(z) {}
  std::string ToString() const {
    std::stringstream ss;
    ss << std::setprecision(10);
    ss << "[" << x << "," << y << "," << relative_z << "]";
    return ss.str();
  }

  bool areAlmostEqual(double a, double b, double epsilon = 1e-15) const {
    return std::fabs(a - b) < epsilon;
  }

  bool IsInvalid() const { return (x >= 180.0 || x < -180.0 || y >= 90.0 || y < -90.0); }

  bool IsValid() const { return !IsInvalid(); }

  bool operator==(const GeoCoordinate &other) const {
    return areAlmostEqual(x,other.x) && areAlmostEqual(y,other.y) && areAlmostEqual(relative_z,other.relative_z);
  }

  bool operator!=(const GeoCoordinate &other) const {
    return !areAlmostEqual(x,other.x) || !areAlmostEqual(y,other.y) || !areAlmostEqual(relative_z,other.relative_z);
  }

 public:
  double x{0};          /**< 经度数据坐标 */
  double y{0};          /**< 维度数据坐标 */
  double relative_z{0}; /**< 相对高程，单位: 米 */
} GeoCoordinate;

// /**
//  * @brief GeoCoordinate LLA球面坐标系
//  */
// typedef struct EnuCoordinate {
//  public:
//   EnuCoordinate() = default;
//   EnuCoordinate(double x, double y) : east(x), north(y) {}
//   std::string ToString() const {
//     std::stringstream ss;
//     ss << std::setprecision(9);
//     ss << "x=" << east;
//     ss << ",y=" << north;
//     ss << ",relativeZ=" << relative_z;
//     return ss.str();
//   }
//
//  public:
//   double east{0};       /**< 经度数据坐标 */
//   double north{0};      /**< 维度数据坐标 */
//   double relative_z{0}; /**< 相对高程，单位:米 */
// } ENUCoordinate;

/**
 * 数据坐标，使用NDS规范存储的球面坐标系
 * @brief Coordinate
 */
struct NERD_EXPORT Coordinate {
 public:
  Coordinate() = default;
  Coordinate(int32_t x, int32_t y) : x(x), y(y) {}
  Coordinate(int32_t x, int32_t y, int32_t relative_z) : x(x), y(y), relative_z(relative_z) {}
  std::string ToString() const {
    std::stringstream ss;
    ss << std::setprecision(9);
    ss << "lng=" << x;
    ss << ",lat=" << y;
    ss << ",relativeZ=" << relative_z;
    return ss.str();
  }
  // /**
  //  * 转换到ENU坐标系
  //  * @param center_point ENU中心点位置
  //  * @return ENU坐标
  //  */
  // ENUCoordinate ToENU(const nerd::api::GeoCoordinate &center_point) const;

  /**
   * 转换到地理球面坐标系
   * @return LLA坐标系
   */
  GeoCoordinate ToGeo() const;
  Coordinate operator-(const Coordinate &p) const { return {x - p.x, y - p.y}; }
  Coordinate operator+(const Coordinate &p) const { return {x + p.x, y + p.y}; }
  Coordinate operator*(double scalar) const { return {int32_t(x * scalar), int32_t(y * scalar)}; }

  bool operator==(const Coordinate &other) const { return x == other.x && y == other.y; }

  bool operator!=(const Coordinate &other) const { return x != other.x || y != other.y; }

 public:
  int32_t x{0};          /**< 经度数据坐标 */
  int32_t y{0};          /**< 维度数据坐标 */
  int32_t relative_z{0}; /**< 相对高程，单位:厘米 */
};

/**
 * @brief MercatorCoordinate 魔卡托平面坐标
 */
typedef struct MercatorCoordinate {
 public:
  MercatorCoordinate() = default;
  MercatorCoordinate(int32_t x, int32_t y) : x(x), y(y) {}
  std::string ToString() const {
    std::stringstream ss;
    ss << "x=" << x;
    ss << ",y=" << y;
    return ss.str();
  }

  bool areAlmostEqual(int intVal, double floatVal, double epsilon = 1e-15) const {
    return std::fabs(static_cast<double>(intVal) - floatVal) < epsilon;
  }

  bool operator==(const GeoCoordinate &other) const { return areAlmostEqual(x, other.x) && areAlmostEqual(y, other.y);}

  bool operator!=(const GeoCoordinate &other) const { return !areAlmostEqual(x, other.x) || !areAlmostEqual(y, other.y); }

 public:
  int32_t x{0}; /**< 经度数据坐标，精度厘米 */
  int32_t y{0}; /**< 维度数据坐标，精度厘米 */
} MercatorCoordinate;

/**
 * 三维向量
 */
struct NERD_EXPORT Vector3D {
  double x{0.0};
  double y{0.0};
  double z{0.0};

  std::string ToString() const {
    std::stringstream ss;
    ss << "{" << x << "," << y << "," << z << "}";
    return ss.str();
  }
};

/**
 * 线内点偏移索引
 */
struct InLinePosition {
  /**
   * 垂足在线上前一个点下标
   */
  uint32_t coordinate_start{0};
  /**
   * 垂足到线上前一个点距离，单位：米
   */
  double offset_length{0.0};
  /**
   * 垂足到线上第一个点距离，单位：米
   */
  double start_length{0.0};
  /**
   * 原始点到垂足的距离，参考线左侧为正，右侧为负，单位：米
   */
  double distance_to_line{0.0};
  /**
   * 垂足坐标点
   */
  Coordinate coordinate;
};

/**
 * @brief 矩形范围
 */
typedef struct Rect {
  /**
   * 矩形框边界点，一般为左下角
   * 当然也可以赋值为屏幕坐标的左上角
   */
  GeoCoordinate p0{0, 0, 0};
  /**
   * 矩形框边界点，一般为右上角
   * 当然也可以赋值为屏幕坐标的右下角
   */
  GeoCoordinate p1{0, 0, 0};

  std::string ToString() const {
    std::stringstream ss;
    ss << "p0:" << p0.ToString();
    ss << ",p1:" << p1.ToString();
    return ss.str();
  }
} Rect;

/**
 * 打断点信息
 * 需求链接: https://iwiki.woa.com/pages/viewpage.action?pageId=696174144
 */
struct BreakPointInfo {
  /**
   * 打断位置下标
   */
  uint16_t coordinate_index{0};
  /**
   * 距离比例, ratio = d1 / d2
   *
   *    |              d2            |
   *    |        d1      |           |
   * ---+----------------x-----------+----------
   *  coordinate_index      coordinate_index+1
   *
   */
  float ratio{0};

  /**
   * 该break point的相对高程值
   * 单位米/厘米; 依赖调用的接口;
   * */
  float relative_height{0};
};

/**
 * @brief 纹理坐标
 */
struct TextureCoordinate {
  float u{0};
  float v{0};
};

/**
 * @brief 法向量
 */
struct NormalVector {
  float x{0};
  float y{0};
  float z{0};
};

struct SurfaceIDType {
  uint32_t tile_id{0};
  uint32_t obj_id{0};

  std::string ToString() const { return std::to_string(tile_id) + ":" + std::to_string(obj_id); }

  bool operator==(const SurfaceIDType &id) const { return id.tile_id == tile_id && id.obj_id == obj_id; }

  bool operator!=(const SurfaceIDType &other) const {
    return !((tile_id == other.tile_id) && (obj_id == other.obj_id));
  }

  bool operator<(const SurfaceIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && obj_id < other.obj_id);
  }

  bool operator>(const SurfaceIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && obj_id > other.obj_id);
  }
};

class SurfaceIDTypeHash {
 public:
  size_t operator()(const SurfaceIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.obj_id);
  }
};

struct OverlapIDType {
  uint32_t tile_id{0};
  uint32_t overlap_id{0};

  std::string ToString() const { return std::to_string(tile_id) + ":" + std::to_string(overlap_id); }

  bool operator==(const OverlapIDType &id) const { return id.tile_id == tile_id && id.overlap_id == overlap_id; }

  bool operator!=(const OverlapIDType &other) const {
    return !((tile_id == other.tile_id) && (overlap_id == other.overlap_id));
  }

  bool operator<(const OverlapIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && overlap_id < other.overlap_id);
  }

  bool operator>(const OverlapIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && overlap_id > other.overlap_id);
  }
};

class OverlapIDTypeHash {
 public:
  size_t operator()(const OverlapIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.overlap_id);
  }
};

class OverlapIDTypeComparor {
 public:
  bool operator()(const OverlapIDType &id1, const OverlapIDType &id2) const {
    if (id1.tile_id != id2.tile_id) {
      return id1.tile_id < id2.tile_id;
    }
    return id1.overlap_id < id2.overlap_id;
  }
};

/**
 * @brief 坡度曲率ID
 */
typedef struct LanePositionIDType {
  /**
   * @brief 瓦片id
   */
  TileIDType tile_id;
  /**
   * @brief 坡度曲率id
   */
  uint32_t lane_pos_id;

  bool operator==(const LanePositionIDType &other) const {
    return (tile_id == other.tile_id) && (lane_pos_id == other.lane_pos_id);
  }

  bool operator<(const LanePositionIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && lane_pos_id < other.lane_pos_id);
  }

  bool operator>(const LanePositionIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && lane_pos_id > other.lane_pos_id);
  }

  std::string ToString() const { return std::to_string(tile_id) + ":" + std::to_string(lane_pos_id); }
} LanePositionIDType;

typedef struct TrafficLightID {
  /**
   * @brief 瓦片id
   */
  TileIDType tile_id;
  /**
   * @brief 红绿灯id
   */
  uint32_t light_id;

  bool operator==(const TrafficLightID &other) const {
    return (tile_id == other.tile_id) && (light_id == other.light_id);
  }

  bool operator<(const TrafficLightID &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && light_id < other.light_id);
  }

  bool operator>(const TrafficLightID &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && light_id > other.light_id);
  }

  std::string ToString() const { return std::to_string(tile_id) + ":" + std::to_string(light_id); }
} TrafficLightID;

/**
 * 通用要素ID定义
 */
struct FeatureIDType {
  FeatureIDType() : tile_id{0}, id{0} {}
  FeatureIDType(TileIDType _tile_id, uint32_t _id) : tile_id(_tile_id), id(_id) {}

  /**
   * 瓦片id
   */
  TileIDType tile_id;
  /**
   * 瓦片内唯一
   */
  uint32_t id;

  bool operator==(const FeatureIDType &other) const {
    return (tile_id == other.tile_id) && (id == other.id);
  }

  bool operator<(const FeatureIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && id < other.id);
  }

  bool operator>(const FeatureIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && id > other.id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << id;
    return ss.str();
  }
};
class FeatureIDTypeHash {
 public:
  size_t operator()(const FeatureIDType &id) const {
    return std::hash<uint64_t>{}((static_cast<uint64_t>(id.tile_id)) << 32u | id.id);
  }
};

using AreaFeatureIDType = FeatureIDType;


/**
 * @brief Poi ID
 */
typedef struct PoiIDType {
  PoiIDType(TileIDType tile, uint32_t lane) : tile_id(tile), lane_id(lane) {}

  PoiIDType() = default;

  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内唯一
   */
  uint32_t lane_id{0};

  bool operator==(const PoiIDType &other) const { return (tile_id == other.tile_id) && (lane_id == other.lane_id); }

  bool operator!=(const PoiIDType &other) const { return !((tile_id == other.tile_id) && (lane_id == other.lane_id)); }

  bool operator<(const PoiIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && lane_id < other.lane_id);
  }

  bool operator>(const PoiIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && lane_id > other.lane_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << lane_id;
    return ss.str();
  }
} PoiIDType;

/**
 * @brief Object3DFeature ID
 */
typedef struct Object3DFeatureIDType {
  Object3DFeatureIDType(TileIDType tile, uint32_t id) : tile_id(tile), feature_id(id) {}

  Object3DFeatureIDType() = default;

  /**
   * 瓦片id
   */
  TileIDType tile_id{0};
  /**
   * 瓦片内唯一
   */
  uint32_t feature_id{0};

  bool operator==(const Object3DFeatureIDType &other) const {
    return (tile_id == other.tile_id) && (feature_id == other.feature_id);
  }

  bool operator!=(const Object3DFeatureIDType &other) const {
    return !((tile_id == other.tile_id) && (feature_id == other.feature_id));
  }

  bool operator<(const Object3DFeatureIDType &other) const {
    return (tile_id < other.tile_id) || (tile_id == other.tile_id && feature_id < other.feature_id);
  }

  bool operator>(const Object3DFeatureIDType &other) const {
    return (tile_id > other.tile_id) || (tile_id == other.tile_id && feature_id > other.feature_id);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << tile_id << ":" << feature_id;
    return ss.str();
  }
} Object3DFeatureIDType;

class Object3DFeatureIDTypeHash {
 public:
  size_t operator()(const Object3DFeatureIDType &id) const {
    return std::hash<size_t>()(id.tile_id) ^ std::hash<size_t>()(id.feature_id);
  }
};

/**
 * @brief 模型tag信息
 * */
typedef struct TagType {
  TagType(std::string k, std::string v) : key(std::move(k)), value(std::move(v)) {}
  TagType() = default;

  std::string key;
  std::string value;
} TagType;

/**
 * @brief 模型ID内容
 */
typedef struct PackedIDType {
  PackedIDType() = default;

  /**
   * 模型ID
   */
  std::string id;
  /**
   * 模型版本
   */
  uint32_t version{0};
  /**
   * 模型的TAG信息
   * */
  std::vector<TagType> tags;

  bool operator==(const PackedIDType &other) const { return (id == other.id) && (version == other.version); }

  bool operator!=(const PackedIDType &other) const { return !((id == other.id) && (version == other.version)); }

  bool operator<(const PackedIDType &other) const {
    return (id < other.id) || (id == other.id && version < other.version);
  }

  bool operator>(const PackedIDType &other) const {
    return (id > other.id) || (id == other.id && version > other.version);
  }

  std::string ToString() const {
    std::stringstream ss;
    ss << id << ":" << version;
    return ss.str();
  }
} PackedIDType;

NERD_EXPORT std::ostream &operator<<(std::ostream &os, const SurfaceIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const LaneGroupIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const LaneIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const BoundaryIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const GeometryIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const LanePositionIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const CrossAreaIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const AccessRestrictIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const OverlapIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const TrafficLightID &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const PoiIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &os, const PackedIDType &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &out, const Coordinate &info);
NERD_EXPORT std::ostream &operator<<(std::ostream &out, const std::vector<Coordinate> &info);

typedef int32_t JobId;
typedef uint32_t ApiId;

/**
 * @brief 图层数据内容状态
 * */
enum class LayerRelationStatus : uint8_t {
  /**
   * @brief 无效值
   * */
  kNone = 0,
  /**
   * @brief 不完整状态
   * */
  kUNCompleted = 1,
  /**
   * @brief 完整状态
   * */
  kCompleted = 2
};

/**
 * @brief 错误消息
 */
class RetMessage {
 public:
  RetMessage(int _ret, std::string _msg) {
    this->ret = _ret;
    this->msg = std::move(_msg);
  }
  int ret;
  std::string msg;
};
std::ostream &operator<<(std::ostream &out, const RetMessage &retMsg);

/**
 * 数据状态，kOfflineOK和kOnlineOK属于正常的，kNotExist明确表达该瓦片在现实中不存在，也是正常的
 * 离线瓦片的数据异常状态会透传给上层，在线瓦片的数据异常状态直接返回nullptr
 */
enum class DataStatus : uint8_t {
  /**
   * @brief 本地有离线数据
   */
  kOfflineOk = 0,
  /**
   * @brief 本地有在线数据
   */
  kOnlineOk = 1,
  /**
   * @brief 数据不存在，在线模式下服务端下发数据为空，也认为是正常的
   */
  kNotExist = 2,
  /**
   * @brief 数据内容无变化，即查询版本的瓦片数据和本地最新瓦片数据内容一致，这种情况下不会返回实际数据内容
   */
  kUnchanged = 3,
  /////////////////////////////////
  // 在线模式下独有状态
  /**
   * @brief 本地无数据，正在加载，只在在线模式下使用
   */
  kDownloading = 4,
  /////////////////////////////////
  // 离线模式下独有状态
  /**
   * @brief 在当前已下载城市中找不到，需要更新离线数据
   */
  kNotFound = 5,
  /**
   * @brief 瓦片所属离线文件异常，如文件版本错误，与引擎不兼容等
   */
  kFileError = 6,
};

typedef uint32_t RestrictionCondID;

enum class TrafficType : int8_t { kArea = 0, kLine = 1, kArea4k = 2 };
std::ostream &operator<<(std::ostream &out, const TrafficType &type);

enum class CloseType : int8_t { kZLevel = 0, kLine = 1, kArea4k = 2 };

/**
 * 多语言名称
 */
struct NameWithLanguage {
  /**
   * 名称
   */
  std::string name;

  /**
   * 对应语言
   */
  MapDataLanguageType language_type{nerd::api::MapDataLanguageType::kLANGUAGE_CN};
};

/**
 * 多语言名称数组
 */
struct NameArrWithLanguage {
  /**
   * 名称数组
   */
  std::vector<std::string> names{};

  /**
   * 对应语言
   */
  MapDataLanguageType language_type{nerd::api::MapDataLanguageType::kLANGUAGE_CN};
};

/**
 * @brief 返回查询tile list是否都是高精
 * @param data_status_ 当前数据状态，仅当kOfflineOk，kOnlineOk时is_all_hd_data_才有意义
 * @param is_all_hd_data_ 是否所有tile都有hd数据
 */
class CheckHDDataResult {
 public:
  DataStatus data_status_{DataStatus::kDownloading};
  bool is_all_hd_data_{false};
  bool is_all_4k_data{false};
};

/**
 * @brief 表面类型
 */
enum class SurfaceObjectType : uint8_t {
  /**
   * 地面标志
   */
  kROAD_MARK = 1,
  /**
   * 保护设施
   */
  kPROTECTION_FACILITY = 2,
  /**
   * 杆状物
   */
  kPOLE = 3,
  /**
   * 障碍物
   */
  kOBSTACLE = 4,
  /**
   * 悬挂物
   */
  kOVERHEAD = 5,
  /**
   * 避险区
   */
  kESCAPE = 6,
  /**
   * 警示牌
   */
  kWARNING_SIGN = 7,
  /**
   * 红绿灯
   */
  kTRAFFIC_LIGHT = 8,
  /**
   * 大型建筑物
   */
  kLARGE_BUILDING = 9,
  /**
   * 桥墩
   */
  kPIER = 10,
  /**
   * 导流区
   */
  kLEAD_AREA = 11,
  /**
   * 收费站
   */
  kTOLL_STATION = 12,
  /**
   * 绿化带
   */
  kGREEN_BELT = 13,
  /**
   * 隔离带
   */
  kSEPARATOR_BELT = 14,
  /**
   * 独立导流带
   */
  kALONE_LEADAREA = 15,
  /**
   * 绿化带
   */
  kOLD_GREEN_BELT = 16,
  /**
   * 路名参考线
   */
  kROAD_NAME_LINE = 17,
  /**
   * 分离合并点
   */
  kSPLIT_MERGE = 18,
  /**
   * 空类型（占位不显示，为了向后兼容新数据）
   */
  kEMPTY = 31,
};

/**
 * @brief LaneChangePoint类型
 */
enum class LaneChangePointType : bool {
  /**
   * 无
   */
  kNONE = 0,
  /**
   * 保护设施
   */
  kSPECICAL_LANE = 1,
};

enum class OverlapType : uint8_t {
  /**
   * 停车让行线
   */
  kGIVE_WAY,
  /**
   * 斑马线
   */
  kZEBRA,
  /**
   * 路口面
   */
  kCrossArea,
  /**
   * 禁停区
   */
  kFORBIDDEN_STOP_ZONE,
  /**
   * 减速带
   */
  kSLOW_DOWN,
  /**
   * 停车区
   */
  kPARKING,
  /**
   * 站台
   */
  kSTATION,
  /**
   * 红绿灯
   */
  kTrafficLight,
  /**
   * 车道
   */
  kLane
};

/**
 * @brief 行政区编号
 */
typedef uint32_t AdCodeType;

}  // namespace api
}  // namespace nerd

namespace std {
/**
 * GCC5.4等低版本使用枚举值为unordered_map的key时，需要对应的哈希函数
 * 对作为key的枚举类型进行std::hash特化
 */
template <>
struct hash<nerd::api::MapDataBuildingBlockID> {
  size_t operator()(const nerd::api::MapDataBuildingBlockID &type) const noexcept {
    return static_cast<std::size_t>(type);
  }
};

template <>
struct hash<nerd::api::SurfaceObjectType> {
  size_t operator()(const nerd::api::SurfaceObjectType &type) const noexcept { return static_cast<std::size_t>(type); }
};

template <>
struct hash<nerd::api::LaneChangePointType> {
  size_t operator()(const nerd::api::LaneChangePointType &type) const noexcept {
    return static_cast<std::size_t>(type);
  }
};

template <>
struct hash<nerd::api::OverlapType> {
  size_t operator()(const nerd::api::OverlapType &type) const noexcept { return static_cast<std::size_t>(type); }
};
}  // namespace std