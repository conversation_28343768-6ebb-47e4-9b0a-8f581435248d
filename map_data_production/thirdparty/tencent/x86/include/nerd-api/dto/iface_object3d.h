// Copyright (c) 2022 Tencent Inc. All rights reserved.
//
// Created by wallst on 2022/7/18.
//

#pragma once

#include "dto/surfaceobject.h"
#include "dto/types.h"

namespace nerd {

namespace api {

class IObject3DFeature;
typedef std::shared_ptr<IObject3DFeature> IObject3DFeaturePtr;
typedef std::shared_ptr<const IObject3DFeature> IObject3DFeatureConstPtr;

/**
 * @brief 通模, 精模原信息，按照瓦片存储
 * */
class NERD_EXPORT IObject3DFeature {
 public:
  virtual ~IObject3DFeature() = default;
  /**
   * @brief 获取该模型的基准点
   **/
  virtual const nerd::api::Coordinate &GetModelPos() const = 0;
  /**
   * @brief 获取该模型的外接矩形框
   * */
  virtual const std::vector<nerd::api::Coordinate> &GetBoundingBox() const = 0;
  /**
   * @brief 获取该模型的ID, 该ID为tile+索引
   * */
  virtual Object3DFeatureIDType GetID() const = 0;
  /**
   * @brief 获取该模型的全局ID, 全局唯一
   * */
  virtual std::string GetModelID() const = 0;
  /**
   * @brief 获取该模型的所有结构ID
   * */
  virtual const std::vector<PackedIDType> &GetMeshIDS() const = 0;
  /**
   * @brief 获取该模型的所有纹理ID
   * */
  virtual const std::vector<PackedIDType> &GetTextureIDS() const = 0;
  /**
   * @brief 获取该模型的所有材质ID
   * */
  virtual const std::vector<PackedIDType> &GetMaterialIDS() const = 0;
  /**
   * @brief 获取该模型对应的POI ID
   * @return uint64_t POI ID, 0表示无效
   */
  virtual uint64_t GetPoiId() const = 0;
};

struct NERD_EXPORT TollStationFeature {
  /**
   * 通行方向,正北方向顺时针夹角 [0,360)
   */
  int direction{0};
  /**
   * 上盖中心点
   */
  Coordinate upper_cover_center_point{0, 0};
  /**
   * 上盖矩形框，共4个点，以左下角为起点，逆时针方向四个顶点坐标
   */
  std::shared_ptr<std::vector<Coordinate>> upper_cover_geometry;
  /**
   * 各收费岛，从左到右排列
   */
  std::shared_ptr<std::vector<std::shared_ptr<TollStationTunnel>>> charge_tunnels;
  /**
   * 收费站名称每个字的坐标，从左到右
   */
  std::shared_ptr<std::vector<Coordinate>> name_points;
  /**
   * 收费站名称，无"收费站"后缀，汉字数量和name_points数量一致;
   */
  std::string name;
  /**
   * 是否被精模覆盖
   */
  bool is_covered_by_model{false};
  /**
   * 精模收费站高度
   * @return
   */
  int GetPreciseTollHeight() const {
    if (upper_cover_geometry == nullptr) {
      return 0;
    }
    auto height = std::numeric_limits<int32_t>::max();
    for (auto const &point : *upper_cover_geometry) {
      height = std::min(point.relative_z, height);
    }
    return height;
  }
};

/**
 * 精模和其他数据的压盖关系
 */
struct NERD_EXPORT LandMarkOverlapGroup {
  /**
   * 精模id
   */
  std::string landmark_id;
  /**
   * 地上部分与landmark_id压盖的室内图建筑id
   */
  std::vector<uint64_t> overground_bld_ids;
  /**
   * 地下部分与landmark_id压盖的室内图建筑id
   */
  std::vector<uint64_t> underground_bld_ids;
  /**
   * 白模id
   */
  std::vector<AreaFeatureIDType> white_bld_ids;
};

/**
 * 没有精模的室内图和白模的压盖关系
 */
struct NERD_EXPORT IndoorOverlapGroup {
  /**
   * 室内图建筑id
   */
  uint64_t bld_id;
  /**
   * 与bld_id的地上部分压盖的白模id
   */
  std::vector<AreaFeatureIDType> overground_white_bld_ids;
  /**
   * 与bld_id的地下部分压盖的白模id
   */
  std::vector<AreaFeatureIDType> underground_white_bld_ids;
};

}  // namespace api
}  // namespace nerd
