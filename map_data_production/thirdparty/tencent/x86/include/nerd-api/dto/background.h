// Copyright 2022 Tencent Inc. All rights reserved.

#pragma once

#include <cstdint>
#include <memory>
#include <vector>

#include "dto/types.h"
#include "nerd_export.h"

namespace nerd {

namespace api {

// 普通背景 + AOI + 地铁面
struct NERD_EXPORT RegionFeature {
  // ID
  AreaFeatureIDType id{0, 0};
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺, 指明各级别比例尺是否需要显示
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 平面点串
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 公共边索引, 指明公共边在几何形状中的下标
  std::vector<int32_t> common_edges;
  // 原始数据提供的ID, 根据src不同代表不同体系
  std::string raw_id;
  // 类型, 原始数据提供的类型码, 根据src不同代表不同体系; 备注: 该字段目前没有赋值;
  std::string kind_code;
  // 数据来源: background, aoi, subway
  std::string src;
  /**
   * 用于景区激活控件显示的开始级别;
   * 默认值:0xFF, 当渲染显示级别 > 该值时 做业务逻辑处理;
   **/
  uint8_t from_level_to_activate = 0xFF;
};

// 楼块
struct NERD_EXPORT BuildingFeature {
  // ID, 备注: 引擎动态生成, 瓦片内唯一;
  FeatureIDType id{0, 0};
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 高度(米)
  float height{0};
  // 平面点串
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 公共边索引, 指明公共边在几何形状中的下标
  std::vector<int32_t> common_edges;
  // 是否被精模覆盖
  bool is_covered_by_model{false};
  // 是否可点击
  bool is_clickable{false};
  // 关联的POI ID, 0: 无效
  uint64_t poi_id{0};
  // 关联的通模ID
  std::vector<FeatureIDType> model_ids{};
  // 是否有女儿墙
  bool has_parapet{false};
  // 顶结构几何, MultiPolygon
  std::vector<std::vector<nerd::api::Coordinate>> roofs{};
  // 楼的质心(备注: 若多个楼属于同一个楼, 该质心代表综合楼体的质心; 若为0代表无效)
  nerd::api::Coordinate centroid;
  // 0(楼块), 1(农田), 2(耕地)
  int32_t src{0};
  // 描述楼块与道路的压盖关系, 每个二进制位代表一种压盖类型, 具体参考需求链接, 默认没有任何压盖;
  uint32_t overlap_flag_with_road{0};
};

// SVG：球场、停车位等
struct NERD_EXPORT SVGFeature {
  // ID
  FeatureIDType id{0, 0};
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 矩形四个顶点的坐标
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 是否跨瓦片
  bool is_cross_tile{false};
};

// 人行天桥面
struct NERD_EXPORT PedestrianOverpassFeature {
  // ID
  FeatureIDType id{0, 0};
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 空间点串 (3D)
  std::shared_ptr<std::vector<Coordinate>> geometry3d;
};

// 普通POI, 行政地名, 公交站, 地铁站, 地铁出入口, 红绿灯, 湖泊标注 (Icon & 文字)
struct NERD_EXPORT PointFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 文字方向
  std::vector<uint8_t> text_dir;
  // 避让优先级
  int32_t priority{0};
  // 名称
  NameWithLanguage name;
  // 名称换行位置
  int32_t name_break_pos{0};
  // 原始ID
  uint64_t raw_id{0};
  // 室内ID
  uint64_t indoor_raw_id{0};
  // 用于检索的类型: 0(普通POI), 1(公交), 2(地铁站), 4(地铁出入口), 101(城市名)
  int32_t search_type{0};
  // Rich信息文本(备注:离线数据有效)
  std::string rich_text;
  // Rich信息换行位置(备注:离线数据有效)
  int32_t rich_break_pos{0};
  // Rich样式ID(备注:离线数据有效)
  int32_t rich_style_id{0};
  // 渲染坐标 (3D)
  Coordinate geometry3d{0, 0};
  // 别名(备注:离线数据有效)
  std::string alias;
  // 原始分类编码(备注:离线数据有效)
  int32_t kind_code{0};
  // 原始数据Rank(备注:离线数据有效)
  int32_t raw_rank{0};
  // 地址(备注:离线数据有效)
  NameWithLanguage address;
  // 电话号码(备注:离线数据有效)
  std::string phone_num;
  // 网点类型, 如4S店, 5S店等(备注:离线数据有效)
  std::string sale_type;
  // 品牌(备注:离线数据有效)
  std::string brand;
  // 导航坐标 (3D)(备注:离线数据有效)
  Coordinate nav_point{0, 0};
  // 角标样式ID
  int32_t icon_style_id{0};
  /**
   * 旋转角度, 范围[0,360], 备注: 若值为-1代表没有角度;
   * 场景: 对应底商的文字需要贴墙显示;
   */
  float angle{-1};
};

// 模型点描述: 种树, 加油站等
struct NERD_EXPORT ModelFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 缩放倍数
  float scale{1};
  // 旋转角度
  float angle{0};
  // 几何坐标 (3D)
  Coordinate geometry3d{0, 0};
  // 是否被精模覆盖
  bool is_covered_by_model{false};
  // 通模类型
  uint8_t kind{0};
};

// 标牌: 道路标牌, 地铁标牌 (图标底板随文字长度自适应)
struct NERD_EXPORT SignFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 名称
  NameWithLanguage name;
  // 名称换行位置
  int32_t name_break_pos{0};
  // 几何坐标 (3D)
  Coordinate geometry3d;
};

// 箭头 (带旋转角度的贴图)
struct NERD_EXPORT ArrowFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 旋转角度
  float angle{0};
  // 几何坐标 (3D)
  Coordinate geometry3d{0, 0};
};

// 沿线文字标注: 道路名, 铁路名
struct NERD_EXPORT TextLabelFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 避让优先级
  int32_t priority{0};
  // 名称
  NameWithLanguage name;
  // 名称换行位置
  int32_t name_break_pos{0};
  // 标注形态类型: 1(直线标注), 2(曲线标注)
  int32_t shape_type{0};
  // 旋转角度[0, 360), 当shape_type=1时有效
  float angle{0};
  // 局部沿线曲线坐标串, 当shape_type=2时有效 */
  std::shared_ptr<std::vector<Coordinate>> curve_geometry;
  // 缩放状态, 三位的十进制数, 百位表示标准, 十位表示放大, 个位表示超大, 数值含义: 0(无效), 1(直线), 2(曲线)
  int32_t zoom_status;
  // 几何坐标 (3D)
  Coordinate geometry3d{0, 0};
};

// 带标注信息的线: 道路线, 地铁线, 铁路线, 河流中线, ZLevel等
struct NERD_EXPORT LineWithLabelFeature {
  // 标注信息描述
  struct NERD_EXPORT LabelInfo {
    // 文本列表
    NameArrWithLanguage text_list;
    // 样式ID
    int32_t style_id{0};
    /**
     * 文本对应显示比例尺信息
     * 备注: 每个文本对应一个显示比例尺列表, 因此二维数组; 外层数组个数与text_list的个数相同, 部分道路有多个名称;
     */
    std::vector<std::vector<uint8_t>> show_level_list;
    // 避让优先级
    int32_t priority{0};
  };

  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺列表
  std::vector<uint8_t> show_level;
  // 压盖优先级
  int32_t priority{0};
  // 平面点串
  std::shared_ptr<std::vector<Coordinate>> geometry2d;
  // 沿线文字标注信息
  LabelInfo name_label_info;
  // 沿线标牌标注信息
  LabelInfo sign_label_info;
  // 数据来源: road, subway, railway, river, zlevel
  std::string src;
};
std::ostream &operator<<(std::ostream &out, const LineWithLabelFeature::LabelInfo &feature);
std::ostream &operator<<(std::ostream &out, const LineWithLabelFeature &feature);

// 普通线: 背景线, 行政线, 球场线, 人行天桥边线
struct NERD_EXPORT LineFeature {
  // ID
  FeatureIDType id;
  // 样式ID
  int32_t style_id{0};
  // 显示比例尺
  std::vector<uint8_t> show_level;
  // 压盖顺序优先级
  int32_t priority{0};
  // 空间点串
  std::shared_ptr<std::vector<Coordinate>> geometry3d;
};

}  // namespace api
}  // namespace nerd
