// Copyright (c) 2024 Tencent Inc. All rights reserved.

#pragma once

#include <map>
#include <memory>
#include <set>
#include <string>
#include <vector>

#include "dto/types.h"

namespace nerd {
namespace api {

class ITrafficRoadStatus;
typedef std::shared_ptr<const ITrafficRoadStatus> ITrafficRoadStatusConstPtr;

/**
 * @brief 路况形点信息
 */
struct RoadInfo {
  std::vector<nerd::api::Coordinate> points;
};
/**
 * @brief 路况图层信息(根据样式拆分的细粒度图层)
 */
struct LayerData {
  /**
   * 图层类型
   */
  uint32_t layer_type = 0;
  /**
   * 图层优先级
   */
  uint32_t priority = 0;
  /**
   *
   */
  uint32_t extra_priority = 0;
  /**
   * 路况样式(路况状态+道路等级形成的样式ID)
   */
  int8_t class_code = 0;
  /**
   * 路况形点
   */
  std::vector<RoadInfo> roads;
};
/**
 * @brief 路况瓦片数据
 */
struct RttTileData {
  /**
   * 状态信息(0:成功; 1:不存在; 2:数据错误)
   */
  int8_t code = 0;
  /**
   * 瓦片ID
   */
  uint32_t tileid = 0;
  /**
   * 图层数据
   */
  std::vector<LayerData> layers;
};
/**
 * @brief SD场景路况数据
 */
struct NERD_EXPORT SDTrafficRoadStatus {
 public:
  SDTrafficRoadStatus() = default;
  /**
   * 获取到路况时间戳; 用于过期计算
   */
  uint32_t last_timestamp{0};
  /**
   * 级别
   */
  int level{0};
  /**
   * 路况数据
   */
  RttTileData tile_data;
  /**
   * 该瓦片路况数据生命周期, 单位秒;
   */
  uint32_t interval{0};
};

/**
 * @brief 高精路况状态
 */
struct AreaStatusData {
  /**
   * 路况状态: [{畅通:0},{缓行:3},{拥堵:6},{堵塞:9}]
   */
  int8_t status = 0;
  /**
   * 百分比，percent*100, 如 100.00% 为10000
   */
  uint16_t radio = 0;
};
/**
 * @brief 道路ID与状态结构描述
 */
struct AreaRoadStatus {
  /**
   * @brief 道路ID
   */
  uint32_t id = 0;
  /**
   * @brief 面路况状态
   */
  std::vector<AreaStatusData> status;
};
/**
 * @brief 高精路况瓦片数据
 */
struct AreaRttTileData {
  /**
   * 瓦片内状态码
   */
  int8_t code = 0;
  /**
   * 瓦片ID
   */
  uint32_t tileid = 0;
  /**
   * 时间戳
   */
  uint32_t timestamp = 0;
  /**
   * 高精数据版本信息
   */
  uint32_t data_version = 0;
  /**
   * 路况状态数据
   */
  std::vector<AreaRoadStatus> road_rtts;
};

/**
 * @brief 道路ID与道路信息结构描述
 */
struct TrafficRoadInfo {
  /**
   * @brief 道路ID
   */
  uint32_t id = 0;
  /**
   * 该路段的长度信息, 单位:厘米
   */
  uint32_t length_cm = 0;
  /**
   * 该路段的前序连接道路ID;
   * 备注: 默认值 0xFFFFFFFF, 非默认值表示有效;
   */
  uint32_t prev_id = 0xFFFFFFFF;
  /**
   * 该路段的后续连接道路ID;
   * 备注: 默认值 0xFFFFFFFF, 非默认值表示有效;
   */
  uint32_t next_id = 0xFFFFFFFF;
  /**
   * 对应该路段的形点数据
   */
  std::vector<nerd::api::Coordinate> points;
};

/**
 * @brief 带有状态的路况形状点串
 */
struct NERD_EXPORT HDTrafficRoadStatus {
 public:
  HDTrafficRoadStatus() = default;
  /**
   * 获取到路况时间戳; 用于过期计算
   */
  uint32_t last_timestamp{0};
  /**
   * 高精路况瓦片数据
   */
  AreaRttTileData tile_data;
  /**
   * 道路ID与路段信息的映射关系
   * 道路ID与形点的映射关系
   * 备注: 即将废弃, 代替属性:traffic_road_infos;
   */
  std::shared_ptr<std::map<uint32_t, std::shared_ptr<TrafficRoadInfo>>> traffic_road_infos;
  /**
   * 该瓦片路况数据生命周期, 单位秒;
   */
  uint32_t interval{0};
  /**
   * 该瓦片所关联的额外的瓦片ID列表
   * 如: 若某个元素F跨多个瓦片A,B,C中，数据F实际存储只存储在A中, B,C瓦片分别存储了额外瓦片A;
   */
  std::set<TileIDType> external_tile_ids;
};

/**
 * @brief 路况road类
 * 路况road类
 */
class NERD_EXPORT ITrafficRoadStatus {
 public:
  virtual ~ITrafficRoadStatus() = default;
  /**
   * 获取该瓦片的处理状态, ret==0, 数据有效; 否则无效;
   * @return 处理状态
   */
  virtual RetMessage GetRetMessage() const = 0;
  /**
   * 返回路况数据类型, SD路况 or HD路况等
   * @return
   */
  virtual TrafficType GetTrafficType() const = 0;
  virtual const HDTrafficRoadStatus &GetHDTrafficRoadStatus() const = 0;
  virtual const SDTrafficRoadStatus &GetSDTrafficRoadStatus() const = 0;
};

/**
 * @brief 路况road类
 * 路况road类
 */
struct NERD_EXPORT TrafficRoadStatus : ITrafficRoadStatus {
 public:
  TrafficRoadStatus() = default;

 public:
  RetMessage GetRetMessage() const override { return ret; }
  TrafficType GetTrafficType() const override { return type; }
  const HDTrafficRoadStatus &GetHDTrafficRoadStatus() const override { return hd_traffic_road_status; }
  const SDTrafficRoadStatus &GetSDTrafficRoadStatus() const override { return sd_traffic_road_status; }

  // 返回结果状态: 若状态码为0, 数据有效; 非0数据数据无效
  RetMessage ret{0, ""};
  // type:指定数据类型，0：面路况数据，1：线路况数据，2：4k面路况数据
  TrafficType type{TrafficType::kArea};
  HDTrafficRoadStatus hd_traffic_road_status;
  SDTrafficRoadStatus sd_traffic_road_status;
};

}  // namespace api
}  // namespace nerd