// Copyright 2021 Tencent Copyright 2021 Tencent Inc.  All rights reserved.
//
// Author: ni<PERSON><PERSON><PERSON>@tencent.com (Emperor Penguin)
// 数据引擎NERD之NERD数据读取模块

#pragma once

#include <memory>
#include <string>
#include <vector>
#include "dto/iface_topology.h"
#include "dto/types.h"

namespace nerd {
namespace api {

enum class BoundaryType : uint8_t;
enum class BoundaryMarkingType : uint8_t;
enum class BoundaryMarkingColorType : uint8_t;
enum class BoundaryExtraInfoType : uint8_t;
enum class BoundaryExtraInfoIndicator : uint8_t;
enum class BoundaryMaterialType : uint8_t;

typedef std::vector<BoundaryType> BoundaryTypeVec;
typedef std::vector<BoundaryMarkingType> BoundaryMarkingTypeVec;
typedef std::vector<BoundaryMarkingColorType> BoundaryMarkingColorTypeVec;
typedef std::vector<BoundaryMaterialType> BoundaryMaterialTypeVec;
typedef std::vector<BoundaryExtraInfoType> BoundaryExtraInfoTypeVec;
typedef std::vector<BoundaryExtraInfoIndicator> BoundaryExtraInfoIndicatorVec;

enum class LaneGroupDirection : uint8_t;

/**
 * @brief 边线类型
 */
enum class BoundaryType : uint8_t {
  OTHER = 0,              /** 未知 */
  MARKING,                /** 标线 */
  CURB,                   /** 路牙 */
  GUARDRAIL,              /** 护栏 */
  WALL,                   /** 墙 */
  ROAD_EDGE,              /** * 道路铺设边缘 */
  PROTECT_NET,            /** 防护网 */
  VIRTUAL_LINE,           /** 虚拟线 */
  POLE,                   /** 杆 */
  CONSTRUCTION,           /** 道路施工边缘 */
  NATURAL_BOUNDARY,       /** 自然边界 */
  ROAD_FACILITY_EDGE,     /** 道路设施边界 */
  ISOLATION_POLE,         /** 隔离桩 */
  EDGE_VIRTUAL_LINE,      /** 边缘虚拟线 */
  MISSING_VIRTUAL_LINE,   /** 缺失虚拟线 */
  CROSS_VIRTUAL_LINE,     /** 路口虚拟线 */
  VIRTUAL_AUXILIARY_LINE, /** 虚拟辅助线 */
  CONSTRUCTION_ENCLOSURE, /** 施工围挡 */
  MOVABLE_GUARD_RAIL      /** 移动护栏 */
};

/**
 * @brief 车道标线类型
 */
enum class BoundaryMarkingType : uint8_t {
  /**
   * 不应用
   */
  kNO_DATA = 15,
  /**
   * 其他
   */
  kOTHER = 0,
  /**
   * 实线
   */
  kSOLID,
  /**
   * 虚线
   */
  kDASHED,
  /**
   * 短粗虚线
   */
  kSHORT_BOLD,
  /**
   * 导流线
   */
  kLEAD,
  /**
   * 停车位标线
   */
  kPARKING,
  /**
   * 缺失虚拟线
   */
  kMISSING_VIRTUAL,
  /**
   * 路口虚拟线
   */
  kCROSS_VIRTUAL,
  /**
   * 道路边缘
   */
  kROAD_EDGE,
  /**
   * 左转待转区边线
   */
  kLEFT_TURN_WAITING_DASHED,
  /**
   * 粗实线
   */
  kBORD_SOLID,
  /**
  * 虚拟辅助线
  */
  kVIRTUAL_ASSIST,
  /**
   * 道路边缘线
   */
  ROAD_EDGE_LINE,
  /**
   * 导向车道线
   */
  GUIDE_LANE_LINE
};

/**
 * @brief 标线颜色类型
 */
enum class BoundaryMarkingColorType : uint8_t {
  /**
   * 其他
   */
  kOTHER = 0,
  /**
   * 白色
   */
  kWHITE,
  /**
   * 黄色
   */
  kYELLOW,
  /**
   * 蓝色
   */
  kBLUE,
  /**
   * 绿色
   */
  kGREEN,
  /**
   * 橙色
   */
  kORANGE,
  /**
   * 红色
   */
  kRED,
  /**
   * 无数据
   */
  kNO_DATA = 15,
};

/**
 * @brief 边线附属线类型
 */
enum class BoundaryExtraInfoType : uint8_t {
  /**
   * 无
   */
  NONE = 0,
  /**
   * 纵向减速标线
   */
  SLOW_DOWN = 1,
  /**
   * 可变导向车道标线
   */
  TIME_CHANGE_LANE,
  /**
   * 借道区标识线
   */
  THROUGH_SHARE_ZONE,
  /**
   * 公交车专用道标识线
   */
  BUS,
  /**
   * HOV 车道标识线
   */
  HOV,
  /**
   * HOV 车道标识线
   */
  OTHER
};

/**
 * @brief 附属标线在标线位置
 */
enum class BoundaryExtraInfoIndicator : uint8_t { kLEFT, kRIGHT, kBOTH };

/**
 * @brief 车道标线材质
 */
enum class BoundaryMaterialType : uint8_t {
  /**
   * 无数据
   */
  kNO_DATA = 0,
  /**
   * 油漆
   */
  kOIL_PAINT = 1,
  /**
   * 凸起
   */
  kBULGE,
  /**
   * 油漆和凸起
   */
  kOIL_PAINT_AND_BULGE
};

struct BoundarySetStyle {
  /**
   * @brief 类型，例如：[MARKING, CURB]
   * 从右到左的顺序
   */
  BoundaryTypeVec boundary_types;
  /**
   * @brief 标线类型, 例如: [kSOLID, kNO_DATA]
   * boundary_type有标线时有效
   */
  BoundaryMarkingTypeVec marking_types;
  /**
   * @brief 颜色,可能为空
   * boundary_type有标线时有效
   */
  BoundaryMarkingColorTypeVec colors;
  /**
   * @brief 附加线,可能为空
   * boundary_type有标线时候有效
   */
  BoundaryExtraInfoTypeVec extra_infos;
  /**
   * @brief 附加线相对于标线的左右关系，和附件线长度一致，一一对应
   * boundary_type有标线时有效
   */
  BoundaryExtraInfoIndicatorVec extra_info_indicators;
  /**
   * @brief 材质,可能为空
   * boundary_type有标线时有效
   */
  BoundaryMaterialTypeVec materials;
};

/**
 * @brief 实线段
 */
struct SolidLineSegment {
  explicit SolidLineSegment(std::vector<Coordinate> coordinates) : points{std::move(coordinates)} {}
  /**
   * @brief 形点坐标
   */
  std::vector<Coordinate> points;
};

/**
 * @brief 形态一致的一段标线称为一个BoundarySet
 * @deprecated 请切换到BoundarySetV2获取更好的性能
 */
struct BoundarySet : public BoundarySetStyle {
  /**
   * @brief 开始坐标
   */
  Coordinate start_position{0, 0};
  /**
   * @brief 结束坐标
   */
  Coordinate end_position{0, 0};
  /**
   * 起始位置，包含
   */
  uint32_t start_coordinate_index{0};
  /**
   * 结束位置，包含
   */
  uint32_t end_coordinate_index{0};
};

/**
 * @brief 形态一致的一段标线称为一个BoundarySet
 */
struct BoundarySetV2 {
  /**
   * @brief 开始坐标
   */
  Coordinate start_position{0, 0};
  /**
   * @brief 结束坐标
   */
  Coordinate end_position{0, 0};
  /**
   * 起始位置，包含
   */
  uint32_t start_coordinate_index{0};
  /**
   * 结束位置，包含
   */
  uint32_t end_coordinate_index{0};
  /**
   * boundary类型
   */
  std::shared_ptr<BoundarySetStyle> boundary_set_style_{nullptr};

  /**
   * 数据来源
   */
  BoundaryDataSourceType source_type{BoundaryDataSourceType::kNONE};

  /**
   * @brief 组成boundary的所有实线段
   */
  std::vector<SolidLineSegment> solid_line_segments;
};

class ILane;
typedef std::shared_ptr<ILane> ILanePtr;
typedef std::shared_ptr<const ILane> ILaneConstPtr;
class IBoundary;
typedef std::shared_ptr<IBoundary> IBoundaryPtr;
typedef std::shared_ptr<const IBoundary> IBoundaryConstPtr;

/**
 * @brief boundary node基类
 */
class IBoundaryNode;
typedef std::shared_ptr<IBoundaryNode> IBoundaryNodePtr;
typedef std::shared_ptr<const IBoundaryNode> IBoundaryNodeConstPtr;
class IBoundaryNode {
 public:
  virtual ~IBoundaryNode() = default;

  /**
   * @brief 获取NodeID
   * @return NodeIDType
   */
  virtual NodeIDType GetNodeID() const = 0;
  /**
   * @brief 获取所有进入Boundary
   * @return 连接到该node的Boundary
   */
  virtual std::vector<IBoundaryConstPtr> GetEnterElements() const = 0;
  /**
   * @brief 获取所有出度的Boundary
   * @return 连接到该node的Boundary
   */
  virtual std::vector<IBoundaryConstPtr> GetOutElements() const = 0;

  /**
   * @brief 获取以id为下游的进入车道线集合
   * 1----------0
   * 2----------0
   * 3----------0
   * @param id 下游id
   * @return 车道线集合
   */
  virtual std::vector<IBoundaryConstPtr> GetEnterElementsByID(BoundaryIDType id) const = 0;

  /**
   * @brief 获取以id为上游的出度车道线集合
   * 0----------1
   * 0----------2
   * 0----------3
   * @param id 上游id
   * @return 车道线集合
   */
  virtual std::vector<IBoundaryConstPtr> GetOutElementsByID(BoundaryIDType id) const = 0;
};

/**
 * @brief 车道边界
 */
class NERD_EXPORT IBoundary : public ITopology<IBoundaryNodeConstPtr, IBoundaryConstPtr, BoundaryIDType> {
 public:
  /**
   * @brief 车道长度
   * @return 单位:厘米
   */
  virtual BoundaryLengthType GetBoundaryLengthCm() const = 0;
  /**
   * @brief 车道宽度
   * @return 单位:厘米
   */
  virtual BoundaryWidthType GetBoundaryWidthCm() const = 0;
  /**
   * @brief 车道几何形状
   * @return 几何形状坐标点
   */
  virtual const std::shared_ptr<std::vector<Coordinate>> &GetBoundaryGeometry() const = 0;
  /**
   * @deprecated 请切换到GetBoundaryChangePointsV2获取更好的性能表现
   * @brief 边界区间，按照变化点区分
   * @return 区间列表，从前到后
   */
  virtual std::vector<BoundarySet> GetBoundaryChangePoints() const = 0;
  /**
   * @brief 边界区间，按照变化点区分
   * @return 区间列表，从前到后
   */
  virtual const std::vector<BoundarySetV2> &GetBoundaryChangePointsV2() const = 0;
  /**
   * @brief 在LaneGroup中的序号，从右到左
   * @return 从0开始
   */
  virtual SeqNumType GetSeqNum() const = 0;

  /**
   * @brief 获取数据来源
   * @return 返回数据来源的类型
   */
  virtual BoundaryDataSourceType GetSourceType() const = 0;

  /**
   * @brief 判断当前boundary与rect是否相交
   * @return false: 不想交, true: 相交
   */
  virtual bool IntersectsWithRange(const Rect &range) const = 0;

  /**
   * @brief 获取该车道线上面的break point点序列
   * @return 车道线上的break point点序列, 返回数据的高程信息的单位是米;
   * */
  virtual const std::vector<BreakPointInfo> GetBreakPoints() const = 0;
  /**
   * @brief 获取该车道线上面的break point点序列
   *  备注: 该接口与GetBreakPoints的差别在于返回高程信息单位不同;
   * @return 车道线上的break point点序列, 返回数据的高程信息的单位是厘米;
   */
  virtual const std::vector<BreakPointInfo> &GetBreakPointsCm() const = 0;

  /**
   * @brief 获取该boundary的重复和显示状态
   * @return 0: 非重复, 1: 重复且显示, 2: 重复且不显示
   */
  virtual int32_t GetDisplayFlag() const = 0;

  /**
   * @brief 获取线上每个点到起点的距离
   * @return 线上每个点到起点的距离
   */
  virtual std::vector<double> GetGeometryDistanceToBegin() const = 0;
  /**
   * @brief 获取该车道线上面所有几何形点
   * 目前点序列包括两种
   * 1. 原始高精采集几何点(GetBoundaryGeometry) 2. 编译增加的打断几何点(GetBreakPointsCm)
   * @return 返回点序列, 返回的高程信息单位是厘米;
   * */
  virtual std::vector<Coordinate> GetAllGeometries() const = 0;
  /**
   * @brief 获取线上每个点到起点的距离
   * 备注: 形点列表相对于 GetAllGeometries的返回;
   * @return 线上每个点到起点的距离
   */
  virtual std::vector<double> GetAllGeometriesDistanceToBegin() const = 0;
  /**
   * 获取任意一个点到线起点的距离
   * @warning 注意，这里不包含BreakPoint
   * @param coordinate 坐标，GCJ02
   * @param position
   * @return 指定下标到起点的距离，点不在车道面内，则返回负数，单位:米
   */
  virtual double CalcDistanceToBegin(const nerd::api::GeoCoordinate &coordinate, InLinePosition &position) const = 0;

  /**
   * 获取任意一个点到线起点的距离
   * @warning 注意，这里不包含BreakPoint
   * @param coordinate 坐标，球面坐标系
   * @param position
   * @return 指定下标到起点的距离，点不在车道面内，则返回负数，单位:米
   */
  virtual double CalcDistanceToBegin(const nerd::api::Coordinate &coordinate, InLinePosition &position) const = 0;

  /**
   * boundary形点是否需要逆序；路况时，双向道路，end2start向渲染需要逆序点
   * @return
   */
  virtual bool IsNeedReverseGeometries() const = 0;

  /**
   * @brief boundary属于的lane group id
   * @return lane group id
   */
  virtual LaneGroupIDType GetAttachedLaneGroupID() const = 0;
};

}  // namespace api
}  // namespace nerd
