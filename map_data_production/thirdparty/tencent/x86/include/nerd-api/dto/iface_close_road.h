// Copyright (c) 2024 Tencent Inc. All rights reserved.

#pragma once

#include "dto/types.h"
#include "fbgenerator/unimap_close_road_generated.h"

namespace nerd {
namespace api {

class ICloseRoadStatus;
typedef std::shared_ptr<const ICloseRoadStatus> ICloseRoadStatusConstPtr;

class ICloseRoadNode;
typedef std::shared_ptr<ICloseRoadNode> ICloseRoadNodePtr;
typedef std::shared_ptr<const ICloseRoadNode> ICloseRoadNodeConstPtr;

/**
 * 封路瓦片数据
 */
struct NERD_EXPORT TileCloseRoadStatus {
  TileCloseRoadStatus() = default;
  /**
   * 瓦片更新到的时间戳，用于计算过期;
   */
  uint32_t last_timestamp{0};
  /**
   * 生命周期
   */
  uint32_t interval{0};
  /**
   * 版本
   */
  uint32_t version{0};
  /**
   * 具体数据
   */
  std::shared_ptr<UnimapCloseRoad::TileDataT> tile_data{nullptr};
};

class NERD_EXPORT ICloseRoadStatus {
 public:
  virtual ~ICloseRoadStatus() = default;
  virtual CloseType GetCloseType() const = 0;
  virtual const TileCloseRoadStatus &GetTileCloseRoadStatus() const = 0;
};

/**
 * 封路状态
 */
struct NERD_EXPORT CloseRoadStatus : public ICloseRoadStatus {
  CloseRoadStatus() = default;
  CloseType GetCloseType() const override { return type; }
  const TileCloseRoadStatus &GetTileCloseRoadStatus() const override { return close_road_status; }

  CloseType type{CloseType::kZLevel};
  TileCloseRoadStatus close_road_status;
};

}  // namespace api
}  // namespace nerd
