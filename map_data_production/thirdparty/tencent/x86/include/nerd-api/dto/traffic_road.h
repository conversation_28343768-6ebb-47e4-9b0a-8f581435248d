//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON>(张仁昌) on 2022/7/26.
//

#pragma once

#include <bitset>
#include <iostream>
#include <memory>
#include <mutex>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>
#include "api/data_cube.h"
#include "dto/iface_traffic_road.h"
#include "dto/types.h"

namespace nerd {
namespace api {

/**
 * @brief 高精路况,不会变的数据,存在data_cube里面
 */
class TrafficRoad {
 public:
  ~TrafficRoad() = default;
  const std::shared_ptr<std::vector<Coordinate>>& GetCoors() const;
  void SetCoors(const std::shared_ptr<std::vector<Coordinate>>& coors);

 private:
  std::shared_ptr<std::vector<Coordinate>> coors_;
};

typedef std::shared_ptr<TrafficRoad> TrafficRoadPtr;

}  // namespace api
}  // namespace nerd
