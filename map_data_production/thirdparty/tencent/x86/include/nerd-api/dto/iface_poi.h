//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by h<PERSON><PERSON> on 2022/4/26.
//

#pragma once

#include <memory>

#include "nerd_export.h"
#include "types.h"

namespace nerd {
namespace api {

class POIAttribute {
 private:
  std::string rawId;
  std::string name;
  std::string alias;
  std::string enName;
  std::string traditionalName;
  int type;
  int typeCode;
  int style;
  int showLevel;
  int priority;
  int rank;
  std::string address;
  std::string phoneNumber;
  std::string netWorkType;
  std::string brand;

 public:
  void SetRawId(std::string id) { rawId = std::move(id); }

  std::string GetRawId() const { return rawId; }

  void SetName(std::string name_) { name = std::move(name_); }

  std::string GetName() const { return name; }

  void SetAlias(std::string alias_) { alias = std::move(alias_); }

  std::string GetAlias() const { return alias; }

  void SetEnName(std::string enName_) { enName = std::move(enName_); }

  std::string GetEnName() const { return enName; }

  void SetTraditionalName(std::string traditionalName_) { traditionalName = std::move(traditionalName_); }

  std::string GetTraditionalName() const { return traditionalName; }

  void SetType(int type_) { type = type_; }

  int GetType() const { return type; }

  void SetTypeCode(int typeCode_) { typeCode = typeCode_; }

  int GetTypeCode() const { return typeCode; }

  void SetStyle(int style_) { style = style_; }

  int GetStyle() const { return style; }

  void SetShowLevel(int showLevel_) { showLevel = showLevel_; }

  int GetShowLevel() const { return showLevel; }

  void SetPriority(int priority_) { priority = priority_; }

  int GetPriority() const { return priority; }

  void SetRank(int rank_) { rank = rank_; }

  int GetRank() const { return rank; }

  void SetAddress(std::string address_) { address = std::move(address_); }

  std::string GetAddress() const { return address; }

  void SetPhoneNumber(std::string phoneNumber_) { phoneNumber = std::move(phoneNumber_); }

  std::string GetPhoneNumber() { return phoneNumber; }

  void SetNetWorkType(std::string netWorkType_) { netWorkType = std::move(netWorkType_); }

  std::string GetNetWorkType() const { return netWorkType; }

  void SetBrand(std::string brand_) { brand = std::move(brand_); }

  std::string GetBrand() const { return brand; }
};

class IPOI;
typedef std::shared_ptr<IPOI> IPOIPtr;
typedef std::shared_ptr<const IPOI> IPOIConstPtr;

class NERD_EXPORT IPOI {
 public:
  virtual ~IPOI() = default;
  /**
   * @brief 获取POI的属性
   * @return POIAttribute
   */
  virtual POIAttribute GetPOIAttribute() const = 0;

  /**
   * @brief 获取POI的子点
   * @return IPoiConstPtr
   */
  virtual std::string GetPOIChild() const = 0;

  /**
   * @brief 获取POI的显示坐标
   * @return 显示坐标
   */
  virtual Coordinate GetPOIDisplayGeometry() const = 0;

  /**
   * @brief 获取POI的导航坐标
   * @return 导航坐标
   */
  virtual Coordinate GetPOINavigationGeometry() const = 0;
};

}  // namespace api
}  // namespace nerd
