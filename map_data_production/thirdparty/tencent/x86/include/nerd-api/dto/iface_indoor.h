//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by weidonghua on 2023/4/21.
//

#pragma once
#include <memory>
#include <string>
#include <vector>
#include "nerd_export.h"

namespace nerd {
namespace api {

/**
 * 室内图图层类型
 */
enum class IndoorLayerType : uint8_t {
  kINDOOR_FRAME_REGION,
  kINDOOR_SHOP_REGION,
  kINDOOR_ASSISTANT_REGION,
  kINDOOR_FACILITIES_REGION,
  kINDOOR_REGION,
  kINDOOR_SHOP_POINT,
  kINDOOR_PUBLIC_SERVICE,
  kINDOOR_ESCALATOR,
  kINDOOR_POI_LABEL,
  kINDOOR_POI,
  kOLD_PARTING_LINE,
  kINDOOR_LINE,
  kINDOOR_ROAD_ARROW,
  kINDOOR_UNKNOWN,
};

enum class BoxType : int8_t { kOTHER = -1, kDOOR = 0, kBRICK, kWALL, kPLANE, k3D, kPLANE_3D };

enum class BuildingFlag : uint32_t {
  kBUILDING_NORMAL = 0,
  kBUILDING_HOLE = 1,
  kBUILDING_FLOOR = 2,
  kBUILDING_3D = 4,
  kBUILDING_ESCALATOR = 8
};

/**
 * 室内图坐标，像素坐标
 */
struct IndoorPoint {
  int32_t x;
  int32_t y;
};

/**
 * 室内图小数位坐标，小数点2位后*100
 */
struct IndoorPointFloat {
  char x;
  char y;
};

/**
 * 精度到小数位坐标
 */
struct Indoor4KPoint {
  float x;
  float y;
  float z;

  void set(float _x, float _y, float _z) {
    x = _x;
    y = _y;
    z = _z;
  }
};

/**
 * 矩形框，像素坐标
 */
struct IndoorRect {
  int32_t left;
  int32_t top;
  int32_t right;
  int32_t bottom;
};

/**
 * 室内图索引中的建筑物信息
 */
struct NERD_EXPORT IndoorBuildingInfo {
  /**
   * 建筑物id
   */
  uint64_t bld_id;
  /**
   * 建筑物自身的版本号
   */
  uint32_t version;
  /**
   * 显示等级，为适配unimap底图等级，内部做了-1操作
   */
  uint32_t show_level;
  /**
   * 包围盒
   */
  IndoorRect bounding_box;
};

/**
 * 室内图白名单数据
 */
struct NERD_EXPORT IndoorWhiteInfo {
  /**
   * 公司名
   */
  std::string company_name;
  /**
   * 属于该公司的建筑物id
   */
  std::vector<std::uint64_t> building_ids;
};

/**
 * 建筑物属性
 */
struct NERD_EXPORT BuildingAttribute {
  /**
   * guid = cityCode+innerId
   */
  uint64_t guid;
  /**
   * 建筑物名称
   */
  std::string bld_name;
  /**
   * 默认楼层
   */
  uint8_t default_floor;
  /**
   * 楼层数
   */
  uint8_t floor_num;
  /**
   * 楼层名称
   */
  std::vector<std::string> floor_names;
  /**
   * 主点内容的包围盒，像素单位
   */
  IndoorRect bound;
  /**
   * category
   */
  std::string category;
  /**
   * 地面之上楼层数量
   */
  uint8_t fr_count_above_ground;
  /**
   * 地上轮廓
   */
  std::vector<IndoorPoint> region_above_ground;
  /**
   * 地下轮廓
   */
  std::vector<IndoorPoint> region_under_ground;
  /**
   * 地上包围盒
   */
  IndoorRect bound_above_ground;
  /**
   * 地下包围盒
   */
  IndoorRect bound_under_ground;
  /**
   * 建筑实际版本号
   */
  uint32_t version_id;
};

/**
 * 室内图建筑Region的3D信息
 */
struct Style3D {
  /**
   * 高度
   */
  char height_3d;
  /**
   * 显示类型
   */
  BoxType display_type;
  /**
   * thickness
   */
  char thickness;
  /**
   * 角度
   */
  uint16_t angle;
  /**
   * 宽度
   */
  uint16_t rect_width;
  /**
   * 高度
   */
  uint16_t rect_height;
  /**
   * 中心坐标
   */
  IndoorPoint center;
};

struct IndoorGateInfo {
  /**
   * 是否有门梁
   */
  char has_ceiling;
  /**
   * 坐标
   */
  Indoor4KPoint point_4k[2];
};

/**
 * 面obj
 */
struct RegionObject {
  /**
   * 高度
   */
  int32_t height;
  /**
   * 坐标
   */
  std::vector<IndoorPoint> points;
  /**
   * 坐标的小数部分
   */
  std::vector<IndoorPointFloat> float_points;
  /**
   * city code << 16 | build_id
   */
  uint32_t id;
  /**
   * 包围盒
   */
  IndoorRect bound;
  /**
   * poi id
   */
  std::string guid;
  /**
   * 建筑物flag, BuildingFlag的组合
   */
  uint32_t flag;
  /**
   * 3d style信息
   */
  std::shared_ptr<Style3D> style3D{nullptr};
  /**
   * 内部门信息
   */
  std::vector<IndoorGateInfo> gate_infos;
  /**
   * region id
   */
  std::string region_id;
};

/**
 * 面图层
 */
struct NERD_EXPORT RegionLayer {
  IndoorLayerType type{IndoorLayerType::kINDOOR_REGION};
  /**
   * 样式
   */
  uint16_t style_id;
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 面集合
   */
  std::vector<std::shared_ptr<RegionObject>> region_objs;
};

/**
 * 电梯数据
 */
struct NERD_EXPORT Escalator {
  /**
   * 电梯矩形
   */
  IndoorRect rect;
};

/**
 * 点object
 */
struct PoiObject {
  /**
   * 样式
   */
  uint16_t style_id;
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 低5位分别表示20,19,18,17,16级是否显示；eg.0000 1110 表示19,18,17级显示
   */
  uint8_t show_mask;
  /**
   * 16到20级每个级别的label方向
   */
  uint8_t dir[5];
  /**
   * 名称，可以是多行
   */
  std::vector<std::string> lines;
  /**
   * 坐标
   */
  IndoorPoint point;
  /**
   * 高度
   */
  uint16_t height;
  /**
   * 室内图的poi id
   */
  uint64_t poi_id;
  /**
   * 小程序角标样式id
   */
  uint32_t subscript_style{0};
};

/**
 * 点图层
 */
struct NERD_EXPORT PointLayer {
  IndoorLayerType type{IndoorLayerType::kINDOOR_POI};
  std::vector<std::shared_ptr<PoiObject>> poi_objs;
};

/**
 * 线object
 */
struct LineObject {
  /**
   * 几何信息
   */
  std::vector<IndoorPoint> points;
  /**
   * 包围盒
   */
  IndoorRect bound;
  /**
   * 样式
   */
  uint16_t style_id;
  /**
   * 高度
   */
  uint16_t height;
};

/**
 * 线图层
 */
struct NERD_EXPORT LineLayer {
  IndoorLayerType type{IndoorLayerType::kINDOOR_LINE};
  /**
   * 样式id
   */
  uint16_t class_code;
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 线object集合
   */
  std::vector<std::shared_ptr<LineObject>> line_objs;
};

/**
 * 道路箭头
 */
struct RoadArrowObject {
  /**
   * 偏转角
   */
  int16_t angle;
  /**
   * 坐标
   */
  IndoorPoint point;
  /**
   * 样式id
   */
  uint16_t style_id;
  /**
   * 高度
   */
  uint16_t height;
};

/**
 * 道路箭头图层
 */
struct NERD_EXPORT RoadArrowLayer {
  IndoorLayerType type{IndoorLayerType::kINDOOR_ROAD_ARROW};
  /**
   * 优先级
   */
  int32_t priority;
  /**
   * 道路箭头集合
   */
  std::vector<std::shared_ptr<RoadArrowObject>> road_arrow_objs;
};

/**
 * 每个楼层的数据
 */
class NERD_EXPORT IFloorData {
 public:
  virtual ~IFloorData() = default;
  /**
   * 获取面数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<RegionLayer>> GetRegionLayers() const = 0;

  /**
   * 获取电梯数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<Escalator>> GetEscalators() const = 0;

  /**
   * 获取点数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<PointLayer>> GetPointLayers() const = 0;

  /**
   * 获取线数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<LineLayer>> GetLineLayers() const = 0;

  /**
   * 获取道路箭头数据
   * @return
   */
  virtual const std::vector<std::shared_ptr<RoadArrowLayer>> GetRoadArrowLayers() const = 0;
};

class NERD_EXPORT IIndoorBuilding {
 public:
  virtual ~IIndoorBuilding() = default;
  /**
   * 建筑物属性
   */
  virtual const BuildingAttribute& GetAttribute() const = 0;

  /**
   * 每个楼层的数据
   */
  virtual const std::vector<std::shared_ptr<IFloorData>>& GetFloors() const = 0;
};

}  // namespace api
}  // namespace nerd
