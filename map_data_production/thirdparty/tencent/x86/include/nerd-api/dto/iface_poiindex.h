//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON> on 2022/5/5.
//

#pragma once

#include <memory>
#include "nerd_export.h"
#include "types.h"
#include "dto/version.h"

namespace nerd {
namespace api {

class IPOIBaseIndex {
 public:
  virtual ~IPOIBaseIndex() = default;
  virtual TileIDType GetTileId() const = 0;

  virtual const std::vector<int> &GetPOIIds() const = 0;
};

typedef std::shared_ptr<IPOIBaseIndex> IPOIBaseIndexPtr;
typedef std::shared_ptr<const IPOIBaseIndex> IPOIBaseIndexConstPtr;
class IPOIIndex;
typedef std::shared_ptr<IPOIIndex> IPOIIndexPtr;
typedef std::shared_ptr<const IPOIIndex> IPOIIndexConstPtr;

class NERD_EXPORT IPOIIndex {
 public:
  virtual ~IPOIIndex() = default;
  /**
   * @brief 获取POI索引
   * @return POIBaseIndex
   */
  virtual const std::vector<IPOIBaseIndexConstPtr> &GetPOIIndex() const = 0;
};

enum class AdminLevel : uint8_t {
  // 省级
  AdminLevelProvince = 2,
  // 市级
  AdminLevelCity = 3,
  // 区/县级
  AdminLevelCounty = 4,
  // 无效等级
  AdminLevelInvalid = 0xFF
};

struct CityBoundRect {
  // 最左端经度
  int32_t left{0};
  // 最上端纬度
  int32_t top{0};
  // 最右端纬度
  int32_t right{0};
  // 最下端经度
  int32_t bottom{0};
  CityBoundRect() = default;
  CityBoundRect(int32_t l, int32_t t, int32_t r, int32_t b) : left(l), top(t), right(r), bottom(b) {}
};

struct CityAdmin {
  // 行政区划编码
  int32_t code{0};
  // 城市名称
  uint16_t name[24]{0};
  // 城市文件名称
  std::string fileName;
  // 城市中心点坐标
  Coordinate center{0, 0, 0};
  // 城市坐标范围盒子
  CityBoundRect bound{0, 0, 0, 0};
  // 每个行政区域边界的点集数量 p_number,v_number
  std::vector<int> polygon_number;
  // 行政区域划边界点集, p1p2..pnv1v2..vn
  std::vector<Coordinate> polygons;
};

struct TotalAdmin {
  // 行政区划名称
  uint16_t name[16]{0};
  // 行政区划短名
  uint16_t shortName[8]{0};
  // 上级行政区划索引，值为-1则没有上级行政区划
  int32_t superIndex{-1};
  // 下级行政区划开始索引，值为-1则没有下级行政区划
  int32_t subFromIndex{-1};
  // 下级行政区划结束索引，值为-1则没有下级行政区划
  int32_t subToIndex{-1};
  // 行政区划等级，参考AdminLevel的定义
  int32_t level{-1};
  // 行政区划的范围盒子
  CityBoundRect bound{0, 0, 0, 0};
  // 城市中心点坐标
  Coordinate center{0, 0, 0};
};

struct Pinyin {
  uint16_t pinyin[8]{0};
};

struct SynonymWord {
  // 别称，如“北大”
  uint16_t name[32]{0};
  // 检索关键字，如“北京大学”
  uint16_t keyword[32]{0};
};

struct KindValueRange {
  // 类型编码的最小值，可以相等
  int32_t minValue{0};
  // 类型编码的最大值，不可以相等
  int32_t maxValue{0};

  KindValueRange(int32_t min, int32_t max) : minValue(min), maxValue(max) {}
};

struct KindWord {
  // 类型名称，如“美食”
  uint16_t name[16]{0};
  // 是否为类型的小类，不可在区分的类型，如“川菜”设置false，“美食”设置为true
  bool indexFlag{false};
  // 类型在列表中的下标
  int32_t index{0};
  // 类型编码取值区间列表，解决区间不连续
  std::vector<KindValueRange> ranges;
};

struct Landmark {
  // 地标名称，如“鸟巢”
  uint16_t name[16]{0};
  // 检索关键字，如“鸟巢”对应“国家体育场”
  uint16_t keyword[16]{0};
  // 地标所在城市下标，如“鸟巢”对应“北京市”在CityAdmin列表的下标
  int32_t index{0};
};

struct NewChain {
  std::vector<uint16_t> newChain;
};

struct GB2312ToBig5 {
  // 简体字符编码
  uint16_t chCH{0};
  // 繁体字符编码
  uint16_t chTW{0};
  GB2312ToBig5() = default;
  GB2312ToBig5(uint16_t chCH, uint16_t chTW) : chCH(chCH), chTW(chTW) {}
};

struct CueWord {
  // 输入字符
  uint16_t input[64]{0};
  // 提示词
  uint16_t cueWord[64]{0};
  // 最小前缀的长度
  int32_t minPrefixLen{0};
};

struct FilterCondition {
  // 名称
  uint16_t name[32]{0};
  // 特殊名称
  uint16_t specialName[32]{0};
  // 过滤条件
  std::string filter;
};

struct TranslateWord {
  // 源单词
  uint16_t wordOrigin[64]{0};
  // 翻译后单词
  uint16_t wordTarget[64]{0};
};

struct MultilingualWord {
  // 多语言单词
  uint16_t word[32]{0};
};

struct AdminMultilingualName {
  // 多语言城市名称
  uint16_t name[64]{0};
};

struct FtsCityHeader {
  // 城市西南角tileId
  TileIDType southWestTileId{0};
  // tile的行数
  int16_t numRows{0};
  // tile的列数
  int16_t numColumns{0};
  // 城市坐标矩形框
  Rect rect;
};

class IPOIGlobalIndex {
 public:
  virtual ~IPOIGlobalIndex() = default;
  virtual std::shared_ptr<const std::vector<CityAdmin>> GetCityAdminList() const = 0;
  virtual std::shared_ptr<const std::vector<TotalAdmin>> GetTotalAdminList() const = 0;
  virtual std::shared_ptr<const std::vector<Pinyin>> GetPinyinList() const = 0;
  virtual std::shared_ptr<const std::vector<SynonymWord>> GetSynonymWordList() const = 0;
  virtual std::shared_ptr<const std::vector<KindWord>> GetKindWordList() const = 0;
  virtual std::shared_ptr<const std::vector<Landmark>> GetLandmarkList() const = 0;
  virtual std::shared_ptr<const std::vector<NewChain>> GetNewChainList() const = 0;
  virtual std::shared_ptr<const std::vector<GB2312ToBig5>> GetGB2312ToBig5List() const = 0;
  virtual std::shared_ptr<const std::vector<CueWord>> GetCueWordList() const = 0;
  virtual std::shared_ptr<const std::vector<FilterCondition>> GetFilterConditionList() const = 0;
  virtual uint32_t GetDataVersion() const = 0;
};

typedef std::shared_ptr<IPOIGlobalIndex> IPOIGlobalIndexPtr;
typedef std::shared_ptr<const IPOIGlobalIndex> IPOIGlobalIndexConstPtr;

class IPOIGlobalMultilingualIndex {
 public:
  virtual ~IPOIGlobalMultilingualIndex() = default;
  virtual std::shared_ptr<const std::vector<CueWord>> GetCueWordList() const = 0;
  virtual std::shared_ptr<const std::vector<TranslateWord>> GetTranslateWordList() const = 0;
  virtual std::shared_ptr<const std::vector<TranslateWord>> GetFilterTranslateList() const = 0;
  virtual std::shared_ptr<const std::vector<AdminMultilingualName>> GetCityAdminMultilingualNameList() const = 0;
  virtual std::shared_ptr<const std::vector<AdminMultilingualName>> GetTotalAdminMultilingualNameList() const = 0;
};

typedef std::shared_ptr<IPOIGlobalMultilingualIndex> IPOIGlobalMultilingualIndexPtr;
typedef std::shared_ptr<const IPOIGlobalMultilingualIndex> IPOIGlobalMultilingualIndexConstPtr;
}  // namespace api
}  // namespace nerd
