//
// Copyright (c) 2024 Tencent Inc. All rights reserved.
// Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/3/2.
//

#prag<PERSON> once

#include "map_data_engine_api.h"

namespace nerd {
namespace api {

class IRoutePlanLink;
class RoutePlanDLink;
class IBranchPicData;

typedef std::shared_ptr<const IRoutePlanLink> IRoutePlanLinkPtr;
typedef std::vector<IRoutePlanLinkPtr> IRoutePlanLinkVec;
typedef std::shared_ptr<IRoutePlanLinkVec> IRoutePlanLinkVecPtr;

typedef std::shared_ptr<RoutePlanDLink> RoutePlanDLinkPtr;
typedef std::vector<RoutePlanDLinkPtr> RoutePlanDLinkVec;

typedef std::vector<Coordinate> LinkLine;
typedef std::shared_ptr<LinkLine> LinkLinePtr;

typedef std::shared_ptr<LinkIDType> LinkIDTypePtr;

typedef std::shared_ptr<IBranchPicData> IBranchPicDataPtr;
typedef std::shared_ptr<const IBranchPicData> IBranchPicDataConstPtr;

struct RoutePlanLinkNode {
  std::vector<LinkIDType> in_links;
  std::vector<LinkIDType> out_links;
  std::vector<LinkIDType> cross_in_links;
  std::vector<LinkIDType> cross_out_links;
  NodeIDType adj_node{0, 0};
  NodeIDType main_node{0, 0};
  NodeIDType id;
  CrossFlag cross_flag;
  bool light_flag;
  Coordinate position;
  std::vector<std::vector<LinkIDType>> pass_link_seqs;
  bool exist_pass_link_seq{false};
};

typedef std::shared_ptr<RoutePlanLinkNode> RoutePlanLinkNodePtr;

struct RouteLayerContent {
  bool routing_layer{false};
  bool geometry_layer{false};
  bool guidance_layer{false};
  RouteLayerContent() {}
  RouteLayerContent(bool routing, bool geometry, bool guidance)
      : routing_layer(routing), geometry_layer(geometry), guidance_layer(guidance) {}
};

class IRoutePlanLink {
 public:
  virtual ~IRoutePlanLink() = default;

  virtual LinkIDType GetId() const = 0;
  virtual uint64_t GetRawId() const = 0;

  virtual bool IsInUpperLayer() const = 0;
  virtual LinkIDTypePtr GetAssociatedId() const = 0;

  virtual LinkLinePtr GetGeometry() const = 0;
  virtual const std::vector<IRestrictionConstPtr> &GetRestrictions() const = 0;
  virtual Coordinate GetStartPosition() const = 0;
  virtual Coordinate GetEndPosition() const = 0;
  virtual LinkAttribute const &GetAttribute() const = 0;
  virtual std::vector<std::shared_ptr<const IGuidanceInfo>> GetGuidance(GuidanceType type) const = 0;
  virtual std::unordered_map<GuidanceType, std::vector<std::shared_ptr<IGuidanceInfo>>> getStartNodeGuidanceMap()
      const = 0;
  virtual std::unordered_map<GuidanceType, std::vector<std::shared_ptr<IGuidanceInfo>>> getEndNodeGuidanceMap()
      const = 0;
  virtual NodeIDType const &GetStartNode() const = 0;
  virtual NodeIDType const &GetEndNode() const = 0;
  virtual bool GetStartNodeTrafficLightFlag() const = 0;
  virtual bool GetEndNodeTrafficLightFlag() const = 0;
  virtual uint32_t GetStartNodeLinkSize() const = 0;
  virtual uint32_t GetEndNodeLinkSize() const = 0;
};

/**
 * @brief 放大图数据基类
 * link放大图数据
 */
class IBranchPicData {
 public:
  virtual ~IBranchPicData() = default;
  /**
   * @brief 获取放大图数据
   * @return 二进制数据
   */
  virtual const std::shared_ptr<const std::vector<uint8_t>> GetPicData() const = 0;
};

/**
 * @brief 带方向的link类(Link with Direction)
 */
class RoutePlanDLink {
 public:
  /**
   * @brief 获取其中的Link，不为空
   * @return Link指针
   */
  IRoutePlanLinkPtr link;

  /**
   * @brief 获取Link的行驶方向。方向只可能是顺向或逆向之一，
   *        逆向时要注意个别link属性是反的，比如 node、形点等属性
   * @return 行驶方向
   */
  LinkDirection direction;
  RoutePlanDLink(IRoutePlanLinkPtr link, LinkDirection direction);
};

class IRoutePlanDataCube {
 public:
  virtual ~IRoutePlanDataCube() = default;

  virtual IRoutePlanLinkVecPtr GetLinks() = 0;
};

struct GetRoutePlanByRectResult {
 public:
  RetMessage ret{0, ""};
  std::unique_ptr<IRoutePlanDataCube> data_cube;
};

class IRoutePlanDataApi {
 public:
  virtual ~IRoutePlanDataApi() = default;

  /**
   * @brief 拉取指定区域内的数据
   * @param param 区域
   * @return 查询结果
   */
  virtual GetRoutePlanByRectResult GetDataByRect(const GetMapDataByRectParam &param) = 0;

  /**
   * @brief 批量预加载tile数据
   * @param tile_ids tile列表
   * @param content 加载tile指定表的数据
   */
  virtual void PreloadTiles(const TileIDTypeVec &tile_ids, const RouteLayerContent &content) = 0;

  /**
   * @brief 通过link_id查询link
   * @param link_id 待查询的linkid
   * @return 指定link id对应的link；没有返回nullptr
   */
  virtual IRoutePlanLinkPtr GetLinkById(const LinkIDType &link_id) = 0;

  /**
   * @brief 通过node_id查询node
   * @param node_id 待查询的linkid
   * @return 指定link id对应的link；没有返回nullptr
   */
  virtual RoutePlanLinkNodePtr GetNodeById(const NodeIDType &node_id) = 0;

  /**
   * @brief 通过GetNext GetPrev获取到的link没有guidance相关信息，需要调用此函数填充
   * @param links 待填充guidance信息的link
   */
  virtual void FillLinkWithGuidanceInfo(IRoutePlanLinkVec const &links) = 0;

  /**
   * @brief 通过GetNext GetPrev获取到的link没有geometry相关信息，需要调用此函数填充
   * @param links 待填充guidance信息的link
   */
  virtual void FillLinkWithGeometry(IRoutePlanLinkVec const &links) = 0;

  /**
   * @brief 查找指定Link的下游
   * @param current_link 指定的link
   * @param start_to_end 是否在指定的link上从开始到终点行驶
   * @return 下游的Link列表
   */
  virtual RoutePlanDLinkVec GetNext(IRoutePlanLinkPtr const &current_link, bool start_to_end) = 0;

  /**
   * @brief 查找指定Link的上游
   * @param current_link 指定的link
   * @param start_to_end 是否在指定的link上从开始到终点行驶
   * @return 上游的Link列表
   */
  virtual RoutePlanDLinkVec GetPrev(IRoutePlanLinkPtr const &current_link, bool start_to_end) = 0;

  /**
   * @brief 查询点所在位置的route版本
   * @param point 待查询版本的点
   * @return 点所在位置route的版本
   */
  virtual uint32_t GetDataVersionAtPoint(Coordinate const &point) = 0;

  /**
   * @brief 查找指定Link的下游交叉点内link,
   * @param current_link 指定的link
   * @param start_to_end 是否在指定的link上从开始到终点行驶
   * @return 下游的Link列表
   */
  virtual RoutePlanDLinkVec GetNextWithinIntersection(const IRoutePlanLinkPtr &current_link, bool start_to_end) = 0;

  /**
   * @brief 查找指定Link的上游交叉点内link,
   * @param current_link 指定的link
   * @param start_to_end 是否在指定的link上从开始到终点行驶
   * @return 上游的Link列表
   */
  virtual RoutePlanDLinkVec GetPrevWithinIntersection(const IRoutePlanLinkPtr &current_link, bool start_to_end) = 0;

  /**
   * @brief 查找指定图片编号的放大图数据
   * @param pic_num 图片编号
   * @param imageData 返回的图片数据
   * @return 是否成功获取数据
   */
  virtual bool GetBranchPicDataCubeByPicNum(const std::string &pic_num, std::vector<int8_t> &imageData) = 0;

  /**
   * @brief Set the Language object
   * @param languageType
   */
  virtual void SetLanguageType(const nerd::api::MapDataLanguageType languageType) = 0;

  /**
   * @brief Set the Current Route Version object
   * 每次算路时，需要保证沿途 DataCube 为同一版本，否则 Link 匹配会出问题
   * @param version 离线数据版本号
   */
  virtual void SetCurrentRouteVersion(const uint32_t version) = 0;

  /**
   * @brief 算完路后清理内存
   */
  virtual void ClearMemory() = 0;

  /**
   * @brief 判断数据是否包含高层诱导数据
   */
  virtual bool HasUpperGuidanceInfo() = 0;

  /**
   * @brief 获取指定入边+方向和出边的交叉点内序列
   * @param curr_link_id
   * @param start_to_end
   * @param next_link_id
   * @return 非复杂路口或无需经过交叉点内link，返回空数组，不可通行时返回null
   */
  virtual std::shared_ptr<std::vector<LinkIDType>> GetInnerLinksBetween(const LinkIDType curr_link_id,
                                                                        bool start_to_end,
                                                                        const LinkIDType next_link_id) = 0;
};

NERD_EXPORT std::unique_ptr<IRoutePlanDataApi> CreateRoutePlanApi();

}  // namespace api
}  // namespace nerd
