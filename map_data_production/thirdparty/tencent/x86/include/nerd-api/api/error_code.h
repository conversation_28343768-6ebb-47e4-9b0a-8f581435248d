// Copyright (c) 2021 Tencent Inc. All rights reserved.
// Created by wallst on 2021/4/2.
//

#pragma once

#include <unordered_map>

namespace nerd {
namespace api {

namespace error_code {
/**
 * 接口调用正常时返回0;
 * 备注: 非0，返回结果不可靠;
 */
constexpr int kOk = 0;
/**
 * 接口调用其他异常时反馈错误
 */
constexpr int kError = 1;
/**
 * 调用接口传入参数不符合预期时返回
 */
constexpr int kInputParameterError = 2;
/**
 * 在线下载遇到网络错误时返回
 */
constexpr int kNetworkError = 3;
/**
 * 数据加载出现版本不匹配时返回
 */
constexpr int kVersionNotMatch = 4;
/**
 * 在线下载数据校验失败时返回
 */
constexpr int kDataChecksumError = 5;
/**
 * 解析数据错误时返回
 */
constexpr int kParseDataError = 6;
/**
 * 写磁盘遇到错误时返回
 */
constexpr int kWriteDataError = 7;
/**
 * 调用接口已经过期或不维护的接口时返回
 */
constexpr int kDeprecated = 8;
/**
 * 请求处理时间超过用户设定最大等待时间
 */
constexpr int kBeyondUserWaitTime = 9;
/**
 * 鉴权失败
 */
constexpr int kAuthError = 10;
}  // namespace error_code

/**
 * @brief 错误码和文字说明
 * 1. {0, "OK"}
 * 2. {1, "ERROR"}
 */
static const std::unordered_map<int32_t, std::string> RET_CODE_MSG = {
    {error_code::kOk, "Ok"},
    {error_code::kError, "Error"},
    {error_code::kInputParameterError, "Input parameter error"},
    {error_code::kNetworkError, "Network error"},
    {error_code::kVersionNotMatch, "Version not match"}};

}  // namespace api
}  // namespace nerd
