//
// Created by learnjiang on 2024/4/3.
//

#ifndef NERDAPI_INCLUDE_API_NERD_GLOBAL_EVENT_LISTENER_H_
#define NERDAPI_INCLUDE_API_NERD_GLOBAL_EVENT_LISTENER_H_

#include "common/common_location.h"


namespace nerd {
namespace api {

/**
 * 编译错误信息透出类
 */
class NerdGlobalEventListener {
 public:
  virtual ~NerdGlobalEventListener() = default;
  /**
   * 获取保留数据范围的中心点，一般为定位点或者屏幕中心点
   * @return
   */
  virtual mapbase::GeoCoordinate OnPreservedDataCenterPos() = 0;
};
}
}
#endif  // NERDAPI_INCLUDE_API_NERD_GLOBAL_EVENT_LISTENER_H_
