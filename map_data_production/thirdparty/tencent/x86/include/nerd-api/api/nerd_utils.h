// Copyright (c) 2021 Tencent Inc. All rights reserved.
// Created by mor<PERSON><PERSON>(morgansun) on 2021/4/1.
//

#pragma once

#include "dto/types.h"

namespace nerd {
namespace api {
namespace utils {

/**
 * @brief 转换数据坐标到球面坐标
 * @param coordinate 数据坐标
 * @return 球面坐标(GCJ02)
 */
NERD_EXPORT GeoCoordinate ToGeoCoordinate(const nerd::api::Coordinate &coordinate);

/**
 * @brief 从球面坐标生成数据坐标
 * @param coordinate 球面坐标(GCJ02)
 * @return 数据坐标
 */
NERD_EXPORT Coordinate FromGeoCoordinate(const GeoCoordinate &coordinate);
NERD_EXPORT Coordinate FromGeoCoordinate(double x, double y, double relative_z = 0);

/**
 * @brief 获取不同级别下的Tile数据坐标偏移量
 * @param level 级别(0-15)
 * @return 宽度，单位：数据坐标offset
 */
NERD_EXPORT int32_t GetTileWidth(int32_t level);

/**
 * @brief 获取指定位置的TileID
 * @param coordinate 球面坐标
 * @param level 级别，[0,15]
 * @return TileID
 * 注意：如果计算失败，返回tile id == 0
 */
NERD_EXPORT TileIDType GetTileIdByGeoCoordinate(const GeoCoordinate &coordinate, int32_t level);

/**
 * 获取指定位置的TileID
 * @param coordinate 数据坐标
 * @param level 级别, [0,15]
 * @return TileID
 * 注意：如果计算失败，返回tile id == 0
 */
NERD_EXPORT TileIDType GetTileIdByCoordinate(const Coordinate &coordinate, int32_t level);

/**
 * @brief 获取Tile的左下角坐标，这里是球面坐标，不是屏幕坐标
 * @param tile_id tile_id
 * @return 左下角坐标，数据坐标
 */
NERD_EXPORT Coordinate GetTileLeftBottom(TileIDType tile_id);

/**
 * 获取Tile的左下角坐标
 * @param tile_id tile_id
 * @return 左下角坐标，数据坐标
 */
NERD_EXPORT Coordinate GetTileRightTop(TileIDType tile_id);

/**
 * 获取Tile的gcj02坐标系下矩形框
 * @param tile_id tile_id
 * @return 矩形框坐标
 */
NERD_EXPORT std::shared_ptr<std::vector<nerd::api::GeoCoordinate>> GetTileRect(TileIDType tile_id);

/**
 * @brief 根据TileID获取级别
 * @param tile_id tileId
 * @return 级别，0-15
 */
NERD_EXPORT int32_t GetLevelByTileId(TileIDType tile_id);

/**
 * @brief 获取比例尺级别对应显示数据的NDS级别
 * @param scale_level 比例尺级别
 * @param building_block
 * @return NDS级别
 */
NERD_EXPORT MapLevel GetMapLevelByScaleLevel(int32_t scale_level, nerd::api::MapDataBuildingBlockID building_block);

/**
 * @brief 获取比例尺级别对应的路况NDS显示级别
 * @param scale_level 比例尺级别
 * @return NDS级别
 */
NERD_EXPORT MapLevel GetMapTrafficLevelByScaleLevel(int32_t scale_level);

/**
 * @brief 根据中心点以及正方形框边长构建范围
 * @param center 正方形中心点
 * @param length 正方形边长，单位米
 * @return 正方形范围
 */
NERD_EXPORT nerd::api::Rect BuildRectByLength(const nerd::api::GeoCoordinate &center, double length);

/**
 * 计算到正北方向夹角
 * @param p1 起始位置
 * @param p2 结束位置
 * @return 角度，[0,360)，p1 == p2则返回 -1
 */
NERD_EXPORT double CalcHeadingToNorth(const nerd::api::GeoCoordinate &p1, const nerd::api::GeoCoordinate &p2);

/**
 * 计算到正北方向夹角
 * @param p1 起始位置
 * @param p2 结束位置
 * @return 角度，[0,360)，p1 == p2则返回 -1
 */
NERD_EXPORT double CalcHeadingToNorth(const nerd::api::Coordinate &p1, const nerd::api::Coordinate &p2);

/**
 * 根据车道中心线计算中心线内任意一个点的相对坡度数据
 * @param lane_const_ptr 车道对象智能指针shared ptr
 * @param coordinate_start 车道中心线原始坐标点下标
 * @param match_pos 吸附到geometry上的坐标
 * @param[out] relative_slop 相对坡度 +代表上坡 -代表下坡，单位：度
 * @param[out] relative_height 相对高程, 单位：米
 * @return true-计算成功 false-参数非法
 * @note 相对坡度并不是真实数据的坡度值，相对坡度是显示用的坡度
 */
NERD_EXPORT bool CalcRelativeSlopeAndHeightOnGeometryByLane(nerd::api::ILaneConstPtr lane_const_ptr,
                                                            uint32_t coordinate_start, const GeoCoordinate &match_pos,
                                                            double &relative_slop, double &relative_height);

/**
 * 根据车道线计算车道线内任意一个点的相对坡度数据
 * @param boundary_const_ptr 车道线对象智能指针shared ptr
 * @param coordinate_start 车道线原始点下标
 * @param match_pos 吸附到geometry上的坐标
 * @param[out] relative_slop 相对坡度 +代表上坡 -代表下坡，单位：度
 * @param[out] relative_height 相对高程, 单位：米
 * @return true-计算成功 false-参数非法
 * @note 相对坡度并不是真实数据的坡度值，相对坡度是显示用的坡度
 */
NERD_EXPORT bool CalcRelativeSlopeAndHeightOnGeometryByBoundary(nerd::api::IBoundaryConstPtr boundary_const_ptr,
                                                                uint32_t coordinate_start,
                                                                const GeoCoordinate &match_pos, double &relative_slop,
                                                                double &relative_height);

/**
 * 计算两个角度的相对偏差
 * @param a1
 * @param a2
 * @return
 */
NERD_EXPORT nerd::api::AngleDegreeType CalcAngleOffset(const nerd::api::AngleDegreeType &a1,
                                                       const nerd::api::AngleDegreeType &a2);

/**
 * 将角度转化成弧度
 * @param angle 角度
 * @return
 */
NERD_EXPORT nerd::api::EnuRadType ToEnuRadType(const nerd::api::AngleDegreeType &angle);

/** 计算点到折线段的投影点，以及点到投影点距离
 * @param[in] geometry 车道中心线点集
 * @param[in] point 目标点
 * @param[out] position 点到车道中心线距离所有信息
 * @return 原始点到垂足的距离 InLinePosition中start_length 此处不计算
 */
NERD_EXPORT double CalcInLinePosition(const std::vector<nerd::api::Coordinate> &geometry,
                                      const nerd::api::Coordinate &point, nerd::api::InLinePosition &position);

/**
 * @param rect 矩形框范围
 * @param level 数据等级
 * @return 与矩形框相交的Tile列表
 */
NERD_EXPORT TileIDTypeVec GetTileIDSByRect(const Rect &rect, const MapLevel &level);

/**
 * @param[in] geometry 车道中心线点集
 * @param[in] geometry_distance_to_begin 车道中心线点到起点的距离
 * @param[in] point 目标点
 * @param[out] position 点到车道中心线距离所有信息
 * @return 原始点到垂足的距离，参考线左侧为正，右侧为负，单位：米
 */
NERD_EXPORT double CalcInLinePosition(const std::vector<nerd::api::Coordinate> &geometry,
                                      const std::vector<double> &geometry_distance_to_begin,
                                      const nerd::api::Coordinate &point, nerd::api::InLinePosition &position);

/**
 * @param start 线起点
 * @param end   线终点
 * @param point 点
 * @return 垂足到线上前一个点距离，单位：米
 */
NERD_EXPORT double GetFootOffsetOnLine(const nerd::api::Coordinate &start, const nerd::api::Coordinate &end,
                                       const nerd::api::Coordinate &point);
/**
 * @param[in] offset_ratio 偏移比例
 * @param[in] start 起点
 * @param[in] end 终点
 * @param[out] position InLinePosition的垂足坐标点
 */
NERD_EXPORT void GetFootPoint(const double offset_ratio, const nerd::api::Coordinate &start,
                              const nerd::api::Coordinate &end, nerd::api::InLinePosition &position);

/**
 * 计算每个点到起始点的距离
 * @param[in] geometry
 * @param[out] geometry_distance_to_begin
 */
NERD_EXPORT void CalcGeometryDistanceToBegin(const std::vector<nerd::api::Coordinate> &geometry,
                                             std::vector<double> &geometry_distance_to_begin);

/**
 * 插入BreakPoint到Geometry中，构造出all_geometry
 * 注意要求: 1.几何形点和打断点的高程必须统一 2. BreakPoint需要升序排列
 * @param[out] all_geometry  所有几何点
 * @param raw_geometry 原始几何点
 * @param break_points 打断点
 */
NERD_EXPORT void BuildRawAndBreakPointsInternal(std::vector<Coordinate> &all_geometry,
                                                const std::vector<Coordinate> &raw_geometry,
                                                const std::vector<BreakPointInfo> &break_points);

/**
 * 计算点的高程和坡度
 * @param coordinates 点串
 * @param break_points break_point断点(记录高程坡度变化点)
 * @param coordinate_start 点index
 * @param match_pos     点经纬度
 * @param relative_slop 返回相对坡度
 * @param relative_height 返回相对高程
 * @return
 */
bool CalcRelativeSlopeAndHeightOnGeometry(const std::vector<Coordinate> &coordinates,
                                            const std::vector<BreakPointInfo> &break_points,
                                            uint32_t coordinate_start,
                                            const GeoCoordinate &match_pos,
                                            double &relative_slop,
                                            double &relative_height);
}  // namespace utils
}  // namespace api
}  // namespace nerd
