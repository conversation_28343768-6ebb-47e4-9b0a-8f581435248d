// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by wallst on 2021/5/17.
//

#pragma once

#include "dto/types.h"
#include "nerd_export.h"

namespace nerd {
namespace api {

/**
 * @brief 发布者接口
 */
class NERD_EXPORT IObserver {
 public:
  /**
   * @brief 接收当前位置，NERD会按照当前的位置和边长预加载数据
   * @param pos 当前位置，坐标系为gcj02
   * @param rect_length 当前位置为中心的正方形边长,单位: 米
   */
  virtual void NotifyLocation(const nerd::api::GeoCoordinate &pos, int32_t rect_length) = 0;

  virtual ~IObserver() = default;
};

}  // namespace api
}  // namespace nerd
