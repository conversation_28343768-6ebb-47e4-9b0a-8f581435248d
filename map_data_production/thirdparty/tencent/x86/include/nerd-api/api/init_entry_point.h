// Copyright (c) 2021 Tencent Inc. All rights reserved.
//
// Created by wallst on 2021/5/14.
//

#pragma once

#include "nerd_export.h"

namespace nerd {
namespace env {

class NERD_EXPORT InitNerdEnv {
 public:
  /**
   * @brief 初始化Nerd环境
   * **如果通过动态库方式使用nerdapi，可以不调用该方法**
   * **如果通过静态库方式使用nerdapi，必须调用该方法**
   * **注意: chrome base库一定要放在第一位**
   * 1. 初始化chrome base库
   */
  NERD_EXPORT static void Init();

 private:
  /**
   * @brief 初始化chrome base库中的线程池
   */
  static void InitCRBaseEnvironment();
};

}  // namespace env
}  // namespace nerd
