// Copyright (c) 2012 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef DATA_MANAGER_INCLUDE_DATA_MANAGER_OFFLINE_DATA_EXPORT_H_
#define DATA_MANAGER_INCLUDE_DATA_MANAGER_OFFLINE_DATA_EXPORT_H_

#if defined(_WIN32)

#if defined(offlinedata_EXPORTS)
#define OFFLINE_DATA_EXPORT __declspec(dllexport)
#else
#define OFFLINE_DATA_EXPORT __declspec(dllimport)
#endif  // defined(OFFLINE_DATA_EXPORT)

#else  // defined(WIN32)
#if defined(offlinedata_EXPORTS)
#define OFFLINE_DATA_EXPORT __attribute__((visibility("default")))
#else
#define OFFLINE_DATA_EXPORT
#endif  // defined(OFFLINE_DATA_EXPORT)
#endif

#endif  // DATA_MANAGER_INCLUDE_DATA_MANAGER_OFFLINE_DATA_EXPORT_H_
