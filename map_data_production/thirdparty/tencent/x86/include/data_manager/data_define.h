// Copyright 2020 Tencent. All rights reserved.

//
// Created by xiashuangrong on 2020/4/18.
//

#ifndef DATA_MANAGER_INCLUDE_DATA_MANAGER_DATA_DEFINE_H_
#define DATA_MANAGER_INCLUDE_DATA_MANAGER_DATA_DEFINE_H_

#include <cstdint>
#include <ostream>
#include <string>

namespace offline_data {

enum class LOG_LEVEL : int {
  VERBOSE = -2,
  DEBUG = -1,
  INFO = 0,
  WARNING = 1,
  ERROR = 2,
  FATAL = 3,
  NONE = 4,
};

/*----------OTA状态回调---------*/
enum class OTAStatusCode : std::int8_t {
  INIT_SUCCESS = 0,  // 已初始化(包含cfg的配置检查)
  INIT_FAILED,       // 初始化失败
};

enum class TileStatusCode : std::int8_t {
  UPDATE_SUCCESS = 0,  // 更新完成（网络操作）
  UPDATING,            // 更新中（网络操作）
  UPDATE_FAILED,       // 更新失败（网络操作）
  NOT_ENOUGH_SPACE,    // 磁盘空间不足，无法下载
  EXIST                // 已经存在，不需要下载
};

struct OTASettings {
  std::string data_path{"./data"};        // 离线数据存放位置
  std::string log_path{"./log"};          // 日志存放位置
  LOG_LEVEL log_level{LOG_LEVEL::ERROR};  // 日志级别
  uint64_t log_total_size{64};            // 日志总大小，单位MB
  uint64_t log_file_size{5};              // 日志单个文件大小，单位MB
  uint64_t log_save_time{30 * 24};        // 日志保存时间，单位小时
  uint64_t tile_total_size{15 * 1024};    // OTA数据存储的大小上限，单位MB
  std::string channel;                    // 渠道号
  std::string imei;                       // 设备唯一标识
  bool use_self_cert_domain{false};       // 是否使用自签名域名
  uint32_t wait_time{30};                 // retry等待重试时长(s)
  uint32_t retry_count{10};               // retry次数，超过该次数认为失败
};

}  // namespace offline_data

#endif  // DATA_MANAGER_INCLUDE_DATA_MANAGER_DATA_DEFINE_H_
