//
// Created by 周豪杰 on 2024/11/14.
//

#ifndef DATAMANAGER_OTAMANAGER_H
#define DATAMANAGER_OTAMANAGER_H

#include <net/http_interface.h>
#include "data_manager/data_define.h"
#include "data_manager/offline_data_export.h"

namespace offline_data {

class OFFLINE_DATA_EXPORT DMStatusListener {
 public:
  virtual ~DMStatusListener() = default;

  /**
   * 监听接口
   * @param status_code ：OTA状态
   **/
  virtual void OnOTAStatusUpdate(OTAStatusCode status_code) = 0;

  /**
   * 监听接口
   * @param tile_id ：tile的id
   * @param status_code ：tile状态
   **/
  virtual void OnTileStatusUpdate(uint32_t tile_id, TileStatusCode status_code) = 0;
};


class OTAManagerImpl;
class OFFLINE_DATA_EXPORT OTAManager {
 public:
  /**
   * 初始化
   * @param settings ：OTA配置
   * @param http     : 网络请求实例
   * @param listener ：状态监听
   **/
  void Init(const OTASettings &settings, std::shared_ptr<mapbase::HttpInterface> http,
            std::shared_ptr<DMStatusListener> listener) const;

  /**
   * 输入坐标到SDK内部
   * @param lon ：经度数据坐标---坐标类型(GCJ20)
   * @param lat ：维度数据坐标---坐标类型(GCJ20)
   **/
  void SendPosition(double lon, double lat);

  /**
   * @brief TencentAdapter 单例
   */
  static OTAManager &GetInstance();

 public:
  explicit OTAManager();
  ~OTAManager();

 private:
  OTAManagerImpl *p_impl_{nullptr};
};

}  // namespace offline_data

#endif  // DATAMANAGER_OTAMANAGER_H
