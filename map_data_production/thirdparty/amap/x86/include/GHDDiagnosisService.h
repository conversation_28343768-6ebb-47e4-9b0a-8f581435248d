/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDDiagnosisDefines.h"

namespace hdmap {
namespace service {

/**
 * 诊断相关服务类
 */
class GHDMAP_API GHDDiagnosisService
{
protected:
    GHDDiagnosisService() {}
public:
    virtual ~GHDDiagnosisService() {}
    
public:
    /**
    * 获取诊断服务对象
    * @return 诊断服务实例对象
    */
    static GHDDiagnosisService* getService();
    
    
    /**
     * 更新诊断信息
     *
     * @param[in] state 诊断状态
     * @param[in] type  诊断类型
     * @param[in] detailMessage 诊断消息详情，可能为NULL，使用前需要判空。
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual void updateDiagnosis(DiagnosisState state, DiagnosisType type, const char* detailMessage) = 0;
    
    /**
     * 清除所有诊断
     */
    virtual void clearAllDiagnosis() = 0;
    
    /**
     * 更新诊断开关
     *
     * @param[in] type 诊断类型
     * @param[in] state 诊断状态\n
     *                 【开启】表示如果诊断类型发生，则会处理透出
     *                 【关闭】表示如果诊断类型发生，不会处理和透出
     */
    virtual void updateDiagnosisSwitchState(DiagnosisSwitchState state, DiagnosisType type) = 0;
    
};

}
}
