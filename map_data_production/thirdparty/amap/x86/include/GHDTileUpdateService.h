/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDMapDefines.h"

namespace hdmap {
namespace service {

class GHDMAP_API GHDTileUpdateService
{
protected:
    GHDTileUpdateService() {}
public:
    virtual ~GHDTileUpdateService() {}
    /**
     * 获取tile更新服务实例接口
     * 需要在@see GHDMainService::init之后调用,否则会返回空指针
     * @return 数据更新服务实例
     */
    static GHDTileUpdateService* getService();

    /**
     * 注意: 集成HQ数据引擎时请勿直接使用此接口. 如要开发使用此接口, 请联系高德解决方案产品沟通\n
     * 设置大范围更新开关, 若未设置, 默认为false\n
     * 调用方可随时通过该接口更改开关状态\n
     * @param[in] enabled true:允许tile大范围更新, false:关闭tile大范围更新
     */
    virtual void setLargeAreaUpdateEnabled(bool enabled) = 0;
};

}
}

