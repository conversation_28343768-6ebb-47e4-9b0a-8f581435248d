/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDOTADefines.h"

namespace hdmap {
namespace service {

/**
 * 数据更新服务类
 */
class GHDMAP_API GHDOTAService
{
protected:
    GHDOTAService() {}
public:
    virtual ~GHDOTAService() {}
    
public:
    /**
     * 获取数据更新服务实例接口
     * @return 数据更新服务实例
     */
    static GHDOTAService* getService();
    
    
    /**
     *  设置数据热更新开关\n
     *  当通过IDOTAReceiver::receiveDOTAStatus收到数据更新状态1100时，则可以通过此接口打开热切换\n
     *  打开后可以通过IDOTAReceiver::receiveDOTAStatus收到数据更新状态1200。
     *
     * @param[in] enabled true:开启数据更新热切换, false:关闭数据更新热切换
     */
    virtual void setHotSwapEnabled(bool enabled) = 0;
    
        
    /**
     *  设置数据更新开关
     *
     * @param[in] enabled 更新开关 true 打开更新， false: 关闭更新
     */
    virtual void setDOTAUpdateEnabled(bool enabled) = 0;
};

}
}
