/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDMapDefines.h"
#include "DataDefine.h"

namespace hdmap {
namespace service {

/**
 * 设备网络状态
 */
enum NetworkState {
    NETWORK_STATE_UNKNOWN = 0, ///< 未知
    NETWORK_STATE_DISCONNECT, ///< 网络未连接
    NETWORK_STATE_2G, ///< 2G网络
    NETWORK_STATE_3G, ///< 3G网络
    NETWORK_STATE_4G, ///< 4G网络
    NETWORK_STATE_5G, ///< 5G网络
    NETWORK_STATE_WIFI, ///< 无线WIFI
    NETWORK_STATE_LINE ///< 有线网络
};

/**
 * 系统状态
 */
enum SystemState {
    SYSTEM_STATE_IGNITION_UNDEFINED = 0,           ///< 点火未定义
    SYSTEM_STATE_IGNITION_LOCK,                    ///< 引擎&电源关闭，方向盘上锁
    SYSTEM_STATE_IGNITION_OFF,                     ///< 引擎&电源关闭，方向盘未锁
    SYSTEM_STATE_IGNITION_ACC,                     ///< 系统上电
    SYSTEM_STATE_IGNITION_ON,                      ///< 系统上电，引擎关闭
    SYSTEM_STATE_IGNITION_START,                   ///< 引擎启动
    SYSTEM_STATE_IGNITION_DRIVING,                 ///< 行驶中
};

/**
 * 系统信息
 */
struct GHDMAP_API SystemInfo {
    SystemInfo() {
        reset();
    }
    
    void reset() {
        systemName.clear();
        systemVersion.clear();
    }
    
    std::string systemName;      ///< 系统名称，示例：linux
    std::string systemVersion;   ///< 系统版本，示例：3.5.0
};

/**
 *  网络流量统计信息
 */
struct GHDMAP_API NetworkStatisticsInfo {
    NetworkStatisticsInfo() {
        byteOfUpstreamFlow = 0;
        byteOfDownstreamFlow = 0;
    }
    
    uint64_t byteOfUpstreamFlow;   ///< 上传流量，单位 byte
    uint64_t byteOfDownstreamFlow; ///< 下载流量，单位 byte
};

enum ActivationStatus
{
    ACTIVATION_STATUS_UNKNOWN,  ///< 未知激活状态
    ACTIVATION_STATUS_REQUESTING,  ///< 请求中
    ACTIVATION_STATUS_ACTIVATED,  ///< 已激活
    ACTIVATION_STATUS_UNACTIVATED  ///< 未激活
};

enum IDCConnectionState
{
    IDC_CONNECTION_STATUS_UNKNOWN = 0, ///< 未知连接状态
    IDC_CONNECTION_STATUS_NOCONNECT = 1, ///<  无连接状态
    IDC_CONNECTION_STATUS_CONNECT = 2, ///<  连接成功状态
    IDC_CONNECTION_STATUS_DISCONNECT = 3, ///< 连接断开状态
};

struct GHDMAP_API IDCConnectionInfo
{
    IDCConnectionInfo()
        : state(IDC_CONNECTION_STATUS_UNKNOWN), code(0)
    {
    }

    IDCConnectionState state; ///< 连接状态

    /**
     * @brief 系统错误码
     * @range [
     *    0：正常
     *   非0：系统错误，错误码含义参考当前系统定义
     * ]
     */
    int code;

    /**
     * @brief 关键错误日志,无错误时为空
     */
    std::string message;
};

/**
 * 激活信息类
 */
struct GHDMAP_API ActivationInfo
{
    ActivationInfo() : status(ACTIVATION_STATUS_UNKNOWN)
    {
    }

     ActivationInfo(ActivationStatus status, const std::vector<std::string>& scopes) : status(status), scopes(scopes)
     {
     }

    bool operator==(const ActivationInfo& other) const
    {
        return status == other.status && scopes == other.scopes;
    }

    ActivationStatus status;  ///< 激活状态
    std::vector<std::string> scopes;  ///< 激活范围
};

/**
 * 系统设备信息监听类
 */
class GHDMAP_API ISystemDeviceListener {
public:
    virtual ~ISystemDeviceListener() {}
    
    /**
     * 设备网络状态变更通知接口\n
     * 此接口可使用GHDDeviceService::updateNetworkState 替代。
     *
     * @param[in] state 网络状态
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual void onNetworkStateChanged(NetworkState state) = 0;
    
    /**
     * 系统状态变更通知接口\n
     * 此接口可使用GHDDeviceService::updateSystemState 替代。
     *
     * @param[in] state 系统状态
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual void onSystemStateChanged(SystemState state) = 0;
    
    /**
     * 获取网络流量统计接口\n
     * 预留接口，暂未支持
     *
     * @return NetworkStatisticsInfo 网络流量统计
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual const NetworkStatisticsInfo& getNetworkStatisticsInfo() = 0;
    
    /**
     * 打开或者关闭SDK日志上传，本方法必须在SDK初始化函数 GHDMapService::init 之后调用。\n
     * 此接口可使用GHDDeviceService::updateLogsEnable 替代。
     *
     * @param[in] enable true 打开日志上传; false 关闭日志上传
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual void onUploadLogsEnableChanged(bool enable) = 0;
};

/**
 * 系统设备接口类
 */
class GHDMAP_API ISystemDevice {
public:
    virtual ~ISystemDevice() {}
    
    /**
     * 获取系统唯一识别号
     *
     * @return 系统唯一识别号，需要与激活时提供的车辆唯一识别号保持一致
     */
    virtual const char *getUid() = 0;
    
    /**
     * 获取系统信息
     *
     * @return 系统信息
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual SystemInfo getSystemInfo() = 0;
    
    /**
     * 获取网络状态
     *
     * @return 网络状态
     */
    virtual NetworkState getNetworkState() = 0;
    
    /**
     * 获取磁盘可用空间
     *
     * @return 磁盘可用空间，单位 bytes，返回-1表示无法获取。
     */
    virtual int64_t getDiskFreeSize() = 0;
    
    /**
     * SDK内部出现不可恢复错误，地图数据更新热切换出错可能会导致不可恢复错误，如果出现此错误，可以尝试一下方法：\n
     * 1、尝试重启进程，重新初始化SDK；
     * 2、重新集成地图数据，重新初始化SDK；
     */
    virtual void appearUnrecoverableError() = 0;
    
    /**
     * 获取自定义配置
     *
     * @return 配置内容，请联系高德产品同学沟通确认
     */
    
    virtual const char* getCustomConfig() {
        return "";
    }

    /**
     * 激活信息内容发送。此接口由SDK调用，SDK通过集成方设置的监听者实例来通知集成方激活信息变更，通知方式为事件触发。
     *
     * @param[in] info 激活信息，包括激活状态和激活范围
     *
     */
    virtual void receiveActivationInfo(const ActivationInfo& info)
    {
    }

    /**
     * IDC连接状态内容发送。此接口由SDK调用，SDK通过集成方设置的监听者实例来通知集成方IDC连接状态信息变更，通知方式为事件触发
     *
     * @param[in] connectionInfo  IDC连接状态信息，包括连接状态，断开状态及相关错误信息
     *
     */
    virtual void receiveIDCConnectionInfo(const IDCConnectionInfo& connectionInfo)
    {
    }
    
    /**
     * 设置系统状态监听者，由SDK设置给集成方，集成方通过监听者实例来通知SDK系统状态变更。
     *
     * @param[in] systemDeviceListener 系统状态监听者，生命周期由SDK管理，集成方无需要释放listener实例，使用前需要判断是否为NULL。
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual void setSystemDeviceListener(GHD_UNUSED ISystemDeviceListener *systemDeviceListener) {}
};

}
}
