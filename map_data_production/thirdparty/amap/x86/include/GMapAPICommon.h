/*
 * Copyright (C) 2019 - 2024 Amap Auto
 */
#pragma once

#include "GHDMapDefines.h"
#include <vector>
#include <string>
#include <memory>
#include <climits>

namespace hdmap {
namespace service {

static const uint64_t INVALID_TILE_ID = 0x7FFFFFFF7FFFFFFF;
static const uint64_t INVALID_ROAD_ID = 0;
static const uint64_t INVALID_ID = 0;
static const uint32_t INVALID_LON_INT = 181 * 1000000;
static const uint32_t INVALID_LAT_INT = 91 * 1000000;
static const double INVALID_LON_DOUBLE = 181.0;
static const double INVALID_LAT_DOUBLE = 91.0;

/**
 *  经纬度信息
 */
struct GHDMAP_API PositionInfo
{
    PositionInfo() : longitude(INVALID_LON_INT), latitude(INVALID_LAT_INT)
    {
    }

    PositionInfo(uint32_t lon, uint32_t lat) : longitude(lon), latitude(lat)
    {
    }

    uint32_t longitude;
    uint32_t latitude;
};

/**
 * link通行方向
 */
enum DIRECTION
{
    DIR_ONEWAY = 1,  ///< 单向
    DIR_BOTH = 2  ///< 双向
};

/**
 * 道路等级
 */
enum ROADCLASS
{
    ROADCLASS_FREEWAY = 0,  ///< 高速公路
    ROADCLASS_NATIONAL_ROAD = 1,  ///< 国道
    ROADCLASS_PROVINCE_ROAD = 2,  ///< 省道
    ROADCLASS_COUNTY_ROAD = 3,  ///< 县道
    ROADCLASS_RURAL_ROAD = 4,  ///< 乡公路
    ROADCLASS_IN_COUNTY_ROAD = 5,  ///< 县乡村内部道路
    ROADCLASS_CITY_SPEED_WAY = 6,  ///< 主要大街\城市快速道
    ROADCLASS_MAIN_ROAD = 7,  ///< 主要道路
    ROADCLASS_SECONDARY_ROAD = 8,  ///< 次要道路
    ROADCLASS_COMMON_ROAD = 9,  ///< 普通道路
    ROADCLASS_NON_NAVI_ROAD = 10,  ///< 非导航道路
    ROADCLASS_INVALID = 0xFF,  ///< 道路等级无效值
};

/**
 * 道路构成
 */
enum FORMOFWAY
{
    FORMWAY_DIVISED_LINK = 1,  ///< 上下线分离 主路
    FORMWAY_CROSS_LINK = 2,  ///< 交叉点内 复杂节点内部道路
    FORMWAY_JCT = 3,  ///< JCT
    FORMWAY_ROUND_CIRCLE = 4,  ///< 环岛
    FORMWAY_SERVICE_ROAD = 5,  ///< 服务区 辅助道路
    FORMWAY_SLIP_ROAD = 6,  ///< 引路 匝道
    FORMWAY_SIDE_ROAD = 7,  ///< 辅路
    FORMWAY_SLIP_JCT = 8,  ///< 引路 JCT
    FORMWAY_EXIT_LINK = 9,  ///< 出口
    FORMWAY_ENTRANCE_LINK = 10,  ///< 入口
    FORMWAY_TURN_RIGHT_LINEA = 11,  ///< 右转专用道
    FORMWAY_TURN_RIGHT_LINEB = 12,  ///< 右转专用道
    FORMWAY_TURN_LEFT_LINEA = 13,  ///< 左转专用道
    FORMWAY_TURN_LEFT_LINEB = 14,  ///< 左转专用道
    FORMWAY_COMMON_LINK = 15,  ///< 普通道路
    FORMWAY_TURN_LEFTRIGHT_LINE = 16,  ///< 左右转专用道
    FORMWAY_NONMOTORIZED_DRIVEWAY = 17,  ///< ADF+ 专用
    FORMWAY_FRONTDOOR_ROAD = 18,  ///< 门前道路
    FORMWAY_SERVICE_SLIP_ROAD = 19,  ///< 服务区 + 引路
    FORMWAY_SERVICE_JCT = 20,  ///< 服务区 + JCT
    FORMWAY_SERVICE_JCT_SLIP_ROAD = 21,  ///< 服务区 + 引路 + JCT
    FORMWAY_NON_VEHICLE = 22,  ///< 非机动车道路
    FORMWAY_INVALID = 0xFF  ///< 道路构成无效值
};

/**
 * 形点
 */
struct GHDMAP_API GEOMETRY
{
    GEOMETRY() : longitude(INVALID_LON_DOUBLE), latitude(INVALID_LAT_DOUBLE), height(0)
    {
    }

    virtual ~GEOMETRY()
    {
    }

    double longitude;
    double latitude;
    int height;
};

/**
 * 道路连接关系
 */
struct GHDMAP_API ConnectRoads
{
    virtual ~ConnectRoads()
    {
    }

    std::vector<uint64_t> fromRoadIds;  ///< 进入道路id
    std::vector<uint64_t> toRoadIds;  ///< 退出道路id
};

/**
 * 作用范围
 */
struct GHDMAP_API Range
{
    Range() : startoffset(0), endOffset(0)
    {
    }

    uint32_t startoffset;  ///< 开始offset，相对于所在中心线起点的偏移量cm
    uint32_t endOffset;  ///< 结束offset，相对于所在中心线起点的偏移量cm
};

/**
 * 时分信息
 */
struct GHDMAP_API TimeOfDay
{
    TimeOfDay() : hours(0), minutes(0)
    {
    }

    uint8_t hours;
    uint8_t minutes;
};

/**
 * 时分规制信息
 */
struct GHDMAP_API TimeRangeOfDay
{
    TimeRangeOfDay() : isLimited(false)
    {
    }

    TimeOfDay startTime;  ///< 规制起始时间
    TimeOfDay endTime;  ///< 规制结束时间
    bool isLimited;  ///< true是该规制时间内限行 false是规制时间外限行
};

/**
 * 星期规制类型
 */
enum DAYSOFWEEKTYPE : uint8_t
{
    DAYSOFWEEKTYPE_INVALID = (uint8_t)0u,
    DAYSOFWEEKTYPE_SUNDAY = (uint8_t)1u,
    DAYSOFWEEKTYPE_MONDAY = (uint8_t)1u << 1,
    DAYSOFWEEKTYPE_TUESDAY = (uint8_t)1u << 2,
    DAYSOFWEEKTYPE_WEDNESDAY = (uint8_t)1u << 3,
    DAYSOFWEEKTYPE_THURSDAY = (uint8_t)1u << 4,
    DAYSOFWEEKTYPE_FRIDAY = (uint8_t)1u << 5,
    DAYSOFWEEKTYPE_SATURDAY = (uint8_t)1u << 6
};

/**
 * 星期规制信息
 */
struct GHDMAP_API DaysOfWeek
{
    DaysOfWeek() : daysOfWeekType(DAYSOFWEEKTYPE_INVALID), isLimited(false)
    {
    }

    uint8_t daysOfWeekType;  ///< 日期规制信息 bit表达 见DAYSOFWEEKTYPE
    bool isLimited;  ///< true是该规制时间内限行 false是规制时间外限行
};

/**
 * 日期信息
 */
struct GHDMAP_API DayOfYear
{
    DayOfYear() : year(0), month(0), day(0)
    {
    }

    uint16_t year;
    uint8_t month;
    uint8_t day;
};

/**
 * 日期规制信息
 */
struct GHDMAP_API DateRangeOfYear
{
    DateRangeOfYear() : isLimited(false)
    {
    }

    DayOfYear startDay;  ///< 规制起始日期
    DayOfYear endDay;  ///< 规制结束日期
    bool isLimited;  ///< true是该规制时间内限行 false是规制时间外限行
};

/**
 * 车种规制类型
 */
enum VEHICLETYPE : uint64_t
{
    VEHICLETYPE_INVALID = (uint64_t)0u,  ///< 车种规制类型无效值
    VEHICLETYPE_PEDESTRIAN = (uint64_t)1u,  ///< Bit0  行人
    VEHICLETYPE_SPECIALVEHICLE = (uint64_t)1u << 1,  ///< Bit1  特殊车种
    VEHICLETYPE_PICKUPTRUCKS = (uint64_t)1u << 17,  ///< Bit17 皮卡
    VEHICLETYPE_ELECTRICBICYCLE = (uint64_t)1u << 18,  ///< Bit18 电动自行车
    VEHICLETYPE_CARRYDANGEROUS = (uint64_t)1u << 19,  ///< Bit19 危险品运输车辆
    VEHICLETYPE_MOTORBIKE = (uint64_t)1u << 20,  ///< Bit20 摩托车（4轮以下）
    VEHICLETYPE_BICYCLE = (uint64_t)1u << 21,  ///< Bit21 自行车/人力车
    VEHICLETYPE_TAXI = (uint64_t)1u << 22,  ///< Bit22 出租车
    VEHICLETYPE_BUS = (uint64_t)1u << 23,  ///< Bit23 公交车
    VEHICLETYPE_LARGEBUS = (uint64_t)1u << 24,  ///< Bit24 大型客车
    VEHICLETYPE_MINIBUS = (uint64_t)1u << 25,  ///< Bit25 小型客车
    VEHICLETYPE_TRAILER = (uint64_t)1u << 26,  ///< Bit26 拖/挂车
    VEHICLETYPE_BIGTRUCK = (uint64_t)1u << 27,  ///< Bit27 大卡/货车
    VEHICLETYPE_MINITRUCK = (uint64_t)1u << 28,  ///< Bit28 小型卡/货车
    VEHICLETYPE_MINICAR = (uint64_t)1u << 29,  ///< Bit29 微型车
    VEHICLETYPE_CAR = (uint64_t)1u << 30,  ///< Bit30 小轿车
    VEHICLETYPE_ALL = (uint64_t)1u << 31  ///< Bit31 全部车辆
};

/**
 * 车种规制信息
 */
struct GHDMAP_API Vehicle
{
    Vehicle() : type(VEHICLETYPE_INVALID), isLimited(false)
    {
    }

    uint64_t type;  ///< 车种规制信息 bit表达 见VEHICLETYPE
    bool isLimited;  ///< true是该车种限行 false是这些车种外限行
};

/**
 * 天气规制类型
 */
enum WEATHERRULETYPE : uint8_t
{
    WEATHERTYPE_INVALID = (uint8_t)0u,  ///<天气类型无效值
    WEATHERTYPE_SUNSHINE = (uint8_t)1u,  ///< Bit0：晴天
    WEATHERTYPE_RAINSNOW = (uint8_t)1u << 1,  ///< Bit1：雨(雪)天
    WEATHERTYPE_ICE = (uint8_t)1u << 2,  ///< Bit2：路面结冰
    WEATHERTYPE_FOG = (uint8_t)1u << 3,  ///< Bit3：雾天
    WEATHERTYPE_WIND = (uint8_t)1u << 4  ///< Bit4：风
};

/**
 * 天气规制信息
 */
struct GHDMAP_API Weather
{
    Weather() : type(WEATHERTYPE_INVALID), isLimited(false)
    {
    }

    uint8_t type;  ///< 天气规制信息 bit表达 见WEATHERRULETYPE
    bool isLimited;  ///< true是该天气限行 false是除该天气外限行
};

/**
 * 季节类型
 */
enum SEASONTYPE : uint8_t
{
    SEASONTYPE_INVALID = (uint8_t)0u,  ///<季节类型无效值
    SEASONTYPE_SPRING = (uint8_t)1u,  ///< Bit0：春季
    SEASONTYPE_SUMMER = (uint8_t)1u << 1,  ///< Bit1：夏季
    SEASONTYPE_FALL = (uint8_t)1u << 2,  ///< Bit2：秋季
    SEASONTYPE_WINTER = (uint8_t)1u << 3,  ///< Bit3：冬季
    SEASONTYPE_RAINY = (uint8_t)1u << 4,  ///< Bit4：雨季/汛期
    SEASONTYPE_DRY = (uint8_t)1u << 5  ///< Bit5：干季
};

/**
 * 季节规制信息
 */
struct GHDMAP_API Season
{
    Season() : type(SEASONTYPE_INVALID), isLimited(false)
    {
    }

    uint8_t type;  ///< 季节规制信息 bit表达 见SEASONTYPE
    bool isLimited;  ///< true是该季节限行 false是除该季节外限行
};

/**
 * 节假日类型
 */
enum HOLIDAYTYPE
{
    HOLIDAYTYPE_NONE = 0,  ///< 未设定
    HOLIDAYTYPE_VALID = 1,  ///< 节假日有交通限制
    HOLIDAYTYPE_EXCEPT = 2  ///< 有交通限制但节假日除外
};

/**
 * 节假日规制信息
 */
struct GHDMAP_API Holiday
{
    Holiday() : type(HOLIDAYTYPE_NONE), isLimited(false)
    {
    }

    HOLIDAYTYPE type;  ///< 节假日规制信息
    bool isLimited;
};

/**
 * hov车道规制信息
 */
struct GHDMAP_API HovNum
{
    HovNum() : count(0), isLimited(false)
    {
    }
    uint32_t count;  ///< HOV车道限制通行人数
    bool isLimited;  ///< true是小于该人数禁行
};

/**
 * 车牌类型
 */
enum LICENSEPLATETYPE
{
    LICENSEPLATETYPE_LOCALFOREIGN = 0,  ///< 本埠 + 外埠
    LICENSEPLATETYPE_FOREIGN = 1,  ///< 外埠
    LICENSEPLATETYPE_LOCAL = 2  ///< 本埠
};

/**
 * 车牌规制信息
 */
struct GHDMAP_API LicensePlate
{
    LicensePlate() : type(LICENSEPLATETYPE_LOCALFOREIGN), isLimited(false)
    {
    }

    LICENSEPLATETYPE type;  ///< 车牌规制信息
    bool isLimited;
};

/**
 * 规制信息结构
 */
struct GHDMAP_API LimitInfo
{
    LimitInfo() :
        timeRangeOfDay(NULL),
        dateRangeOfYear(NULL),
        daysOfWeek(NULL),
        vehicle(NULL),
        weather(NULL),
        season(NULL),
        holiday(NULL),
        hovNum(NULL),
        licensePlate(NULL),
        sourceType(0)
    {
    }

    std::shared_ptr<TimeRangeOfDay> timeRangeOfDay;  ///< 时间规制
    std::shared_ptr<DateRangeOfYear> dateRangeOfYear;  ///< 时间规制
    std::shared_ptr<DaysOfWeek> daysOfWeek;  ///< 时间规制
    std::shared_ptr<Vehicle> vehicle;  ///< 车种规制
    std::shared_ptr<Weather> weather;  ///< 天气规制
    std::shared_ptr<Season> season;  ///< 季节规制
    std::shared_ptr<Holiday> holiday;  ///< 节假日规制
    std::shared_ptr<HovNum> hovNum;  ///< HOV车道人数规制
    std::shared_ptr<LicensePlate> licensePlate;  ///< 车牌规制
    Range range;  ///< 作用范围
    int32_t sourceType;  ///< 规制信息来源类型
};

/**
 * 限速规制信息
 */
struct GHDMAP_API SpeedLimitInfo : public LimitInfo
{
    SpeedLimitInfo() : speed(0)
    {
    }

    uint32_t speed;  ///< 限速值，单位 km/h
};

}  // namespace service
}  // namespace hdmap
