/*
 * Copyright (C) 2019 - 2024 Amap Auto
 */
#pragma once
#include <vector>
#include "GMapAPICommon.h"

namespace hdmap {
namespace service {

static const int64_t ROUTE_INVALID_NUMBER = -1;
static const int32_t ROUTE_INVALID_INDEX = -1;

/**
 * 关键点类型
 */
enum ROUTEPOINTTYPE
{
    START_POINT = 0,  ///< 起点
    END_POINT = 1,  ///< 终点
    WAY_POINT = 2,  ///< 途经点
    NONE_POINT = 3  ///< 未知
};

/**
 * 导航状态
 */
enum ROUTESTATUS
{
    ROUTESTATUS_UNKNOWN = 0,
    ROUTESTATUS_FIRSTROUTE = 1,  ///< 导航路径开始
    ROUTESTATUS_NONFIRSTROUTE = 2,  ///< 导航路径变更
    ROUTESTATUS_NAVIFINISH = 3,  ///< 导航路径结束
    ROUTESTATUS_OFFLINEROUTE = 7,  ///< 离线导航路径
    ROUTESTATUS_SUCCESS = 255  ///< mapping 成功
};

/**
 * 原始导航关键点信息
 */
struct GHDMAP_API OriginalRoutePoint
{
    OriginalRoutePoint() : number(ROUTE_INVALID_NUMBER), type(NONE_POINT), index(ROUTE_INVALID_INDEX)
    {
    }

    OriginalRoutePoint(ROUTEPOINTTYPE tp, int32_t idx, uint32_t lon, uint32_t lat, int32_t num = ROUTE_INVALID_NUMBER) :
        number(num), type(tp), index(idx), pos(lon, lat)
    {
    }

    int32_t number;  ///< 途径点的序号信息，起点和终点为无效
    ROUTEPOINTTYPE type;  ///<  起点/终点/途经点
    int32_t index;  ///< 关键点在局部路径中的索引信息
    PositionInfo pos;  ///< 经纬度信息
};

/**
 * 原始导航信息
 */
struct GHDMAP_API OriginalRoute
{
    std::vector<OriginalRoutePoint> points;  ///< 起点/终点/途经点信息
    std::vector<uint64_t> ids;  ///< 导航路径序列
};

}  // namespace service
}  // namespace hdmap
