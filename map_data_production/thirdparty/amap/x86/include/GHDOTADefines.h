/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDMapDefines.h"

namespace hdmap {
namespace service {

/**
 * 数据OTA监听者
 */
class GHDMAP_API IDOTAListener {
public:
    virtual ~IDOTAListener() {}
    
    /**
     *  设置是否允许数据热更新，该接口是异步接口，同步接口参考接口isHotSwapEnabled。\n
     *  当通过IDOTAReceiver::receiveDOTAStatus收到数据更新状态1100时，则可以通过此接口打开热切换\n
     *  打开后可以通过IDOTAReceiver::receiveDOTAStatus收到数据更新状态1200。
     *
     * @param[in] enabled true:允许数据更新热切换, false:不允许数据更新热切换
     */
    virtual void setHotSwapEnabled(bool enabled) = 0;
    
    /**
     *  有数据需要更新前，当SDK通过接口confirmDOTAUpdateAuthorization 询问集成方是否可以更新，集成方通过该接口进行更新授权。
     *
     *  @param[in] enabled 更新开关 true 允许更新， false: 不允许更新
     */
    virtual void setDOTAUpdateEnabled(bool enabled) = 0;
};

/**
 * 数据更新状态接收者，由集成方设置给SDK
 */
class GHDMAP_API IDOTAReceiver {
public:
    virtual ~IDOTAReceiver() {}
    
    /**
     * 数据进行热更新前，会调用此接口，由集成方通过返回值来控制热切换。\n
     * 该接口是同步接口，异步接口参考接口setHotSwapEnabled。\n
     *
     * @return true: 允许热切换, false: 不允许热切换
     */
    virtual bool isHotSwapEnabled() = 0;
    
    /**
     * 接收数据更新状态接口
     *
     * @param[in] status 数据更新状态
     *
     * 状态码说明：\n
     *   1    空闲状态\n
     * 500    开始下载数据包\n
     * 501    下载数据包成功\n
     * 700    开始合并数据\n
     * 701    合并数据完成\n
     * 1000   开始启动切换\n
     * 1001   启动切换成功\n
     * 1002   启动切换失败\n
     * 1100   开始准备热切换\n
     * 1102   准备热切换失败\n
     * 1200   开始热切换\n
     * 1201   热切换成功\n
     * 1202   热切换失败\n
     * 1400   开始清理数据\n
     * 1401   结束清理数据\n
     * 5000   当前城市不是最新版本\n
     * 5001   当前城市是最新版本\n
     */
    virtual void receiveDOTAStatus(uint32_t status) = 0;
    
    /**
     *  授权地图数据更新，可以使用IDOTAListener::setDOTAUpdateEnabled()接口实现授权。
     */
    virtual void confirmDOTAUpdateAuthorization() {
    }
    
    /**
     * 设置数据更新监听者，由SDK设置给集成方。
     *
     * @param[in] listener 数据更新监听者，此实例的生命周期由SDK管理，使用前需要判空。
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual void setDOTAListener(GHD_UNUSED IDOTAListener *listener) {}
};

}
}
