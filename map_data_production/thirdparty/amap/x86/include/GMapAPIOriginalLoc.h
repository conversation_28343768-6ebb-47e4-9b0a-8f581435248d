/*
 * Copyright (C) 2019 - 2024 Amap Auto
 */
#pragma once

namespace hdmap {
namespace service {

struct GHDMAP_API Datetime
{
    Datetime() :
        year(0),  // 0为无效值
        month(0),
        day(0),
        hour(0),
        minute(0),
        second(0),
        milliSec(0)
    {
    }

    Datetime(const Datetime& dateTime)
    {
        year = dateTime.year;
        month = dateTime.month;
        day = dateTime.day;
        hour = dateTime.hour;
        minute = dateTime.minute;
        second = dateTime.second;
        milliSec = dateTime.milliSec;
    }

    int16_t year; /**< 四位数年份*/
    int8_t month; /**< 月份，数值范围：1-12*/
    int8_t day; /**< 日，数值范围：1-31*/
    int8_t hour; /**< 时，数值范围：0-23*/
    int8_t minute; /**< 分，数值范围：0-59*/
    int8_t second; /**< 秒，数值范围：0-59*/
    int16_t milliSec; /**< 毫秒，数值范围：0-999。*/
};

/**
 * Original定位信息结构
 */
struct GHDMAP_API OriginalLocInfo
{
    static const uint32_t INVALID_OFFSET = 0xFFFFFFFF;  // offset无效值
    static const uint32_t INVALID_REGION_CODE = 0;  // regionCode无效值

    OriginalLocInfo() : roadId(INVALID_ROAD_ID), offset(INVALID_OFFSET), regionCode(INVALID_REGION_CODE)
    {
    }

    OriginalLocInfo(uint64_t roadId, uint32_t offset, uint32_t regionCode, Datetime datetime) :
        roadId(roadId), offset(offset), regionCode(regionCode), gpsDatetime(datetime)
    {
    }

    uint64_t roadId;  ///< 定位点所在的roadId
    uint32_t offset;  ///< 定位点在roadId上的偏移Offset, 单位cm
    uint32_t regionCode;  ///< 定位点所在的全局城市码
    Datetime gpsDatetime;  ///< gps时间
};

}  // namespace service
}  // namespace hdmap
