/*
 * Copyright (C) 2019 - 2024 Amap Auto
 */
#pragma once

#include "GMapAPICommon.h"
namespace hdmap {
namespace service {

static const uint32_t CURVATURE_UNKNOWN_VALUE = 1023;
static const uint16_t INVALID_ANGLE_VALUE = 0xFFFF;
static const uint16_t INVALID_REGION_CODE = 0;

/**
 * 道路曲率值
 */
struct GHDMAP_API PointCurvature
{
    PointCurvature() : offset(0), value(CURVATURE_UNKNOWN_VALUE)
    {
    }

    uint32_t offset;  ///< 距离Road起点的Offset
    uint32_t value;  ///< 曲率值
};

/**
 * 道路电子眼类型
 */
enum ECAMERA_TYPE
{
    ECAMERA_SPEED = 0,  ///< 测速摄像头、测速雷达
    ECAMERA_MONITOR = 1,  ///< 监控摄像头
    ECAMERA_TRAFIC_LIGHT = 2,  ///< 闯红灯照相
    ECAMERA_RULE = 3,  ///< 违章摄像头*/,
    ECAMERA_BUS = 4,  ///< 公交专用道摄像头
    ECAMERA_EMERGENCY = 5,  ///< 应急车道
    ECAMERA_NON_MOTORIZED = 6,  ///< 非机动车道
    ECAMERA_RANGE = 7,  ///< 区间测速
    ECAMERA_INTER_SPEED_START = 8,  ///< 区间测速起始
    ECAMERA_INTER_SPEED_END = 9,  ///< 区间测速解除
    ECAMERA_UNKNOWN = 255,  ///< 未知
};

/**
 * 道路电子眼信息
 */
struct GHDMAP_API Camera
{
    Camera() : offset(0), type(ECAMERA_UNKNOWN), value(0)
    {
    }

    Camera(uint32_t offt, ECAMERA_TYPE tp, uint32_t val) : offset(offt), type(tp), value(val)
    {
    }

    uint32_t offset;  ///< 距离Road起点的Offset
    ECAMERA_TYPE type;  ///< 电子眼类型
    uint32_t value;  ///< 电子眼限速值
};

/**
* 时间信息
*/
struct GHDMAP_API TimeRegulation
{
    TimeRegulation() : week(0)
    {
    }

    TimeOfDay day;      // 时分信息: hour, min
    uint8_t week;    //星期信息: 默认值0, 有效值1-7:周一至周日
    DayOfYear year;     // 日期信息: year, month, day
};

/**
* 时间规制信息
*/
struct GHDMAP_API TimeRegulationRange
{
    TimeRegulationRange() : isLimited(false)
    {
    }

    TimeRegulation startTime;  ///< 规制起始时间
    TimeRegulation endTime;    ///< 规制结束时间
    bool isLimited;       ///< true是该规制时间内限行 false是规制时间外限行
};

/**
* 道路设施类型枚举
*/
enum FACILITY_TYPE
{
    FACILITY_LEFT_INTER_FLOW = 1,  ///< 左侧合流
    FACILITY_RIGHT_INTER_FLOW = 2,  ///< 右侧合流
    FACILITY_REVERSE_TURN = 4,  ///< 反向转弯
    FACILITY_LINKING_TURN = 5,  ///< 连续转弯
    FACILITY_ACCIDENT_AREA = 6,  ///< 事故多发地
    FACILITY_RAILWAY_CROSS = 8,  ///< 铁路道口
    FACILITY_SLIPPERY = 9,  ///< 易滑
    FACILITY_MAX_SPEED_LIMIT = 10,  ///< 最大限速标志
    FACILITY_MIN_SPEED_LIMIT = 11,  ///< 最小限速标志
    FACILITY_VILLAGE = 12,  ///< 村庄
    FACILITY_LEFT_NARROW = 13,  ///< 左侧变窄
    FACILITY_RIGHT_NARROW = 14,  ///< 右侧变窄
    FACILITY_DOUBLE_NARROW = 15,  ///< 两侧变窄
    FACILITY_CROSS_WIND_AREA = 16,  ///< 横风区
    FACILITY_SCHOOL = 17,  ///< 前方学校
    FACILITY_NOPASSING = 18,  ///< 禁止超车
    FACILITY_NARROW_BRIDGE = 19,  ///< 窄桥
    FACILITY_AROUND_THE_LINE = 20,  ///< 左右绕行
    FACILITY_LEFT_BYPASS = 21,  ///< 左侧绕行
    FACILITY_RIGHT_BYPASS = 22,  ///< 右侧绕行
    FACILITY_LEFT_MOUNTAIN_ROAD = 23,  ///< 傍山险路（左侧）
    FACILITY_RIGHT_MOUNTAIN_ROAD = 24,  ///< 傍山险路（右侧）
    FACILITY_ONSTEEPLOPE = 25,  ///< 上陡坡
    FACILITY_UNDERSTEEPLOPE = 26,  ///< 下陡坡
    FACILITY_OVER_WATER_ROAD = 27,  ///< 过水路面
    FACILITY_UNEVEN_ROAD = 28,  ///< 路面不平
    FACILITY_STROLL = 29,  ///< 慢行
    FACILITY_ATTENTION_TO_DANGER = 30,  ///< 注意危险
    FACILITY_CROSSWALK = 31,  ///< 人行横道
    FACILITY_RIVER_BANK_LEFT = 33,  ///< 堤坝路A(左侧)
    FACILITY_RIVER_BANK_RIGHT = 34,  ///< 堤坝路B(右侧)
    FACILITY_CHILDREN = 35,  ///< 注意儿童
    FACILITY_CYCLISTS = 36,  ///< 注意非机动车
    FACILITY_DOMESTIC_ANIMALS_CROSSING = 37,  ///< 注意牲畜穿越
    FACILITY_GIVE_WAY = 38,  ///< 减速让行
    FACILITY_STOP = 39,  ///< 停车让行
    FACILITY_PRIORITY_FOR_ONCOMING_TRAFFIC = 40,  ///< 会车让行
    FACILITY_PRIORITY_OVER_ONCOMING_TRAFFIC = 41,  ///< 会车先行
    FACILITY_HUMPBACK_BRIDGE = 42,  ///< 驼峰桥
    FACILITY_END_OF_SPEED_LIMIT = 43,  ///< 限速结束
    FACILITY_END_OF_PROHIBITION_ON_OVERTAKING = 44,  ///< 禁止超车结束
    FACILITY_RAILWAY_CROSS_EXTEND = 50,  ///< 铁道路口
    FACILITY_RAILWAY_WITH_GATES = 51,  ///< 有人看管的铁道路口
    FACILITY_RAILWAY_WITHOUT_GATES = 52,  ///<  无人看管的铁道路口
    FACILITY_VARIABLE_SPEED = 53,  ///< 可变限速牌标志,
    FACILITY_MAX_SPEED_LIMIT_WITH_RULEINFO = 54,  ///< 带规制信息的最大限速标志
    FACILITY_PEDESTRIANS = 56,  ///< 注意行人
    FACILITY_UNKNOWN = 255  ///< 无效值
};

enum FACILITY_SCENETYPE
{
    FACILITY_SCENE_TYPE_UNKNOWN = 0,
    FACILITY_SCENE_TYPE_RAMP_SPEED_LIMIT = 2
};

/**
 * 道路设施-TrafficSgin信息
 */
struct GHDMAP_API Facility
{
    Facility() :
        offset(0),
        type(FACILITY_UNKNOWN),
        sceneType(FACILITY_SCENE_TYPE_UNKNOWN),
        value(0),
        weather(nullptr)
    {
    }

    uint32_t offset;  ///< 距离Road起点的Offset
    FACILITY_TYPE type;  ///< 道路设施类型
    FACILITY_SCENETYPE sceneType;  ///< 场景类型
    uint32_t value;    ///< 限速值
    std::vector<TimeRegulationRange> time; ///< 时间规制
    std::shared_ptr<Weather> weather;   ///< 天气规制
};



/**
 * 检查站信息
 */
struct GHDMAP_API CheckPoint
{
    CheckPoint() : offset(0)
    {
    }

    uint32_t offset;  ///< 距离road起点的偏移量，单位cm
};

/**
 * 路权定义
 */
enum OWNERSHIP
{
    OWNERSHIP_PUBLIC = 0,  ///< 公众道路
    OWNERSHIP_INNER = 1,  ///< 内部道路
    OWNERSHIP_PRIVATE = 2,  ///< 私有道路
    OWNERSHIP_UNKNOWN = 255  ///< 未知类型
};

/**
 * 长实线
 */
struct GHDMAP_API LongSolidLine
{
    virtual ~LongSolidLine()
    {
    }

    std::vector<uint64_t> effectRoadIds;  ///< 影响的roadId列表
    std::vector<uint32_t> validLanes;  ///< 可走车道
};

/**
 * 补班日规制类型
 */
enum WORKHOLIDAYTYPE {
    WORKHOLIDAYTYPE_NONE = 0,   ///< 未设定
    WORKHOLIDAYTYPE_VALID = 1,  ///< 补班日生效
    WORKHOLIDAYTYPE_INVALID = 2,  ///< 补班日不生效
    WORKHOLIDAYTYPE_WEEKEND = 3  ///< 补班日按周末生效
};

/**
 * 公交车道信息
*/
struct GHDMAP_API BusLaneInfo {
    BusLaneInfo() :
        offset(0),
        holiday(HOLIDAYTYPE_NONE),
        workHoliday(WORKHOLIDAYTYPE_NONE)
    {
    }
    
    uint32_t offset;   ///< 距离Road起点的Offset
    std::vector<uint8_t> onLanes; //< 从左往右，[0,0,1]
    std::vector<TimeRegulationRange> time; ///< 时间规制
    HOLIDAYTYPE holiday;   ///< 节假日规制
    WORKHOLIDAYTYPE workHoliday;   ///< 补班日规制
};
/**
 * 前/背景车道类型
 */
enum LANE_DIRECT_TYPE {
    LANE_DIRECT_TYPE_NORMAL = 0,  ///< 普通车道
    LANE_DIRECT_TYPE_BUS = 1,  ///< 公交车专用车道
    LANE_DIRECT_TYPE_OTHER_SPECIAL = 2, ///< 其他专用车道
    LANE_DIRECT_TYPE_REVERSIBLE = 3, ///< 潮汐车道
    LANE_DIRECT_TYPE_VARIABLE = 4 ///< 可变车道
};

/**
 * 箭头类型
 */
enum ARROW_TYPE: uint32_t
{
    ARROW_TYPE_INVALID = (uint32_t)0u,     ///< 无效值
    ARROW_TYPE_LEFTUTURN = (uint32_t)1u << 0,     ///< bit0:左掉头
    ARROW_TYPE_TURNLEFT = (uint32_t)1u << 1,     ///< bit1:左转
    ARROW_TYPE_STRAIGHT = (uint32_t)1u << 2,     ///< bit2:直行
    ARROW_TYPE_TURNRIGHT = (uint32_t)1u << 3,     ///< bit3:右转
    ARROW_TYPE_RIGHTUTURN = (uint32_t)1u << 4,     ///< bit4:右掉头
    ARROW_TYPE_LEFT_SIDE_WIDTH_CHANGE = (uint32_t)1u << 20,     ///< bit20:左侧车道变窄
    ARROW_TYPE_RIGHT_SIDE_WIDTH_CHANGE = (uint32_t)1u << 21,     ///< bit21:右侧车道变窄
    ARROW_TYPE_SINGLE_LANE_NO_UTURN = (uint32_t)1u << 22,     ///< bit22:单车道禁止掉头
    ARROW_TYPE_LEFT_CURVATURE = (uint32_t)1u << 23,     ///< bit23:左歪
    ARROW_TYPE_RIGHT_CURVATURE = (uint32_t)1u << 24,     ///< bit24:右歪
    ARROW_TYPE_EMPTY_LANE = (uint32_t)1u << 25     ///< bit25:空车道
};

/**
 * 车道箭头信息
 */
struct GHDMAP_API LaneArrow {
    LaneArrow() :
        arrowType(ARROW_TYPE_INVALID),
        holiday(HOLIDAYTYPE_NONE),
        workHoliday(WORKHOLIDAYTYPE_NONE)
    {
    }

    uint32_t arrowType; ///< 箭头类型 bit表示，见ARROW_TYPE
    std::vector<TimeRegulationRange> time; ///< 时间规制
    HOLIDAYTYPE holiday;   ///< 节假日规制
    WORKHOLIDAYTYPE workHoliday;   ///< 补班日规制
};

/**
 * 背景车道信息
 */
struct GHDMAP_API BackLaneInfo {
    BackLaneInfo() :
        number(0),
        laneType(LANE_DIRECT_TYPE_NORMAL)
    {
    }

    uint32_t number; ///< 车道编号：从1开始编号，无效值为0
    LANE_DIRECT_TYPE laneType; ///< 车道类型
    std::vector<LaneArrow> arrows; ///< 车道箭头信息
};

/**
 * 前景车道信息
 */
struct GHDMAP_API FrontLane {
    FrontLane() :
        number(0),
        laneType(LANE_DIRECT_TYPE_NORMAL)
    {
    }

    uint32_t number; ///< 车道编号：从1开始编号，无效值为0
    LANE_DIRECT_TYPE laneType; ///< 车道类型
    std::vector<LaneArrow> arrows; ///< 车道箭头信息和规制信息
};

struct GHDMAP_API FrontLaneInfo {
    FrontLaneInfo() :
        toRoadId(INVALID_ROAD_ID)
    {
    }

    uint64_t toRoadId; ///< 退出道路ID
    std::vector<FrontLane> lanes;  ///< 背景车道信息
};


struct GHDMAP_API RoadPackage
{
    RoadPackage() :
        roadId(INVALID_ROAD_ID),
        length(0),
        direction(DIR_ONEWAY),
        roadClass(ROADCLASS_INVALID),
        formWay(FORMWAY_INVALID),
        angleF(INVALID_ANGLE_VALUE),
        angleT(INVALID_ANGLE_VALUE),
        linkType(0),
        historyAverageSpeed(0),
        hasToll(false),
        hasTrafficLight(false),
        plns(0),
        ownership(OWNERSHIP_UNKNOWN),
        regionCode(INVALID_REGION_CODE)
    {
    }

    uint64_t roadId;
    uint32_t length;  ///< 道路长度cm
    DIRECTION direction;  ///< 道路方向
    ROADCLASS roadClass;  ///< 道路等级
    FORMOFWAY formWay;  ///< 道路构成
    uint16_t angleF;  ///< 道路起始角度
    uint16_t angleT;  ///< 道路结束角度
    uint16_t linkType;  ///< 道路类型 bit表示，见LINKTYPE
    uint16_t historyAverageSpeed;  ///< 道路历史经验速度
    bool hasToll;  ///< 退出交叉点上是否有收费站
    bool hasTrafficLight;  ///< 退出交叉点上是否有红绿灯
    uint8_t plns;  ///< 道路的正向车道数
    OWNERSHIP ownership;  ///< 路权信息
    uint16_t regionCode;  ///< road所在城市码
    std::vector<GEOMETRY> geometries;  ///< 道路形点
    std::vector<PointCurvature> curvatures;  ///< 道路曲率
    std::vector<uint8_t> arrowMarkings;  ///< 道路箭头信息
    std::vector<Camera> cameras;  ///< 电子眼列表
    std::vector<Facility> facilities;  ///< 道路设施列表
    std::vector<SpeedLimitInfo> speedLimitInfos;  ///< 道路最大限速信息
    std::vector<SpeedLimitInfo> derivedSpeedLimitInfos;  ///< 推导最大限速信息
    ConnectRoads connectRoads;  ///< 进入和退出节点关联的link
    std::vector<CheckPoint> checkPoints;  ///< 检查站列表
    std::vector<LongSolidLine> longSolidLines;  ///< 长实线
    std::vector<BusLaneInfo> busLaneInfos;  ///< 公交车道信息
    std::vector<BackLaneInfo> backLanes;  ///< 背景车道信息
    std::vector<FrontLaneInfo> frontLanes;  ///< 前景车道信息
};

}  // namespace service
}  // namespace hdmap
