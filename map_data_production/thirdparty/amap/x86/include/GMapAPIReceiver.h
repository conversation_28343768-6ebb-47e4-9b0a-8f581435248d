/*
 * Copyright (C) 2019 - 2024 Amap Auto
 */
#pragma once
#include "GHDMapDefines.h"
#include "GMapAPIRoute.h"
#include "GMapAPIRoadPackage.h"
#include "GMapAPIOriginalLoc.h"
#include "GMapAPIOriginalDynamic.h"
#include <vector>
#include <list>
#include <string>

namespace hdmap {
namespace service {
/**
 * mapapi消息对外通知接口
 */
class GHDMAP_API IMapAPIReceiver
{
public:
    virtual ~IMapAPIReceiver()
    {
    }

    /**
     * 接收静态数据删除信息接口
     * @param[in] cyclicCounter 删除数据消息标识，每次播发+1，在重置后清0
     * @param[in] roadIds 拖尾删除的roadId
     */
    virtual void receiveDeleteRoadIds(const uint32_t cyclicCounter, const std::vector<uint64_t>& roadIds)
    {
    }

    /**
     * 接收静态数据接口
     * @param[in] cyclicCounter 静态数据消息标识，每次播发+1，在重置后清0
     * @param[in] roadPackages 静态Road数据
     */
    virtual void receiveRoadPackages(const uint32_t cyclicCounter, const std::vector<RoadPackage>& roadPackages)
    {
    }

    /**
     * 接收额外的导航信息接口
     *
     * @param[in] cyclicCounter 导航路径消息标识，每次播发+1，在重置后清0
     * @param[in] routeId 导航路径唯一标识
     * @param[in] status  导航路径状态
     * @param[in] route  导航路径详细信息
     *
     */
    virtual void receiveNaviRoute(
        const uint32_t cyclicCounter, const std::string& routeId, ROUTESTATUS status, const OriginalRoute& route)
    {
    }

    /**
     * 接收定位信号接口
     *
     * @param[in] cyclicCounter 消息标识，每次播发+1
     * @param[in] locInfos 定位点
     *
     */
    virtual void receiveLocInfo(const uint32_t cyclicCounter, const std::vector<OriginalLocInfo>& locInfos)
    {
    }

    /**
     * 接收动态数据接口
     * @param[in] cyclicCounter 消息标识，每次播发+1，在重置后清0
     * @param[in] dynamicDatas 动态信息数据集合
     */
    virtual void receiveDynamicData(const uint32_t cyclicCounter, const std::vector<OriginalDynamicData>& dynamicDatas)
    {
    }

    /**
     * 接收删除动态数据接口
     * @param[in] cyclicCounter 消息标识，每次播发+1，在重置后清0
     * @param[in] dynamicDataIds 需要删除的动态信息数据ID集合
     */
    virtual void receiveDeletedDynamicData(
        const uint32_t cyclicCounter, const std::vector<OriginalDynamicDataId>& dynamicDataIds)
    {
    }
};

}  // namespace service
}  // namespace hdmap
