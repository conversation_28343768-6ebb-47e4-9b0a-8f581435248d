/*
 * Copyright (C) 2019 - 2024 Amap Auto
 */
#pragma once

namespace hdmap {
namespace service {

/**
 * 动态信息状态
 */
enum ORIGINALDYNAMICSTATUS
{
    ORIGINAL_DYNAMIC_STATUS_UNKOWN = 0,  ///< 未知
    ORIGINAL_DYNAMIC_STATUS_CREATE = 1,  ///< 新增
    ORIGINAL_DYNAMIC_STATUS_UPDATE = 2,  ///< 更新
    ORIGINAL_DYNAMIC_STATUS_DELETE = 3,  ///< 删除
};

/**
 * 交通流状态
 */
enum ORIGINALTRAFFICFLOWSTATUS
{
    ORIGINALTRAFFICFLOWSTATUS_UNKNOWN = 0,  ///< 未知
    ORIGINALTRAFFICFLOWSTATUS_SMOOTH = 1,  ///< 畅通
    ORIGINALTRAFFICFLOWSTATUS_AMBLE = 2,  ///< 缓行
    ORIGINALTRAFFICFLOWSTATUS_CONGESTION = 3,  ///< 拥堵
    ORIGINALTRAFFICFLOWSTATUS_BLOCKAGE = 4,  ///< 堵塞
    ORIGINALTRAFFICFLOWSTATUS_NO = 5,  ///< 无交通流
    ORIGINALTRAFFICFLOWSTATUS_OTHER = 99,  ///< 其他
};

/**
 * 动态信息Id
 */
struct GHDMAP_API OriginalDynamicDataId
{
    OriginalDynamicDataId() : roadId(0)
    {
    }

    OriginalDynamicDataId(const OriginalDynamicDataId& other) : roadId(other.roadId)
    {
    }

    uint64_t roadId;  ///< 道路Id
};

/**
 * 交通流动态信息
 */
struct GHDMAP_API OriginalTrafficFlowDynamicAttr
{
    OriginalTrafficFlowDynamicAttr() :
        status(ORIGINAL_DYNAMIC_STATUS_UNKOWN), trafficFlowStatus(ORIGINALTRAFFICFLOWSTATUS_UNKNOWN)
    {
    }

    OriginalTrafficFlowDynamicAttr(const OriginalTrafficFlowDynamicAttr& other) :
        status(other.status), trafficFlowStatus(other.trafficFlowStatus)
    {
    }

    ORIGINALDYNAMICSTATUS status;
    ORIGINALTRAFFICFLOWSTATUS trafficFlowStatus;  ///< 交通流状态
};

/**
 * Original动态数据
 */
struct GHDMAP_API OriginalDynamicData
{
    OriginalDynamicData() : dynamicDataId()
    {
    }

    OriginalDynamicData(const OriginalDynamicData& other) :
        dynamicDataId(other.dynamicDataId), trafficFlows(other.trafficFlows)
    {
        *this = other;
    }

    OriginalDynamicDataId dynamicDataId;  ///< Dynamic data id
    std::vector<OriginalTrafficFlowDynamicAttr> trafficFlows;  ///< 交通流信息
};

}  // namespace service
}  // namespace hdmap
