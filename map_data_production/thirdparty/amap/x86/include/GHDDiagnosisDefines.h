/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDMapDefines.h"

namespace hdmap {
namespace service {

/**
 * 诊断类型
 */
enum DiagnosisType {
    DIAGNOSIS_TYPE_SYSTEM_FAULT = 1,              ///< 系统错误
    DIAGNOSIS_TYPE_UID_FAULT = 2,                 ///< 获取系统UID失败
    DIAGNOSIS_TYPE_ROM_ERROR,                     ///< 系统磁盘空间不足
    DIAGNOSIS_TYPE_PATH_ERROR,                    ///< 高快地图数据路径错误
    DIAGNOSIS_TYPE_FILE_AUTH_FAULT,               ///< 高快地图数据文件权限错误
    DIAGNOSIS_TYPE_GLOBAL_PATH_ERROR,             ///< 城市+高快地图数据路径错误
    DIAGNOSIS_TYPE_GLOBAL_FILE_AUTH_FAULT,        ///< 城市+高快地图数据文件权限错误
    
    DIAGNOSIS_TYPE_HDMAP_FAULT = 100,             ///< 高快地图数据文件损坏
    DIAGNOSIS_TYPE_GLOBAL_HDMAP_FAULT,            ///< 城市+高快地图数据文件损坏
    
    DIAGNOSIS_TYPE_RECEIVE_MESSAGE_FAULT = 200,   ///<  接收消息错误
    DIAGNOSIS_TYPE_SEND_MESSAGE_FAULT = 201,      ///<  发送消息错误
    
    DIAGNOSIS_TYPE_POS_SIGNAL_FAULT = 300,        ///< 接收定位信号错误
    
    DIAGNOSIS_TYPE_GNSS_LOST_FAULT = 302,          ///< 接收gnss lost 错误
    DIAGNOSIS_TYPE_GNSS_INVALID_FAULT = 303,       ///< 接收gnss invalid 错误
    DIAGNOSIS_TYPE_ACC_LOST_FAULT = 309,          ///< 接收acc lost 错误
    DIAGNOSIS_TYPE_ACC_INVALID_FAULT = 310,       ///< 接收acc invalid 错误
    DIAGNOSIS_TYPE_GYRO_LOST_FAULT = 316,          ///< 接收gyro lost 错误
    DIAGNOSIS_TYPE_GYRO_INVALID_FAULT = 317,       ///< 接收gyro invalid 错误
    DIAGNOSIS_TYPE_PULSE_LOST_FAULT = 323,          ///< 接收vehicle speed lost 错误
    DIAGNOSIS_TYPE_PULSE_INVALID_FAULT = 324,       ///< 接收vehicle speed invalid 错误
    DIAGNOSIS_TYPE_CAMERA_LOST_FAULT = 330,          ///< 接收camera lost 错误
    DIAGNOSIS_TYPE_CAMERA_INVALID_FAULT = 331,       ///< 接收camera invalid 错误
    DIAGNOSIS_TYPE_AUTO_LOCATION_LOST_FAULT = 340,   ///< 接收AUTO定位信息错误

    DIAGNOSIS_TYPE_ACTIVATION_FAULT = 400,        ///<  激活错误
};

/**
 * 诊断状态
 */
enum DiagnosisState {
    DIAGNOSIS_STATUS_ON,         ///< 报诊断
    DIAGNOSIS_STATUS_OFF         ///< 清诊断
};

/**
 * 诊断开关状态
 */
enum DiagnosisSwitchState {
    DIAGNOSIS_SWITCH_ENABLE,     ///< 开启诊断
    DIAGNOSIS_SWITCH_DISABLE     ///< 关闭诊断
};

/**
 * SDK诊断信息监听类
 */
class GHDMAP_API IHDMapDiagnosisListener {
public:
    virtual ~IHDMapDiagnosisListener() {}
    
    /**
     * 诊断信息变化接口
     *
     * @param[in] state 诊断状态
     * @param[in] type  诊断类型
     * @param[in] detailMessage 诊断描述信息，如无，则为NULL。
     */
    virtual void onDiagnosisChanged(DiagnosisState state, DiagnosisType type, const char *detailMessage) = 0;
    
    /**
     * 清空所有诊断信息接口
     */
    virtual void onDiagnosisCleared() = 0;
    
    /**
     * 更新诊断开关接口
     *
     * @param[in] state 诊断开关，打开或者关闭对应诊断类型。
     * @param[in] type 诊断类型
     */
    virtual void onUpdateDiagnosisSwitchState(DiagnosisSwitchState state, DiagnosisType type) = 0;
};

/**
 *诊断信息接收者
 */
class GHDMAP_API IHDMapDiagnosisReceiver {
public:
    virtual ~IHDMapDiagnosisReceiver() {}
    
    /**
     * 接收诊断信息接口
     *
     * @param[in] state 诊断状态
     * @param[in] type  诊断类型
     * @param[in] detailMessage 诊断描述信息，如无，则为NULL。
     *
     */
    virtual void receiveDiagnosis(DiagnosisState state, DiagnosisType type, const char *detailMessage) = 0;
    
    /**
     * 设置诊断监听者
     *
     * @param[in] diagnosisListener 诊断监听者，由SDK设置到集成方，用于监听诊断信息变化。生命周期由SDK管理。
     * @deprecated v5.9版本后废弃
     */
    GHD_DEPRECATED_FROM("v5.9") virtual void setDiagnosisListener(GHD_UNUSED IHDMapDiagnosisListener *diagnosisListener) {}
};

}
}
