﻿#ifndef __DATA_DEFINE_H__
#define __DATA_DEFINE_H__

#include <stdlib.h>

/**
* @brief 定位模式数据类型。

*/
enum LocTypeT {
    LocTypeUnknown_T = -1,           /**< 未知 */
    LocTypeGNSS_T = 0,            /**< 纯GPS定位模式 */
    LocTypeDrBackHPP_T = 1,            /**< DR模式(后端融合)高精度定位 */
    LocTypeDrFront_T = 2,            /**< DR模式(前端融合) */
    LocTypeDrBackHPM_T = 3,            /**< DR模式(后端融合)HQ粗匹配 */
    LocTypeCount_T,            /**< 模式个数 */
};
/**
* @brief 设备安装角数据定义。

*/
struct LocMountAngleT {

    LocMountAngleT() {
        reset();
    }

    void reset() {
        isValid = false;
        yaw     = 0;
        pitch   = 0;
        roll    = 0;
    }

    bool      isValid;      /**< 安装角是否有效。*/
    double    yaw;          /**< 偏航角。 */
    double    pitch;        /**< 俯仰角。 */
    double    roll;         /**< 翻滚角。 */
};

/**
* @brief 传感器参数。

*/
struct LocSensorOptionT {

    LocSensorOptionT() {
        reset();
    }

    void reset() {
        hasAcc  = 0;
        hasGyro = 0;
        hasTemp = 0;
        hasPressure = 0;
        hasMag  = 0;
        hasW4m  = 0;
        pulseFreq = 0;
        gyroFreq  = 0;
        gpsFreq   = 0;
        accFreq   = 0;
        w4mFreq   = 0;
        visFreq   = 0;
    }

    int hasAcc;      /**< 加速度计轴数。值：{0|1|3}， 0 表示没有。 */
    int hasGyro;     /**< 陀螺仪轴数。值：{0|1|3} ， 0 表示没有。 */
    int hasTemp;     /**< 有无陀螺温度传感器
                        - 0：无
                        - 1：有 */
    int hasPressure; /**< 有无气压计
                        - 0：无
                        - 1：有 */
    int hasMag;      /**< 有无磁力计
                        - 0：无
                        - 1：有 */
    int hasW4m;      /**< 有无四轮速传感器
                        - 0：无
                        - 1：有 */
    int pulseFreq;   /**< 脉冲信息输入频率。@note 单位：Hz */
    int gyroFreq;    /**< 陀螺仪信息输入频率。@note 单位：Hz */
    int gpsFreq;     /**< GNSS信息输入频率。@note 单位：Hz */
    int accFreq;     /**< 加速度计信息输入频率。@note 单位：Hz */
    int w4mFreq;     /**< 四轮速信息输入频率。@note 单位：Hz */
    int visFreq;     /**< 视觉车道线信息输入频率。@note 单位：Hz */
};

/**
* @brief 相机杆臂数据定义(在以指定输出点为原点的车身坐标系的位置，车身坐标系轴向为右前上坐标系)

*/
struct LocCameraLeverT {

    LocCameraLeverT() {
        reset();
    }

    void reset() {
        isValid = false;
        lbx = 0;
        lby = 0;
        lbz = 0;
    }

    bool      isValid;      /**< 杆臂是否有效。*/
    double    lbx;          /**< 横向杆臂值。 */
    double    lby;          /**< 纵向杆臂值。 */
    double    lbz;          /**< 天向杆臂值。*/
};

/**
* @brief IMU杆臂数据定义(在以指定输出点为原点的车身坐标系的位置，车身坐标系轴向为右前上坐标系)

*/
struct LocIMULeverT {

    LocIMULeverT() {
        reset();
    }

    void reset() {
        isValid = false;
        lbx = 0;
        lby = 0;
        lbz = 0;
    }

    bool      isValid;      /**< 杆臂是否有效。*/
    double    lbx;          /**< 横向杆臂值。 */
    double    lby;          /**< 纵向杆臂值。 */
    double    lbz;          /**< 天向杆臂值。 */
};

/**
* @brief GNSS天线杆臂数据定义(在以指定输出点为原点的车身坐标系的位置，车身坐标系轴向为右前上坐标系)

*/
struct LocGNSSLeverT {

    LocGNSSLeverT() {
        reset();
    }

    void reset() {
        isValid = false;
        lbx = 0;
        lby = 0;
        lbz = 0;
    }

    bool      isValid;      /**< 杆臂是否有效。*/
    double    lbx;          /**< 横向杆臂值。 */
    double    lby;          /**< 纵向杆臂值。*/
    double    lbz;          /**< 天向杆臂值。 */
};

/**
* @brief 输出杆臂数据定义(在以指定输出点为原点的车身坐标系的位置，车身坐标系轴向为右前上坐标系)

*/
struct LocOutPutLeverT {

    LocOutPutLeverT() {
        reset();
    }

    void reset() {
        isValid = false;
        lbx = 0;
        lby = 0;
        lbz = 0;
    }

    bool      isValid;      /**< 杆臂是否有效。*/
    double    lbx;          /**< 横向杆臂值。 */
    double    lby;          /**< 纵向杆臂值。 */
    double    lbz;          /**< 天向杆臂值。 */
};

/**
* @brief 离线数据类型。
*/
enum OfflineDataTypeT
{
    OFFLINE_DATA_TYPE_NULL,  /**< 无数据 */
    OFFLINE_DATA_TYPE_SD,  /**< SD数据 */
    OFFLINE_DATA_TYPE_HD,  /**< HD数据 */
};
/**
* @brief 外部相机输入的坐标系。
*/
enum CameraCoordinateSystem {
    FrontRightDown = 0,          /**< 前右下 */
    FrontLeftUp = 1,             /**< 前左上 */
    RightFrontUp = 2,            /**< 右前上 */
};

/**
* @brief IMU型号
* @see   IMUModel
*/
enum LocIMUModelT {
    IMU_IAM20685_T = 0,    /**< INVENSENSE IAM20685  */
    IMU_SCHA600_T = 1,     /**< MuraTa SCHA600       */
};

/**
* @brief GNaviData相关路径配置方式。
*/
enum DataConfigType {
    DATA_CONFIG_TYPE_PATH = 0,    /**< 通过传入xml文件的路径配置。 */
    DATA_CONFIG_TYPE_CONTENT = 1, /**< 通过传入xml文件的内容配置。 */
};

/**
 * @brief 定位引擎初始化参数配置项。
 * @see LocTypeT
 * @see LocMountAngleT
 * @see LocSensorOptionT
 * @see LocCameraLeverT
 * @see LocIMULeverT
 * @see LocGNSSLeverT
 * @see LocOutPutLeverT
 * @see LocIMUModelT
 * @see CameraCoordinateSystem
 */
struct Config {

    Config() {
        reset();
    }

    void reset() {
        locType = LocTypeUnknown_T;
        mountAngle.reset();
        sensorOption.reset();
        cameraLever.reset();
        imuLever.reset();
        gnssLever.reset();
        dataProviderCoarse = NULL;
        dataProviderFine   = NULL;
        signalDelay = 0;
        funcs = 0;
        freqOutput = 10;
        isHDReplay = false;
        hmiIp = NULL;
        hmiPort = -1;
        cameraCoordOption = RightFrontUp;
        IsOTAUpdateSwitch = true;
        imuModel = IMU_IAM20685_T;
    }

    LocTypeT locType;              /**< 定位模式。 @see LocTypeT */
    LocMountAngleT mountAngle;     /**< 安装角信息。 @see LocMountAngleT */
    LocSensorOptionT sensorOption; /**< 传感器参数信息。 @see LocSensorOptionT */
    LocCameraLeverT cameraLever;   /**< 相机杆臂信息。 @see LocCameraLeverT */
    LocIMULeverT imuLever;         /**< IMU杆臂信息。@see LocIMULeverT */
    LocGNSSLeverT gnssLever;       /**< GPS杆臂信息。 @see LocGNSSLeverT */
    LocOutPutLeverT outputLever;   /**< 输出杆臂信息。 @see LocOutputLeverT */
    LocIMUModelT imuModel;          /**< IMU型号信息。 @see LocIMUModelT */
    void *dataProviderCoarse;      /**< 粗匹配定位使用的 dice::IDataProvider 实例指针，
                                     * 粗匹配项目基于HD离线数据创建，高精项目基于SD离线数据创建。 */
    void *dataProviderFine;        /**< 高精定位使用的 dice::IDataProvider 实例指针，
                                     * 粗匹配项目传NULL，高精项目基于HD离线数据创建。 */

    int funcs;                     /**< 功能开关 */
    int freqOutput;           /**< 定位结果输出频率，正整数有效，默认值10。 */
    int signalDelay;          /**< 传感器信号最大延迟，非负有效，默认值0。 */
    bool isHDReplay;        /**< 是否高精快速回放模式，可选配置。仅在回放模式下有用，如要启用，请确保回放loc日志中
                              * 有@GLF记录。启用后，将通过日志中的@GLF（而非定位内部定时器）触发定位对外输出，
                              * 这样可以保证在快速回放模式下定位对外输出的个数与原始日志一致，且多次回放的高精定位结果一样，
                              * 不受系统时间变化影响（非高精定位结果个数一致，但内容不能保证一致）。
                              * 如果要求快速回放模式下定位对外输出个数不缺失，或对多次回放时高精定位结果的一致性有严格要求，请启用。*/
    char* hmiIp;            /**< hmi显示工具PC的ip地址。 */
    int   hmiPort;          /**< hmi显示工具PC的端口号。 */
    CameraCoordinateSystem    cameraCoordOption;  /**< 外部相机输入的坐标系, 需将当前相机输出信号转换成定位引擎需要的相机坐标系（前右下）下的信号。 @see CameraCoordinateSystem */
    bool IsOTAUpdateSwitch;  /**< 是否接受OTA升级。*/
};

/**
* @brief 定位信号数据类型。
* @see   LocSignDataT
*/
enum LocDataTypeT {
    LocDataEmpty_T           = 0x00000000,               /**< 空数据 */
    LocDataAcce3D_T          = 0x00000002,               /**< 3D加速度计 */
    LocDataGyro_T            = 0x00000004,               /**< 陀螺 */
    LocDataPulse_T           = 0x00000008,               /**< 脉冲/车速 */
    LocDataGnss_T            = 0x00000010,               /**< 卫星定位数据 */
    LocDataGpgsv_T           = 0x00000040,               /**< 卫星星历数据 */
    LocDataW4M_T             = 0x00000100,               /**< 四轮速模型数据 */
    LocDataDrFusion_T        = 0x00000200,               /**< 融合信号 */
    LocDataW4MTR_T           = 0x00000800,               /**< 四轮胎压数据 */
    LocDataVision_T          = 0x00040000,               /**< 视觉识别信号 */
    LocDataVisualObj_T       = 0x00080000,	             /**< 视觉目标识别结果 */
    LocDataRAI_T			 = 0x00100000,			     /**< 道路信息 */
    LocDataGLF_T      	     = 0x00200000,			     /**< GLF信息 */
    LocDataSpdDir_T          = 0x00400000,			     /**< 车轮移动方向 */
    LocDataTNL_T             = 0x00800000,			     /**< TNL信息 */
    LocDataCPY_T             = 0x01000000,			     /**< CPY信息 */
};

/**
* @brief 车辆档位状态。
* @see   LocSignDataT
*/
enum LocGearStateT {
    LocGearReverse_T,                   /**< 倒档 */
    LocGearNotReverse_T                 /**< 非倒档 */
};

/**
* @brief 四轮速信号数据类型。
* @see   LocSignDataT
*/
struct LocW4MT {
    LocDataTypeT dataType;                     /**< 本结构体所表示的数据类型，赋值：LocDataW4M_T。 @see LocDataW4M_T */
    float       vrl;                          /**< 后左轮速。@note 单位：千米/小时 */
    float       vrr;                          /**< 后右轮速。@note 单位：千米/小时 */
    float       vfl;                          /**< 前左轮速。@note 单位：千米/小时 */
    float       vfr;                          /**< 前右轮速。@note 单位：千米/小时 */
    float       steerAngle;                   /**< 方向盘转角。@note 单位：度 */
    float       yawRate;                      /**< 横摆角速度。@note 单位：弧度/秒 */
    float       lonAcc;                       /**< 纵向加速度。@note 单位：米/平方秒 */
    float       latAcc;                       /**< 横向加速度。@note 单位：米/平方秒 */
    LocGearStateT gearState;                   /**< 档位，是否倒挡。 @see LocGearStateT */
    int     interval;                         /**< 前后两个信号的间隔时间。@note 单位：毫秒 */
    unsigned int    tickTime;                 /**< 时间戳。@note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    /* 新协议下补充数据 */
    float       prl;                          /**< 后左脉冲。*/
    float       prr;                          /**< 后右脉冲。*/
    float       pfl;                          /**< 前左脉冲。*/
    float       pfr;                          /**< 前右脉冲。*/
    /* 拆解传感器偏移数据 */
    float       offYR;                        /**< 横摆角速度偏移。@note 单位：弧度/秒 */
    float       offLonA;                      /**< 纵向加速度。@note 单位：米/平方秒 */
    float       offLatA;                      /**< 横向加速度。@note 单位：米/平方秒 */
};

/**
* @brief 有效轴定义。
* @see   LocAcce3dT
* @see   LocGyroT
*/
enum LocThreeAxisT{
    LocAxisNull_T     = 0,            /**< 空值。 */
    LocAxisYaw_T      = 1,            /**< yaw轴有效，左右。 */
    LocAxisPitch_T    = 2,            /**< Yaw + Pitch有效。@note 暂不支持 */
    LocAxisYawRoll_T  = 3,            /**< Yaw + Roll有效。@note 暂不支持 */
    LocAxisAll_T      = 4,            /**< 三轴均有效 */
};

/**
* @brief 陀螺信号数据类型。
* @see   LocSignDataT
* @see   LocThreeAxisT
*/
struct LocGyroT
{
    LocDataTypeT       dataType;        /**< 本结构体所表示的数据类型，赋值：LocDataGyro_T。 @see LocDataGyro_T */
    bool               isValid;         /**< 从故障诊断的角度看该信号是否有效；如不需故障诊断功能请赋值为true。当启用诊断功能时：
                                            - false（信号无效）：此时信号不可用，各字段以无效信号(该字段支持的最大值)的形式发送
                                            - true（信号有效）：此时信号可用，各字段以默认值或规定范围内的值发送，保证有效信号不存在某字段无效或超出范围的情况
                                            
                                            @note 必填 
                                        */
    LocThreeAxisT      axis;            /**< 有效数据轴。@see LocThreeAxisT */
    float              valueZ;          /**< 上。@note 单位：度/秒 */
    float              valueX;          /**< 右。@note 单位：度/秒 */
    float              valueY;          /**< 前。@note 单位：度/秒 */
    float              temperature;     /**< 陀螺仪芯片上的温度传感器的读值。 @note 单位：摄氏度*/
    int                interval;        /**< 前后两个信号的间隔时间。@note 单位：毫秒 */
    unsigned long long tickTime;        /**< 时间戳。@note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    unsigned long long tickTimeRecv;    /**< 仅在高精回放模式下有用，填充从loc日志中记录的本地时间。 @note 单位：毫秒 */
};

/**
* @brief 3D加速度计信号数据类型。
* @see   LocSignDataT
* @see   LocThreeAxisT
*/
struct LocAcce3dT {
    LocDataTypeT       dataType;         /**< 本结构体所表示的数据类型，赋值：LocDataAcce3D_T。 @see LocDataAcce3D_T */
    bool               isValid;          /**< 从故障诊断的角度看该信号是否有效；如不需故障诊断功能请赋值为true。当启用诊断功能时：
                                            - false（信号无效）：此时信号不可用，各字段以无效信号(该字段支持的最大值)的形式发送
                                            - true（信号有效）：此时信号可用，各字段以默认值或规定范围内的值发送，保证有效信号不存在某字段无效或超出范围的情况
                                            
                                            @note 必填 
                                        */
    LocThreeAxisT      axis;             /**< 有效数据轴。 @see LocThreeAxisT*/
    float              acceZ;            /**< 上。@note 单位：g(9.8米/秒^2) */
    float              acceX;            /**< 右。@note 单位：g(9.8米/秒^2) */
    float              acceY;            /**< 前。@note 单位：g(9.8米/秒^2) */
    int                interval;         /**< 前后两个信号的间隔时间。@note 单位：毫秒 */
    unsigned long long tickTime;         /**< 时间戳。@note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    unsigned long long tickTimeRecv;     /**< 仅在高精回放模式下有用，填充从loc日志中记录的本地时间。 @note 单位：毫秒 */
};

/**
 * @brief GNSS卫星定位信号数据类型。
 * @note  数据频率：以卫星定位信号的实际观测频率为准，一般为1Hz或10Hz（RTK）
 * @see   LocSignDataT
 */
struct LocGnssT {
    LocDataTypeT dataType;                    /**< 本结构体所表示的数据类型，赋值：LocDataGnss_T。@see LocDataGnss_T @note 必填*/
    bool isValid;                             /**< 从故障诊断的角度看该信号是否有效；如不需故障诊断功能请赋值为true。当启用诊断功能时：
                                                    - false（信号无效）：此时信号不可用，各字段以无效信号(该字段支持的最大值)的形式发送
                                                    - true（信号有效）：此时信号可用，各字段以默认值或规定范围内的值发送，保证有效信号不存在某字段无效或超出范围的情况
                                                    
                                                    @note 必填 
                                                */
    int     sourType;                    /**< 信号来源
                                             - 0：GPS定位
                                             - 1：网络定位(包括室内定位和基站定位) 
                                             - 9: RTK 
                                            
                                            表示使用的GPS硬件带不带RTK，如果带，赋值9；不带赋值0。 @note 必填 */
    char      mode;                        /**< 定位模式，非RTK模式下不使用；RTK模式下为RTK定位质量因子，取值及含义如下：
										     - 0：Fix not valid，未定位 
											 - 1：GPS fix，GPS单点定位固定解
											 - 2：Differential GPS fix, OmniSTAR VBS，GPS差分定位
											 - 3：PPS解
											 - 4：Real-Time Kinematic, fixed integers，RTK固定解
											 - 5：Real-Time Kinematic, float integers, OmniSTAR XP/HP or Location RTK，RTK浮点解
											 - 6：Dead Reckoning mode, fix valid, 估计值
											 - 7：手工输入模式
											 - 8：模拟模式
                                             - 30-纯DR
                                             - 31-GPS+DR
                                             - 32-DGPS+DR
                                             - 33-PPS+DR
                                             - 34-RTK固定解+DR
                                             - 35-RTK浮点解+DR
                                            
                                            @note 必填 */
    char      status;                      /**< GPS定位状态位 
                                             - 'A'：有效定位
                                             - 'V'：无效定位 
                                            
                                            @note 必填 */
    unsigned char isEncrypted;                 /**< 位置是否加密偏移。 @note 必填
                                             - 0：未偏移    表示(lonS, latS)已经赋值了未加密的经纬度，(lon, lat)未处理。@note 高精定位模式时设定
                                             - 1：已经偏移  表示(lon, lat)字段已经赋值了加密后的经纬度，(lonS, latS)未处理。@note 粗匹配（/粗匹配PLUS）模式时设定 */
    signed char isNS;								/**< 纬度半球。'N'或'S'，如果在非中国区内使用，则需提供。 @note 选填。获取不到时赋默认值'N'*/
    signed char isEW;								/**< 经度半球。'E'或'W'，如果在非中国区内使用，则需提供。 @note 选填。获取不到时赋默认值'E'*/
    int lon;                               /**< 加密后的经度。@note 粗匹配（/粗匹配PLUS）模式必填。获取不到或定位模式为高精定位模式时赋默认值0。GCJ02，单位：千万分之一度 */
    int lat;                               /**< 加密后的纬度。@note 粗匹配（/粗匹配PLUS）模式必填。获取不到或定位模式为高精定位模式时赋默认值0。GCJ02，单位：千万分之一度 */
    int lonS;                              /**< 未加密的经度。@note 高精定位模式必填。获取不到或定位模式为粗匹配（/粗匹配PLUS）模式时赋默认值0。WGS84，单位：千万分之一度 */
    int latS;                              /**< 未加密的纬度。@note 高精定位模式必填。获取不到或定位模式为粗匹配（/粗匹配PLUS）模式时赋默认值0。WGS84，单位：千万分之一度 */
    float       speed;                       /**< 速度。@note 必填。单位：千米/小时 */
    float       course;                      /**< 航向。@note 必填。单位：度。范围：[0-360)，北零顺时针 */
    float       alt;                         /**< 海拔高度。@note 选填。获取不到时赋默认值-10001。单位：米 */
    int     num;                             /**< 卫星个数。 @note 必填*/
    float       hdop;                        /**< 水平分量精度因子。 @note 必填。 */
    float       vdop;                        /**< 垂直分量精度因子。 @note 选填。获取不到时赋默认值-1.0 */
    float       pdop;                        /**< 位置精度因子。 @note 选填。获取不到时赋默认值-1.0 */
    int     year;                        /**< UTC时间，填充年月日时分秒，必须为东八区的时间。四位数年份。 @note 必填*/
    int     month;                       /**< UTC时间，填充年月日时分秒，必须为东八区的时间。月份，数值范围：1-12。 @note 必填*/
    int     day;                         /**< UTC时间，填充年月日时分秒，必须为东八区的时间。日，数值范围：1-31。 @note 必填*/
    int     hour;                        /**< UTC时间，填充年月日时分秒，必须为东八区的时间。时，数值范围：0-23。 @note 必填*/
    int     minute;                      /**< UTC时间，填充年月日时分秒，必须为东八区的时间。分，数值范围：0-59。 @note 必填*/
    int     second;                      /**< UTC时间，填充年月日时分秒，必须为东八区的时间。秒，数值范围：0-59，润秒时为60。 @note 必填*/
    int     milliSec;                    /**< UTC时间，填充年月日时分秒，必须为东八区的时间。毫秒，数值范围：0-999。 @note 必填*/
    float   accuracy;                    /**< 水平精度半径。 @note 必填。单位：米 */
    float   courseAccuracy;              /**< 航向精度半径。 @note 必填。单位：度，范围：(0-180)。*/
    unsigned long long tickTime;         /**< 时间戳。 @note 必填。@note 系统滴答数（相对系统启动时间）@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    unsigned long long tickTimeSys;      /**< 仅在高精回放模式下有用，填充从loc日志中记录的系统时间，该时间表示当此gnss信号初次进入定位引擎时的引擎系统时间。@note 单位：毫秒*/
    unsigned long long tickTimeRecv;     /**< 仅在高精回放模式下有用，填充从loc日志中记录的本地时间。@note 单位：毫秒 */
};

/**
* @brief 脉冲信号数据类型。
* @note  倒车的情况下，速度为负值
* @see   LocSignDataT
*/
struct LocPulseT {
    LocDataTypeT dataType;         /**< 本结构体所表示的数据类型，赋值：LocDataPulse_T。 @see LocDataPulse_T*/
    bool        isValid;           /**< 从故障诊断的角度看该信号是否有效；如不需故障诊断功能请赋值为true。当启用诊断功能时：
                                        - false（信号无效）：此时信号不可用，各字段以无效信号(该字段支持的最大值)的形式发送
                                        - true（信号有效）：此时信号可用，各字段以默认值或规定范围内的值发送，保证有效信号不存在某字段无效或超出范围的情况
                                        
                                        @note 必填 
                                    */
    float       value;            /**< 脉冲速度。@note 单位：千米/小时。标量值，其正负由LocSpdDirT信号确定。*/ 
    int     interval;         /**< 前后两个信号的间隔时间。@note 单位：毫秒 */
    unsigned long long    tickTime;         /**< 时间戳。@note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    unsigned long long    tickTimeSys;      /**< 仅在高精回放模式下有用，填充从loc日志中记录的系统时间，该时间表示当此pulse信号初次进入定位引擎时的引擎系统时间。@note 单位：毫秒*/
    unsigned long long    tickTimeRecv;     /**< 仅在高精回放模式下有用，填充从loc日志中记录的本地时间。@note 单位：毫秒 */
};

/**
* @brief 车轮移动方向枚举。
* @see   LocSpdDirT
*/
enum LocSpdDirValueT {
    LocSpdDirForward_T,     /**< 前进 */
    LocSpdDirBackward_T,    /**< 后退 */
    LocSpdDirInvalid_T,     /**< 方向无效*/
};

/**
* @brief 车轮移动方向数据类型。
* @see LocSpdDirValueT
*/
struct LocSpdDirT {
    LocDataTypeT dataType;        /**< 本结构体所表示的数据类型，取值LocDataSpdDir_T。 */
    unsigned long long tickTime;  /**< 时间戳。@note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    bool isValid;                   /**< 从故障诊断的角度看该信号是否有效；如不需故障诊断功能请赋值为true。当启用诊断功能时：
                                        - false（信号无效）：此时信号不可用，各字段以无效信号(该字段支持的最大值)的形式发送
                                        - true（信号有效）：此时信号可用，各字段以默认值或规定范围内的值发送，保证有效信号不存在某字段无效或超出范围的情况
                                        
                                        @note 必填 
                                    */
    LocSpdDirValueT value;        /**< 方向值 @see LocSpdDirValueT*/
};

/**
* @brief 卫星星历数据类型。
* @note 推算输入频率：与GPS或融合信号一致
* @note 不同定位系统分开输入，如GPS放在一帧输入，北斗放一帧输入
* @see  LocSignDataT
*/
struct LocGpgsvT {
    static const int MAX_SAT_CNT = 36; /**< 常量36，该结构体表示的最大卫星数为36。*/
    LocDataTypeT dataType;               /**< 本结构体所表示的数据类型，赋值：LocDataGpgsv_T。 @see LocDataGpgsv_T @note 必填*/
    bool    isValid;                     /**< 从故障诊断的角度看该信号是否有效；如不需故障诊断功能请赋值为true。当启用诊断功能时：
                                            - false（信号无效）：此时信号不可用，各字段以无效信号(该字段支持的最大值)的形式发送
                                            - true（信号有效）：此时信号可用，各字段以默认值或规定范围内的值发送，保证有效信号不存在某字段无效或超出范围的情况
                                            
                                            @note 必填 
                                        */
    int     type;                        /**< 定位系统
                                             - 0：GPS
                                             - 1：北斗
                                             - 2：glonass
                                             - 3: galileo
                                             - 5: qzss
                                             - 6: SBAS
                                             - 7: unknown
                                        
                                            @note 必填
                                         */
    int     num;                         /**< 可用卫星总数。 @note 必填 */
    int     prn[MAX_SAT_CNT];                     /**< 卫星编号。 @note 必填 */
    int     elevation[MAX_SAT_CNT];               /**< 仰角。@note 必填 @note 00～90度 */
    int     azimuth[MAX_SAT_CNT];                 /**< 航向角。@note 选填 @note 0～359° */
    int     snr[MAX_SAT_CNT];                     /**< 信噪比。 @note 选填 */
    unsigned long long    tickTime;                    /**< 时间戳。@note 选填 @note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    unsigned long long    tickTimeRecv;  /**< 仅在高精回放模式下有用，填充从loc日志中记录的本地时间。 @note 单位：毫秒 */
};

/**
* @brief 车道线类型
*/
//enum LaneTypeT {
//    Dashed_T = 0,   // 虚线
//    Solid_T = 1,   // 实线
//    Undecided_T = 2,   // 不确定             
//    RoadEdge_T = 3,   // 路沿                  
//    DoubleMark_T = 4,   // 双线（虚/实）
//    BottsDots_T = 5,   // 凸钉
//    Invalid_T = 6,   // 无效
//    // 以上为Mobileye协议定义的类型
//    // 以下为扩展类型：
//    SingleWhiteDot_T = 7,   // 单白虚线
//    SingleWhiteSolid_T = 8,   // 单白实线
//    SingleYellowDot_T = 9,   // 单黄虚线
//    SingleYellowSolid_T = 10,  // 单黄实线
//    DoubleYellowSolid_T = 11,  // 双黄实线
//    DotSolid_T = 12,  // 虚实线
//    SolidDot_T = 13,  // 实虚线
//    SlowDown_T = 14,  // 减速线
//};
/**
* @brief 车道线类型(20200407新修，将车道类型和颜色分开)
*/
enum LaneTypeT {
    Undecided_T = 0,  /**< 未识别 */
    Solid_T = 1,      /**< 单实线 */
    Dashed_T = 2,    /**< 单虚线 */          
    RoadEdge_T = 3,   /**< 路沿 */                
    DoubleMark_Solid_T = 4,   /**<双实线 */
    DoubleMark_Dashed_T = 5,  /**< 双虚线 */
    BottsDots_T = 6, /**< 凸钉 */
    DotSolid_T = 7,  /**< 左虚右实 */
    SolidDot_T = 8,  /**< 左实右虚 */
    SlowDown_T = 9, /**< 减速 */
    Invalid_T = 10, /**< 无效 */
};

/**
* @brief 车道线颜色。
*/
enum LaneMarkColorT {
    UnknownColor_T = 0, /**< 未知颜色 */
    White_T = 1, /**< 白色 */
    Yellow_T = 2, /**< 黄色 */
    Blue_T = 3, /**< 蓝色 */
};


/**
* @brief 车道线识别结果。
-  横向: y；
-  纵向: x；
-  曲线方程: y = c3*x^3 + c2*x^2 + c1*x + c0
* @see LocVisionT
* @see LaneMarkColorT
*/
struct LocLaneT
{
    LaneTypeT type; /**< 车道线类型。 */
    double c0;      /**< 曲线方程常数项。 */
    double c1;      /**< 曲线方程一次项系数。 */
    double c2;      /**< 曲线方程二次项系数。 */
    double c3;      /**< 曲线方程三次项系数。 */
    double width;   /**< 车道线线宽，单位米。 */
    double qualityGeometry; /**< 车道线几何可信度，取值范围[0, 1.0]，值越大表示越可信。 */
    double qualityType;     /**< 车道线类型可信度，取值范围[0, 1.0]，值越大表示越可信。 */
    float startRange;       /**< 车道线方程起点。 */
    float endRange;       /**< 车道线方程终点。 */
    LaneMarkColorT color; /**< 车道线颜色。 @see LaneMarkColorT*/
};

#define MAX_LANE_CNT_T (6)

/**
* @brief 视觉信号数据类型。
* @see LocLaneT
*/
struct LocVisionT {
    LocDataTypeT  dataType;              /**< 本结构体所表示的数据类型，赋值：LocDataVision_T。@see LocDataVision_T */
    bool          isValid;               /**< 从故障诊断的角度看该信号是否有效；如不需故障诊断功能请赋值为true。当启用诊断功能时：
                                            - false（信号无效）：此时信号不可用，各字段以无效信号(该字段支持的最大值)的形式发送
                                            - true（信号有效）：此时信号可用，各字段以默认值或规定范围内的值发送，保证有效信号不存在某字段无效或超出范围的情况
                                            
                                            @note 必填
                                        */
    unsigned short leftLaneCnt;          /**< 识别的左侧车道线个数。 */
    unsigned short rightLaneCnt;         /**< 识别的右侧车道线个数。 */
    LocLaneT leftLanes[MAX_LANE_CNT_T];  /**< 左侧车道线识别结果详细信息，多个车道线按照由近及远（c0由大到小）的顺序排序。 @see LocLaneT */
    LocLaneT rightLanes[MAX_LANE_CNT_T]; /**< 由侧车道线识别结果详细信息，多个车道线按照由近及远（c0由小到大）的顺序排序。 @see LocLaneT */
    int interval;     /**< 前后两个信号的间隔时间。@note 单位：毫秒。已过期，可不填。 */
    unsigned long long ticktimeMCU; /**< MCU时间戳。@note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
	unsigned long long ticktimeCV;  /**< 取值同ticktimeMCU，暂时不做区分。 */
    unsigned long long tickTimeRecv; /**<仅在高精回放模式下有用，填充从loc日志中记录的本地时间。@note 单位：毫秒 */
};

/**
* @brief 交通标志牌类型。
*/
enum LocTrafficSignTypeT {
    LOC_TRAFFIC_SIGN_OTHER_T = 99,               /**< 其他 */
    LOC_TRAFFIC_SIGN_WARNING_T = 100,            /**< 警告标志(其他或无需细分类) */
    LOC_TRAFFIC_SIGN_SPEED_LIMIT_T = 101,        /**< 建议限速 */
    LOC_TRAFFIC_SIGN_CROSS_T = 102,              /**< 十字交叉 */
    LOC_TRAFFIC_SIGN_T_CROSS_LEFT_T = 103,       /**< T形交叉(左侧) */
    LOC_TRAFFIC_SIGN_T_CROSS_RIGHT_T = 104,      /**< T形交叉(右侧) */
    LOC_TRAFFIC_SIGN_T_CROSS_LEFT_RIGHT_T = 105, /**< T形交叉(左右侧) */
    LOC_TRAFFIC_SIGN_Y_CROSS_T = 106,            /**< Y形交叉 */
    LOC_TRAFFIC_SIGN_RING_CROSS_T = 107,         /**< 环形交叉 */
    LOC_TRAFFIC_SIGN_SHARP_TURN_LEFT_T = 108,    /**< 向左急转弯 */
    LOC_TRAFFIC_SIGN_SHARP_TURN_RIGHT_T= 109,   /**< 向右急转弯 */
    LOC_TRAFFIC_SIGN_REVERSE_DETOUR_T = 110,     /**< 反向弯路 */
    LOC_TRAFFIC_SIGN_CONTINUOUS_DETOUR_T = 111,  /**< 连续弯路 */
    LOC_TRAFFIC_SIGN_UPPER_STEP_T = 112,         /**< 上陡坡 */
    LOC_TRAFFIC_SIGN_DOWN_STEP_T = 113,          /**< 下陡坡 */
    LOC_TRAFFIC_SIGN_CONTINUOUS_DOWNHILL_T = 114,/**< 连续下坡 */
    LOC_TRAFFIC_SIGN_DOUBLE_NARROW_T = 115,      /**< 两侧变窄 */
    LOC_TRAFFIC_SIGN_RIGHT_NARROW_T = 116,       /**< 右侧变窄 */
    LOC_TRAFFIC_SIGN_LEFT_NARROW_T = 117,        /**< 左侧变窄 */
    LOC_TRAFFIC_SIGN_NARROW_BRIDGE_T = 118,      /**< 窄桥 */
    LOC_TRAFFIC_SIGN_TWO_WAY_TRIFFIC_T = 119,    /**< 双向交通 */
    LOC_TRAFFIC_SIGN_PEDESTRIANS_T = 120,        /**< 注意行人 */
    LOC_TRAFFIC_SIGN_CHIDREN_T = 121,            /**< 注意儿童 */
    LOC_TRAFFIC_SIGN_LIVETOCKS_T = 122,          /**< 注意牲畜 */
    LOC_TRAFFIC_SIGN_WILD_ANIMALS_T = 123,       /**< 注意野生动物 */
    LOC_TRAFFIC_SIGN_SIGNAL_LIGHT_T = 124,       /**< 注意信号灯 */
    LOC_TRAFFIC_SIGN_FALING_ROCKS_RIGHT_T = 125, /**< 注意右侧落石 */
    LOC_TRAFFIC_SIGN_FALING_ROCKS_LEFT_T = 126,  /**< 注意左侧落石 */
    LOC_TRAFFIC_SIGN_CROSSWIND_AREA_T = 127,     /**< 注意横风 */
    LOC_TRAFFIC_SIGN_SLIPPERY_T = 128,           /**< 易滑 */
    LOC_TRAFFIC_SIGN_DANGEROUS_RIGHT_T = 129,    /**< 傍山险路右侧 */
    LOC_TRAFFIC_SIGN_DANGEROUS_LEFT_T = 130,     /**< 傍山险路左侧 */
    LOC_TRAFFIC_SIGN_DAM_ROAD_RIGHT_T = 131,     /**< 堤坝路 右侧 */
    LOC_TRAFFIC_SIGN_DAM_RAOD_LEFT_T = 132,      /**< 堤坝路 左侧 */
    LOC_TRAFFIC_SIGN_VILLAGE_T = 133,            /**< 村庄 */
    LOC_TRAFFIC_SIGN_TUNNEL_T = 134,             /**< 隧道 */
    LOC_TRAFFIC_SIGN_FERRY_T = 135,              /**< 渡口 */
    LOC_TRAFFIC_SIGN_HUMPBACK_BRIDGE_T = 136,    /**< 驼峰桥 */
    LOC_TRAFFIC_SIGN_ROUGH_ROAD_T = 137,         /**< 路面不平 */
    LOC_TRAFFIC_SIGN_CONVEX_ROAD_T = 138,        /**< 路面高凸 */
    LOC_TRAFFIC_SIGN_LOWLYING_ROAD_T = 139,      /**< 路面低洼 */
    LOC_TRAFFIC_SIGN_ROAD_FLOODS_T = 140,        /**< 过水路面 */
    LOC_TRAFFIC_SIGN_GUARDED_RAILWAY_CROSSING_T = 141,   /**< 有人看守铁道路口 */
    LOC_TRAFFIC_SIGN_UNGUARDED_RAILWAY_CROSSING_T = 142, /**< 无人看守铁道路口 */
    LOC_TRAFFIC_SIGN_NON_MOTOR_T = 143,          /**< 注意非机动车 */
    LOC_TRAFFIC_SIGN_HANDICAPPED_T = 144,        /**< 注意残疾人 */
    LOC_TRAFFIC_SIGN_ACCIDENT_T = 145,           /**< 事故易发路段 */
    LOC_TRAFFIC_SIGN_SLOWNDOWM_T = 146,          /**< 慢行 */
    LOC_TRAFFIC_SIGN_DETOUR_LEFT_RIGHT_T = 147,  /**< 左右绕行 */
    LOC_TRAFFIC_SIGN_DETOUR_LEFT_T = 148,        /**< 左侧绕行 */
    LOC_TRAFFIC_SIGN_DETOUR_RIGHT_T = 149,       /**< 右侧绕行 */
    LOC_TRAFFIC_SIGN_DANGEROUS_T = 150,          /**< 注意危险 */
    LOC_TRAFFIC_SIGN_ROAD_WORKS_T = 151,         /**< 施工 */
    LOC_TRAFFIC_SIGN_TUNNEL_LIGHTING_T = 152,    /**< 隧道开车灯 */
    LOC_TRAFFIC_SIGN_REVERSIBLE_LANE_T = 153,    /**< 注意潮汐车道 */
    LOC_TRAFFIC_SIGN_KEEP_DISTANCE_T = 154,      /**< 注意保持车距 */
    LOC_TRAFFIC_SIGN_SPLIT_ROAD_CROSS_T = 155,   /**< 注意分离式道路-十字平面交叉 */
    LOC_TRAFFIC_SIGN_SPLIT_ROAD_T_CROSS_T = 156, /**< 注意分离式道路-丁字平面交叉 */
    LOC_TRAFFIC_SIGN_ROAD_MERGE_LEFT_T = 157,    /**< 左侧合流 */
    LOC_TRAFFIC_SIGN_ROAD_MERGE_RIGHT_T = 158,   /**< 右侧合流 */
    LOC_TRAFFIC_SIGN_TRUCK_ESCAPE_RAMP_T = 159,  /**< 避险车道 */
    LOC_TRAFFIC_SIGN_ICY_RAOD_T = 160,           /**< 注意路面结冰 */
    LOC_TRAFFIC_SIGN_RAIN_SNOW_T = 161,          /**< 注意雨雪天 */
    LOC_TRAFFIC_SIGN_FOGGY_T = 162,              /**< 注意雾天 */
    LOC_TRAFFIC_SIGN_ADVERSE_WEATHER_T = 163,    /**< 注意不利气象条件 */
    LOC_TRAFFIC_SIGN_QUEUES_T = 164,             /**< 注意前方车辆排队 */
    LOC_TRAFFIC_SIGN_PROHIBITION_T = 200,        /**< 禁令标志(其他或无需细分类) */
    LOC_TRAFFIC_SIGN_SPEED_LIMIT_END_T = 202,    /**< 解除限制速度 */
    LOC_TRAFFIC_SIGN_SPEED_LIMIT_AREA_T = 203,   /**< 区域限制速度 */
    LOC_TRAFFIC_SIGN_SPEED_LIMIT_END_AREA_T = 204,/**< 区域限制速度解除 */
    LOC_TRAFFIC_SIGN_STOP_T = 205,               /**< 停车让行 */
    LOC_TRAFFIC_SIGN_YIELD_T = 206,              /**< 减速让行 */
    LOC_TRAFFIC_SIGN_YIELD_TO_ONCOMING_T = 207,  /**< 会车让行 */
    LOC_TRAFFIC_SIGN_NO_ENTRY_T = 208,           /**< 禁止通行 */
    LOC_TRAFFIC_SIGN_DO_NOT_ENTER_T = 209,       /**< 禁止驶入 */
    LOC_TRAFFIC_SIGN_NO_MOTOR_VEHICLES_T = 210,  /**< 禁止机动车驶入 */
    LOC_TRAFFIC_SIGN_NO_MINIBUSES_T = 211,       /**< 禁止小型客车驶入 */
    LOC_TRAFFIC_SIGN_NO_TRACTORS_T = 212,        /**< 禁止拖拉机驶入 */
    LOC_TRAFFIC_SIGN_NO_LEFT_TURN_T = 213,       /**< 禁止向左转弯 */
    LOC_TRAFFIC_SIGN_NO_RIGHT_TURN_T = 214,      /**< 禁止向右转弯 */
    LOC_TRAFFIC_SIGN_NO_STRAIGHT_THRU_T = 215,   /**< 禁止直行 */
    LOC_TRAFFIC_SIGN_NO_TURNS_T = 216,           /**< 禁止向左向右转弯 */
    LOC_TRAFFIC_SIGN_NO_STRAIGHT_THRU_LEFT_TURN_T = 217,   /**< 禁止直行与向左转弯 */
    LOC_TRAFFIC_SIGN_NO_STRAIGHT_THRU_RIGHT_TURN_T = 218,  /**< 禁止直行与向右转弯 */
    LOC_TRAFFIC_SIGN_NO_U_TURN_T = 219,          /**< 禁止调头 */
    LOC_TRAFFIC_SIGN_NO_OVERTAKING_T = 220,      /**< 禁止超车 */
    LOC_TRAFFIC_SIGN_END_OF_NO_OVERTAKING_T = 221,/**< 解除禁止超车 */
    LOC_TRAFFIC_SIGN_NO_STOPPING_T = 222,        /**< 禁止停车 */
    LOC_TRAFFIC_SIGN_NO_PARKING_T = 223,         /**< 禁止长时停车 */
    LOC_TRAFFIC_SIGN_NO_HONKING_T = 224,         /**< 禁止鸣喇叭 */
    LOC_TRAFFIC_SIGN_MAXIMUN_WIDTH_T = 225,      /**< 限制宽度 */
    LOC_TRAFFIC_SIGN_MAXIMUM_CLEARANCE_T = 226,  /**< 限制高度 */
    LOC_TRAFFIC_SIGN_WEIGTH_LIMIT_T = 227,       /**< 限制质量 */
    LOC_TRAFFIC_SIGN_AXLE_WEIGTH_LIMIT_T = 228,  /**< 限制轴重 */
    LOC_TRAFFIC_SIGN_STOP_AND_CHECK_T = 229,     /**< 停车检查 */
    LOC_TRAFFIC_SIGN_CARRYING_HAZARDOUS_MATERIAL_PROHIBITED_T = 230,/**< 禁止运输危险物品车辆驶入 */
    LOC_TRAFFIC_SIGN_CUSTOMS_T = 231,            /**<  海关 */
    LOC_TRAFFIC_SIGN_NO_PARKING_AREA_T = 232,    /**< 区域禁止长时停车 */
    LOC_TRAFFIC_SIGN_END_OF_NO_PARKING_ZONE_T = 233,/**< 区域禁止长时停车解除 */
    LOC_TRAFFIC_SIGN_NO_STOPPING_AREA_T = 234,    /**< 区域禁止停车 */
    LOC_TRAFFIC_SIGN_END_OF_NO_STOPPING_ZONE_T = 235,/**< 区域禁止停车解除 */
    LOC_TRAFFIC_SIGN_MAX_SPEED_LIMIT_T = 236,    /**< 最高限速 */
    LOC_TRAFFIC_SIGN_MIN_SPEED_LIMIT_T = 237,    /**< 最低限速 */
    LOC_TRAFFIC_SIGN_INDICATOR_T = 300,          /**< 指示标识(其他或无需细分类) */
    LOC_TRAFFIC_SIGN_GO_STRAIGHT_T = 301,        /**< 直行 */
    LOC_TRAFFIC_SIGN_TURN_LEFT_T = 302,          /**< 向左转弯 */
    LOC_TRAFFIC_SIGN_TURN_RIGHT_T = 303,         /**< 向右转弯 */
    LOC_TRAFFIC_SIGN_STRAIGHT_LEFT_TURN_T = 304, /**< 直行和向左转弯 */
    LOC_TRAFFIC_SIGN_STRAIGTH_RIGHT_TURN_T = 305,/**< 直行或向右转弯 */
    LOC_TRAFFIC_SIGN_LEFT_RIGHT_TURN_T = 306,    /**< 向左向右转弯 */
    LOC_TRAFFIC_SIGN_KEEP_RIGHT_T = 307,         /**< 靠右侧道路行驶 */
    LOC_TRAFFIC_SIGN_KEEP_LEFT_T = 308,          /**< 靠左侧道路行驶 */
    LOC_TRAFFIC_SIGN_OVER_PASS_AHEAD_LEFT_TURN_T = 309,/**< 立体交叉直行和左转弯行驶 */
    LOC_TRAFFIC_SIGN_OVER_PASS_AHEAD_RIGHT_TURN_T = 310,/**< 立体交叉直行和右转弯行驶 */
    LOC_TRAFFIC_SIGN_ROUNDABOUT_T = 311,         /**< 环岛行驶 */
    LOC_TRAFFIC_SIGN_ONEWAY_RIGHT_T = 312,       /**< 向右单行路 */
    LOC_TRAFFIC_SIGN_ONEWAY_LEFT_T = 313,        /**< 向左单行路 */
    LOC_TRAFFIC_SIGN_ONEWAY_STRAIGHT_T = 314,    /**< 直行单行路 */
    LOC_TRAFFIC_SIGN_WALK_T = 315,               /**< 步行 */
    LOC_TRAFFIC_SIGN_HONK_T = 316,               /**< 鸣喇叭 */
    LOC_TRAFFIC_SIGN_PRIORITY_AT_INTERSECTION_T = 317, /**< 路口优先通行 */
    LOC_TRAFFIC_SIGN_PRIORITY_OVER_ONCOMING_T = 318,   /**< 会车让行 */
    LOC_TRAFFIC_SIGN_PEDESTRIAN_CROSSING_T = 319,      /**< 人行横道 */
    LOC_TRAFFIC_SIGN_RIGHT_TURN_LANE_T = 320,          /**< 右转车道 */
    LOC_TRAFFIC_SIGN_LEFT_TURN_LANE_T = 321,           /**< 左转车道 */
    LOC_TRAFFIC_SIGN_STRAIGHT_LANE_T = 322,            /**< 直行车道 */
    LOC_TRAFFIC_SIGN_STRAIGHT_RIGHT_TURN_LANE_T = 323, /**< 直行和右转合用车道 */
    LOC_TRAFFIC_SIGN_STRAIGHT_LEFT_TURN_LANE_T = 324,  /**<  直行和左转合用车道 */
    LOC_TRAFFIC_SIGN_U_TURN_LANE_T = 325,              /**< 调头车道 */
    LOC_TRAFFIC_SIGN_U_LEFT_TURN_LANE_T = 326,         /**< 调头和左转合用车道 */
    LOC_TRAFFIC_SIGN_ROAD_DIVIDES_T = 327,             /**< 分向行驶车道 */
    LOC_TRAFFIC_SIGN_BUS_LANE_T = 328,                 /**< 公交专用道 */
    LOC_TRAFFIC_SIGN_MOTOR_VEHICLES_T = 329,           /**< 机动车行驶 */
    LOC_TRAFFIC_SIGN_MOTOR_VEHICLES_LANE_T = 330,      /**< 机动车车道 */
    LOC_TRAFFIC_SIGN_NON_MOTOR_VEHICLES_T = 331,       /**< 非机动车行驶 */
    LOC_TRAFFIC_SIGN_NON_MOTOR_VEHICLES_LANE_T = 332,  /**< 非机动车车道 */
    LOC_TRAFFIC_SIGN_HIGH_OCCUPANCY_VEHICLE_LANE_T = 333,/**< 多乘员车辆专用道 */
    LOC_TRAFFIC_SIGN_PARKING_SPACE_T = 334,            /**< 停车位 */
    LOC_TRAFFIC_SIGN_ALLOW_U_TURN_T = 335,             /**< 允许调头 */
    LOC_TRAFFIC_SIGN_CARRIAGEWAY_T = 336,              /**< 行车道 */
    LOC_TRAFFIC_SIGN_EMERGENCY_LANE_T = 337,           /**< 应急车道 */
    LOC_TRAFFIC_SIGN_SIGNPOST_T = 400,                 /**< 指路标识 */
    LOC_TRAFFIC_SIGN_TOURIST_AREA_T = 500,             /**< 旅游区 */
    LOC_TRAFFIC_SIGN_ROAD_WORKS_FLAG_T = 600,          /**< 道路施工 */
    LOC_TRAFFIC_SIGN_COMPOUND_SYMBOL_T = 700,          /**< 复合型标识 */
    LOC_TRAFFIC_SIGN_AUXILIARY_SYMBOL_T = 800,         /**< 辅助标识 */
    LOC_TRAFFIC_SIGN_VARIBALE_INFORMATION_T = 900,     /**< 可变信息标识  */
    LOC_TRAFFIC_SIGN_INDUCTION_T = 1000,               /**< 诱导屏 */
    LOC_TRAFFIC_SIGN_LED_DOT_MATRIX_T = 1001,          /**< LED点阵屏 */
    LOC_TRAFFIC_SIGN_STRIPE_SCREEN_T = 1002,           /**< 条带屏 */
    LOC_TRAFFIC_SIGN_COMPOSITE_PANEL_T = 1003,         /**< 复合屏 */
    LOC_TRAFFIC_SIGN_ADVERTISING_BOARD_T = 1004,       /**< 广告牌 */
    LOC_TRAFFIC_SIGN_COUNT_T = 0xFFFFFFFF
};
/**
* 与视觉目标识别相关的自车坐标系为：
* 以单目摄像头为坐标原点，沿车身横向向右为x轴正方向，
* 沿车身纵向向前为y轴正方向，向上为z轴正方向。
*/

/**
* @brief 交通标志牌内容类型。
*/
enum LocTrafficSignContentTypeT {
    LOC_TRAFFIC_SIGN_CONTENT_TYPE_UNKNOWN_T = -1, /**< 无效 */
    LOC_TRAFFIC_SIGN_CONTENT_TYPE_QRCODE_T = 0,   /**< QR Code */
    LOC_TRAFFIC_SIGN_CONTENT_TYPE_NUMBER_T = 1,   /**< 数字 */
    LOC_TRAFFIC_SIGN_CONTENT_TYPE_TEXT_T = 2,     /**< 文本 */
};

/**
 * @brief 交通标志字符最大存放有效长度。
 * 
 */
const int MAX_TRAFFIC_SIGN_TEXT_LEN_T = 32;

/**
* @brief 交通标志牌内容。
* @see LocTrafficSignContentTypeT
*/
struct LocTrafficSignContentT {
    LocTrafficSignContentTypeT type;            /**< 类型。@see LocTrafficSignContentTypeT */
    union {
        unsigned short number;                  /**< 数字 */
        unsigned long long qrCode;              /**< QR Code */
        char text[MAX_TRAFFIC_SIGN_TEXT_LEN_T]; /**< 文本，最多存放(MAX_TRAFFIC_SIGN_TEXT_LEN_T - 1)个有效字符 */
    };
};

/**
* @brief 交通标志牌识别结果。
* @see LocTrafficSignTypeT
* @see LocTrafficSignContentT
*/
struct LocTrafficSignT {
    LocTrafficSignTypeT type;  /**< 类型。@see LocTrafficSignTypeT */
    LocTrafficSignContentT content; /**< 内容。@see LocTrafficSignContentT */
    float x; /**< 中心点x轴坐标，单位米。 */
    float y; /**< 中心点y轴坐标，单位米。 */
    float z; /**< 中心点z轴坐标，单位米。 */
    float height; /**< 高度，单位米。 */
    float width;  /**< 宽度，单位米。 */
    float accuracy; /**< 中心点坐标的精度半径，单位米。 */
};

/**
* @brief 杆状物类型。
*/
enum LocPoleTypeT {
    LOC_POLE_GUARDRAIL_POST_T = 0,  /**< 护栏杆 */
    LOC_POLE_LIGHT_POLE_T = 1,      /**< 灯杆 */
    LOC_POLE_DELINEATOR_POST_T = 2, /**< 路牌杆 */
    LOC_POLE_REFLECTOR_POST_T = 3,  /**< 反光镜杆 */
    LOC_POLE_GANTRY_POLE_T = 4,     /**< 龙门架杆 */
    LOC_POLE_SIGNPOST_T = 5,        /**< 指示牌杆 */
    LOC_POLE_OTHER_T = 6,           /**< 其他杆状物 */
    LOC_POLE_UNCLASSIFIED_T = 7,    /**< 未分类 */
    LOC_POLE_COUNT_T = 0xFF         /**< 无效值 */
};

/**
* @brief 杆状物识别结果。
* @see LocPoleTypeT
*/
struct LocPoleT {
    LocPoleTypeT type; /**< 类型。@see LocPoleTypeT*/
    float bx; /**< 底部x轴坐标，单位米。 */
    float by; /**< 底部y轴坐标，单位米。 */
    float bz; /**< 底部z轴坐标，单位米。 */
    float tx; /**< 顶部x轴坐标，单位米。 */
    float ty; /**< 顶部y轴坐标，单位米。 */
    float tz; /**< 顶部z轴坐标，单位米。 */
    float height;   /**< 高度，单位米。 */
    float diameter; /**< 半径，单位米。 */
    float accuracy; /**< 置信度，单位米。 */
};

/**
* @brief 地面标识类型。
*/
enum LocMarkingTypeT {
    LOC_MARKING_NONE_T = 0,                       /**< 未制作 */
    LOC_MARKING_OTHER_T = 99,                     /**< 其他 */
    LOC_MARKING_TEXT_T = 100,                     /**< 文字 */
    LOC_MARKING_ARROW_T = 200,                    /**< 箭头(其他或无需细分类) */
    LOC_MARKING_STRAIGHT_T = 201,                 /**< 直行 */
    LOC_MARKING_STRAIGHT_OR_LEFT_T = 202,         /**< 直行或左转 */
    LOC_MARKING_STRAIGHT_OR_RIGHT_T = 203,        /**< 直行或右转 */
    LOC_MARKING_STRAIGHT_U_TURN_T = 204,          /**< 直行或调头 */
    LOC_MARKING_LEFT_TURN_T = 205,                /**< 左转 */
    LOC_MARKING_LEFT_TURN_U_TURN_T = 206,         /**< 左转或调头 */
    LOC_MARKING_LEFT_TURN_AND_INTERFLOW_T = 207,  /**< 左转或向左合流 */
    LOC_MARKING_RIGHT_TURN_T = 208,               /**< 右转 */
    LOC_MARKING_RIGHT_TURN_AND_INTERFLOW_T = 209, /**< 右转或向右合流 */
    LOC_MARKING_LEFT_RIGHT_TURN_T = 210,          /**< 左右转弯 */
    LOC_MARKING_U_TURN_T = 211,                   /**< 调头 */
    LOC_MARKING_NO_LEFT_TURN_T = 212,             /**< 禁止左弯标识 */
    LOC_MARKING_NO_RIGHT_TURN_T = 213,            /**< 禁止右弯标识 */
    LOC_MARKING_NO_U_TURN_T = 214,                /**< 禁止调头标记 */
    LOC_MARKING_STRAIGHT_LEFT_RIGHT_T = 215,      /**< 直行或左转或右转 */
    LOC_MARKING_STRAIGHT_U_LEFT_T = 216,          /**< 直行或调头或左转 */
    LOC_MARKING_RIGHT_U_TURN_T = 217,             /**< 右转或调头 */
    LOC_MARKING_NUM_T = 300,                      /**< 数字(其他或无需细分类) */
    LOC_MARKING_MAX_SPEED_LIMIT_T = 301,          /**< 最大限速 */
    LOC_MARKING_MIN_SPEED_LIMIT_T = 302,          /**< 最小限速 */
    LOC_MARKING_TIME_T = 303,                     /**< 时间 */
    LOC_MARKING_SYMBOL_T = 400,                   /**< 符号(其他或无需细分类) */
    LOC_MARKING_SEPCIAL_SYMBOL_T = 401,           /**< 特殊图文符号 */
    LOC_MARKING_SLOWDOWN_T = 402,                 /**< 减速让行 */
    LOC_MARKING_PEDESTRIAN_WARNING_T = 403,       /**< 人行道警示 */
    LOC_MARKING_OTHER_MARKING_T = 500,            /**< 其他标线(其他或无需细分类) */
    LOC_MARKING_SLOWDONW_ANTISKID_T = 501,        /**< 减速防滑带 */
    LOC_MARKING_ROAD_HUMP_T = 502,                /**< 减速丘 */
    LOC_MARKING_CHECK_FOLLOWING_DISTANCE_T = 503, /**< 车距确认线 */
    LOC_MARKING_PARKING_T = 504,                  /**< 停车位标线 */
    LOC_MARKING_STOP_TO_GIVEWAY_T = 505,          /**< 停车让行线 */
    LOC_MARKING_SLOWDOWN_TO_GIVEWAY_T = 506,      /**< 减速让行线 */
    LOC_MARKING_STOP_MARK_T = 507,                /**< 停止线 */
    LOC_MARKING_PEDESTRIAN_ISLAND_T = 508,        /**< 行人安全岛 */
    LOC_MARKING_NETS_T = 509,                     /**< 网状线 */
    LOC_MARKING_BUS_STOP_T = 510,                 /**< 停靠站标线 */
    LOC_MARKING_DECELERATION_STRIP_T = 511,       /**< (固定)减速条状物 */
    LOC_MARKING_VIRTUAL_STOP_LINE_T = 512,        /**< 虚拟停止线 */
    LOC_MARKING_DIVERSION_KERB_T = 600,           /**< 导流带(其他或无需细分类) */
    LOC_MARKING_STRAIGHT_LINE_DIVERSION_KERB_T = 601, /**< 直线导流带 */
    LOC_MARKING_BROKEN_LINE_DIVERSION_KERB_T = 602,   /**< 折线导流带 */
    LOC_MARKING_GREEN_BELT_T = 700,                   /**< 绿化带 */
    LOC_MARKING_ISOLATION_BELT_T = 800,               /**< 隔离带 */
    LOC_MARKING_COUNT_T = 0xFFFFFFFF                    /**< 无效值 */
};

/**
* @brief 地面标识识别结果。
* @see LocMarkingTypeT
*/
struct LocMarkingT {
    LocMarkingTypeT type;  /**< 类型。 @see LocMarkingTypeT*/
    float x; /**< 中心点x轴坐标，单位米。 */
    float y; /**< 中心点y轴坐标，单位米。 */
    float z; /**< 中心点z轴坐标，单位米。 */
    float width;    /**< 宽度，单位米。 */
    float height;   /**< 高度，单位米。 */
    float accuracy; /**< 中心点坐标的精度半径，单位米。 */
};

/**
* @brief 交通灯识别结果。
*/
struct LocTrafficLightT {
    float x; /**< 中心点x轴坐标，单位米。 */
    float y; /**< 中心点y轴坐标，单位米。 */
    float z; /**< 中心点z轴坐标，单位米。 */
    float width;    /**< 宽度，单位米。 */
    float height;   /**< 高度，单位米。 */
    float accuracy; /**< 中心点坐标的精度半径，单位米。 */
};

struct LocWallT
{

};

struct LocOverheadT
{

};

struct LocBarrierT
{

};

#define MAX_TRAFFIC_SIGN_CNT (8)
#define MAX_POLE_CNT (8)
#define MAX_MARKING_CNT (8)
#define MAX_TRAFFIC_LIGHT_CNT (8)
#define MAX_WALL_CNT (4)
#define MAX_OVERHEAD_CNT (4)
#define MAX_BARRIER_CNT (4)

/**
* @brief 单帧图像中的目标识别结果。
* @see LocTrafficSignT
* @see LocPoleT
* @see LocMarkingT
* @see LocTrafficLightT
* @see LocWallT
* @see LocOverheadT
* @see LocBarrierT
*/
struct LocVisualObjT {
    LocDataTypeT dataType; /**< 本结构体所表示的数据类型，赋值：LocDataVisualObj_T。 @see LocDataVisualObj_T */	
    unsigned long long ticktime;  /**< 时间戳。@note 系统滴答数（相对系统启动时间）。@note 某些系统取不到系统滴答数时取1970年01月01日00时00分00秒以来的时间戳。@note 单位：毫秒 */
    signed int cntTrafficSigns; /**< 交通标志牌个数。 */
    LocTrafficSignT trafficSigns[MAX_TRAFFIC_SIGN_CNT]; /**< 交通标志牌详细识别结果。 @see LocTrafficSignT */
    signed int cntPoles; /**< 杆状物个数。 */
    LocPoleT poles[MAX_POLE_CNT]; /**< 杆状物详细识别结果。@see LocPoleT */
    signed int cntMarkings;  /**< 地面标识个数。 */
    LocMarkingT markings[MAX_MARKING_CNT]; /**< 地面标识详细识别结果。@see LocMarkingT */
    signed int cntTrafficLights; /**< 交通灯个数。 */
    LocTrafficLightT trafficLights[MAX_TRAFFIC_LIGHT_CNT]; /**< 交通灯详细识别结果。 @see LocTrafficLightT*/
    signed int cntWalls; /**< 墙体个数。 */
    LocWallT walls[MAX_WALL_CNT]; /**< 墙体详细识别结果。@see LocWallT */
    signed int cntOverheads; /**< 暂未定义，赋值0。 */
    LocOverheadT overheads[MAX_OVERHEAD_CNT]; /**< 暂未定义，无需赋值。 @see LocOverheadT */
    signed int cntBarriers; /**< 暂未定义，赋值0。 */
    LocBarrierT barriers[MAX_BARRIER_CNT]; /**< 暂未定义，无需赋值。@see LocBarrierT */
    unsigned long long  tickTimeRecv; /**< 仅在高精回放模式下有用，填充从loc日志中记录的本地时间。@note 单位：毫秒 */
};

/**
 * @brief 定位输出信息。
 * @note 仅日志回放模式下使用
 *
 */
struct LocGLFT {
    LocDataTypeT dataType; /**< 本结构体所表示的数据类型 */
    unsigned long long ticktime;
    unsigned long long ticktimeUTC;  //UTC时间
    unsigned long long recordIndex;
};

/**
 * @brief 定位隧道构造信息。 
 * @note 仅日志回放模式下使用
 */
struct LocTNLT {
    LocDataTypeT dataType; /**< 本结构体所表示的数据类型 */
    unsigned long long ticktime;
};

/**
 * @brief 定位引擎拷贝次数 (还原性需要)
 * @note 仅日志回放模式下使用
 */
struct LocCPYT {
    LocDataTypeT dataType;
    unsigned long long recordIndex;
};

/**
* @brief 定位以及传感器信息。
* @see LocGyroT
* @see LocPulseT
* @see LocAcce3dT
* @see LocGnssT
* @see LocGpgsvT
* @see LocW4MT
* @see LocVisionT
* @see LocVisualObjT
* @see LocGLFT
* @see LocTNLT
* @see LocCPYT
*/
union LocSignDataT {
	LocDataTypeT  dataType;      /**< 本联合体中存储的信号类型。 @see LocDataTypeT @note GNSS模式：必填 @note 后端融合粗匹配模式：必填 @note 后端融合高精模式：必填*/
	LocGyroT      gyro;          /**< 陀螺数据。 @see LocGyroT @note GNSS模式：选填 @note 后端融合粗匹配模式：必填 @note 后端融合高精模式：必填*/
    LocPulseT     pulse;         /**< 脉冲数据。 @see LocPulseT @note GNSS模式：选填 @note 后端融合粗匹配模式：必填 @note 后端融合高精模式：必填*/
	LocAcce3dT    acce3D;        /**< 3D加速度计。 @see LocAcce3dT @note GNSS模式：选填 @note 后端融合粗匹配模式：必填 @note 后端融合高精模式：必填*/
	LocGnssT      gnss;          /**< 卫星定位信号。 @see LocGnssT @note GNSS模式：必填 @note 后端融合粗匹配模式：必填 @note 后端融合高精模式：必填*/
    LocGpgsvT     gpgsv;         /**< 卫星星历数据。 @see LocGpgsvT @note GNSS模式：选填 @note 后端融合粗匹配模式：必填 @note 后端融合高精模式：必填*/
    LocW4MT       w4m;           /**< 四轮速模型数据。 @see LocW4MT @note GNSS模式：选填 @note 后端融合粗匹配模式：选填 @note 后端融合高精模式：选填*/  
    LocVisionT    locVision;     /**< 视觉识别数据。 @see LocVisionT @note GNSS模式：选填 @note 后端融合粗匹配模式：选填 @note 后端融合高精模式：必填*/
    LocVisualObjT locVisualObj; /**< 视觉目标识别结果，路牌路标，杆状物，面状物等。 @see LocVisualObjT @note GNSS模式：选填 @note 后端融合粗匹配模式：选填 @note 后端融合高精模式：选填*/
    LocGLFT       locGLF;       /**< 定位输出信息，仅日志回放模式下使用。 @see LocGLFT */
    LocSpdDirT    spdDir;       /**< 车轮移动方向数据。 @see LocSpdDirT @note GNSS模式：选填 @note 后端融合粗匹配模式：必填 @note 后端融合高精模式：必填*/
    LocTNLT       locTNL;       /**< 定位隧道构造信息，仅日志回放模式下使用。 @see LocTNLT */
    LocCPYT       locCPY;       /**< 定位引擎拷贝次数 (还原性需要)，仅日志回放模式下使用 @see LocCPYT */
};


#endif
