/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDDeviceDefines.h"

namespace hdmap {
namespace service {

/**
* 设备相关服务类，集成方可以通过此类来更新系统信息
*/
class GHDMAP_API GHDDeviceService
{
protected:
    GHDDeviceService() {}
public:
    virtual ~GHDDeviceService() {}
    
public:
    /**
    * 获取设备服务对象
    * @return 设备服务对象实例
    */
    static GHDDeviceService* getService();
    
    /**
     * 更新设备网络状态接口
     *
     * @param[in] state 网络状态
     */
    virtual void updateNetworkState(NetworkState state) = 0;
    
    /**
     * 更新日志上传开关接口, 本方法必须在SDK初始化函数 GHDMapService::init 之后调用。
     *
     * @param[in] enable true表示打开，false表示关闭
     */
    virtual void updateLogsEnable(bool enable) = 0;
    
};

}
}
