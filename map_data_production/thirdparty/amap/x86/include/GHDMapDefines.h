/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include <stdlib.h>
#include <stddef.h>
#include <stdint.h>
#include <string>
#include <vector>

#if defined WIN32 || defined __CYGWIN__
#ifdef GEHP_EXPORT
#ifdef __GNUC__
#define GHDMAP_API __attribute__((dllexport))
#else
#define GHDMAP_API __declspec(dllexport)  // Note: actually gcc seems to also supports this syntax.
#endif

#else  // GEHP_EXPORT

#ifdef __GNUC__
#define GHDMAP_API __attribute__((dllimport))
#else
#define GHDMAP_API __declspec(dllimport)  // Note: actually gcc seems to also supports this syntax.
#endif
#endif  // else
#define DLL_LOCAL
#else
#if __GNUC__ >= 4
#define GHDMAP_API __attribute__((visibility("default")))
#define DLL_LOCAL __attribute__((visibility("hidden")))
#else
#define GHDMAP_API "do not suport"
#define DLL_LOCAL "do not suport"
#endif
#endif

#ifdef __GNUC__
    #define GHD_UNUSED __attribute__((unused))
#else
    #define GHD_UNUSED
#endif

#if (defined(__GNUC__) && (__GNUC__ * 100 + __GNUC_MINOR__ >= 405)) || defined(__clang__)
#  define GHD_DEPRECATED_FROM(x) __attribute__((deprecated("Deprecated from verson: " #x)))
#elif  defined(__GNUC__)
#  define GHD_DEPRECATED_FROM(x) __attribute__((deprecated))
#elif defined(_MSC_VER) && _MSC_VER >= 1500
#  define GHD_DEPRECATED_FROM(x) __declspec(deprecated("Deprecated from verson: " #x))
#elif defined(_MSC_VER)
#  define GHD_DEPRECATED_FROM(x) __declspec(deprecated)
#else
#  define GHD_DEPRECATED_FROM(x)
#endif

namespace hdmap {
namespace service {

/**
 * SDK初始化配置类
 */
struct GHDMAP_API HDMapConfig {
    HDMapConfig() {
        reset();
    }
    
    /**
     * 重置配置
     */
    void reset() {
        configFilePath.clear();
        basicDataRootDir.clear();
        logRootDir.clear();
        extraMapDataDir.clear();
        vehicleType.clear();
    }
    
    /**
     * 判断配置是否有效
     * @return true 有效，false 无效
    */
    bool isConfigValid() const {
        if (basicDataRootDir.empty()) {
            return false;
        }
        return true;
    }
    
    std::string basicDataRootDir;     ///< 必填参数, SDK运行根目录
    std::string extraMapDataDir;      ///< 选填参数, 扩展数据路径
    std::string configFilePath;       ///< 选填参数, 配置文件路径
    std::string logRootDir;           ///< 选填参数, 日志根路径
    std::string vehicleType;          ///< 选填参数, 车型
};

/**
 * 设置配置结果
 */
enum ConfigResult {
    CONFIG_RESULT_SUCCESS,      ///< 成功
    CONFIG_RESULT_ERR_UNDEFINE, ///< 未定义错误
    CONFIG_RESULT_ERR_KEY,      ///< 配置键值错误
    CONFIG_RESULT_ERR_VALUE,    ///< 配置值错误
};

static const int32_t DEFAULT_LOC_LON = 181 * 10000000;
static const int32_t DEFAULT_LOC_LAT = 91 * 10000000;

}
}
