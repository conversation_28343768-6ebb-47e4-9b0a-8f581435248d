/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GMapAPIReceiver.h"

namespace hdmap {
namespace service {


/**
 * MapAPI服务类
 */
class GHDMAP_API GHDMapAPIService
{
protected:
    GHDMapAPIService() {}
public:
    virtual ~GHDMapAPIService() {}
    
public:
    /**
     * 获取MapAPI服务实例接口
     * @return MapAPI服务实例
     */
    static GHDMapAPIService* getService();
    
    /**
     * 启动MapAPI服务接口
     *
     * @param[in] receiver MapAPI数据接收者，用于接收导航路径等数据
     * @return true启动成功，false表示启动失败
     *
     */
    virtual bool start(IMapAPIReceiver* receiver) = 0;
    
    
};

}
}
