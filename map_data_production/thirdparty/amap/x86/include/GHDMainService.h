/*
* Copyright (C) 2019 - 2024 Amap Auto
*/

#pragma once

#include "GHDMapDefines.h"
#include "GHDDeviceService.h"
#include "GHDDiagnosisService.h"
#include "GHDOTAService.h"
#include "GHDTileUpdateService.h"

namespace hdmap {
namespace service {

/**
 * SDK 主服务类
 */
class GHDMAP_API GHDMainService
{
protected:
    GHDMainService() {}
    
public:
    /**
     * 初始化接口
     *
     * @param[in] config 初始化所需配置信息，主要包括数据根路径、日志路径等。
     * @param[in] device 系统接口实例，SDK通过此实例获取系统参数。因为部分系统参数为SDK运行所必须的，因此该系统接口实例不可为空，否则会初始化失败。
     * @param[in] diagnosisReceiver 诊断信息接收者。如不需要接收诊断信息，该参数可设置为空。
     * @param[in] otaReceiver 地图数据更新接收者。如不涉及地图数据OTA更新或无需接收数据OTA信息，该参数可设置为空。
     *
     * @return true表示初始化成功，false表示初始化失败。如返回失败可检查config或device参数是否设置正确，详情可参考控制台打印信息。
     */
    static bool init(const HDMapConfig& config, ISystemDevice* device, IHDMapDiagnosisReceiver* diagnosisReceiver, IDOTAReceiver* otaReceiver);
    
    /**
     * 反初始化接口，需要与init接口配对调用
     *
     * @return true表示反初始化成功，false表示反初始化失败
     */
    static bool deInit();
    
    /**
     * 输入定位信息接口
     *
     * @param[in] signData 定位信息
     */
    static void setLocSignal(const LocSignDataT& signData);
    
    /**
     * 设置配置接口
     *
     * @param[in] key 配置名称\n
     *  "versionSync.value" 数据版本同步配置
     *
     * @param[in] value 配置值
     * |key                  |  value\n
     * |---------------------|----------------------------------------------------------\n
     * |"versionSync.value"  |  bit0 : 0 -> 关闭ehpv3数据版本同步; 1 -> 打开ehpv3数据版本同步\n
     * |                     |  bit1 : 0 -> 关闭mapapi数据版本同步; 1 -> 打开mapapi数据版本同步\n
     * |                     |  bit2~bit31 : 保留位\n
     * |---------------------------------------------------------------------------------\n
     * @return  设置配置结果
     */
    static ConfigResult setConfiguration(const char* key, uint32_t value);
    
    /**
     * 获取SDK版本号接口
     * @return  版本号
     */
    static const char* sdkVersion();
};

}
}
