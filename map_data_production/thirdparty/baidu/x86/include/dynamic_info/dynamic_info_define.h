#pragma once
#include <stdint.h>
#include <vector>
#include <string>

namespace baidu {
namespace imap {

// 交通事件类型
enum class TrafficEventType : uint16_t{
    TRAFFIC_JAM = 100,      // 路况
    TRAFFIC_ACCIDENT = 102, // 交通事故
    TRAFFIC_CONTROL = 103,  // 交通管制
    ROAD_CLOSURE =  104,    // 封路
    DANGER = 107,           // 危险
    CONSTRUCTION = 110,     // 施工
    BLOCKADE = 112,         // 交通阻断
    ROAD_COLLAPSE = 201,    // 路面塌陷
    ICING_EVENT = 403,      // 结冰
    SNOW_EVENT = 404,       // 积雪
    SEEPER_EVENT = 405      // 积水
};

//路况状态
enum class TrafficJamStatus : uint8_t{
    FREE = 1,   //畅通
    SLOW,       //缓行
    JAM,        //拥堵
    EXTREME_JAM //极度拥堵
};

//动态事件模式
enum DynamicEventModel
{
    DYNAMIC_EVENT_MODEL_LINK = 1,          //道路级
    DYNAMIC_EVENT_MODEL_LANE = 2,          //车道级
};

// 紧急情况类型
enum EmergenceInfoType {
    TYPHOON = 901,      // 台风
    RAINSTROM,          // 暴雨
    BLIZZARD,           // 暴雪
    COLDWARE,           // 寒潮
    GALE,               // 大风
    SANDSTORM,          // 沙尘暴
    HIGH_TEMPERATURE,   // 高温
    DROUGH,             // 干旱
    THUNDER,            // 雷电
    HAIL,               // 冰雹
    FROST,              // 霜冻
    DENSEFOG,           // 大雾
    HAZE,               // 霾
    ICYROAD = 914       // 道路结冰
};

// 交通信息
struct TrafficData
{
    int32_t road_id;        //道路ID
    int32_t lane_index;     //按照道路的通向方向，从左往右数第几个车道，以此论推，1表示第1个车道，2表示第2个车道
    uint32_t s_offset;      //起点距离自身所在的link的偏移
    double s_lat;		    //起点纬度
    double s_lon;		    //起点经度
    uint32_t e_offset;		//终点距离自身所在的link的偏移
    double e_lat;		    //终点纬度
    double e_lon;		    //终点经度

    TrafficData() : road_id(0), lane_index(0), 
                    s_offset(0), s_lat(0), s_lon(0), 
                    e_offset(0), e_lat(0), e_lon(0){
    }
};

// 交通事件
struct TrafficEventInfo
{
    TrafficEventType event_type;    //事件类型
    DynamicEventModel event_model;  //动态事件的模式
    std::string event_s_time;       //动态事件的开始时间，采用北京时区，时间格式类似为“ 2019-10-23 6:00 UTC+8 ”
    std::string event_e_time;       //动态事件的结束时间，采用北京时区，时间格式类似为“2019-10-23 6:00 UTC+8 ”
    TrafficJamStatus jam_status;    //路况状态，事件类型为路况时，该字段才有效
    double average_speed;           //平均速度，单位KM/H，事件类型为路况时，该字段才有效
    double experience_speed;        //经验限速，不为0时有效。
    std::vector<TrafficData> datas; //交通信息

    TrafficEventInfo() : event_type(TrafficEventType(0)), event_model(DynamicEventModel(0)),
                        jam_status(TrafficJamStatus(0)), average_speed(0),experience_speed(0) {
    }
};
typedef std::vector<TrafficEventInfo> TrafficEventInfos;

// 天气信息
struct WeatherInfo
{
    int32_t precipitation;  //降水量
    int wind_direction;     //风向（0 无固定风向；1 东北风；2 东风；3 东南风；4 南风；5 西南风；6 西风；7 西北风；8 北风；9 旋转风）
    int wind_class;         //风力等级（n代表n级风力，比如1代表1级风力）
    int weather;            //天气现象(0 晴；1 多云；2 阴；3 阵雨；4 雷阵雨；5 雷阵雨伴有冰雹；6 雨夹雪；7 小雨；8 中雨；9 大雨；
                            //10 暴雨；11 大暴雨；12 特大暴雨；13 阵雪；14 小雪；15 中雪；16 大雪；17 暴雪；18 雾；19 冻雨；20 沙尘暴；
                            //21 小到中雨；22 中到大雨；23 大到暴雨；24 暴雨到大暴雨；25 大暴雨到特大暴雨；26 小到中雪；27 中到大雪；
                            //28 大到暴雪；29 浮尘；30 扬尘；31 强沙尘暴；32 浓雾；33 龙卷风；34 弱高吹雪；35 轻雾；49 强浓雾；53 霾；
                            //54 中度霾；55 重度霾；56 严重霾；57 大雾；58 特大浓雾)

    WeatherInfo() : precipitation(0), wind_direction(0), 
                wind_class(0), weather(0) {
    }
};

// 紧急情况
typedef std::vector<EmergenceInfoType> EmergenceInfo;
}
}