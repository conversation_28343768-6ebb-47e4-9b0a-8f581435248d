#pragma once

namespace baidu{
namespace imap{

enum IMAP_DTC_CODE{
    IMAP_DTC_NONE = 0X13AD0001
};

enum DTCErrorCode {    
    DTC_HD_EE_OPEN_DB_FAILED     = 3001,
    DTC_HD_EE_LOSS_OF_MAP        = 3002,
    DTC_HD_EE_IO_ERROR           = 3003,
    DTC_HD_NO_MAP_AROUND         = 3004,
    DTC_HD_MAP_AROUND            = 3005,
    DTC_HD_EE_CACHE_FULL         = 3006,
    DTC_HD_EE_CACHE_OK           = 3007,
    DTC_HD_EE_RESERVE            = 3499,

    DTC_HD_OTA_NETWORK_TIMEOUT   = 4501,
    DTC_HD_OTA_NETWORK_ERROR     = 4502,
    DTC_HD_OTA_NETWORK_OK        = 4503,    
    DTC_HD_OTA_RESERVE           = 4999,

    DTC_HD_LICENSE_EXPIRED       = 6001,
    DTC_HD_LICENSE_NOT_ACTIVATED = 6003,
    DTC_HD_LICENSE_SUCC          = 6004
};

}
}
