#pragma once

#include <stdint.h>
#include <sstream>

namespace message {

struct MapDtcData {
    std::string m_category;// ＤＴＣ类别
    int32_t m_dtc_no;// ＤＴＣ错误码
    std::string m_message;// 信息描述

    MapDtcData() : m_category(""), m_dtc_no(0), m_message("") {
    }

    void set_category(const std::string& v) {
        m_category = v;
    } 
    
    void set_dtc_no(int32_t v) {
        m_dtc_no = v;
    }

    void set_message(const std::string& v) {
        m_message = v;
    }
    std::string category() const {
        return m_category;
    }
    int32_t dtc_no() const {
        return m_dtc_no;
    }
    const std::string& message() const {
        return m_message;
    }
};

}
