#pragma once

#include <map>
#include <stdint.h>
#include <vector>

#ifndef __ANDROID__
#include "map_engine/hdmap_define.h"
#endif

namespace baidu
{
namespace imap
{

typedef std::vector<std::vector<uint32_t>> MppSegment;

enum NoaStatus : uint8_t
{
    NOA_STATUS_OK = 0,
    NOA_STATUS_LOW_CONFIDENCE = 1,
    NOA_STATUS_OFF_WAY = 2,
    NOA_STATUS_MPP_INSUFFICIENT = 3,
    NOA_STATUS_NOT_INIT = 4,
    NOA_STATUS_INVALID_LOCATION = 5,
    NOA_STATUS_NOT_IN_NAVIGATION = 6,
    NOA_STATUS_HD_END = 7,
    NOA_STATUS_SD_END = 8,
    NOA_STATUS_INNER_ERROR
};

enum NavigationStatus : uint8_t
{
    // Navigation system status definition 1 :  Path planning 2 :  Navigation 3 :  Cruise 4 :  Yaw    5 :  Re planning
    NS_NOT_HAVE_NAVIGATION = 0,
    NS_NAVIGATION_PATH_CALCULATING = 1,
    NS_NAVIGATION = 2,
    NS_CRUISE = 3,
    NS_GOING_OFF_COURSE = 4,
    NS_RE_PLANNING = 5
};

/// include SD data and EHP Judge
enum MatchingStatus : uint8_t
{
    MS_MATCHING_DEFAULT = 0,
    MS_MATCHING_TABLE_OK = 1,
    MS_SD_MAP_DESTINATION_REARCH = 2,
    MS_HD_MAP_DATA_END = 3,
    MS_NAVIGATION_NO_LOCAL_ASSOCIATED_TABLE = 4,
    MS_MATCHING_TABLE_NOT_AVAILABLE = 5, //(version mismatch or matching degree < 80%
    MS_RESERVED1 = 6,
    MS_RESERVED2 = 7,
    MS_OTHERS = 8
};

enum SwitchType : uint8_t
{
    ST_NO_REASON = 0,
    ST_LANE_DIES = 1,
    ST_HIGH_SPEED_INTER_CHANGE_RAMP_AHEAD = 2,
    ST_ENTER_HIGH_SPEED_INTER_CHANGE_RAMP = 3,
    ST_HIGH_SPEED_INTER_CHANGE_EXIT_RAMP_AHEAD = 4,
    ST_EXIT_HIGH_SPEED_INTER_CHANGE_RAMP = 5,
    ST_DIVIDE_ROAD = 6,
    ST_RESERVED1 = 7,
    ST_CONTROL_LANE = 8,
    ST_RESERVED2 = 9,
    ST_NO_HIGH_SPEED_INTER_CHANGE_RAMP_AHEAD = 10,
    ST_ENTER_NO_HIGH_SPEED_INTER_CHANGE_RAMP = 11,
    ST_NO_HIGH_SPEED_INTER_CHANGE_EXIT_RAMP_AHEAD = 12,
    ST_EXIT_NO_HIGH_SPEED_INTER_CHANGE_RAMP = 13,
    ST_OFF_WAY = 14,
    ST_ETC = 15,
    ST_OTHERS
};

struct NoaState
{
    NoaState() : weight(0), switch_event(ST_NO_REASON), event_distance(0), sequence(0), origin_link(0), origin_lane(0)
    {
    }
    int8_t weight;
    SwitchType switch_event;
    int32_t event_distance; // unit : centimeter
    uint8_t sequence;       // sequece of current lane
    uint32_t origin_link;   // start link of switch event
    uint32_t origin_lane;   // start lane of switch event
};
typedef std::map<uint32_t, NoaState> NoaStates;     // key: laneid
typedef std::map<uint32_t, NoaStates> NoaStatesMap; // key: linkid

enum SwitchDirection : uint8_t
{
    SD_NO = 0,
    SD_LEFT = 1,
    SD_RIGHT = 2,
    SD_INVALID = 3
};

struct SwitchInfo
{
    // 0: No switch 1: To left 2: To right 3.INVALID
    SwitchDirection direction;

    // switch lane reason
    SwitchType reason;

    // to switch point distance
    int32_t distance; // unit : centimeter

    //特指上下IC/JCT路口场景终点距离，其他提醒类型该值与distance相等
    int32_t end_distance;

    // switch point
    Point3I point;

    // 0: No switch 1: To left 2: To right 3.INVALID
    SwitchDirection front_direction;

    //距离front_direction换道点的距离
    int32_t front_distance; // unit : centimeter

    //当前link上所有车道的状态
    NoaStates noa_states;

    NoaStatesMap noa_states_map;

    //当前link id
    uint32_t link_id;

    //当前lane id
    uint32_t lane_id;

    SwitchInfo() : noa_states(), noa_states_map()
    {
        direction = SD_NO;
        reason = ST_NO_REASON;
        distance = 0;
        end_distance = 0;
        point.x = 0;
        point.y = 0;
        point.z = 0;
        front_direction = SD_INVALID;
        front_distance = 0;
        link_id = 0;
        lane_id = 0;
    };
};

struct NavStatus
{
    uint8_t navigation_status;
    uint8_t matching_table_status;
    uint32_t remain_distance;      // unit : meter
    uint32_t via_point_distance;   // unit : meter
    uint32_t to_hd_start_distance; // unit : meter

    NavStatus()
        : navigation_status(NS_NOT_HAVE_NAVIGATION), matching_table_status(MS_MATCHING_DEFAULT), remain_distance(0),
          via_point_distance(0), to_hd_start_distance(0){};
};

struct NavSDLinkList
{
    uint64_t map_version;
    std::vector<uint64_t> linkid;
};

enum RouteUpdateStatus : uint8_t
{
    RUS_INVALID = 0,     // 占位，无效状态
    RUS_UPDATE_ROUTE = 1 // 路线变化
};

struct NavInfo
{
    uint8_t navigation_status;     //导航状态
    uint8_t matching_table_status; //匹配状态
    uint32_t remain_distance;      // 导航剩余距离
    uint32_t via_point_distance;   // unit : meter
    uint32_t to_hd_start_distance; // unit : meter

    uint64_t map_version;          // SD版本
    std::vector<uint64_t> linkid;  // SD轨迹
    
    uint8_t route_update_status;   //导航轨迹更新信息  
    std::vector<uint32_t> link_lengths;  // SD轨迹长度
    std::vector<uint32_t> link_dirs;  // SD轨迹方向
    uint64_t cur_link_id;          //当前导航link id
    uint32_t cur_link_offset;      //当前导航link offset
    uint32_t cur_link_index;      //当前导航link index

    NavInfo()
        : navigation_status(NS_NOT_HAVE_NAVIGATION), matching_table_status(MS_MATCHING_DEFAULT), remain_distance(0),
          via_point_distance(0),to_hd_start_distance(0),map_version(0), linkid(), route_update_status(RUS_INVALID), link_lengths(), 
          link_dirs(),cur_link_id(0), cur_link_offset(0), cur_link_index(0){};
};

struct MppIndex
{
    int32_t x; // 1层vector index
    int32_t y; // 2层vector index
    int32_t z; // 3层vector index
};

struct MppSegmentInfo
{
    MppSegment mpp; // mpp with 3-vector structure
    uint8_t segment_type; //0 表示该segment无ld映射， 1 表示该segment有ld映射，
    uint32_t segment_length; //当前无LD映射的 segment的长度，segment_type == 0 是生效，其他情况该值为0
    MppSegmentInfo() : mpp(), segment_type(1), segment_length(0){};
};

struct BatchMppInfo
{
    std::vector<MppSegmentInfo> mpp; // mpp segment detail info
    uint32_t dis_to_ld; //距离导航路线上有LD数据区域的距离，当前位于ld路段、无导航、无定位时，该值为0
    uint32_t mpp_remain_len;     // mpp remain length
    MppIndex mpp_index_of_car; // where car is on presented by mpp index 依赖高精定位，（-1，-1，-1）表示无效
    bool is_reach_hd_end;      // mpp reach hd end point
    bool is_reach_navi_end;        // mpp reach navi end point
    int32_t hdroute_match_status;  // 0 OK, 1 HD_LACK, etc.
    GpsCoord hdroute_broken_point; // hdroute匹配异常点位置
    BatchMppInfo() : mpp(), dis_to_ld(0), mpp_remain_len(0), is_reach_hd_end(false), is_reach_navi_end(false), hdroute_match_status(0)
    {
        mpp_index_of_car.x = -1;
        mpp_index_of_car.y = -1;
        mpp_index_of_car.z = -1;
        hdroute_broken_point.lon = 0.0;
        hdroute_broken_point.lat = 0.0;
        hdroute_broken_point.height = 0.0;
    };
};

struct MppInfo
{
    std::vector<MppSegment> mpp; // mpp with 3-vector structure
    uint32_t mpp_remain_len;     // mpp remain length
    MppIndex mpp_index_of_car; // where car is on presented by mpp index 依赖高精定位，（-1，-1，-1）表示无效
    bool is_reach_hd_end;      // mpp reach hd end point
    bool is_reach_navi_end;        // mpp reach navi end point
    int32_t hdroute_match_status;  // 0 OK, 1 HD_LACK, etc.
    GpsCoord hdroute_broken_point; // hdroute匹配异常点位置
    MppInfo() : mpp{}, mpp_remain_len(0), is_reach_hd_end(false), is_reach_navi_end(false), hdroute_match_status(0)
    {
        mpp_index_of_car.x = -1;
        mpp_index_of_car.y = -1;
        mpp_index_of_car.z = -1;
        hdroute_broken_point.lon = 0.0;
        hdroute_broken_point.lat = 0.0;
        hdroute_broken_point.height = 0.0;
    };
};

} // namespace imap
} // namespace baidu
