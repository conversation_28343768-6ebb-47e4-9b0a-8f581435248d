// #pragma once
#ifndef __HDMAP_ENUM__
#define __HDMAP_ENUM__

namespace baidu {
namespace imap {
/**
 * Link's type
 */
enum LinkType {
    LT_NORMAL               = 0X00000000, // 普通
    LT_TUNNEL               = 0X00000001, // 隧道
    LT_BRIDGE               = 0X00000002, // 桥
    LT_TOLLBOOTH            = 0X00000004, // 收费站道路
    LT_DEAD_END             = 0X00000008, // 断头路
    LT_IC                   = 0X00000010, // IC(高速连接普通路的道路)
    LT_JCT                  = 0X00000020, // JCT(高速连接高速的道路)
    LT_SAPA                 = 0X00000040, // SAPA(服务器、停车区道路)
    LT_WITHIN_INTERSECTION  = 0X00000080, // 路口内道路
    LT_AUTHORIZED           = 0X00000100, // 授权道路
    LT_TOLLGATE             = 0X00000200, // 收费岛道路
    LT_ABANDONED_TOLLGATE   = 0X00000400, // 废弃收费岛道路
    LT_CHECKPOINT           = 0X00000800, // 检查站道路
    LT_ROUNDABOUT           = 0X00001000, // 环岛内道路
    LT_SERRATED             = 0X00002000, // 锯齿道路
    LT_MAIN_ROAD            = 0X00004000, // 主路(main road)
    LT_SIDE_ROAD            = 0X00008000, // 辅路(side road)
    LT_MAINROAD_CONNECTION  = 0X00010000, // 主辅路连接路(mainside connection)
    LT_NO_S_INTERSECTION    = 0X00020000, // 无小路口
    LT_S_INTERSECTION_LEFT  = 0X00040000, // 小路口左侧道路
    LT_S_INTERSECTION_RIGHT = 0X00080000, // 小路口右侧道路
    LT_S_INTERSECTION_BOTH  = 0X00100000, // 小路口两侧道路
    LT_INJUNCTION           = 0X00200000, // 路口内道路（编译计算）
    LT_BOOTH_EXIT           = 0X00400000, // 出口收费站
    LT_BOOTH_ENTRANCE       = 0X00800000, // 入口收费站
    LT_BOOTH_EXIT_ENTRANCE  = 0X01000000  // 出入口收费站
};

/**
 * Link's Class
 */
enum LinkClass {
    LC_EXPRESSWAY       = 1,  // 高速(Highway)
    LC_URBAN_EXPRESSWAY = 2,  // 城市快速路(Urban Expressway)
    LC_NATION_ROAD      = 3,  // 国道(national road)
    LC_PROVINCE_ROAD    = 4,  // 省道(provincial road)
    LC_RESERVE          = 5,  // 预留(reserve)
    LC_COUNTY_ROAD      = 6,  // 县道(prefectural road)
    LC_TOWN_ROAD        = 7,  // 乡道(prefectural road)
    LC_SPECIAL_ROAD     = 8,  // 特服道路
    LC_WALK_ROAD        = 9,  // 步行道路
    LC_PEOPLE_FERRY     = 10, // 人渡
    LC_FERRY            = 11, // 轮渡
    LC_OTHER_ROAD       = 12  // 其他道路
};

/**
 * Link、Lane Direction
 */
enum Direction {
    IN_POSITIVE_DIRECTION       = 1, // 正向
    IN_NEGATIVE_DIRECTION       = 2, // 反向
    IN_BOTH_DIRECTIONS_PROHIBIT = 3, // 双向禁止
    IN_BOTH_DIRECTIONS          = 4  // 双向
};

enum LaneType {
    LAT_NORMAL                  = 0,  // 普通
    LAT_ENTRY                   = 1,  // 入口
    LAT_EXIT                    = 2,  // 出口
    LAT_EMERGENCY               = 3,  // 应急车道
    LAT_ON_RAMP                 = 4,  // 进入匝道
    LAT_OFF_RAMP                = 5,  // 退出匝道
    LAT_CONNECT_RAMP            = 6,  // 连接匝道
    LAT_ACCELERATE              = 7,  // 加速车道
    LAT_DECELERATE              = 8,  // 减速车道
    LAT_EMERGENCY_PARKING_STRIP = 9,  // 紧急停车带
    LAT_CLIMBING                = 10, // 爬坡车道
    LAT_ESCAPE                  = 11, // 避险车道
    LAT_DEDICATED_CUSTOMS       = 12, // 海关专用车道
    LAT_VIEWING_PLATFROM        = 13, // 观景台车道
    LAT_PARALLEL_LANE           = 14, // 平行车道
    LAT_DIVERSION               = 17, // 导流区车道
    LAT_PARKING                 = 19, // 停车车道
    LAT_LEFT_TURN_AREA          = 20, // 左转等待车道
    LAT_VARIABLE_LANE           = 21, // 可变车道
    LAT_NON_MOTOR_LANE          = 22, // 非机动车道
    LAT_BUS_STOP                = 23, // 公交停靠站
    LAT_NO_ENTRY_LANE           = 24, // 不可行驶车道
    LAT_U_TURN_LANE             = 25, // 掉头车道
    LAT_RIGHT_TURN_LANE         = 26, // 右转专用车道
    LAT_ETC_LANE                = 28, // ETC车道
    LAT_MANUAL_TOLL_LANE        = 29, // 人工收费车道
    LAT_PLAZA_LANE              = 30, // 收费站外广场车道
    LAT_HARBOR_STOP             = 31, // 港湾停靠站
    LAT_RIGHT_TURN_AREA         = 32, // 右转等待车道
    LAT_U_TURN_AREA             = 33, // 掉头等待车道
    LAT_NO_TURN_AREA            = 34, // 直行等待车道
    LAT_VIRTUAL_CONNECTED_LANE  = 35, // 虚拟连接车道
};

enum RestrictedLaneType {
    RESTRICTED_LANE_TYPE_NONE   = 0, // 无
    RESTRICTED_LANE_TYPE_TIDAL  = 1, // 潮汐车道
    RESTRICTED_LANE_TYPE_BRT    = 2, // 公交专用道
    RESTRICTED_LANE_TYPE_HOV    = 3, // HOV车道
    RESTRICTED_LANE_TYPE_TAXI   = 4, // 出租车专用道
    RESTRICTED_LANE_TYPE_OTHER  = 5  // 其他车道
};

enum LaneConnectionType {
    LAN_STATUS_NORMAL       = 0X00, // 正常
    LAN_STATUS_FORMING      = 0x02, // 新生
    LAN_STATUS_ENDING       = 0x04, // 消亡
    LAN_STATUS_RAMP_BROKEN  = 0x08, // 匝道消亡
    LAN_STATUS_SPLITING     = 0x10, // 分流
    LAN_STATUS_MERGING      = 0x20  // 合流
};

enum LaneTransition {
    LTS_NONE                  = 0,  // 普通
    LTS_MERGE                 = 1,  // 合流
    LTS_SPLIT                 = 2,  // 分流
    LTS_RIGHT_EXIT_BEGIN      = 3,  // 向右退出开始
    LTS_RIGHT_EXIT_END        = 4,  // 向右退出结束
    LTS_LEFT_EXIT_BEGIN       = 5,  // 向左退出开始
    LTS_LETF_EXIT_END         = 6,  // 向左退出结束
    LTS_RIGHT_ENTER_BEGIN     = 7,  // 向右进入开始
    LTS_RIGHT_ENTER_END       = 8,  // 向右进入结束
    LTS_LEFT_ENTER_BEGIN      = 9,  // 向左进入开始
    LTS_LETF_ENTER_END        = 10, // 向左进入结束
    LTS_LANE_END              = 11, // 车道消亡
    LTS_LANE_ADD_RIGHT        = 12, // 右侧车道增加
    LTS_LANE_ADD_LEFT         = 13, // 左侧车道增加
    LTS_RIGHT_EXIT_BEGIN_END  = 14, // 右侧退出开始及结束
    LTS_LEFT_EXIT_BEGIN_END   = 15, // 左侧退出开始及结束
    LTS_RIGHT_ENTER_BEGIN_END = 16, // 右侧进入开始及结束
    LTS_LEFT_ENTER_BEGIN_END  = 17  // 左侧进入开始及结束
};

enum TurnDirection {
    TURNDIR_STRAIGHT           = 0x01, // 直行
    TURNDIR_LEFT               = 0x02, // 左转
    TURNDIR_RIGHT              = 0x04, // 右转
    TURNDIR_TURN               = 0x08  // 掉头
};

/**
 * Lane boundary type
 */
enum DividerMarkingType {
    DMT_MARKING_NONE                              = 0,  // 无属性
    DMT_MARKING_LONG_DASHED_LINE                  = 1,  // 长虚线
    DMT_MARKING_DOUBLE_SOLID_LINE                 = 2,  // 双实线
    DMT_MARKING_SINGLE_SOLID_LINE                 = 3,  // 单实线
    DMT_MARKING_RIGHT_SOLID_LINE_LEFT_DASHED_LINE = 4,  // 右实左虚线
    DMT_MARKING_LEFT_SOLID_LINE_RIGHT_DASHED_LINE = 5,  // 左实右虚线
    DMT_MARKING_DOUBLE_DASHED_LINE                = 9,  // 双虚线
    DMT_MARKING_VIRTUAL_FIT                       = 22, // 虚拟边线（编译）
    DMT_MARKING_VIRTUAL                           = 23, // 虚拟边线（生产）
    DMT_MARKING_UNKNOWN                           = 254 // 目前未使用，用于之后扩展
};

enum RoadBoundaryType {
    ROAD_BOUNDARY_TYPE_NONE     = 0, // 无
    ROAD_BOUNDARY_TYPE_CURB     = 1, // 路沿
    ROAD_BOUNDARY_TYPE_BARRIER  = 2, // 护栏
    ROAD_BOUNDARY_TYPE_NATURAL  = 3, // 自然边界
    ROAD_BOUNDARY_TYPE_VIRTUAL  = 4, // 虚拟边界
    ROAD_BOUNDARY_TYPE_DITCH    = 5  // 沟
};

enum RoadBoundarySubType {
    ROAD_BOUNDARY_SUB_TYPE_NONE                    = 0x00, // 无

    ROAD_BOUNDARY_SUB_TYPE_CURB_CLOSE_OTHER        = 0x10, // 其他封闭地理边界
    ROAD_BOUNDARY_SUB_TYPE_CURB_HILL               = 0x11, // 山体
    ROAD_BOUNDARY_SUB_TYPE_CURB_WALL               = 0x12, // 墙体
    ROAD_BOUNDARY_SUB_TYPE_CURB_OPEN_OTHER         = 0x13, // 其他非封闭地理边界
    ROAD_BOUNDARY_SUB_TYPE_CURB_WOOD               = 0x14, // 树林
    ROAD_BOUNDARY_SUB_TYPE_CURB_LAWN               = 0x15, // 草坪
    ROAD_BOUNDARY_SUB_TYPE_CURB_GREEN_BELT         = 0x16, // 绿化带
    ROAD_BOUNDARY_SUB_TYPE_CURB_CLIFF              = 0x17, // 悬崖

    ROAD_BOUNDARY_SUB_TYPE_BARRIER_OTHER           = 0x20, // 其他护栏
    ROAD_BOUNDARY_SUB_TYPE_BARRIER_JERSEY          = 0x21, // 新泽西护栏
    ROAD_BOUNDARY_SUB_TYPE_BARRIER_CORRUGATED      = 0x22, // 波型护栏
    ROAD_BOUNDARY_SUB_TYPE_BARRIER_POLE            = 0x23, // 短柱

    ROAD_BOUNDARY_SUB_TYPE_NATURAL_CLOSE_OTHER     = 0x30, // 其他封闭地理边界
    ROAD_BOUNDARY_SUB_TYPE_NATURAL_HILL            = 0x31, // 山体
    ROAD_BOUNDARY_SUB_TYPE_NATURAL_WALL            = 0x32, // 墙体
    ROAD_BOUNDARY_SUB_TYPE_NATURAL_OPEN_OTHER      = 0x33, // 其他非封闭地理边界
    ROAD_BOUNDARY_SUB_TYPE_NATURAL_WOOD            = 0x34, // 树林
    ROAD_BOUNDARY_SUB_TYPE_NATURAL_LAWN            = 0x35, // 草坪
    ROAD_BOUNDARY_SUB_TYPE_NATURAL_GREEN_BELT      = 0x36, // 绿化带
    ROAD_BOUNDARY_SUB_TYPE_NATURAL_CLIFF           = 0x37, // 悬崖

    ROAD_BOUNDARY_SUB_TYPE_VIRTUAL_OTHR            = 0x40, // 其他虚拟边界
    ROAD_BOUNDARY_SUB_TYPE_VIRTUAL_DIVISION_ZONE   = 0x41, // 导流区

    ROAD_BOUNDARY_SUB_TYPE_DITCH_CLOSE_OTHER       = 0x50, // 其他封闭地理边界
    ROAD_BOUNDARY_SUB_TYPE_DITCH_HILL              = 0x51, // 山体
    ROAD_BOUNDARY_SUB_TYPE_DITCH_WALL              = 0x52, // 墙体
    ROAD_BOUNDARY_SUB_TYPE_DITCH_OPEN_OTHER        = 0x53, // 其他非封闭地理边界
    ROAD_BOUNDARY_SUB_TYPE_DITCH_WOOD              = 0x54, // 树林
    ROAD_BOUNDARY_SUB_TYPE_DITCH_LAWN              = 0x55, // 草坪
    ROAD_BOUNDARY_SUB_TYPE_DITCH_GREEN_BELT        = 0x56, // 绿化带
    ROAD_BOUNDARY_SUB_TYPE_DITCH_CLIFF             = 0x57  // 悬崖
};

enum DividerType {
    DIVIDER_TYPE_NONE               = 0x00, // 无
    DIVIDER_TYPE_BOLLARD            = 0x01, // 短柱
    DIVIDER_TYPE_FENCE              = 0x02, // 栅栏
    DIVIDER_TYPE_CONE               = 0x04, // 锥形警告路标
    DIVIDER_TYPE_JUMA               = 0x08, // 拒马路障
    DIVIDER_TYPE_SPEED_HUMP         = 0x10, // 减速标线
    DIVIDER_TYPE_ACCESS_DIVERSION   = 0x20, // 出入口导流区边线
    DIVIDER_TYPE_SPECIAL_VEHICLE    = 0x40  // 专用车道线
};

enum DividerSubType {
    DIVIDER_SUB_TYPE_NONE           = 0, // 无
    DIVIDER_SUB_TYPE_LEFT_RHOMBUS   = 1, // 左菱形
    DIVIDER_SUB_TYPE_RIGHT_RHOMBUS  = 2, // 右凌形
    DIVIDER_SUB_TYPE_BOTH_RHOMBUS   = 4, // 双侧菱形
    DIVIDER_SUB_TYPE_HORIZON        = 8, // 车行道横向减速标线
    DIVIDER_SUB_TYPE_TOLLGAE        = 16, // 收费广场减速标线
    DIVIDER_SUB_TYPE_VISUAL         = 32, // 错视觉标线
    DIVIDER_SUB_TYPE_YELLOW_SOLID   = 64, // 黄实线
    DIVIDER_SUB_TYPE_YELLOW_DASH    = 128 // 黄虚线
};

/**
 * Attribute Value
 */
enum DividerMaterial {
    DIVIDER_MATERIAL_UN_KNOWN                    = 0, // 未知，目前默认都是未知
    DIVIDER_MATERIAL_METAL                       = 1, // 金属
    DIVIDER_MATERIAL_CONCRETE                    = 2, // 混泥土
    DIVIDER_MATERIAL_STONE                       = 3, // 石材
    DIVIDER_MATERIAL_WOOD                        = 4, // 木材
    DIVIDER_MATERIAL_PLASTIC                     = 5, // 塑料
    DIVIDER_MATERIAL_TRANSPARENT                 = 6, // 透明的
    DIVIDER_MATERIAL_VIBRATION_MARKINGS          = 7, // 道路上的凸出标记
    DIVIDER_MATERIAL_PAINTED_VIBRATION_DIVIDER   = 8  // 涂漆分割
};

/**
 * Lane boundary marking color
 */
enum DividerColor {
    DIVIDER_COLOR_NONE          = 0,  // 无属性
    DIVIDER_COLOR_YELLOW        = 1,  // 黄色
    DIVIDER_COLOR_WHITE         = 2,  // 白色
    DIVIDER_COLOR_GREEN         = 8,  // 绿色
    DIVIDER_COLOR_BLUE          = 10, // 蓝色
    DIVIDER_COLOR_ORANGE        = 11, // 橘色
    DIVIDER_COLOR_WHITE_YELLOW  = 12, // 左白右黄
    DIVIDER_COLOR_YELLOW_WHITE  = 13  // 左黄右白
};

enum ObjectType {
    OBJECT_TYPE_NONE          = 0,  // 无
    OBJECT_TYPE_POLE          = 1,  // 杆
    OBJECT_TYPE_ROADMARK      = 2,  // 地面标线
    OBJECT_TYPE_SIGN          = 3,  // 牌
    OBJECT_TYPE_BUILDING      = 4,  // 建筑物
    OBJECT_TYPE_GANTRY        = 5,  // 龙门架
    OBJECT_TYPE_MESSAGE       = 6,  // 可变信息牌
    OBJECT_TYPE_OVERPASS      = 8,  // 桥
    OBJECT_TYPE_BILLBOARD     = 9,  // 广告牌
    OBJECT_TYPE_TUNNEL        = 10, // 隧道
    OBJECT_TYPE_CALLBOX       = 11, // 电话亭
    OBJECT_TYPE_SIGNAL        = 12, // 交通灯
    OBJECT_TYPE_MULTI_SIGNAL  = 13, // 交通灯组
    OBJECT_TYPE_SPEED_BUMP    = 14, // 减速带
    OBJECT_TYPE_COUNT_DOWN    = 15, // 倒计时牌
    OBJECT_TYPE_OBSTACLE      = 16, // 障碍物
    OBJECT_TYPE_SAFETY_ISLAND = 17, // 安全岛
    OBJECT_TYPE_BUS_STATION   = 18, // 公交车站标识面
    OBJECT_TYPE_OTHER         = 254 // 其他
};

/**
 * Object Sub Type
 */
enum ObjectSubType {
    OST_NO_SUB_TYPE                        = 0,   // 无子类

    OST_ROADMARK_ARROW                     = 1,   // 导向箭头
    OST_ROADMARK_SHADEAREA                 = 2,   // 导流区
    OST_ROADMARK_CHARACTER                 = 3,   // 文字
    OST_ROADMARK_SPEEDLIMIT                = 4,   // 地面限速 (添加地面限速)
    OST_ROADMAK_STOP_LINE                  = 5,   // 停止让行线
    OST_ROADMAK_PARKING_LINE               = 6,   // 停车让行线
    OST_ROADMAK_SLOWDOWN_LINE              = 7,   // 减速让行线
    OST_ROADMAK_PEDESTRIAN                 = 8,   // 人行横道
    OST_ROADMAK_DRIVING_RESTRICTION        = 9,   // 通行限制
    OST_ROADMAK_SPEED_DOWN                 = 10,  // 减速标记
    OST_ROADMARK_NO_PARKING                = 11,  // 禁停区

    OST_SIGN_OTHER                         = 50,  // 其他标牌子类
    OST_SIGN_SPEEDLIMIT                    = 51,  // 标牌限速
    OST_SIGN_CHEVRON_ALIGNMENT             = 52,  // 线型诱导
    OST_SIGN_NO_ENTRY                      = 53,  // 禁止驶入
    OST_SIGN_NO_OVERTAKING                 = 54,  // 禁止超车
    OST_SIGN_END_NO_OVERTAKING             = 55,  // 解除禁止超车
    OST_SIGN_STOP                          = 56,  // 停车让行
    OST_SIGN_ROAD_WORK_AHEAD               = 57,  // 注意施工
    OST_SIGN_SHARP_LEFT_TURN               = 58,  // 向左急转弯
    OST_SIGN_SHARP_RIGHT_TURN              = 59,  // 向右急转弯
    OST_SIGN_WINDING_ROAD                  = 60,  // 连续弯路
    OST_SIGN_MIND_CHILDREN                 = 61,  // 注意儿童
    OST_SIGN_NO_PARKING                    = 62,  // 禁止停车
    OST_SIGN_HIGH_WAY_BEGIN                = 63,  // 高速公路起点
    OST_SIGN_HIGH_WAY_END                  = 64,  // 高速公路终点
    OST_SIGN_NO_THROUGH                    = 65,  // 禁止通行
    OST_SIGN_NO_TURN_RIGHT                 = 66,  // 禁止向右转弯
    OST_SIGN_NO_TURN_LEFT                  = 67,  // 禁止向左转弯
    OST_SIGN_NO_HONK                       = 68,  // 禁止鸣喇叭
    OST_SIGN_SLOW_ACCIDENT                 = 69,  // 慢行，事故易发路段
    OST_SIGN_HIGH_LIMIT                    = 70,  // 限制高度
    OST_SIGN_NO_TURN                       = 71,  // 禁止掉头
    OST_SIGN_WEIGHT_GROSS_LIMIT            = 72,  // 限制重量、限制轴重
    OST_SIGN_NO_ENTRY_PEDESTRIAN_NONMOTOR  = 73,   // 禁止行人，非机动车驶入
    OST_SIGN_NO_ENTRY_MOTOR                = 74,  // 禁止机动车驶入
    OST_SIGN_NONMOTOR_DRIVE_LANE           = 75,  // 非机动车道
    OST_SIGN_BUS_BRT                       = 76,  // 公交线路专用车道
    OST_SIGN_DISTRICT_SPEED_LIMIT_REMOVAL  = 77,  // 区域限制速度解除
    OST_SIGN_NO_ENTRY_VECHICLE             = 78,  // 禁止机动车驶入
    OST_SIGN_PARKING_INDICATOR             = 80,  // 停车相关指示
    OST_SIGN_PEDESTRIAN                    = 81,  // 人行横道标牌

    OST_SIGNAL_OTHER                       = 150, // 未分类交通灯
    OST_SIGNAL_VEHICLE                     = 151, // 机动车信号灯
    OST_SIGNAL_DIRECTION                   = 152, // 方向指示信号灯
    OST_SIGNAL_LANE                        = 153, // 车道信号灯
    OST_SIGNAL_NON_VEHICLE                 = 154, // 非机动车信号灯
    OST_SIGNAL_PAVEMENT                    = 155, // 人行横道信号灯
    OST_SIGNAL_WARNING                     = 156, // 闪光信号灯
    OST_SIGNAL_JUNCTION                    = 157, // 道口信号灯

    OST_POLE_OTHER                         = 200, // 其他杆
    OST_POLE_TRAFFIC_SIGN                  = 201, // 交通信号杆
    OST_POLE_GANTRY                        = 202, // 龙门架杆
    OST_POLE_STREET_LIGHT                  = 203, // 路灯杆
    OST_POLE_BARRIER_ROD                   = 204  // 挡杆
};

enum ObjectShape {
    OBJECT_SHAPE_OTHER           = 0, // 其他类型
    OBJECT_SHAPE_RECTANGLE       = 1, // 矩形
    OBJECT_SHAPE_CIRCLE          = 2, // 圆形
    OBJECT_SHAPE_TRIANGLE        = 3, // 三角形
    OBJECT_SHAPE_DIAMOND         = 4, // 菱形
    OBJECT_SHAPE_OCTAGONAL       = 5, // 八角形
    OBJECT_SHAPE_CROSS_SIGNAL    = 6, // 横杆灯
    OBJECT_SHAPE_VERTICAL_SIGNAL = 7  // 竖杆灯
};

enum RoadMarkType {
    RMT_NONE                        = 0,   // 其他未分类
    RMT_STRAIGHT                    = 201, // 直行
    RMT_STRAIGHT_OR_LEFT            = 202, // 直行或左转
    RMT_LEFT                        = 203, // 左转
    RMT_RIGHT                       = 204, // 右转
    RMT_STRAIGHT_OR_RIGHT           = 205, // 直行或右转
    RMT_TURN                        = 206, // 掉头
    RMT_STRAIGHT_OR_TURN            = 207, // 直行或掉头
    RMT_LEFT_OR_TURN                = 208, // 左转或掉头
    RMT_LEFT_RIGHT_SWERVE           = 209, // 左右转弯
    RMT_LEFT_TURN_OR_INTERFLOW      = 210, // 左转或向左合流
    RMT_RIGHT_TURN_OR_INTERFLOW     = 211, // 右转或向右合流
    RMT_STRAIGHT_OR_LEFT_OR_RIGHT   = 212, // 直行或左转或右转
};

enum JunctionType {
    JUNCTION_TYPE_JUNCTION            = 0, // 汇入汇出
    JUNCTION_TYPE_GATEWAY             = 1, // 
    JUNCTION_TYPE_CROSS               = 2, // 路口
    JUNCTION_TYPE_ROUNDABOUT          = 3, // 环岛
    JUNCTION_TYPE_U_TURN              = 4, // 掉头口
    JUNCTION_TYPE_LANE_MERGE          = 5, // 车道合流
    JUNCTION_TYPE_LANE_SPLIT          = 6, // 车道分流
    JUNCTION_TYPE_ROAD_DEAD_END       = 7, // 断头路路口
    JUNCTION_TYPE_GATEWAY_WITHIN_CITY = 8, // 城市内连接
    JUNCTION_TYPE_TOOLGATE            = 9, // 收费站
    JUNCTION_TYPE_NOA                 = 100 // NOA Junction
};

enum VehicleType {
    RESTRICTION_VEHICLE_ALL         = 0, // 全部车辆
    RESTRICTION_VEHICLE_MOTOR       = 1, // 机动车
    RESTRICTION_VEHICLE_CAR         = 2, // 小轿车
    RESTRICTION_VEHICLE_MINI_CAR    = 3, // 微型车
    RESTRICTION_VEHICLE_STRUCK      = 4, // 小型车/货车
    RESTRICTION_VEHICLE_BTRUCK      = 5, // 大卡/货车(Truck/lorry)
    RESTRICTION_VEHICLE_TOWING      = 6, // 拖挂车
    RESTRICTION_VEHICLE_SCOACH      = 7, // 小型客车
    RESTRICTION_VEHICLE_BCOACH      = 8, // 大型客车
    RESTRICTION_VEHICLE_BUS         = 9, // 公交车
    RESTRICTION_VEHICLE_TAXI        = 10, // 出租车
    RESTRICTION_VEHICLE_HOV         = 11, // HOV
    RESTRICTION_VEHICLE_TRANSIT     = 12, // 过境车辆
    RESTRICTION_VEHICLE_BICYCLE     = 13, // 自行车
    RESTRICTION_VEHICLE_MOTORCYCLE  = 14  // 摩托车
};

enum SpeedLimitSignType {
    SLS_MAX_SPEED_LIMIT      = 0, // 最高限速
    SLS_MIN_SPEED_LIMIT      = 1, // 最低限速
    SLS_REMOVAL_SPEED_LIMIT  = 2, // 解除限速
    SLS_VARIBALE_SPEED_LIMIT = 3, // 可变限速
    SLS_SUGGEST_SPEED_LIMIT  = 4  // 建议限速
};

enum ConvergeType {
    CT_NONE  = 0x0, // 无
    CT_MERGE = 0x1, // 汇入
    CT_SPLIT = 0x2  // 汇出
};

enum SurfaceType {
    SFT_UNKNOWN          = 0, // 未知
    SFT_PLANE_SURFACE    = 1, // 平面
    SFT_NO_PLANE_SURFACE = 2  // 非平面
};

enum DataMask {
    DATA_MASK_LINK     = 0x1,
    DATA_MASK_LANE     = 0x2,
    DATA_MASK_OBJECT   = 0x4,
    DATA_MASK_JUNCTION = 0x8
};

enum EngineFlag{
    EF_TILEOTA_OFF = 0x00000001 //表示引擎启动时不开启TileOTA功能
};

enum ODDStatus
{
    OS_INVALID      = 0,
    OS_UNKNOWN      = 1,  //缓存中无此Link对应ODD信息
    OS_ODD_INSIDE   = 2,  //缓存中存在Link对应ODD信息，且ODD内, 可MD
    OS_ODD_OUTSIDE  = 3,  //缓存中存在Link对应ODD信息，且ODD外, 不可MD
};

enum CoordType {
    CT_INVALID     = -1,
    CT_WGS84       = 0,
    CT_GCJ_OFFSET  = 1,
    CT_SELF_OFFSET = 2,
};

enum SourceType {
    ST_HD = 0,
    ST_LD = 1
};
/**
 * realtime map matching enum class
*/

// amap road class and type
enum AmapRoadClass {
    ARC_FREEWAY         = 0,   /**< 高速公路 */
    ARC_NATIONAL_ROAD   = 1,   /**< 国道 */
    ARC_PROVINCE_ROAD   = 2,   /**< 省道 */
    ARC_COUNTY_ROAD     = 3,   /**< 县道 */
    ARC_RURAL_ROAD      = 4,   /**< 乡公路 */
    ARC_INCOUNTY_ROAD   = 5,   /**< 县乡村内部道路 */
    ARC_CITY_SPEEDWAY   = 6,   /**< 主要大街、城市快速道 */
    ARC_MAIN_ROAD       = 7,   /**< 主要道路 */
    ARC_SECONDARY_ROAD  = 8,   /**< 次要道路 */
    ARC_COMMON_ROAD     = 9,   /**< 普通道路 */
    ARC_NONNAVI_ROAD    = 10,  /**< 非导航道路 */
    ARC_COUNT           = 11,  /**< RoadClass的个数 */
    ARC_NULL            = 9999  /**< 无效值 */
};

enum AmapFormway {
    AF_NONE                     = 0,   /**< 无效 */
    AF_DIVISED_LINK             = 1,   /**< 主路 */
    AF_CROSS_LINK               = 2,   /**< 复杂节点内部道路 */
    AF_JCT                      = 3,   /**< 高架 */
    AF_ROUND_CIRCLE             = 4,   /**< 环岛 */
    AF_SERVICE_ROAD             = 5,   /**< 辅助道路 */
    AF_SLIP_ROAD                = 6,   /**< 匝道 */
    AF_SIDE_ROAD                = 7,   /**< 辅路 */
    AF_SLIP_JCT                 = 8,   /**< 匝道 */
    AF_EXIT_LINK                = 9,   /**< 出口 */
    AF_ENTRANCE_LINK            = 10,  /**< 入口 */
    AF_TURN_RIGHT_LINEA         = 11,  /**< 右转专用道 */
    AF_TURN_RIGHT_LINEB         = 12,  /**< 右转专用道 */
    AF_TURN_LEFT_LINEA          = 13,  /**< 左转专用道 */
    AF_TURN_LEFT_LINEB          = 14,  /**< 左转专用道 */
    AF_COMMON_LINK              = 15,  /**< 普通道路 */
    AF_TURN_LEFT_RIGHT_LINE     = 16,  /**< 左右转专用道 */
    AF_SERVICE_JCT_ROAD         = 17,  /**< 高架 */
    AF_SERVICE_SLIP_ROAD        = 18,  /**< 匝道 */
    AF_SERVICE_SLIP_JCT_ROAD    = 19,  /**< 匝道 */

    AF_FERRY                    = 102, /**< 航道 */
    AF_TUNNEL                   = 103, /**< 隧道 */
    AF_BRIDGE_TYPE              = 104, /**< 桥梁 */
};

// baidu road class and type

enum BaiduSDClass {
    BD_CLASS_HIGHWAY            = 0,    // 高速道路
    BD_CLASS_CITY_HIGHWAY       = 1,    // 城市高速
    BD_CLASS_NATIONAL_ROAD      = 2,    // 国道（城市主要主干道）
    BD_CLASS_PROVINCIAL_ROAD    = 3,    // 省道（城市主干道）
    BD_CLASS_COUNTY_ROAD        = 4,    // 县道（城市次干道）
    BD_CLASS_RURAL_ROAD         = 5,    // 乡镇村道（城市支路）
    BD_CLASS_OTHER_ROAD         = 6,    // 其它道路
    BD_CLASS_CLASS_NINE_ROAD    = 7,    // 九级路
    BD_CLASS_FERRY              = 8,    // 航线(轮渡)
    BD_CLASS_SIDEWALK           = 9,    // 行人道路
    BD_CLASS_NULL               = 999   // 自定义无效
};

enum BDFormType {
    FORM_TYPE_RINGWAY                   = 0,    // 环岛
    FORM_TYPE_NOTYPE                    = 1,    // 无属性
    FORM_TYPE_SEPERATEWAY               = 2,    // 上下线分离
    FORM_TYPE_JCT                       = 3,    // JCT
    FORM_TYPE_CROSSLINK                 = 4,    // 交叉点内link
    FORM_TYPE_IC                        = 5,    // IC
    FORM_TYPE_PA                        = 6,    // 停车区
    FORM_TYPE_SA                        = 7,    // 服务区
    FORM_TYPE_BRIDGE                    = 8,    // 桥
    FORM_TYPE_WALKSTREET                = 9,    // 步行街
    FORM_TYPE_SLAVEWAY                  = 10,   // 辅路
    FORM_TYPE_RAMP                      = 11,   // 匝道
    FORM_TYPE_CLOSEDWAY                 = 12,   // 全封闭道路
    FORM_TYPE_UNDEFINEDEREA             = 13,   // 未定义交通区域
    FORM_TYPE_POILINKWAY                = 14,   // POI连接路
    FORM_TYPE_TUNNEL                    = 15,   // 隧道
    FORM_TYPE_FOOTWAY                   = 16,   // 步行道
    FORM_TYPE_BUSWAY                    = 17,   // 公交专用道
    FORM_TYPE_FORRIGHT                  = 18,   // 提前右转
    FORM_TYPE_SCENIC                    = 19,   // 风景线路
    FORM_TYPE_INAREA                    = 20,   // 区域内道路
    FORM_TYPE_FORLEFT                   = 21,   // 提前左转
    FORM_TYPE_FORTURN                   = 22,   // 调头口
    FORM_TYPE_FORENTEREXITMAIN          = 23,   // 主辅路出入口
    FORM_TYPE_VIRTUALLINKWAY            = 24,   // 虚拟连接路
    FORM_TYPE_PARKINGWAY                = 25,   // 停车位引导路
    FORM_TYPE_PARKING_ACCESS_ROAD       = 26,   // 停车场出入口连接路
    FORM_TYPE_CONNECTION_GAS_STATION    = 27,   // 通往加油站的连接路
    FORM_TYPE_HEDGE_LANES               = 28,   // 避险车道
    FORM_TYPE_TRUCK_LANE                = 29,   // 货车专用道
    FORM_TYPE_NON_STANDARD_RINGWAY      = 30,   // 非标环岛
    FORM_TYPE_FRONT_DOOR_ROAD           = 31,   // 门前道路
    FORM_TYPE_LINE_OVERPASS             = 32,   // 跨线天桥
    FORM_TYPE_CROSS_LINE_TUNNEL         = 33,   // 跨线地道
    FORM_TYPE_OVERPASS                  = 34,   // 立交桥
    FORM_TYPE_TAXILANE                  = 35,   // 出租车专用道
    FORM_TYPE_PASSENGERCARLANE          = 36,   // 客运车专用道
    FORM_TYPE_PANSHANROAD               = 37    // 盘山路
};
}  // namespace imap
}  // namespace baidu

#endif
