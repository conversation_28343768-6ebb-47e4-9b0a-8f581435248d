#pragma once

#include <stdint.h>
#include <string>
#include <memory>
#include <vector>
#include <deque>
#include <unordered_map>
#include "map_engine/hdmap_errcode.h"
#include "map_engine/hdmap_define.h"
#include "map_engine/hdmap_enum.h"
#include "map_engine/hdmap_constant.h"

namespace baidu {
namespace imap {

class ILink;
class ILane;
class ILaneBoundary;
class IObject;
class IJunction;
typedef std::shared_ptr<ILink> LinkPtr;
typedef std::shared_ptr<ILane> LanePtr;
typedef std::shared_ptr<IObject> ObjectPtr;
typedef std::shared_ptr<IJunction> JunctionPtr;
typedef std::shared_ptr<ILaneBoundary> HdLaneBoundaryPtr;
class ILink
{
public:
    virtual uint32_t get_id() const = 0;

    virtual LinkClass get_link_class() const = 0;

    virtual Direction get_travel_direction() const = 0;

    virtual uint8_t get_lane_num() const = 0;

    virtual uint32_t get_link_type() const = 0;

    virtual uint32_t get_length() const = 0;

    virtual uint16_t get_speed_limit() const = 0;

    virtual const std::vector<Point3I>& get_geometry() const = 0;

    virtual const std::vector<uint32_t>& get_adas_offset() const = 0;

    virtual const std::vector<int16_t>& get_adas_curvature() const = 0;

    virtual const std::vector<int16_t>& get_adas_slope() const = 0;

    virtual const std::vector<int16_t>& get_adas_cross_slope() const = 0;

    virtual const std::vector<uint16_t>& get_adas_heading() const = 0;

    virtual const std::vector<uint32_t>& get_successor_links_ids() const = 0;

    virtual const std::vector<uint32_t>& get_precursor_links_ids() const = 0;

    virtual const std::vector<uint32_t>& get_laneids() const = 0;

    virtual UpDownLayerInfo get_up_down_layer_info() const = 0;
    
    virtual std::vector<uint32_t> get_in_junction_id() const = 0;

    virtual std::vector<uint32_t> get_out_junction_id() const = 0;

    virtual std::vector<uint32_t> get_pass_junction_id() const = 0;

    virtual SourceType get_source_type() const = 0;

};

class ILane
{
public:
    virtual uint32_t get_laneid() const = 0;

    virtual uint32_t get_linkid() const = 0;

    virtual uint8_t get_sequence() const = 0;

    virtual LaneType get_lane_type() const = 0;

    virtual RestrictedLaneType get_restricted_lane_type() const = 0;

    virtual LaneConnectionType get_lane_connection_type() const = 0;

    virtual LaneTransition get_lane_transition() const = 0;

    virtual baidu::imap::Direction get_travel_direction() const = 0;

    virtual uint16_t get_min_speed_limit() const = 0;

    virtual uint16_t get_max_speed_limit() const = 0;

    virtual bool is_under_construction() const = 0;

    virtual uint16_t get_min_width() const = 0;

    virtual uint16_t get_max_width() const = 0;

    virtual uint16_t get_average_width() const = 0;

    virtual uint32_t get_length() const = 0;

    virtual const std::vector<uint32_t>& get_adas_offset() const = 0;

    virtual const std::vector<int16_t>& get_adas_curvature() const = 0;

    virtual const std::vector<int16_t>& get_adas_slope() const = 0;

    virtual const std::vector<int16_t>& get_adas_cross_slope() const = 0;

    virtual const std::vector<uint16_t>& get_adas_heading() const = 0;

    virtual std::vector<HdLaneBoundaryPtr> get_left_marking_list() const = 0;

    virtual std::vector<HdLaneBoundaryPtr> get_right_marking_list() const = 0;

    virtual const std::vector<Point3I>& get_geometry() const = 0;

    virtual const std::vector<uint32_t>& get_successor_lanes_ids() const = 0;

    virtual const std::vector<uint32_t>& get_precursor_lanes_ids() const = 0;

    virtual const std::vector<uint32_t>& get_objids() const = 0;
    
    virtual bool get_has_pos_restrictive() const = 0;
    
    virtual bool get_has_neg_restrictive() const = 0;
        
    virtual baidu::imap::RestrictiveCondition get_pos_res() const = 0;

    virtual baidu::imap::RestrictiveCondition get_neg_res() const = 0;

    virtual uint16_t get_plan_direction() const = 0;

    virtual uint16_t get_current_direction() const = 0;

    virtual uint32_t get_junction_id() const = 0;

    virtual bool has_junction_id() const = 0;

    virtual std::vector<OppositeLaneInfo> get_left_opposite_lane_info() const = 0;

    virtual std::vector<OppositeLaneInfo> get_right_opposite_lane_info() const = 0;

    virtual uint8_t get_converge_type() const = 0;

    virtual uint16_t get_road_boundary_width() const = 0;

    virtual uint16_t get_min_left_road_boundary_distance() const = 0;

    virtual uint16_t get_max_left_road_boundary_distance() const = 0;

    virtual uint16_t get_min_right_road_boundary_distance() const = 0;

    virtual uint16_t get_max_right_road_boundary_distance() const = 0;

    virtual std::vector<SpecialLaneObj> get_successor_special_lanes() const = 0;
    virtual std::vector<SpecialLaneObj> get_precursor_special_lanes() const = 0;
};

class IObject
{
public:
    virtual uint32_t get_id() const = 0;

    virtual ObjectType get_type() const = 0;

    virtual ObjectSubType get_subtype() const = 0;

    virtual Point3I get_center() const = 0;

    virtual uint16_t get_height() const = 0;

    virtual uint16_t get_width() const = 0;

    virtual uint16_t get_length() const = 0;

    virtual int16_t get_heading() const = 0;

    virtual bool has_geometry() const = 0;

    virtual const std::vector<Point3I>& get_geometry() const = 0;

    virtual const std::vector<Point3I>& get_bounding_box_geometry() const = 0;

    virtual const std::vector<uint32_t>& get_laneids() const = 0;

    virtual ObjectShape get_object_shape() const = 0;

    virtual std::vector<SpeedLimitSign> get_speed_limit_sign() const = 0;

    virtual RoadMarkType get_roadmark_type() const = 0;

    virtual std::vector<uint32_t> get_ref_object_ids() const = 0;
};

class ILaneBoundary
{
public:
    virtual uint32_t get_id() const = 0;
    
    virtual DividerMarkingType get_type() const = 0;
    
    virtual DividerColor get_color() const = 0;
    
    virtual DividerMaterial get_material() const = 0;
    
    virtual uint16_t get_width() const = 0;
    
    virtual uint32_t get_length() const = 0;
   
    virtual uint32_t get_order_number() const = 0;

    virtual uint32_t get_offset() const = 0;
    
    virtual RoadBoundaryType get_road_boundary_type() const = 0;
    
    virtual RoadBoundarySubType get_road_boundary_subtype() const = 0;

    virtual uint8_t get_divider_type() const = 0;
    
    virtual uint8_t get_divider_subtype() const = 0;

    virtual const std::vector<Point3I>& get_geometry() const = 0;
    
    virtual bool get_is_road_boundary() const = 0;

    virtual SurfaceType get_surface_type() const = 0;
    
};
class IJunction{
public:
    virtual uint32_t get_id() const = 0;

    virtual JunctionType get_type() const = 0;

    virtual const std::vector<Point3I>& get_geometry() const = 0;

    virtual std::vector<JunctionConnection> get_connections() const = 0;

    virtual const std::vector<uint32_t>& get_laneids() const = 0;

    virtual std::vector<uint32_t> get_obstacles() const = 0;
};
typedef std::shared_ptr<IJunction> JunctionPtr;
struct LocalMap {
    Point3D center;
    double radius;
    uint32_t data_mask;
    std::vector<LinkPtr> hd_links;
    std::vector<LanePtr> hd_lanes;
    std::vector<ObjectPtr> hd_objects;
    std::vector<JunctionPtr> hd_junctions;
    LocalMap() : data_mask(0xFFFFFFFF){} 
};
typedef std::shared_ptr<LocalMap> LocalMapPtr;

} // namespace imap
} // namespace baidu
