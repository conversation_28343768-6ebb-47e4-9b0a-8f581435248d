// #pragma once
#ifndef __HDMAP_DEFINE__
#define __HDMAP_DEFINE__

#include <stdint.h>
#include <cmath>
#include <vector>
#include <stdexcept>
#include <iostream>
#include "map_engine/hdmap_enum.h"

namespace baidu {
namespace imap {

class Point3D;
class Point3I;
class Point2D;
class Point2I;

class RestrictiveCondition {
public:
    class Restriction {
    public:
        Restriction()
            : m_type(false)
            , m_vehicle()
            , m_validaty("")
        {
        }

        Restriction(const Restriction& t)
            : m_type(t.m_type)
            , m_vehicle(t.m_vehicle)
            , m_validaty(t.m_validaty)
        {
        }

        Restriction(const bool type,
            const std::vector<enum baidu::imap::VehicleType>& vehicle,
            const std::string& validaty)
            : m_type(type)
            , m_vehicle(vehicle)
            , m_validaty(validaty)
        {
        }

        Restriction& operator=(const Restriction& t)
        {
            m_type = t.m_type;
            m_vehicle = t.m_vehicle;
            m_validaty = t.m_validaty;
            return *this;
        }

    public:
        bool m_type;
        std::vector<enum baidu::imap::VehicleType> m_vehicle;
        std::string m_validaty;
    };

public:
    RestrictiveCondition()
        : m_type(false)
        , m_restriction()
    {
    }

    RestrictiveCondition(const RestrictiveCondition& t)
        : m_type(t.m_type)
        , m_restriction(t.m_restriction)
    {
    }

    RestrictiveCondition& operator=(const RestrictiveCondition& t)
    {
        m_type = t.m_type;
        m_restriction = t.m_restriction;
        return *this;
    }

public:
    bool m_type;
    std::vector<Restriction> m_restriction;
};
class Point3I
{
public:
    int32_t x;
    int32_t y;
    int32_t z;
    
    Point3I() : x(0), y(0), z(0)
    {
    }

    Point3I(const int32_t _x, const int32_t  _y, const int32_t _z) :
    x(_x), y(_y), z(_z)
    {
    }

    bool operator==(const Point3I& _other) const
    {
        if (this != &_other)
        {
            return
                (x == _other.x) &&
                (y == _other.y) &&
                (z == _other.z);
        }
        return true;
    }

    // 从Point3D自动转Point3I,
    // x = r.x * 1.0e7;
    // y = r.y * 1.0e7;
    // z = r.z * 100.0;
    Point3I(const Point3D& r);
    Point3I& operator= (const Point3D& r);
};

/**
* class point3D
*/
class Point3D
{
public:
    double x;
    double y;
    double z;
    
    Point3D() : x(0), y(0), z(0)
    {
    }
    
    Point3D(const double _x, const double  _y, const double _z) :
    x(_x), y(_y), z(_z)
    {
    }

    Point3D(const Point3D& r) :
        x(r.x), y(r.y), z(r.z)
    {
    }
    
    Point3D& operator= (const Point3D& r)
    {
        x = r.x;
        y = r.y;
        z = r.z;
        return *this;
    }

    // 从Point3I自动转Point3D,
    // x = r.x / 1.0e7;
    // y = r.y / 1.0e7;
    // z = r.z / 100.0;
    Point3D(const Point3I& r);
    Point3D& operator= (const Point3I& r);
};

class Point2I
{
public:
    int32_t x;
    int32_t y;
    
    Point2I() : x(0), y(0)
    {
    }

    Point2I(const int32_t _x, const int32_t _y) :
    x(_x), y(_y)
    {
    }

    bool operator==(const Point2I& _other) const
    {
        if (this != &_other)
        {
            return
                (x == _other.x) &&
                (y == _other.y);
        }
        return true;
    }

    // 从Point2D自动转Point2I,
    // x = r.x * 1.0e7;
    // y = r.y * 1.0e7;
    Point2I(const Point2D& r);
    Point2I& operator= (const Point2D& r);
};

/**
* struct point2D
*/
class Point2D
{
public:
    double x;
    double y;
    
    Point2D() : x(0), y(0)
    {
    }

    Point2D(const double _x, const double _y) :
    x(_x), y(_y)
    {
    }

    Point2D(const Point2D& r) :
        x(r.x), y(r.y)
    {
    }
    
    Point2D& operator= (const Point2D& r)
    {
        x = r.x;
        y = r.y;
        return *this;
    }

    // 从Point2I自动转Point2D,
    // x = r.x / 1.0e7;
    // y = r.y / 1.0e7;
    Point2D(const Point2I& r);
    Point2D& operator= (const Point2I& r);
};

/**
 * GPS Coord
*/
struct GpsCoord
{
    double lon;
    double lat;
    double height;
    double heading;

    GpsCoord() : lon(0.0), lat(0.0), height(0.0), heading(0.0)
    {
    }

    GpsCoord(double _lon, double _lat, double _height, double _heading)
    : lon(_lon), lat(_lat), height(_height), heading(_heading)
    {
    }

    bool is_same_point(const GpsCoord& newPoint)
    {
        return (fabs(newPoint.lon - lon) <= 1e-7) && 
            (fabs(newPoint.lat - lat) <= 1e-7) &&
            (fabs(newPoint.height - height) <= 1e-7);
    }
};

struct HadmapPilotResult{
    GpsCoord gps;
    uint32_t link_id;
    uint32_t lane_id;
    double distance_to_link_end;
    double distance_to_lane_end;
    HadmapPilotResult() : link_id(0),
                          lane_id(0),
                          distance_to_link_end(0), 
                          distance_to_lane_end(0){};
};

struct UpDownLayerInfo{
    uint32_t roadLevel;
    uint8_t isTop;
    uint32_t startOffset;
    uint32_t endOffset;
    UpDownLayerInfo() : roadLevel(0), isTop(0), startOffset(0), endOffset(0) {}
};

struct OppositeLaneInfo{
    uint32_t lane_id;
    int32_t offset; 
    OppositeLaneInfo() : lane_id(0), offset(0) {}
};

struct SpecialLaneObj {
    uint32_t laneid;
    uint32_t type; // 0：无特殊属性 1：中心线不挂接的拓扑关系
};

struct SpeedLimitSign{
    uint8_t value;
    SpeedLimitSignType type;
};

struct LogControl
{
    enum LogLevel{
        INFO = 1,
        WARNING = 2,
        ERROR = 3,
        FATAL = 4,
    };

    int log_num;
    int log_size;
    std::string log_path;
    LogLevel log_level;

    LogControl() : log_num(10), log_size(100000), log_path(""), log_level(LogControl::ERROR)
    {
    }

    LogControl(const LogControl& log_control) : 
        log_num(log_control.log_num), log_size(log_control.log_size),
        log_path(log_control.log_path), log_level(log_control.log_level)
    {
    }

    LogControl(int _log_num, int _log_size, std::string _log_path, LogLevel _log_level)
    : log_num(_log_num), log_size(_log_size), log_path(_log_path), log_level(_log_level)
    {
    }
};

struct InitParameter
{
    std::string m_vg;
    std::string m_conf_path;
    LogControl m_log_control;

    InitParameter() : m_vg(""), m_conf_path(""), m_log_control(10, 100000, "", LogControl::ERROR)
    {
    }

    InitParameter(std::string vg, std::string conf_path = "",
    const LogControl& log_control = LogControl(10, 100000, "", LogControl::ERROR))
    : m_vg(vg), m_conf_path(conf_path), m_log_control(log_control)
    {
    }
};

struct LaneLink{
    uint32_t from_lane_tile_id;
    uint32_t from_lane_id;
    uint32_t to_lane_tile_id;
    uint32_t to_lane_id;
};

struct JunctionConnection{
    uint32_t from_link_tile_id;
    uint32_t from_link_id;
    uint32_t to_link_tile_id;
    uint32_t to_link_id;
    std::vector<LaneLink> lanelinks;
};

struct SDLinkInfo
{
    // 道路 Id
    uint64_t linkid;

    // 道路类型，参见AmapFormway
    std::vector<uint32_t> formways;

    // 功能等级，参见AmapRoadClass
    uint32_t func_class;
    
    // 道路名称
    std::string name;
            
    // 通行方向,对应Direction枚举
    uint8_t direction;

    // link长度，单位: 米
    uint32_t length;

    // 最高限速，单位：km/h
    uint32_t speed_limit;

    // 是否下穿道路
    bool is_under_pass;

    // link车道数
    uint8_t laneNum;

    //是否是高架
    bool is_over_pass;

    std::vector<Point3D> coords;

    SDLinkInfo():
        linkid(0),
        func_class(9999),
        name(""),
        direction(0),
        length(0),
        speed_limit(0),
        is_under_pass(false),
        laneNum(0),
        is_over_pass(false){};
};
struct TBTInfo                                    //该结构体目前为异源匹配预留接口中使用
{    
    uint64_t link_id;                           //道路Id 
    uint32_t main_action;              //主导航（左转、右转、左前右前等）
    uint32_t assistant_action;      // 导航辅助动作（进入高速、匝道等）

    TBTInfo():
        link_id(0),
        main_action(0),
        assistant_action(0){};
};


} // namespace imap
} // namespace baidu

#endif
