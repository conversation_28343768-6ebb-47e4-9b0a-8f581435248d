#pragma once

#include "noa/noa_define.h"
#include "dynamic_info/dynamic_info_define.h"
#include "map_engine/hdmap_interface.h"
#include "dtc/map_dtc_data.h"

#include <vector>
#include <string>
#include <functional>

namespace baidu {
namespace imap {
namespace sdk {

class IHadDB {
public:
    /**
     * @brief initialize, initialize the hd map root path
     * @param dbpath: hd map root path
     * @param vid: vehicle id
     * @return the dbpath is empty or not
     */
    virtual uint32_t initialize(
            const std::string& dbpath,
            const std::string& vid,
            const InitParameter& init_para =
                    InitParameter("", "", LogControl(10, 100000, "", LogControl::ERROR)),
            uint32_t flag = 0) = 0;

    /**
     *
     *     get map version
     *     @param  map version
     *     @return   status
     */
    virtual uint32_t get_map_version(std::string& version) const = 0;

    /**
     *
     *     get engine status
     *     @param  null
     *     @return   engine status
     */
    virtual uint32_t get_engine_status() const = 0;

    /**
     *
     *     get engine version
     *     @param engine version
     *     @return   status
     */
    virtual uint32_t get_engine_version(std::string& version) const = 0;
    virtual uint32_t set_current_position(const GpsCoord& gps) = 0;

    virtual LanePtr get_lane(uint32_t id) const = 0;
    virtual LinkPtr get_link(uint32_t id) const = 0;
    virtual ObjectPtr get_object(uint32_t id) const = 0;
    virtual JunctionPtr get_junction(uint32_t id) const = 0;

    virtual uint32_t get_local_map(
            const baidu::imap::GpsCoord& gps,
            double radius,
            LocalMap& local_map) const = 0;
            
    virtual void finalize_map() = 0;

    // NOA Interface
    virtual void set_pilot_result(const HadmapPilotResult& pilot_result) = 0;
    
    virtual void set_navi_info(const NavInfo& nav_info) = 0;

    virtual NoaStatus get_mpp_info(MppInfo& mpp_info) = 0;

    virtual void get_navi_status(NavStatus& ns) = 0;

    // dtc
    virtual void set_dtc_callback(std::function<void(message::MapDtcData)> dtc_receiver) = 0;
};

typedef std::shared_ptr<IHadDB> MapEnginePtr;

/**
 *
 *     get the had interface pointer
 *     @return    baidu::imap::MapEnginePtr the had interface pointer
 */
MapEnginePtr __attribute__((visibility("default"))) get_map_engine();
}  // namespace sdk
}  // namespace imap
}  // namespace baidu
