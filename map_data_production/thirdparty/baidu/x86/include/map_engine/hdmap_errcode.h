#pragma once

namespace baidu {
namespace imap {

enum ErrorCode {
    //common
    IMAP_STATUS_OK                      = 0,
    IMAP_UNKOWN_ERROR                   = 9999,

    //engine 1000---1999
    //HD_OK                     = 0,
    HD_DB_OPEN_FAILED         = 1001,
    HD_DB_NOT_COMPATIBLE      = 1002,
    HD_INVALID_POS            = 1003,
    HD_LOSS_OF_MAP            = 1004,
    HD_PARAM_ERROR            = 1005,    
    HD_INNER_ERROR            = 1006,
    HD_LOAD_MAP_OVERTIME      = 1007,
    HD_BUFFER_LOADING         = 1008,
    HD_ENGINE_NOT_INITIALIZE  = 1009,
    HD_GPS_OUT_OF_BUFFER      = 1010,
    HD_WAIT_FIRST_POS         = 1011,
    HD_TARGET_NOT_IN_MEMORY   = 1012,
    HD_USE_WB_WITH_NOWBCONF   = 1013,
    HD_NOT_USE_WB_WITH_WBCONF = 1014,
    HD_SDK_CONFIG_NOT_MATCH   = 1015,
    HD_DATA_INCOMPLETE        = 1016,
    //licence related
    HD_LICENCE_NOT_INITIALIZE = 1100,
    HD_ACTIVATE_FAILED        = 1101,
    HD_LICENCE_NOT_FOUND      = 1102,
    HD_LICENCE_EXPIRED        = 1103,
    HD_LICENCE_IO_ERROR       = 1104,
    //net related
    HD_NET_CONNECT_FAILED     = 1200,
    HD_NET_TIMEOUT            = 1201,
    //map updating
    HD_UPDATING_BLOCKED                    = 1300,
    HD_NETWORK_ERROR_MAP_EXPIRED           = 1301,
    HD_MAPUPDATE_FAILED_MAP_EXPIRED        = 1302,
    HD_NETWORK_ERROR_MAP_INCONSISTENT      = 1303,
    HD_MAPUPDATE_FAILED_MAP_INCONSISTENT   = 1304,
    //ref data relation
    HD_REF_UPDATING = 1401,
    HD_REF_UPDATE_FAILED = 1402,

    // map manager 1500-1599
    HD_MD_INITIALIZE_OK                    = 1500,  //map_manager初始化失败
    HD_SERV_ERROR                          = 1501,  //本地无地图但版本同步失败
    HD_LOC_VRSN_OK_SERV_ERROR              = 1502,  //本地有地图但版本同步失败
    HD_FULL_UPDATE                         = 1503,  //本地有地图，版本同步时，进行本地缓存清除

    //ODD type 开关管理相关，校验下游设置的过滤类型
    ODD_FILTER_EMPTY                       = 1621, //无具体条件设置
    ODD_TYPE_CONFLICT                      = 1622, //类型逻辑冲突。只有和排他容器都不空
    ODD_LINK_FF_CONFLICT                   = 1623, //link class 存在0值代表全部同时存在其他具体类型
    ODD_OT_FF_CONFLICT                     = 1624, //oddtype容器， 存在0值代表全部同时存在其他具体类型
    ODD_EOT_FF_CONFLICT                    = 1625, //oddtype 排他容器， 存在0值代表全部同时存在其他具体类型
};

} // namespace imap
} // namespace baidu
