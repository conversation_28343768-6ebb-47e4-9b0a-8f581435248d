syntax = "proto3";

package gwm.common;

/**
 * PointENU: 地图参考框架中的一个点。
 * 地图定义了一个原点，其坐标为 (0, 0, 0)。
 * 包括定位、感知和预测在内的大多数模块都基于地图参考框架生成结果。
 * 当前，地图使用通用横轴墨卡托投影（UTM）。地图原点的定义见以下链接：
 *   <url id="cv96hmol3dcba11cpmf0" type="url" status="parsed" title="Universal Transverse Mercator coordinate system" wc="13178">https://en.wikipedia.org/wiki/Universal_Transverse_Mercator_coordinate_system</url> 
 * PointENU 的 z 字段可以省略。如果省略，则表示一个二维位置，其高度不被考虑。
 */
message PointENU {
  double x = 1;  // 东向距离原点的距离，单位为米。
  double y = 2;  // 北向距离原点的距离，单位为米。
  double z = 3;  // 距离 WGS-84 椭球面的高度，单位为米。
}

/**
 * PointLLH: 全球参考框架中的一个点。
 * 与 PointENU 类似，PointLLH 也允许省略高度字段以表示二维位置。
 */
message PointLLH {
  // 经度，单位为度，范围为 -180 到 180。
  double lon = 1;
  // 纬度，单位为度，范围为 -90 到 90。
  double lat = 2;
  // WGS-84 椭球面高度，单位为米。
  double height = 3;
}

/**
 * Point2D: 一个通用的二维点。
 * 其含义和单位取决于上下文，必须在注释中解释清楚。
 */
message Point2D {
  double x = 1;
  double y = 2;
}

/**
 * Point3D: 一个通用的三维点。
 * 其含义和单位取决于上下文，必须在注释中解释清楚。
 */
message Point3D {
  double x = 1;
  double y = 2;
  double z = 3;
}

/**
 * Vector3: 三维空间中的一个向量。
 */
message Vector3 {
  float x = 1;
  float y = 2;
  float z = 3;
}

/**
 * Quaternion: 一个用于表示空间旋转的单位四元数。
 * 详情见以下链接：
 *   <url id="cv96hmol3dcba11cpmfg" type="url" status="parsed" title="Quaternions and spatial rotation" wc="41508">https://en.wikipedia.org/wiki/Quaternions_and_spatial_rotation</url> 
 * 标量部分 qw 可以省略。在这种情况下，qw 应通过以下公式计算：
 *   qw = sqrt(1 - qx * qx - qy * qy - qz * qz)。
 */
message Quaternion {
  double qx = 1;
  double qy = 2;
  double qz = 3;
  double qw = 4;
}

/**
 * Quaternion_f: 四元数的浮点版本。
 */
message Quaternion_f {
  float qx = 1;
  float qy = 2;
  float qz = 3;
  float qw = 4;
}

/**
 * Polygon: 一个通用多边形，顶点按顺时针方向排列。
 */
message Polygon {
  repeated Point3D point = 1;
}

/**
 * Polyline: 一条由多个三维点组成的折线。
 */
message Polyline {
  repeated Point3D point = 1;
}

/**
 * PolygonLLH: 一个由经纬度和高度定义的多边形，顶点按顺时针方向排列。
 */
message PolygonLLH {
  repeated PointLLH point = 1;
}

/**
 * PolylineLLH: 一条由经纬度和高度定义的折线。
 */
message PolylineLLH {
  repeated PointLLH point = 1;
}

/**
 * Transformation3: 一个三维变换，包含位置和方向。
 */
message Transformation3 {
  Vector3 position = 1;  // 位置
  Quaternion_f orientation = 2;  // 方向
}

/**
 * Polygon2D: 一个简单的二维多边形，由所有顶点组成。
 * 顶点可以按顺时针或逆时针方向排列。
 */
message Polygon2D {
  repeated Point2D points = 1;
}

/**
 * RotatedRect: 一个旋转矩形，包含中心点、大小和旋转角度。
 */
message RotatedRect {
  Point2D center = 1;  // 中心点
  Point2D size = 2;  // 大小
  float angle = 3;  // 旋转角度
}

/**
 * RotatedRect3D: 一个三维旋转矩形，包含中心点、大小和旋转角度。
 */
message RotatedRect3D {
  Point3D center = 1;  // 中心点
  Point3D size = 2;  // 大小
  float angle = 3;  // 旋转角度
}

/**
 * PlaneCoeffs: 平面方程的系数。
 */
message PlaneCoeffs {
  double a = 1;
  double b = 2;
  double c = 3;
  double d = 4;
}
