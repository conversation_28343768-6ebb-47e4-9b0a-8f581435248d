// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/geometry.proto

#include "common/geometry.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Point2D_common_2fgeometry_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Point3D_common_2fgeometry_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PointLLH_common_2fgeometry_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Quaternion_f_common_2fgeometry_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Vector3_common_2fgeometry_2eproto;
namespace gwm {
namespace common {
class PointENUDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PointENU> _instance;
} _PointENU_default_instance_;
class PointLLHDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PointLLH> _instance;
} _PointLLH_default_instance_;
class Point2DDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Point2D> _instance;
} _Point2D_default_instance_;
class Point3DDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Point3D> _instance;
} _Point3D_default_instance_;
class Vector3DefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Vector3> _instance;
} _Vector3_default_instance_;
class QuaternionDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Quaternion> _instance;
} _Quaternion_default_instance_;
class Quaternion_fDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Quaternion_f> _instance;
} _Quaternion_f_default_instance_;
class PolygonDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Polygon> _instance;
} _Polygon_default_instance_;
class PolylineDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Polyline> _instance;
} _Polyline_default_instance_;
class PolygonLLHDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PolygonLLH> _instance;
} _PolygonLLH_default_instance_;
class PolylineLLHDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PolylineLLH> _instance;
} _PolylineLLH_default_instance_;
class Transformation3DefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Transformation3> _instance;
} _Transformation3_default_instance_;
class Polygon2DDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Polygon2D> _instance;
} _Polygon2D_default_instance_;
class RotatedRectDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RotatedRect> _instance;
} _RotatedRect_default_instance_;
class RotatedRect3DDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RotatedRect3D> _instance;
} _RotatedRect3D_default_instance_;
class PlaneCoeffsDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PlaneCoeffs> _instance;
} _PlaneCoeffs_default_instance_;
}  // namespace common
}  // namespace gwm
static void InitDefaultsscc_info_PlaneCoeffs_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_PlaneCoeffs_default_instance_;
    new (ptr) ::gwm::common::PlaneCoeffs();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PlaneCoeffs_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_PlaneCoeffs_common_2fgeometry_2eproto}, {}};

static void InitDefaultsscc_info_Point2D_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Point2D_default_instance_;
    new (ptr) ::gwm::common::Point2D();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Point2D_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Point2D_common_2fgeometry_2eproto}, {}};

static void InitDefaultsscc_info_Point3D_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Point3D_default_instance_;
    new (ptr) ::gwm::common::Point3D();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Point3D_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Point3D_common_2fgeometry_2eproto}, {}};

static void InitDefaultsscc_info_PointENU_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_PointENU_default_instance_;
    new (ptr) ::gwm::common::PointENU();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PointENU_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_PointENU_common_2fgeometry_2eproto}, {}};

static void InitDefaultsscc_info_PointLLH_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_PointLLH_default_instance_;
    new (ptr) ::gwm::common::PointLLH();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PointLLH_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_PointLLH_common_2fgeometry_2eproto}, {}};

static void InitDefaultsscc_info_Polygon_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Polygon_default_instance_;
    new (ptr) ::gwm::common::Polygon();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Polygon_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_Polygon_common_2fgeometry_2eproto}, {
      &scc_info_Point3D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_Polygon2D_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Polygon2D_default_instance_;
    new (ptr) ::gwm::common::Polygon2D();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Polygon2D_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_Polygon2D_common_2fgeometry_2eproto}, {
      &scc_info_Point2D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_PolygonLLH_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_PolygonLLH_default_instance_;
    new (ptr) ::gwm::common::PolygonLLH();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_PolygonLLH_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_PolygonLLH_common_2fgeometry_2eproto}, {
      &scc_info_PointLLH_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_Polyline_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Polyline_default_instance_;
    new (ptr) ::gwm::common::Polyline();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Polyline_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_Polyline_common_2fgeometry_2eproto}, {
      &scc_info_Point3D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_PolylineLLH_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_PolylineLLH_default_instance_;
    new (ptr) ::gwm::common::PolylineLLH();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_PolylineLLH_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_PolylineLLH_common_2fgeometry_2eproto}, {
      &scc_info_PointLLH_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_Quaternion_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Quaternion_default_instance_;
    new (ptr) ::gwm::common::Quaternion();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Quaternion_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Quaternion_common_2fgeometry_2eproto}, {}};

static void InitDefaultsscc_info_Quaternion_f_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Quaternion_f_default_instance_;
    new (ptr) ::gwm::common::Quaternion_f();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Quaternion_f_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Quaternion_f_common_2fgeometry_2eproto}, {}};

static void InitDefaultsscc_info_RotatedRect_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_RotatedRect_default_instance_;
    new (ptr) ::gwm::common::RotatedRect();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RotatedRect_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_RotatedRect_common_2fgeometry_2eproto}, {
      &scc_info_Point2D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_RotatedRect3D_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_RotatedRect3D_default_instance_;
    new (ptr) ::gwm::common::RotatedRect3D();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_RotatedRect3D_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_RotatedRect3D_common_2fgeometry_2eproto}, {
      &scc_info_Point3D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_Transformation3_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Transformation3_default_instance_;
    new (ptr) ::gwm::common::Transformation3();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_Transformation3_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_Transformation3_common_2fgeometry_2eproto}, {
      &scc_info_Vector3_common_2fgeometry_2eproto.base,
      &scc_info_Quaternion_f_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_Vector3_common_2fgeometry_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::common::_Vector3_default_instance_;
    new (ptr) ::gwm::common::Vector3();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Vector3_common_2fgeometry_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Vector3_common_2fgeometry_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_common_2fgeometry_2eproto[16];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_common_2fgeometry_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_common_2fgeometry_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_common_2fgeometry_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointENU, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointENU, x_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointENU, y_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointENU, z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointLLH, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointLLH, lon_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointLLH, lat_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::PointLLH, height_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Point2D, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Point2D, x_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Point2D, y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Point3D, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Point3D, x_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Point3D, y_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Point3D, z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Vector3, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Vector3, x_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Vector3, y_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Vector3, z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion, qx_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion, qy_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion, qz_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion, qw_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion_f, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion_f, qx_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion_f, qy_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion_f, qz_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Quaternion_f, qw_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Polygon, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Polygon, point_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Polyline, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Polyline, point_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PolygonLLH, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PolygonLLH, point_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PolylineLLH, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PolylineLLH, point_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Transformation3, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Transformation3, position_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::Transformation3, orientation_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Polygon2D, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::Polygon2D, points_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect, center_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect, size_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect, angle_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect3D, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect3D, center_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect3D, size_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::RotatedRect3D, angle_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PlaneCoeffs, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::common::PlaneCoeffs, a_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::PlaneCoeffs, b_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::PlaneCoeffs, c_),
  PROTOBUF_FIELD_OFFSET(::gwm::common::PlaneCoeffs, d_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::gwm::common::PointENU)},
  { 8, -1, sizeof(::gwm::common::PointLLH)},
  { 16, -1, sizeof(::gwm::common::Point2D)},
  { 23, -1, sizeof(::gwm::common::Point3D)},
  { 31, -1, sizeof(::gwm::common::Vector3)},
  { 39, -1, sizeof(::gwm::common::Quaternion)},
  { 48, -1, sizeof(::gwm::common::Quaternion_f)},
  { 57, -1, sizeof(::gwm::common::Polygon)},
  { 63, -1, sizeof(::gwm::common::Polyline)},
  { 69, -1, sizeof(::gwm::common::PolygonLLH)},
  { 75, -1, sizeof(::gwm::common::PolylineLLH)},
  { 81, -1, sizeof(::gwm::common::Transformation3)},
  { 88, -1, sizeof(::gwm::common::Polygon2D)},
  { 94, -1, sizeof(::gwm::common::RotatedRect)},
  { 102, -1, sizeof(::gwm::common::RotatedRect3D)},
  { 110, -1, sizeof(::gwm::common::PlaneCoeffs)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_PointENU_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_PointLLH_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Point2D_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Point3D_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Vector3_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Quaternion_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Quaternion_f_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Polygon_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Polyline_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_PolygonLLH_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_PolylineLLH_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Transformation3_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_Polygon2D_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_RotatedRect_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_RotatedRect3D_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::common::_PlaneCoeffs_default_instance_),
};

const char descriptor_table_protodef_common_2fgeometry_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\025common/geometry.proto\022\ngwm.common\"+\n\010P"
  "ointENU\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002 \001(\001\022\t\n\001z\030\003 \001(\001"
  "\"4\n\010PointLLH\022\013\n\003lon\030\001 \001(\001\022\013\n\003lat\030\002 \001(\001\022\016"
  "\n\006height\030\003 \001(\001\"\037\n\007Point2D\022\t\n\001x\030\001 \001(\001\022\t\n\001"
  "y\030\002 \001(\001\"*\n\007Point3D\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002 \001(\001"
  "\022\t\n\001z\030\003 \001(\001\"*\n\007Vector3\022\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002"
  " \001(\002\022\t\n\001z\030\003 \001(\002\"<\n\nQuaternion\022\n\n\002qx\030\001 \001("
  "\001\022\n\n\002qy\030\002 \001(\001\022\n\n\002qz\030\003 \001(\001\022\n\n\002qw\030\004 \001(\001\">\n"
  "\014Quaternion_f\022\n\n\002qx\030\001 \001(\002\022\n\n\002qy\030\002 \001(\002\022\n\n"
  "\002qz\030\003 \001(\002\022\n\n\002qw\030\004 \001(\002\"-\n\007Polygon\022\"\n\005poin"
  "t\030\001 \003(\0132\023.gwm.common.Point3D\".\n\010Polyline"
  "\022\"\n\005point\030\001 \003(\0132\023.gwm.common.Point3D\"1\n\n"
  "PolygonLLH\022#\n\005point\030\001 \003(\0132\024.gwm.common.P"
  "ointLLH\"2\n\013PolylineLLH\022#\n\005point\030\001 \003(\0132\024."
  "gwm.common.PointLLH\"g\n\017Transformation3\022%"
  "\n\010position\030\001 \001(\0132\023.gwm.common.Vector3\022-\n"
  "\013orientation\030\002 \001(\0132\030.gwm.common.Quaterni"
  "on_f\"0\n\tPolygon2D\022#\n\006points\030\001 \003(\0132\023.gwm."
  "common.Point2D\"d\n\013RotatedRect\022#\n\006center\030"
  "\001 \001(\0132\023.gwm.common.Point2D\022!\n\004size\030\002 \001(\013"
  "2\023.gwm.common.Point2D\022\r\n\005angle\030\003 \001(\002\"f\n\r"
  "RotatedRect3D\022#\n\006center\030\001 \001(\0132\023.gwm.comm"
  "on.Point3D\022!\n\004size\030\002 \001(\0132\023.gwm.common.Po"
  "int3D\022\r\n\005angle\030\003 \001(\002\"9\n\013PlaneCoeffs\022\t\n\001a"
  "\030\001 \001(\001\022\t\n\001b\030\002 \001(\001\022\t\n\001c\030\003 \001(\001\022\t\n\001d\030\004 \001(\001b"
  "\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_common_2fgeometry_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_common_2fgeometry_2eproto_sccs[16] = {
  &scc_info_PlaneCoeffs_common_2fgeometry_2eproto.base,
  &scc_info_Point2D_common_2fgeometry_2eproto.base,
  &scc_info_Point3D_common_2fgeometry_2eproto.base,
  &scc_info_PointENU_common_2fgeometry_2eproto.base,
  &scc_info_PointLLH_common_2fgeometry_2eproto.base,
  &scc_info_Polygon_common_2fgeometry_2eproto.base,
  &scc_info_Polygon2D_common_2fgeometry_2eproto.base,
  &scc_info_PolygonLLH_common_2fgeometry_2eproto.base,
  &scc_info_Polyline_common_2fgeometry_2eproto.base,
  &scc_info_PolylineLLH_common_2fgeometry_2eproto.base,
  &scc_info_Quaternion_common_2fgeometry_2eproto.base,
  &scc_info_Quaternion_f_common_2fgeometry_2eproto.base,
  &scc_info_RotatedRect_common_2fgeometry_2eproto.base,
  &scc_info_RotatedRect3D_common_2fgeometry_2eproto.base,
  &scc_info_Transformation3_common_2fgeometry_2eproto.base,
  &scc_info_Vector3_common_2fgeometry_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_common_2fgeometry_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_common_2fgeometry_2eproto = {
  false, false, descriptor_table_protodef_common_2fgeometry_2eproto, "common/geometry.proto", 1007,
  &descriptor_table_common_2fgeometry_2eproto_once, descriptor_table_common_2fgeometry_2eproto_sccs, descriptor_table_common_2fgeometry_2eproto_deps, 16, 0,
  schemas, file_default_instances, TableStruct_common_2fgeometry_2eproto::offsets,
  file_level_metadata_common_2fgeometry_2eproto, 16, file_level_enum_descriptors_common_2fgeometry_2eproto, file_level_service_descriptors_common_2fgeometry_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_common_2fgeometry_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_common_2fgeometry_2eproto)), true);
namespace gwm {
namespace common {

// ===================================================================

class PointENU::_Internal {
 public:
};

PointENU::PointENU(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.PointENU)
}
PointENU::PointENU(const PointENU& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.PointENU)
}

void PointENU::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

PointENU::~PointENU() {
  // @@protoc_insertion_point(destructor:gwm.common.PointENU)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PointENU::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void PointENU::ArenaDtor(void* object) {
  PointENU* _this = reinterpret_cast< PointENU* >(object);
  (void)_this;
}
void PointENU::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PointENU::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PointENU& PointENU::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PointENU_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void PointENU::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.PointENU)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PointENU::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PointENU::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.PointENU)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  // double z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.PointENU)
  return target;
}

size_t PointENU::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.PointENU)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    total_size += 1 + 8;
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    total_size += 1 + 8;
  }

  // double z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PointENU::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.PointENU)
  GOOGLE_DCHECK_NE(&from, this);
  const PointENU* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PointENU>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.PointENU)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.PointENU)
    MergeFrom(*source);
  }
}

void PointENU::MergeFrom(const PointENU& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.PointENU)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.x() <= 0 && from.x() >= 0)) {
    _internal_set_x(from._internal_x());
  }
  if (!(from.y() <= 0 && from.y() >= 0)) {
    _internal_set_y(from._internal_y());
  }
  if (!(from.z() <= 0 && from.z() >= 0)) {
    _internal_set_z(from._internal_z());
  }
}

void PointENU::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.PointENU)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PointENU::CopyFrom(const PointENU& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.PointENU)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PointENU::IsInitialized() const {
  return true;
}

void PointENU::InternalSwap(PointENU* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PointENU, z_)
      + sizeof(PointENU::z_)
      - PROTOBUF_FIELD_OFFSET(PointENU, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PointENU::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class PointLLH::_Internal {
 public:
};

PointLLH::PointLLH(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.PointLLH)
}
PointLLH::PointLLH(const PointLLH& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lon_, &from.lon_,
    static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&lon_)) + sizeof(height_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.PointLLH)
}

void PointLLH::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&lon_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&height_) -
      reinterpret_cast<char*>(&lon_)) + sizeof(height_));
}

PointLLH::~PointLLH() {
  // @@protoc_insertion_point(destructor:gwm.common.PointLLH)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PointLLH::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void PointLLH::ArenaDtor(void* object) {
  PointLLH* _this = reinterpret_cast< PointLLH* >(object);
  (void)_this;
}
void PointLLH::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PointLLH::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PointLLH& PointLLH::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PointLLH_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void PointLLH::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.PointLLH)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lon_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&height_) -
      reinterpret_cast<char*>(&lon_)) + sizeof(height_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PointLLH::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double lon = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          lon_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double lat = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          lat_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double height = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PointLLH::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.PointLLH)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double lon = 1;
  if (!(this->lon() <= 0 && this->lon() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_lon(), target);
  }

  // double lat = 2;
  if (!(this->lat() <= 0 && this->lat() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_lat(), target);
  }

  // double height = 3;
  if (!(this->height() <= 0 && this->height() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_height(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.PointLLH)
  return target;
}

size_t PointLLH::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.PointLLH)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double lon = 1;
  if (!(this->lon() <= 0 && this->lon() >= 0)) {
    total_size += 1 + 8;
  }

  // double lat = 2;
  if (!(this->lat() <= 0 && this->lat() >= 0)) {
    total_size += 1 + 8;
  }

  // double height = 3;
  if (!(this->height() <= 0 && this->height() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PointLLH::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.PointLLH)
  GOOGLE_DCHECK_NE(&from, this);
  const PointLLH* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PointLLH>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.PointLLH)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.PointLLH)
    MergeFrom(*source);
  }
}

void PointLLH::MergeFrom(const PointLLH& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.PointLLH)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.lon() <= 0 && from.lon() >= 0)) {
    _internal_set_lon(from._internal_lon());
  }
  if (!(from.lat() <= 0 && from.lat() >= 0)) {
    _internal_set_lat(from._internal_lat());
  }
  if (!(from.height() <= 0 && from.height() >= 0)) {
    _internal_set_height(from._internal_height());
  }
}

void PointLLH::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.PointLLH)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PointLLH::CopyFrom(const PointLLH& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.PointLLH)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PointLLH::IsInitialized() const {
  return true;
}

void PointLLH::InternalSwap(PointLLH* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PointLLH, height_)
      + sizeof(PointLLH::height_)
      - PROTOBUF_FIELD_OFFSET(PointLLH, lon_)>(
          reinterpret_cast<char*>(&lon_),
          reinterpret_cast<char*>(&other->lon_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PointLLH::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Point2D::_Internal {
 public:
};

Point2D::Point2D(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Point2D)
}
Point2D::Point2D(const Point2D& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.Point2D)
}

void Point2D::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
}

Point2D::~Point2D() {
  // @@protoc_insertion_point(destructor:gwm.common.Point2D)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Point2D::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Point2D::ArenaDtor(void* object) {
  Point2D* _this = reinterpret_cast< Point2D* >(object);
  (void)_this;
}
void Point2D::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Point2D::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Point2D& Point2D::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Point2D_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Point2D::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Point2D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Point2D::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Point2D::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Point2D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Point2D)
  return target;
}

size_t Point2D::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Point2D)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    total_size += 1 + 8;
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Point2D::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Point2D)
  GOOGLE_DCHECK_NE(&from, this);
  const Point2D* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Point2D>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Point2D)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Point2D)
    MergeFrom(*source);
  }
}

void Point2D::MergeFrom(const Point2D& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Point2D)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.x() <= 0 && from.x() >= 0)) {
    _internal_set_x(from._internal_x());
  }
  if (!(from.y() <= 0 && from.y() >= 0)) {
    _internal_set_y(from._internal_y());
  }
}

void Point2D::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Point2D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Point2D::CopyFrom(const Point2D& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Point2D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Point2D::IsInitialized() const {
  return true;
}

void Point2D::InternalSwap(Point2D* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Point2D, y_)
      + sizeof(Point2D::y_)
      - PROTOBUF_FIELD_OFFSET(Point2D, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Point2D::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Point3D::_Internal {
 public:
};

Point3D::Point3D(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Point3D)
}
Point3D::Point3D(const Point3D& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.Point3D)
}

void Point3D::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

Point3D::~Point3D() {
  // @@protoc_insertion_point(destructor:gwm.common.Point3D)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Point3D::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Point3D::ArenaDtor(void* object) {
  Point3D* _this = reinterpret_cast< Point3D* >(object);
  (void)_this;
}
void Point3D::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Point3D::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Point3D& Point3D::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Point3D_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Point3D::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Point3D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Point3D::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Point3D::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Point3D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  // double z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Point3D)
  return target;
}

size_t Point3D::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Point3D)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    total_size += 1 + 8;
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    total_size += 1 + 8;
  }

  // double z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Point3D::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Point3D)
  GOOGLE_DCHECK_NE(&from, this);
  const Point3D* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Point3D>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Point3D)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Point3D)
    MergeFrom(*source);
  }
}

void Point3D::MergeFrom(const Point3D& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Point3D)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.x() <= 0 && from.x() >= 0)) {
    _internal_set_x(from._internal_x());
  }
  if (!(from.y() <= 0 && from.y() >= 0)) {
    _internal_set_y(from._internal_y());
  }
  if (!(from.z() <= 0 && from.z() >= 0)) {
    _internal_set_z(from._internal_z());
  }
}

void Point3D::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Point3D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Point3D::CopyFrom(const Point3D& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Point3D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Point3D::IsInitialized() const {
  return true;
}

void Point3D::InternalSwap(Point3D* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Point3D, z_)
      + sizeof(Point3D::z_)
      - PROTOBUF_FIELD_OFFSET(Point3D, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Point3D::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Vector3::_Internal {
 public:
};

Vector3::Vector3(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Vector3)
}
Vector3::Vector3(const Vector3& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.Vector3)
}

void Vector3::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

Vector3::~Vector3() {
  // @@protoc_insertion_point(destructor:gwm.common.Vector3)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Vector3::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Vector3::ArenaDtor(void* object) {
  Vector3* _this = reinterpret_cast< Vector3* >(object);
  (void)_this;
}
void Vector3::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Vector3::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Vector3& Vector3::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Vector3_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Vector3::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Vector3)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector3::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Vector3::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Vector3)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  // float z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Vector3)
  return target;
}

size_t Vector3::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Vector3)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    total_size += 1 + 4;
  }

  // float y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    total_size += 1 + 4;
  }

  // float z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Vector3::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Vector3)
  GOOGLE_DCHECK_NE(&from, this);
  const Vector3* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Vector3>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Vector3)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Vector3)
    MergeFrom(*source);
  }
}

void Vector3::MergeFrom(const Vector3& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Vector3)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.x() <= 0 && from.x() >= 0)) {
    _internal_set_x(from._internal_x());
  }
  if (!(from.y() <= 0 && from.y() >= 0)) {
    _internal_set_y(from._internal_y());
  }
  if (!(from.z() <= 0 && from.z() >= 0)) {
    _internal_set_z(from._internal_z());
  }
}

void Vector3::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Vector3)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Vector3::CopyFrom(const Vector3& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Vector3)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3::IsInitialized() const {
  return true;
}

void Vector3::InternalSwap(Vector3* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector3, z_)
      + sizeof(Vector3::z_)
      - PROTOBUF_FIELD_OFFSET(Vector3, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector3::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Quaternion::_Internal {
 public:
};

Quaternion::Quaternion(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Quaternion)
}
Quaternion::Quaternion(const Quaternion& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&qx_, &from.qx_,
    static_cast<size_t>(reinterpret_cast<char*>(&qw_) -
    reinterpret_cast<char*>(&qx_)) + sizeof(qw_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.Quaternion)
}

void Quaternion::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&qx_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&qw_) -
      reinterpret_cast<char*>(&qx_)) + sizeof(qw_));
}

Quaternion::~Quaternion() {
  // @@protoc_insertion_point(destructor:gwm.common.Quaternion)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Quaternion::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Quaternion::ArenaDtor(void* object) {
  Quaternion* _this = reinterpret_cast< Quaternion* >(object);
  (void)_this;
}
void Quaternion::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Quaternion::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Quaternion& Quaternion::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Quaternion_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Quaternion::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Quaternion)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&qx_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&qw_) -
      reinterpret_cast<char*>(&qx_)) + sizeof(qw_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Quaternion::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double qx = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          qx_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double qy = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          qy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double qz = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          qz_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double qw = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 33)) {
          qw_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Quaternion::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Quaternion)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double qx = 1;
  if (!(this->qx() <= 0 && this->qx() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_qx(), target);
  }

  // double qy = 2;
  if (!(this->qy() <= 0 && this->qy() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_qy(), target);
  }

  // double qz = 3;
  if (!(this->qz() <= 0 && this->qz() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_qz(), target);
  }

  // double qw = 4;
  if (!(this->qw() <= 0 && this->qw() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_qw(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Quaternion)
  return target;
}

size_t Quaternion::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Quaternion)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double qx = 1;
  if (!(this->qx() <= 0 && this->qx() >= 0)) {
    total_size += 1 + 8;
  }

  // double qy = 2;
  if (!(this->qy() <= 0 && this->qy() >= 0)) {
    total_size += 1 + 8;
  }

  // double qz = 3;
  if (!(this->qz() <= 0 && this->qz() >= 0)) {
    total_size += 1 + 8;
  }

  // double qw = 4;
  if (!(this->qw() <= 0 && this->qw() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Quaternion::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Quaternion)
  GOOGLE_DCHECK_NE(&from, this);
  const Quaternion* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Quaternion>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Quaternion)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Quaternion)
    MergeFrom(*source);
  }
}

void Quaternion::MergeFrom(const Quaternion& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Quaternion)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.qx() <= 0 && from.qx() >= 0)) {
    _internal_set_qx(from._internal_qx());
  }
  if (!(from.qy() <= 0 && from.qy() >= 0)) {
    _internal_set_qy(from._internal_qy());
  }
  if (!(from.qz() <= 0 && from.qz() >= 0)) {
    _internal_set_qz(from._internal_qz());
  }
  if (!(from.qw() <= 0 && from.qw() >= 0)) {
    _internal_set_qw(from._internal_qw());
  }
}

void Quaternion::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Quaternion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Quaternion::CopyFrom(const Quaternion& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Quaternion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaternion::IsInitialized() const {
  return true;
}

void Quaternion::InternalSwap(Quaternion* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Quaternion, qw_)
      + sizeof(Quaternion::qw_)
      - PROTOBUF_FIELD_OFFSET(Quaternion, qx_)>(
          reinterpret_cast<char*>(&qx_),
          reinterpret_cast<char*>(&other->qx_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Quaternion::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Quaternion_f::_Internal {
 public:
};

Quaternion_f::Quaternion_f(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Quaternion_f)
}
Quaternion_f::Quaternion_f(const Quaternion_f& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&qx_, &from.qx_,
    static_cast<size_t>(reinterpret_cast<char*>(&qw_) -
    reinterpret_cast<char*>(&qx_)) + sizeof(qw_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.Quaternion_f)
}

void Quaternion_f::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&qx_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&qw_) -
      reinterpret_cast<char*>(&qx_)) + sizeof(qw_));
}

Quaternion_f::~Quaternion_f() {
  // @@protoc_insertion_point(destructor:gwm.common.Quaternion_f)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Quaternion_f::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Quaternion_f::ArenaDtor(void* object) {
  Quaternion_f* _this = reinterpret_cast< Quaternion_f* >(object);
  (void)_this;
}
void Quaternion_f::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Quaternion_f::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Quaternion_f& Quaternion_f::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Quaternion_f_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Quaternion_f::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Quaternion_f)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&qx_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&qw_) -
      reinterpret_cast<char*>(&qx_)) + sizeof(qw_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Quaternion_f::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // float qx = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          qx_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float qy = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 21)) {
          qy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float qz = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          qz_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float qw = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          qw_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Quaternion_f::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Quaternion_f)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float qx = 1;
  if (!(this->qx() <= 0 && this->qx() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_qx(), target);
  }

  // float qy = 2;
  if (!(this->qy() <= 0 && this->qy() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_qy(), target);
  }

  // float qz = 3;
  if (!(this->qz() <= 0 && this->qz() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_qz(), target);
  }

  // float qw = 4;
  if (!(this->qw() <= 0 && this->qw() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_qw(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Quaternion_f)
  return target;
}

size_t Quaternion_f::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Quaternion_f)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float qx = 1;
  if (!(this->qx() <= 0 && this->qx() >= 0)) {
    total_size += 1 + 4;
  }

  // float qy = 2;
  if (!(this->qy() <= 0 && this->qy() >= 0)) {
    total_size += 1 + 4;
  }

  // float qz = 3;
  if (!(this->qz() <= 0 && this->qz() >= 0)) {
    total_size += 1 + 4;
  }

  // float qw = 4;
  if (!(this->qw() <= 0 && this->qw() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Quaternion_f::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Quaternion_f)
  GOOGLE_DCHECK_NE(&from, this);
  const Quaternion_f* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Quaternion_f>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Quaternion_f)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Quaternion_f)
    MergeFrom(*source);
  }
}

void Quaternion_f::MergeFrom(const Quaternion_f& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Quaternion_f)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.qx() <= 0 && from.qx() >= 0)) {
    _internal_set_qx(from._internal_qx());
  }
  if (!(from.qy() <= 0 && from.qy() >= 0)) {
    _internal_set_qy(from._internal_qy());
  }
  if (!(from.qz() <= 0 && from.qz() >= 0)) {
    _internal_set_qz(from._internal_qz());
  }
  if (!(from.qw() <= 0 && from.qw() >= 0)) {
    _internal_set_qw(from._internal_qw());
  }
}

void Quaternion_f::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Quaternion_f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Quaternion_f::CopyFrom(const Quaternion_f& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Quaternion_f)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaternion_f::IsInitialized() const {
  return true;
}

void Quaternion_f::InternalSwap(Quaternion_f* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Quaternion_f, qw_)
      + sizeof(Quaternion_f::qw_)
      - PROTOBUF_FIELD_OFFSET(Quaternion_f, qx_)>(
          reinterpret_cast<char*>(&qx_),
          reinterpret_cast<char*>(&other->qx_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Quaternion_f::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Polygon::_Internal {
 public:
};

Polygon::Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  point_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Polygon)
}
Polygon::Polygon(const Polygon& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      point_(from.point_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.common.Polygon)
}

void Polygon::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Polygon_common_2fgeometry_2eproto.base);
}

Polygon::~Polygon() {
  // @@protoc_insertion_point(destructor:gwm.common.Polygon)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Polygon::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Polygon::ArenaDtor(void* object) {
  Polygon* _this = reinterpret_cast< Polygon* >(object);
  (void)_this;
}
void Polygon::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Polygon::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Polygon& Polygon::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Polygon_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Polygon::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Polygon)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  point_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polygon::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.common.Point3D point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_point(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Polygon::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Polygon)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.common.Point3D point = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_point_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_point(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Polygon)
  return target;
}

size_t Polygon::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Polygon)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.common.Point3D point = 1;
  total_size += 1UL * this->_internal_point_size();
  for (const auto& msg : this->point_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Polygon::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Polygon)
  GOOGLE_DCHECK_NE(&from, this);
  const Polygon* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Polygon>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Polygon)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Polygon)
    MergeFrom(*source);
  }
}

void Polygon::MergeFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Polygon)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  point_.MergeFrom(from.point_);
}

void Polygon::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Polygon::CopyFrom(const Polygon& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Polygon)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polygon::IsInitialized() const {
  return true;
}

void Polygon::InternalSwap(Polygon* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  point_.InternalSwap(&other->point_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Polygon::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Polyline::_Internal {
 public:
};

Polyline::Polyline(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  point_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Polyline)
}
Polyline::Polyline(const Polyline& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      point_(from.point_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.common.Polyline)
}

void Polyline::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Polyline_common_2fgeometry_2eproto.base);
}

Polyline::~Polyline() {
  // @@protoc_insertion_point(destructor:gwm.common.Polyline)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Polyline::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Polyline::ArenaDtor(void* object) {
  Polyline* _this = reinterpret_cast< Polyline* >(object);
  (void)_this;
}
void Polyline::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Polyline::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Polyline& Polyline::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Polyline_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Polyline::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Polyline)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  point_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polyline::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.common.Point3D point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_point(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Polyline::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Polyline)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.common.Point3D point = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_point_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_point(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Polyline)
  return target;
}

size_t Polyline::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Polyline)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.common.Point3D point = 1;
  total_size += 1UL * this->_internal_point_size();
  for (const auto& msg : this->point_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Polyline::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Polyline)
  GOOGLE_DCHECK_NE(&from, this);
  const Polyline* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Polyline>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Polyline)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Polyline)
    MergeFrom(*source);
  }
}

void Polyline::MergeFrom(const Polyline& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Polyline)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  point_.MergeFrom(from.point_);
}

void Polyline::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Polyline)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Polyline::CopyFrom(const Polyline& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Polyline)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polyline::IsInitialized() const {
  return true;
}

void Polyline::InternalSwap(Polyline* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  point_.InternalSwap(&other->point_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Polyline::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class PolygonLLH::_Internal {
 public:
};

PolygonLLH::PolygonLLH(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  point_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.PolygonLLH)
}
PolygonLLH::PolygonLLH(const PolygonLLH& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      point_(from.point_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.common.PolygonLLH)
}

void PolygonLLH::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_PolygonLLH_common_2fgeometry_2eproto.base);
}

PolygonLLH::~PolygonLLH() {
  // @@protoc_insertion_point(destructor:gwm.common.PolygonLLH)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PolygonLLH::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void PolygonLLH::ArenaDtor(void* object) {
  PolygonLLH* _this = reinterpret_cast< PolygonLLH* >(object);
  (void)_this;
}
void PolygonLLH::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PolygonLLH::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PolygonLLH& PolygonLLH::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PolygonLLH_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void PolygonLLH::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.PolygonLLH)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  point_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PolygonLLH::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.common.PointLLH point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_point(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PolygonLLH::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.PolygonLLH)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.common.PointLLH point = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_point_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_point(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.PolygonLLH)
  return target;
}

size_t PolygonLLH::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.PolygonLLH)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.common.PointLLH point = 1;
  total_size += 1UL * this->_internal_point_size();
  for (const auto& msg : this->point_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PolygonLLH::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.PolygonLLH)
  GOOGLE_DCHECK_NE(&from, this);
  const PolygonLLH* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PolygonLLH>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.PolygonLLH)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.PolygonLLH)
    MergeFrom(*source);
  }
}

void PolygonLLH::MergeFrom(const PolygonLLH& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.PolygonLLH)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  point_.MergeFrom(from.point_);
}

void PolygonLLH::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.PolygonLLH)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PolygonLLH::CopyFrom(const PolygonLLH& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.PolygonLLH)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PolygonLLH::IsInitialized() const {
  return true;
}

void PolygonLLH::InternalSwap(PolygonLLH* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  point_.InternalSwap(&other->point_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PolygonLLH::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class PolylineLLH::_Internal {
 public:
};

PolylineLLH::PolylineLLH(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  point_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.PolylineLLH)
}
PolylineLLH::PolylineLLH(const PolylineLLH& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      point_(from.point_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.common.PolylineLLH)
}

void PolylineLLH::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_PolylineLLH_common_2fgeometry_2eproto.base);
}

PolylineLLH::~PolylineLLH() {
  // @@protoc_insertion_point(destructor:gwm.common.PolylineLLH)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PolylineLLH::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void PolylineLLH::ArenaDtor(void* object) {
  PolylineLLH* _this = reinterpret_cast< PolylineLLH* >(object);
  (void)_this;
}
void PolylineLLH::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PolylineLLH::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PolylineLLH& PolylineLLH::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PolylineLLH_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void PolylineLLH::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.PolylineLLH)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  point_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PolylineLLH::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.common.PointLLH point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_point(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PolylineLLH::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.PolylineLLH)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.common.PointLLH point = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_point_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_point(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.PolylineLLH)
  return target;
}

size_t PolylineLLH::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.PolylineLLH)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.common.PointLLH point = 1;
  total_size += 1UL * this->_internal_point_size();
  for (const auto& msg : this->point_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PolylineLLH::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.PolylineLLH)
  GOOGLE_DCHECK_NE(&from, this);
  const PolylineLLH* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PolylineLLH>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.PolylineLLH)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.PolylineLLH)
    MergeFrom(*source);
  }
}

void PolylineLLH::MergeFrom(const PolylineLLH& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.PolylineLLH)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  point_.MergeFrom(from.point_);
}

void PolylineLLH::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.PolylineLLH)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PolylineLLH::CopyFrom(const PolylineLLH& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.PolylineLLH)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PolylineLLH::IsInitialized() const {
  return true;
}

void PolylineLLH::InternalSwap(PolylineLLH* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  point_.InternalSwap(&other->point_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PolylineLLH::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Transformation3::_Internal {
 public:
  static const ::gwm::common::Vector3& position(const Transformation3* msg);
  static const ::gwm::common::Quaternion_f& orientation(const Transformation3* msg);
};

const ::gwm::common::Vector3&
Transformation3::_Internal::position(const Transformation3* msg) {
  return *msg->position_;
}
const ::gwm::common::Quaternion_f&
Transformation3::_Internal::orientation(const Transformation3* msg) {
  return *msg->orientation_;
}
Transformation3::Transformation3(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Transformation3)
}
Transformation3::Transformation3(const Transformation3& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_position()) {
    position_ = new ::gwm::common::Vector3(*from.position_);
  } else {
    position_ = nullptr;
  }
  if (from._internal_has_orientation()) {
    orientation_ = new ::gwm::common::Quaternion_f(*from.orientation_);
  } else {
    orientation_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:gwm.common.Transformation3)
}

void Transformation3::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Transformation3_common_2fgeometry_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&position_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&orientation_) -
      reinterpret_cast<char*>(&position_)) + sizeof(orientation_));
}

Transformation3::~Transformation3() {
  // @@protoc_insertion_point(destructor:gwm.common.Transformation3)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Transformation3::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete position_;
  if (this != internal_default_instance()) delete orientation_;
}

void Transformation3::ArenaDtor(void* object) {
  Transformation3* _this = reinterpret_cast< Transformation3* >(object);
  (void)_this;
}
void Transformation3::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Transformation3::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Transformation3& Transformation3::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Transformation3_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Transformation3::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Transformation3)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArena() == nullptr && position_ != nullptr) {
    delete position_;
  }
  position_ = nullptr;
  if (GetArena() == nullptr && orientation_ != nullptr) {
    delete orientation_;
  }
  orientation_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Transformation3::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.common.Vector3 position = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.common.Quaternion_f orientation = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_orientation(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Transformation3::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Transformation3)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.common.Vector3 position = 1;
  if (this->has_position()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::position(this), target, stream);
  }

  // .gwm.common.Quaternion_f orientation = 2;
  if (this->has_orientation()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::orientation(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Transformation3)
  return target;
}

size_t Transformation3::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Transformation3)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.common.Vector3 position = 1;
  if (this->has_position()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *position_);
  }

  // .gwm.common.Quaternion_f orientation = 2;
  if (this->has_orientation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *orientation_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Transformation3::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Transformation3)
  GOOGLE_DCHECK_NE(&from, this);
  const Transformation3* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Transformation3>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Transformation3)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Transformation3)
    MergeFrom(*source);
  }
}

void Transformation3::MergeFrom(const Transformation3& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Transformation3)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_position()) {
    _internal_mutable_position()->::gwm::common::Vector3::MergeFrom(from._internal_position());
  }
  if (from.has_orientation()) {
    _internal_mutable_orientation()->::gwm::common::Quaternion_f::MergeFrom(from._internal_orientation());
  }
}

void Transformation3::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Transformation3)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Transformation3::CopyFrom(const Transformation3& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Transformation3)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Transformation3::IsInitialized() const {
  return true;
}

void Transformation3::InternalSwap(Transformation3* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Transformation3, orientation_)
      + sizeof(Transformation3::orientation_)
      - PROTOBUF_FIELD_OFFSET(Transformation3, position_)>(
          reinterpret_cast<char*>(&position_),
          reinterpret_cast<char*>(&other->position_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Transformation3::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Polygon2D::_Internal {
 public:
};

Polygon2D::Polygon2D(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  points_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.Polygon2D)
}
Polygon2D::Polygon2D(const Polygon2D& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      points_(from.points_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.common.Polygon2D)
}

void Polygon2D::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Polygon2D_common_2fgeometry_2eproto.base);
}

Polygon2D::~Polygon2D() {
  // @@protoc_insertion_point(destructor:gwm.common.Polygon2D)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Polygon2D::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Polygon2D::ArenaDtor(void* object) {
  Polygon2D* _this = reinterpret_cast< Polygon2D* >(object);
  (void)_this;
}
void Polygon2D::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Polygon2D::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Polygon2D& Polygon2D::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Polygon2D_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void Polygon2D::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.Polygon2D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Polygon2D::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.common.Point2D points = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Polygon2D::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.Polygon2D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.common.Point2D points = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_points_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_points(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.Polygon2D)
  return target;
}

size_t Polygon2D::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.Polygon2D)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.common.Point2D points = 1;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Polygon2D::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.Polygon2D)
  GOOGLE_DCHECK_NE(&from, this);
  const Polygon2D* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Polygon2D>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.Polygon2D)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.Polygon2D)
    MergeFrom(*source);
  }
}

void Polygon2D::MergeFrom(const Polygon2D& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.Polygon2D)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
}

void Polygon2D::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.Polygon2D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Polygon2D::CopyFrom(const Polygon2D& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.Polygon2D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Polygon2D::IsInitialized() const {
  return true;
}

void Polygon2D::InternalSwap(Polygon2D* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  points_.InternalSwap(&other->points_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Polygon2D::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RotatedRect::_Internal {
 public:
  static const ::gwm::common::Point2D& center(const RotatedRect* msg);
  static const ::gwm::common::Point2D& size(const RotatedRect* msg);
};

const ::gwm::common::Point2D&
RotatedRect::_Internal::center(const RotatedRect* msg) {
  return *msg->center_;
}
const ::gwm::common::Point2D&
RotatedRect::_Internal::size(const RotatedRect* msg) {
  return *msg->size_;
}
RotatedRect::RotatedRect(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.RotatedRect)
}
RotatedRect::RotatedRect(const RotatedRect& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_center()) {
    center_ = new ::gwm::common::Point2D(*from.center_);
  } else {
    center_ = nullptr;
  }
  if (from._internal_has_size()) {
    size_ = new ::gwm::common::Point2D(*from.size_);
  } else {
    size_ = nullptr;
  }
  angle_ = from.angle_;
  // @@protoc_insertion_point(copy_constructor:gwm.common.RotatedRect)
}

void RotatedRect::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RotatedRect_common_2fgeometry_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&center_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&angle_) -
      reinterpret_cast<char*>(&center_)) + sizeof(angle_));
}

RotatedRect::~RotatedRect() {
  // @@protoc_insertion_point(destructor:gwm.common.RotatedRect)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RotatedRect::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete center_;
  if (this != internal_default_instance()) delete size_;
}

void RotatedRect::ArenaDtor(void* object) {
  RotatedRect* _this = reinterpret_cast< RotatedRect* >(object);
  (void)_this;
}
void RotatedRect::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RotatedRect::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RotatedRect& RotatedRect::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RotatedRect_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void RotatedRect::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.RotatedRect)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArena() == nullptr && center_ != nullptr) {
    delete center_;
  }
  center_ = nullptr;
  if (GetArena() == nullptr && size_ != nullptr) {
    delete size_;
  }
  size_ = nullptr;
  angle_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RotatedRect::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.common.Point2D center = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_center(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.common.Point2D size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_size(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float angle = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          angle_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RotatedRect::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.RotatedRect)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.common.Point2D center = 1;
  if (this->has_center()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::center(this), target, stream);
  }

  // .gwm.common.Point2D size = 2;
  if (this->has_size()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::size(this), target, stream);
  }

  // float angle = 3;
  if (!(this->angle() <= 0 && this->angle() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_angle(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.RotatedRect)
  return target;
}

size_t RotatedRect::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.RotatedRect)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.common.Point2D center = 1;
  if (this->has_center()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *center_);
  }

  // .gwm.common.Point2D size = 2;
  if (this->has_size()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *size_);
  }

  // float angle = 3;
  if (!(this->angle() <= 0 && this->angle() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RotatedRect::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.RotatedRect)
  GOOGLE_DCHECK_NE(&from, this);
  const RotatedRect* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RotatedRect>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.RotatedRect)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.RotatedRect)
    MergeFrom(*source);
  }
}

void RotatedRect::MergeFrom(const RotatedRect& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.RotatedRect)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_center()) {
    _internal_mutable_center()->::gwm::common::Point2D::MergeFrom(from._internal_center());
  }
  if (from.has_size()) {
    _internal_mutable_size()->::gwm::common::Point2D::MergeFrom(from._internal_size());
  }
  if (!(from.angle() <= 0 && from.angle() >= 0)) {
    _internal_set_angle(from._internal_angle());
  }
}

void RotatedRect::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.RotatedRect)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RotatedRect::CopyFrom(const RotatedRect& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.RotatedRect)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RotatedRect::IsInitialized() const {
  return true;
}

void RotatedRect::InternalSwap(RotatedRect* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RotatedRect, angle_)
      + sizeof(RotatedRect::angle_)
      - PROTOBUF_FIELD_OFFSET(RotatedRect, center_)>(
          reinterpret_cast<char*>(&center_),
          reinterpret_cast<char*>(&other->center_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RotatedRect::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RotatedRect3D::_Internal {
 public:
  static const ::gwm::common::Point3D& center(const RotatedRect3D* msg);
  static const ::gwm::common::Point3D& size(const RotatedRect3D* msg);
};

const ::gwm::common::Point3D&
RotatedRect3D::_Internal::center(const RotatedRect3D* msg) {
  return *msg->center_;
}
const ::gwm::common::Point3D&
RotatedRect3D::_Internal::size(const RotatedRect3D* msg) {
  return *msg->size_;
}
RotatedRect3D::RotatedRect3D(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.RotatedRect3D)
}
RotatedRect3D::RotatedRect3D(const RotatedRect3D& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_center()) {
    center_ = new ::gwm::common::Point3D(*from.center_);
  } else {
    center_ = nullptr;
  }
  if (from._internal_has_size()) {
    size_ = new ::gwm::common::Point3D(*from.size_);
  } else {
    size_ = nullptr;
  }
  angle_ = from.angle_;
  // @@protoc_insertion_point(copy_constructor:gwm.common.RotatedRect3D)
}

void RotatedRect3D::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RotatedRect3D_common_2fgeometry_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&center_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&angle_) -
      reinterpret_cast<char*>(&center_)) + sizeof(angle_));
}

RotatedRect3D::~RotatedRect3D() {
  // @@protoc_insertion_point(destructor:gwm.common.RotatedRect3D)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RotatedRect3D::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete center_;
  if (this != internal_default_instance()) delete size_;
}

void RotatedRect3D::ArenaDtor(void* object) {
  RotatedRect3D* _this = reinterpret_cast< RotatedRect3D* >(object);
  (void)_this;
}
void RotatedRect3D::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RotatedRect3D::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RotatedRect3D& RotatedRect3D::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RotatedRect3D_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void RotatedRect3D::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.RotatedRect3D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArena() == nullptr && center_ != nullptr) {
    delete center_;
  }
  center_ = nullptr;
  if (GetArena() == nullptr && size_ != nullptr) {
    delete size_;
  }
  size_ = nullptr;
  angle_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RotatedRect3D::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.common.Point3D center = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_center(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.common.Point3D size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_size(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float angle = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 29)) {
          angle_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RotatedRect3D::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.RotatedRect3D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.common.Point3D center = 1;
  if (this->has_center()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::center(this), target, stream);
  }

  // .gwm.common.Point3D size = 2;
  if (this->has_size()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::size(this), target, stream);
  }

  // float angle = 3;
  if (!(this->angle() <= 0 && this->angle() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_angle(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.RotatedRect3D)
  return target;
}

size_t RotatedRect3D::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.RotatedRect3D)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.common.Point3D center = 1;
  if (this->has_center()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *center_);
  }

  // .gwm.common.Point3D size = 2;
  if (this->has_size()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *size_);
  }

  // float angle = 3;
  if (!(this->angle() <= 0 && this->angle() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RotatedRect3D::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.RotatedRect3D)
  GOOGLE_DCHECK_NE(&from, this);
  const RotatedRect3D* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RotatedRect3D>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.RotatedRect3D)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.RotatedRect3D)
    MergeFrom(*source);
  }
}

void RotatedRect3D::MergeFrom(const RotatedRect3D& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.RotatedRect3D)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_center()) {
    _internal_mutable_center()->::gwm::common::Point3D::MergeFrom(from._internal_center());
  }
  if (from.has_size()) {
    _internal_mutable_size()->::gwm::common::Point3D::MergeFrom(from._internal_size());
  }
  if (!(from.angle() <= 0 && from.angle() >= 0)) {
    _internal_set_angle(from._internal_angle());
  }
}

void RotatedRect3D::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.RotatedRect3D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RotatedRect3D::CopyFrom(const RotatedRect3D& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.RotatedRect3D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RotatedRect3D::IsInitialized() const {
  return true;
}

void RotatedRect3D::InternalSwap(RotatedRect3D* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RotatedRect3D, angle_)
      + sizeof(RotatedRect3D::angle_)
      - PROTOBUF_FIELD_OFFSET(RotatedRect3D, center_)>(
          reinterpret_cast<char*>(&center_),
          reinterpret_cast<char*>(&other->center_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RotatedRect3D::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class PlaneCoeffs::_Internal {
 public:
};

PlaneCoeffs::PlaneCoeffs(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.common.PlaneCoeffs)
}
PlaneCoeffs::PlaneCoeffs(const PlaneCoeffs& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&a_, &from.a_,
    static_cast<size_t>(reinterpret_cast<char*>(&d_) -
    reinterpret_cast<char*>(&a_)) + sizeof(d_));
  // @@protoc_insertion_point(copy_constructor:gwm.common.PlaneCoeffs)
}

void PlaneCoeffs::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&a_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&d_) -
      reinterpret_cast<char*>(&a_)) + sizeof(d_));
}

PlaneCoeffs::~PlaneCoeffs() {
  // @@protoc_insertion_point(destructor:gwm.common.PlaneCoeffs)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PlaneCoeffs::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void PlaneCoeffs::ArenaDtor(void* object) {
  PlaneCoeffs* _this = reinterpret_cast< PlaneCoeffs* >(object);
  (void)_this;
}
void PlaneCoeffs::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PlaneCoeffs::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PlaneCoeffs& PlaneCoeffs::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PlaneCoeffs_common_2fgeometry_2eproto.base);
  return *internal_default_instance();
}


void PlaneCoeffs::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.common.PlaneCoeffs)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&a_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&d_) -
      reinterpret_cast<char*>(&a_)) + sizeof(d_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PlaneCoeffs::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double a = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          a_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double b = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          b_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double c = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          c_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double d = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 33)) {
          d_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PlaneCoeffs::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.common.PlaneCoeffs)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double a = 1;
  if (!(this->a() <= 0 && this->a() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_a(), target);
  }

  // double b = 2;
  if (!(this->b() <= 0 && this->b() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_b(), target);
  }

  // double c = 3;
  if (!(this->c() <= 0 && this->c() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_c(), target);
  }

  // double d = 4;
  if (!(this->d() <= 0 && this->d() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_d(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.common.PlaneCoeffs)
  return target;
}

size_t PlaneCoeffs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.common.PlaneCoeffs)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double a = 1;
  if (!(this->a() <= 0 && this->a() >= 0)) {
    total_size += 1 + 8;
  }

  // double b = 2;
  if (!(this->b() <= 0 && this->b() >= 0)) {
    total_size += 1 + 8;
  }

  // double c = 3;
  if (!(this->c() <= 0 && this->c() >= 0)) {
    total_size += 1 + 8;
  }

  // double d = 4;
  if (!(this->d() <= 0 && this->d() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PlaneCoeffs::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.common.PlaneCoeffs)
  GOOGLE_DCHECK_NE(&from, this);
  const PlaneCoeffs* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PlaneCoeffs>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.common.PlaneCoeffs)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.common.PlaneCoeffs)
    MergeFrom(*source);
  }
}

void PlaneCoeffs::MergeFrom(const PlaneCoeffs& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.common.PlaneCoeffs)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.a() <= 0 && from.a() >= 0)) {
    _internal_set_a(from._internal_a());
  }
  if (!(from.b() <= 0 && from.b() >= 0)) {
    _internal_set_b(from._internal_b());
  }
  if (!(from.c() <= 0 && from.c() >= 0)) {
    _internal_set_c(from._internal_c());
  }
  if (!(from.d() <= 0 && from.d() >= 0)) {
    _internal_set_d(from._internal_d());
  }
}

void PlaneCoeffs::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.common.PlaneCoeffs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PlaneCoeffs::CopyFrom(const PlaneCoeffs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.common.PlaneCoeffs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlaneCoeffs::IsInitialized() const {
  return true;
}

void PlaneCoeffs::InternalSwap(PlaneCoeffs* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PlaneCoeffs, d_)
      + sizeof(PlaneCoeffs::d_)
      - PROTOBUF_FIELD_OFFSET(PlaneCoeffs, a_)>(
          reinterpret_cast<char*>(&a_),
          reinterpret_cast<char*>(&other->a_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PlaneCoeffs::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace common
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::gwm::common::PointENU* Arena::CreateMaybeMessage< ::gwm::common::PointENU >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::PointENU >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::PointLLH* Arena::CreateMaybeMessage< ::gwm::common::PointLLH >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::PointLLH >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Point2D* Arena::CreateMaybeMessage< ::gwm::common::Point2D >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Point2D >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Point3D* Arena::CreateMaybeMessage< ::gwm::common::Point3D >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Point3D >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Vector3* Arena::CreateMaybeMessage< ::gwm::common::Vector3 >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Vector3 >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Quaternion* Arena::CreateMaybeMessage< ::gwm::common::Quaternion >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Quaternion >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Quaternion_f* Arena::CreateMaybeMessage< ::gwm::common::Quaternion_f >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Quaternion_f >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Polygon* Arena::CreateMaybeMessage< ::gwm::common::Polygon >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Polygon >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Polyline* Arena::CreateMaybeMessage< ::gwm::common::Polyline >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Polyline >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::PolygonLLH* Arena::CreateMaybeMessage< ::gwm::common::PolygonLLH >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::PolygonLLH >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::PolylineLLH* Arena::CreateMaybeMessage< ::gwm::common::PolylineLLH >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::PolylineLLH >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Transformation3* Arena::CreateMaybeMessage< ::gwm::common::Transformation3 >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Transformation3 >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::Polygon2D* Arena::CreateMaybeMessage< ::gwm::common::Polygon2D >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::Polygon2D >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::RotatedRect* Arena::CreateMaybeMessage< ::gwm::common::RotatedRect >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::RotatedRect >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::RotatedRect3D* Arena::CreateMaybeMessage< ::gwm::common::RotatedRect3D >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::RotatedRect3D >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::common::PlaneCoeffs* Arena::CreateMaybeMessage< ::gwm::common::PlaneCoeffs >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::common::PlaneCoeffs >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
