// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: common/geometry.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_common_2fgeometry_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_common_2fgeometry_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_common_2fgeometry_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[16]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_common_2fgeometry_2eproto;
namespace gwm {
namespace common {
class PlaneCoeffs;
class PlaneCoeffsDefaultTypeInternal;
extern PlaneCoeffsDefaultTypeInternal _PlaneCoeffs_default_instance_;
class Point2D;
class Point2DDefaultTypeInternal;
extern Point2DDefaultTypeInternal _Point2D_default_instance_;
class Point3D;
class Point3DDefaultTypeInternal;
extern Point3DDefaultTypeInternal _Point3D_default_instance_;
class PointENU;
class PointENUDefaultTypeInternal;
extern PointENUDefaultTypeInternal _PointENU_default_instance_;
class PointLLH;
class PointLLHDefaultTypeInternal;
extern PointLLHDefaultTypeInternal _PointLLH_default_instance_;
class Polygon;
class PolygonDefaultTypeInternal;
extern PolygonDefaultTypeInternal _Polygon_default_instance_;
class Polygon2D;
class Polygon2DDefaultTypeInternal;
extern Polygon2DDefaultTypeInternal _Polygon2D_default_instance_;
class PolygonLLH;
class PolygonLLHDefaultTypeInternal;
extern PolygonLLHDefaultTypeInternal _PolygonLLH_default_instance_;
class Polyline;
class PolylineDefaultTypeInternal;
extern PolylineDefaultTypeInternal _Polyline_default_instance_;
class PolylineLLH;
class PolylineLLHDefaultTypeInternal;
extern PolylineLLHDefaultTypeInternal _PolylineLLH_default_instance_;
class Quaternion;
class QuaternionDefaultTypeInternal;
extern QuaternionDefaultTypeInternal _Quaternion_default_instance_;
class Quaternion_f;
class Quaternion_fDefaultTypeInternal;
extern Quaternion_fDefaultTypeInternal _Quaternion_f_default_instance_;
class RotatedRect;
class RotatedRectDefaultTypeInternal;
extern RotatedRectDefaultTypeInternal _RotatedRect_default_instance_;
class RotatedRect3D;
class RotatedRect3DDefaultTypeInternal;
extern RotatedRect3DDefaultTypeInternal _RotatedRect3D_default_instance_;
class Transformation3;
class Transformation3DefaultTypeInternal;
extern Transformation3DefaultTypeInternal _Transformation3_default_instance_;
class Vector3;
class Vector3DefaultTypeInternal;
extern Vector3DefaultTypeInternal _Vector3_default_instance_;
}  // namespace common
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> ::gwm::common::PlaneCoeffs* Arena::CreateMaybeMessage<::gwm::common::PlaneCoeffs>(Arena*);
template<> ::gwm::common::Point2D* Arena::CreateMaybeMessage<::gwm::common::Point2D>(Arena*);
template<> ::gwm::common::Point3D* Arena::CreateMaybeMessage<::gwm::common::Point3D>(Arena*);
template<> ::gwm::common::PointENU* Arena::CreateMaybeMessage<::gwm::common::PointENU>(Arena*);
template<> ::gwm::common::PointLLH* Arena::CreateMaybeMessage<::gwm::common::PointLLH>(Arena*);
template<> ::gwm::common::Polygon* Arena::CreateMaybeMessage<::gwm::common::Polygon>(Arena*);
template<> ::gwm::common::Polygon2D* Arena::CreateMaybeMessage<::gwm::common::Polygon2D>(Arena*);
template<> ::gwm::common::PolygonLLH* Arena::CreateMaybeMessage<::gwm::common::PolygonLLH>(Arena*);
template<> ::gwm::common::Polyline* Arena::CreateMaybeMessage<::gwm::common::Polyline>(Arena*);
template<> ::gwm::common::PolylineLLH* Arena::CreateMaybeMessage<::gwm::common::PolylineLLH>(Arena*);
template<> ::gwm::common::Quaternion* Arena::CreateMaybeMessage<::gwm::common::Quaternion>(Arena*);
template<> ::gwm::common::Quaternion_f* Arena::CreateMaybeMessage<::gwm::common::Quaternion_f>(Arena*);
template<> ::gwm::common::RotatedRect* Arena::CreateMaybeMessage<::gwm::common::RotatedRect>(Arena*);
template<> ::gwm::common::RotatedRect3D* Arena::CreateMaybeMessage<::gwm::common::RotatedRect3D>(Arena*);
template<> ::gwm::common::Transformation3* Arena::CreateMaybeMessage<::gwm::common::Transformation3>(Arena*);
template<> ::gwm::common::Vector3* Arena::CreateMaybeMessage<::gwm::common::Vector3>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace gwm {
namespace common {

// ===================================================================

class PointENU PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.PointENU) */ {
 public:
  inline PointENU() : PointENU(nullptr) {}
  virtual ~PointENU();

  PointENU(const PointENU& from);
  PointENU(PointENU&& from) noexcept
    : PointENU() {
    *this = ::std::move(from);
  }

  inline PointENU& operator=(const PointENU& from) {
    CopyFrom(from);
    return *this;
  }
  inline PointENU& operator=(PointENU&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PointENU& default_instance();

  static inline const PointENU* internal_default_instance() {
    return reinterpret_cast<const PointENU*>(
               &_PointENU_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PointENU& a, PointENU& b) {
    a.Swap(&b);
  }
  inline void Swap(PointENU* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PointENU* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PointENU* New() const final {
    return CreateMaybeMessage<PointENU>(nullptr);
  }

  PointENU* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PointENU>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PointENU& from);
  void MergeFrom(const PointENU& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PointENU* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.PointENU";
  }
  protected:
  explicit PointENU(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 3;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.PointENU)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double x_;
  double y_;
  double z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class PointLLH PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.PointLLH) */ {
 public:
  inline PointLLH() : PointLLH(nullptr) {}
  virtual ~PointLLH();

  PointLLH(const PointLLH& from);
  PointLLH(PointLLH&& from) noexcept
    : PointLLH() {
    *this = ::std::move(from);
  }

  inline PointLLH& operator=(const PointLLH& from) {
    CopyFrom(from);
    return *this;
  }
  inline PointLLH& operator=(PointLLH&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PointLLH& default_instance();

  static inline const PointLLH* internal_default_instance() {
    return reinterpret_cast<const PointLLH*>(
               &_PointLLH_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PointLLH& a, PointLLH& b) {
    a.Swap(&b);
  }
  inline void Swap(PointLLH* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PointLLH* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PointLLH* New() const final {
    return CreateMaybeMessage<PointLLH>(nullptr);
  }

  PointLLH* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PointLLH>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PointLLH& from);
  void MergeFrom(const PointLLH& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PointLLH* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.PointLLH";
  }
  protected:
  explicit PointLLH(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLonFieldNumber = 1,
    kLatFieldNumber = 2,
    kHeightFieldNumber = 3,
  };
  // double lon = 1;
  void clear_lon();
  double lon() const;
  void set_lon(double value);
  private:
  double _internal_lon() const;
  void _internal_set_lon(double value);
  public:

  // double lat = 2;
  void clear_lat();
  double lat() const;
  void set_lat(double value);
  private:
  double _internal_lat() const;
  void _internal_set_lat(double value);
  public:

  // double height = 3;
  void clear_height();
  double height() const;
  void set_height(double value);
  private:
  double _internal_height() const;
  void _internal_set_height(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.PointLLH)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double lon_;
  double lat_;
  double height_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Point2D PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Point2D) */ {
 public:
  inline Point2D() : Point2D(nullptr) {}
  virtual ~Point2D();

  Point2D(const Point2D& from);
  Point2D(Point2D&& from) noexcept
    : Point2D() {
    *this = ::std::move(from);
  }

  inline Point2D& operator=(const Point2D& from) {
    CopyFrom(from);
    return *this;
  }
  inline Point2D& operator=(Point2D&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Point2D& default_instance();

  static inline const Point2D* internal_default_instance() {
    return reinterpret_cast<const Point2D*>(
               &_Point2D_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Point2D& a, Point2D& b) {
    a.Swap(&b);
  }
  inline void Swap(Point2D* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Point2D* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Point2D* New() const final {
    return CreateMaybeMessage<Point2D>(nullptr);
  }

  Point2D* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Point2D>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Point2D& from);
  void MergeFrom(const Point2D& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Point2D* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Point2D";
  }
  protected:
  explicit Point2D(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.Point2D)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double x_;
  double y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Point3D PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Point3D) */ {
 public:
  inline Point3D() : Point3D(nullptr) {}
  virtual ~Point3D();

  Point3D(const Point3D& from);
  Point3D(Point3D&& from) noexcept
    : Point3D() {
    *this = ::std::move(from);
  }

  inline Point3D& operator=(const Point3D& from) {
    CopyFrom(from);
    return *this;
  }
  inline Point3D& operator=(Point3D&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Point3D& default_instance();

  static inline const Point3D* internal_default_instance() {
    return reinterpret_cast<const Point3D*>(
               &_Point3D_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Point3D& a, Point3D& b) {
    a.Swap(&b);
  }
  inline void Swap(Point3D* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Point3D* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Point3D* New() const final {
    return CreateMaybeMessage<Point3D>(nullptr);
  }

  Point3D* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Point3D>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Point3D& from);
  void MergeFrom(const Point3D& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Point3D* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Point3D";
  }
  protected:
  explicit Point3D(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 3;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.Point3D)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double x_;
  double y_;
  double z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Vector3 PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Vector3) */ {
 public:
  inline Vector3() : Vector3(nullptr) {}
  virtual ~Vector3();

  Vector3(const Vector3& from);
  Vector3(Vector3&& from) noexcept
    : Vector3() {
    *this = ::std::move(from);
  }

  inline Vector3& operator=(const Vector3& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vector3& operator=(Vector3&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Vector3& default_instance();

  static inline const Vector3* internal_default_instance() {
    return reinterpret_cast<const Vector3*>(
               &_Vector3_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Vector3& a, Vector3& b) {
    a.Swap(&b);
  }
  inline void Swap(Vector3* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vector3* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Vector3* New() const final {
    return CreateMaybeMessage<Vector3>(nullptr);
  }

  Vector3* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Vector3>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Vector3& from);
  void MergeFrom(const Vector3& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Vector3";
  }
  protected:
  explicit Vector3(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // float x = 1;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 2;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // float z = 3;
  void clear_z();
  float z() const;
  void set_z(float value);
  private:
  float _internal_z() const;
  void _internal_set_z(float value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.Vector3)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float x_;
  float y_;
  float z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Quaternion PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Quaternion) */ {
 public:
  inline Quaternion() : Quaternion(nullptr) {}
  virtual ~Quaternion();

  Quaternion(const Quaternion& from);
  Quaternion(Quaternion&& from) noexcept
    : Quaternion() {
    *this = ::std::move(from);
  }

  inline Quaternion& operator=(const Quaternion& from) {
    CopyFrom(from);
    return *this;
  }
  inline Quaternion& operator=(Quaternion&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Quaternion& default_instance();

  static inline const Quaternion* internal_default_instance() {
    return reinterpret_cast<const Quaternion*>(
               &_Quaternion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Quaternion& a, Quaternion& b) {
    a.Swap(&b);
  }
  inline void Swap(Quaternion* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Quaternion* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Quaternion* New() const final {
    return CreateMaybeMessage<Quaternion>(nullptr);
  }

  Quaternion* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Quaternion>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Quaternion& from);
  void MergeFrom(const Quaternion& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quaternion* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Quaternion";
  }
  protected:
  explicit Quaternion(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQxFieldNumber = 1,
    kQyFieldNumber = 2,
    kQzFieldNumber = 3,
    kQwFieldNumber = 4,
  };
  // double qx = 1;
  void clear_qx();
  double qx() const;
  void set_qx(double value);
  private:
  double _internal_qx() const;
  void _internal_set_qx(double value);
  public:

  // double qy = 2;
  void clear_qy();
  double qy() const;
  void set_qy(double value);
  private:
  double _internal_qy() const;
  void _internal_set_qy(double value);
  public:

  // double qz = 3;
  void clear_qz();
  double qz() const;
  void set_qz(double value);
  private:
  double _internal_qz() const;
  void _internal_set_qz(double value);
  public:

  // double qw = 4;
  void clear_qw();
  double qw() const;
  void set_qw(double value);
  private:
  double _internal_qw() const;
  void _internal_set_qw(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.Quaternion)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double qx_;
  double qy_;
  double qz_;
  double qw_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Quaternion_f PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Quaternion_f) */ {
 public:
  inline Quaternion_f() : Quaternion_f(nullptr) {}
  virtual ~Quaternion_f();

  Quaternion_f(const Quaternion_f& from);
  Quaternion_f(Quaternion_f&& from) noexcept
    : Quaternion_f() {
    *this = ::std::move(from);
  }

  inline Quaternion_f& operator=(const Quaternion_f& from) {
    CopyFrom(from);
    return *this;
  }
  inline Quaternion_f& operator=(Quaternion_f&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Quaternion_f& default_instance();

  static inline const Quaternion_f* internal_default_instance() {
    return reinterpret_cast<const Quaternion_f*>(
               &_Quaternion_f_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Quaternion_f& a, Quaternion_f& b) {
    a.Swap(&b);
  }
  inline void Swap(Quaternion_f* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Quaternion_f* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Quaternion_f* New() const final {
    return CreateMaybeMessage<Quaternion_f>(nullptr);
  }

  Quaternion_f* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Quaternion_f>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Quaternion_f& from);
  void MergeFrom(const Quaternion_f& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quaternion_f* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Quaternion_f";
  }
  protected:
  explicit Quaternion_f(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kQxFieldNumber = 1,
    kQyFieldNumber = 2,
    kQzFieldNumber = 3,
    kQwFieldNumber = 4,
  };
  // float qx = 1;
  void clear_qx();
  float qx() const;
  void set_qx(float value);
  private:
  float _internal_qx() const;
  void _internal_set_qx(float value);
  public:

  // float qy = 2;
  void clear_qy();
  float qy() const;
  void set_qy(float value);
  private:
  float _internal_qy() const;
  void _internal_set_qy(float value);
  public:

  // float qz = 3;
  void clear_qz();
  float qz() const;
  void set_qz(float value);
  private:
  float _internal_qz() const;
  void _internal_set_qz(float value);
  public:

  // float qw = 4;
  void clear_qw();
  float qw() const;
  void set_qw(float value);
  private:
  float _internal_qw() const;
  void _internal_set_qw(float value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.Quaternion_f)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float qx_;
  float qy_;
  float qz_;
  float qw_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Polygon PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Polygon) */ {
 public:
  inline Polygon() : Polygon(nullptr) {}
  virtual ~Polygon();

  Polygon(const Polygon& from);
  Polygon(Polygon&& from) noexcept
    : Polygon() {
    *this = ::std::move(from);
  }

  inline Polygon& operator=(const Polygon& from) {
    CopyFrom(from);
    return *this;
  }
  inline Polygon& operator=(Polygon&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Polygon& default_instance();

  static inline const Polygon* internal_default_instance() {
    return reinterpret_cast<const Polygon*>(
               &_Polygon_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(Polygon& a, Polygon& b) {
    a.Swap(&b);
  }
  inline void Swap(Polygon* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Polygon* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Polygon* New() const final {
    return CreateMaybeMessage<Polygon>(nullptr);
  }

  Polygon* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Polygon>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Polygon& from);
  void MergeFrom(const Polygon& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polygon* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Polygon";
  }
  protected:
  explicit Polygon(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointFieldNumber = 1,
  };
  // repeated .gwm.common.Point3D point = 1;
  int point_size() const;
  private:
  int _internal_point_size() const;
  public:
  void clear_point();
  ::gwm::common::Point3D* mutable_point(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >*
      mutable_point();
  private:
  const ::gwm::common::Point3D& _internal_point(int index) const;
  ::gwm::common::Point3D* _internal_add_point();
  public:
  const ::gwm::common::Point3D& point(int index) const;
  ::gwm::common::Point3D* add_point();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >&
      point() const;

  // @@protoc_insertion_point(class_scope:gwm.common.Polygon)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D > point_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Polyline PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Polyline) */ {
 public:
  inline Polyline() : Polyline(nullptr) {}
  virtual ~Polyline();

  Polyline(const Polyline& from);
  Polyline(Polyline&& from) noexcept
    : Polyline() {
    *this = ::std::move(from);
  }

  inline Polyline& operator=(const Polyline& from) {
    CopyFrom(from);
    return *this;
  }
  inline Polyline& operator=(Polyline&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Polyline& default_instance();

  static inline const Polyline* internal_default_instance() {
    return reinterpret_cast<const Polyline*>(
               &_Polyline_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(Polyline& a, Polyline& b) {
    a.Swap(&b);
  }
  inline void Swap(Polyline* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Polyline* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Polyline* New() const final {
    return CreateMaybeMessage<Polyline>(nullptr);
  }

  Polyline* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Polyline>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Polyline& from);
  void MergeFrom(const Polyline& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polyline* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Polyline";
  }
  protected:
  explicit Polyline(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointFieldNumber = 1,
  };
  // repeated .gwm.common.Point3D point = 1;
  int point_size() const;
  private:
  int _internal_point_size() const;
  public:
  void clear_point();
  ::gwm::common::Point3D* mutable_point(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >*
      mutable_point();
  private:
  const ::gwm::common::Point3D& _internal_point(int index) const;
  ::gwm::common::Point3D* _internal_add_point();
  public:
  const ::gwm::common::Point3D& point(int index) const;
  ::gwm::common::Point3D* add_point();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >&
      point() const;

  // @@protoc_insertion_point(class_scope:gwm.common.Polyline)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D > point_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class PolygonLLH PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.PolygonLLH) */ {
 public:
  inline PolygonLLH() : PolygonLLH(nullptr) {}
  virtual ~PolygonLLH();

  PolygonLLH(const PolygonLLH& from);
  PolygonLLH(PolygonLLH&& from) noexcept
    : PolygonLLH() {
    *this = ::std::move(from);
  }

  inline PolygonLLH& operator=(const PolygonLLH& from) {
    CopyFrom(from);
    return *this;
  }
  inline PolygonLLH& operator=(PolygonLLH&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PolygonLLH& default_instance();

  static inline const PolygonLLH* internal_default_instance() {
    return reinterpret_cast<const PolygonLLH*>(
               &_PolygonLLH_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(PolygonLLH& a, PolygonLLH& b) {
    a.Swap(&b);
  }
  inline void Swap(PolygonLLH* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PolygonLLH* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PolygonLLH* New() const final {
    return CreateMaybeMessage<PolygonLLH>(nullptr);
  }

  PolygonLLH* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PolygonLLH>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PolygonLLH& from);
  void MergeFrom(const PolygonLLH& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PolygonLLH* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.PolygonLLH";
  }
  protected:
  explicit PolygonLLH(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointFieldNumber = 1,
  };
  // repeated .gwm.common.PointLLH point = 1;
  int point_size() const;
  private:
  int _internal_point_size() const;
  public:
  void clear_point();
  ::gwm::common::PointLLH* mutable_point(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >*
      mutable_point();
  private:
  const ::gwm::common::PointLLH& _internal_point(int index) const;
  ::gwm::common::PointLLH* _internal_add_point();
  public:
  const ::gwm::common::PointLLH& point(int index) const;
  ::gwm::common::PointLLH* add_point();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >&
      point() const;

  // @@protoc_insertion_point(class_scope:gwm.common.PolygonLLH)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH > point_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class PolylineLLH PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.PolylineLLH) */ {
 public:
  inline PolylineLLH() : PolylineLLH(nullptr) {}
  virtual ~PolylineLLH();

  PolylineLLH(const PolylineLLH& from);
  PolylineLLH(PolylineLLH&& from) noexcept
    : PolylineLLH() {
    *this = ::std::move(from);
  }

  inline PolylineLLH& operator=(const PolylineLLH& from) {
    CopyFrom(from);
    return *this;
  }
  inline PolylineLLH& operator=(PolylineLLH&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PolylineLLH& default_instance();

  static inline const PolylineLLH* internal_default_instance() {
    return reinterpret_cast<const PolylineLLH*>(
               &_PolylineLLH_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(PolylineLLH& a, PolylineLLH& b) {
    a.Swap(&b);
  }
  inline void Swap(PolylineLLH* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PolylineLLH* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PolylineLLH* New() const final {
    return CreateMaybeMessage<PolylineLLH>(nullptr);
  }

  PolylineLLH* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PolylineLLH>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PolylineLLH& from);
  void MergeFrom(const PolylineLLH& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PolylineLLH* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.PolylineLLH";
  }
  protected:
  explicit PolylineLLH(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointFieldNumber = 1,
  };
  // repeated .gwm.common.PointLLH point = 1;
  int point_size() const;
  private:
  int _internal_point_size() const;
  public:
  void clear_point();
  ::gwm::common::PointLLH* mutable_point(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >*
      mutable_point();
  private:
  const ::gwm::common::PointLLH& _internal_point(int index) const;
  ::gwm::common::PointLLH* _internal_add_point();
  public:
  const ::gwm::common::PointLLH& point(int index) const;
  ::gwm::common::PointLLH* add_point();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >&
      point() const;

  // @@protoc_insertion_point(class_scope:gwm.common.PolylineLLH)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH > point_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Transformation3 PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Transformation3) */ {
 public:
  inline Transformation3() : Transformation3(nullptr) {}
  virtual ~Transformation3();

  Transformation3(const Transformation3& from);
  Transformation3(Transformation3&& from) noexcept
    : Transformation3() {
    *this = ::std::move(from);
  }

  inline Transformation3& operator=(const Transformation3& from) {
    CopyFrom(from);
    return *this;
  }
  inline Transformation3& operator=(Transformation3&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Transformation3& default_instance();

  static inline const Transformation3* internal_default_instance() {
    return reinterpret_cast<const Transformation3*>(
               &_Transformation3_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(Transformation3& a, Transformation3& b) {
    a.Swap(&b);
  }
  inline void Swap(Transformation3* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Transformation3* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Transformation3* New() const final {
    return CreateMaybeMessage<Transformation3>(nullptr);
  }

  Transformation3* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Transformation3>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Transformation3& from);
  void MergeFrom(const Transformation3& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Transformation3* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Transformation3";
  }
  protected:
  explicit Transformation3(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPositionFieldNumber = 1,
    kOrientationFieldNumber = 2,
  };
  // .gwm.common.Vector3 position = 1;
  bool has_position() const;
  private:
  bool _internal_has_position() const;
  public:
  void clear_position();
  const ::gwm::common::Vector3& position() const;
  ::gwm::common::Vector3* release_position();
  ::gwm::common::Vector3* mutable_position();
  void set_allocated_position(::gwm::common::Vector3* position);
  private:
  const ::gwm::common::Vector3& _internal_position() const;
  ::gwm::common::Vector3* _internal_mutable_position();
  public:
  void unsafe_arena_set_allocated_position(
      ::gwm::common::Vector3* position);
  ::gwm::common::Vector3* unsafe_arena_release_position();

  // .gwm.common.Quaternion_f orientation = 2;
  bool has_orientation() const;
  private:
  bool _internal_has_orientation() const;
  public:
  void clear_orientation();
  const ::gwm::common::Quaternion_f& orientation() const;
  ::gwm::common::Quaternion_f* release_orientation();
  ::gwm::common::Quaternion_f* mutable_orientation();
  void set_allocated_orientation(::gwm::common::Quaternion_f* orientation);
  private:
  const ::gwm::common::Quaternion_f& _internal_orientation() const;
  ::gwm::common::Quaternion_f* _internal_mutable_orientation();
  public:
  void unsafe_arena_set_allocated_orientation(
      ::gwm::common::Quaternion_f* orientation);
  ::gwm::common::Quaternion_f* unsafe_arena_release_orientation();

  // @@protoc_insertion_point(class_scope:gwm.common.Transformation3)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::gwm::common::Vector3* position_;
  ::gwm::common::Quaternion_f* orientation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Polygon2D PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.Polygon2D) */ {
 public:
  inline Polygon2D() : Polygon2D(nullptr) {}
  virtual ~Polygon2D();

  Polygon2D(const Polygon2D& from);
  Polygon2D(Polygon2D&& from) noexcept
    : Polygon2D() {
    *this = ::std::move(from);
  }

  inline Polygon2D& operator=(const Polygon2D& from) {
    CopyFrom(from);
    return *this;
  }
  inline Polygon2D& operator=(Polygon2D&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Polygon2D& default_instance();

  static inline const Polygon2D* internal_default_instance() {
    return reinterpret_cast<const Polygon2D*>(
               &_Polygon2D_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(Polygon2D& a, Polygon2D& b) {
    a.Swap(&b);
  }
  inline void Swap(Polygon2D* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Polygon2D* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Polygon2D* New() const final {
    return CreateMaybeMessage<Polygon2D>(nullptr);
  }

  Polygon2D* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Polygon2D>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Polygon2D& from);
  void MergeFrom(const Polygon2D& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Polygon2D* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.Polygon2D";
  }
  protected:
  explicit Polygon2D(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 1,
  };
  // repeated .gwm.common.Point2D points = 1;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::gwm::common::Point2D* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point2D >*
      mutable_points();
  private:
  const ::gwm::common::Point2D& _internal_points(int index) const;
  ::gwm::common::Point2D* _internal_add_points();
  public:
  const ::gwm::common::Point2D& points(int index) const;
  ::gwm::common::Point2D* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point2D >&
      points() const;

  // @@protoc_insertion_point(class_scope:gwm.common.Polygon2D)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point2D > points_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class RotatedRect PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.RotatedRect) */ {
 public:
  inline RotatedRect() : RotatedRect(nullptr) {}
  virtual ~RotatedRect();

  RotatedRect(const RotatedRect& from);
  RotatedRect(RotatedRect&& from) noexcept
    : RotatedRect() {
    *this = ::std::move(from);
  }

  inline RotatedRect& operator=(const RotatedRect& from) {
    CopyFrom(from);
    return *this;
  }
  inline RotatedRect& operator=(RotatedRect&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RotatedRect& default_instance();

  static inline const RotatedRect* internal_default_instance() {
    return reinterpret_cast<const RotatedRect*>(
               &_RotatedRect_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RotatedRect& a, RotatedRect& b) {
    a.Swap(&b);
  }
  inline void Swap(RotatedRect* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RotatedRect* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RotatedRect* New() const final {
    return CreateMaybeMessage<RotatedRect>(nullptr);
  }

  RotatedRect* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RotatedRect>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RotatedRect& from);
  void MergeFrom(const RotatedRect& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RotatedRect* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.RotatedRect";
  }
  protected:
  explicit RotatedRect(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCenterFieldNumber = 1,
    kSizeFieldNumber = 2,
    kAngleFieldNumber = 3,
  };
  // .gwm.common.Point2D center = 1;
  bool has_center() const;
  private:
  bool _internal_has_center() const;
  public:
  void clear_center();
  const ::gwm::common::Point2D& center() const;
  ::gwm::common::Point2D* release_center();
  ::gwm::common::Point2D* mutable_center();
  void set_allocated_center(::gwm::common::Point2D* center);
  private:
  const ::gwm::common::Point2D& _internal_center() const;
  ::gwm::common::Point2D* _internal_mutable_center();
  public:
  void unsafe_arena_set_allocated_center(
      ::gwm::common::Point2D* center);
  ::gwm::common::Point2D* unsafe_arena_release_center();

  // .gwm.common.Point2D size = 2;
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  const ::gwm::common::Point2D& size() const;
  ::gwm::common::Point2D* release_size();
  ::gwm::common::Point2D* mutable_size();
  void set_allocated_size(::gwm::common::Point2D* size);
  private:
  const ::gwm::common::Point2D& _internal_size() const;
  ::gwm::common::Point2D* _internal_mutable_size();
  public:
  void unsafe_arena_set_allocated_size(
      ::gwm::common::Point2D* size);
  ::gwm::common::Point2D* unsafe_arena_release_size();

  // float angle = 3;
  void clear_angle();
  float angle() const;
  void set_angle(float value);
  private:
  float _internal_angle() const;
  void _internal_set_angle(float value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.RotatedRect)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::gwm::common::Point2D* center_;
  ::gwm::common::Point2D* size_;
  float angle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class RotatedRect3D PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.RotatedRect3D) */ {
 public:
  inline RotatedRect3D() : RotatedRect3D(nullptr) {}
  virtual ~RotatedRect3D();

  RotatedRect3D(const RotatedRect3D& from);
  RotatedRect3D(RotatedRect3D&& from) noexcept
    : RotatedRect3D() {
    *this = ::std::move(from);
  }

  inline RotatedRect3D& operator=(const RotatedRect3D& from) {
    CopyFrom(from);
    return *this;
  }
  inline RotatedRect3D& operator=(RotatedRect3D&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RotatedRect3D& default_instance();

  static inline const RotatedRect3D* internal_default_instance() {
    return reinterpret_cast<const RotatedRect3D*>(
               &_RotatedRect3D_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RotatedRect3D& a, RotatedRect3D& b) {
    a.Swap(&b);
  }
  inline void Swap(RotatedRect3D* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RotatedRect3D* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RotatedRect3D* New() const final {
    return CreateMaybeMessage<RotatedRect3D>(nullptr);
  }

  RotatedRect3D* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RotatedRect3D>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RotatedRect3D& from);
  void MergeFrom(const RotatedRect3D& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RotatedRect3D* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.RotatedRect3D";
  }
  protected:
  explicit RotatedRect3D(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCenterFieldNumber = 1,
    kSizeFieldNumber = 2,
    kAngleFieldNumber = 3,
  };
  // .gwm.common.Point3D center = 1;
  bool has_center() const;
  private:
  bool _internal_has_center() const;
  public:
  void clear_center();
  const ::gwm::common::Point3D& center() const;
  ::gwm::common::Point3D* release_center();
  ::gwm::common::Point3D* mutable_center();
  void set_allocated_center(::gwm::common::Point3D* center);
  private:
  const ::gwm::common::Point3D& _internal_center() const;
  ::gwm::common::Point3D* _internal_mutable_center();
  public:
  void unsafe_arena_set_allocated_center(
      ::gwm::common::Point3D* center);
  ::gwm::common::Point3D* unsafe_arena_release_center();

  // .gwm.common.Point3D size = 2;
  bool has_size() const;
  private:
  bool _internal_has_size() const;
  public:
  void clear_size();
  const ::gwm::common::Point3D& size() const;
  ::gwm::common::Point3D* release_size();
  ::gwm::common::Point3D* mutable_size();
  void set_allocated_size(::gwm::common::Point3D* size);
  private:
  const ::gwm::common::Point3D& _internal_size() const;
  ::gwm::common::Point3D* _internal_mutable_size();
  public:
  void unsafe_arena_set_allocated_size(
      ::gwm::common::Point3D* size);
  ::gwm::common::Point3D* unsafe_arena_release_size();

  // float angle = 3;
  void clear_angle();
  float angle() const;
  void set_angle(float value);
  private:
  float _internal_angle() const;
  void _internal_set_angle(float value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.RotatedRect3D)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::gwm::common::Point3D* center_;
  ::gwm::common::Point3D* size_;
  float angle_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class PlaneCoeffs PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.common.PlaneCoeffs) */ {
 public:
  inline PlaneCoeffs() : PlaneCoeffs(nullptr) {}
  virtual ~PlaneCoeffs();

  PlaneCoeffs(const PlaneCoeffs& from);
  PlaneCoeffs(PlaneCoeffs&& from) noexcept
    : PlaneCoeffs() {
    *this = ::std::move(from);
  }

  inline PlaneCoeffs& operator=(const PlaneCoeffs& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlaneCoeffs& operator=(PlaneCoeffs&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const PlaneCoeffs& default_instance();

  static inline const PlaneCoeffs* internal_default_instance() {
    return reinterpret_cast<const PlaneCoeffs*>(
               &_PlaneCoeffs_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(PlaneCoeffs& a, PlaneCoeffs& b) {
    a.Swap(&b);
  }
  inline void Swap(PlaneCoeffs* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlaneCoeffs* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline PlaneCoeffs* New() const final {
    return CreateMaybeMessage<PlaneCoeffs>(nullptr);
  }

  PlaneCoeffs* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<PlaneCoeffs>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const PlaneCoeffs& from);
  void MergeFrom(const PlaneCoeffs& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlaneCoeffs* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.common.PlaneCoeffs";
  }
  protected:
  explicit PlaneCoeffs(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_common_2fgeometry_2eproto);
    return ::descriptor_table_common_2fgeometry_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAFieldNumber = 1,
    kBFieldNumber = 2,
    kCFieldNumber = 3,
    kDFieldNumber = 4,
  };
  // double a = 1;
  void clear_a();
  double a() const;
  void set_a(double value);
  private:
  double _internal_a() const;
  void _internal_set_a(double value);
  public:

  // double b = 2;
  void clear_b();
  double b() const;
  void set_b(double value);
  private:
  double _internal_b() const;
  void _internal_set_b(double value);
  public:

  // double c = 3;
  void clear_c();
  double c() const;
  void set_c(double value);
  private:
  double _internal_c() const;
  void _internal_set_c(double value);
  public:

  // double d = 4;
  void clear_d();
  double d() const;
  void set_d(double value);
  private:
  double _internal_d() const;
  void _internal_set_d(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.common.PlaneCoeffs)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double a_;
  double b_;
  double c_;
  double d_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_common_2fgeometry_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PointENU

// double x = 1;
inline void PointENU::clear_x() {
  x_ = 0;
}
inline double PointENU::_internal_x() const {
  return x_;
}
inline double PointENU::x() const {
  // @@protoc_insertion_point(field_get:gwm.common.PointENU.x)
  return _internal_x();
}
inline void PointENU::_internal_set_x(double value) {
  
  x_ = value;
}
inline void PointENU::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:gwm.common.PointENU.x)
}

// double y = 2;
inline void PointENU::clear_y() {
  y_ = 0;
}
inline double PointENU::_internal_y() const {
  return y_;
}
inline double PointENU::y() const {
  // @@protoc_insertion_point(field_get:gwm.common.PointENU.y)
  return _internal_y();
}
inline void PointENU::_internal_set_y(double value) {
  
  y_ = value;
}
inline void PointENU::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:gwm.common.PointENU.y)
}

// double z = 3;
inline void PointENU::clear_z() {
  z_ = 0;
}
inline double PointENU::_internal_z() const {
  return z_;
}
inline double PointENU::z() const {
  // @@protoc_insertion_point(field_get:gwm.common.PointENU.z)
  return _internal_z();
}
inline void PointENU::_internal_set_z(double value) {
  
  z_ = value;
}
inline void PointENU::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:gwm.common.PointENU.z)
}

// -------------------------------------------------------------------

// PointLLH

// double lon = 1;
inline void PointLLH::clear_lon() {
  lon_ = 0;
}
inline double PointLLH::_internal_lon() const {
  return lon_;
}
inline double PointLLH::lon() const {
  // @@protoc_insertion_point(field_get:gwm.common.PointLLH.lon)
  return _internal_lon();
}
inline void PointLLH::_internal_set_lon(double value) {
  
  lon_ = value;
}
inline void PointLLH::set_lon(double value) {
  _internal_set_lon(value);
  // @@protoc_insertion_point(field_set:gwm.common.PointLLH.lon)
}

// double lat = 2;
inline void PointLLH::clear_lat() {
  lat_ = 0;
}
inline double PointLLH::_internal_lat() const {
  return lat_;
}
inline double PointLLH::lat() const {
  // @@protoc_insertion_point(field_get:gwm.common.PointLLH.lat)
  return _internal_lat();
}
inline void PointLLH::_internal_set_lat(double value) {
  
  lat_ = value;
}
inline void PointLLH::set_lat(double value) {
  _internal_set_lat(value);
  // @@protoc_insertion_point(field_set:gwm.common.PointLLH.lat)
}

// double height = 3;
inline void PointLLH::clear_height() {
  height_ = 0;
}
inline double PointLLH::_internal_height() const {
  return height_;
}
inline double PointLLH::height() const {
  // @@protoc_insertion_point(field_get:gwm.common.PointLLH.height)
  return _internal_height();
}
inline void PointLLH::_internal_set_height(double value) {
  
  height_ = value;
}
inline void PointLLH::set_height(double value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:gwm.common.PointLLH.height)
}

// -------------------------------------------------------------------

// Point2D

// double x = 1;
inline void Point2D::clear_x() {
  x_ = 0;
}
inline double Point2D::_internal_x() const {
  return x_;
}
inline double Point2D::x() const {
  // @@protoc_insertion_point(field_get:gwm.common.Point2D.x)
  return _internal_x();
}
inline void Point2D::_internal_set_x(double value) {
  
  x_ = value;
}
inline void Point2D::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:gwm.common.Point2D.x)
}

// double y = 2;
inline void Point2D::clear_y() {
  y_ = 0;
}
inline double Point2D::_internal_y() const {
  return y_;
}
inline double Point2D::y() const {
  // @@protoc_insertion_point(field_get:gwm.common.Point2D.y)
  return _internal_y();
}
inline void Point2D::_internal_set_y(double value) {
  
  y_ = value;
}
inline void Point2D::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:gwm.common.Point2D.y)
}

// -------------------------------------------------------------------

// Point3D

// double x = 1;
inline void Point3D::clear_x() {
  x_ = 0;
}
inline double Point3D::_internal_x() const {
  return x_;
}
inline double Point3D::x() const {
  // @@protoc_insertion_point(field_get:gwm.common.Point3D.x)
  return _internal_x();
}
inline void Point3D::_internal_set_x(double value) {
  
  x_ = value;
}
inline void Point3D::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:gwm.common.Point3D.x)
}

// double y = 2;
inline void Point3D::clear_y() {
  y_ = 0;
}
inline double Point3D::_internal_y() const {
  return y_;
}
inline double Point3D::y() const {
  // @@protoc_insertion_point(field_get:gwm.common.Point3D.y)
  return _internal_y();
}
inline void Point3D::_internal_set_y(double value) {
  
  y_ = value;
}
inline void Point3D::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:gwm.common.Point3D.y)
}

// double z = 3;
inline void Point3D::clear_z() {
  z_ = 0;
}
inline double Point3D::_internal_z() const {
  return z_;
}
inline double Point3D::z() const {
  // @@protoc_insertion_point(field_get:gwm.common.Point3D.z)
  return _internal_z();
}
inline void Point3D::_internal_set_z(double value) {
  
  z_ = value;
}
inline void Point3D::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:gwm.common.Point3D.z)
}

// -------------------------------------------------------------------

// Vector3

// float x = 1;
inline void Vector3::clear_x() {
  x_ = 0;
}
inline float Vector3::_internal_x() const {
  return x_;
}
inline float Vector3::x() const {
  // @@protoc_insertion_point(field_get:gwm.common.Vector3.x)
  return _internal_x();
}
inline void Vector3::_internal_set_x(float value) {
  
  x_ = value;
}
inline void Vector3::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:gwm.common.Vector3.x)
}

// float y = 2;
inline void Vector3::clear_y() {
  y_ = 0;
}
inline float Vector3::_internal_y() const {
  return y_;
}
inline float Vector3::y() const {
  // @@protoc_insertion_point(field_get:gwm.common.Vector3.y)
  return _internal_y();
}
inline void Vector3::_internal_set_y(float value) {
  
  y_ = value;
}
inline void Vector3::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:gwm.common.Vector3.y)
}

// float z = 3;
inline void Vector3::clear_z() {
  z_ = 0;
}
inline float Vector3::_internal_z() const {
  return z_;
}
inline float Vector3::z() const {
  // @@protoc_insertion_point(field_get:gwm.common.Vector3.z)
  return _internal_z();
}
inline void Vector3::_internal_set_z(float value) {
  
  z_ = value;
}
inline void Vector3::set_z(float value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:gwm.common.Vector3.z)
}

// -------------------------------------------------------------------

// Quaternion

// double qx = 1;
inline void Quaternion::clear_qx() {
  qx_ = 0;
}
inline double Quaternion::_internal_qx() const {
  return qx_;
}
inline double Quaternion::qx() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion.qx)
  return _internal_qx();
}
inline void Quaternion::_internal_set_qx(double value) {
  
  qx_ = value;
}
inline void Quaternion::set_qx(double value) {
  _internal_set_qx(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion.qx)
}

// double qy = 2;
inline void Quaternion::clear_qy() {
  qy_ = 0;
}
inline double Quaternion::_internal_qy() const {
  return qy_;
}
inline double Quaternion::qy() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion.qy)
  return _internal_qy();
}
inline void Quaternion::_internal_set_qy(double value) {
  
  qy_ = value;
}
inline void Quaternion::set_qy(double value) {
  _internal_set_qy(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion.qy)
}

// double qz = 3;
inline void Quaternion::clear_qz() {
  qz_ = 0;
}
inline double Quaternion::_internal_qz() const {
  return qz_;
}
inline double Quaternion::qz() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion.qz)
  return _internal_qz();
}
inline void Quaternion::_internal_set_qz(double value) {
  
  qz_ = value;
}
inline void Quaternion::set_qz(double value) {
  _internal_set_qz(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion.qz)
}

// double qw = 4;
inline void Quaternion::clear_qw() {
  qw_ = 0;
}
inline double Quaternion::_internal_qw() const {
  return qw_;
}
inline double Quaternion::qw() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion.qw)
  return _internal_qw();
}
inline void Quaternion::_internal_set_qw(double value) {
  
  qw_ = value;
}
inline void Quaternion::set_qw(double value) {
  _internal_set_qw(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion.qw)
}

// -------------------------------------------------------------------

// Quaternion_f

// float qx = 1;
inline void Quaternion_f::clear_qx() {
  qx_ = 0;
}
inline float Quaternion_f::_internal_qx() const {
  return qx_;
}
inline float Quaternion_f::qx() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion_f.qx)
  return _internal_qx();
}
inline void Quaternion_f::_internal_set_qx(float value) {
  
  qx_ = value;
}
inline void Quaternion_f::set_qx(float value) {
  _internal_set_qx(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion_f.qx)
}

// float qy = 2;
inline void Quaternion_f::clear_qy() {
  qy_ = 0;
}
inline float Quaternion_f::_internal_qy() const {
  return qy_;
}
inline float Quaternion_f::qy() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion_f.qy)
  return _internal_qy();
}
inline void Quaternion_f::_internal_set_qy(float value) {
  
  qy_ = value;
}
inline void Quaternion_f::set_qy(float value) {
  _internal_set_qy(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion_f.qy)
}

// float qz = 3;
inline void Quaternion_f::clear_qz() {
  qz_ = 0;
}
inline float Quaternion_f::_internal_qz() const {
  return qz_;
}
inline float Quaternion_f::qz() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion_f.qz)
  return _internal_qz();
}
inline void Quaternion_f::_internal_set_qz(float value) {
  
  qz_ = value;
}
inline void Quaternion_f::set_qz(float value) {
  _internal_set_qz(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion_f.qz)
}

// float qw = 4;
inline void Quaternion_f::clear_qw() {
  qw_ = 0;
}
inline float Quaternion_f::_internal_qw() const {
  return qw_;
}
inline float Quaternion_f::qw() const {
  // @@protoc_insertion_point(field_get:gwm.common.Quaternion_f.qw)
  return _internal_qw();
}
inline void Quaternion_f::_internal_set_qw(float value) {
  
  qw_ = value;
}
inline void Quaternion_f::set_qw(float value) {
  _internal_set_qw(value);
  // @@protoc_insertion_point(field_set:gwm.common.Quaternion_f.qw)
}

// -------------------------------------------------------------------

// Polygon

// repeated .gwm.common.Point3D point = 1;
inline int Polygon::_internal_point_size() const {
  return point_.size();
}
inline int Polygon::point_size() const {
  return _internal_point_size();
}
inline void Polygon::clear_point() {
  point_.Clear();
}
inline ::gwm::common::Point3D* Polygon::mutable_point(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.common.Polygon.point)
  return point_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >*
Polygon::mutable_point() {
  // @@protoc_insertion_point(field_mutable_list:gwm.common.Polygon.point)
  return &point_;
}
inline const ::gwm::common::Point3D& Polygon::_internal_point(int index) const {
  return point_.Get(index);
}
inline const ::gwm::common::Point3D& Polygon::point(int index) const {
  // @@protoc_insertion_point(field_get:gwm.common.Polygon.point)
  return _internal_point(index);
}
inline ::gwm::common::Point3D* Polygon::_internal_add_point() {
  return point_.Add();
}
inline ::gwm::common::Point3D* Polygon::add_point() {
  // @@protoc_insertion_point(field_add:gwm.common.Polygon.point)
  return _internal_add_point();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >&
Polygon::point() const {
  // @@protoc_insertion_point(field_list:gwm.common.Polygon.point)
  return point_;
}

// -------------------------------------------------------------------

// Polyline

// repeated .gwm.common.Point3D point = 1;
inline int Polyline::_internal_point_size() const {
  return point_.size();
}
inline int Polyline::point_size() const {
  return _internal_point_size();
}
inline void Polyline::clear_point() {
  point_.Clear();
}
inline ::gwm::common::Point3D* Polyline::mutable_point(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.common.Polyline.point)
  return point_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >*
Polyline::mutable_point() {
  // @@protoc_insertion_point(field_mutable_list:gwm.common.Polyline.point)
  return &point_;
}
inline const ::gwm::common::Point3D& Polyline::_internal_point(int index) const {
  return point_.Get(index);
}
inline const ::gwm::common::Point3D& Polyline::point(int index) const {
  // @@protoc_insertion_point(field_get:gwm.common.Polyline.point)
  return _internal_point(index);
}
inline ::gwm::common::Point3D* Polyline::_internal_add_point() {
  return point_.Add();
}
inline ::gwm::common::Point3D* Polyline::add_point() {
  // @@protoc_insertion_point(field_add:gwm.common.Polyline.point)
  return _internal_add_point();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point3D >&
Polyline::point() const {
  // @@protoc_insertion_point(field_list:gwm.common.Polyline.point)
  return point_;
}

// -------------------------------------------------------------------

// PolygonLLH

// repeated .gwm.common.PointLLH point = 1;
inline int PolygonLLH::_internal_point_size() const {
  return point_.size();
}
inline int PolygonLLH::point_size() const {
  return _internal_point_size();
}
inline void PolygonLLH::clear_point() {
  point_.Clear();
}
inline ::gwm::common::PointLLH* PolygonLLH::mutable_point(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.common.PolygonLLH.point)
  return point_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >*
PolygonLLH::mutable_point() {
  // @@protoc_insertion_point(field_mutable_list:gwm.common.PolygonLLH.point)
  return &point_;
}
inline const ::gwm::common::PointLLH& PolygonLLH::_internal_point(int index) const {
  return point_.Get(index);
}
inline const ::gwm::common::PointLLH& PolygonLLH::point(int index) const {
  // @@protoc_insertion_point(field_get:gwm.common.PolygonLLH.point)
  return _internal_point(index);
}
inline ::gwm::common::PointLLH* PolygonLLH::_internal_add_point() {
  return point_.Add();
}
inline ::gwm::common::PointLLH* PolygonLLH::add_point() {
  // @@protoc_insertion_point(field_add:gwm.common.PolygonLLH.point)
  return _internal_add_point();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >&
PolygonLLH::point() const {
  // @@protoc_insertion_point(field_list:gwm.common.PolygonLLH.point)
  return point_;
}

// -------------------------------------------------------------------

// PolylineLLH

// repeated .gwm.common.PointLLH point = 1;
inline int PolylineLLH::_internal_point_size() const {
  return point_.size();
}
inline int PolylineLLH::point_size() const {
  return _internal_point_size();
}
inline void PolylineLLH::clear_point() {
  point_.Clear();
}
inline ::gwm::common::PointLLH* PolylineLLH::mutable_point(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.common.PolylineLLH.point)
  return point_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >*
PolylineLLH::mutable_point() {
  // @@protoc_insertion_point(field_mutable_list:gwm.common.PolylineLLH.point)
  return &point_;
}
inline const ::gwm::common::PointLLH& PolylineLLH::_internal_point(int index) const {
  return point_.Get(index);
}
inline const ::gwm::common::PointLLH& PolylineLLH::point(int index) const {
  // @@protoc_insertion_point(field_get:gwm.common.PolylineLLH.point)
  return _internal_point(index);
}
inline ::gwm::common::PointLLH* PolylineLLH::_internal_add_point() {
  return point_.Add();
}
inline ::gwm::common::PointLLH* PolylineLLH::add_point() {
  // @@protoc_insertion_point(field_add:gwm.common.PolylineLLH.point)
  return _internal_add_point();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::PointLLH >&
PolylineLLH::point() const {
  // @@protoc_insertion_point(field_list:gwm.common.PolylineLLH.point)
  return point_;
}

// -------------------------------------------------------------------

// Transformation3

// .gwm.common.Vector3 position = 1;
inline bool Transformation3::_internal_has_position() const {
  return this != internal_default_instance() && position_ != nullptr;
}
inline bool Transformation3::has_position() const {
  return _internal_has_position();
}
inline void Transformation3::clear_position() {
  if (GetArena() == nullptr && position_ != nullptr) {
    delete position_;
  }
  position_ = nullptr;
}
inline const ::gwm::common::Vector3& Transformation3::_internal_position() const {
  const ::gwm::common::Vector3* p = position_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Vector3&>(
      ::gwm::common::_Vector3_default_instance_);
}
inline const ::gwm::common::Vector3& Transformation3::position() const {
  // @@protoc_insertion_point(field_get:gwm.common.Transformation3.position)
  return _internal_position();
}
inline void Transformation3::unsafe_arena_set_allocated_position(
    ::gwm::common::Vector3* position) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position_);
  }
  position_ = position;
  if (position) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.common.Transformation3.position)
}
inline ::gwm::common::Vector3* Transformation3::release_position() {
  
  ::gwm::common::Vector3* temp = position_;
  position_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Vector3* Transformation3::unsafe_arena_release_position() {
  // @@protoc_insertion_point(field_release:gwm.common.Transformation3.position)
  
  ::gwm::common::Vector3* temp = position_;
  position_ = nullptr;
  return temp;
}
inline ::gwm::common::Vector3* Transformation3::_internal_mutable_position() {
  
  if (position_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Vector3>(GetArena());
    position_ = p;
  }
  return position_;
}
inline ::gwm::common::Vector3* Transformation3::mutable_position() {
  // @@protoc_insertion_point(field_mutable:gwm.common.Transformation3.position)
  return _internal_mutable_position();
}
inline void Transformation3::set_allocated_position(::gwm::common::Vector3* position) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete position_;
  }
  if (position) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(position);
    if (message_arena != submessage_arena) {
      position = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position, submessage_arena);
    }
    
  } else {
    
  }
  position_ = position;
  // @@protoc_insertion_point(field_set_allocated:gwm.common.Transformation3.position)
}

// .gwm.common.Quaternion_f orientation = 2;
inline bool Transformation3::_internal_has_orientation() const {
  return this != internal_default_instance() && orientation_ != nullptr;
}
inline bool Transformation3::has_orientation() const {
  return _internal_has_orientation();
}
inline void Transformation3::clear_orientation() {
  if (GetArena() == nullptr && orientation_ != nullptr) {
    delete orientation_;
  }
  orientation_ = nullptr;
}
inline const ::gwm::common::Quaternion_f& Transformation3::_internal_orientation() const {
  const ::gwm::common::Quaternion_f* p = orientation_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Quaternion_f&>(
      ::gwm::common::_Quaternion_f_default_instance_);
}
inline const ::gwm::common::Quaternion_f& Transformation3::orientation() const {
  // @@protoc_insertion_point(field_get:gwm.common.Transformation3.orientation)
  return _internal_orientation();
}
inline void Transformation3::unsafe_arena_set_allocated_orientation(
    ::gwm::common::Quaternion_f* orientation) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(orientation_);
  }
  orientation_ = orientation;
  if (orientation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.common.Transformation3.orientation)
}
inline ::gwm::common::Quaternion_f* Transformation3::release_orientation() {
  
  ::gwm::common::Quaternion_f* temp = orientation_;
  orientation_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Quaternion_f* Transformation3::unsafe_arena_release_orientation() {
  // @@protoc_insertion_point(field_release:gwm.common.Transformation3.orientation)
  
  ::gwm::common::Quaternion_f* temp = orientation_;
  orientation_ = nullptr;
  return temp;
}
inline ::gwm::common::Quaternion_f* Transformation3::_internal_mutable_orientation() {
  
  if (orientation_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Quaternion_f>(GetArena());
    orientation_ = p;
  }
  return orientation_;
}
inline ::gwm::common::Quaternion_f* Transformation3::mutable_orientation() {
  // @@protoc_insertion_point(field_mutable:gwm.common.Transformation3.orientation)
  return _internal_mutable_orientation();
}
inline void Transformation3::set_allocated_orientation(::gwm::common::Quaternion_f* orientation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete orientation_;
  }
  if (orientation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(orientation);
    if (message_arena != submessage_arena) {
      orientation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, orientation, submessage_arena);
    }
    
  } else {
    
  }
  orientation_ = orientation;
  // @@protoc_insertion_point(field_set_allocated:gwm.common.Transformation3.orientation)
}

// -------------------------------------------------------------------

// Polygon2D

// repeated .gwm.common.Point2D points = 1;
inline int Polygon2D::_internal_points_size() const {
  return points_.size();
}
inline int Polygon2D::points_size() const {
  return _internal_points_size();
}
inline void Polygon2D::clear_points() {
  points_.Clear();
}
inline ::gwm::common::Point2D* Polygon2D::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.common.Polygon2D.points)
  return points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point2D >*
Polygon2D::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:gwm.common.Polygon2D.points)
  return &points_;
}
inline const ::gwm::common::Point2D& Polygon2D::_internal_points(int index) const {
  return points_.Get(index);
}
inline const ::gwm::common::Point2D& Polygon2D::points(int index) const {
  // @@protoc_insertion_point(field_get:gwm.common.Polygon2D.points)
  return _internal_points(index);
}
inline ::gwm::common::Point2D* Polygon2D::_internal_add_points() {
  return points_.Add();
}
inline ::gwm::common::Point2D* Polygon2D::add_points() {
  // @@protoc_insertion_point(field_add:gwm.common.Polygon2D.points)
  return _internal_add_points();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::common::Point2D >&
Polygon2D::points() const {
  // @@protoc_insertion_point(field_list:gwm.common.Polygon2D.points)
  return points_;
}

// -------------------------------------------------------------------

// RotatedRect

// .gwm.common.Point2D center = 1;
inline bool RotatedRect::_internal_has_center() const {
  return this != internal_default_instance() && center_ != nullptr;
}
inline bool RotatedRect::has_center() const {
  return _internal_has_center();
}
inline void RotatedRect::clear_center() {
  if (GetArena() == nullptr && center_ != nullptr) {
    delete center_;
  }
  center_ = nullptr;
}
inline const ::gwm::common::Point2D& RotatedRect::_internal_center() const {
  const ::gwm::common::Point2D* p = center_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Point2D&>(
      ::gwm::common::_Point2D_default_instance_);
}
inline const ::gwm::common::Point2D& RotatedRect::center() const {
  // @@protoc_insertion_point(field_get:gwm.common.RotatedRect.center)
  return _internal_center();
}
inline void RotatedRect::unsafe_arena_set_allocated_center(
    ::gwm::common::Point2D* center) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_);
  }
  center_ = center;
  if (center) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.common.RotatedRect.center)
}
inline ::gwm::common::Point2D* RotatedRect::release_center() {
  
  ::gwm::common::Point2D* temp = center_;
  center_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Point2D* RotatedRect::unsafe_arena_release_center() {
  // @@protoc_insertion_point(field_release:gwm.common.RotatedRect.center)
  
  ::gwm::common::Point2D* temp = center_;
  center_ = nullptr;
  return temp;
}
inline ::gwm::common::Point2D* RotatedRect::_internal_mutable_center() {
  
  if (center_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Point2D>(GetArena());
    center_ = p;
  }
  return center_;
}
inline ::gwm::common::Point2D* RotatedRect::mutable_center() {
  // @@protoc_insertion_point(field_mutable:gwm.common.RotatedRect.center)
  return _internal_mutable_center();
}
inline void RotatedRect::set_allocated_center(::gwm::common::Point2D* center) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete center_;
  }
  if (center) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(center);
    if (message_arena != submessage_arena) {
      center = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, center, submessage_arena);
    }
    
  } else {
    
  }
  center_ = center;
  // @@protoc_insertion_point(field_set_allocated:gwm.common.RotatedRect.center)
}

// .gwm.common.Point2D size = 2;
inline bool RotatedRect::_internal_has_size() const {
  return this != internal_default_instance() && size_ != nullptr;
}
inline bool RotatedRect::has_size() const {
  return _internal_has_size();
}
inline void RotatedRect::clear_size() {
  if (GetArena() == nullptr && size_ != nullptr) {
    delete size_;
  }
  size_ = nullptr;
}
inline const ::gwm::common::Point2D& RotatedRect::_internal_size() const {
  const ::gwm::common::Point2D* p = size_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Point2D&>(
      ::gwm::common::_Point2D_default_instance_);
}
inline const ::gwm::common::Point2D& RotatedRect::size() const {
  // @@protoc_insertion_point(field_get:gwm.common.RotatedRect.size)
  return _internal_size();
}
inline void RotatedRect::unsafe_arena_set_allocated_size(
    ::gwm::common::Point2D* size) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(size_);
  }
  size_ = size;
  if (size) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.common.RotatedRect.size)
}
inline ::gwm::common::Point2D* RotatedRect::release_size() {
  
  ::gwm::common::Point2D* temp = size_;
  size_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Point2D* RotatedRect::unsafe_arena_release_size() {
  // @@protoc_insertion_point(field_release:gwm.common.RotatedRect.size)
  
  ::gwm::common::Point2D* temp = size_;
  size_ = nullptr;
  return temp;
}
inline ::gwm::common::Point2D* RotatedRect::_internal_mutable_size() {
  
  if (size_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Point2D>(GetArena());
    size_ = p;
  }
  return size_;
}
inline ::gwm::common::Point2D* RotatedRect::mutable_size() {
  // @@protoc_insertion_point(field_mutable:gwm.common.RotatedRect.size)
  return _internal_mutable_size();
}
inline void RotatedRect::set_allocated_size(::gwm::common::Point2D* size) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete size_;
  }
  if (size) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(size);
    if (message_arena != submessage_arena) {
      size = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, size, submessage_arena);
    }
    
  } else {
    
  }
  size_ = size;
  // @@protoc_insertion_point(field_set_allocated:gwm.common.RotatedRect.size)
}

// float angle = 3;
inline void RotatedRect::clear_angle() {
  angle_ = 0;
}
inline float RotatedRect::_internal_angle() const {
  return angle_;
}
inline float RotatedRect::angle() const {
  // @@protoc_insertion_point(field_get:gwm.common.RotatedRect.angle)
  return _internal_angle();
}
inline void RotatedRect::_internal_set_angle(float value) {
  
  angle_ = value;
}
inline void RotatedRect::set_angle(float value) {
  _internal_set_angle(value);
  // @@protoc_insertion_point(field_set:gwm.common.RotatedRect.angle)
}

// -------------------------------------------------------------------

// RotatedRect3D

// .gwm.common.Point3D center = 1;
inline bool RotatedRect3D::_internal_has_center() const {
  return this != internal_default_instance() && center_ != nullptr;
}
inline bool RotatedRect3D::has_center() const {
  return _internal_has_center();
}
inline void RotatedRect3D::clear_center() {
  if (GetArena() == nullptr && center_ != nullptr) {
    delete center_;
  }
  center_ = nullptr;
}
inline const ::gwm::common::Point3D& RotatedRect3D::_internal_center() const {
  const ::gwm::common::Point3D* p = center_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Point3D&>(
      ::gwm::common::_Point3D_default_instance_);
}
inline const ::gwm::common::Point3D& RotatedRect3D::center() const {
  // @@protoc_insertion_point(field_get:gwm.common.RotatedRect3D.center)
  return _internal_center();
}
inline void RotatedRect3D::unsafe_arena_set_allocated_center(
    ::gwm::common::Point3D* center) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(center_);
  }
  center_ = center;
  if (center) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.common.RotatedRect3D.center)
}
inline ::gwm::common::Point3D* RotatedRect3D::release_center() {
  
  ::gwm::common::Point3D* temp = center_;
  center_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Point3D* RotatedRect3D::unsafe_arena_release_center() {
  // @@protoc_insertion_point(field_release:gwm.common.RotatedRect3D.center)
  
  ::gwm::common::Point3D* temp = center_;
  center_ = nullptr;
  return temp;
}
inline ::gwm::common::Point3D* RotatedRect3D::_internal_mutable_center() {
  
  if (center_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Point3D>(GetArena());
    center_ = p;
  }
  return center_;
}
inline ::gwm::common::Point3D* RotatedRect3D::mutable_center() {
  // @@protoc_insertion_point(field_mutable:gwm.common.RotatedRect3D.center)
  return _internal_mutable_center();
}
inline void RotatedRect3D::set_allocated_center(::gwm::common::Point3D* center) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete center_;
  }
  if (center) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(center);
    if (message_arena != submessage_arena) {
      center = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, center, submessage_arena);
    }
    
  } else {
    
  }
  center_ = center;
  // @@protoc_insertion_point(field_set_allocated:gwm.common.RotatedRect3D.center)
}

// .gwm.common.Point3D size = 2;
inline bool RotatedRect3D::_internal_has_size() const {
  return this != internal_default_instance() && size_ != nullptr;
}
inline bool RotatedRect3D::has_size() const {
  return _internal_has_size();
}
inline void RotatedRect3D::clear_size() {
  if (GetArena() == nullptr && size_ != nullptr) {
    delete size_;
  }
  size_ = nullptr;
}
inline const ::gwm::common::Point3D& RotatedRect3D::_internal_size() const {
  const ::gwm::common::Point3D* p = size_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Point3D&>(
      ::gwm::common::_Point3D_default_instance_);
}
inline const ::gwm::common::Point3D& RotatedRect3D::size() const {
  // @@protoc_insertion_point(field_get:gwm.common.RotatedRect3D.size)
  return _internal_size();
}
inline void RotatedRect3D::unsafe_arena_set_allocated_size(
    ::gwm::common::Point3D* size) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(size_);
  }
  size_ = size;
  if (size) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.common.RotatedRect3D.size)
}
inline ::gwm::common::Point3D* RotatedRect3D::release_size() {
  
  ::gwm::common::Point3D* temp = size_;
  size_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Point3D* RotatedRect3D::unsafe_arena_release_size() {
  // @@protoc_insertion_point(field_release:gwm.common.RotatedRect3D.size)
  
  ::gwm::common::Point3D* temp = size_;
  size_ = nullptr;
  return temp;
}
inline ::gwm::common::Point3D* RotatedRect3D::_internal_mutable_size() {
  
  if (size_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Point3D>(GetArena());
    size_ = p;
  }
  return size_;
}
inline ::gwm::common::Point3D* RotatedRect3D::mutable_size() {
  // @@protoc_insertion_point(field_mutable:gwm.common.RotatedRect3D.size)
  return _internal_mutable_size();
}
inline void RotatedRect3D::set_allocated_size(::gwm::common::Point3D* size) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete size_;
  }
  if (size) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(size);
    if (message_arena != submessage_arena) {
      size = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, size, submessage_arena);
    }
    
  } else {
    
  }
  size_ = size;
  // @@protoc_insertion_point(field_set_allocated:gwm.common.RotatedRect3D.size)
}

// float angle = 3;
inline void RotatedRect3D::clear_angle() {
  angle_ = 0;
}
inline float RotatedRect3D::_internal_angle() const {
  return angle_;
}
inline float RotatedRect3D::angle() const {
  // @@protoc_insertion_point(field_get:gwm.common.RotatedRect3D.angle)
  return _internal_angle();
}
inline void RotatedRect3D::_internal_set_angle(float value) {
  
  angle_ = value;
}
inline void RotatedRect3D::set_angle(float value) {
  _internal_set_angle(value);
  // @@protoc_insertion_point(field_set:gwm.common.RotatedRect3D.angle)
}

// -------------------------------------------------------------------

// PlaneCoeffs

// double a = 1;
inline void PlaneCoeffs::clear_a() {
  a_ = 0;
}
inline double PlaneCoeffs::_internal_a() const {
  return a_;
}
inline double PlaneCoeffs::a() const {
  // @@protoc_insertion_point(field_get:gwm.common.PlaneCoeffs.a)
  return _internal_a();
}
inline void PlaneCoeffs::_internal_set_a(double value) {
  
  a_ = value;
}
inline void PlaneCoeffs::set_a(double value) {
  _internal_set_a(value);
  // @@protoc_insertion_point(field_set:gwm.common.PlaneCoeffs.a)
}

// double b = 2;
inline void PlaneCoeffs::clear_b() {
  b_ = 0;
}
inline double PlaneCoeffs::_internal_b() const {
  return b_;
}
inline double PlaneCoeffs::b() const {
  // @@protoc_insertion_point(field_get:gwm.common.PlaneCoeffs.b)
  return _internal_b();
}
inline void PlaneCoeffs::_internal_set_b(double value) {
  
  b_ = value;
}
inline void PlaneCoeffs::set_b(double value) {
  _internal_set_b(value);
  // @@protoc_insertion_point(field_set:gwm.common.PlaneCoeffs.b)
}

// double c = 3;
inline void PlaneCoeffs::clear_c() {
  c_ = 0;
}
inline double PlaneCoeffs::_internal_c() const {
  return c_;
}
inline double PlaneCoeffs::c() const {
  // @@protoc_insertion_point(field_get:gwm.common.PlaneCoeffs.c)
  return _internal_c();
}
inline void PlaneCoeffs::_internal_set_c(double value) {
  
  c_ = value;
}
inline void PlaneCoeffs::set_c(double value) {
  _internal_set_c(value);
  // @@protoc_insertion_point(field_set:gwm.common.PlaneCoeffs.c)
}

// double d = 4;
inline void PlaneCoeffs::clear_d() {
  d_ = 0;
}
inline double PlaneCoeffs::_internal_d() const {
  return d_;
}
inline double PlaneCoeffs::d() const {
  // @@protoc_insertion_point(field_get:gwm.common.PlaneCoeffs.d)
  return _internal_d();
}
inline void PlaneCoeffs::_internal_set_d(double value) {
  
  d_ = value;
}
inline void PlaneCoeffs::set_d(double value) {
  _internal_set_d(value);
  // @@protoc_insertion_point(field_set:gwm.common.PlaneCoeffs.d)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace common
}  // namespace gwm

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_common_2fgeometry_2eproto
