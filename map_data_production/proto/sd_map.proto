syntax = "proto3";

package gwm.sdmap;

message SensorStamp {
  double lidar_stamp = 1;
  double radar_stamp = 2;
  double uss_stamp = 3;
  double chassis_stamp = 4;
  double camera_stamp = 5;
  double imuins_stamp = 6;
  double gnss_stamp = 7;
}

message Header {
  int32 seq = 1;
  string frame_id = 2;
  double publish_stamp = 3;  // topic数据发布时刻的系统时间，主要用于分析传输时延和处理时延
  double gnss_stamp = 4;  // 管理面时间戳，基本可忽略
  SensorStamp sensor_stamp = 5;  // 原始传感器的数据面时间戳，异源数据的时间戳
  double data_stamp = 6;  // 算法同步、运算后时间戳(数据产生时刻的时间)，主要用于算法对异源数据做时间对齐和补偿
}

message Point3D {
  double x = 1;
  double y = 2;
  double z = 3;
}

message ConnectRoads{
  repeated uint64 fromRoadIds = 1;  ///< 进入道路id
  repeated uint64 toRoadIds = 2;  ///< 退出道路id
}
message Road {
  uint64 roadId = 1;
  repeated Point3D points = 2;
  uint32 lane_count = 3;///< 道路的正向车道数
  repeated uint32 arrowMarkings = 4; ///道路箭头信息
  uint32 angleF = 5;  ///< 道路起始角度
  uint32 angleT = 6;  ///< 道路结束角度

  enum RoadType {
    ROADCLASS_FREEWAY = 0;  ///< 高速公路
    ROADCLASS_NATIONAL_ROAD = 1;  ///< 国道
    ROADCLASS_PROVINCE_ROAD = 2;  ///< 省道
    ROADCLASS_COUNTY_ROAD = 3;  ///< 县道
    ROADCLASS_RURAL_ROAD = 4;  ///< 乡公路
    ROADCLASS_IN_COUNTY_ROAD = 5;  ///< 县乡村内部道路
    ROADCLASS_CITY_SPEED_WAY = 6;  ///< 主要大街\城市快速道
    ROADCLASS_MAIN_ROAD = 7;  ///< 主要道路
    ROADCLASS_SECONDARY_ROAD = 8;  ///< 次要道路
    ROADCLASS_COMMON_ROAD = 9; ///< 普通道路
    ROADCLASS_NON_NAVI_ROAD = 10;  ///< 非导航道路
    ROADCLASS_INVALID = 0xFF;  ///< 道路等级无效值
  };
  /**
 * 道路构成
 */
  enum FormOfWay
  {
    FORMWAY_UNKNOWN = 0;
    FORMWAY_DIVISED_LINK = 1;  ///< 上下线分离 主路
    FORMWAY_CROSS_LINK = 2;  ///< 交叉点内 复杂节点内部道路
    FORMWAY_JCT = 3; ///< JCT
    FORMWAY_ROUND_CIRCLE = 4;  ///< 环岛
    FORMWAY_SERVICE_ROAD = 5;  ///< 服务区 辅助道路
    FORMWAY_SLIP_ROAD = 6;  ///< 引路 匝道
    FORMWAY_SIDE_ROAD = 7;  ///< 辅路
    FORMWAY_SLIP_JCT = 8;  ///< 引路 JCT
    FORMWAY_EXIT_LINK = 9;  ///< 出口
    FORMWAY_ENTRANCE_LINK = 10;  ///< 入口
    FORMWAY_TURN_RIGHT_LINEA = 11;  ///< 右转专用道
    FORMWAY_TURN_RIGHT_LINEB = 12;  ///< 右转专用道
    FORMWAY_TURN_LEFT_LINEA = 13;  ///< 左转专用道
    FORMWAY_TURN_LEFT_LINEB = 14;  ///< 左转专用道
    FORMWAY_COMMON_LINK = 15;  ///< 普通道路
    FORMWAY_TURN_LEFTRIGHT_LINE = 16;  ///< 左右转专用道
    FORMWAY_NONMOTORIZED_DRIVEWAY = 17;  ///< ADF+ 专用
    FORMWAY_FRONTDOOR_ROAD = 18;  ///< 门前道路
    FORMWAY_SERVICE_SLIP_ROAD = 19;  ///< 服务区 + 引路
    FORMWAY_SERVICE_JCT = 20;  ///< 服务区 + JCT
    FORMWAY_SERVICE_JCT_SLIP_ROAD = 21;  ///< 服务区 + 引路 + JCT
    FORMWAY_NON_VEHICLE = 22;  ///< 非机动车道路
    FORMWAY_INVALID = 0xFF;  ///< 道路构成无效值
  };
  RoadType road_type = 7;
  FormOfWay road_form = 8;
  ConnectRoads connect_road = 9;
  uint64 length = 10 ; //道路长度
}
message SDMapLocation{
  uint64 roadId = 1;
  uint32 offset = 2;
  Point3D matchPoint = 3;
}

message SDMap {
  Header header = 1;
  repeated Road road = 2;
  SDMapLocation sd_Location = 3;
}