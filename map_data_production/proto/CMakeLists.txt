cmake_minimum_required(VERSION 3.14)
project(proto_project)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

include(${PROJECT_ROOT_DIR}/cmake/FindProtobuf.cmake)
set(CMAKE_PREFIX_PATH "/usr/local/protobuf")
find_package(Protobuf REQUIRED)
include_directories(${Protobuf_INCLUDE_DIRS})
file(GLOB PROTO_FILES ${PROJECT_ROOT_DIR}/proto/common/*.proto  ${PROJECT_ROOT_DIR}/proto/*.proto)
message(STATUS ${PROTO_FILES})

LOCAL_PROTOBUF_GENERATE_CPP(PROTO_SRCS PROTO_HDRS ${PROTO_FILES})
message(STATUS ${PROTO_SRCS} ${PROTO_HDR})


#add_library(${proto_project} SHARED ${PROTO_SRCS} ${PROTO_HDR})

#include_directories(proto)
