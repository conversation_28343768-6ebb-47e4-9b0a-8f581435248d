// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rtk_gps.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_rtk_5fgps_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_rtk_5fgps_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_rtk_5fgps_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_rtk_5fgps_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_rtk_5fgps_2eproto;
namespace gwm {
namespace hdmap {
class GNSSCollection;
class GNSSCollectionDefaultTypeInternal;
extern GNSSCollectionDefaultTypeInternal _GNSSCollection_default_instance_;
class GNSSInfo;
class GNSSInfoDefaultTypeInternal;
extern GNSSInfoDefaultTypeInternal _GNSSInfo_default_instance_;
}  // namespace hdmap
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> ::gwm::hdmap::GNSSCollection* Arena::CreateMaybeMessage<::gwm::hdmap::GNSSCollection>(Arena*);
template<> ::gwm::hdmap::GNSSInfo* Arena::CreateMaybeMessage<::gwm::hdmap::GNSSInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace gwm {
namespace hdmap {

// ===================================================================

class GNSSInfo PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.GNSSInfo) */ {
 public:
  inline GNSSInfo() : GNSSInfo(nullptr) {}
  virtual ~GNSSInfo();

  GNSSInfo(const GNSSInfo& from);
  GNSSInfo(GNSSInfo&& from) noexcept
    : GNSSInfo() {
    *this = ::std::move(from);
  }

  inline GNSSInfo& operator=(const GNSSInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GNSSInfo& operator=(GNSSInfo&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GNSSInfo& default_instance();

  static inline const GNSSInfo* internal_default_instance() {
    return reinterpret_cast<const GNSSInfo*>(
               &_GNSSInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GNSSInfo& a, GNSSInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GNSSInfo* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GNSSInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GNSSInfo* New() const final {
    return CreateMaybeMessage<GNSSInfo>(nullptr);
  }

  GNSSInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GNSSInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GNSSInfo& from);
  void MergeFrom(const GNSSInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GNSSInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.GNSSInfo";
  }
  protected:
  explicit GNSSInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_rtk_5fgps_2eproto);
    return ::descriptor_table_rtk_5fgps_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimeStampFieldNumber = 1,
    kEastingFieldNumber = 2,
    kNorthingFieldNumber = 3,
    kUpFieldNumber = 4,
    kStatusFieldNumber = 5,
  };
  // int64 time_stamp = 1;
  void clear_time_stamp();
  ::PROTOBUF_NAMESPACE_ID::int64 time_stamp() const;
  void set_time_stamp(::PROTOBUF_NAMESPACE_ID::int64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int64 _internal_time_stamp() const;
  void _internal_set_time_stamp(::PROTOBUF_NAMESPACE_ID::int64 value);
  public:

  // double easting = 2;
  void clear_easting();
  double easting() const;
  void set_easting(double value);
  private:
  double _internal_easting() const;
  void _internal_set_easting(double value);
  public:

  // double northing = 3;
  void clear_northing();
  double northing() const;
  void set_northing(double value);
  private:
  double _internal_northing() const;
  void _internal_set_northing(double value);
  public:

  // double up = 4;
  void clear_up();
  double up() const;
  void set_up(double value);
  private:
  double _internal_up() const;
  void _internal_set_up(double value);
  public:

  // int32 status = 5;
  void clear_status();
  ::PROTOBUF_NAMESPACE_ID::int32 status() const;
  void set_status(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_status() const;
  void _internal_set_status(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.GNSSInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int64 time_stamp_;
  double easting_;
  double northing_;
  double up_;
  ::PROTOBUF_NAMESPACE_ID::int32 status_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_rtk_5fgps_2eproto;
};
// -------------------------------------------------------------------

class GNSSCollection PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.GNSSCollection) */ {
 public:
  inline GNSSCollection() : GNSSCollection(nullptr) {}
  virtual ~GNSSCollection();

  GNSSCollection(const GNSSCollection& from);
  GNSSCollection(GNSSCollection&& from) noexcept
    : GNSSCollection() {
    *this = ::std::move(from);
  }

  inline GNSSCollection& operator=(const GNSSCollection& from) {
    CopyFrom(from);
    return *this;
  }
  inline GNSSCollection& operator=(GNSSCollection&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const GNSSCollection& default_instance();

  static inline const GNSSCollection* internal_default_instance() {
    return reinterpret_cast<const GNSSCollection*>(
               &_GNSSCollection_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GNSSCollection& a, GNSSCollection& b) {
    a.Swap(&b);
  }
  inline void Swap(GNSSCollection* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GNSSCollection* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline GNSSCollection* New() const final {
    return CreateMaybeMessage<GNSSCollection>(nullptr);
  }

  GNSSCollection* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<GNSSCollection>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const GNSSCollection& from);
  void MergeFrom(const GNSSCollection& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GNSSCollection* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.GNSSCollection";
  }
  protected:
  explicit GNSSCollection(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_rtk_5fgps_2eproto);
    return ::descriptor_table_rtk_5fgps_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGnssFieldNumber = 1,
  };
  // repeated .gwm.hdmap.GNSSInfo gnss = 1;
  int gnss_size() const;
  private:
  int _internal_gnss_size() const;
  public:
  void clear_gnss();
  ::gwm::hdmap::GNSSInfo* mutable_gnss(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::GNSSInfo >*
      mutable_gnss();
  private:
  const ::gwm::hdmap::GNSSInfo& _internal_gnss(int index) const;
  ::gwm::hdmap::GNSSInfo* _internal_add_gnss();
  public:
  const ::gwm::hdmap::GNSSInfo& gnss(int index) const;
  ::gwm::hdmap::GNSSInfo* add_gnss();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::GNSSInfo >&
      gnss() const;

  // @@protoc_insertion_point(class_scope:gwm.hdmap.GNSSCollection)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::GNSSInfo > gnss_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_rtk_5fgps_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GNSSInfo

// int64 time_stamp = 1;
inline void GNSSInfo::clear_time_stamp() {
  time_stamp_ = PROTOBUF_LONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GNSSInfo::_internal_time_stamp() const {
  return time_stamp_;
}
inline ::PROTOBUF_NAMESPACE_ID::int64 GNSSInfo::time_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.GNSSInfo.time_stamp)
  return _internal_time_stamp();
}
inline void GNSSInfo::_internal_set_time_stamp(::PROTOBUF_NAMESPACE_ID::int64 value) {
  
  time_stamp_ = value;
}
inline void GNSSInfo::set_time_stamp(::PROTOBUF_NAMESPACE_ID::int64 value) {
  _internal_set_time_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.GNSSInfo.time_stamp)
}

// double easting = 2;
inline void GNSSInfo::clear_easting() {
  easting_ = 0;
}
inline double GNSSInfo::_internal_easting() const {
  return easting_;
}
inline double GNSSInfo::easting() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.GNSSInfo.easting)
  return _internal_easting();
}
inline void GNSSInfo::_internal_set_easting(double value) {
  
  easting_ = value;
}
inline void GNSSInfo::set_easting(double value) {
  _internal_set_easting(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.GNSSInfo.easting)
}

// double northing = 3;
inline void GNSSInfo::clear_northing() {
  northing_ = 0;
}
inline double GNSSInfo::_internal_northing() const {
  return northing_;
}
inline double GNSSInfo::northing() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.GNSSInfo.northing)
  return _internal_northing();
}
inline void GNSSInfo::_internal_set_northing(double value) {
  
  northing_ = value;
}
inline void GNSSInfo::set_northing(double value) {
  _internal_set_northing(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.GNSSInfo.northing)
}

// double up = 4;
inline void GNSSInfo::clear_up() {
  up_ = 0;
}
inline double GNSSInfo::_internal_up() const {
  return up_;
}
inline double GNSSInfo::up() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.GNSSInfo.up)
  return _internal_up();
}
inline void GNSSInfo::_internal_set_up(double value) {
  
  up_ = value;
}
inline void GNSSInfo::set_up(double value) {
  _internal_set_up(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.GNSSInfo.up)
}

// int32 status = 5;
inline void GNSSInfo::clear_status() {
  status_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GNSSInfo::_internal_status() const {
  return status_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 GNSSInfo::status() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.GNSSInfo.status)
  return _internal_status();
}
inline void GNSSInfo::_internal_set_status(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  status_ = value;
}
inline void GNSSInfo::set_status(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.GNSSInfo.status)
}

// -------------------------------------------------------------------

// GNSSCollection

// repeated .gwm.hdmap.GNSSInfo gnss = 1;
inline int GNSSCollection::_internal_gnss_size() const {
  return gnss_.size();
}
inline int GNSSCollection::gnss_size() const {
  return _internal_gnss_size();
}
inline void GNSSCollection::clear_gnss() {
  gnss_.Clear();
}
inline ::gwm::hdmap::GNSSInfo* GNSSCollection::mutable_gnss(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.GNSSCollection.gnss)
  return gnss_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::GNSSInfo >*
GNSSCollection::mutable_gnss() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.GNSSCollection.gnss)
  return &gnss_;
}
inline const ::gwm::hdmap::GNSSInfo& GNSSCollection::_internal_gnss(int index) const {
  return gnss_.Get(index);
}
inline const ::gwm::hdmap::GNSSInfo& GNSSCollection::gnss(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.GNSSCollection.gnss)
  return _internal_gnss(index);
}
inline ::gwm::hdmap::GNSSInfo* GNSSCollection::_internal_add_gnss() {
  return gnss_.Add();
}
inline ::gwm::hdmap::GNSSInfo* GNSSCollection::add_gnss() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.GNSSCollection.gnss)
  return _internal_add_gnss();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::GNSSInfo >&
GNSSCollection::gnss() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.GNSSCollection.gnss)
  return gnss_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdmap
}  // namespace gwm

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_rtk_5fgps_2eproto
