// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sd_map.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_sd_5fmap_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_sd_5fmap_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_sd_5fmap_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_sd_5fmap_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_sd_5fmap_2eproto;
namespace gwm {
namespace sdmap {
class ConnectRoads;
class ConnectRoadsDefaultTypeInternal;
extern ConnectRoadsDefaultTypeInternal _ConnectRoads_default_instance_;
class Header;
class HeaderDefaultTypeInternal;
extern HeaderDefaultTypeInternal _Header_default_instance_;
class Point3D;
class Point3DDefaultTypeInternal;
extern Point3DDefaultTypeInternal _Point3D_default_instance_;
class Road;
class RoadDefaultTypeInternal;
extern RoadDefaultTypeInternal _Road_default_instance_;
class SDMap;
class SDMapDefaultTypeInternal;
extern SDMapDefaultTypeInternal _SDMap_default_instance_;
class SDMapLocation;
class SDMapLocationDefaultTypeInternal;
extern SDMapLocationDefaultTypeInternal _SDMapLocation_default_instance_;
class SensorStamp;
class SensorStampDefaultTypeInternal;
extern SensorStampDefaultTypeInternal _SensorStamp_default_instance_;
}  // namespace sdmap
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> ::gwm::sdmap::ConnectRoads* Arena::CreateMaybeMessage<::gwm::sdmap::ConnectRoads>(Arena*);
template<> ::gwm::sdmap::Header* Arena::CreateMaybeMessage<::gwm::sdmap::Header>(Arena*);
template<> ::gwm::sdmap::Point3D* Arena::CreateMaybeMessage<::gwm::sdmap::Point3D>(Arena*);
template<> ::gwm::sdmap::Road* Arena::CreateMaybeMessage<::gwm::sdmap::Road>(Arena*);
template<> ::gwm::sdmap::SDMap* Arena::CreateMaybeMessage<::gwm::sdmap::SDMap>(Arena*);
template<> ::gwm::sdmap::SDMapLocation* Arena::CreateMaybeMessage<::gwm::sdmap::SDMapLocation>(Arena*);
template<> ::gwm::sdmap::SensorStamp* Arena::CreateMaybeMessage<::gwm::sdmap::SensorStamp>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace gwm {
namespace sdmap {

enum Road_RoadType : int {
  Road_RoadType_ROADCLASS_FREEWAY = 0,
  Road_RoadType_ROADCLASS_NATIONAL_ROAD = 1,
  Road_RoadType_ROADCLASS_PROVINCE_ROAD = 2,
  Road_RoadType_ROADCLASS_COUNTY_ROAD = 3,
  Road_RoadType_ROADCLASS_RURAL_ROAD = 4,
  Road_RoadType_ROADCLASS_IN_COUNTY_ROAD = 5,
  Road_RoadType_ROADCLASS_CITY_SPEED_WAY = 6,
  Road_RoadType_ROADCLASS_MAIN_ROAD = 7,
  Road_RoadType_ROADCLASS_SECONDARY_ROAD = 8,
  Road_RoadType_ROADCLASS_COMMON_ROAD = 9,
  Road_RoadType_ROADCLASS_NON_NAVI_ROAD = 10,
  Road_RoadType_ROADCLASS_INVALID = 255,
  Road_RoadType_Road_RoadType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Road_RoadType_Road_RoadType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Road_RoadType_IsValid(int value);
constexpr Road_RoadType Road_RoadType_RoadType_MIN = Road_RoadType_ROADCLASS_FREEWAY;
constexpr Road_RoadType Road_RoadType_RoadType_MAX = Road_RoadType_ROADCLASS_INVALID;
constexpr int Road_RoadType_RoadType_ARRAYSIZE = Road_RoadType_RoadType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Road_RoadType_descriptor();
template<typename T>
inline const std::string& Road_RoadType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Road_RoadType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Road_RoadType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Road_RoadType_descriptor(), enum_t_value);
}
inline bool Road_RoadType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Road_RoadType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Road_RoadType>(
    Road_RoadType_descriptor(), name, value);
}
enum Road_FormOfWay : int {
  Road_FormOfWay_FORMWAY_UNKNOWN = 0,
  Road_FormOfWay_FORMWAY_DIVISED_LINK = 1,
  Road_FormOfWay_FORMWAY_CROSS_LINK = 2,
  Road_FormOfWay_FORMWAY_JCT = 3,
  Road_FormOfWay_FORMWAY_ROUND_CIRCLE = 4,
  Road_FormOfWay_FORMWAY_SERVICE_ROAD = 5,
  Road_FormOfWay_FORMWAY_SLIP_ROAD = 6,
  Road_FormOfWay_FORMWAY_SIDE_ROAD = 7,
  Road_FormOfWay_FORMWAY_SLIP_JCT = 8,
  Road_FormOfWay_FORMWAY_EXIT_LINK = 9,
  Road_FormOfWay_FORMWAY_ENTRANCE_LINK = 10,
  Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEA = 11,
  Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEB = 12,
  Road_FormOfWay_FORMWAY_TURN_LEFT_LINEA = 13,
  Road_FormOfWay_FORMWAY_TURN_LEFT_LINEB = 14,
  Road_FormOfWay_FORMWAY_COMMON_LINK = 15,
  Road_FormOfWay_FORMWAY_TURN_LEFTRIGHT_LINE = 16,
  Road_FormOfWay_FORMWAY_NONMOTORIZED_DRIVEWAY = 17,
  Road_FormOfWay_FORMWAY_FRONTDOOR_ROAD = 18,
  Road_FormOfWay_FORMWAY_SERVICE_SLIP_ROAD = 19,
  Road_FormOfWay_FORMWAY_SERVICE_JCT = 20,
  Road_FormOfWay_FORMWAY_SERVICE_JCT_SLIP_ROAD = 21,
  Road_FormOfWay_FORMWAY_NON_VEHICLE = 22,
  Road_FormOfWay_FORMWAY_INVALID = 255,
  Road_FormOfWay_Road_FormOfWay_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Road_FormOfWay_Road_FormOfWay_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Road_FormOfWay_IsValid(int value);
constexpr Road_FormOfWay Road_FormOfWay_FormOfWay_MIN = Road_FormOfWay_FORMWAY_UNKNOWN;
constexpr Road_FormOfWay Road_FormOfWay_FormOfWay_MAX = Road_FormOfWay_FORMWAY_INVALID;
constexpr int Road_FormOfWay_FormOfWay_ARRAYSIZE = Road_FormOfWay_FormOfWay_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Road_FormOfWay_descriptor();
template<typename T>
inline const std::string& Road_FormOfWay_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Road_FormOfWay>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Road_FormOfWay_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Road_FormOfWay_descriptor(), enum_t_value);
}
inline bool Road_FormOfWay_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Road_FormOfWay* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Road_FormOfWay>(
    Road_FormOfWay_descriptor(), name, value);
}
// ===================================================================

class SensorStamp PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.sdmap.SensorStamp) */ {
 public:
  inline SensorStamp() : SensorStamp(nullptr) {}
  virtual ~SensorStamp();

  SensorStamp(const SensorStamp& from);
  SensorStamp(SensorStamp&& from) noexcept
    : SensorStamp() {
    *this = ::std::move(from);
  }

  inline SensorStamp& operator=(const SensorStamp& from) {
    CopyFrom(from);
    return *this;
  }
  inline SensorStamp& operator=(SensorStamp&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SensorStamp& default_instance();

  static inline const SensorStamp* internal_default_instance() {
    return reinterpret_cast<const SensorStamp*>(
               &_SensorStamp_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SensorStamp& a, SensorStamp& b) {
    a.Swap(&b);
  }
  inline void Swap(SensorStamp* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SensorStamp* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SensorStamp* New() const final {
    return CreateMaybeMessage<SensorStamp>(nullptr);
  }

  SensorStamp* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SensorStamp>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SensorStamp& from);
  void MergeFrom(const SensorStamp& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SensorStamp* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.sdmap.SensorStamp";
  }
  protected:
  explicit SensorStamp(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_sd_5fmap_2eproto);
    return ::descriptor_table_sd_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLidarStampFieldNumber = 1,
    kRadarStampFieldNumber = 2,
    kUssStampFieldNumber = 3,
    kChassisStampFieldNumber = 4,
    kCameraStampFieldNumber = 5,
    kImuinsStampFieldNumber = 6,
    kGnssStampFieldNumber = 7,
  };
  // double lidar_stamp = 1;
  void clear_lidar_stamp();
  double lidar_stamp() const;
  void set_lidar_stamp(double value);
  private:
  double _internal_lidar_stamp() const;
  void _internal_set_lidar_stamp(double value);
  public:

  // double radar_stamp = 2;
  void clear_radar_stamp();
  double radar_stamp() const;
  void set_radar_stamp(double value);
  private:
  double _internal_radar_stamp() const;
  void _internal_set_radar_stamp(double value);
  public:

  // double uss_stamp = 3;
  void clear_uss_stamp();
  double uss_stamp() const;
  void set_uss_stamp(double value);
  private:
  double _internal_uss_stamp() const;
  void _internal_set_uss_stamp(double value);
  public:

  // double chassis_stamp = 4;
  void clear_chassis_stamp();
  double chassis_stamp() const;
  void set_chassis_stamp(double value);
  private:
  double _internal_chassis_stamp() const;
  void _internal_set_chassis_stamp(double value);
  public:

  // double camera_stamp = 5;
  void clear_camera_stamp();
  double camera_stamp() const;
  void set_camera_stamp(double value);
  private:
  double _internal_camera_stamp() const;
  void _internal_set_camera_stamp(double value);
  public:

  // double imuins_stamp = 6;
  void clear_imuins_stamp();
  double imuins_stamp() const;
  void set_imuins_stamp(double value);
  private:
  double _internal_imuins_stamp() const;
  void _internal_set_imuins_stamp(double value);
  public:

  // double gnss_stamp = 7;
  void clear_gnss_stamp();
  double gnss_stamp() const;
  void set_gnss_stamp(double value);
  private:
  double _internal_gnss_stamp() const;
  void _internal_set_gnss_stamp(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.sdmap.SensorStamp)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double lidar_stamp_;
  double radar_stamp_;
  double uss_stamp_;
  double chassis_stamp_;
  double camera_stamp_;
  double imuins_stamp_;
  double gnss_stamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_sd_5fmap_2eproto;
};
// -------------------------------------------------------------------

class Header PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.sdmap.Header) */ {
 public:
  inline Header() : Header(nullptr) {}
  virtual ~Header();

  Header(const Header& from);
  Header(Header&& from) noexcept
    : Header() {
    *this = ::std::move(from);
  }

  inline Header& operator=(const Header& from) {
    CopyFrom(from);
    return *this;
  }
  inline Header& operator=(Header&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Header& default_instance();

  static inline const Header* internal_default_instance() {
    return reinterpret_cast<const Header*>(
               &_Header_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Header& a, Header& b) {
    a.Swap(&b);
  }
  inline void Swap(Header* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Header* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Header* New() const final {
    return CreateMaybeMessage<Header>(nullptr);
  }

  Header* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Header>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Header& from);
  void MergeFrom(const Header& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Header* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.sdmap.Header";
  }
  protected:
  explicit Header(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_sd_5fmap_2eproto);
    return ::descriptor_table_sd_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFrameIdFieldNumber = 2,
    kSensorStampFieldNumber = 5,
    kPublishStampFieldNumber = 3,
    kGnssStampFieldNumber = 4,
    kDataStampFieldNumber = 6,
    kSeqFieldNumber = 1,
  };
  // string frame_id = 2;
  void clear_frame_id();
  const std::string& frame_id() const;
  void set_frame_id(const std::string& value);
  void set_frame_id(std::string&& value);
  void set_frame_id(const char* value);
  void set_frame_id(const char* value, size_t size);
  std::string* mutable_frame_id();
  std::string* release_frame_id();
  void set_allocated_frame_id(std::string* frame_id);
  private:
  const std::string& _internal_frame_id() const;
  void _internal_set_frame_id(const std::string& value);
  std::string* _internal_mutable_frame_id();
  public:

  // .gwm.sdmap.SensorStamp sensor_stamp = 5;
  bool has_sensor_stamp() const;
  private:
  bool _internal_has_sensor_stamp() const;
  public:
  void clear_sensor_stamp();
  const ::gwm::sdmap::SensorStamp& sensor_stamp() const;
  ::gwm::sdmap::SensorStamp* release_sensor_stamp();
  ::gwm::sdmap::SensorStamp* mutable_sensor_stamp();
  void set_allocated_sensor_stamp(::gwm::sdmap::SensorStamp* sensor_stamp);
  private:
  const ::gwm::sdmap::SensorStamp& _internal_sensor_stamp() const;
  ::gwm::sdmap::SensorStamp* _internal_mutable_sensor_stamp();
  public:
  void unsafe_arena_set_allocated_sensor_stamp(
      ::gwm::sdmap::SensorStamp* sensor_stamp);
  ::gwm::sdmap::SensorStamp* unsafe_arena_release_sensor_stamp();

  // double publish_stamp = 3;
  void clear_publish_stamp();
  double publish_stamp() const;
  void set_publish_stamp(double value);
  private:
  double _internal_publish_stamp() const;
  void _internal_set_publish_stamp(double value);
  public:

  // double gnss_stamp = 4;
  void clear_gnss_stamp();
  double gnss_stamp() const;
  void set_gnss_stamp(double value);
  private:
  double _internal_gnss_stamp() const;
  void _internal_set_gnss_stamp(double value);
  public:

  // double data_stamp = 6;
  void clear_data_stamp();
  double data_stamp() const;
  void set_data_stamp(double value);
  private:
  double _internal_data_stamp() const;
  void _internal_set_data_stamp(double value);
  public:

  // int32 seq = 1;
  void clear_seq();
  ::PROTOBUF_NAMESPACE_ID::int32 seq() const;
  void set_seq(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_seq() const;
  void _internal_set_seq(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.sdmap.Header)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr frame_id_;
  ::gwm::sdmap::SensorStamp* sensor_stamp_;
  double publish_stamp_;
  double gnss_stamp_;
  double data_stamp_;
  ::PROTOBUF_NAMESPACE_ID::int32 seq_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_sd_5fmap_2eproto;
};
// -------------------------------------------------------------------

class Point3D PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.sdmap.Point3D) */ {
 public:
  inline Point3D() : Point3D(nullptr) {}
  virtual ~Point3D();

  Point3D(const Point3D& from);
  Point3D(Point3D&& from) noexcept
    : Point3D() {
    *this = ::std::move(from);
  }

  inline Point3D& operator=(const Point3D& from) {
    CopyFrom(from);
    return *this;
  }
  inline Point3D& operator=(Point3D&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Point3D& default_instance();

  static inline const Point3D* internal_default_instance() {
    return reinterpret_cast<const Point3D*>(
               &_Point3D_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Point3D& a, Point3D& b) {
    a.Swap(&b);
  }
  inline void Swap(Point3D* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Point3D* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Point3D* New() const final {
    return CreateMaybeMessage<Point3D>(nullptr);
  }

  Point3D* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Point3D>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Point3D& from);
  void MergeFrom(const Point3D& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Point3D* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.sdmap.Point3D";
  }
  protected:
  explicit Point3D(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_sd_5fmap_2eproto);
    return ::descriptor_table_sd_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 3;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.sdmap.Point3D)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double x_;
  double y_;
  double z_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_sd_5fmap_2eproto;
};
// -------------------------------------------------------------------

class ConnectRoads PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.sdmap.ConnectRoads) */ {
 public:
  inline ConnectRoads() : ConnectRoads(nullptr) {}
  virtual ~ConnectRoads();

  ConnectRoads(const ConnectRoads& from);
  ConnectRoads(ConnectRoads&& from) noexcept
    : ConnectRoads() {
    *this = ::std::move(from);
  }

  inline ConnectRoads& operator=(const ConnectRoads& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConnectRoads& operator=(ConnectRoads&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const ConnectRoads& default_instance();

  static inline const ConnectRoads* internal_default_instance() {
    return reinterpret_cast<const ConnectRoads*>(
               &_ConnectRoads_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ConnectRoads& a, ConnectRoads& b) {
    a.Swap(&b);
  }
  inline void Swap(ConnectRoads* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConnectRoads* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline ConnectRoads* New() const final {
    return CreateMaybeMessage<ConnectRoads>(nullptr);
  }

  ConnectRoads* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<ConnectRoads>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const ConnectRoads& from);
  void MergeFrom(const ConnectRoads& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConnectRoads* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.sdmap.ConnectRoads";
  }
  protected:
  explicit ConnectRoads(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_sd_5fmap_2eproto);
    return ::descriptor_table_sd_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFromRoadIdsFieldNumber = 1,
    kToRoadIdsFieldNumber = 2,
  };
  // repeated uint64 fromRoadIds = 1;
  int fromroadids_size() const;
  private:
  int _internal_fromroadids_size() const;
  public:
  void clear_fromroadids();
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_fromroadids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      _internal_fromroadids() const;
  void _internal_add_fromroadids(::PROTOBUF_NAMESPACE_ID::uint64 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      _internal_mutable_fromroadids();
  public:
  ::PROTOBUF_NAMESPACE_ID::uint64 fromroadids(int index) const;
  void set_fromroadids(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value);
  void add_fromroadids(::PROTOBUF_NAMESPACE_ID::uint64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      fromroadids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      mutable_fromroadids();

  // repeated uint64 toRoadIds = 2;
  int toroadids_size() const;
  private:
  int _internal_toroadids_size() const;
  public:
  void clear_toroadids();
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_toroadids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      _internal_toroadids() const;
  void _internal_add_toroadids(::PROTOBUF_NAMESPACE_ID::uint64 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      _internal_mutable_toroadids();
  public:
  ::PROTOBUF_NAMESPACE_ID::uint64 toroadids(int index) const;
  void set_toroadids(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value);
  void add_toroadids(::PROTOBUF_NAMESPACE_ID::uint64 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
      toroadids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
      mutable_toroadids();

  // @@protoc_insertion_point(class_scope:gwm.sdmap.ConnectRoads)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 > fromroadids_;
  mutable std::atomic<int> _fromroadids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 > toroadids_;
  mutable std::atomic<int> _toroadids_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_sd_5fmap_2eproto;
};
// -------------------------------------------------------------------

class Road PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.sdmap.Road) */ {
 public:
  inline Road() : Road(nullptr) {}
  virtual ~Road();

  Road(const Road& from);
  Road(Road&& from) noexcept
    : Road() {
    *this = ::std::move(from);
  }

  inline Road& operator=(const Road& from) {
    CopyFrom(from);
    return *this;
  }
  inline Road& operator=(Road&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Road& default_instance();

  static inline const Road* internal_default_instance() {
    return reinterpret_cast<const Road*>(
               &_Road_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Road& a, Road& b) {
    a.Swap(&b);
  }
  inline void Swap(Road* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Road* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Road* New() const final {
    return CreateMaybeMessage<Road>(nullptr);
  }

  Road* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Road>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Road& from);
  void MergeFrom(const Road& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Road* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.sdmap.Road";
  }
  protected:
  explicit Road(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_sd_5fmap_2eproto);
    return ::descriptor_table_sd_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef Road_RoadType RoadType;
  static constexpr RoadType ROADCLASS_FREEWAY =
    Road_RoadType_ROADCLASS_FREEWAY;
  static constexpr RoadType ROADCLASS_NATIONAL_ROAD =
    Road_RoadType_ROADCLASS_NATIONAL_ROAD;
  static constexpr RoadType ROADCLASS_PROVINCE_ROAD =
    Road_RoadType_ROADCLASS_PROVINCE_ROAD;
  static constexpr RoadType ROADCLASS_COUNTY_ROAD =
    Road_RoadType_ROADCLASS_COUNTY_ROAD;
  static constexpr RoadType ROADCLASS_RURAL_ROAD =
    Road_RoadType_ROADCLASS_RURAL_ROAD;
  static constexpr RoadType ROADCLASS_IN_COUNTY_ROAD =
    Road_RoadType_ROADCLASS_IN_COUNTY_ROAD;
  static constexpr RoadType ROADCLASS_CITY_SPEED_WAY =
    Road_RoadType_ROADCLASS_CITY_SPEED_WAY;
  static constexpr RoadType ROADCLASS_MAIN_ROAD =
    Road_RoadType_ROADCLASS_MAIN_ROAD;
  static constexpr RoadType ROADCLASS_SECONDARY_ROAD =
    Road_RoadType_ROADCLASS_SECONDARY_ROAD;
  static constexpr RoadType ROADCLASS_COMMON_ROAD =
    Road_RoadType_ROADCLASS_COMMON_ROAD;
  static constexpr RoadType ROADCLASS_NON_NAVI_ROAD =
    Road_RoadType_ROADCLASS_NON_NAVI_ROAD;
  static constexpr RoadType ROADCLASS_INVALID =
    Road_RoadType_ROADCLASS_INVALID;
  static inline bool RoadType_IsValid(int value) {
    return Road_RoadType_IsValid(value);
  }
  static constexpr RoadType RoadType_MIN =
    Road_RoadType_RoadType_MIN;
  static constexpr RoadType RoadType_MAX =
    Road_RoadType_RoadType_MAX;
  static constexpr int RoadType_ARRAYSIZE =
    Road_RoadType_RoadType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  RoadType_descriptor() {
    return Road_RoadType_descriptor();
  }
  template<typename T>
  static inline const std::string& RoadType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, RoadType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function RoadType_Name.");
    return Road_RoadType_Name(enum_t_value);
  }
  static inline bool RoadType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      RoadType* value) {
    return Road_RoadType_Parse(name, value);
  }

  typedef Road_FormOfWay FormOfWay;
  static constexpr FormOfWay FORMWAY_UNKNOWN =
    Road_FormOfWay_FORMWAY_UNKNOWN;
  static constexpr FormOfWay FORMWAY_DIVISED_LINK =
    Road_FormOfWay_FORMWAY_DIVISED_LINK;
  static constexpr FormOfWay FORMWAY_CROSS_LINK =
    Road_FormOfWay_FORMWAY_CROSS_LINK;
  static constexpr FormOfWay FORMWAY_JCT =
    Road_FormOfWay_FORMWAY_JCT;
  static constexpr FormOfWay FORMWAY_ROUND_CIRCLE =
    Road_FormOfWay_FORMWAY_ROUND_CIRCLE;
  static constexpr FormOfWay FORMWAY_SERVICE_ROAD =
    Road_FormOfWay_FORMWAY_SERVICE_ROAD;
  static constexpr FormOfWay FORMWAY_SLIP_ROAD =
    Road_FormOfWay_FORMWAY_SLIP_ROAD;
  static constexpr FormOfWay FORMWAY_SIDE_ROAD =
    Road_FormOfWay_FORMWAY_SIDE_ROAD;
  static constexpr FormOfWay FORMWAY_SLIP_JCT =
    Road_FormOfWay_FORMWAY_SLIP_JCT;
  static constexpr FormOfWay FORMWAY_EXIT_LINK =
    Road_FormOfWay_FORMWAY_EXIT_LINK;
  static constexpr FormOfWay FORMWAY_ENTRANCE_LINK =
    Road_FormOfWay_FORMWAY_ENTRANCE_LINK;
  static constexpr FormOfWay FORMWAY_TURN_RIGHT_LINEA =
    Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEA;
  static constexpr FormOfWay FORMWAY_TURN_RIGHT_LINEB =
    Road_FormOfWay_FORMWAY_TURN_RIGHT_LINEB;
  static constexpr FormOfWay FORMWAY_TURN_LEFT_LINEA =
    Road_FormOfWay_FORMWAY_TURN_LEFT_LINEA;
  static constexpr FormOfWay FORMWAY_TURN_LEFT_LINEB =
    Road_FormOfWay_FORMWAY_TURN_LEFT_LINEB;
  static constexpr FormOfWay FORMWAY_COMMON_LINK =
    Road_FormOfWay_FORMWAY_COMMON_LINK;
  static constexpr FormOfWay FORMWAY_TURN_LEFTRIGHT_LINE =
    Road_FormOfWay_FORMWAY_TURN_LEFTRIGHT_LINE;
  static constexpr FormOfWay FORMWAY_NONMOTORIZED_DRIVEWAY =
    Road_FormOfWay_FORMWAY_NONMOTORIZED_DRIVEWAY;
  static constexpr FormOfWay FORMWAY_FRONTDOOR_ROAD =
    Road_FormOfWay_FORMWAY_FRONTDOOR_ROAD;
  static constexpr FormOfWay FORMWAY_SERVICE_SLIP_ROAD =
    Road_FormOfWay_FORMWAY_SERVICE_SLIP_ROAD;
  static constexpr FormOfWay FORMWAY_SERVICE_JCT =
    Road_FormOfWay_FORMWAY_SERVICE_JCT;
  static constexpr FormOfWay FORMWAY_SERVICE_JCT_SLIP_ROAD =
    Road_FormOfWay_FORMWAY_SERVICE_JCT_SLIP_ROAD;
  static constexpr FormOfWay FORMWAY_NON_VEHICLE =
    Road_FormOfWay_FORMWAY_NON_VEHICLE;
  static constexpr FormOfWay FORMWAY_INVALID =
    Road_FormOfWay_FORMWAY_INVALID;
  static inline bool FormOfWay_IsValid(int value) {
    return Road_FormOfWay_IsValid(value);
  }
  static constexpr FormOfWay FormOfWay_MIN =
    Road_FormOfWay_FormOfWay_MIN;
  static constexpr FormOfWay FormOfWay_MAX =
    Road_FormOfWay_FormOfWay_MAX;
  static constexpr int FormOfWay_ARRAYSIZE =
    Road_FormOfWay_FormOfWay_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  FormOfWay_descriptor() {
    return Road_FormOfWay_descriptor();
  }
  template<typename T>
  static inline const std::string& FormOfWay_Name(T enum_t_value) {
    static_assert(::std::is_same<T, FormOfWay>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function FormOfWay_Name.");
    return Road_FormOfWay_Name(enum_t_value);
  }
  static inline bool FormOfWay_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      FormOfWay* value) {
    return Road_FormOfWay_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kPointsFieldNumber = 2,
    kArrowMarkingsFieldNumber = 4,
    kConnectRoadFieldNumber = 9,
    kRoadIdFieldNumber = 1,
    kLaneCountFieldNumber = 3,
    kAngleFFieldNumber = 5,
    kAngleTFieldNumber = 6,
    kRoadTypeFieldNumber = 7,
    kLengthFieldNumber = 10,
    kRoadFormFieldNumber = 8,
  };
  // repeated .gwm.sdmap.Point3D points = 2;
  int points_size() const;
  private:
  int _internal_points_size() const;
  public:
  void clear_points();
  ::gwm::sdmap::Point3D* mutable_points(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Point3D >*
      mutable_points();
  private:
  const ::gwm::sdmap::Point3D& _internal_points(int index) const;
  ::gwm::sdmap::Point3D* _internal_add_points();
  public:
  const ::gwm::sdmap::Point3D& points(int index) const;
  ::gwm::sdmap::Point3D* add_points();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Point3D >&
      points() const;

  // repeated uint32 arrowMarkings = 4;
  int arrowmarkings_size() const;
  private:
  int _internal_arrowmarkings_size() const;
  public:
  void clear_arrowmarkings();
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_arrowmarkings(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
      _internal_arrowmarkings() const;
  void _internal_add_arrowmarkings(::PROTOBUF_NAMESPACE_ID::uint32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
      _internal_mutable_arrowmarkings();
  public:
  ::PROTOBUF_NAMESPACE_ID::uint32 arrowmarkings(int index) const;
  void set_arrowmarkings(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value);
  void add_arrowmarkings(::PROTOBUF_NAMESPACE_ID::uint32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
      arrowmarkings() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
      mutable_arrowmarkings();

  // .gwm.sdmap.ConnectRoads connect_road = 9;
  bool has_connect_road() const;
  private:
  bool _internal_has_connect_road() const;
  public:
  void clear_connect_road();
  const ::gwm::sdmap::ConnectRoads& connect_road() const;
  ::gwm::sdmap::ConnectRoads* release_connect_road();
  ::gwm::sdmap::ConnectRoads* mutable_connect_road();
  void set_allocated_connect_road(::gwm::sdmap::ConnectRoads* connect_road);
  private:
  const ::gwm::sdmap::ConnectRoads& _internal_connect_road() const;
  ::gwm::sdmap::ConnectRoads* _internal_mutable_connect_road();
  public:
  void unsafe_arena_set_allocated_connect_road(
      ::gwm::sdmap::ConnectRoads* connect_road);
  ::gwm::sdmap::ConnectRoads* unsafe_arena_release_connect_road();

  // uint64 roadId = 1;
  void clear_roadid();
  ::PROTOBUF_NAMESPACE_ID::uint64 roadid() const;
  void set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_roadid() const;
  void _internal_set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 lane_count = 3;
  void clear_lane_count();
  ::PROTOBUF_NAMESPACE_ID::uint32 lane_count() const;
  void set_lane_count(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_lane_count() const;
  void _internal_set_lane_count(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 angleF = 5;
  void clear_anglef();
  ::PROTOBUF_NAMESPACE_ID::uint32 anglef() const;
  void set_anglef(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_anglef() const;
  void _internal_set_anglef(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // uint32 angleT = 6;
  void clear_anglet();
  ::PROTOBUF_NAMESPACE_ID::uint32 anglet() const;
  void set_anglet(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_anglet() const;
  void _internal_set_anglet(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // .gwm.sdmap.Road.RoadType road_type = 7;
  void clear_road_type();
  ::gwm::sdmap::Road_RoadType road_type() const;
  void set_road_type(::gwm::sdmap::Road_RoadType value);
  private:
  ::gwm::sdmap::Road_RoadType _internal_road_type() const;
  void _internal_set_road_type(::gwm::sdmap::Road_RoadType value);
  public:

  // uint64 length = 10;
  void clear_length();
  ::PROTOBUF_NAMESPACE_ID::uint64 length() const;
  void set_length(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_length() const;
  void _internal_set_length(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // .gwm.sdmap.Road.FormOfWay road_form = 8;
  void clear_road_form();
  ::gwm::sdmap::Road_FormOfWay road_form() const;
  void set_road_form(::gwm::sdmap::Road_FormOfWay value);
  private:
  ::gwm::sdmap::Road_FormOfWay _internal_road_form() const;
  void _internal_set_road_form(::gwm::sdmap::Road_FormOfWay value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.sdmap.Road)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Point3D > points_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 > arrowmarkings_;
  mutable std::atomic<int> _arrowmarkings_cached_byte_size_;
  ::gwm::sdmap::ConnectRoads* connect_road_;
  ::PROTOBUF_NAMESPACE_ID::uint64 roadid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 lane_count_;
  ::PROTOBUF_NAMESPACE_ID::uint32 anglef_;
  ::PROTOBUF_NAMESPACE_ID::uint32 anglet_;
  int road_type_;
  ::PROTOBUF_NAMESPACE_ID::uint64 length_;
  int road_form_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_sd_5fmap_2eproto;
};
// -------------------------------------------------------------------

class SDMapLocation PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.sdmap.SDMapLocation) */ {
 public:
  inline SDMapLocation() : SDMapLocation(nullptr) {}
  virtual ~SDMapLocation();

  SDMapLocation(const SDMapLocation& from);
  SDMapLocation(SDMapLocation&& from) noexcept
    : SDMapLocation() {
    *this = ::std::move(from);
  }

  inline SDMapLocation& operator=(const SDMapLocation& from) {
    CopyFrom(from);
    return *this;
  }
  inline SDMapLocation& operator=(SDMapLocation&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SDMapLocation& default_instance();

  static inline const SDMapLocation* internal_default_instance() {
    return reinterpret_cast<const SDMapLocation*>(
               &_SDMapLocation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SDMapLocation& a, SDMapLocation& b) {
    a.Swap(&b);
  }
  inline void Swap(SDMapLocation* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SDMapLocation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SDMapLocation* New() const final {
    return CreateMaybeMessage<SDMapLocation>(nullptr);
  }

  SDMapLocation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SDMapLocation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SDMapLocation& from);
  void MergeFrom(const SDMapLocation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SDMapLocation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.sdmap.SDMapLocation";
  }
  protected:
  explicit SDMapLocation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_sd_5fmap_2eproto);
    return ::descriptor_table_sd_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMatchPointFieldNumber = 3,
    kRoadIdFieldNumber = 1,
    kOffsetFieldNumber = 2,
  };
  // .gwm.sdmap.Point3D matchPoint = 3;
  bool has_matchpoint() const;
  private:
  bool _internal_has_matchpoint() const;
  public:
  void clear_matchpoint();
  const ::gwm::sdmap::Point3D& matchpoint() const;
  ::gwm::sdmap::Point3D* release_matchpoint();
  ::gwm::sdmap::Point3D* mutable_matchpoint();
  void set_allocated_matchpoint(::gwm::sdmap::Point3D* matchpoint);
  private:
  const ::gwm::sdmap::Point3D& _internal_matchpoint() const;
  ::gwm::sdmap::Point3D* _internal_mutable_matchpoint();
  public:
  void unsafe_arena_set_allocated_matchpoint(
      ::gwm::sdmap::Point3D* matchpoint);
  ::gwm::sdmap::Point3D* unsafe_arena_release_matchpoint();

  // uint64 roadId = 1;
  void clear_roadid();
  ::PROTOBUF_NAMESPACE_ID::uint64 roadid() const;
  void set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint64 _internal_roadid() const;
  void _internal_set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value);
  public:

  // uint32 offset = 2;
  void clear_offset();
  ::PROTOBUF_NAMESPACE_ID::uint32 offset() const;
  void set_offset(::PROTOBUF_NAMESPACE_ID::uint32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::uint32 _internal_offset() const;
  void _internal_set_offset(::PROTOBUF_NAMESPACE_ID::uint32 value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.sdmap.SDMapLocation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::gwm::sdmap::Point3D* matchpoint_;
  ::PROTOBUF_NAMESPACE_ID::uint64 roadid_;
  ::PROTOBUF_NAMESPACE_ID::uint32 offset_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_sd_5fmap_2eproto;
};
// -------------------------------------------------------------------

class SDMap PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.sdmap.SDMap) */ {
 public:
  inline SDMap() : SDMap(nullptr) {}
  virtual ~SDMap();

  SDMap(const SDMap& from);
  SDMap(SDMap&& from) noexcept
    : SDMap() {
    *this = ::std::move(from);
  }

  inline SDMap& operator=(const SDMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline SDMap& operator=(SDMap&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const SDMap& default_instance();

  static inline const SDMap* internal_default_instance() {
    return reinterpret_cast<const SDMap*>(
               &_SDMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(SDMap& a, SDMap& b) {
    a.Swap(&b);
  }
  inline void Swap(SDMap* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SDMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline SDMap* New() const final {
    return CreateMaybeMessage<SDMap>(nullptr);
  }

  SDMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<SDMap>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const SDMap& from);
  void MergeFrom(const SDMap& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SDMap* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.sdmap.SDMap";
  }
  protected:
  explicit SDMap(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_sd_5fmap_2eproto);
    return ::descriptor_table_sd_5fmap_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRoadFieldNumber = 2,
    kHeaderFieldNumber = 1,
    kSdLocationFieldNumber = 3,
  };
  // repeated .gwm.sdmap.Road road = 2;
  int road_size() const;
  private:
  int _internal_road_size() const;
  public:
  void clear_road();
  ::gwm::sdmap::Road* mutable_road(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Road >*
      mutable_road();
  private:
  const ::gwm::sdmap::Road& _internal_road(int index) const;
  ::gwm::sdmap::Road* _internal_add_road();
  public:
  const ::gwm::sdmap::Road& road(int index) const;
  ::gwm::sdmap::Road* add_road();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Road >&
      road() const;

  // .gwm.sdmap.Header header = 1;
  bool has_header() const;
  private:
  bool _internal_has_header() const;
  public:
  void clear_header();
  const ::gwm::sdmap::Header& header() const;
  ::gwm::sdmap::Header* release_header();
  ::gwm::sdmap::Header* mutable_header();
  void set_allocated_header(::gwm::sdmap::Header* header);
  private:
  const ::gwm::sdmap::Header& _internal_header() const;
  ::gwm::sdmap::Header* _internal_mutable_header();
  public:
  void unsafe_arena_set_allocated_header(
      ::gwm::sdmap::Header* header);
  ::gwm::sdmap::Header* unsafe_arena_release_header();

  // .gwm.sdmap.SDMapLocation sd_Location = 3;
  bool has_sd_location() const;
  private:
  bool _internal_has_sd_location() const;
  public:
  void clear_sd_location();
  const ::gwm::sdmap::SDMapLocation& sd_location() const;
  ::gwm::sdmap::SDMapLocation* release_sd_location();
  ::gwm::sdmap::SDMapLocation* mutable_sd_location();
  void set_allocated_sd_location(::gwm::sdmap::SDMapLocation* sd_location);
  private:
  const ::gwm::sdmap::SDMapLocation& _internal_sd_location() const;
  ::gwm::sdmap::SDMapLocation* _internal_mutable_sd_location();
  public:
  void unsafe_arena_set_allocated_sd_location(
      ::gwm::sdmap::SDMapLocation* sd_location);
  ::gwm::sdmap::SDMapLocation* unsafe_arena_release_sd_location();

  // @@protoc_insertion_point(class_scope:gwm.sdmap.SDMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Road > road_;
  ::gwm::sdmap::Header* header_;
  ::gwm::sdmap::SDMapLocation* sd_location_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_sd_5fmap_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SensorStamp

// double lidar_stamp = 1;
inline void SensorStamp::clear_lidar_stamp() {
  lidar_stamp_ = 0;
}
inline double SensorStamp::_internal_lidar_stamp() const {
  return lidar_stamp_;
}
inline double SensorStamp::lidar_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SensorStamp.lidar_stamp)
  return _internal_lidar_stamp();
}
inline void SensorStamp::_internal_set_lidar_stamp(double value) {
  
  lidar_stamp_ = value;
}
inline void SensorStamp::set_lidar_stamp(double value) {
  _internal_set_lidar_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SensorStamp.lidar_stamp)
}

// double radar_stamp = 2;
inline void SensorStamp::clear_radar_stamp() {
  radar_stamp_ = 0;
}
inline double SensorStamp::_internal_radar_stamp() const {
  return radar_stamp_;
}
inline double SensorStamp::radar_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SensorStamp.radar_stamp)
  return _internal_radar_stamp();
}
inline void SensorStamp::_internal_set_radar_stamp(double value) {
  
  radar_stamp_ = value;
}
inline void SensorStamp::set_radar_stamp(double value) {
  _internal_set_radar_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SensorStamp.radar_stamp)
}

// double uss_stamp = 3;
inline void SensorStamp::clear_uss_stamp() {
  uss_stamp_ = 0;
}
inline double SensorStamp::_internal_uss_stamp() const {
  return uss_stamp_;
}
inline double SensorStamp::uss_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SensorStamp.uss_stamp)
  return _internal_uss_stamp();
}
inline void SensorStamp::_internal_set_uss_stamp(double value) {
  
  uss_stamp_ = value;
}
inline void SensorStamp::set_uss_stamp(double value) {
  _internal_set_uss_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SensorStamp.uss_stamp)
}

// double chassis_stamp = 4;
inline void SensorStamp::clear_chassis_stamp() {
  chassis_stamp_ = 0;
}
inline double SensorStamp::_internal_chassis_stamp() const {
  return chassis_stamp_;
}
inline double SensorStamp::chassis_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SensorStamp.chassis_stamp)
  return _internal_chassis_stamp();
}
inline void SensorStamp::_internal_set_chassis_stamp(double value) {
  
  chassis_stamp_ = value;
}
inline void SensorStamp::set_chassis_stamp(double value) {
  _internal_set_chassis_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SensorStamp.chassis_stamp)
}

// double camera_stamp = 5;
inline void SensorStamp::clear_camera_stamp() {
  camera_stamp_ = 0;
}
inline double SensorStamp::_internal_camera_stamp() const {
  return camera_stamp_;
}
inline double SensorStamp::camera_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SensorStamp.camera_stamp)
  return _internal_camera_stamp();
}
inline void SensorStamp::_internal_set_camera_stamp(double value) {
  
  camera_stamp_ = value;
}
inline void SensorStamp::set_camera_stamp(double value) {
  _internal_set_camera_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SensorStamp.camera_stamp)
}

// double imuins_stamp = 6;
inline void SensorStamp::clear_imuins_stamp() {
  imuins_stamp_ = 0;
}
inline double SensorStamp::_internal_imuins_stamp() const {
  return imuins_stamp_;
}
inline double SensorStamp::imuins_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SensorStamp.imuins_stamp)
  return _internal_imuins_stamp();
}
inline void SensorStamp::_internal_set_imuins_stamp(double value) {
  
  imuins_stamp_ = value;
}
inline void SensorStamp::set_imuins_stamp(double value) {
  _internal_set_imuins_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SensorStamp.imuins_stamp)
}

// double gnss_stamp = 7;
inline void SensorStamp::clear_gnss_stamp() {
  gnss_stamp_ = 0;
}
inline double SensorStamp::_internal_gnss_stamp() const {
  return gnss_stamp_;
}
inline double SensorStamp::gnss_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SensorStamp.gnss_stamp)
  return _internal_gnss_stamp();
}
inline void SensorStamp::_internal_set_gnss_stamp(double value) {
  
  gnss_stamp_ = value;
}
inline void SensorStamp::set_gnss_stamp(double value) {
  _internal_set_gnss_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SensorStamp.gnss_stamp)
}

// -------------------------------------------------------------------

// Header

// int32 seq = 1;
inline void Header::clear_seq() {
  seq_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Header::_internal_seq() const {
  return seq_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Header::seq() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Header.seq)
  return _internal_seq();
}
inline void Header::_internal_set_seq(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  seq_ = value;
}
inline void Header::set_seq(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_seq(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Header.seq)
}

// string frame_id = 2;
inline void Header::clear_frame_id() {
  frame_id_.ClearToEmpty();
}
inline const std::string& Header::frame_id() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Header.frame_id)
  return _internal_frame_id();
}
inline void Header::set_frame_id(const std::string& value) {
  _internal_set_frame_id(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Header.frame_id)
}
inline std::string* Header::mutable_frame_id() {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.Header.frame_id)
  return _internal_mutable_frame_id();
}
inline const std::string& Header::_internal_frame_id() const {
  return frame_id_.Get();
}
inline void Header::_internal_set_frame_id(const std::string& value) {
  
  frame_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArena());
}
inline void Header::set_frame_id(std::string&& value) {
  
  frame_id_.Set(
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::move(value), GetArena());
  // @@protoc_insertion_point(field_set_rvalue:gwm.sdmap.Header.frame_id)
}
inline void Header::set_frame_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  
  frame_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(value), GetArena());
  // @@protoc_insertion_point(field_set_char:gwm.sdmap.Header.frame_id)
}
inline void Header::set_frame_id(const char* value,
    size_t size) {
  
  frame_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, ::std::string(
      reinterpret_cast<const char*>(value), size), GetArena());
  // @@protoc_insertion_point(field_set_pointer:gwm.sdmap.Header.frame_id)
}
inline std::string* Header::_internal_mutable_frame_id() {
  
  return frame_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArena());
}
inline std::string* Header::release_frame_id() {
  // @@protoc_insertion_point(field_release:gwm.sdmap.Header.frame_id)
  return frame_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}
inline void Header::set_allocated_frame_id(std::string* frame_id) {
  if (frame_id != nullptr) {
    
  } else {
    
  }
  frame_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), frame_id,
      GetArena());
  // @@protoc_insertion_point(field_set_allocated:gwm.sdmap.Header.frame_id)
}

// double publish_stamp = 3;
inline void Header::clear_publish_stamp() {
  publish_stamp_ = 0;
}
inline double Header::_internal_publish_stamp() const {
  return publish_stamp_;
}
inline double Header::publish_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Header.publish_stamp)
  return _internal_publish_stamp();
}
inline void Header::_internal_set_publish_stamp(double value) {
  
  publish_stamp_ = value;
}
inline void Header::set_publish_stamp(double value) {
  _internal_set_publish_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Header.publish_stamp)
}

// double gnss_stamp = 4;
inline void Header::clear_gnss_stamp() {
  gnss_stamp_ = 0;
}
inline double Header::_internal_gnss_stamp() const {
  return gnss_stamp_;
}
inline double Header::gnss_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Header.gnss_stamp)
  return _internal_gnss_stamp();
}
inline void Header::_internal_set_gnss_stamp(double value) {
  
  gnss_stamp_ = value;
}
inline void Header::set_gnss_stamp(double value) {
  _internal_set_gnss_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Header.gnss_stamp)
}

// .gwm.sdmap.SensorStamp sensor_stamp = 5;
inline bool Header::_internal_has_sensor_stamp() const {
  return this != internal_default_instance() && sensor_stamp_ != nullptr;
}
inline bool Header::has_sensor_stamp() const {
  return _internal_has_sensor_stamp();
}
inline void Header::clear_sensor_stamp() {
  if (GetArena() == nullptr && sensor_stamp_ != nullptr) {
    delete sensor_stamp_;
  }
  sensor_stamp_ = nullptr;
}
inline const ::gwm::sdmap::SensorStamp& Header::_internal_sensor_stamp() const {
  const ::gwm::sdmap::SensorStamp* p = sensor_stamp_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::sdmap::SensorStamp&>(
      ::gwm::sdmap::_SensorStamp_default_instance_);
}
inline const ::gwm::sdmap::SensorStamp& Header::sensor_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Header.sensor_stamp)
  return _internal_sensor_stamp();
}
inline void Header::unsafe_arena_set_allocated_sensor_stamp(
    ::gwm::sdmap::SensorStamp* sensor_stamp) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(sensor_stamp_);
  }
  sensor_stamp_ = sensor_stamp;
  if (sensor_stamp) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.sdmap.Header.sensor_stamp)
}
inline ::gwm::sdmap::SensorStamp* Header::release_sensor_stamp() {
  
  ::gwm::sdmap::SensorStamp* temp = sensor_stamp_;
  sensor_stamp_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::sdmap::SensorStamp* Header::unsafe_arena_release_sensor_stamp() {
  // @@protoc_insertion_point(field_release:gwm.sdmap.Header.sensor_stamp)
  
  ::gwm::sdmap::SensorStamp* temp = sensor_stamp_;
  sensor_stamp_ = nullptr;
  return temp;
}
inline ::gwm::sdmap::SensorStamp* Header::_internal_mutable_sensor_stamp() {
  
  if (sensor_stamp_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::sdmap::SensorStamp>(GetArena());
    sensor_stamp_ = p;
  }
  return sensor_stamp_;
}
inline ::gwm::sdmap::SensorStamp* Header::mutable_sensor_stamp() {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.Header.sensor_stamp)
  return _internal_mutable_sensor_stamp();
}
inline void Header::set_allocated_sensor_stamp(::gwm::sdmap::SensorStamp* sensor_stamp) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete sensor_stamp_;
  }
  if (sensor_stamp) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(sensor_stamp);
    if (message_arena != submessage_arena) {
      sensor_stamp = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sensor_stamp, submessage_arena);
    }
    
  } else {
    
  }
  sensor_stamp_ = sensor_stamp;
  // @@protoc_insertion_point(field_set_allocated:gwm.sdmap.Header.sensor_stamp)
}

// double data_stamp = 6;
inline void Header::clear_data_stamp() {
  data_stamp_ = 0;
}
inline double Header::_internal_data_stamp() const {
  return data_stamp_;
}
inline double Header::data_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Header.data_stamp)
  return _internal_data_stamp();
}
inline void Header::_internal_set_data_stamp(double value) {
  
  data_stamp_ = value;
}
inline void Header::set_data_stamp(double value) {
  _internal_set_data_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Header.data_stamp)
}

// -------------------------------------------------------------------

// Point3D

// double x = 1;
inline void Point3D::clear_x() {
  x_ = 0;
}
inline double Point3D::_internal_x() const {
  return x_;
}
inline double Point3D::x() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Point3D.x)
  return _internal_x();
}
inline void Point3D::_internal_set_x(double value) {
  
  x_ = value;
}
inline void Point3D::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Point3D.x)
}

// double y = 2;
inline void Point3D::clear_y() {
  y_ = 0;
}
inline double Point3D::_internal_y() const {
  return y_;
}
inline double Point3D::y() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Point3D.y)
  return _internal_y();
}
inline void Point3D::_internal_set_y(double value) {
  
  y_ = value;
}
inline void Point3D::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Point3D.y)
}

// double z = 3;
inline void Point3D::clear_z() {
  z_ = 0;
}
inline double Point3D::_internal_z() const {
  return z_;
}
inline double Point3D::z() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Point3D.z)
  return _internal_z();
}
inline void Point3D::_internal_set_z(double value) {
  
  z_ = value;
}
inline void Point3D::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Point3D.z)
}

// -------------------------------------------------------------------

// ConnectRoads

// repeated uint64 fromRoadIds = 1;
inline int ConnectRoads::_internal_fromroadids_size() const {
  return fromroadids_.size();
}
inline int ConnectRoads::fromroadids_size() const {
  return _internal_fromroadids_size();
}
inline void ConnectRoads::clear_fromroadids() {
  fromroadids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ConnectRoads::_internal_fromroadids(int index) const {
  return fromroadids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ConnectRoads::fromroadids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.ConnectRoads.fromRoadIds)
  return _internal_fromroadids(index);
}
inline void ConnectRoads::set_fromroadids(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value) {
  fromroadids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.ConnectRoads.fromRoadIds)
}
inline void ConnectRoads::_internal_add_fromroadids(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  fromroadids_.Add(value);
}
inline void ConnectRoads::add_fromroadids(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_add_fromroadids(value);
  // @@protoc_insertion_point(field_add:gwm.sdmap.ConnectRoads.fromRoadIds)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
ConnectRoads::_internal_fromroadids() const {
  return fromroadids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
ConnectRoads::fromroadids() const {
  // @@protoc_insertion_point(field_list:gwm.sdmap.ConnectRoads.fromRoadIds)
  return _internal_fromroadids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
ConnectRoads::_internal_mutable_fromroadids() {
  return &fromroadids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
ConnectRoads::mutable_fromroadids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.sdmap.ConnectRoads.fromRoadIds)
  return _internal_mutable_fromroadids();
}

// repeated uint64 toRoadIds = 2;
inline int ConnectRoads::_internal_toroadids_size() const {
  return toroadids_.size();
}
inline int ConnectRoads::toroadids_size() const {
  return _internal_toroadids_size();
}
inline void ConnectRoads::clear_toroadids() {
  toroadids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ConnectRoads::_internal_toroadids(int index) const {
  return toroadids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 ConnectRoads::toroadids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.ConnectRoads.toRoadIds)
  return _internal_toroadids(index);
}
inline void ConnectRoads::set_toroadids(int index, ::PROTOBUF_NAMESPACE_ID::uint64 value) {
  toroadids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.ConnectRoads.toRoadIds)
}
inline void ConnectRoads::_internal_add_toroadids(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  toroadids_.Add(value);
}
inline void ConnectRoads::add_toroadids(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_add_toroadids(value);
  // @@protoc_insertion_point(field_add:gwm.sdmap.ConnectRoads.toRoadIds)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
ConnectRoads::_internal_toroadids() const {
  return toroadids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >&
ConnectRoads::toroadids() const {
  // @@protoc_insertion_point(field_list:gwm.sdmap.ConnectRoads.toRoadIds)
  return _internal_toroadids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
ConnectRoads::_internal_mutable_toroadids() {
  return &toroadids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint64 >*
ConnectRoads::mutable_toroadids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.sdmap.ConnectRoads.toRoadIds)
  return _internal_mutable_toroadids();
}

// -------------------------------------------------------------------

// Road

// uint64 roadId = 1;
inline void Road::clear_roadid() {
  roadid_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Road::_internal_roadid() const {
  return roadid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Road::roadid() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.roadId)
  return _internal_roadid();
}
inline void Road::_internal_set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  roadid_ = value;
}
inline void Road::set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_roadid(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.roadId)
}

// repeated .gwm.sdmap.Point3D points = 2;
inline int Road::_internal_points_size() const {
  return points_.size();
}
inline int Road::points_size() const {
  return _internal_points_size();
}
inline void Road::clear_points() {
  points_.Clear();
}
inline ::gwm::sdmap::Point3D* Road::mutable_points(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.Road.points)
  return points_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Point3D >*
Road::mutable_points() {
  // @@protoc_insertion_point(field_mutable_list:gwm.sdmap.Road.points)
  return &points_;
}
inline const ::gwm::sdmap::Point3D& Road::_internal_points(int index) const {
  return points_.Get(index);
}
inline const ::gwm::sdmap::Point3D& Road::points(int index) const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.points)
  return _internal_points(index);
}
inline ::gwm::sdmap::Point3D* Road::_internal_add_points() {
  return points_.Add();
}
inline ::gwm::sdmap::Point3D* Road::add_points() {
  // @@protoc_insertion_point(field_add:gwm.sdmap.Road.points)
  return _internal_add_points();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Point3D >&
Road::points() const {
  // @@protoc_insertion_point(field_list:gwm.sdmap.Road.points)
  return points_;
}

// uint32 lane_count = 3;
inline void Road::clear_lane_count() {
  lane_count_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::_internal_lane_count() const {
  return lane_count_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::lane_count() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.lane_count)
  return _internal_lane_count();
}
inline void Road::_internal_set_lane_count(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  lane_count_ = value;
}
inline void Road::set_lane_count(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_lane_count(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.lane_count)
}

// repeated uint32 arrowMarkings = 4;
inline int Road::_internal_arrowmarkings_size() const {
  return arrowmarkings_.size();
}
inline int Road::arrowmarkings_size() const {
  return _internal_arrowmarkings_size();
}
inline void Road::clear_arrowmarkings() {
  arrowmarkings_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::_internal_arrowmarkings(int index) const {
  return arrowmarkings_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::arrowmarkings(int index) const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.arrowMarkings)
  return _internal_arrowmarkings(index);
}
inline void Road::set_arrowmarkings(int index, ::PROTOBUF_NAMESPACE_ID::uint32 value) {
  arrowmarkings_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.arrowMarkings)
}
inline void Road::_internal_add_arrowmarkings(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  arrowmarkings_.Add(value);
}
inline void Road::add_arrowmarkings(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_add_arrowmarkings(value);
  // @@protoc_insertion_point(field_add:gwm.sdmap.Road.arrowMarkings)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
Road::_internal_arrowmarkings() const {
  return arrowmarkings_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >&
Road::arrowmarkings() const {
  // @@protoc_insertion_point(field_list:gwm.sdmap.Road.arrowMarkings)
  return _internal_arrowmarkings();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
Road::_internal_mutable_arrowmarkings() {
  return &arrowmarkings_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::uint32 >*
Road::mutable_arrowmarkings() {
  // @@protoc_insertion_point(field_mutable_list:gwm.sdmap.Road.arrowMarkings)
  return _internal_mutable_arrowmarkings();
}

// uint32 angleF = 5;
inline void Road::clear_anglef() {
  anglef_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::_internal_anglef() const {
  return anglef_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::anglef() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.angleF)
  return _internal_anglef();
}
inline void Road::_internal_set_anglef(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  anglef_ = value;
}
inline void Road::set_anglef(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_anglef(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.angleF)
}

// uint32 angleT = 6;
inline void Road::clear_anglet() {
  anglet_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::_internal_anglet() const {
  return anglet_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 Road::anglet() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.angleT)
  return _internal_anglet();
}
inline void Road::_internal_set_anglet(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  anglet_ = value;
}
inline void Road::set_anglet(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_anglet(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.angleT)
}

// .gwm.sdmap.Road.RoadType road_type = 7;
inline void Road::clear_road_type() {
  road_type_ = 0;
}
inline ::gwm::sdmap::Road_RoadType Road::_internal_road_type() const {
  return static_cast< ::gwm::sdmap::Road_RoadType >(road_type_);
}
inline ::gwm::sdmap::Road_RoadType Road::road_type() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.road_type)
  return _internal_road_type();
}
inline void Road::_internal_set_road_type(::gwm::sdmap::Road_RoadType value) {
  
  road_type_ = value;
}
inline void Road::set_road_type(::gwm::sdmap::Road_RoadType value) {
  _internal_set_road_type(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.road_type)
}

// .gwm.sdmap.Road.FormOfWay road_form = 8;
inline void Road::clear_road_form() {
  road_form_ = 0;
}
inline ::gwm::sdmap::Road_FormOfWay Road::_internal_road_form() const {
  return static_cast< ::gwm::sdmap::Road_FormOfWay >(road_form_);
}
inline ::gwm::sdmap::Road_FormOfWay Road::road_form() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.road_form)
  return _internal_road_form();
}
inline void Road::_internal_set_road_form(::gwm::sdmap::Road_FormOfWay value) {
  
  road_form_ = value;
}
inline void Road::set_road_form(::gwm::sdmap::Road_FormOfWay value) {
  _internal_set_road_form(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.road_form)
}

// .gwm.sdmap.ConnectRoads connect_road = 9;
inline bool Road::_internal_has_connect_road() const {
  return this != internal_default_instance() && connect_road_ != nullptr;
}
inline bool Road::has_connect_road() const {
  return _internal_has_connect_road();
}
inline void Road::clear_connect_road() {
  if (GetArena() == nullptr && connect_road_ != nullptr) {
    delete connect_road_;
  }
  connect_road_ = nullptr;
}
inline const ::gwm::sdmap::ConnectRoads& Road::_internal_connect_road() const {
  const ::gwm::sdmap::ConnectRoads* p = connect_road_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::sdmap::ConnectRoads&>(
      ::gwm::sdmap::_ConnectRoads_default_instance_);
}
inline const ::gwm::sdmap::ConnectRoads& Road::connect_road() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.connect_road)
  return _internal_connect_road();
}
inline void Road::unsafe_arena_set_allocated_connect_road(
    ::gwm::sdmap::ConnectRoads* connect_road) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(connect_road_);
  }
  connect_road_ = connect_road;
  if (connect_road) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.sdmap.Road.connect_road)
}
inline ::gwm::sdmap::ConnectRoads* Road::release_connect_road() {
  
  ::gwm::sdmap::ConnectRoads* temp = connect_road_;
  connect_road_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::sdmap::ConnectRoads* Road::unsafe_arena_release_connect_road() {
  // @@protoc_insertion_point(field_release:gwm.sdmap.Road.connect_road)
  
  ::gwm::sdmap::ConnectRoads* temp = connect_road_;
  connect_road_ = nullptr;
  return temp;
}
inline ::gwm::sdmap::ConnectRoads* Road::_internal_mutable_connect_road() {
  
  if (connect_road_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::sdmap::ConnectRoads>(GetArena());
    connect_road_ = p;
  }
  return connect_road_;
}
inline ::gwm::sdmap::ConnectRoads* Road::mutable_connect_road() {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.Road.connect_road)
  return _internal_mutable_connect_road();
}
inline void Road::set_allocated_connect_road(::gwm::sdmap::ConnectRoads* connect_road) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete connect_road_;
  }
  if (connect_road) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(connect_road);
    if (message_arena != submessage_arena) {
      connect_road = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, connect_road, submessage_arena);
    }
    
  } else {
    
  }
  connect_road_ = connect_road;
  // @@protoc_insertion_point(field_set_allocated:gwm.sdmap.Road.connect_road)
}

// uint64 length = 10;
inline void Road::clear_length() {
  length_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Road::_internal_length() const {
  return length_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 Road::length() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.Road.length)
  return _internal_length();
}
inline void Road::_internal_set_length(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  length_ = value;
}
inline void Road::set_length(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_length(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.Road.length)
}

// -------------------------------------------------------------------

// SDMapLocation

// uint64 roadId = 1;
inline void SDMapLocation::clear_roadid() {
  roadid_ = PROTOBUF_ULONGLONG(0);
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SDMapLocation::_internal_roadid() const {
  return roadid_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint64 SDMapLocation::roadid() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SDMapLocation.roadId)
  return _internal_roadid();
}
inline void SDMapLocation::_internal_set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  
  roadid_ = value;
}
inline void SDMapLocation::set_roadid(::PROTOBUF_NAMESPACE_ID::uint64 value) {
  _internal_set_roadid(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SDMapLocation.roadId)
}

// uint32 offset = 2;
inline void SDMapLocation::clear_offset() {
  offset_ = 0u;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SDMapLocation::_internal_offset() const {
  return offset_;
}
inline ::PROTOBUF_NAMESPACE_ID::uint32 SDMapLocation::offset() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SDMapLocation.offset)
  return _internal_offset();
}
inline void SDMapLocation::_internal_set_offset(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  
  offset_ = value;
}
inline void SDMapLocation::set_offset(::PROTOBUF_NAMESPACE_ID::uint32 value) {
  _internal_set_offset(value);
  // @@protoc_insertion_point(field_set:gwm.sdmap.SDMapLocation.offset)
}

// .gwm.sdmap.Point3D matchPoint = 3;
inline bool SDMapLocation::_internal_has_matchpoint() const {
  return this != internal_default_instance() && matchpoint_ != nullptr;
}
inline bool SDMapLocation::has_matchpoint() const {
  return _internal_has_matchpoint();
}
inline void SDMapLocation::clear_matchpoint() {
  if (GetArena() == nullptr && matchpoint_ != nullptr) {
    delete matchpoint_;
  }
  matchpoint_ = nullptr;
}
inline const ::gwm::sdmap::Point3D& SDMapLocation::_internal_matchpoint() const {
  const ::gwm::sdmap::Point3D* p = matchpoint_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::sdmap::Point3D&>(
      ::gwm::sdmap::_Point3D_default_instance_);
}
inline const ::gwm::sdmap::Point3D& SDMapLocation::matchpoint() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SDMapLocation.matchPoint)
  return _internal_matchpoint();
}
inline void SDMapLocation::unsafe_arena_set_allocated_matchpoint(
    ::gwm::sdmap::Point3D* matchpoint) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(matchpoint_);
  }
  matchpoint_ = matchpoint;
  if (matchpoint) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.sdmap.SDMapLocation.matchPoint)
}
inline ::gwm::sdmap::Point3D* SDMapLocation::release_matchpoint() {
  
  ::gwm::sdmap::Point3D* temp = matchpoint_;
  matchpoint_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::sdmap::Point3D* SDMapLocation::unsafe_arena_release_matchpoint() {
  // @@protoc_insertion_point(field_release:gwm.sdmap.SDMapLocation.matchPoint)
  
  ::gwm::sdmap::Point3D* temp = matchpoint_;
  matchpoint_ = nullptr;
  return temp;
}
inline ::gwm::sdmap::Point3D* SDMapLocation::_internal_mutable_matchpoint() {
  
  if (matchpoint_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::sdmap::Point3D>(GetArena());
    matchpoint_ = p;
  }
  return matchpoint_;
}
inline ::gwm::sdmap::Point3D* SDMapLocation::mutable_matchpoint() {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.SDMapLocation.matchPoint)
  return _internal_mutable_matchpoint();
}
inline void SDMapLocation::set_allocated_matchpoint(::gwm::sdmap::Point3D* matchpoint) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete matchpoint_;
  }
  if (matchpoint) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(matchpoint);
    if (message_arena != submessage_arena) {
      matchpoint = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, matchpoint, submessage_arena);
    }
    
  } else {
    
  }
  matchpoint_ = matchpoint;
  // @@protoc_insertion_point(field_set_allocated:gwm.sdmap.SDMapLocation.matchPoint)
}

// -------------------------------------------------------------------

// SDMap

// .gwm.sdmap.Header header = 1;
inline bool SDMap::_internal_has_header() const {
  return this != internal_default_instance() && header_ != nullptr;
}
inline bool SDMap::has_header() const {
  return _internal_has_header();
}
inline void SDMap::clear_header() {
  if (GetArena() == nullptr && header_ != nullptr) {
    delete header_;
  }
  header_ = nullptr;
}
inline const ::gwm::sdmap::Header& SDMap::_internal_header() const {
  const ::gwm::sdmap::Header* p = header_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::sdmap::Header&>(
      ::gwm::sdmap::_Header_default_instance_);
}
inline const ::gwm::sdmap::Header& SDMap::header() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SDMap.header)
  return _internal_header();
}
inline void SDMap::unsafe_arena_set_allocated_header(
    ::gwm::sdmap::Header* header) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(header_);
  }
  header_ = header;
  if (header) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.sdmap.SDMap.header)
}
inline ::gwm::sdmap::Header* SDMap::release_header() {
  
  ::gwm::sdmap::Header* temp = header_;
  header_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::sdmap::Header* SDMap::unsafe_arena_release_header() {
  // @@protoc_insertion_point(field_release:gwm.sdmap.SDMap.header)
  
  ::gwm::sdmap::Header* temp = header_;
  header_ = nullptr;
  return temp;
}
inline ::gwm::sdmap::Header* SDMap::_internal_mutable_header() {
  
  if (header_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::sdmap::Header>(GetArena());
    header_ = p;
  }
  return header_;
}
inline ::gwm::sdmap::Header* SDMap::mutable_header() {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.SDMap.header)
  return _internal_mutable_header();
}
inline void SDMap::set_allocated_header(::gwm::sdmap::Header* header) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete header_;
  }
  if (header) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(header);
    if (message_arena != submessage_arena) {
      header = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, header, submessage_arena);
    }
    
  } else {
    
  }
  header_ = header;
  // @@protoc_insertion_point(field_set_allocated:gwm.sdmap.SDMap.header)
}

// repeated .gwm.sdmap.Road road = 2;
inline int SDMap::_internal_road_size() const {
  return road_.size();
}
inline int SDMap::road_size() const {
  return _internal_road_size();
}
inline void SDMap::clear_road() {
  road_.Clear();
}
inline ::gwm::sdmap::Road* SDMap::mutable_road(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.SDMap.road)
  return road_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Road >*
SDMap::mutable_road() {
  // @@protoc_insertion_point(field_mutable_list:gwm.sdmap.SDMap.road)
  return &road_;
}
inline const ::gwm::sdmap::Road& SDMap::_internal_road(int index) const {
  return road_.Get(index);
}
inline const ::gwm::sdmap::Road& SDMap::road(int index) const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SDMap.road)
  return _internal_road(index);
}
inline ::gwm::sdmap::Road* SDMap::_internal_add_road() {
  return road_.Add();
}
inline ::gwm::sdmap::Road* SDMap::add_road() {
  // @@protoc_insertion_point(field_add:gwm.sdmap.SDMap.road)
  return _internal_add_road();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::sdmap::Road >&
SDMap::road() const {
  // @@protoc_insertion_point(field_list:gwm.sdmap.SDMap.road)
  return road_;
}

// .gwm.sdmap.SDMapLocation sd_Location = 3;
inline bool SDMap::_internal_has_sd_location() const {
  return this != internal_default_instance() && sd_location_ != nullptr;
}
inline bool SDMap::has_sd_location() const {
  return _internal_has_sd_location();
}
inline void SDMap::clear_sd_location() {
  if (GetArena() == nullptr && sd_location_ != nullptr) {
    delete sd_location_;
  }
  sd_location_ = nullptr;
}
inline const ::gwm::sdmap::SDMapLocation& SDMap::_internal_sd_location() const {
  const ::gwm::sdmap::SDMapLocation* p = sd_location_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::sdmap::SDMapLocation&>(
      ::gwm::sdmap::_SDMapLocation_default_instance_);
}
inline const ::gwm::sdmap::SDMapLocation& SDMap::sd_location() const {
  // @@protoc_insertion_point(field_get:gwm.sdmap.SDMap.sd_Location)
  return _internal_sd_location();
}
inline void SDMap::unsafe_arena_set_allocated_sd_location(
    ::gwm::sdmap::SDMapLocation* sd_location) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(sd_location_);
  }
  sd_location_ = sd_location;
  if (sd_location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.sdmap.SDMap.sd_Location)
}
inline ::gwm::sdmap::SDMapLocation* SDMap::release_sd_location() {
  
  ::gwm::sdmap::SDMapLocation* temp = sd_location_;
  sd_location_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::sdmap::SDMapLocation* SDMap::unsafe_arena_release_sd_location() {
  // @@protoc_insertion_point(field_release:gwm.sdmap.SDMap.sd_Location)
  
  ::gwm::sdmap::SDMapLocation* temp = sd_location_;
  sd_location_ = nullptr;
  return temp;
}
inline ::gwm::sdmap::SDMapLocation* SDMap::_internal_mutable_sd_location() {
  
  if (sd_location_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::sdmap::SDMapLocation>(GetArena());
    sd_location_ = p;
  }
  return sd_location_;
}
inline ::gwm::sdmap::SDMapLocation* SDMap::mutable_sd_location() {
  // @@protoc_insertion_point(field_mutable:gwm.sdmap.SDMap.sd_Location)
  return _internal_mutable_sd_location();
}
inline void SDMap::set_allocated_sd_location(::gwm::sdmap::SDMapLocation* sd_location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete sd_location_;
  }
  if (sd_location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(sd_location);
    if (message_arena != submessage_arena) {
      sd_location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, sd_location, submessage_arena);
    }
    
  } else {
    
  }
  sd_location_ = sd_location;
  // @@protoc_insertion_point(field_set_allocated:gwm.sdmap.SDMap.sd_Location)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace sdmap
}  // namespace gwm

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::gwm::sdmap::Road_RoadType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::sdmap::Road_RoadType>() {
  return ::gwm::sdmap::Road_RoadType_descriptor();
}
template <> struct is_proto_enum< ::gwm::sdmap::Road_FormOfWay> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::sdmap::Road_FormOfWay>() {
  return ::gwm::sdmap::Road_FormOfWay_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_sd_5fmap_2eproto
