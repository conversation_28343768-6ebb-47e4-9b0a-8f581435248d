// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: hd_map_lane.proto

#include "hd_map_lane.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Point3D_common_2fgeometry_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_common_2fgeometry_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Polyline_common_2fgeometry_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<6> scc_info_Lane_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_LaneBoundary_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_LaneBoundaryType_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_LaneGroup_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_LanePassableInfo_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_LaneRule_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_LaneSampleAssociation_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_LocationInfo_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_MergeSplit_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_MergeSplit_Merge_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_MergeSplit_Split_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_NeighborMerge_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_NeighborMerge_Ids_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Rule_hd_5fmap_5flane_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Trigger_hd_5fmap_5flane_2eproto;
namespace gwm {
namespace hdmap {
class LaneBoundaryTypeDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<LaneBoundaryType> _instance;
} _LaneBoundaryType_default_instance_;
class LaneBoundaryDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<LaneBoundary> _instance;
} _LaneBoundary_default_instance_;
class LaneSampleAssociationDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<LaneSampleAssociation> _instance;
} _LaneSampleAssociation_default_instance_;
class EntranceDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Entrance> _instance;
} _Entrance_default_instance_;
class RuleDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Rule> _instance;
} _Rule_default_instance_;
class TriggerDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Trigger> _instance;
} _Trigger_default_instance_;
class LaneRuleDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<LaneRule> _instance;
} _LaneRule_default_instance_;
class MergeSplit_MergeDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<MergeSplit_Merge> _instance;
} _MergeSplit_Merge_default_instance_;
class MergeSplit_SplitDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<MergeSplit_Split> _instance;
} _MergeSplit_Split_default_instance_;
class MergeSplitDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<MergeSplit> _instance;
} _MergeSplit_default_instance_;
class NeighborMerge_IdsDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<NeighborMerge_Ids> _instance;
} _NeighborMerge_Ids_default_instance_;
class NeighborMergeDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<NeighborMerge> _instance;
} _NeighborMerge_default_instance_;
class LaneDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Lane> _instance;
} _Lane_default_instance_;
class EgoLaneInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<EgoLaneInfo> _instance;
} _EgoLaneInfo_default_instance_;
class LanePassableInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<LanePassableInfo> _instance;
} _LanePassableInfo_default_instance_;
class LaneGroupDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<LaneGroup> _instance;
} _LaneGroup_default_instance_;
class RoutingLaneInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RoutingLaneInfo> _instance;
} _RoutingLaneInfo_default_instance_;
class RoutingMapInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<RoutingMapInfo> _instance;
} _RoutingMapInfo_default_instance_;
class LocationInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<LocationInfo> _instance;
} _LocationInfo_default_instance_;
class TrackListDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TrackList> _instance;
} _TrackList_default_instance_;
}  // namespace hdmap
}  // namespace gwm
static void InitDefaultsscc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_EgoLaneInfo_default_instance_;
    new (ptr) ::gwm::hdmap::EgoLaneInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto}, {
      &scc_info_Point3D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_Entrance_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_Entrance_default_instance_;
    new (ptr) ::gwm::hdmap::Entrance();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Entrance_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_Entrance_hd_5fmap_5flane_2eproto}, {
      &scc_info_Point3D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_Lane_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_Lane_default_instance_;
    new (ptr) ::gwm::hdmap::Lane();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<6> scc_info_Lane_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 6, 0, InitDefaultsscc_info_Lane_hd_5fmap_5flane_2eproto}, {
      &scc_info_LaneBoundary_hd_5fmap_5flane_2eproto.base,
      &scc_info_LaneSampleAssociation_hd_5fmap_5flane_2eproto.base,
      &scc_info_Polyline_common_2fgeometry_2eproto.base,
      &scc_info_LaneRule_hd_5fmap_5flane_2eproto.base,
      &scc_info_MergeSplit_hd_5fmap_5flane_2eproto.base,
      &scc_info_NeighborMerge_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_LaneBoundary_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_LaneBoundary_default_instance_;
    new (ptr) ::gwm::hdmap::LaneBoundary();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_LaneBoundary_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_LaneBoundary_hd_5fmap_5flane_2eproto}, {
      &scc_info_LaneBoundaryType_hd_5fmap_5flane_2eproto.base,
      &scc_info_Polyline_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_LaneBoundaryType_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_LaneBoundaryType_default_instance_;
    new (ptr) ::gwm::hdmap::LaneBoundaryType();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_LaneBoundaryType_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_LaneBoundaryType_hd_5fmap_5flane_2eproto}, {}};

static void InitDefaultsscc_info_LaneGroup_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_LaneGroup_default_instance_;
    new (ptr) ::gwm::hdmap::LaneGroup();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_LaneGroup_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_LaneGroup_hd_5fmap_5flane_2eproto}, {
      &scc_info_LanePassableInfo_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_LanePassableInfo_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_LanePassableInfo_default_instance_;
    new (ptr) ::gwm::hdmap::LanePassableInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_LanePassableInfo_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_LanePassableInfo_hd_5fmap_5flane_2eproto}, {}};

static void InitDefaultsscc_info_LaneRule_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_LaneRule_default_instance_;
    new (ptr) ::gwm::hdmap::LaneRule();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_LaneRule_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_LaneRule_hd_5fmap_5flane_2eproto}, {
      &scc_info_Trigger_hd_5fmap_5flane_2eproto.base,
      &scc_info_Rule_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_LaneSampleAssociation_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_LaneSampleAssociation_default_instance_;
    new (ptr) ::gwm::hdmap::LaneSampleAssociation();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_LaneSampleAssociation_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_LaneSampleAssociation_hd_5fmap_5flane_2eproto}, {}};

static void InitDefaultsscc_info_LocationInfo_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_LocationInfo_default_instance_;
    new (ptr) ::gwm::hdmap::LocationInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_LocationInfo_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_LocationInfo_hd_5fmap_5flane_2eproto}, {
      &scc_info_Point3D_common_2fgeometry_2eproto.base,}};

static void InitDefaultsscc_info_MergeSplit_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_MergeSplit_default_instance_;
    new (ptr) ::gwm::hdmap::MergeSplit();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_MergeSplit_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_MergeSplit_hd_5fmap_5flane_2eproto}, {
      &scc_info_MergeSplit_Merge_hd_5fmap_5flane_2eproto.base,
      &scc_info_MergeSplit_Split_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_MergeSplit_Merge_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_MergeSplit_Merge_default_instance_;
    new (ptr) ::gwm::hdmap::MergeSplit_Merge();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_MergeSplit_Merge_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_MergeSplit_Merge_hd_5fmap_5flane_2eproto}, {}};

static void InitDefaultsscc_info_MergeSplit_Split_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_MergeSplit_Split_default_instance_;
    new (ptr) ::gwm::hdmap::MergeSplit_Split();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_MergeSplit_Split_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_MergeSplit_Split_hd_5fmap_5flane_2eproto}, {}};

static void InitDefaultsscc_info_NeighborMerge_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_NeighborMerge_default_instance_;
    new (ptr) ::gwm::hdmap::NeighborMerge();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_NeighborMerge_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_NeighborMerge_hd_5fmap_5flane_2eproto}, {
      &scc_info_NeighborMerge_Ids_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_NeighborMerge_Ids_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_NeighborMerge_Ids_default_instance_;
    new (ptr) ::gwm::hdmap::NeighborMerge_Ids();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_NeighborMerge_Ids_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_NeighborMerge_Ids_hd_5fmap_5flane_2eproto}, {}};

static void InitDefaultsscc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_RoutingLaneInfo_default_instance_;
    new (ptr) ::gwm::hdmap::RoutingLaneInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto}, {
      &scc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto.base,
      &scc_info_LaneGroup_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_RoutingMapInfo_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_RoutingMapInfo_default_instance_;
    new (ptr) ::gwm::hdmap::RoutingMapInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_RoutingMapInfo_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_RoutingMapInfo_hd_5fmap_5flane_2eproto}, {
      &scc_info_Lane_hd_5fmap_5flane_2eproto.base,
      &scc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_Rule_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_Rule_default_instance_;
    new (ptr) ::gwm::hdmap::Rule();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Rule_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Rule_hd_5fmap_5flane_2eproto}, {}};

static void InitDefaultsscc_info_TrackList_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_TrackList_default_instance_;
    new (ptr) ::gwm::hdmap::TrackList();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TrackList_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_TrackList_hd_5fmap_5flane_2eproto}, {
      &scc_info_LocationInfo_hd_5fmap_5flane_2eproto.base,}};

static void InitDefaultsscc_info_Trigger_hd_5fmap_5flane_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_Trigger_default_instance_;
    new (ptr) ::gwm::hdmap::Trigger();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Trigger_hd_5fmap_5flane_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Trigger_hd_5fmap_5flane_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_hd_5fmap_5flane_2eproto[20];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_hd_5fmap_5flane_2eproto[11];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_hd_5fmap_5flane_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_hd_5fmap_5flane_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundaryType, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundaryType, s_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundaryType, types_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, length_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, virtual__),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, boundary_type_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, boundary_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, crossable_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, cost_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, layers_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, type_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, has_left_deceleration_marking_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneBoundary, has_right_deceleration_marking_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneSampleAssociation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneSampleAssociation, s_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneSampleAssociation, width_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Entrance, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Entrance, id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Entrance, lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Entrance, location_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Entrance, layers_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Rule, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Rule, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Rule, rule_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Trigger, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Trigger, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Trigger, trigger_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneRule, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneRule, vehicle_type_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneRule, trigger_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneRule, rule_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit_Merge, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit_Merge, direction_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit_Merge, to_lane_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit_Split, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit_Split, direction_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit_Split, from_lane_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::MergeSplit, type_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::NeighborMerge_Ids, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::NeighborMerge_Ids, ids_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::NeighborMerge, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::NeighborMerge, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::NeighborMerge, successor_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::NeighborMerge, type_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, left_boundary_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, right_boundary_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, length_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, speed_limit_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, overlap_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, predecessor_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, successor_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, left_neighbor_forward_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, right_neighbor_forward_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, type_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, turn_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, left_neighbor_reverse_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, right_neighbor_reverse_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, junction_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, left_sample_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, right_sample_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, direction_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, left_road_sample_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, right_road_sample_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, self_reverse_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, centerline_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, centerline_s_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, left_boundary_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, right_boundary_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, boundary_direction_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, cost_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, merge_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, merge_to_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, merge_from_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, layers_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, bidi_copy_from_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, manually_set_left_neighbor_forward_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, manually_set_right_neighbor_forward_lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, road_name_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, is_disabled_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, is_virtual_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, truck_speed_limit_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, min_width_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, max_curvature_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, heading_diff_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, lane_rules_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, left_boundary_plus_ids_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, right_boundary_plus_ids_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, merge_splits_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, road_section_ids_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, neighbor_merges_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, manually_set_predecessor_ids_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, manually_set_successor_ids_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, next_lane_ids_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, last_lane_ids_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, slope_type_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::Lane, types_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, lane_index_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, prev_coordinate_index_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, offset_length_from_prev_point_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, offset_length_from_start_point_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, distance_to_line_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::EgoLaneInfo, projecting_point_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LanePassableInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LanePassableInfo, lane_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LanePassableInfo, lane_index_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LanePassableInfo, lane_accessibility_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneGroup, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LaneGroup, lane_frame_info_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::RoutingLaneInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::RoutingLaneInfo, time_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::RoutingLaneInfo, ego_lane_info_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::RoutingLaneInfo, lane_groups_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::RoutingMapInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::RoutingMapInfo, lanes_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::RoutingMapInfo, routing_lanes_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LocationInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LocationInfo, point_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LocationInfo, radias_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LocationInfo, heading_degree_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LocationInfo, max_tolerance_angle_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::LocationInfo, time_stamp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::TrackList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::TrackList, locations_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::gwm::hdmap::LaneBoundaryType)},
  { 7, -1, sizeof(::gwm::hdmap::LaneBoundary)},
  { 23, -1, sizeof(::gwm::hdmap::LaneSampleAssociation)},
  { 30, -1, sizeof(::gwm::hdmap::Entrance)},
  { 39, -1, sizeof(::gwm::hdmap::Rule)},
  { 47, -1, sizeof(::gwm::hdmap::Trigger)},
  { 54, -1, sizeof(::gwm::hdmap::LaneRule)},
  { 62, -1, sizeof(::gwm::hdmap::MergeSplit_Merge)},
  { 69, -1, sizeof(::gwm::hdmap::MergeSplit_Split)},
  { 76, -1, sizeof(::gwm::hdmap::MergeSplit)},
  { 84, -1, sizeof(::gwm::hdmap::NeighborMerge_Ids)},
  { 90, -1, sizeof(::gwm::hdmap::NeighborMerge)},
  { 99, -1, sizeof(::gwm::hdmap::Lane)},
  { 157, -1, sizeof(::gwm::hdmap::EgoLaneInfo)},
  { 169, -1, sizeof(::gwm::hdmap::LanePassableInfo)},
  { 177, -1, sizeof(::gwm::hdmap::LaneGroup)},
  { 183, -1, sizeof(::gwm::hdmap::RoutingLaneInfo)},
  { 191, -1, sizeof(::gwm::hdmap::RoutingMapInfo)},
  { 198, -1, sizeof(::gwm::hdmap::LocationInfo)},
  { 208, -1, sizeof(::gwm::hdmap::TrackList)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_LaneBoundaryType_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_LaneBoundary_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_LaneSampleAssociation_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_Entrance_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_Rule_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_Trigger_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_LaneRule_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_MergeSplit_Merge_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_MergeSplit_Split_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_MergeSplit_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_NeighborMerge_Ids_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_NeighborMerge_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_Lane_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_EgoLaneInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_LanePassableInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_LaneGroup_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_RoutingLaneInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_RoutingMapInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_LocationInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_TrackList_default_instance_),
};

const char descriptor_table_protodef_hd_5fmap_5flane_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\021hd_map_lane.proto\022\tgwm.hdmap\032\025common/g"
  "eometry.proto\"\231\007\n\020LaneBoundaryType\022\t\n\001s\030"
  "\001 \001(\001\022/\n\005types\030\002 \003(\0162 .gwm.hdmap.LaneBou"
  "ndaryType.Type\"\310\006\n\004Type\022\013\n\007UNKNOWN\020\000\022\021\n\r"
  "DOTTED_YELLOW\020\001\022\020\n\014DOTTED_WHITE\020\002\022\020\n\014SOL"
  "ID_YELLOW\020\003\022\017\n\013SOLID_WHITE\020\004\022\021\n\rDOUBLE_Y"
  "ELLOW\020\005\022\010\n\004CURB\020\006\022\027\n\023DOUBLE_SOLID_YELLOW"
  "\020\007\022\027\n\023DOUBLE_DOTTED_WHITE\020\010\022\030\n\024DOUBLE_DO"
  "TTED_YELLOW\020\t\022\026\n\022DOUBLE_SOLID_WHITE\020\n\022\030\n"
  "\024DOUBLE_LEFT_TO_RIGHT\020\013\022\030\n\024DOUBLE_RIGHT_"
  "TO_LEFT\020\014\022\023\n\017PEDESTRIAN_POLE\020\r\022\033\n\027OTHER_"
  "PHYSICAL_OBSTACLE\020\016\022\037\n\033DOUBLE_LEFT_TO_RI"
  "GHT_YELLOW\020\017\022\037\n\033DOUBLE_RIGHT_TO_LEFT_YEL"
  "LOW\020\020\022\034\n\030SHORT_THICK_DOTTED_WHITE\020\021\022%\n!D"
  "OUBLE_LEFT_WHITE_TO_RIGHT_YELLOW\020\022\022%\n!DO"
  "UBLE_RIGHT_WHITE_TO_LEFT_YELLOW\020\023\022%\n!DOU"
  "BLE_LEFT_YELLOW_TO_RIGHT_WHITE\020\024\022%\n!DOUB"
  "LE_RIGHT_YELLOW_TO_LEFT_WHITE\020\025\022\020\n\014SOLID"
  "_ORANGE\020\026\022\021\n\rDOTTED_ORANGE\020\027\022\016\n\nSOLID_BL"
  "UE\020\030\022\017\n\013DOTTED_BLUE\020\031\022\010\n\004EDGE\020\032\022,\n(DOUBL"
  "E_SOLID_LEFT_YELLOW_AND_RIGHT_WHITE\020\033\022-\n"
  ")DOUBLE_DOTTED_LEFT_YELLOW_AND_RIGHT_WHI"
  "TE\020\034\022,\n(DOUBLE_SOLID_LEFT_WHITE_AND_RIGH"
  "T_YELLOW\020\035\022-\n)DOUBLE_DOTTED_LEFT_WHITE_A"
  "ND_RIGHT_YELLOW\020\036\"\344\003\n\014LaneBoundary\022\016\n\006le"
  "ngth\030\002 \001(\001\022\017\n\007virtual\030\003 \001(\010\0222\n\rboundary_"
  "type\030\004 \003(\0132\033.gwm.hdmap.LaneBoundaryType\022"
  "\n\n\002id\030\005 \001(\005\022&\n\010boundary\030\006 \001(\0132\024.gwm.comm"
  "on.Polyline\0224\n\tcrossable\030\007 \001(\0162!.gwm.hdm"
  "ap.LaneBoundary.Crossable\022\014\n\004cost\030\010 \001(\001\022"
  "\016\n\006layers\030\t \003(\005\022.\n\004type\030\n \001(\0162 .gwm.hdma"
  "p.LaneBoundaryType.Type\022%\n\035has_left_dece"
  "leration_marking\030\014 \001(\010\022&\n\036has_right_dece"
  "leration_marking\030\r \001(\010\"x\n\tCrossable\022\022\n\016P"
  "HYSICALLY_NOT\020\000\022\017\n\013LEGALLY_NOT\020\001\022\021\n\rRIGH"
  "T_TO_LEFT\020\002\022\021\n\rLEFT_TO_RIGHT\020\003\022\010\n\004BOTH\020\004"
  "\022\026\n\022CAR_PHYSICALLY_NOT\020\005\"1\n\025LaneSampleAs"
  "sociation\022\t\n\001s\030\001 \001(\001\022\r\n\005width\030\002 \001(\001\"^\n\010E"
  "ntrance\022\n\n\002id\030\001 \001(\005\022\017\n\007lane_id\030\002 \001(\005\022%\n\010"
  "location\030\003 \001(\0132\023.gwm.common.Point3D\022\016\n\006l"
  "ayers\030\004 \003(\005\"9\n\004Rule\022\025\n\013speed_limit\030\001 \001(\002"
  "H\000\022\022\n\010disabled\030\002 \001(\010H\000B\006\n\004rule\"&\n\007Trigge"
  "r\022\020\n\006always\030\001 \001(\010H\000B\t\n\007trigger\"\262\001\n\010LaneR"
  "ule\0225\n\014vehicle_type\030\001 \001(\0162\037.gwm.hdmap.La"
  "neRule.VehicleType\022#\n\007trigger\030\002 \001(\0132\022.gw"
  "m.hdmap.Trigger\022\035\n\004rule\030\003 \001(\0132\017.gwm.hdma"
  "p.Rule\"+\n\013VehicleType\022\013\n\007DEFAULT\020\000\022\017\n\013LI"
  "GHT_TRUCK\020\001\"\303\002\n\nMergeSplit\022,\n\005merge\030\001 \001("
  "\0132\033.gwm.hdmap.MergeSplit.MergeH\000\022,\n\005spli"
  "t\030\002 \001(\0132\033.gwm.hdmap.MergeSplit.SplitH\000\032O"
  "\n\005Merge\0222\n\tdirection\030\001 \001(\0162\037.gwm.hdmap.M"
  "ergeSplit.Direction\022\022\n\nto_lane_id\030\002 \001(\005\032"
  "Q\n\005Split\0222\n\tdirection\030\001 \001(\0162\037.gwm.hdmap."
  "MergeSplit.Direction\022\024\n\014from_lane_id\030\002 \001"
  "(\005\"-\n\tDirection\022\013\n\007UNKNOWN\020\000\022\010\n\004LEFT\020\001\022\t"
  "\n\005RIGHT\020\002B\006\n\004type\"\237\001\n\rNeighborMerge\022;\n\023m"
  "erge_from_lane_ids\030\001 \001(\0132\034.gwm.hdmap.Nei"
  "ghborMerge.IdsH\000\022\032\n\020merge_to_lane_id\030\002 \001"
  "(\005H\000\022\031\n\021successor_lane_id\030\003 \001(\005\032\022\n\003Ids\022\013"
  "\n\003ids\030\001 \003(\005B\006\n\004type\"\341\023\n\004Lane\022\n\n\002id\030\001 \001(\005"
  "\022.\n\rleft_boundary\030\003 \001(\0132\027.gwm.hdmap.Lane"
  "Boundary\022/\n\016right_boundary\030\004 \001(\0132\027.gwm.h"
  "dmap.LaneBoundary\022\016\n\006length\030\005 \001(\001\022\023\n\013spe"
  "ed_limit\030\006 \001(\002\022\022\n\noverlap_id\030\007 \003(\005\022\026\n\016pr"
  "edecessor_id\030\010 \003(\005\022\024\n\014successor_id\030\t \003(\005"
  "\022%\n\035left_neighbor_forward_lane_id\030\n \001(\005\022"
  "&\n\036right_neighbor_forward_lane_id\030\013 \001(\005\022"
  "&\n\004type\030\014 \001(\0162\030.gwm.hdmap.Lane.LaneType\022"
  "&\n\004turn\030\r \001(\0162\030.gwm.hdmap.Lane.LaneTurn\022"
  "%\n\035left_neighbor_reverse_lane_id\030\016 \001(\005\022&"
  "\n\036right_neighbor_reverse_lane_id\030\017 \001(\005\022\023"
  "\n\013junction_id\030\020 \001(\005\0225\n\013left_sample\030\021 \003(\013"
  "2 .gwm.hdmap.LaneSampleAssociation\0226\n\014ri"
  "ght_sample\030\022 \003(\0132 .gwm.hdmap.LaneSampleA"
  "ssociation\0220\n\tdirection\030\023 \001(\0162\035.gwm.hdma"
  "p.Lane.LaneDirection\022:\n\020left_road_sample"
  "\030\024 \003(\0132 .gwm.hdmap.LaneSampleAssociation"
  "\022;\n\021right_road_sample\030\025 \003(\0132 .gwm.hdmap."
  "LaneSampleAssociation\022\034\n\024self_reverse_la"
  "ne_id\030\026 \003(\005\022(\n\ncenterline\030\027 \001(\0132\024.gwm.co"
  "mmon.Polyline\022\030\n\014centerline_s\030\030 \003(\002B\002\020\001\022"
  "\030\n\020left_boundary_id\030\031 \001(\005\022\031\n\021right_bound"
  "ary_id\030\032 \001(\005\022=\n\022boundary_direction\030\033 \001(\016"
  "2!.gwm.hdmap.Lane.BoundaryDirection\022\014\n\004c"
  "ost\030\035 \001(\001\022(\n\005merge\030\036 \001(\0162\031.gwm.hdmap.Lan"
  "e.MergeType\022\030\n\020merge_to_lane_id\030\037 \001(\005\022\032\n"
  "\022merge_from_lane_id\030  \003(\005\022\016\n\006layers\030! \003("
  "\005\022\031\n\021bidi_copy_from_id\030\" \001(\005\0222\n*manually"
  "_set_left_neighbor_forward_lane_id\030# \001(\005"
  "\0223\n+manually_set_right_neighbor_forward_"
  "lane_id\030$ \001(\005\022\021\n\troad_name\030% \003(\t\022\023\n\013is_d"
  "isabled\030& \001(\010\022\026\n\nis_virtual\030\' \001(\010B\002\030\001\022\031\n"
  "\021truck_speed_limit\030( \001(\002\022\021\n\tmin_width\030) "
  "\001(\002\022\025\n\rmax_curvature\030* \001(\002\022\024\n\014heading_di"
  "ff\030+ \001(\002\022\'\n\nlane_rules\030, \003(\0132\023.gwm.hdmap"
  ".LaneRule\022\036\n\026left_boundary_plus_ids\030- \003("
  "\005\022\037\n\027right_boundary_plus_ids\030. \003(\005\022+\n\014me"
  "rge_splits\030/ \003(\0132\025.gwm.hdmap.MergeSplit\022"
  "\030\n\020road_section_ids\0300 \003(\005\0221\n\017neighbor_me"
  "rges\0301 \003(\0132\030.gwm.hdmap.NeighborMerge\022(\n\034"
  "manually_set_predecessor_ids\0302 \003(\005B\002\030\001\022&"
  "\n\032manually_set_successor_ids\0303 \003(\005B\002\030\001\022\025"
  "\n\rnext_lane_ids\0304 \003(\005\022\025\n\rlast_lane_ids\0305"
  " \003(\005\022-\n\nslope_type\0306 \001(\0162\031.gwm.hdmap.Lan"
  "e.SlopeType\022\'\n\005types\0307 \003(\0162\030.gwm.hdmap.L"
  "ane.LaneType\"\351\002\n\010LaneType\022\013\n\007UNKNOWN\020\000\022\013"
  "\n\007HIGHWAY\020\001\022\n\n\006STREET\020\002\022\021\n\rBIDIRECTIONAL"
  "\020\003\022\014\n\010SHOULDER\020\004\022\n\n\006BIKING\020\005\022\014\n\010SIDEWALK"
  "\020\006\022\016\n\nRESTRICTED\020\007\022\013\n\007PARKING\020\010\022\014\n\010ROADW"
  "ORK\020\t\022\013\n\007OFFRAMP\020\n\022\n\n\006ONRAMP\020\013\022\013\n\007BUSLAN"
  "E\020\014\022\027\n\023LEFTTURNWAITINGAREA\020\r\022\010\n\004PARK\020\016\022\016"
  "\n\nROUNDABOUT\020\017\022\023\n\017RIGHT_TURN_ONLY\020\020\022\020\n\014P"
  "ARK_ON_LANE\020\021\022\020\n\014DYNAMIC_LANE\020\022\022\r\n\tWIDE_"
  "LANE\020\023\022\016\n\nTIDAL_LANE\020\024\022\021\n\rTRANSFER_LANE\020"
  "\025\022\r\n\tEMERGENCY\020\026\"]\n\010LaneTurn\022\013\n\007INVALID\020"
  "\000\022\014\n\010STRAIGHT\020\001\022\010\n\004LEFT\020\002\022\t\n\005RIGHT\020\004\022\017\n\013"
  "U_TURN_LEFT\020\010\022\020\n\014U_TURN_RIGHT\020\020\";\n\rLaneD"
  "irection\022\013\n\007FORWARD\020\000\022\014\n\010BACKWARD\020\001\022\017\n\013B"
  "IDIRECTION\020\002\"T\n\021BoundaryDirection\022\010\n\004SAM"
  "E\020\000\022\020\n\014LEFT_REVERSE\020\001\022\021\n\rRIGHT_REVERSE\020\002"
  "\022\020\n\014BOTH_REVERSE\020\003\"4\n\tMergeType\022\010\n\004NONE\020"
  "\000\022\r\n\tFROM_LEFT\020\001\022\016\n\nFROM_RIGHT\020\002\"%\n\tSlop"
  "eType\022\n\n\006UPHILL\020\000\022\014\n\010DOWNHILL\020\001\"\351\001\n\013EgoL"
  "aneInfo\022\017\n\007lane_id\030\001 \001(\005\022\022\n\nlane_index\030\002"
  " \001(\005\022\035\n\025prev_coordinate_index\030\003 \001(\005\022%\n\035o"
  "ffset_length_from_prev_point\030\004 \001(\002\022&\n\036of"
  "fset_length_from_start_point\030\005 \001(\002\022\030\n\020di"
  "stance_to_line\030\006 \001(\002\022-\n\020projecting_point"
  "\030\007 \001(\0132\023.gwm.common.Point3D\"\306\001\n\020LanePass"
  "ableInfo\022\017\n\007lane_id\030\001 \001(\005\022\022\n\nlane_index\030"
  "\002 \001(\005\022I\n\022lane_accessibility\030\003 \001(\0162-.gwm."
  "hdmap.LanePassableInfo.LaneAccessibility"
  "\"B\n\021LaneAccessibility\022\013\n\007DEFAULT\020\000\022\r\n\tRE"
  "COMMEND\020\001\022\021\n\rNOT_RECOMMEND\020\002\"A\n\tLaneGrou"
  "p\0224\n\017lane_frame_info\030\001 \003(\0132\033.gwm.hdmap.L"
  "anePassableInfo\"\177\n\017RoutingLaneInfo\022\022\n\nti"
  "me_stamp\030\001 \001(\001\022-\n\rego_lane_info\030\002 \003(\0132\026."
  "gwm.hdmap.EgoLaneInfo\022)\n\013lane_groups\030\003 \003"
  "(\0132\024.gwm.hdmap.LaneGroup\"c\n\016RoutingMapIn"
  "fo\022\036\n\005lanes\030\001 \003(\0132\017.gwm.hdmap.Lane\0221\n\rro"
  "uting_lanes\030\002 \003(\0132\032.gwm.hdmap.RoutingLan"
  "eInfo\"\213\001\n\014LocationInfo\022\"\n\005point\030\001 \001(\0132\023."
  "gwm.common.Point3D\022\016\n\006radias\030\002 \001(\001\022\026\n\016he"
  "ading_degree\030\003 \001(\001\022\033\n\023max_tolerance_angl"
  "e\030\004 \001(\001\022\022\n\ntime_stamp\030\005 \001(\001\"7\n\tTrackList"
  "\022*\n\tlocations\030\001 \003(\0132\027.gwm.hdmap.Location"
  "Infob\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_hd_5fmap_5flane_2eproto_deps[1] = {
  &::descriptor_table_common_2fgeometry_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_hd_5fmap_5flane_2eproto_sccs[20] = {
  &scc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto.base,
  &scc_info_Entrance_hd_5fmap_5flane_2eproto.base,
  &scc_info_Lane_hd_5fmap_5flane_2eproto.base,
  &scc_info_LaneBoundary_hd_5fmap_5flane_2eproto.base,
  &scc_info_LaneBoundaryType_hd_5fmap_5flane_2eproto.base,
  &scc_info_LaneGroup_hd_5fmap_5flane_2eproto.base,
  &scc_info_LanePassableInfo_hd_5fmap_5flane_2eproto.base,
  &scc_info_LaneRule_hd_5fmap_5flane_2eproto.base,
  &scc_info_LaneSampleAssociation_hd_5fmap_5flane_2eproto.base,
  &scc_info_LocationInfo_hd_5fmap_5flane_2eproto.base,
  &scc_info_MergeSplit_hd_5fmap_5flane_2eproto.base,
  &scc_info_MergeSplit_Merge_hd_5fmap_5flane_2eproto.base,
  &scc_info_MergeSplit_Split_hd_5fmap_5flane_2eproto.base,
  &scc_info_NeighborMerge_hd_5fmap_5flane_2eproto.base,
  &scc_info_NeighborMerge_Ids_hd_5fmap_5flane_2eproto.base,
  &scc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto.base,
  &scc_info_RoutingMapInfo_hd_5fmap_5flane_2eproto.base,
  &scc_info_Rule_hd_5fmap_5flane_2eproto.base,
  &scc_info_TrackList_hd_5fmap_5flane_2eproto.base,
  &scc_info_Trigger_hd_5fmap_5flane_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_hd_5fmap_5flane_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_hd_5fmap_5flane_2eproto = {
  false, false, descriptor_table_protodef_hd_5fmap_5flane_2eproto, "hd_map_lane.proto", 5852,
  &descriptor_table_hd_5fmap_5flane_2eproto_once, descriptor_table_hd_5fmap_5flane_2eproto_sccs, descriptor_table_hd_5fmap_5flane_2eproto_deps, 20, 1,
  schemas, file_default_instances, TableStruct_hd_5fmap_5flane_2eproto::offsets,
  file_level_metadata_hd_5fmap_5flane_2eproto, 20, file_level_enum_descriptors_hd_5fmap_5flane_2eproto, file_level_service_descriptors_hd_5fmap_5flane_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_hd_5fmap_5flane_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto)), true);
namespace gwm {
namespace hdmap {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LaneBoundaryType_Type_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[0];
}
bool LaneBoundaryType_Type_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
    case 27:
    case 28:
    case 29:
    case 30:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr LaneBoundaryType_Type LaneBoundaryType::UNKNOWN;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOTTED_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOTTED_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::SOLID_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::SOLID_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::CURB;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_SOLID_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_DOTTED_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_DOTTED_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_SOLID_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_LEFT_TO_RIGHT;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_RIGHT_TO_LEFT;
constexpr LaneBoundaryType_Type LaneBoundaryType::PEDESTRIAN_POLE;
constexpr LaneBoundaryType_Type LaneBoundaryType::OTHER_PHYSICAL_OBSTACLE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_LEFT_TO_RIGHT_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_RIGHT_TO_LEFT_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::SHORT_THICK_DOTTED_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_LEFT_WHITE_TO_RIGHT_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_RIGHT_WHITE_TO_LEFT_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_LEFT_YELLOW_TO_RIGHT_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_RIGHT_YELLOW_TO_LEFT_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::SOLID_ORANGE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOTTED_ORANGE;
constexpr LaneBoundaryType_Type LaneBoundaryType::SOLID_BLUE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOTTED_BLUE;
constexpr LaneBoundaryType_Type LaneBoundaryType::EDGE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_SOLID_LEFT_YELLOW_AND_RIGHT_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_DOTTED_LEFT_YELLOW_AND_RIGHT_WHITE;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_SOLID_LEFT_WHITE_AND_RIGHT_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::DOUBLE_DOTTED_LEFT_WHITE_AND_RIGHT_YELLOW;
constexpr LaneBoundaryType_Type LaneBoundaryType::Type_MIN;
constexpr LaneBoundaryType_Type LaneBoundaryType::Type_MAX;
constexpr int LaneBoundaryType::Type_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LaneBoundary_Crossable_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[1];
}
bool LaneBoundary_Crossable_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr LaneBoundary_Crossable LaneBoundary::PHYSICALLY_NOT;
constexpr LaneBoundary_Crossable LaneBoundary::LEGALLY_NOT;
constexpr LaneBoundary_Crossable LaneBoundary::RIGHT_TO_LEFT;
constexpr LaneBoundary_Crossable LaneBoundary::LEFT_TO_RIGHT;
constexpr LaneBoundary_Crossable LaneBoundary::BOTH;
constexpr LaneBoundary_Crossable LaneBoundary::CAR_PHYSICALLY_NOT;
constexpr LaneBoundary_Crossable LaneBoundary::Crossable_MIN;
constexpr LaneBoundary_Crossable LaneBoundary::Crossable_MAX;
constexpr int LaneBoundary::Crossable_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LaneRule_VehicleType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[2];
}
bool LaneRule_VehicleType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr LaneRule_VehicleType LaneRule::DEFAULT;
constexpr LaneRule_VehicleType LaneRule::LIGHT_TRUCK;
constexpr LaneRule_VehicleType LaneRule::VehicleType_MIN;
constexpr LaneRule_VehicleType LaneRule::VehicleType_MAX;
constexpr int LaneRule::VehicleType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MergeSplit_Direction_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[3];
}
bool MergeSplit_Direction_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr MergeSplit_Direction MergeSplit::UNKNOWN;
constexpr MergeSplit_Direction MergeSplit::LEFT;
constexpr MergeSplit_Direction MergeSplit::RIGHT;
constexpr MergeSplit_Direction MergeSplit::Direction_MIN;
constexpr MergeSplit_Direction MergeSplit::Direction_MAX;
constexpr int MergeSplit::Direction_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_LaneType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[4];
}
bool Lane_LaneType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Lane_LaneType Lane::UNKNOWN;
constexpr Lane_LaneType Lane::HIGHWAY;
constexpr Lane_LaneType Lane::STREET;
constexpr Lane_LaneType Lane::BIDIRECTIONAL;
constexpr Lane_LaneType Lane::SHOULDER;
constexpr Lane_LaneType Lane::BIKING;
constexpr Lane_LaneType Lane::SIDEWALK;
constexpr Lane_LaneType Lane::RESTRICTED;
constexpr Lane_LaneType Lane::PARKING;
constexpr Lane_LaneType Lane::ROADWORK;
constexpr Lane_LaneType Lane::OFFRAMP;
constexpr Lane_LaneType Lane::ONRAMP;
constexpr Lane_LaneType Lane::BUSLANE;
constexpr Lane_LaneType Lane::LEFTTURNWAITINGAREA;
constexpr Lane_LaneType Lane::PARK;
constexpr Lane_LaneType Lane::ROUNDABOUT;
constexpr Lane_LaneType Lane::RIGHT_TURN_ONLY;
constexpr Lane_LaneType Lane::PARK_ON_LANE;
constexpr Lane_LaneType Lane::DYNAMIC_LANE;
constexpr Lane_LaneType Lane::WIDE_LANE;
constexpr Lane_LaneType Lane::TIDAL_LANE;
constexpr Lane_LaneType Lane::TRANSFER_LANE;
constexpr Lane_LaneType Lane::EMERGENCY;
constexpr Lane_LaneType Lane::LaneType_MIN;
constexpr Lane_LaneType Lane::LaneType_MAX;
constexpr int Lane::LaneType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_LaneTurn_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[5];
}
bool Lane_LaneTurn_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 4:
    case 8:
    case 16:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Lane_LaneTurn Lane::INVALID;
constexpr Lane_LaneTurn Lane::STRAIGHT;
constexpr Lane_LaneTurn Lane::LEFT;
constexpr Lane_LaneTurn Lane::RIGHT;
constexpr Lane_LaneTurn Lane::U_TURN_LEFT;
constexpr Lane_LaneTurn Lane::U_TURN_RIGHT;
constexpr Lane_LaneTurn Lane::LaneTurn_MIN;
constexpr Lane_LaneTurn Lane::LaneTurn_MAX;
constexpr int Lane::LaneTurn_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_LaneDirection_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[6];
}
bool Lane_LaneDirection_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Lane_LaneDirection Lane::FORWARD;
constexpr Lane_LaneDirection Lane::BACKWARD;
constexpr Lane_LaneDirection Lane::BIDIRECTION;
constexpr Lane_LaneDirection Lane::LaneDirection_MIN;
constexpr Lane_LaneDirection Lane::LaneDirection_MAX;
constexpr int Lane::LaneDirection_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_BoundaryDirection_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[7];
}
bool Lane_BoundaryDirection_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Lane_BoundaryDirection Lane::SAME;
constexpr Lane_BoundaryDirection Lane::LEFT_REVERSE;
constexpr Lane_BoundaryDirection Lane::RIGHT_REVERSE;
constexpr Lane_BoundaryDirection Lane::BOTH_REVERSE;
constexpr Lane_BoundaryDirection Lane::BoundaryDirection_MIN;
constexpr Lane_BoundaryDirection Lane::BoundaryDirection_MAX;
constexpr int Lane::BoundaryDirection_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_MergeType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[8];
}
bool Lane_MergeType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Lane_MergeType Lane::NONE;
constexpr Lane_MergeType Lane::FROM_LEFT;
constexpr Lane_MergeType Lane::FROM_RIGHT;
constexpr Lane_MergeType Lane::MergeType_MIN;
constexpr Lane_MergeType Lane::MergeType_MAX;
constexpr int Lane::MergeType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_SlopeType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[9];
}
bool Lane_SlopeType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Lane_SlopeType Lane::UPHILL;
constexpr Lane_SlopeType Lane::DOWNHILL;
constexpr Lane_SlopeType Lane::SlopeType_MIN;
constexpr Lane_SlopeType Lane::SlopeType_MAX;
constexpr int Lane::SlopeType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LanePassableInfo_LaneAccessibility_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_hd_5fmap_5flane_2eproto);
  return file_level_enum_descriptors_hd_5fmap_5flane_2eproto[10];
}
bool LanePassableInfo_LaneAccessibility_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr LanePassableInfo_LaneAccessibility LanePassableInfo::DEFAULT;
constexpr LanePassableInfo_LaneAccessibility LanePassableInfo::RECOMMEND;
constexpr LanePassableInfo_LaneAccessibility LanePassableInfo::NOT_RECOMMEND;
constexpr LanePassableInfo_LaneAccessibility LanePassableInfo::LaneAccessibility_MIN;
constexpr LanePassableInfo_LaneAccessibility LanePassableInfo::LaneAccessibility_MAX;
constexpr int LanePassableInfo::LaneAccessibility_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)

// ===================================================================

class LaneBoundaryType::_Internal {
 public:
};

LaneBoundaryType::LaneBoundaryType(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  types_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.LaneBoundaryType)
}
LaneBoundaryType::LaneBoundaryType(const LaneBoundaryType& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      types_(from.types_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  s_ = from.s_;
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.LaneBoundaryType)
}

void LaneBoundaryType::SharedCtor() {
  s_ = 0;
}

LaneBoundaryType::~LaneBoundaryType() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.LaneBoundaryType)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void LaneBoundaryType::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void LaneBoundaryType::ArenaDtor(void* object) {
  LaneBoundaryType* _this = reinterpret_cast< LaneBoundaryType* >(object);
  (void)_this;
}
void LaneBoundaryType::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneBoundaryType::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const LaneBoundaryType& LaneBoundaryType::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_LaneBoundaryType_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void LaneBoundaryType::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.LaneBoundaryType)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  types_.Clear();
  s_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneBoundaryType::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double s = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          s_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneBoundaryType.Type types = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_types(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_types(static_cast<::gwm::hdmap::LaneBoundaryType_Type>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LaneBoundaryType::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.LaneBoundaryType)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double s = 1;
  if (!(this->s() <= 0 && this->s() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_s(), target);
  }

  // repeated .gwm.hdmap.LaneBoundaryType.Type types = 2;
  {
    int byte_size = _types_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          2, types_, byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.LaneBoundaryType)
  return target;
}

size_t LaneBoundaryType::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.LaneBoundaryType)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.hdmap.LaneBoundaryType.Type types = 2;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_types_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_types(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _types_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // double s = 1;
  if (!(this->s() <= 0 && this->s() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LaneBoundaryType::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.LaneBoundaryType)
  GOOGLE_DCHECK_NE(&from, this);
  const LaneBoundaryType* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<LaneBoundaryType>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.LaneBoundaryType)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.LaneBoundaryType)
    MergeFrom(*source);
  }
}

void LaneBoundaryType::MergeFrom(const LaneBoundaryType& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.LaneBoundaryType)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  types_.MergeFrom(from.types_);
  if (!(from.s() <= 0 && from.s() >= 0)) {
    _internal_set_s(from._internal_s());
  }
}

void LaneBoundaryType::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.LaneBoundaryType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LaneBoundaryType::CopyFrom(const LaneBoundaryType& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.LaneBoundaryType)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneBoundaryType::IsInitialized() const {
  return true;
}

void LaneBoundaryType::InternalSwap(LaneBoundaryType* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  types_.InternalSwap(&other->types_);
  swap(s_, other->s_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneBoundaryType::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class LaneBoundary::_Internal {
 public:
  static const ::gwm::common::Polyline& boundary(const LaneBoundary* msg);
};

const ::gwm::common::Polyline&
LaneBoundary::_Internal::boundary(const LaneBoundary* msg) {
  return *msg->boundary_;
}
void LaneBoundary::clear_boundary() {
  if (GetArena() == nullptr && boundary_ != nullptr) {
    delete boundary_;
  }
  boundary_ = nullptr;
}
LaneBoundary::LaneBoundary(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  boundary_type_(arena),
  layers_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.LaneBoundary)
}
LaneBoundary::LaneBoundary(const LaneBoundary& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      boundary_type_(from.boundary_type_),
      layers_(from.layers_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_boundary()) {
    boundary_ = new ::gwm::common::Polyline(*from.boundary_);
  } else {
    boundary_ = nullptr;
  }
  ::memcpy(&length_, &from.length_,
    static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&length_)) + sizeof(type_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.LaneBoundary)
}

void LaneBoundary::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_LaneBoundary_hd_5fmap_5flane_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&boundary_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&boundary_)) + sizeof(type_));
}

LaneBoundary::~LaneBoundary() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.LaneBoundary)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void LaneBoundary::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete boundary_;
}

void LaneBoundary::ArenaDtor(void* object) {
  LaneBoundary* _this = reinterpret_cast< LaneBoundary* >(object);
  (void)_this;
}
void LaneBoundary::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneBoundary::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const LaneBoundary& LaneBoundary::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_LaneBoundary_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void LaneBoundary::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.LaneBoundary)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  boundary_type_.Clear();
  layers_.Clear();
  if (GetArena() == nullptr && boundary_ != nullptr) {
    delete boundary_;
  }
  boundary_ = nullptr;
  ::memset(&length_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&length_)) + sizeof(type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneBoundary::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double length = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          length_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // bool virtual = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          virtual__ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneBoundaryType boundary_type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_boundary_type(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      // int32 id = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.common.Polyline boundary = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_boundary(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.LaneBoundary.Crossable crossable = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_crossable(static_cast<::gwm::hdmap::LaneBoundary_Crossable>(val));
        } else goto handle_unusual;
        continue;
      // double cost = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 65)) {
          cost_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // repeated int32 layers = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_layers(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72) {
          _internal_add_layers(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.LaneBoundaryType.Type type = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::gwm::hdmap::LaneBoundaryType_Type>(val));
        } else goto handle_unusual;
        continue;
      // bool has_left_deceleration_marking = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          has_left_deceleration_marking_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool has_right_deceleration_marking = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          has_right_deceleration_marking_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LaneBoundary::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.LaneBoundary)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double length = 2;
  if (!(this->length() <= 0 && this->length() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_length(), target);
  }

  // bool virtual = 3;
  if (this->virtual_() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_virtual_(), target);
  }

  // repeated .gwm.hdmap.LaneBoundaryType boundary_type = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_boundary_type_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_boundary_type(i), target, stream);
  }

  // int32 id = 5;
  if (this->id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_id(), target);
  }

  // .gwm.common.Polyline boundary = 6;
  if (this->has_boundary()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::boundary(this), target, stream);
  }

  // .gwm.hdmap.LaneBoundary.Crossable crossable = 7;
  if (this->crossable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_crossable(), target);
  }

  // double cost = 8;
  if (!(this->cost() <= 0 && this->cost() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(8, this->_internal_cost(), target);
  }

  // repeated int32 layers = 9;
  {
    int byte_size = _layers_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          9, _internal_layers(), byte_size, target);
    }
  }

  // .gwm.hdmap.LaneBoundaryType.Type type = 10;
  if (this->type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      10, this->_internal_type(), target);
  }

  // bool has_left_deceleration_marking = 12;
  if (this->has_left_deceleration_marking() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(12, this->_internal_has_left_deceleration_marking(), target);
  }

  // bool has_right_deceleration_marking = 13;
  if (this->has_right_deceleration_marking() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(13, this->_internal_has_right_deceleration_marking(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.LaneBoundary)
  return target;
}

size_t LaneBoundary::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.LaneBoundary)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.hdmap.LaneBoundaryType boundary_type = 4;
  total_size += 1UL * this->_internal_boundary_type_size();
  for (const auto& msg : this->boundary_type_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated int32 layers = 9;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->layers_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _layers_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // .gwm.common.Polyline boundary = 6;
  if (this->has_boundary()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *boundary_);
  }

  // double length = 2;
  if (!(this->length() <= 0 && this->length() >= 0)) {
    total_size += 1 + 8;
  }

  // int32 id = 5;
  if (this->id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_id());
  }

  // .gwm.hdmap.LaneBoundary.Crossable crossable = 7;
  if (this->crossable() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_crossable());
  }

  // double cost = 8;
  if (!(this->cost() <= 0 && this->cost() >= 0)) {
    total_size += 1 + 8;
  }

  // bool virtual = 3;
  if (this->virtual_() != 0) {
    total_size += 1 + 1;
  }

  // bool has_left_deceleration_marking = 12;
  if (this->has_left_deceleration_marking() != 0) {
    total_size += 1 + 1;
  }

  // bool has_right_deceleration_marking = 13;
  if (this->has_right_deceleration_marking() != 0) {
    total_size += 1 + 1;
  }

  // .gwm.hdmap.LaneBoundaryType.Type type = 10;
  if (this->type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LaneBoundary::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.LaneBoundary)
  GOOGLE_DCHECK_NE(&from, this);
  const LaneBoundary* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<LaneBoundary>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.LaneBoundary)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.LaneBoundary)
    MergeFrom(*source);
  }
}

void LaneBoundary::MergeFrom(const LaneBoundary& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.LaneBoundary)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  boundary_type_.MergeFrom(from.boundary_type_);
  layers_.MergeFrom(from.layers_);
  if (from.has_boundary()) {
    _internal_mutable_boundary()->::gwm::common::Polyline::MergeFrom(from._internal_boundary());
  }
  if (!(from.length() <= 0 && from.length() >= 0)) {
    _internal_set_length(from._internal_length());
  }
  if (from.id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from.crossable() != 0) {
    _internal_set_crossable(from._internal_crossable());
  }
  if (!(from.cost() <= 0 && from.cost() >= 0)) {
    _internal_set_cost(from._internal_cost());
  }
  if (from.virtual_() != 0) {
    _internal_set_virtual_(from._internal_virtual_());
  }
  if (from.has_left_deceleration_marking() != 0) {
    _internal_set_has_left_deceleration_marking(from._internal_has_left_deceleration_marking());
  }
  if (from.has_right_deceleration_marking() != 0) {
    _internal_set_has_right_deceleration_marking(from._internal_has_right_deceleration_marking());
  }
  if (from.type() != 0) {
    _internal_set_type(from._internal_type());
  }
}

void LaneBoundary::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.LaneBoundary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LaneBoundary::CopyFrom(const LaneBoundary& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.LaneBoundary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneBoundary::IsInitialized() const {
  return true;
}

void LaneBoundary::InternalSwap(LaneBoundary* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  boundary_type_.InternalSwap(&other->boundary_type_);
  layers_.InternalSwap(&other->layers_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneBoundary, type_)
      + sizeof(LaneBoundary::type_)
      - PROTOBUF_FIELD_OFFSET(LaneBoundary, boundary_)>(
          reinterpret_cast<char*>(&boundary_),
          reinterpret_cast<char*>(&other->boundary_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneBoundary::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class LaneSampleAssociation::_Internal {
 public:
};

LaneSampleAssociation::LaneSampleAssociation(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.LaneSampleAssociation)
}
LaneSampleAssociation::LaneSampleAssociation(const LaneSampleAssociation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&s_, &from.s_,
    static_cast<size_t>(reinterpret_cast<char*>(&width_) -
    reinterpret_cast<char*>(&s_)) + sizeof(width_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.LaneSampleAssociation)
}

void LaneSampleAssociation::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&s_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&width_) -
      reinterpret_cast<char*>(&s_)) + sizeof(width_));
}

LaneSampleAssociation::~LaneSampleAssociation() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.LaneSampleAssociation)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void LaneSampleAssociation::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void LaneSampleAssociation::ArenaDtor(void* object) {
  LaneSampleAssociation* _this = reinterpret_cast< LaneSampleAssociation* >(object);
  (void)_this;
}
void LaneSampleAssociation::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneSampleAssociation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const LaneSampleAssociation& LaneSampleAssociation::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_LaneSampleAssociation_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void LaneSampleAssociation::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.LaneSampleAssociation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&s_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&width_) -
      reinterpret_cast<char*>(&s_)) + sizeof(width_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneSampleAssociation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double s = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          s_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double width = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LaneSampleAssociation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.LaneSampleAssociation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double s = 1;
  if (!(this->s() <= 0 && this->s() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_s(), target);
  }

  // double width = 2;
  if (!(this->width() <= 0 && this->width() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_width(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.LaneSampleAssociation)
  return target;
}

size_t LaneSampleAssociation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.LaneSampleAssociation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double s = 1;
  if (!(this->s() <= 0 && this->s() >= 0)) {
    total_size += 1 + 8;
  }

  // double width = 2;
  if (!(this->width() <= 0 && this->width() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LaneSampleAssociation::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.LaneSampleAssociation)
  GOOGLE_DCHECK_NE(&from, this);
  const LaneSampleAssociation* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<LaneSampleAssociation>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.LaneSampleAssociation)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.LaneSampleAssociation)
    MergeFrom(*source);
  }
}

void LaneSampleAssociation::MergeFrom(const LaneSampleAssociation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.LaneSampleAssociation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.s() <= 0 && from.s() >= 0)) {
    _internal_set_s(from._internal_s());
  }
  if (!(from.width() <= 0 && from.width() >= 0)) {
    _internal_set_width(from._internal_width());
  }
}

void LaneSampleAssociation::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.LaneSampleAssociation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LaneSampleAssociation::CopyFrom(const LaneSampleAssociation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.LaneSampleAssociation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneSampleAssociation::IsInitialized() const {
  return true;
}

void LaneSampleAssociation::InternalSwap(LaneSampleAssociation* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneSampleAssociation, width_)
      + sizeof(LaneSampleAssociation::width_)
      - PROTOBUF_FIELD_OFFSET(LaneSampleAssociation, s_)>(
          reinterpret_cast<char*>(&s_),
          reinterpret_cast<char*>(&other->s_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneSampleAssociation::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Entrance::_Internal {
 public:
  static const ::gwm::common::Point3D& location(const Entrance* msg);
};

const ::gwm::common::Point3D&
Entrance::_Internal::location(const Entrance* msg) {
  return *msg->location_;
}
void Entrance::clear_location() {
  if (GetArena() == nullptr && location_ != nullptr) {
    delete location_;
  }
  location_ = nullptr;
}
Entrance::Entrance(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  layers_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.Entrance)
}
Entrance::Entrance(const Entrance& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      layers_(from.layers_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_location()) {
    location_ = new ::gwm::common::Point3D(*from.location_);
  } else {
    location_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&lane_id_) -
    reinterpret_cast<char*>(&id_)) + sizeof(lane_id_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.Entrance)
}

void Entrance::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Entrance_hd_5fmap_5flane_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&location_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&lane_id_) -
      reinterpret_cast<char*>(&location_)) + sizeof(lane_id_));
}

Entrance::~Entrance() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.Entrance)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Entrance::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete location_;
}

void Entrance::ArenaDtor(void* object) {
  Entrance* _this = reinterpret_cast< Entrance* >(object);
  (void)_this;
}
void Entrance::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Entrance::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Entrance& Entrance::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Entrance_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void Entrance::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.Entrance)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  layers_.Clear();
  if (GetArena() == nullptr && location_ != nullptr) {
    delete location_;
  }
  location_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&lane_id_) -
      reinterpret_cast<char*>(&id_)) + sizeof(lane_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Entrance::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 lane_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.common.Point3D location = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_location(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 layers = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_layers(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32) {
          _internal_add_layers(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Entrance::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.Entrance)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // int32 lane_id = 2;
  if (this->lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_lane_id(), target);
  }

  // .gwm.common.Point3D location = 3;
  if (this->has_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::location(this), target, stream);
  }

  // repeated int32 layers = 4;
  {
    int byte_size = _layers_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          4, _internal_layers(), byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.Entrance)
  return target;
}

size_t Entrance::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.Entrance)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 layers = 4;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->layers_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _layers_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // .gwm.common.Point3D location = 3;
  if (this->has_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *location_);
  }

  // int32 id = 1;
  if (this->id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_id());
  }

  // int32 lane_id = 2;
  if (this->lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_lane_id());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Entrance::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.Entrance)
  GOOGLE_DCHECK_NE(&from, this);
  const Entrance* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Entrance>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.Entrance)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.Entrance)
    MergeFrom(*source);
  }
}

void Entrance::MergeFrom(const Entrance& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.Entrance)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  layers_.MergeFrom(from.layers_);
  if (from.has_location()) {
    _internal_mutable_location()->::gwm::common::Point3D::MergeFrom(from._internal_location());
  }
  if (from.id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (from.lane_id() != 0) {
    _internal_set_lane_id(from._internal_lane_id());
  }
}

void Entrance::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.Entrance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Entrance::CopyFrom(const Entrance& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.Entrance)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Entrance::IsInitialized() const {
  return true;
}

void Entrance::InternalSwap(Entrance* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  layers_.InternalSwap(&other->layers_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Entrance, lane_id_)
      + sizeof(Entrance::lane_id_)
      - PROTOBUF_FIELD_OFFSET(Entrance, location_)>(
          reinterpret_cast<char*>(&location_),
          reinterpret_cast<char*>(&other->location_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Entrance::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Rule::_Internal {
 public:
};

Rule::Rule(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.Rule)
}
Rule::Rule(const Rule& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_rule();
  switch (from.rule_case()) {
    case kSpeedLimit: {
      _internal_set_speed_limit(from._internal_speed_limit());
      break;
    }
    case kDisabled: {
      _internal_set_disabled(from._internal_disabled());
      break;
    }
    case RULE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.Rule)
}

void Rule::SharedCtor() {
  clear_has_rule();
}

Rule::~Rule() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.Rule)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Rule::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (has_rule()) {
    clear_rule();
  }
}

void Rule::ArenaDtor(void* object) {
  Rule* _this = reinterpret_cast< Rule* >(object);
  (void)_this;
}
void Rule::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Rule::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Rule& Rule::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Rule_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void Rule::clear_rule() {
// @@protoc_insertion_point(one_of_clear_start:gwm.hdmap.Rule)
  switch (rule_case()) {
    case kSpeedLimit: {
      // No need to clear
      break;
    }
    case kDisabled: {
      // No need to clear
      break;
    }
    case RULE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = RULE_NOT_SET;
}


void Rule::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.Rule)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_rule();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Rule::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // float speed_limit = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 13)) {
          _internal_set_speed_limit(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // bool disabled = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _internal_set_disabled(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Rule::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.Rule)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // float speed_limit = 1;
  if (_internal_has_speed_limit()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_speed_limit(), target);
  }

  // bool disabled = 2;
  if (_internal_has_disabled()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_disabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.Rule)
  return target;
}

size_t Rule::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.Rule)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (rule_case()) {
    // float speed_limit = 1;
    case kSpeedLimit: {
      total_size += 1 + 4;
      break;
    }
    // bool disabled = 2;
    case kDisabled: {
      total_size += 1 + 1;
      break;
    }
    case RULE_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Rule::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.Rule)
  GOOGLE_DCHECK_NE(&from, this);
  const Rule* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Rule>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.Rule)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.Rule)
    MergeFrom(*source);
  }
}

void Rule::MergeFrom(const Rule& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.Rule)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.rule_case()) {
    case kSpeedLimit: {
      _internal_set_speed_limit(from._internal_speed_limit());
      break;
    }
    case kDisabled: {
      _internal_set_disabled(from._internal_disabled());
      break;
    }
    case RULE_NOT_SET: {
      break;
    }
  }
}

void Rule::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.Rule)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Rule::CopyFrom(const Rule& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.Rule)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Rule::IsInitialized() const {
  return true;
}

void Rule::InternalSwap(Rule* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(rule_, other->rule_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Rule::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Trigger::_Internal {
 public:
};

Trigger::Trigger(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.Trigger)
}
Trigger::Trigger(const Trigger& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_trigger();
  switch (from.trigger_case()) {
    case kAlways: {
      _internal_set_always(from._internal_always());
      break;
    }
    case TRIGGER_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.Trigger)
}

void Trigger::SharedCtor() {
  clear_has_trigger();
}

Trigger::~Trigger() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.Trigger)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Trigger::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (has_trigger()) {
    clear_trigger();
  }
}

void Trigger::ArenaDtor(void* object) {
  Trigger* _this = reinterpret_cast< Trigger* >(object);
  (void)_this;
}
void Trigger::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Trigger::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Trigger& Trigger::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Trigger_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void Trigger::clear_trigger() {
// @@protoc_insertion_point(one_of_clear_start:gwm.hdmap.Trigger)
  switch (trigger_case()) {
    case kAlways: {
      // No need to clear
      break;
    }
    case TRIGGER_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = TRIGGER_NOT_SET;
}


void Trigger::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.Trigger)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_trigger();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Trigger::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // bool always = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          _internal_set_always(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Trigger::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.Trigger)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool always = 1;
  if (_internal_has_always()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_always(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.Trigger)
  return target;
}

size_t Trigger::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.Trigger)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (trigger_case()) {
    // bool always = 1;
    case kAlways: {
      total_size += 1 + 1;
      break;
    }
    case TRIGGER_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Trigger::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.Trigger)
  GOOGLE_DCHECK_NE(&from, this);
  const Trigger* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Trigger>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.Trigger)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.Trigger)
    MergeFrom(*source);
  }
}

void Trigger::MergeFrom(const Trigger& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.Trigger)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.trigger_case()) {
    case kAlways: {
      _internal_set_always(from._internal_always());
      break;
    }
    case TRIGGER_NOT_SET: {
      break;
    }
  }
}

void Trigger::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.Trigger)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Trigger::CopyFrom(const Trigger& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.Trigger)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Trigger::IsInitialized() const {
  return true;
}

void Trigger::InternalSwap(Trigger* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(trigger_, other->trigger_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata Trigger::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class LaneRule::_Internal {
 public:
  static const ::gwm::hdmap::Trigger& trigger(const LaneRule* msg);
  static const ::gwm::hdmap::Rule& rule(const LaneRule* msg);
};

const ::gwm::hdmap::Trigger&
LaneRule::_Internal::trigger(const LaneRule* msg) {
  return *msg->trigger_;
}
const ::gwm::hdmap::Rule&
LaneRule::_Internal::rule(const LaneRule* msg) {
  return *msg->rule_;
}
LaneRule::LaneRule(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.LaneRule)
}
LaneRule::LaneRule(const LaneRule& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_trigger()) {
    trigger_ = new ::gwm::hdmap::Trigger(*from.trigger_);
  } else {
    trigger_ = nullptr;
  }
  if (from._internal_has_rule()) {
    rule_ = new ::gwm::hdmap::Rule(*from.rule_);
  } else {
    rule_ = nullptr;
  }
  vehicle_type_ = from.vehicle_type_;
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.LaneRule)
}

void LaneRule::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_LaneRule_hd_5fmap_5flane_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&trigger_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&vehicle_type_) -
      reinterpret_cast<char*>(&trigger_)) + sizeof(vehicle_type_));
}

LaneRule::~LaneRule() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.LaneRule)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void LaneRule::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete trigger_;
  if (this != internal_default_instance()) delete rule_;
}

void LaneRule::ArenaDtor(void* object) {
  LaneRule* _this = reinterpret_cast< LaneRule* >(object);
  (void)_this;
}
void LaneRule::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneRule::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const LaneRule& LaneRule::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_LaneRule_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void LaneRule::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.LaneRule)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArena() == nullptr && trigger_ != nullptr) {
    delete trigger_;
  }
  trigger_ = nullptr;
  if (GetArena() == nullptr && rule_ != nullptr) {
    delete rule_;
  }
  rule_ = nullptr;
  vehicle_type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneRule::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.hdmap.LaneRule.VehicleType vehicle_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_vehicle_type(static_cast<::gwm::hdmap::LaneRule_VehicleType>(val));
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Trigger trigger = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_trigger(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Rule rule = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_rule(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LaneRule::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.LaneRule)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.hdmap.LaneRule.VehicleType vehicle_type = 1;
  if (this->vehicle_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_vehicle_type(), target);
  }

  // .gwm.hdmap.Trigger trigger = 2;
  if (this->has_trigger()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::trigger(this), target, stream);
  }

  // .gwm.hdmap.Rule rule = 3;
  if (this->has_rule()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::rule(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.LaneRule)
  return target;
}

size_t LaneRule::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.LaneRule)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.hdmap.Trigger trigger = 2;
  if (this->has_trigger()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *trigger_);
  }

  // .gwm.hdmap.Rule rule = 3;
  if (this->has_rule()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rule_);
  }

  // .gwm.hdmap.LaneRule.VehicleType vehicle_type = 1;
  if (this->vehicle_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_vehicle_type());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LaneRule::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.LaneRule)
  GOOGLE_DCHECK_NE(&from, this);
  const LaneRule* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<LaneRule>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.LaneRule)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.LaneRule)
    MergeFrom(*source);
  }
}

void LaneRule::MergeFrom(const LaneRule& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.LaneRule)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_trigger()) {
    _internal_mutable_trigger()->::gwm::hdmap::Trigger::MergeFrom(from._internal_trigger());
  }
  if (from.has_rule()) {
    _internal_mutable_rule()->::gwm::hdmap::Rule::MergeFrom(from._internal_rule());
  }
  if (from.vehicle_type() != 0) {
    _internal_set_vehicle_type(from._internal_vehicle_type());
  }
}

void LaneRule::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.LaneRule)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LaneRule::CopyFrom(const LaneRule& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.LaneRule)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneRule::IsInitialized() const {
  return true;
}

void LaneRule::InternalSwap(LaneRule* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneRule, vehicle_type_)
      + sizeof(LaneRule::vehicle_type_)
      - PROTOBUF_FIELD_OFFSET(LaneRule, trigger_)>(
          reinterpret_cast<char*>(&trigger_),
          reinterpret_cast<char*>(&other->trigger_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneRule::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class MergeSplit_Merge::_Internal {
 public:
};

MergeSplit_Merge::MergeSplit_Merge(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.MergeSplit.Merge)
}
MergeSplit_Merge::MergeSplit_Merge(const MergeSplit_Merge& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&direction_, &from.direction_,
    static_cast<size_t>(reinterpret_cast<char*>(&to_lane_id_) -
    reinterpret_cast<char*>(&direction_)) + sizeof(to_lane_id_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.MergeSplit.Merge)
}

void MergeSplit_Merge::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&direction_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&to_lane_id_) -
      reinterpret_cast<char*>(&direction_)) + sizeof(to_lane_id_));
}

MergeSplit_Merge::~MergeSplit_Merge() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.MergeSplit.Merge)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void MergeSplit_Merge::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void MergeSplit_Merge::ArenaDtor(void* object) {
  MergeSplit_Merge* _this = reinterpret_cast< MergeSplit_Merge* >(object);
  (void)_this;
}
void MergeSplit_Merge::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MergeSplit_Merge::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const MergeSplit_Merge& MergeSplit_Merge::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_MergeSplit_Merge_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void MergeSplit_Merge::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.MergeSplit.Merge)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&direction_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&to_lane_id_) -
      reinterpret_cast<char*>(&direction_)) + sizeof(to_lane_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MergeSplit_Merge::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.hdmap.MergeSplit.Direction direction = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_direction(static_cast<::gwm::hdmap::MergeSplit_Direction>(val));
        } else goto handle_unusual;
        continue;
      // int32 to_lane_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          to_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MergeSplit_Merge::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.MergeSplit.Merge)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.hdmap.MergeSplit.Direction direction = 1;
  if (this->direction() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_direction(), target);
  }

  // int32 to_lane_id = 2;
  if (this->to_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_to_lane_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.MergeSplit.Merge)
  return target;
}

size_t MergeSplit_Merge::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.MergeSplit.Merge)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.hdmap.MergeSplit.Direction direction = 1;
  if (this->direction() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_direction());
  }

  // int32 to_lane_id = 2;
  if (this->to_lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_to_lane_id());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MergeSplit_Merge::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.MergeSplit.Merge)
  GOOGLE_DCHECK_NE(&from, this);
  const MergeSplit_Merge* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<MergeSplit_Merge>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.MergeSplit.Merge)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.MergeSplit.Merge)
    MergeFrom(*source);
  }
}

void MergeSplit_Merge::MergeFrom(const MergeSplit_Merge& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.MergeSplit.Merge)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.direction() != 0) {
    _internal_set_direction(from._internal_direction());
  }
  if (from.to_lane_id() != 0) {
    _internal_set_to_lane_id(from._internal_to_lane_id());
  }
}

void MergeSplit_Merge::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.MergeSplit.Merge)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MergeSplit_Merge::CopyFrom(const MergeSplit_Merge& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.MergeSplit.Merge)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MergeSplit_Merge::IsInitialized() const {
  return true;
}

void MergeSplit_Merge::InternalSwap(MergeSplit_Merge* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MergeSplit_Merge, to_lane_id_)
      + sizeof(MergeSplit_Merge::to_lane_id_)
      - PROTOBUF_FIELD_OFFSET(MergeSplit_Merge, direction_)>(
          reinterpret_cast<char*>(&direction_),
          reinterpret_cast<char*>(&other->direction_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MergeSplit_Merge::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class MergeSplit_Split::_Internal {
 public:
};

MergeSplit_Split::MergeSplit_Split(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.MergeSplit.Split)
}
MergeSplit_Split::MergeSplit_Split(const MergeSplit_Split& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&direction_, &from.direction_,
    static_cast<size_t>(reinterpret_cast<char*>(&from_lane_id_) -
    reinterpret_cast<char*>(&direction_)) + sizeof(from_lane_id_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.MergeSplit.Split)
}

void MergeSplit_Split::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&direction_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&from_lane_id_) -
      reinterpret_cast<char*>(&direction_)) + sizeof(from_lane_id_));
}

MergeSplit_Split::~MergeSplit_Split() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.MergeSplit.Split)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void MergeSplit_Split::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void MergeSplit_Split::ArenaDtor(void* object) {
  MergeSplit_Split* _this = reinterpret_cast< MergeSplit_Split* >(object);
  (void)_this;
}
void MergeSplit_Split::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MergeSplit_Split::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const MergeSplit_Split& MergeSplit_Split::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_MergeSplit_Split_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void MergeSplit_Split::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.MergeSplit.Split)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&direction_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&from_lane_id_) -
      reinterpret_cast<char*>(&direction_)) + sizeof(from_lane_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MergeSplit_Split::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.hdmap.MergeSplit.Direction direction = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_direction(static_cast<::gwm::hdmap::MergeSplit_Direction>(val));
        } else goto handle_unusual;
        continue;
      // int32 from_lane_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          from_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MergeSplit_Split::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.MergeSplit.Split)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.hdmap.MergeSplit.Direction direction = 1;
  if (this->direction() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_direction(), target);
  }

  // int32 from_lane_id = 2;
  if (this->from_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_from_lane_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.MergeSplit.Split)
  return target;
}

size_t MergeSplit_Split::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.MergeSplit.Split)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.hdmap.MergeSplit.Direction direction = 1;
  if (this->direction() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_direction());
  }

  // int32 from_lane_id = 2;
  if (this->from_lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_from_lane_id());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MergeSplit_Split::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.MergeSplit.Split)
  GOOGLE_DCHECK_NE(&from, this);
  const MergeSplit_Split* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<MergeSplit_Split>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.MergeSplit.Split)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.MergeSplit.Split)
    MergeFrom(*source);
  }
}

void MergeSplit_Split::MergeFrom(const MergeSplit_Split& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.MergeSplit.Split)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.direction() != 0) {
    _internal_set_direction(from._internal_direction());
  }
  if (from.from_lane_id() != 0) {
    _internal_set_from_lane_id(from._internal_from_lane_id());
  }
}

void MergeSplit_Split::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.MergeSplit.Split)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MergeSplit_Split::CopyFrom(const MergeSplit_Split& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.MergeSplit.Split)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MergeSplit_Split::IsInitialized() const {
  return true;
}

void MergeSplit_Split::InternalSwap(MergeSplit_Split* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MergeSplit_Split, from_lane_id_)
      + sizeof(MergeSplit_Split::from_lane_id_)
      - PROTOBUF_FIELD_OFFSET(MergeSplit_Split, direction_)>(
          reinterpret_cast<char*>(&direction_),
          reinterpret_cast<char*>(&other->direction_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MergeSplit_Split::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class MergeSplit::_Internal {
 public:
  static const ::gwm::hdmap::MergeSplit_Merge& merge(const MergeSplit* msg);
  static const ::gwm::hdmap::MergeSplit_Split& split(const MergeSplit* msg);
};

const ::gwm::hdmap::MergeSplit_Merge&
MergeSplit::_Internal::merge(const MergeSplit* msg) {
  return *msg->type_.merge_;
}
const ::gwm::hdmap::MergeSplit_Split&
MergeSplit::_Internal::split(const MergeSplit* msg) {
  return *msg->type_.split_;
}
void MergeSplit::set_allocated_merge(::gwm::hdmap::MergeSplit_Merge* merge) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  clear_type();
  if (merge) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(merge);
    if (message_arena != submessage_arena) {
      merge = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, merge, submessage_arena);
    }
    set_has_merge();
    type_.merge_ = merge;
  }
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.MergeSplit.merge)
}
void MergeSplit::set_allocated_split(::gwm::hdmap::MergeSplit_Split* split) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  clear_type();
  if (split) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(split);
    if (message_arena != submessage_arena) {
      split = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, split, submessage_arena);
    }
    set_has_split();
    type_.split_ = split;
  }
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.MergeSplit.split)
}
MergeSplit::MergeSplit(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.MergeSplit)
}
MergeSplit::MergeSplit(const MergeSplit& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_type();
  switch (from.type_case()) {
    case kMerge: {
      _internal_mutable_merge()->::gwm::hdmap::MergeSplit_Merge::MergeFrom(from._internal_merge());
      break;
    }
    case kSplit: {
      _internal_mutable_split()->::gwm::hdmap::MergeSplit_Split::MergeFrom(from._internal_split());
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.MergeSplit)
}

void MergeSplit::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_MergeSplit_hd_5fmap_5flane_2eproto.base);
  clear_has_type();
}

MergeSplit::~MergeSplit() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.MergeSplit)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void MergeSplit::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (has_type()) {
    clear_type();
  }
}

void MergeSplit::ArenaDtor(void* object) {
  MergeSplit* _this = reinterpret_cast< MergeSplit* >(object);
  (void)_this;
}
void MergeSplit::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MergeSplit::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const MergeSplit& MergeSplit::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_MergeSplit_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void MergeSplit::clear_type() {
// @@protoc_insertion_point(one_of_clear_start:gwm.hdmap.MergeSplit)
  switch (type_case()) {
    case kMerge: {
      if (GetArena() == nullptr) {
        delete type_.merge_;
      }
      break;
    }
    case kSplit: {
      if (GetArena() == nullptr) {
        delete type_.split_;
      }
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = TYPE_NOT_SET;
}


void MergeSplit::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.MergeSplit)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_type();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MergeSplit::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.hdmap.MergeSplit.Merge merge = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_merge(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.MergeSplit.Split split = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_split(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MergeSplit::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.MergeSplit)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.hdmap.MergeSplit.Merge merge = 1;
  if (_internal_has_merge()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::merge(this), target, stream);
  }

  // .gwm.hdmap.MergeSplit.Split split = 2;
  if (_internal_has_split()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::split(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.MergeSplit)
  return target;
}

size_t MergeSplit::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.MergeSplit)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (type_case()) {
    // .gwm.hdmap.MergeSplit.Merge merge = 1;
    case kMerge: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_.merge_);
      break;
    }
    // .gwm.hdmap.MergeSplit.Split split = 2;
    case kSplit: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_.split_);
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MergeSplit::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.MergeSplit)
  GOOGLE_DCHECK_NE(&from, this);
  const MergeSplit* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<MergeSplit>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.MergeSplit)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.MergeSplit)
    MergeFrom(*source);
  }
}

void MergeSplit::MergeFrom(const MergeSplit& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.MergeSplit)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.type_case()) {
    case kMerge: {
      _internal_mutable_merge()->::gwm::hdmap::MergeSplit_Merge::MergeFrom(from._internal_merge());
      break;
    }
    case kSplit: {
      _internal_mutable_split()->::gwm::hdmap::MergeSplit_Split::MergeFrom(from._internal_split());
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
}

void MergeSplit::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.MergeSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MergeSplit::CopyFrom(const MergeSplit& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.MergeSplit)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MergeSplit::IsInitialized() const {
  return true;
}

void MergeSplit::InternalSwap(MergeSplit* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(type_, other->type_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata MergeSplit::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class NeighborMerge_Ids::_Internal {
 public:
};

NeighborMerge_Ids::NeighborMerge_Ids(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  ids_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.NeighborMerge.Ids)
}
NeighborMerge_Ids::NeighborMerge_Ids(const NeighborMerge_Ids& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      ids_(from.ids_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.NeighborMerge.Ids)
}

void NeighborMerge_Ids::SharedCtor() {
}

NeighborMerge_Ids::~NeighborMerge_Ids() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.NeighborMerge.Ids)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void NeighborMerge_Ids::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void NeighborMerge_Ids::ArenaDtor(void* object) {
  NeighborMerge_Ids* _this = reinterpret_cast< NeighborMerge_Ids* >(object);
  (void)_this;
}
void NeighborMerge_Ids::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void NeighborMerge_Ids::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const NeighborMerge_Ids& NeighborMerge_Ids::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_NeighborMerge_Ids_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void NeighborMerge_Ids::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.NeighborMerge.Ids)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ids_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* NeighborMerge_Ids::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated int32 ids = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8) {
          _internal_add_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* NeighborMerge_Ids::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.NeighborMerge.Ids)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated int32 ids = 1;
  {
    int byte_size = _ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          1, _internal_ids(), byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.NeighborMerge.Ids)
  return target;
}

size_t NeighborMerge_Ids::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.NeighborMerge.Ids)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 ids = 1;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->ids_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NeighborMerge_Ids::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.NeighborMerge.Ids)
  GOOGLE_DCHECK_NE(&from, this);
  const NeighborMerge_Ids* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<NeighborMerge_Ids>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.NeighborMerge.Ids)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.NeighborMerge.Ids)
    MergeFrom(*source);
  }
}

void NeighborMerge_Ids::MergeFrom(const NeighborMerge_Ids& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.NeighborMerge.Ids)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  ids_.MergeFrom(from.ids_);
}

void NeighborMerge_Ids::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.NeighborMerge.Ids)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NeighborMerge_Ids::CopyFrom(const NeighborMerge_Ids& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.NeighborMerge.Ids)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NeighborMerge_Ids::IsInitialized() const {
  return true;
}

void NeighborMerge_Ids::InternalSwap(NeighborMerge_Ids* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ids_.InternalSwap(&other->ids_);
}

::PROTOBUF_NAMESPACE_ID::Metadata NeighborMerge_Ids::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class NeighborMerge::_Internal {
 public:
  static const ::gwm::hdmap::NeighborMerge_Ids& merge_from_lane_ids(const NeighborMerge* msg);
};

const ::gwm::hdmap::NeighborMerge_Ids&
NeighborMerge::_Internal::merge_from_lane_ids(const NeighborMerge* msg) {
  return *msg->type_.merge_from_lane_ids_;
}
void NeighborMerge::set_allocated_merge_from_lane_ids(::gwm::hdmap::NeighborMerge_Ids* merge_from_lane_ids) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  clear_type();
  if (merge_from_lane_ids) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(merge_from_lane_ids);
    if (message_arena != submessage_arena) {
      merge_from_lane_ids = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, merge_from_lane_ids, submessage_arena);
    }
    set_has_merge_from_lane_ids();
    type_.merge_from_lane_ids_ = merge_from_lane_ids;
  }
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.NeighborMerge.merge_from_lane_ids)
}
NeighborMerge::NeighborMerge(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.NeighborMerge)
}
NeighborMerge::NeighborMerge(const NeighborMerge& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  successor_lane_id_ = from.successor_lane_id_;
  clear_has_type();
  switch (from.type_case()) {
    case kMergeFromLaneIds: {
      _internal_mutable_merge_from_lane_ids()->::gwm::hdmap::NeighborMerge_Ids::MergeFrom(from._internal_merge_from_lane_ids());
      break;
    }
    case kMergeToLaneId: {
      _internal_set_merge_to_lane_id(from._internal_merge_to_lane_id());
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.NeighborMerge)
}

void NeighborMerge::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_NeighborMerge_hd_5fmap_5flane_2eproto.base);
  successor_lane_id_ = 0;
  clear_has_type();
}

NeighborMerge::~NeighborMerge() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.NeighborMerge)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void NeighborMerge::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (has_type()) {
    clear_type();
  }
}

void NeighborMerge::ArenaDtor(void* object) {
  NeighborMerge* _this = reinterpret_cast< NeighborMerge* >(object);
  (void)_this;
}
void NeighborMerge::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void NeighborMerge::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const NeighborMerge& NeighborMerge::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_NeighborMerge_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void NeighborMerge::clear_type() {
// @@protoc_insertion_point(one_of_clear_start:gwm.hdmap.NeighborMerge)
  switch (type_case()) {
    case kMergeFromLaneIds: {
      if (GetArena() == nullptr) {
        delete type_.merge_from_lane_ids_;
      }
      break;
    }
    case kMergeToLaneId: {
      // No need to clear
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = TYPE_NOT_SET;
}


void NeighborMerge::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.NeighborMerge)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  successor_lane_id_ = 0;
  clear_type();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* NeighborMerge::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.hdmap.NeighborMerge.Ids merge_from_lane_ids = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_merge_from_lane_ids(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 merge_to_lane_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          _internal_set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 successor_lane_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          successor_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* NeighborMerge::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.NeighborMerge)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.hdmap.NeighborMerge.Ids merge_from_lane_ids = 1;
  if (_internal_has_merge_from_lane_ids()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::merge_from_lane_ids(this), target, stream);
  }

  // int32 merge_to_lane_id = 2;
  if (_internal_has_merge_to_lane_id()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_merge_to_lane_id(), target);
  }

  // int32 successor_lane_id = 3;
  if (this->successor_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_successor_lane_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.NeighborMerge)
  return target;
}

size_t NeighborMerge::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.NeighborMerge)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 successor_lane_id = 3;
  if (this->successor_lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_successor_lane_id());
  }

  switch (type_case()) {
    // .gwm.hdmap.NeighborMerge.Ids merge_from_lane_ids = 1;
    case kMergeFromLaneIds: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_.merge_from_lane_ids_);
      break;
    }
    // int32 merge_to_lane_id = 2;
    case kMergeToLaneId: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
          this->_internal_merge_to_lane_id());
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void NeighborMerge::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.NeighborMerge)
  GOOGLE_DCHECK_NE(&from, this);
  const NeighborMerge* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<NeighborMerge>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.NeighborMerge)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.NeighborMerge)
    MergeFrom(*source);
  }
}

void NeighborMerge::MergeFrom(const NeighborMerge& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.NeighborMerge)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.successor_lane_id() != 0) {
    _internal_set_successor_lane_id(from._internal_successor_lane_id());
  }
  switch (from.type_case()) {
    case kMergeFromLaneIds: {
      _internal_mutable_merge_from_lane_ids()->::gwm::hdmap::NeighborMerge_Ids::MergeFrom(from._internal_merge_from_lane_ids());
      break;
    }
    case kMergeToLaneId: {
      _internal_set_merge_to_lane_id(from._internal_merge_to_lane_id());
      break;
    }
    case TYPE_NOT_SET: {
      break;
    }
  }
}

void NeighborMerge::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.NeighborMerge)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void NeighborMerge::CopyFrom(const NeighborMerge& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.NeighborMerge)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NeighborMerge::IsInitialized() const {
  return true;
}

void NeighborMerge::InternalSwap(NeighborMerge* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  swap(successor_lane_id_, other->successor_lane_id_);
  swap(type_, other->type_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata NeighborMerge::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Lane::_Internal {
 public:
  static const ::gwm::hdmap::LaneBoundary& left_boundary(const Lane* msg);
  static const ::gwm::hdmap::LaneBoundary& right_boundary(const Lane* msg);
  static const ::gwm::common::Polyline& centerline(const Lane* msg);
};

const ::gwm::hdmap::LaneBoundary&
Lane::_Internal::left_boundary(const Lane* msg) {
  return *msg->left_boundary_;
}
const ::gwm::hdmap::LaneBoundary&
Lane::_Internal::right_boundary(const Lane* msg) {
  return *msg->right_boundary_;
}
const ::gwm::common::Polyline&
Lane::_Internal::centerline(const Lane* msg) {
  return *msg->centerline_;
}
void Lane::clear_centerline() {
  if (GetArena() == nullptr && centerline_ != nullptr) {
    delete centerline_;
  }
  centerline_ = nullptr;
}
Lane::Lane(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  overlap_id_(arena),
  predecessor_id_(arena),
  successor_id_(arena),
  left_sample_(arena),
  right_sample_(arena),
  left_road_sample_(arena),
  right_road_sample_(arena),
  self_reverse_lane_id_(arena),
  centerline_s_(arena),
  merge_from_lane_id_(arena),
  layers_(arena),
  road_name_(arena),
  lane_rules_(arena),
  left_boundary_plus_ids_(arena),
  right_boundary_plus_ids_(arena),
  merge_splits_(arena),
  road_section_ids_(arena),
  neighbor_merges_(arena),
  manually_set_predecessor_ids_(arena),
  manually_set_successor_ids_(arena),
  next_lane_ids_(arena),
  last_lane_ids_(arena),
  types_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.Lane)
}
Lane::Lane(const Lane& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      overlap_id_(from.overlap_id_),
      predecessor_id_(from.predecessor_id_),
      successor_id_(from.successor_id_),
      left_sample_(from.left_sample_),
      right_sample_(from.right_sample_),
      left_road_sample_(from.left_road_sample_),
      right_road_sample_(from.right_road_sample_),
      self_reverse_lane_id_(from.self_reverse_lane_id_),
      centerline_s_(from.centerline_s_),
      merge_from_lane_id_(from.merge_from_lane_id_),
      layers_(from.layers_),
      road_name_(from.road_name_),
      lane_rules_(from.lane_rules_),
      left_boundary_plus_ids_(from.left_boundary_plus_ids_),
      right_boundary_plus_ids_(from.right_boundary_plus_ids_),
      merge_splits_(from.merge_splits_),
      road_section_ids_(from.road_section_ids_),
      neighbor_merges_(from.neighbor_merges_),
      manually_set_predecessor_ids_(from.manually_set_predecessor_ids_),
      manually_set_successor_ids_(from.manually_set_successor_ids_),
      next_lane_ids_(from.next_lane_ids_),
      last_lane_ids_(from.last_lane_ids_),
      types_(from.types_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_left_boundary()) {
    left_boundary_ = new ::gwm::hdmap::LaneBoundary(*from.left_boundary_);
  } else {
    left_boundary_ = nullptr;
  }
  if (from._internal_has_right_boundary()) {
    right_boundary_ = new ::gwm::hdmap::LaneBoundary(*from.right_boundary_);
  } else {
    right_boundary_ = nullptr;
  }
  if (from._internal_has_centerline()) {
    centerline_ = new ::gwm::common::Polyline(*from.centerline_);
  } else {
    centerline_ = nullptr;
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&slope_type_) -
    reinterpret_cast<char*>(&id_)) + sizeof(slope_type_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.Lane)
}

void Lane::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Lane_hd_5fmap_5flane_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&left_boundary_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&slope_type_) -
      reinterpret_cast<char*>(&left_boundary_)) + sizeof(slope_type_));
}

Lane::~Lane() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.Lane)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Lane::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete left_boundary_;
  if (this != internal_default_instance()) delete right_boundary_;
  if (this != internal_default_instance()) delete centerline_;
}

void Lane::ArenaDtor(void* object) {
  Lane* _this = reinterpret_cast< Lane* >(object);
  (void)_this;
}
void Lane::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Lane::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Lane& Lane::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Lane_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void Lane::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.Lane)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  overlap_id_.Clear();
  predecessor_id_.Clear();
  successor_id_.Clear();
  left_sample_.Clear();
  right_sample_.Clear();
  left_road_sample_.Clear();
  right_road_sample_.Clear();
  self_reverse_lane_id_.Clear();
  centerline_s_.Clear();
  merge_from_lane_id_.Clear();
  layers_.Clear();
  road_name_.Clear();
  lane_rules_.Clear();
  left_boundary_plus_ids_.Clear();
  right_boundary_plus_ids_.Clear();
  merge_splits_.Clear();
  road_section_ids_.Clear();
  neighbor_merges_.Clear();
  manually_set_predecessor_ids_.Clear();
  manually_set_successor_ids_.Clear();
  next_lane_ids_.Clear();
  last_lane_ids_.Clear();
  types_.Clear();
  if (GetArena() == nullptr && left_boundary_ != nullptr) {
    delete left_boundary_;
  }
  left_boundary_ = nullptr;
  if (GetArena() == nullptr && right_boundary_ != nullptr) {
    delete right_boundary_;
  }
  right_boundary_ = nullptr;
  if (GetArena() == nullptr && centerline_ != nullptr) {
    delete centerline_;
  }
  centerline_ = nullptr;
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&slope_type_) -
      reinterpret_cast<char*>(&id_)) + sizeof(slope_type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Lane::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.LaneBoundary left_boundary = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_left_boundary(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.LaneBoundary right_boundary = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_right_boundary(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double length = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 41)) {
          length_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // float speed_limit = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          speed_limit_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // repeated int32 overlap_id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_overlap_id(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56) {
          _internal_add_overlap_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 predecessor_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_predecessor_id(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64) {
          _internal_add_predecessor_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 successor_id = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_successor_id(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 72) {
          _internal_add_successor_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 left_neighbor_forward_lane_id = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          left_neighbor_forward_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 right_neighbor_forward_lane_id = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 88)) {
          right_neighbor_forward_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Lane.LaneType type = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 96)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::gwm::hdmap::Lane_LaneType>(val));
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Lane.LaneTurn turn = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_turn(static_cast<::gwm::hdmap::Lane_LaneTurn>(val));
        } else goto handle_unusual;
        continue;
      // int32 left_neighbor_reverse_lane_id = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112)) {
          left_neighbor_reverse_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 right_neighbor_reverse_lane_id = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          right_neighbor_reverse_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 junction_id = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128)) {
          junction_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneSampleAssociation left_sample = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 138)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_left_sample(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<138>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneSampleAssociation right_sample = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_right_sample(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<146>(ptr));
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Lane.LaneDirection direction = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 152)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_direction(static_cast<::gwm::hdmap::Lane_LaneDirection>(val));
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneSampleAssociation left_road_sample = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_left_road_sample(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<162>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneSampleAssociation right_road_sample = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 170)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_right_road_sample(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<170>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated int32 self_reverse_lane_id = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 178)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_self_reverse_lane_id(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 176) {
          _internal_add_self_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.common.Polyline centerline = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 186)) {
          ptr = ctx->ParseMessage(_internal_mutable_centerline(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated float centerline_s = 24 [packed = true];
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 194)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_centerline_s(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 197) {
          _internal_add_centerline_s(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // int32 left_boundary_id = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 200)) {
          left_boundary_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 right_boundary_id = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 208)) {
          right_boundary_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Lane.BoundaryDirection boundary_direction = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 216)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_boundary_direction(static_cast<::gwm::hdmap::Lane_BoundaryDirection>(val));
        } else goto handle_unusual;
        continue;
      // double cost = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 233)) {
          cost_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Lane.MergeType merge = 30;
      case 30:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 240)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_merge(static_cast<::gwm::hdmap::Lane_MergeType>(val));
        } else goto handle_unusual;
        continue;
      // int32 merge_to_lane_id = 31;
      case 31:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 248)) {
          merge_to_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 merge_from_lane_id = 32;
      case 32:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 2)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_merge_from_lane_id(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 0) {
          _internal_add_merge_from_lane_id(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 layers = 33;
      case 33:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_layers(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8) {
          _internal_add_layers(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 bidi_copy_from_id = 34;
      case 34:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          bidi_copy_from_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 manually_set_left_neighbor_forward_lane_id = 35;
      case 35:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          manually_set_left_neighbor_forward_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 manually_set_right_neighbor_forward_lane_id = 36;
      case 36:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          manually_set_right_neighbor_forward_lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string road_name = 37;
      case 37:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_road_name();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "gwm.hdmap.Lane.road_name"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<298>(ptr));
        } else goto handle_unusual;
        continue;
      // bool is_disabled = 38;
      case 38:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          is_disabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool is_virtual = 39 [deprecated = true];
      case 39:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          is_virtual_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float truck_speed_limit = 40;
      case 40:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 69)) {
          truck_speed_limit_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float min_width = 41;
      case 41:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 77)) {
          min_width_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float max_curvature = 42;
      case 42:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 85)) {
          max_curvature_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float heading_diff = 43;
      case 43:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 93)) {
          heading_diff_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneRule lane_rules = 44;
      case 44:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_lane_rules(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<354>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated int32 left_boundary_plus_ids = 45;
      case 45:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_left_boundary_plus_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 104) {
          _internal_add_left_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 right_boundary_plus_ids = 46;
      case 46:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_right_boundary_plus_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 112) {
          _internal_add_right_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.MergeSplit merge_splits = 47;
      case 47:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 122)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_merge_splits(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<378>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated int32 road_section_ids = 48;
      case 48:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 130)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_road_section_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 128) {
          _internal_add_road_section_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.NeighborMerge neighbor_merges = 49;
      case 49:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 138)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_neighbor_merges(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<394>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated int32 manually_set_predecessor_ids = 50 [deprecated = true];
      case 50:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 146)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_manually_set_predecessor_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 144) {
          _internal_add_manually_set_predecessor_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 manually_set_successor_ids = 51 [deprecated = true];
      case 51:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 154)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_manually_set_successor_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 152) {
          _internal_add_manually_set_successor_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 next_lane_ids = 52;
      case 52:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 162)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_next_lane_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 160) {
          _internal_add_next_lane_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated int32 last_lane_ids = 53;
      case 53:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 170)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt32Parser(_internal_mutable_last_lane_ids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 168) {
          _internal_add_last_lane_ids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.Lane.SlopeType slope_type = 54;
      case 54:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 176)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_slope_type(static_cast<::gwm::hdmap::Lane_SlopeType>(val));
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.Lane.LaneType types = 55;
      case 55:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 186)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_types(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 184) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_types(static_cast<::gwm::hdmap::Lane_LaneType>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Lane::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.Lane)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 id = 1;
  if (this->id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // .gwm.hdmap.LaneBoundary left_boundary = 3;
  if (this->has_left_boundary()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::left_boundary(this), target, stream);
  }

  // .gwm.hdmap.LaneBoundary right_boundary = 4;
  if (this->has_right_boundary()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::right_boundary(this), target, stream);
  }

  // double length = 5;
  if (!(this->length() <= 0 && this->length() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_length(), target);
  }

  // float speed_limit = 6;
  if (!(this->speed_limit() <= 0 && this->speed_limit() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_speed_limit(), target);
  }

  // repeated int32 overlap_id = 7;
  {
    int byte_size = _overlap_id_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          7, _internal_overlap_id(), byte_size, target);
    }
  }

  // repeated int32 predecessor_id = 8;
  {
    int byte_size = _predecessor_id_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          8, _internal_predecessor_id(), byte_size, target);
    }
  }

  // repeated int32 successor_id = 9;
  {
    int byte_size = _successor_id_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          9, _internal_successor_id(), byte_size, target);
    }
  }

  // int32 left_neighbor_forward_lane_id = 10;
  if (this->left_neighbor_forward_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(10, this->_internal_left_neighbor_forward_lane_id(), target);
  }

  // int32 right_neighbor_forward_lane_id = 11;
  if (this->right_neighbor_forward_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(11, this->_internal_right_neighbor_forward_lane_id(), target);
  }

  // .gwm.hdmap.Lane.LaneType type = 12;
  if (this->type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      12, this->_internal_type(), target);
  }

  // .gwm.hdmap.Lane.LaneTurn turn = 13;
  if (this->turn() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      13, this->_internal_turn(), target);
  }

  // int32 left_neighbor_reverse_lane_id = 14;
  if (this->left_neighbor_reverse_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(14, this->_internal_left_neighbor_reverse_lane_id(), target);
  }

  // int32 right_neighbor_reverse_lane_id = 15;
  if (this->right_neighbor_reverse_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(15, this->_internal_right_neighbor_reverse_lane_id(), target);
  }

  // int32 junction_id = 16;
  if (this->junction_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(16, this->_internal_junction_id(), target);
  }

  // repeated .gwm.hdmap.LaneSampleAssociation left_sample = 17;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_left_sample_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(17, this->_internal_left_sample(i), target, stream);
  }

  // repeated .gwm.hdmap.LaneSampleAssociation right_sample = 18;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_right_sample_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(18, this->_internal_right_sample(i), target, stream);
  }

  // .gwm.hdmap.Lane.LaneDirection direction = 19;
  if (this->direction() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      19, this->_internal_direction(), target);
  }

  // repeated .gwm.hdmap.LaneSampleAssociation left_road_sample = 20;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_left_road_sample_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(20, this->_internal_left_road_sample(i), target, stream);
  }

  // repeated .gwm.hdmap.LaneSampleAssociation right_road_sample = 21;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_right_road_sample_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(21, this->_internal_right_road_sample(i), target, stream);
  }

  // repeated int32 self_reverse_lane_id = 22;
  {
    int byte_size = _self_reverse_lane_id_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          22, _internal_self_reverse_lane_id(), byte_size, target);
    }
  }

  // .gwm.common.Polyline centerline = 23;
  if (this->has_centerline()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        23, _Internal::centerline(this), target, stream);
  }

  // repeated float centerline_s = 24 [packed = true];
  if (this->_internal_centerline_s_size() > 0) {
    target = stream->WriteFixedPacked(24, _internal_centerline_s(), target);
  }

  // int32 left_boundary_id = 25;
  if (this->left_boundary_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(25, this->_internal_left_boundary_id(), target);
  }

  // int32 right_boundary_id = 26;
  if (this->right_boundary_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(26, this->_internal_right_boundary_id(), target);
  }

  // .gwm.hdmap.Lane.BoundaryDirection boundary_direction = 27;
  if (this->boundary_direction() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      27, this->_internal_boundary_direction(), target);
  }

  // double cost = 29;
  if (!(this->cost() <= 0 && this->cost() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(29, this->_internal_cost(), target);
  }

  // .gwm.hdmap.Lane.MergeType merge = 30;
  if (this->merge() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      30, this->_internal_merge(), target);
  }

  // int32 merge_to_lane_id = 31;
  if (this->merge_to_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(31, this->_internal_merge_to_lane_id(), target);
  }

  // repeated int32 merge_from_lane_id = 32;
  {
    int byte_size = _merge_from_lane_id_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          32, _internal_merge_from_lane_id(), byte_size, target);
    }
  }

  // repeated int32 layers = 33;
  {
    int byte_size = _layers_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          33, _internal_layers(), byte_size, target);
    }
  }

  // int32 bidi_copy_from_id = 34;
  if (this->bidi_copy_from_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(34, this->_internal_bidi_copy_from_id(), target);
  }

  // int32 manually_set_left_neighbor_forward_lane_id = 35;
  if (this->manually_set_left_neighbor_forward_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(35, this->_internal_manually_set_left_neighbor_forward_lane_id(), target);
  }

  // int32 manually_set_right_neighbor_forward_lane_id = 36;
  if (this->manually_set_right_neighbor_forward_lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(36, this->_internal_manually_set_right_neighbor_forward_lane_id(), target);
  }

  // repeated string road_name = 37;
  for (int i = 0, n = this->_internal_road_name_size(); i < n; i++) {
    const auto& s = this->_internal_road_name(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "gwm.hdmap.Lane.road_name");
    target = stream->WriteString(37, s, target);
  }

  // bool is_disabled = 38;
  if (this->is_disabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(38, this->_internal_is_disabled(), target);
  }

  // bool is_virtual = 39 [deprecated = true];
  if (this->is_virtual() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(39, this->_internal_is_virtual(), target);
  }

  // float truck_speed_limit = 40;
  if (!(this->truck_speed_limit() <= 0 && this->truck_speed_limit() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(40, this->_internal_truck_speed_limit(), target);
  }

  // float min_width = 41;
  if (!(this->min_width() <= 0 && this->min_width() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(41, this->_internal_min_width(), target);
  }

  // float max_curvature = 42;
  if (!(this->max_curvature() <= 0 && this->max_curvature() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(42, this->_internal_max_curvature(), target);
  }

  // float heading_diff = 43;
  if (!(this->heading_diff() <= 0 && this->heading_diff() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(43, this->_internal_heading_diff(), target);
  }

  // repeated .gwm.hdmap.LaneRule lane_rules = 44;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lane_rules_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(44, this->_internal_lane_rules(i), target, stream);
  }

  // repeated int32 left_boundary_plus_ids = 45;
  {
    int byte_size = _left_boundary_plus_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          45, _internal_left_boundary_plus_ids(), byte_size, target);
    }
  }

  // repeated int32 right_boundary_plus_ids = 46;
  {
    int byte_size = _right_boundary_plus_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          46, _internal_right_boundary_plus_ids(), byte_size, target);
    }
  }

  // repeated .gwm.hdmap.MergeSplit merge_splits = 47;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_merge_splits_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(47, this->_internal_merge_splits(i), target, stream);
  }

  // repeated int32 road_section_ids = 48;
  {
    int byte_size = _road_section_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          48, _internal_road_section_ids(), byte_size, target);
    }
  }

  // repeated .gwm.hdmap.NeighborMerge neighbor_merges = 49;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_neighbor_merges_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(49, this->_internal_neighbor_merges(i), target, stream);
  }

  // repeated int32 manually_set_predecessor_ids = 50 [deprecated = true];
  {
    int byte_size = _manually_set_predecessor_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          50, _internal_manually_set_predecessor_ids(), byte_size, target);
    }
  }

  // repeated int32 manually_set_successor_ids = 51 [deprecated = true];
  {
    int byte_size = _manually_set_successor_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          51, _internal_manually_set_successor_ids(), byte_size, target);
    }
  }

  // repeated int32 next_lane_ids = 52;
  {
    int byte_size = _next_lane_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          52, _internal_next_lane_ids(), byte_size, target);
    }
  }

  // repeated int32 last_lane_ids = 53;
  {
    int byte_size = _last_lane_ids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt32Packed(
          53, _internal_last_lane_ids(), byte_size, target);
    }
  }

  // .gwm.hdmap.Lane.SlopeType slope_type = 54;
  if (this->slope_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      54, this->_internal_slope_type(), target);
  }

  // repeated .gwm.hdmap.Lane.LaneType types = 55;
  {
    int byte_size = _types_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          55, types_, byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.Lane)
  return target;
}

size_t Lane::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.Lane)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int32 overlap_id = 7;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->overlap_id_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _overlap_id_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 predecessor_id = 8;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->predecessor_id_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _predecessor_id_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 successor_id = 9;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->successor_id_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _successor_id_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .gwm.hdmap.LaneSampleAssociation left_sample = 17;
  total_size += 2UL * this->_internal_left_sample_size();
  for (const auto& msg : this->left_sample_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .gwm.hdmap.LaneSampleAssociation right_sample = 18;
  total_size += 2UL * this->_internal_right_sample_size();
  for (const auto& msg : this->right_sample_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .gwm.hdmap.LaneSampleAssociation left_road_sample = 20;
  total_size += 2UL * this->_internal_left_road_sample_size();
  for (const auto& msg : this->left_road_sample_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .gwm.hdmap.LaneSampleAssociation right_road_sample = 21;
  total_size += 2UL * this->_internal_right_road_sample_size();
  for (const auto& msg : this->right_road_sample_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated int32 self_reverse_lane_id = 22;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->self_reverse_lane_id_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _self_reverse_lane_id_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated float centerline_s = 24 [packed = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_centerline_s_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _centerline_s_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 merge_from_lane_id = 32;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->merge_from_lane_id_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _merge_from_lane_id_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 layers = 33;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->layers_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _layers_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated string road_name = 37;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(road_name_.size());
  for (int i = 0, n = road_name_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      road_name_.Get(i));
  }

  // repeated .gwm.hdmap.LaneRule lane_rules = 44;
  total_size += 2UL * this->_internal_lane_rules_size();
  for (const auto& msg : this->lane_rules_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated int32 left_boundary_plus_ids = 45;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->left_boundary_plus_ids_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _left_boundary_plus_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 right_boundary_plus_ids = 46;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->right_boundary_plus_ids_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _right_boundary_plus_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .gwm.hdmap.MergeSplit merge_splits = 47;
  total_size += 2UL * this->_internal_merge_splits_size();
  for (const auto& msg : this->merge_splits_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated int32 road_section_ids = 48;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->road_section_ids_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _road_section_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .gwm.hdmap.NeighborMerge neighbor_merges = 49;
  total_size += 2UL * this->_internal_neighbor_merges_size();
  for (const auto& msg : this->neighbor_merges_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated int32 manually_set_predecessor_ids = 50 [deprecated = true];
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->manually_set_predecessor_ids_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _manually_set_predecessor_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 manually_set_successor_ids = 51 [deprecated = true];
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->manually_set_successor_ids_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _manually_set_successor_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 next_lane_ids = 52;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->next_lane_ids_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _next_lane_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int32 last_lane_ids = 53;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int32Size(this->last_lane_ids_);
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _last_lane_ids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .gwm.hdmap.Lane.LaneType types = 55;
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_types_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_types(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _types_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // .gwm.hdmap.LaneBoundary left_boundary = 3;
  if (this->has_left_boundary()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *left_boundary_);
  }

  // .gwm.hdmap.LaneBoundary right_boundary = 4;
  if (this->has_right_boundary()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *right_boundary_);
  }

  // .gwm.common.Polyline centerline = 23;
  if (this->has_centerline()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *centerline_);
  }

  // int32 id = 1;
  if (this->id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_id());
  }

  // float speed_limit = 6;
  if (!(this->speed_limit() <= 0 && this->speed_limit() >= 0)) {
    total_size += 1 + 4;
  }

  // double length = 5;
  if (!(this->length() <= 0 && this->length() >= 0)) {
    total_size += 1 + 8;
  }

  // int32 left_neighbor_forward_lane_id = 10;
  if (this->left_neighbor_forward_lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_left_neighbor_forward_lane_id());
  }

  // int32 right_neighbor_forward_lane_id = 11;
  if (this->right_neighbor_forward_lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_right_neighbor_forward_lane_id());
  }

  // .gwm.hdmap.Lane.LaneType type = 12;
  if (this->type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  // .gwm.hdmap.Lane.LaneTurn turn = 13;
  if (this->turn() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_turn());
  }

  // int32 left_neighbor_reverse_lane_id = 14;
  if (this->left_neighbor_reverse_lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_left_neighbor_reverse_lane_id());
  }

  // int32 right_neighbor_reverse_lane_id = 15;
  if (this->right_neighbor_reverse_lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_right_neighbor_reverse_lane_id());
  }

  // int32 junction_id = 16;
  if (this->junction_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_junction_id());
  }

  // .gwm.hdmap.Lane.LaneDirection direction = 19;
  if (this->direction() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_direction());
  }

  // int32 left_boundary_id = 25;
  if (this->left_boundary_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_left_boundary_id());
  }

  // int32 right_boundary_id = 26;
  if (this->right_boundary_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_right_boundary_id());
  }

  // .gwm.hdmap.Lane.BoundaryDirection boundary_direction = 27;
  if (this->boundary_direction() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_boundary_direction());
  }

  // .gwm.hdmap.Lane.MergeType merge = 30;
  if (this->merge() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_merge());
  }

  // double cost = 29;
  if (!(this->cost() <= 0 && this->cost() >= 0)) {
    total_size += 2 + 8;
  }

  // int32 merge_to_lane_id = 31;
  if (this->merge_to_lane_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_merge_to_lane_id());
  }

  // int32 bidi_copy_from_id = 34;
  if (this->bidi_copy_from_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_bidi_copy_from_id());
  }

  // int32 manually_set_left_neighbor_forward_lane_id = 35;
  if (this->manually_set_left_neighbor_forward_lane_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_manually_set_left_neighbor_forward_lane_id());
  }

  // int32 manually_set_right_neighbor_forward_lane_id = 36;
  if (this->manually_set_right_neighbor_forward_lane_id() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_manually_set_right_neighbor_forward_lane_id());
  }

  // bool is_disabled = 38;
  if (this->is_disabled() != 0) {
    total_size += 2 + 1;
  }

  // bool is_virtual = 39 [deprecated = true];
  if (this->is_virtual() != 0) {
    total_size += 2 + 1;
  }

  // float truck_speed_limit = 40;
  if (!(this->truck_speed_limit() <= 0 && this->truck_speed_limit() >= 0)) {
    total_size += 2 + 4;
  }

  // float min_width = 41;
  if (!(this->min_width() <= 0 && this->min_width() >= 0)) {
    total_size += 2 + 4;
  }

  // float max_curvature = 42;
  if (!(this->max_curvature() <= 0 && this->max_curvature() >= 0)) {
    total_size += 2 + 4;
  }

  // float heading_diff = 43;
  if (!(this->heading_diff() <= 0 && this->heading_diff() >= 0)) {
    total_size += 2 + 4;
  }

  // .gwm.hdmap.Lane.SlopeType slope_type = 54;
  if (this->slope_type() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_slope_type());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Lane::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.Lane)
  GOOGLE_DCHECK_NE(&from, this);
  const Lane* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Lane>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.Lane)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.Lane)
    MergeFrom(*source);
  }
}

void Lane::MergeFrom(const Lane& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.Lane)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  overlap_id_.MergeFrom(from.overlap_id_);
  predecessor_id_.MergeFrom(from.predecessor_id_);
  successor_id_.MergeFrom(from.successor_id_);
  left_sample_.MergeFrom(from.left_sample_);
  right_sample_.MergeFrom(from.right_sample_);
  left_road_sample_.MergeFrom(from.left_road_sample_);
  right_road_sample_.MergeFrom(from.right_road_sample_);
  self_reverse_lane_id_.MergeFrom(from.self_reverse_lane_id_);
  centerline_s_.MergeFrom(from.centerline_s_);
  merge_from_lane_id_.MergeFrom(from.merge_from_lane_id_);
  layers_.MergeFrom(from.layers_);
  road_name_.MergeFrom(from.road_name_);
  lane_rules_.MergeFrom(from.lane_rules_);
  left_boundary_plus_ids_.MergeFrom(from.left_boundary_plus_ids_);
  right_boundary_plus_ids_.MergeFrom(from.right_boundary_plus_ids_);
  merge_splits_.MergeFrom(from.merge_splits_);
  road_section_ids_.MergeFrom(from.road_section_ids_);
  neighbor_merges_.MergeFrom(from.neighbor_merges_);
  manually_set_predecessor_ids_.MergeFrom(from.manually_set_predecessor_ids_);
  manually_set_successor_ids_.MergeFrom(from.manually_set_successor_ids_);
  next_lane_ids_.MergeFrom(from.next_lane_ids_);
  last_lane_ids_.MergeFrom(from.last_lane_ids_);
  types_.MergeFrom(from.types_);
  if (from.has_left_boundary()) {
    _internal_mutable_left_boundary()->::gwm::hdmap::LaneBoundary::MergeFrom(from._internal_left_boundary());
  }
  if (from.has_right_boundary()) {
    _internal_mutable_right_boundary()->::gwm::hdmap::LaneBoundary::MergeFrom(from._internal_right_boundary());
  }
  if (from.has_centerline()) {
    _internal_mutable_centerline()->::gwm::common::Polyline::MergeFrom(from._internal_centerline());
  }
  if (from.id() != 0) {
    _internal_set_id(from._internal_id());
  }
  if (!(from.speed_limit() <= 0 && from.speed_limit() >= 0)) {
    _internal_set_speed_limit(from._internal_speed_limit());
  }
  if (!(from.length() <= 0 && from.length() >= 0)) {
    _internal_set_length(from._internal_length());
  }
  if (from.left_neighbor_forward_lane_id() != 0) {
    _internal_set_left_neighbor_forward_lane_id(from._internal_left_neighbor_forward_lane_id());
  }
  if (from.right_neighbor_forward_lane_id() != 0) {
    _internal_set_right_neighbor_forward_lane_id(from._internal_right_neighbor_forward_lane_id());
  }
  if (from.type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from.turn() != 0) {
    _internal_set_turn(from._internal_turn());
  }
  if (from.left_neighbor_reverse_lane_id() != 0) {
    _internal_set_left_neighbor_reverse_lane_id(from._internal_left_neighbor_reverse_lane_id());
  }
  if (from.right_neighbor_reverse_lane_id() != 0) {
    _internal_set_right_neighbor_reverse_lane_id(from._internal_right_neighbor_reverse_lane_id());
  }
  if (from.junction_id() != 0) {
    _internal_set_junction_id(from._internal_junction_id());
  }
  if (from.direction() != 0) {
    _internal_set_direction(from._internal_direction());
  }
  if (from.left_boundary_id() != 0) {
    _internal_set_left_boundary_id(from._internal_left_boundary_id());
  }
  if (from.right_boundary_id() != 0) {
    _internal_set_right_boundary_id(from._internal_right_boundary_id());
  }
  if (from.boundary_direction() != 0) {
    _internal_set_boundary_direction(from._internal_boundary_direction());
  }
  if (from.merge() != 0) {
    _internal_set_merge(from._internal_merge());
  }
  if (!(from.cost() <= 0 && from.cost() >= 0)) {
    _internal_set_cost(from._internal_cost());
  }
  if (from.merge_to_lane_id() != 0) {
    _internal_set_merge_to_lane_id(from._internal_merge_to_lane_id());
  }
  if (from.bidi_copy_from_id() != 0) {
    _internal_set_bidi_copy_from_id(from._internal_bidi_copy_from_id());
  }
  if (from.manually_set_left_neighbor_forward_lane_id() != 0) {
    _internal_set_manually_set_left_neighbor_forward_lane_id(from._internal_manually_set_left_neighbor_forward_lane_id());
  }
  if (from.manually_set_right_neighbor_forward_lane_id() != 0) {
    _internal_set_manually_set_right_neighbor_forward_lane_id(from._internal_manually_set_right_neighbor_forward_lane_id());
  }
  if (from.is_disabled() != 0) {
    _internal_set_is_disabled(from._internal_is_disabled());
  }
  if (from.is_virtual() != 0) {
    _internal_set_is_virtual(from._internal_is_virtual());
  }
  if (!(from.truck_speed_limit() <= 0 && from.truck_speed_limit() >= 0)) {
    _internal_set_truck_speed_limit(from._internal_truck_speed_limit());
  }
  if (!(from.min_width() <= 0 && from.min_width() >= 0)) {
    _internal_set_min_width(from._internal_min_width());
  }
  if (!(from.max_curvature() <= 0 && from.max_curvature() >= 0)) {
    _internal_set_max_curvature(from._internal_max_curvature());
  }
  if (!(from.heading_diff() <= 0 && from.heading_diff() >= 0)) {
    _internal_set_heading_diff(from._internal_heading_diff());
  }
  if (from.slope_type() != 0) {
    _internal_set_slope_type(from._internal_slope_type());
  }
}

void Lane::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.Lane)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Lane::CopyFrom(const Lane& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.Lane)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Lane::IsInitialized() const {
  return true;
}

void Lane::InternalSwap(Lane* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  overlap_id_.InternalSwap(&other->overlap_id_);
  predecessor_id_.InternalSwap(&other->predecessor_id_);
  successor_id_.InternalSwap(&other->successor_id_);
  left_sample_.InternalSwap(&other->left_sample_);
  right_sample_.InternalSwap(&other->right_sample_);
  left_road_sample_.InternalSwap(&other->left_road_sample_);
  right_road_sample_.InternalSwap(&other->right_road_sample_);
  self_reverse_lane_id_.InternalSwap(&other->self_reverse_lane_id_);
  centerline_s_.InternalSwap(&other->centerline_s_);
  merge_from_lane_id_.InternalSwap(&other->merge_from_lane_id_);
  layers_.InternalSwap(&other->layers_);
  road_name_.InternalSwap(&other->road_name_);
  lane_rules_.InternalSwap(&other->lane_rules_);
  left_boundary_plus_ids_.InternalSwap(&other->left_boundary_plus_ids_);
  right_boundary_plus_ids_.InternalSwap(&other->right_boundary_plus_ids_);
  merge_splits_.InternalSwap(&other->merge_splits_);
  road_section_ids_.InternalSwap(&other->road_section_ids_);
  neighbor_merges_.InternalSwap(&other->neighbor_merges_);
  manually_set_predecessor_ids_.InternalSwap(&other->manually_set_predecessor_ids_);
  manually_set_successor_ids_.InternalSwap(&other->manually_set_successor_ids_);
  next_lane_ids_.InternalSwap(&other->next_lane_ids_);
  last_lane_ids_.InternalSwap(&other->last_lane_ids_);
  types_.InternalSwap(&other->types_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Lane, slope_type_)
      + sizeof(Lane::slope_type_)
      - PROTOBUF_FIELD_OFFSET(Lane, left_boundary_)>(
          reinterpret_cast<char*>(&left_boundary_),
          reinterpret_cast<char*>(&other->left_boundary_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Lane::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class EgoLaneInfo::_Internal {
 public:
  static const ::gwm::common::Point3D& projecting_point(const EgoLaneInfo* msg);
};

const ::gwm::common::Point3D&
EgoLaneInfo::_Internal::projecting_point(const EgoLaneInfo* msg) {
  return *msg->projecting_point_;
}
void EgoLaneInfo::clear_projecting_point() {
  if (GetArena() == nullptr && projecting_point_ != nullptr) {
    delete projecting_point_;
  }
  projecting_point_ = nullptr;
}
EgoLaneInfo::EgoLaneInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.EgoLaneInfo)
}
EgoLaneInfo::EgoLaneInfo(const EgoLaneInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_projecting_point()) {
    projecting_point_ = new ::gwm::common::Point3D(*from.projecting_point_);
  } else {
    projecting_point_ = nullptr;
  }
  ::memcpy(&lane_id_, &from.lane_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&distance_to_line_) -
    reinterpret_cast<char*>(&lane_id_)) + sizeof(distance_to_line_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.EgoLaneInfo)
}

void EgoLaneInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&projecting_point_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&distance_to_line_) -
      reinterpret_cast<char*>(&projecting_point_)) + sizeof(distance_to_line_));
}

EgoLaneInfo::~EgoLaneInfo() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.EgoLaneInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void EgoLaneInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete projecting_point_;
}

void EgoLaneInfo::ArenaDtor(void* object) {
  EgoLaneInfo* _this = reinterpret_cast< EgoLaneInfo* >(object);
  (void)_this;
}
void EgoLaneInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EgoLaneInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const EgoLaneInfo& EgoLaneInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_EgoLaneInfo_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void EgoLaneInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.EgoLaneInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArena() == nullptr && projecting_point_ != nullptr) {
    delete projecting_point_;
  }
  projecting_point_ = nullptr;
  ::memset(&lane_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&distance_to_line_) -
      reinterpret_cast<char*>(&lane_id_)) + sizeof(distance_to_line_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EgoLaneInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int32 lane_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 lane_index = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          lane_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 prev_coordinate_index = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          prev_coordinate_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // float offset_length_from_prev_point = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 37)) {
          offset_length_from_prev_point_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float offset_length_from_start_point = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 45)) {
          offset_length_from_start_point_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // float distance_to_line = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 53)) {
          distance_to_line_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else goto handle_unusual;
        continue;
      // .gwm.common.Point3D projecting_point = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_projecting_point(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EgoLaneInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.EgoLaneInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 lane_id = 1;
  if (this->lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_lane_id(), target);
  }

  // int32 lane_index = 2;
  if (this->lane_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_lane_index(), target);
  }

  // int32 prev_coordinate_index = 3;
  if (this->prev_coordinate_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_prev_coordinate_index(), target);
  }

  // float offset_length_from_prev_point = 4;
  if (!(this->offset_length_from_prev_point() <= 0 && this->offset_length_from_prev_point() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_offset_length_from_prev_point(), target);
  }

  // float offset_length_from_start_point = 5;
  if (!(this->offset_length_from_start_point() <= 0 && this->offset_length_from_start_point() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_offset_length_from_start_point(), target);
  }

  // float distance_to_line = 6;
  if (!(this->distance_to_line() <= 0 && this->distance_to_line() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_distance_to_line(), target);
  }

  // .gwm.common.Point3D projecting_point = 7;
  if (this->has_projecting_point()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::projecting_point(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.EgoLaneInfo)
  return target;
}

size_t EgoLaneInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.EgoLaneInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.common.Point3D projecting_point = 7;
  if (this->has_projecting_point()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *projecting_point_);
  }

  // int32 lane_id = 1;
  if (this->lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_lane_id());
  }

  // int32 lane_index = 2;
  if (this->lane_index() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_lane_index());
  }

  // int32 prev_coordinate_index = 3;
  if (this->prev_coordinate_index() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_prev_coordinate_index());
  }

  // float offset_length_from_prev_point = 4;
  if (!(this->offset_length_from_prev_point() <= 0 && this->offset_length_from_prev_point() >= 0)) {
    total_size += 1 + 4;
  }

  // float offset_length_from_start_point = 5;
  if (!(this->offset_length_from_start_point() <= 0 && this->offset_length_from_start_point() >= 0)) {
    total_size += 1 + 4;
  }

  // float distance_to_line = 6;
  if (!(this->distance_to_line() <= 0 && this->distance_to_line() >= 0)) {
    total_size += 1 + 4;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EgoLaneInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.EgoLaneInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const EgoLaneInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<EgoLaneInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.EgoLaneInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.EgoLaneInfo)
    MergeFrom(*source);
  }
}

void EgoLaneInfo::MergeFrom(const EgoLaneInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.EgoLaneInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_projecting_point()) {
    _internal_mutable_projecting_point()->::gwm::common::Point3D::MergeFrom(from._internal_projecting_point());
  }
  if (from.lane_id() != 0) {
    _internal_set_lane_id(from._internal_lane_id());
  }
  if (from.lane_index() != 0) {
    _internal_set_lane_index(from._internal_lane_index());
  }
  if (from.prev_coordinate_index() != 0) {
    _internal_set_prev_coordinate_index(from._internal_prev_coordinate_index());
  }
  if (!(from.offset_length_from_prev_point() <= 0 && from.offset_length_from_prev_point() >= 0)) {
    _internal_set_offset_length_from_prev_point(from._internal_offset_length_from_prev_point());
  }
  if (!(from.offset_length_from_start_point() <= 0 && from.offset_length_from_start_point() >= 0)) {
    _internal_set_offset_length_from_start_point(from._internal_offset_length_from_start_point());
  }
  if (!(from.distance_to_line() <= 0 && from.distance_to_line() >= 0)) {
    _internal_set_distance_to_line(from._internal_distance_to_line());
  }
}

void EgoLaneInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.EgoLaneInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EgoLaneInfo::CopyFrom(const EgoLaneInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.EgoLaneInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EgoLaneInfo::IsInitialized() const {
  return true;
}

void EgoLaneInfo::InternalSwap(EgoLaneInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(EgoLaneInfo, distance_to_line_)
      + sizeof(EgoLaneInfo::distance_to_line_)
      - PROTOBUF_FIELD_OFFSET(EgoLaneInfo, projecting_point_)>(
          reinterpret_cast<char*>(&projecting_point_),
          reinterpret_cast<char*>(&other->projecting_point_));
}

::PROTOBUF_NAMESPACE_ID::Metadata EgoLaneInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class LanePassableInfo::_Internal {
 public:
};

LanePassableInfo::LanePassableInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.LanePassableInfo)
}
LanePassableInfo::LanePassableInfo(const LanePassableInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lane_id_, &from.lane_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&lane_accessibility_) -
    reinterpret_cast<char*>(&lane_id_)) + sizeof(lane_accessibility_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.LanePassableInfo)
}

void LanePassableInfo::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&lane_id_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&lane_accessibility_) -
      reinterpret_cast<char*>(&lane_id_)) + sizeof(lane_accessibility_));
}

LanePassableInfo::~LanePassableInfo() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.LanePassableInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void LanePassableInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void LanePassableInfo::ArenaDtor(void* object) {
  LanePassableInfo* _this = reinterpret_cast< LanePassableInfo* >(object);
  (void)_this;
}
void LanePassableInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LanePassableInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const LanePassableInfo& LanePassableInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_LanePassableInfo_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void LanePassableInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.LanePassableInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lane_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&lane_accessibility_) -
      reinterpret_cast<char*>(&lane_id_)) + sizeof(lane_accessibility_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LanePassableInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int32 lane_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          lane_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 lane_index = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          lane_index_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.hdmap.LanePassableInfo.LaneAccessibility lane_accessibility = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_lane_accessibility(static_cast<::gwm::hdmap::LanePassableInfo_LaneAccessibility>(val));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LanePassableInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.LanePassableInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 lane_id = 1;
  if (this->lane_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_lane_id(), target);
  }

  // int32 lane_index = 2;
  if (this->lane_index() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_lane_index(), target);
  }

  // .gwm.hdmap.LanePassableInfo.LaneAccessibility lane_accessibility = 3;
  if (this->lane_accessibility() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_lane_accessibility(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.LanePassableInfo)
  return target;
}

size_t LanePassableInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.LanePassableInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 lane_id = 1;
  if (this->lane_id() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_lane_id());
  }

  // int32 lane_index = 2;
  if (this->lane_index() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_lane_index());
  }

  // .gwm.hdmap.LanePassableInfo.LaneAccessibility lane_accessibility = 3;
  if (this->lane_accessibility() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_lane_accessibility());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LanePassableInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.LanePassableInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const LanePassableInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<LanePassableInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.LanePassableInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.LanePassableInfo)
    MergeFrom(*source);
  }
}

void LanePassableInfo::MergeFrom(const LanePassableInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.LanePassableInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.lane_id() != 0) {
    _internal_set_lane_id(from._internal_lane_id());
  }
  if (from.lane_index() != 0) {
    _internal_set_lane_index(from._internal_lane_index());
  }
  if (from.lane_accessibility() != 0) {
    _internal_set_lane_accessibility(from._internal_lane_accessibility());
  }
}

void LanePassableInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.LanePassableInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LanePassableInfo::CopyFrom(const LanePassableInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.LanePassableInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LanePassableInfo::IsInitialized() const {
  return true;
}

void LanePassableInfo::InternalSwap(LanePassableInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LanePassableInfo, lane_accessibility_)
      + sizeof(LanePassableInfo::lane_accessibility_)
      - PROTOBUF_FIELD_OFFSET(LanePassableInfo, lane_id_)>(
          reinterpret_cast<char*>(&lane_id_),
          reinterpret_cast<char*>(&other->lane_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LanePassableInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class LaneGroup::_Internal {
 public:
};

LaneGroup::LaneGroup(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  lane_frame_info_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.LaneGroup)
}
LaneGroup::LaneGroup(const LaneGroup& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      lane_frame_info_(from.lane_frame_info_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.LaneGroup)
}

void LaneGroup::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_LaneGroup_hd_5fmap_5flane_2eproto.base);
}

LaneGroup::~LaneGroup() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.LaneGroup)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void LaneGroup::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void LaneGroup::ArenaDtor(void* object) {
  LaneGroup* _this = reinterpret_cast< LaneGroup* >(object);
  (void)_this;
}
void LaneGroup::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneGroup::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const LaneGroup& LaneGroup::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_LaneGroup_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void LaneGroup::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.LaneGroup)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lane_frame_info_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneGroup::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.hdmap.LanePassableInfo lane_frame_info = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lane_frame_info(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LaneGroup::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.LaneGroup)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.hdmap.LanePassableInfo lane_frame_info = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lane_frame_info_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_lane_frame_info(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.LaneGroup)
  return target;
}

size_t LaneGroup::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.LaneGroup)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.hdmap.LanePassableInfo lane_frame_info = 1;
  total_size += 1UL * this->_internal_lane_frame_info_size();
  for (const auto& msg : this->lane_frame_info_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LaneGroup::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.LaneGroup)
  GOOGLE_DCHECK_NE(&from, this);
  const LaneGroup* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<LaneGroup>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.LaneGroup)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.LaneGroup)
    MergeFrom(*source);
  }
}

void LaneGroup::MergeFrom(const LaneGroup& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.LaneGroup)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  lane_frame_info_.MergeFrom(from.lane_frame_info_);
}

void LaneGroup::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.LaneGroup)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LaneGroup::CopyFrom(const LaneGroup& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.LaneGroup)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneGroup::IsInitialized() const {
  return true;
}

void LaneGroup::InternalSwap(LaneGroup* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  lane_frame_info_.InternalSwap(&other->lane_frame_info_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneGroup::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RoutingLaneInfo::_Internal {
 public:
};

RoutingLaneInfo::RoutingLaneInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  ego_lane_info_(arena),
  lane_groups_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.RoutingLaneInfo)
}
RoutingLaneInfo::RoutingLaneInfo(const RoutingLaneInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      ego_lane_info_(from.ego_lane_info_),
      lane_groups_(from.lane_groups_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  time_stamp_ = from.time_stamp_;
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.RoutingLaneInfo)
}

void RoutingLaneInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto.base);
  time_stamp_ = 0;
}

RoutingLaneInfo::~RoutingLaneInfo() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.RoutingLaneInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RoutingLaneInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RoutingLaneInfo::ArenaDtor(void* object) {
  RoutingLaneInfo* _this = reinterpret_cast< RoutingLaneInfo* >(object);
  (void)_this;
}
void RoutingLaneInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RoutingLaneInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RoutingLaneInfo& RoutingLaneInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RoutingLaneInfo_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void RoutingLaneInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.RoutingLaneInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ego_lane_info_.Clear();
  lane_groups_.Clear();
  time_stamp_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoutingLaneInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double time_stamp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          time_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.EgoLaneInfo ego_lane_info = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_ego_lane_info(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.LaneGroup lane_groups = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lane_groups(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RoutingLaneInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.RoutingLaneInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double time_stamp = 1;
  if (!(this->time_stamp() <= 0 && this->time_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_time_stamp(), target);
  }

  // repeated .gwm.hdmap.EgoLaneInfo ego_lane_info = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_ego_lane_info_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_ego_lane_info(i), target, stream);
  }

  // repeated .gwm.hdmap.LaneGroup lane_groups = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lane_groups_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_lane_groups(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.RoutingLaneInfo)
  return target;
}

size_t RoutingLaneInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.RoutingLaneInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.hdmap.EgoLaneInfo ego_lane_info = 2;
  total_size += 1UL * this->_internal_ego_lane_info_size();
  for (const auto& msg : this->ego_lane_info_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .gwm.hdmap.LaneGroup lane_groups = 3;
  total_size += 1UL * this->_internal_lane_groups_size();
  for (const auto& msg : this->lane_groups_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // double time_stamp = 1;
  if (!(this->time_stamp() <= 0 && this->time_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RoutingLaneInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.RoutingLaneInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const RoutingLaneInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RoutingLaneInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.RoutingLaneInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.RoutingLaneInfo)
    MergeFrom(*source);
  }
}

void RoutingLaneInfo::MergeFrom(const RoutingLaneInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.RoutingLaneInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  ego_lane_info_.MergeFrom(from.ego_lane_info_);
  lane_groups_.MergeFrom(from.lane_groups_);
  if (!(from.time_stamp() <= 0 && from.time_stamp() >= 0)) {
    _internal_set_time_stamp(from._internal_time_stamp());
  }
}

void RoutingLaneInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.RoutingLaneInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RoutingLaneInfo::CopyFrom(const RoutingLaneInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.RoutingLaneInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoutingLaneInfo::IsInitialized() const {
  return true;
}

void RoutingLaneInfo::InternalSwap(RoutingLaneInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ego_lane_info_.InternalSwap(&other->ego_lane_info_);
  lane_groups_.InternalSwap(&other->lane_groups_);
  swap(time_stamp_, other->time_stamp_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RoutingLaneInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class RoutingMapInfo::_Internal {
 public:
};

RoutingMapInfo::RoutingMapInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  lanes_(arena),
  routing_lanes_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.RoutingMapInfo)
}
RoutingMapInfo::RoutingMapInfo(const RoutingMapInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      lanes_(from.lanes_),
      routing_lanes_(from.routing_lanes_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.RoutingMapInfo)
}

void RoutingMapInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_RoutingMapInfo_hd_5fmap_5flane_2eproto.base);
}

RoutingMapInfo::~RoutingMapInfo() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.RoutingMapInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void RoutingMapInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void RoutingMapInfo::ArenaDtor(void* object) {
  RoutingMapInfo* _this = reinterpret_cast< RoutingMapInfo* >(object);
  (void)_this;
}
void RoutingMapInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RoutingMapInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const RoutingMapInfo& RoutingMapInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_RoutingMapInfo_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void RoutingMapInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.RoutingMapInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lanes_.Clear();
  routing_lanes_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RoutingMapInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.hdmap.Lane lanes = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lanes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated .gwm.hdmap.RoutingLaneInfo routing_lanes = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_routing_lanes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* RoutingMapInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.RoutingMapInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.hdmap.Lane lanes = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lanes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_lanes(i), target, stream);
  }

  // repeated .gwm.hdmap.RoutingLaneInfo routing_lanes = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_routing_lanes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_routing_lanes(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.RoutingMapInfo)
  return target;
}

size_t RoutingMapInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.RoutingMapInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.hdmap.Lane lanes = 1;
  total_size += 1UL * this->_internal_lanes_size();
  for (const auto& msg : this->lanes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .gwm.hdmap.RoutingLaneInfo routing_lanes = 2;
  total_size += 1UL * this->_internal_routing_lanes_size();
  for (const auto& msg : this->routing_lanes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RoutingMapInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.RoutingMapInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const RoutingMapInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<RoutingMapInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.RoutingMapInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.RoutingMapInfo)
    MergeFrom(*source);
  }
}

void RoutingMapInfo::MergeFrom(const RoutingMapInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.RoutingMapInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  lanes_.MergeFrom(from.lanes_);
  routing_lanes_.MergeFrom(from.routing_lanes_);
}

void RoutingMapInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.RoutingMapInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RoutingMapInfo::CopyFrom(const RoutingMapInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.RoutingMapInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RoutingMapInfo::IsInitialized() const {
  return true;
}

void RoutingMapInfo::InternalSwap(RoutingMapInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  lanes_.InternalSwap(&other->lanes_);
  routing_lanes_.InternalSwap(&other->routing_lanes_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RoutingMapInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class LocationInfo::_Internal {
 public:
  static const ::gwm::common::Point3D& point(const LocationInfo* msg);
};

const ::gwm::common::Point3D&
LocationInfo::_Internal::point(const LocationInfo* msg) {
  return *msg->point_;
}
void LocationInfo::clear_point() {
  if (GetArena() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
}
LocationInfo::LocationInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.LocationInfo)
}
LocationInfo::LocationInfo(const LocationInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_point()) {
    point_ = new ::gwm::common::Point3D(*from.point_);
  } else {
    point_ = nullptr;
  }
  ::memcpy(&radias_, &from.radias_,
    static_cast<size_t>(reinterpret_cast<char*>(&time_stamp_) -
    reinterpret_cast<char*>(&radias_)) + sizeof(time_stamp_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.LocationInfo)
}

void LocationInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_LocationInfo_hd_5fmap_5flane_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&point_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&time_stamp_) -
      reinterpret_cast<char*>(&point_)) + sizeof(time_stamp_));
}

LocationInfo::~LocationInfo() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.LocationInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void LocationInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete point_;
}

void LocationInfo::ArenaDtor(void* object) {
  LocationInfo* _this = reinterpret_cast< LocationInfo* >(object);
  (void)_this;
}
void LocationInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LocationInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const LocationInfo& LocationInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_LocationInfo_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void LocationInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.LocationInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArena() == nullptr && point_ != nullptr) {
    delete point_;
  }
  point_ = nullptr;
  ::memset(&radias_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&time_stamp_) -
      reinterpret_cast<char*>(&radias_)) + sizeof(time_stamp_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LocationInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.common.Point3D point = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_point(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double radias = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          radias_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double heading_degree = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          heading_degree_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double max_tolerance_angle = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 33)) {
          max_tolerance_angle_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double time_stamp = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 41)) {
          time_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* LocationInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.LocationInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.common.Point3D point = 1;
  if (this->has_point()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::point(this), target, stream);
  }

  // double radias = 2;
  if (!(this->radias() <= 0 && this->radias() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_radias(), target);
  }

  // double heading_degree = 3;
  if (!(this->heading_degree() <= 0 && this->heading_degree() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_heading_degree(), target);
  }

  // double max_tolerance_angle = 4;
  if (!(this->max_tolerance_angle() <= 0 && this->max_tolerance_angle() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_max_tolerance_angle(), target);
  }

  // double time_stamp = 5;
  if (!(this->time_stamp() <= 0 && this->time_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_time_stamp(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.LocationInfo)
  return target;
}

size_t LocationInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.LocationInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.common.Point3D point = 1;
  if (this->has_point()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *point_);
  }

  // double radias = 2;
  if (!(this->radias() <= 0 && this->radias() >= 0)) {
    total_size += 1 + 8;
  }

  // double heading_degree = 3;
  if (!(this->heading_degree() <= 0 && this->heading_degree() >= 0)) {
    total_size += 1 + 8;
  }

  // double max_tolerance_angle = 4;
  if (!(this->max_tolerance_angle() <= 0 && this->max_tolerance_angle() >= 0)) {
    total_size += 1 + 8;
  }

  // double time_stamp = 5;
  if (!(this->time_stamp() <= 0 && this->time_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LocationInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.LocationInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const LocationInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<LocationInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.LocationInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.LocationInfo)
    MergeFrom(*source);
  }
}

void LocationInfo::MergeFrom(const LocationInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.LocationInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_point()) {
    _internal_mutable_point()->::gwm::common::Point3D::MergeFrom(from._internal_point());
  }
  if (!(from.radias() <= 0 && from.radias() >= 0)) {
    _internal_set_radias(from._internal_radias());
  }
  if (!(from.heading_degree() <= 0 && from.heading_degree() >= 0)) {
    _internal_set_heading_degree(from._internal_heading_degree());
  }
  if (!(from.max_tolerance_angle() <= 0 && from.max_tolerance_angle() >= 0)) {
    _internal_set_max_tolerance_angle(from._internal_max_tolerance_angle());
  }
  if (!(from.time_stamp() <= 0 && from.time_stamp() >= 0)) {
    _internal_set_time_stamp(from._internal_time_stamp());
  }
}

void LocationInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.LocationInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LocationInfo::CopyFrom(const LocationInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.LocationInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LocationInfo::IsInitialized() const {
  return true;
}

void LocationInfo::InternalSwap(LocationInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LocationInfo, time_stamp_)
      + sizeof(LocationInfo::time_stamp_)
      - PROTOBUF_FIELD_OFFSET(LocationInfo, point_)>(
          reinterpret_cast<char*>(&point_),
          reinterpret_cast<char*>(&other->point_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LocationInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class TrackList::_Internal {
 public:
};

TrackList::TrackList(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  locations_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.TrackList)
}
TrackList::TrackList(const TrackList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      locations_(from.locations_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.TrackList)
}

void TrackList::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TrackList_hd_5fmap_5flane_2eproto.base);
}

TrackList::~TrackList() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.TrackList)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void TrackList::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void TrackList::ArenaDtor(void* object) {
  TrackList* _this = reinterpret_cast< TrackList* >(object);
  (void)_this;
}
void TrackList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TrackList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TrackList& TrackList::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TrackList_hd_5fmap_5flane_2eproto.base);
  return *internal_default_instance();
}


void TrackList::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.TrackList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  locations_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TrackList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.hdmap.LocationInfo locations = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_locations(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TrackList::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.TrackList)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.hdmap.LocationInfo locations = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_locations_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_locations(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.TrackList)
  return target;
}

size_t TrackList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.TrackList)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.hdmap.LocationInfo locations = 1;
  total_size += 1UL * this->_internal_locations_size();
  for (const auto& msg : this->locations_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TrackList::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.TrackList)
  GOOGLE_DCHECK_NE(&from, this);
  const TrackList* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TrackList>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.TrackList)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.TrackList)
    MergeFrom(*source);
  }
}

void TrackList::MergeFrom(const TrackList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.TrackList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  locations_.MergeFrom(from.locations_);
}

void TrackList::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.TrackList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TrackList::CopyFrom(const TrackList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.TrackList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackList::IsInitialized() const {
  return true;
}

void TrackList::InternalSwap(TrackList* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  locations_.InternalSwap(&other->locations_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TrackList::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace hdmap
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::gwm::hdmap::LaneBoundaryType* Arena::CreateMaybeMessage< ::gwm::hdmap::LaneBoundaryType >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::LaneBoundaryType >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::LaneBoundary* Arena::CreateMaybeMessage< ::gwm::hdmap::LaneBoundary >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::LaneBoundary >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::LaneSampleAssociation* Arena::CreateMaybeMessage< ::gwm::hdmap::LaneSampleAssociation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::LaneSampleAssociation >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::Entrance* Arena::CreateMaybeMessage< ::gwm::hdmap::Entrance >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::Entrance >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::Rule* Arena::CreateMaybeMessage< ::gwm::hdmap::Rule >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::Rule >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::Trigger* Arena::CreateMaybeMessage< ::gwm::hdmap::Trigger >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::Trigger >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::LaneRule* Arena::CreateMaybeMessage< ::gwm::hdmap::LaneRule >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::LaneRule >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::MergeSplit_Merge* Arena::CreateMaybeMessage< ::gwm::hdmap::MergeSplit_Merge >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::MergeSplit_Merge >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::MergeSplit_Split* Arena::CreateMaybeMessage< ::gwm::hdmap::MergeSplit_Split >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::MergeSplit_Split >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::MergeSplit* Arena::CreateMaybeMessage< ::gwm::hdmap::MergeSplit >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::MergeSplit >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::NeighborMerge_Ids* Arena::CreateMaybeMessage< ::gwm::hdmap::NeighborMerge_Ids >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::NeighborMerge_Ids >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::NeighborMerge* Arena::CreateMaybeMessage< ::gwm::hdmap::NeighborMerge >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::NeighborMerge >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::Lane* Arena::CreateMaybeMessage< ::gwm::hdmap::Lane >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::Lane >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::EgoLaneInfo* Arena::CreateMaybeMessage< ::gwm::hdmap::EgoLaneInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::EgoLaneInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::LanePassableInfo* Arena::CreateMaybeMessage< ::gwm::hdmap::LanePassableInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::LanePassableInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::LaneGroup* Arena::CreateMaybeMessage< ::gwm::hdmap::LaneGroup >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::LaneGroup >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::RoutingLaneInfo* Arena::CreateMaybeMessage< ::gwm::hdmap::RoutingLaneInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::RoutingLaneInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::RoutingMapInfo* Arena::CreateMaybeMessage< ::gwm::hdmap::RoutingMapInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::RoutingMapInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::LocationInfo* Arena::CreateMaybeMessage< ::gwm::hdmap::LocationInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::LocationInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::TrackList* Arena::CreateMaybeMessage< ::gwm::hdmap::TrackList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::TrackList >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
