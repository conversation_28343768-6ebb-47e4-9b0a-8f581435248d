// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: hd_map_lane.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_hd_5fmap_5flane_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_hd_5fmap_5flane_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3014000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3014000 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "common/geometry.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_hd_5fmap_5flane_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_hd_5fmap_5flane_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[20]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const ::PROTOBUF_NAMESPACE_ID::uint32 offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_hd_5fmap_5flane_2eproto;
namespace gwm {
namespace hdmap {
class EgoLaneInfo;
class EgoLaneInfoDefaultTypeInternal;
extern EgoLaneInfoDefaultTypeInternal _EgoLaneInfo_default_instance_;
class Entrance;
class EntranceDefaultTypeInternal;
extern EntranceDefaultTypeInternal _Entrance_default_instance_;
class Lane;
class LaneDefaultTypeInternal;
extern LaneDefaultTypeInternal _Lane_default_instance_;
class LaneBoundary;
class LaneBoundaryDefaultTypeInternal;
extern LaneBoundaryDefaultTypeInternal _LaneBoundary_default_instance_;
class LaneBoundaryType;
class LaneBoundaryTypeDefaultTypeInternal;
extern LaneBoundaryTypeDefaultTypeInternal _LaneBoundaryType_default_instance_;
class LaneGroup;
class LaneGroupDefaultTypeInternal;
extern LaneGroupDefaultTypeInternal _LaneGroup_default_instance_;
class LanePassableInfo;
class LanePassableInfoDefaultTypeInternal;
extern LanePassableInfoDefaultTypeInternal _LanePassableInfo_default_instance_;
class LaneRule;
class LaneRuleDefaultTypeInternal;
extern LaneRuleDefaultTypeInternal _LaneRule_default_instance_;
class LaneSampleAssociation;
class LaneSampleAssociationDefaultTypeInternal;
extern LaneSampleAssociationDefaultTypeInternal _LaneSampleAssociation_default_instance_;
class LocationInfo;
class LocationInfoDefaultTypeInternal;
extern LocationInfoDefaultTypeInternal _LocationInfo_default_instance_;
class MergeSplit;
class MergeSplitDefaultTypeInternal;
extern MergeSplitDefaultTypeInternal _MergeSplit_default_instance_;
class MergeSplit_Merge;
class MergeSplit_MergeDefaultTypeInternal;
extern MergeSplit_MergeDefaultTypeInternal _MergeSplit_Merge_default_instance_;
class MergeSplit_Split;
class MergeSplit_SplitDefaultTypeInternal;
extern MergeSplit_SplitDefaultTypeInternal _MergeSplit_Split_default_instance_;
class NeighborMerge;
class NeighborMergeDefaultTypeInternal;
extern NeighborMergeDefaultTypeInternal _NeighborMerge_default_instance_;
class NeighborMerge_Ids;
class NeighborMerge_IdsDefaultTypeInternal;
extern NeighborMerge_IdsDefaultTypeInternal _NeighborMerge_Ids_default_instance_;
class RoutingLaneInfo;
class RoutingLaneInfoDefaultTypeInternal;
extern RoutingLaneInfoDefaultTypeInternal _RoutingLaneInfo_default_instance_;
class RoutingMapInfo;
class RoutingMapInfoDefaultTypeInternal;
extern RoutingMapInfoDefaultTypeInternal _RoutingMapInfo_default_instance_;
class Rule;
class RuleDefaultTypeInternal;
extern RuleDefaultTypeInternal _Rule_default_instance_;
class TrackList;
class TrackListDefaultTypeInternal;
extern TrackListDefaultTypeInternal _TrackList_default_instance_;
class Trigger;
class TriggerDefaultTypeInternal;
extern TriggerDefaultTypeInternal _Trigger_default_instance_;
}  // namespace hdmap
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> ::gwm::hdmap::EgoLaneInfo* Arena::CreateMaybeMessage<::gwm::hdmap::EgoLaneInfo>(Arena*);
template<> ::gwm::hdmap::Entrance* Arena::CreateMaybeMessage<::gwm::hdmap::Entrance>(Arena*);
template<> ::gwm::hdmap::Lane* Arena::CreateMaybeMessage<::gwm::hdmap::Lane>(Arena*);
template<> ::gwm::hdmap::LaneBoundary* Arena::CreateMaybeMessage<::gwm::hdmap::LaneBoundary>(Arena*);
template<> ::gwm::hdmap::LaneBoundaryType* Arena::CreateMaybeMessage<::gwm::hdmap::LaneBoundaryType>(Arena*);
template<> ::gwm::hdmap::LaneGroup* Arena::CreateMaybeMessage<::gwm::hdmap::LaneGroup>(Arena*);
template<> ::gwm::hdmap::LanePassableInfo* Arena::CreateMaybeMessage<::gwm::hdmap::LanePassableInfo>(Arena*);
template<> ::gwm::hdmap::LaneRule* Arena::CreateMaybeMessage<::gwm::hdmap::LaneRule>(Arena*);
template<> ::gwm::hdmap::LaneSampleAssociation* Arena::CreateMaybeMessage<::gwm::hdmap::LaneSampleAssociation>(Arena*);
template<> ::gwm::hdmap::LocationInfo* Arena::CreateMaybeMessage<::gwm::hdmap::LocationInfo>(Arena*);
template<> ::gwm::hdmap::MergeSplit* Arena::CreateMaybeMessage<::gwm::hdmap::MergeSplit>(Arena*);
template<> ::gwm::hdmap::MergeSplit_Merge* Arena::CreateMaybeMessage<::gwm::hdmap::MergeSplit_Merge>(Arena*);
template<> ::gwm::hdmap::MergeSplit_Split* Arena::CreateMaybeMessage<::gwm::hdmap::MergeSplit_Split>(Arena*);
template<> ::gwm::hdmap::NeighborMerge* Arena::CreateMaybeMessage<::gwm::hdmap::NeighborMerge>(Arena*);
template<> ::gwm::hdmap::NeighborMerge_Ids* Arena::CreateMaybeMessage<::gwm::hdmap::NeighborMerge_Ids>(Arena*);
template<> ::gwm::hdmap::RoutingLaneInfo* Arena::CreateMaybeMessage<::gwm::hdmap::RoutingLaneInfo>(Arena*);
template<> ::gwm::hdmap::RoutingMapInfo* Arena::CreateMaybeMessage<::gwm::hdmap::RoutingMapInfo>(Arena*);
template<> ::gwm::hdmap::Rule* Arena::CreateMaybeMessage<::gwm::hdmap::Rule>(Arena*);
template<> ::gwm::hdmap::TrackList* Arena::CreateMaybeMessage<::gwm::hdmap::TrackList>(Arena*);
template<> ::gwm::hdmap::Trigger* Arena::CreateMaybeMessage<::gwm::hdmap::Trigger>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace gwm {
namespace hdmap {

enum LaneBoundaryType_Type : int {
  LaneBoundaryType_Type_UNKNOWN = 0,
  LaneBoundaryType_Type_DOTTED_YELLOW = 1,
  LaneBoundaryType_Type_DOTTED_WHITE = 2,
  LaneBoundaryType_Type_SOLID_YELLOW = 3,
  LaneBoundaryType_Type_SOLID_WHITE = 4,
  LaneBoundaryType_Type_DOUBLE_YELLOW = 5,
  LaneBoundaryType_Type_CURB = 6,
  LaneBoundaryType_Type_DOUBLE_SOLID_YELLOW = 7,
  LaneBoundaryType_Type_DOUBLE_DOTTED_WHITE = 8,
  LaneBoundaryType_Type_DOUBLE_DOTTED_YELLOW = 9,
  LaneBoundaryType_Type_DOUBLE_SOLID_WHITE = 10,
  LaneBoundaryType_Type_DOUBLE_LEFT_TO_RIGHT = 11,
  LaneBoundaryType_Type_DOUBLE_RIGHT_TO_LEFT = 12,
  LaneBoundaryType_Type_PEDESTRIAN_POLE = 13,
  LaneBoundaryType_Type_OTHER_PHYSICAL_OBSTACLE = 14,
  LaneBoundaryType_Type_DOUBLE_LEFT_TO_RIGHT_YELLOW = 15,
  LaneBoundaryType_Type_DOUBLE_RIGHT_TO_LEFT_YELLOW = 16,
  LaneBoundaryType_Type_SHORT_THICK_DOTTED_WHITE = 17,
  LaneBoundaryType_Type_DOUBLE_LEFT_WHITE_TO_RIGHT_YELLOW = 18,
  LaneBoundaryType_Type_DOUBLE_RIGHT_WHITE_TO_LEFT_YELLOW = 19,
  LaneBoundaryType_Type_DOUBLE_LEFT_YELLOW_TO_RIGHT_WHITE = 20,
  LaneBoundaryType_Type_DOUBLE_RIGHT_YELLOW_TO_LEFT_WHITE = 21,
  LaneBoundaryType_Type_SOLID_ORANGE = 22,
  LaneBoundaryType_Type_DOTTED_ORANGE = 23,
  LaneBoundaryType_Type_SOLID_BLUE = 24,
  LaneBoundaryType_Type_DOTTED_BLUE = 25,
  LaneBoundaryType_Type_EDGE = 26,
  LaneBoundaryType_Type_DOUBLE_SOLID_LEFT_YELLOW_AND_RIGHT_WHITE = 27,
  LaneBoundaryType_Type_DOUBLE_DOTTED_LEFT_YELLOW_AND_RIGHT_WHITE = 28,
  LaneBoundaryType_Type_DOUBLE_SOLID_LEFT_WHITE_AND_RIGHT_YELLOW = 29,
  LaneBoundaryType_Type_DOUBLE_DOTTED_LEFT_WHITE_AND_RIGHT_YELLOW = 30,
  LaneBoundaryType_Type_LaneBoundaryType_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  LaneBoundaryType_Type_LaneBoundaryType_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool LaneBoundaryType_Type_IsValid(int value);
constexpr LaneBoundaryType_Type LaneBoundaryType_Type_Type_MIN = LaneBoundaryType_Type_UNKNOWN;
constexpr LaneBoundaryType_Type LaneBoundaryType_Type_Type_MAX = LaneBoundaryType_Type_DOUBLE_DOTTED_LEFT_WHITE_AND_RIGHT_YELLOW;
constexpr int LaneBoundaryType_Type_Type_ARRAYSIZE = LaneBoundaryType_Type_Type_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LaneBoundaryType_Type_descriptor();
template<typename T>
inline const std::string& LaneBoundaryType_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LaneBoundaryType_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LaneBoundaryType_Type_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LaneBoundaryType_Type_descriptor(), enum_t_value);
}
inline bool LaneBoundaryType_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LaneBoundaryType_Type* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LaneBoundaryType_Type>(
    LaneBoundaryType_Type_descriptor(), name, value);
}
enum LaneBoundary_Crossable : int {
  LaneBoundary_Crossable_PHYSICALLY_NOT = 0,
  LaneBoundary_Crossable_LEGALLY_NOT = 1,
  LaneBoundary_Crossable_RIGHT_TO_LEFT = 2,
  LaneBoundary_Crossable_LEFT_TO_RIGHT = 3,
  LaneBoundary_Crossable_BOTH = 4,
  LaneBoundary_Crossable_CAR_PHYSICALLY_NOT = 5,
  LaneBoundary_Crossable_LaneBoundary_Crossable_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  LaneBoundary_Crossable_LaneBoundary_Crossable_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool LaneBoundary_Crossable_IsValid(int value);
constexpr LaneBoundary_Crossable LaneBoundary_Crossable_Crossable_MIN = LaneBoundary_Crossable_PHYSICALLY_NOT;
constexpr LaneBoundary_Crossable LaneBoundary_Crossable_Crossable_MAX = LaneBoundary_Crossable_CAR_PHYSICALLY_NOT;
constexpr int LaneBoundary_Crossable_Crossable_ARRAYSIZE = LaneBoundary_Crossable_Crossable_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LaneBoundary_Crossable_descriptor();
template<typename T>
inline const std::string& LaneBoundary_Crossable_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LaneBoundary_Crossable>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LaneBoundary_Crossable_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LaneBoundary_Crossable_descriptor(), enum_t_value);
}
inline bool LaneBoundary_Crossable_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LaneBoundary_Crossable* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LaneBoundary_Crossable>(
    LaneBoundary_Crossable_descriptor(), name, value);
}
enum LaneRule_VehicleType : int {
  LaneRule_VehicleType_DEFAULT = 0,
  LaneRule_VehicleType_LIGHT_TRUCK = 1,
  LaneRule_VehicleType_LaneRule_VehicleType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  LaneRule_VehicleType_LaneRule_VehicleType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool LaneRule_VehicleType_IsValid(int value);
constexpr LaneRule_VehicleType LaneRule_VehicleType_VehicleType_MIN = LaneRule_VehicleType_DEFAULT;
constexpr LaneRule_VehicleType LaneRule_VehicleType_VehicleType_MAX = LaneRule_VehicleType_LIGHT_TRUCK;
constexpr int LaneRule_VehicleType_VehicleType_ARRAYSIZE = LaneRule_VehicleType_VehicleType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LaneRule_VehicleType_descriptor();
template<typename T>
inline const std::string& LaneRule_VehicleType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LaneRule_VehicleType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LaneRule_VehicleType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LaneRule_VehicleType_descriptor(), enum_t_value);
}
inline bool LaneRule_VehicleType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LaneRule_VehicleType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LaneRule_VehicleType>(
    LaneRule_VehicleType_descriptor(), name, value);
}
enum MergeSplit_Direction : int {
  MergeSplit_Direction_UNKNOWN = 0,
  MergeSplit_Direction_LEFT = 1,
  MergeSplit_Direction_RIGHT = 2,
  MergeSplit_Direction_MergeSplit_Direction_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  MergeSplit_Direction_MergeSplit_Direction_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool MergeSplit_Direction_IsValid(int value);
constexpr MergeSplit_Direction MergeSplit_Direction_Direction_MIN = MergeSplit_Direction_UNKNOWN;
constexpr MergeSplit_Direction MergeSplit_Direction_Direction_MAX = MergeSplit_Direction_RIGHT;
constexpr int MergeSplit_Direction_Direction_ARRAYSIZE = MergeSplit_Direction_Direction_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MergeSplit_Direction_descriptor();
template<typename T>
inline const std::string& MergeSplit_Direction_Name(T enum_t_value) {
  static_assert(::std::is_same<T, MergeSplit_Direction>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function MergeSplit_Direction_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    MergeSplit_Direction_descriptor(), enum_t_value);
}
inline bool MergeSplit_Direction_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MergeSplit_Direction* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<MergeSplit_Direction>(
    MergeSplit_Direction_descriptor(), name, value);
}
enum Lane_LaneType : int {
  Lane_LaneType_UNKNOWN = 0,
  Lane_LaneType_HIGHWAY = 1,
  Lane_LaneType_STREET = 2,
  Lane_LaneType_BIDIRECTIONAL = 3,
  Lane_LaneType_SHOULDER = 4,
  Lane_LaneType_BIKING = 5,
  Lane_LaneType_SIDEWALK = 6,
  Lane_LaneType_RESTRICTED = 7,
  Lane_LaneType_PARKING = 8,
  Lane_LaneType_ROADWORK = 9,
  Lane_LaneType_OFFRAMP = 10,
  Lane_LaneType_ONRAMP = 11,
  Lane_LaneType_BUSLANE = 12,
  Lane_LaneType_LEFTTURNWAITINGAREA = 13,
  Lane_LaneType_PARK = 14,
  Lane_LaneType_ROUNDABOUT = 15,
  Lane_LaneType_RIGHT_TURN_ONLY = 16,
  Lane_LaneType_PARK_ON_LANE = 17,
  Lane_LaneType_DYNAMIC_LANE = 18,
  Lane_LaneType_WIDE_LANE = 19,
  Lane_LaneType_TIDAL_LANE = 20,
  Lane_LaneType_TRANSFER_LANE = 21,
  Lane_LaneType_EMERGENCY = 22,
  Lane_LaneType_Lane_LaneType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Lane_LaneType_Lane_LaneType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Lane_LaneType_IsValid(int value);
constexpr Lane_LaneType Lane_LaneType_LaneType_MIN = Lane_LaneType_UNKNOWN;
constexpr Lane_LaneType Lane_LaneType_LaneType_MAX = Lane_LaneType_EMERGENCY;
constexpr int Lane_LaneType_LaneType_ARRAYSIZE = Lane_LaneType_LaneType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_LaneType_descriptor();
template<typename T>
inline const std::string& Lane_LaneType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lane_LaneType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lane_LaneType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lane_LaneType_descriptor(), enum_t_value);
}
inline bool Lane_LaneType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lane_LaneType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lane_LaneType>(
    Lane_LaneType_descriptor(), name, value);
}
enum Lane_LaneTurn : int {
  Lane_LaneTurn_INVALID = 0,
  Lane_LaneTurn_STRAIGHT = 1,
  Lane_LaneTurn_LEFT = 2,
  Lane_LaneTurn_RIGHT = 4,
  Lane_LaneTurn_U_TURN_LEFT = 8,
  Lane_LaneTurn_U_TURN_RIGHT = 16,
  Lane_LaneTurn_Lane_LaneTurn_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Lane_LaneTurn_Lane_LaneTurn_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Lane_LaneTurn_IsValid(int value);
constexpr Lane_LaneTurn Lane_LaneTurn_LaneTurn_MIN = Lane_LaneTurn_INVALID;
constexpr Lane_LaneTurn Lane_LaneTurn_LaneTurn_MAX = Lane_LaneTurn_U_TURN_RIGHT;
constexpr int Lane_LaneTurn_LaneTurn_ARRAYSIZE = Lane_LaneTurn_LaneTurn_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_LaneTurn_descriptor();
template<typename T>
inline const std::string& Lane_LaneTurn_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lane_LaneTurn>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lane_LaneTurn_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lane_LaneTurn_descriptor(), enum_t_value);
}
inline bool Lane_LaneTurn_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lane_LaneTurn* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lane_LaneTurn>(
    Lane_LaneTurn_descriptor(), name, value);
}
enum Lane_LaneDirection : int {
  Lane_LaneDirection_FORWARD = 0,
  Lane_LaneDirection_BACKWARD = 1,
  Lane_LaneDirection_BIDIRECTION = 2,
  Lane_LaneDirection_Lane_LaneDirection_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Lane_LaneDirection_Lane_LaneDirection_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Lane_LaneDirection_IsValid(int value);
constexpr Lane_LaneDirection Lane_LaneDirection_LaneDirection_MIN = Lane_LaneDirection_FORWARD;
constexpr Lane_LaneDirection Lane_LaneDirection_LaneDirection_MAX = Lane_LaneDirection_BIDIRECTION;
constexpr int Lane_LaneDirection_LaneDirection_ARRAYSIZE = Lane_LaneDirection_LaneDirection_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_LaneDirection_descriptor();
template<typename T>
inline const std::string& Lane_LaneDirection_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lane_LaneDirection>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lane_LaneDirection_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lane_LaneDirection_descriptor(), enum_t_value);
}
inline bool Lane_LaneDirection_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lane_LaneDirection* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lane_LaneDirection>(
    Lane_LaneDirection_descriptor(), name, value);
}
enum Lane_BoundaryDirection : int {
  Lane_BoundaryDirection_SAME = 0,
  Lane_BoundaryDirection_LEFT_REVERSE = 1,
  Lane_BoundaryDirection_RIGHT_REVERSE = 2,
  Lane_BoundaryDirection_BOTH_REVERSE = 3,
  Lane_BoundaryDirection_Lane_BoundaryDirection_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Lane_BoundaryDirection_Lane_BoundaryDirection_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Lane_BoundaryDirection_IsValid(int value);
constexpr Lane_BoundaryDirection Lane_BoundaryDirection_BoundaryDirection_MIN = Lane_BoundaryDirection_SAME;
constexpr Lane_BoundaryDirection Lane_BoundaryDirection_BoundaryDirection_MAX = Lane_BoundaryDirection_BOTH_REVERSE;
constexpr int Lane_BoundaryDirection_BoundaryDirection_ARRAYSIZE = Lane_BoundaryDirection_BoundaryDirection_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_BoundaryDirection_descriptor();
template<typename T>
inline const std::string& Lane_BoundaryDirection_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lane_BoundaryDirection>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lane_BoundaryDirection_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lane_BoundaryDirection_descriptor(), enum_t_value);
}
inline bool Lane_BoundaryDirection_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lane_BoundaryDirection* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lane_BoundaryDirection>(
    Lane_BoundaryDirection_descriptor(), name, value);
}
enum Lane_MergeType : int {
  Lane_MergeType_NONE = 0,
  Lane_MergeType_FROM_LEFT = 1,
  Lane_MergeType_FROM_RIGHT = 2,
  Lane_MergeType_Lane_MergeType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Lane_MergeType_Lane_MergeType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Lane_MergeType_IsValid(int value);
constexpr Lane_MergeType Lane_MergeType_MergeType_MIN = Lane_MergeType_NONE;
constexpr Lane_MergeType Lane_MergeType_MergeType_MAX = Lane_MergeType_FROM_RIGHT;
constexpr int Lane_MergeType_MergeType_ARRAYSIZE = Lane_MergeType_MergeType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_MergeType_descriptor();
template<typename T>
inline const std::string& Lane_MergeType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lane_MergeType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lane_MergeType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lane_MergeType_descriptor(), enum_t_value);
}
inline bool Lane_MergeType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lane_MergeType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lane_MergeType>(
    Lane_MergeType_descriptor(), name, value);
}
enum Lane_SlopeType : int {
  Lane_SlopeType_UPHILL = 0,
  Lane_SlopeType_DOWNHILL = 1,
  Lane_SlopeType_Lane_SlopeType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  Lane_SlopeType_Lane_SlopeType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool Lane_SlopeType_IsValid(int value);
constexpr Lane_SlopeType Lane_SlopeType_SlopeType_MIN = Lane_SlopeType_UPHILL;
constexpr Lane_SlopeType Lane_SlopeType_SlopeType_MAX = Lane_SlopeType_DOWNHILL;
constexpr int Lane_SlopeType_SlopeType_ARRAYSIZE = Lane_SlopeType_SlopeType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lane_SlopeType_descriptor();
template<typename T>
inline const std::string& Lane_SlopeType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lane_SlopeType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lane_SlopeType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lane_SlopeType_descriptor(), enum_t_value);
}
inline bool Lane_SlopeType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lane_SlopeType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lane_SlopeType>(
    Lane_SlopeType_descriptor(), name, value);
}
enum LanePassableInfo_LaneAccessibility : int {
  LanePassableInfo_LaneAccessibility_DEFAULT = 0,
  LanePassableInfo_LaneAccessibility_RECOMMEND = 1,
  LanePassableInfo_LaneAccessibility_NOT_RECOMMEND = 2,
  LanePassableInfo_LaneAccessibility_LanePassableInfo_LaneAccessibility_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::min(),
  LanePassableInfo_LaneAccessibility_LanePassableInfo_LaneAccessibility_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<::PROTOBUF_NAMESPACE_ID::int32>::max()
};
bool LanePassableInfo_LaneAccessibility_IsValid(int value);
constexpr LanePassableInfo_LaneAccessibility LanePassableInfo_LaneAccessibility_LaneAccessibility_MIN = LanePassableInfo_LaneAccessibility_DEFAULT;
constexpr LanePassableInfo_LaneAccessibility LanePassableInfo_LaneAccessibility_LaneAccessibility_MAX = LanePassableInfo_LaneAccessibility_NOT_RECOMMEND;
constexpr int LanePassableInfo_LaneAccessibility_LaneAccessibility_ARRAYSIZE = LanePassableInfo_LaneAccessibility_LaneAccessibility_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LanePassableInfo_LaneAccessibility_descriptor();
template<typename T>
inline const std::string& LanePassableInfo_LaneAccessibility_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LanePassableInfo_LaneAccessibility>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LanePassableInfo_LaneAccessibility_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LanePassableInfo_LaneAccessibility_descriptor(), enum_t_value);
}
inline bool LanePassableInfo_LaneAccessibility_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LanePassableInfo_LaneAccessibility* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LanePassableInfo_LaneAccessibility>(
    LanePassableInfo_LaneAccessibility_descriptor(), name, value);
}
// ===================================================================

class LaneBoundaryType PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.LaneBoundaryType) */ {
 public:
  inline LaneBoundaryType() : LaneBoundaryType(nullptr) {}
  virtual ~LaneBoundaryType();

  LaneBoundaryType(const LaneBoundaryType& from);
  LaneBoundaryType(LaneBoundaryType&& from) noexcept
    : LaneBoundaryType() {
    *this = ::std::move(from);
  }

  inline LaneBoundaryType& operator=(const LaneBoundaryType& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneBoundaryType& operator=(LaneBoundaryType&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LaneBoundaryType& default_instance();

  static inline const LaneBoundaryType* internal_default_instance() {
    return reinterpret_cast<const LaneBoundaryType*>(
               &_LaneBoundaryType_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(LaneBoundaryType& a, LaneBoundaryType& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneBoundaryType* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneBoundaryType* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LaneBoundaryType* New() const final {
    return CreateMaybeMessage<LaneBoundaryType>(nullptr);
  }

  LaneBoundaryType* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LaneBoundaryType>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LaneBoundaryType& from);
  void MergeFrom(const LaneBoundaryType& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneBoundaryType* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.LaneBoundaryType";
  }
  protected:
  explicit LaneBoundaryType(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef LaneBoundaryType_Type Type;
  static constexpr Type UNKNOWN =
    LaneBoundaryType_Type_UNKNOWN;
  static constexpr Type DOTTED_YELLOW =
    LaneBoundaryType_Type_DOTTED_YELLOW;
  static constexpr Type DOTTED_WHITE =
    LaneBoundaryType_Type_DOTTED_WHITE;
  static constexpr Type SOLID_YELLOW =
    LaneBoundaryType_Type_SOLID_YELLOW;
  static constexpr Type SOLID_WHITE =
    LaneBoundaryType_Type_SOLID_WHITE;
  static constexpr Type DOUBLE_YELLOW =
    LaneBoundaryType_Type_DOUBLE_YELLOW;
  static constexpr Type CURB =
    LaneBoundaryType_Type_CURB;
  static constexpr Type DOUBLE_SOLID_YELLOW =
    LaneBoundaryType_Type_DOUBLE_SOLID_YELLOW;
  static constexpr Type DOUBLE_DOTTED_WHITE =
    LaneBoundaryType_Type_DOUBLE_DOTTED_WHITE;
  static constexpr Type DOUBLE_DOTTED_YELLOW =
    LaneBoundaryType_Type_DOUBLE_DOTTED_YELLOW;
  static constexpr Type DOUBLE_SOLID_WHITE =
    LaneBoundaryType_Type_DOUBLE_SOLID_WHITE;
  static constexpr Type DOUBLE_LEFT_TO_RIGHT =
    LaneBoundaryType_Type_DOUBLE_LEFT_TO_RIGHT;
  static constexpr Type DOUBLE_RIGHT_TO_LEFT =
    LaneBoundaryType_Type_DOUBLE_RIGHT_TO_LEFT;
  static constexpr Type PEDESTRIAN_POLE =
    LaneBoundaryType_Type_PEDESTRIAN_POLE;
  static constexpr Type OTHER_PHYSICAL_OBSTACLE =
    LaneBoundaryType_Type_OTHER_PHYSICAL_OBSTACLE;
  static constexpr Type DOUBLE_LEFT_TO_RIGHT_YELLOW =
    LaneBoundaryType_Type_DOUBLE_LEFT_TO_RIGHT_YELLOW;
  static constexpr Type DOUBLE_RIGHT_TO_LEFT_YELLOW =
    LaneBoundaryType_Type_DOUBLE_RIGHT_TO_LEFT_YELLOW;
  static constexpr Type SHORT_THICK_DOTTED_WHITE =
    LaneBoundaryType_Type_SHORT_THICK_DOTTED_WHITE;
  static constexpr Type DOUBLE_LEFT_WHITE_TO_RIGHT_YELLOW =
    LaneBoundaryType_Type_DOUBLE_LEFT_WHITE_TO_RIGHT_YELLOW;
  static constexpr Type DOUBLE_RIGHT_WHITE_TO_LEFT_YELLOW =
    LaneBoundaryType_Type_DOUBLE_RIGHT_WHITE_TO_LEFT_YELLOW;
  static constexpr Type DOUBLE_LEFT_YELLOW_TO_RIGHT_WHITE =
    LaneBoundaryType_Type_DOUBLE_LEFT_YELLOW_TO_RIGHT_WHITE;
  static constexpr Type DOUBLE_RIGHT_YELLOW_TO_LEFT_WHITE =
    LaneBoundaryType_Type_DOUBLE_RIGHT_YELLOW_TO_LEFT_WHITE;
  static constexpr Type SOLID_ORANGE =
    LaneBoundaryType_Type_SOLID_ORANGE;
  static constexpr Type DOTTED_ORANGE =
    LaneBoundaryType_Type_DOTTED_ORANGE;
  static constexpr Type SOLID_BLUE =
    LaneBoundaryType_Type_SOLID_BLUE;
  static constexpr Type DOTTED_BLUE =
    LaneBoundaryType_Type_DOTTED_BLUE;
  static constexpr Type EDGE =
    LaneBoundaryType_Type_EDGE;
  static constexpr Type DOUBLE_SOLID_LEFT_YELLOW_AND_RIGHT_WHITE =
    LaneBoundaryType_Type_DOUBLE_SOLID_LEFT_YELLOW_AND_RIGHT_WHITE;
  static constexpr Type DOUBLE_DOTTED_LEFT_YELLOW_AND_RIGHT_WHITE =
    LaneBoundaryType_Type_DOUBLE_DOTTED_LEFT_YELLOW_AND_RIGHT_WHITE;
  static constexpr Type DOUBLE_SOLID_LEFT_WHITE_AND_RIGHT_YELLOW =
    LaneBoundaryType_Type_DOUBLE_SOLID_LEFT_WHITE_AND_RIGHT_YELLOW;
  static constexpr Type DOUBLE_DOTTED_LEFT_WHITE_AND_RIGHT_YELLOW =
    LaneBoundaryType_Type_DOUBLE_DOTTED_LEFT_WHITE_AND_RIGHT_YELLOW;
  static inline bool Type_IsValid(int value) {
    return LaneBoundaryType_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    LaneBoundaryType_Type_Type_MIN;
  static constexpr Type Type_MAX =
    LaneBoundaryType_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    LaneBoundaryType_Type_Type_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Type_descriptor() {
    return LaneBoundaryType_Type_descriptor();
  }
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return LaneBoundaryType_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Type* value) {
    return LaneBoundaryType_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTypesFieldNumber = 2,
    kSFieldNumber = 1,
  };
  // repeated .gwm.hdmap.LaneBoundaryType.Type types = 2;
  int types_size() const;
  private:
  int _internal_types_size() const;
  public:
  void clear_types();
  private:
  ::gwm::hdmap::LaneBoundaryType_Type _internal_types(int index) const;
  void _internal_add_types(::gwm::hdmap::LaneBoundaryType_Type value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_types();
  public:
  ::gwm::hdmap::LaneBoundaryType_Type types(int index) const;
  void set_types(int index, ::gwm::hdmap::LaneBoundaryType_Type value);
  void add_types(::gwm::hdmap::LaneBoundaryType_Type value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& types() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_types();

  // double s = 1;
  void clear_s();
  double s() const;
  void set_s(double value);
  private:
  double _internal_s() const;
  void _internal_set_s(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.LaneBoundaryType)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> types_;
  mutable std::atomic<int> _types_cached_byte_size_;
  double s_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class LaneBoundary PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.LaneBoundary) */ {
 public:
  inline LaneBoundary() : LaneBoundary(nullptr) {}
  virtual ~LaneBoundary();

  LaneBoundary(const LaneBoundary& from);
  LaneBoundary(LaneBoundary&& from) noexcept
    : LaneBoundary() {
    *this = ::std::move(from);
  }

  inline LaneBoundary& operator=(const LaneBoundary& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneBoundary& operator=(LaneBoundary&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LaneBoundary& default_instance();

  static inline const LaneBoundary* internal_default_instance() {
    return reinterpret_cast<const LaneBoundary*>(
               &_LaneBoundary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LaneBoundary& a, LaneBoundary& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneBoundary* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneBoundary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LaneBoundary* New() const final {
    return CreateMaybeMessage<LaneBoundary>(nullptr);
  }

  LaneBoundary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LaneBoundary>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LaneBoundary& from);
  void MergeFrom(const LaneBoundary& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneBoundary* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.LaneBoundary";
  }
  protected:
  explicit LaneBoundary(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef LaneBoundary_Crossable Crossable;
  static constexpr Crossable PHYSICALLY_NOT =
    LaneBoundary_Crossable_PHYSICALLY_NOT;
  static constexpr Crossable LEGALLY_NOT =
    LaneBoundary_Crossable_LEGALLY_NOT;
  static constexpr Crossable RIGHT_TO_LEFT =
    LaneBoundary_Crossable_RIGHT_TO_LEFT;
  static constexpr Crossable LEFT_TO_RIGHT =
    LaneBoundary_Crossable_LEFT_TO_RIGHT;
  static constexpr Crossable BOTH =
    LaneBoundary_Crossable_BOTH;
  static constexpr Crossable CAR_PHYSICALLY_NOT =
    LaneBoundary_Crossable_CAR_PHYSICALLY_NOT;
  static inline bool Crossable_IsValid(int value) {
    return LaneBoundary_Crossable_IsValid(value);
  }
  static constexpr Crossable Crossable_MIN =
    LaneBoundary_Crossable_Crossable_MIN;
  static constexpr Crossable Crossable_MAX =
    LaneBoundary_Crossable_Crossable_MAX;
  static constexpr int Crossable_ARRAYSIZE =
    LaneBoundary_Crossable_Crossable_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Crossable_descriptor() {
    return LaneBoundary_Crossable_descriptor();
  }
  template<typename T>
  static inline const std::string& Crossable_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Crossable>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Crossable_Name.");
    return LaneBoundary_Crossable_Name(enum_t_value);
  }
  static inline bool Crossable_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Crossable* value) {
    return LaneBoundary_Crossable_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kBoundaryTypeFieldNumber = 4,
    kLayersFieldNumber = 9,
    kBoundaryFieldNumber = 6,
    kLengthFieldNumber = 2,
    kIdFieldNumber = 5,
    kCrossableFieldNumber = 7,
    kCostFieldNumber = 8,
    kVirtualFieldNumber = 3,
    kHasLeftDecelerationMarkingFieldNumber = 12,
    kHasRightDecelerationMarkingFieldNumber = 13,
    kTypeFieldNumber = 10,
  };
  // repeated .gwm.hdmap.LaneBoundaryType boundary_type = 4;
  int boundary_type_size() const;
  private:
  int _internal_boundary_type_size() const;
  public:
  void clear_boundary_type();
  ::gwm::hdmap::LaneBoundaryType* mutable_boundary_type(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneBoundaryType >*
      mutable_boundary_type();
  private:
  const ::gwm::hdmap::LaneBoundaryType& _internal_boundary_type(int index) const;
  ::gwm::hdmap::LaneBoundaryType* _internal_add_boundary_type();
  public:
  const ::gwm::hdmap::LaneBoundaryType& boundary_type(int index) const;
  ::gwm::hdmap::LaneBoundaryType* add_boundary_type();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneBoundaryType >&
      boundary_type() const;

  // repeated int32 layers = 9;
  int layers_size() const;
  private:
  int _internal_layers_size() const;
  public:
  void clear_layers();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_layers(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_layers() const;
  void _internal_add_layers(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_layers();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 layers(int index) const;
  void set_layers(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_layers(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      layers() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_layers();

  // .gwm.common.Polyline boundary = 6;
  bool has_boundary() const;
  private:
  bool _internal_has_boundary() const;
  public:
  void clear_boundary();
  const ::gwm::common::Polyline& boundary() const;
  ::gwm::common::Polyline* release_boundary();
  ::gwm::common::Polyline* mutable_boundary();
  void set_allocated_boundary(::gwm::common::Polyline* boundary);
  private:
  const ::gwm::common::Polyline& _internal_boundary() const;
  ::gwm::common::Polyline* _internal_mutable_boundary();
  public:
  void unsafe_arena_set_allocated_boundary(
      ::gwm::common::Polyline* boundary);
  ::gwm::common::Polyline* unsafe_arena_release_boundary();

  // double length = 2;
  void clear_length();
  double length() const;
  void set_length(double value);
  private:
  double _internal_length() const;
  void _internal_set_length(double value);
  public:

  // int32 id = 5;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .gwm.hdmap.LaneBoundary.Crossable crossable = 7;
  void clear_crossable();
  ::gwm::hdmap::LaneBoundary_Crossable crossable() const;
  void set_crossable(::gwm::hdmap::LaneBoundary_Crossable value);
  private:
  ::gwm::hdmap::LaneBoundary_Crossable _internal_crossable() const;
  void _internal_set_crossable(::gwm::hdmap::LaneBoundary_Crossable value);
  public:

  // double cost = 8;
  void clear_cost();
  double cost() const;
  void set_cost(double value);
  private:
  double _internal_cost() const;
  void _internal_set_cost(double value);
  public:

  // bool virtual = 3;
  void clear_virtual_();
  bool virtual_() const;
  void set_virtual_(bool value);
  private:
  bool _internal_virtual_() const;
  void _internal_set_virtual_(bool value);
  public:

  // bool has_left_deceleration_marking = 12;
  void clear_has_left_deceleration_marking();
  bool has_left_deceleration_marking() const;
  void set_has_left_deceleration_marking(bool value);
  private:
  bool _internal_has_left_deceleration_marking() const;
  void _internal_set_has_left_deceleration_marking(bool value);
  public:

  // bool has_right_deceleration_marking = 13;
  void clear_has_right_deceleration_marking();
  bool has_right_deceleration_marking() const;
  void set_has_right_deceleration_marking(bool value);
  private:
  bool _internal_has_right_deceleration_marking() const;
  void _internal_set_has_right_deceleration_marking(bool value);
  public:

  // .gwm.hdmap.LaneBoundaryType.Type type = 10;
  void clear_type();
  ::gwm::hdmap::LaneBoundaryType_Type type() const;
  void set_type(::gwm::hdmap::LaneBoundaryType_Type value);
  private:
  ::gwm::hdmap::LaneBoundaryType_Type _internal_type() const;
  void _internal_set_type(::gwm::hdmap::LaneBoundaryType_Type value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.LaneBoundary)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneBoundaryType > boundary_type_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > layers_;
  mutable std::atomic<int> _layers_cached_byte_size_;
  ::gwm::common::Polyline* boundary_;
  double length_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  int crossable_;
  double cost_;
  bool virtual__;
  bool has_left_deceleration_marking_;
  bool has_right_deceleration_marking_;
  int type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class LaneSampleAssociation PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.LaneSampleAssociation) */ {
 public:
  inline LaneSampleAssociation() : LaneSampleAssociation(nullptr) {}
  virtual ~LaneSampleAssociation();

  LaneSampleAssociation(const LaneSampleAssociation& from);
  LaneSampleAssociation(LaneSampleAssociation&& from) noexcept
    : LaneSampleAssociation() {
    *this = ::std::move(from);
  }

  inline LaneSampleAssociation& operator=(const LaneSampleAssociation& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneSampleAssociation& operator=(LaneSampleAssociation&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LaneSampleAssociation& default_instance();

  static inline const LaneSampleAssociation* internal_default_instance() {
    return reinterpret_cast<const LaneSampleAssociation*>(
               &_LaneSampleAssociation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LaneSampleAssociation& a, LaneSampleAssociation& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneSampleAssociation* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneSampleAssociation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LaneSampleAssociation* New() const final {
    return CreateMaybeMessage<LaneSampleAssociation>(nullptr);
  }

  LaneSampleAssociation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LaneSampleAssociation>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LaneSampleAssociation& from);
  void MergeFrom(const LaneSampleAssociation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneSampleAssociation* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.LaneSampleAssociation";
  }
  protected:
  explicit LaneSampleAssociation(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSFieldNumber = 1,
    kWidthFieldNumber = 2,
  };
  // double s = 1;
  void clear_s();
  double s() const;
  void set_s(double value);
  private:
  double _internal_s() const;
  void _internal_set_s(double value);
  public:

  // double width = 2;
  void clear_width();
  double width() const;
  void set_width(double value);
  private:
  double _internal_width() const;
  void _internal_set_width(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.LaneSampleAssociation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double s_;
  double width_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class Entrance PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.Entrance) */ {
 public:
  inline Entrance() : Entrance(nullptr) {}
  virtual ~Entrance();

  Entrance(const Entrance& from);
  Entrance(Entrance&& from) noexcept
    : Entrance() {
    *this = ::std::move(from);
  }

  inline Entrance& operator=(const Entrance& from) {
    CopyFrom(from);
    return *this;
  }
  inline Entrance& operator=(Entrance&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Entrance& default_instance();

  static inline const Entrance* internal_default_instance() {
    return reinterpret_cast<const Entrance*>(
               &_Entrance_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Entrance& a, Entrance& b) {
    a.Swap(&b);
  }
  inline void Swap(Entrance* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Entrance* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Entrance* New() const final {
    return CreateMaybeMessage<Entrance>(nullptr);
  }

  Entrance* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Entrance>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Entrance& from);
  void MergeFrom(const Entrance& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Entrance* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.Entrance";
  }
  protected:
  explicit Entrance(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLayersFieldNumber = 4,
    kLocationFieldNumber = 3,
    kIdFieldNumber = 1,
    kLaneIdFieldNumber = 2,
  };
  // repeated int32 layers = 4;
  int layers_size() const;
  private:
  int _internal_layers_size() const;
  public:
  void clear_layers();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_layers(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_layers() const;
  void _internal_add_layers(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_layers();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 layers(int index) const;
  void set_layers(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_layers(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      layers() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_layers();

  // .gwm.common.Point3D location = 3;
  bool has_location() const;
  private:
  bool _internal_has_location() const;
  public:
  void clear_location();
  const ::gwm::common::Point3D& location() const;
  ::gwm::common::Point3D* release_location();
  ::gwm::common::Point3D* mutable_location();
  void set_allocated_location(::gwm::common::Point3D* location);
  private:
  const ::gwm::common::Point3D& _internal_location() const;
  ::gwm::common::Point3D* _internal_mutable_location();
  public:
  void unsafe_arena_set_allocated_location(
      ::gwm::common::Point3D* location);
  ::gwm::common::Point3D* unsafe_arena_release_location();

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 lane_id = 2;
  void clear_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 lane_id() const;
  void set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_lane_id() const;
  void _internal_set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.Entrance)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > layers_;
  mutable std::atomic<int> _layers_cached_byte_size_;
  ::gwm::common::Point3D* location_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  ::PROTOBUF_NAMESPACE_ID::int32 lane_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class Rule PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.Rule) */ {
 public:
  inline Rule() : Rule(nullptr) {}
  virtual ~Rule();

  Rule(const Rule& from);
  Rule(Rule&& from) noexcept
    : Rule() {
    *this = ::std::move(from);
  }

  inline Rule& operator=(const Rule& from) {
    CopyFrom(from);
    return *this;
  }
  inline Rule& operator=(Rule&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Rule& default_instance();

  enum RuleCase {
    kSpeedLimit = 1,
    kDisabled = 2,
    RULE_NOT_SET = 0,
  };

  static inline const Rule* internal_default_instance() {
    return reinterpret_cast<const Rule*>(
               &_Rule_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Rule& a, Rule& b) {
    a.Swap(&b);
  }
  inline void Swap(Rule* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Rule* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Rule* New() const final {
    return CreateMaybeMessage<Rule>(nullptr);
  }

  Rule* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Rule>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Rule& from);
  void MergeFrom(const Rule& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Rule* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.Rule";
  }
  protected:
  explicit Rule(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSpeedLimitFieldNumber = 1,
    kDisabledFieldNumber = 2,
  };
  // float speed_limit = 1;
  private:
  bool _internal_has_speed_limit() const;
  public:
  void clear_speed_limit();
  float speed_limit() const;
  void set_speed_limit(float value);
  private:
  float _internal_speed_limit() const;
  void _internal_set_speed_limit(float value);
  public:

  // bool disabled = 2;
  private:
  bool _internal_has_disabled() const;
  public:
  void clear_disabled();
  bool disabled() const;
  void set_disabled(bool value);
  private:
  bool _internal_disabled() const;
  void _internal_set_disabled(bool value);
  public:

  void clear_rule();
  RuleCase rule_case() const;
  // @@protoc_insertion_point(class_scope:gwm.hdmap.Rule)
 private:
  class _Internal;
  void set_has_speed_limit();
  void set_has_disabled();

  inline bool has_rule() const;
  inline void clear_has_rule();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union RuleUnion {
    RuleUnion() {}
    float speed_limit_;
    bool disabled_;
  } rule_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class Trigger PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.Trigger) */ {
 public:
  inline Trigger() : Trigger(nullptr) {}
  virtual ~Trigger();

  Trigger(const Trigger& from);
  Trigger(Trigger&& from) noexcept
    : Trigger() {
    *this = ::std::move(from);
  }

  inline Trigger& operator=(const Trigger& from) {
    CopyFrom(from);
    return *this;
  }
  inline Trigger& operator=(Trigger&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Trigger& default_instance();

  enum TriggerCase {
    kAlways = 1,
    TRIGGER_NOT_SET = 0,
  };

  static inline const Trigger* internal_default_instance() {
    return reinterpret_cast<const Trigger*>(
               &_Trigger_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Trigger& a, Trigger& b) {
    a.Swap(&b);
  }
  inline void Swap(Trigger* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Trigger* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Trigger* New() const final {
    return CreateMaybeMessage<Trigger>(nullptr);
  }

  Trigger* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Trigger>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Trigger& from);
  void MergeFrom(const Trigger& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Trigger* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.Trigger";
  }
  protected:
  explicit Trigger(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlwaysFieldNumber = 1,
  };
  // bool always = 1;
  private:
  bool _internal_has_always() const;
  public:
  void clear_always();
  bool always() const;
  void set_always(bool value);
  private:
  bool _internal_always() const;
  void _internal_set_always(bool value);
  public:

  void clear_trigger();
  TriggerCase trigger_case() const;
  // @@protoc_insertion_point(class_scope:gwm.hdmap.Trigger)
 private:
  class _Internal;
  void set_has_always();

  inline bool has_trigger() const;
  inline void clear_has_trigger();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union TriggerUnion {
    TriggerUnion() {}
    bool always_;
  } trigger_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class LaneRule PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.LaneRule) */ {
 public:
  inline LaneRule() : LaneRule(nullptr) {}
  virtual ~LaneRule();

  LaneRule(const LaneRule& from);
  LaneRule(LaneRule&& from) noexcept
    : LaneRule() {
    *this = ::std::move(from);
  }

  inline LaneRule& operator=(const LaneRule& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneRule& operator=(LaneRule&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LaneRule& default_instance();

  static inline const LaneRule* internal_default_instance() {
    return reinterpret_cast<const LaneRule*>(
               &_LaneRule_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(LaneRule& a, LaneRule& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneRule* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneRule* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LaneRule* New() const final {
    return CreateMaybeMessage<LaneRule>(nullptr);
  }

  LaneRule* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LaneRule>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LaneRule& from);
  void MergeFrom(const LaneRule& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneRule* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.LaneRule";
  }
  protected:
  explicit LaneRule(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef LaneRule_VehicleType VehicleType;
  static constexpr VehicleType DEFAULT =
    LaneRule_VehicleType_DEFAULT;
  static constexpr VehicleType LIGHT_TRUCK =
    LaneRule_VehicleType_LIGHT_TRUCK;
  static inline bool VehicleType_IsValid(int value) {
    return LaneRule_VehicleType_IsValid(value);
  }
  static constexpr VehicleType VehicleType_MIN =
    LaneRule_VehicleType_VehicleType_MIN;
  static constexpr VehicleType VehicleType_MAX =
    LaneRule_VehicleType_VehicleType_MAX;
  static constexpr int VehicleType_ARRAYSIZE =
    LaneRule_VehicleType_VehicleType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  VehicleType_descriptor() {
    return LaneRule_VehicleType_descriptor();
  }
  template<typename T>
  static inline const std::string& VehicleType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, VehicleType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function VehicleType_Name.");
    return LaneRule_VehicleType_Name(enum_t_value);
  }
  static inline bool VehicleType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      VehicleType* value) {
    return LaneRule_VehicleType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTriggerFieldNumber = 2,
    kRuleFieldNumber = 3,
    kVehicleTypeFieldNumber = 1,
  };
  // .gwm.hdmap.Trigger trigger = 2;
  bool has_trigger() const;
  private:
  bool _internal_has_trigger() const;
  public:
  void clear_trigger();
  const ::gwm::hdmap::Trigger& trigger() const;
  ::gwm::hdmap::Trigger* release_trigger();
  ::gwm::hdmap::Trigger* mutable_trigger();
  void set_allocated_trigger(::gwm::hdmap::Trigger* trigger);
  private:
  const ::gwm::hdmap::Trigger& _internal_trigger() const;
  ::gwm::hdmap::Trigger* _internal_mutable_trigger();
  public:
  void unsafe_arena_set_allocated_trigger(
      ::gwm::hdmap::Trigger* trigger);
  ::gwm::hdmap::Trigger* unsafe_arena_release_trigger();

  // .gwm.hdmap.Rule rule = 3;
  bool has_rule() const;
  private:
  bool _internal_has_rule() const;
  public:
  void clear_rule();
  const ::gwm::hdmap::Rule& rule() const;
  ::gwm::hdmap::Rule* release_rule();
  ::gwm::hdmap::Rule* mutable_rule();
  void set_allocated_rule(::gwm::hdmap::Rule* rule);
  private:
  const ::gwm::hdmap::Rule& _internal_rule() const;
  ::gwm::hdmap::Rule* _internal_mutable_rule();
  public:
  void unsafe_arena_set_allocated_rule(
      ::gwm::hdmap::Rule* rule);
  ::gwm::hdmap::Rule* unsafe_arena_release_rule();

  // .gwm.hdmap.LaneRule.VehicleType vehicle_type = 1;
  void clear_vehicle_type();
  ::gwm::hdmap::LaneRule_VehicleType vehicle_type() const;
  void set_vehicle_type(::gwm::hdmap::LaneRule_VehicleType value);
  private:
  ::gwm::hdmap::LaneRule_VehicleType _internal_vehicle_type() const;
  void _internal_set_vehicle_type(::gwm::hdmap::LaneRule_VehicleType value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.LaneRule)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::gwm::hdmap::Trigger* trigger_;
  ::gwm::hdmap::Rule* rule_;
  int vehicle_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class MergeSplit_Merge PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.MergeSplit.Merge) */ {
 public:
  inline MergeSplit_Merge() : MergeSplit_Merge(nullptr) {}
  virtual ~MergeSplit_Merge();

  MergeSplit_Merge(const MergeSplit_Merge& from);
  MergeSplit_Merge(MergeSplit_Merge&& from) noexcept
    : MergeSplit_Merge() {
    *this = ::std::move(from);
  }

  inline MergeSplit_Merge& operator=(const MergeSplit_Merge& from) {
    CopyFrom(from);
    return *this;
  }
  inline MergeSplit_Merge& operator=(MergeSplit_Merge&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MergeSplit_Merge& default_instance();

  static inline const MergeSplit_Merge* internal_default_instance() {
    return reinterpret_cast<const MergeSplit_Merge*>(
               &_MergeSplit_Merge_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(MergeSplit_Merge& a, MergeSplit_Merge& b) {
    a.Swap(&b);
  }
  inline void Swap(MergeSplit_Merge* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MergeSplit_Merge* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MergeSplit_Merge* New() const final {
    return CreateMaybeMessage<MergeSplit_Merge>(nullptr);
  }

  MergeSplit_Merge* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MergeSplit_Merge>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MergeSplit_Merge& from);
  void MergeFrom(const MergeSplit_Merge& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MergeSplit_Merge* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.MergeSplit.Merge";
  }
  protected:
  explicit MergeSplit_Merge(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDirectionFieldNumber = 1,
    kToLaneIdFieldNumber = 2,
  };
  // .gwm.hdmap.MergeSplit.Direction direction = 1;
  void clear_direction();
  ::gwm::hdmap::MergeSplit_Direction direction() const;
  void set_direction(::gwm::hdmap::MergeSplit_Direction value);
  private:
  ::gwm::hdmap::MergeSplit_Direction _internal_direction() const;
  void _internal_set_direction(::gwm::hdmap::MergeSplit_Direction value);
  public:

  // int32 to_lane_id = 2;
  void clear_to_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 to_lane_id() const;
  void set_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_to_lane_id() const;
  void _internal_set_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.MergeSplit.Merge)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int direction_;
  ::PROTOBUF_NAMESPACE_ID::int32 to_lane_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class MergeSplit_Split PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.MergeSplit.Split) */ {
 public:
  inline MergeSplit_Split() : MergeSplit_Split(nullptr) {}
  virtual ~MergeSplit_Split();

  MergeSplit_Split(const MergeSplit_Split& from);
  MergeSplit_Split(MergeSplit_Split&& from) noexcept
    : MergeSplit_Split() {
    *this = ::std::move(from);
  }

  inline MergeSplit_Split& operator=(const MergeSplit_Split& from) {
    CopyFrom(from);
    return *this;
  }
  inline MergeSplit_Split& operator=(MergeSplit_Split&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MergeSplit_Split& default_instance();

  static inline const MergeSplit_Split* internal_default_instance() {
    return reinterpret_cast<const MergeSplit_Split*>(
               &_MergeSplit_Split_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(MergeSplit_Split& a, MergeSplit_Split& b) {
    a.Swap(&b);
  }
  inline void Swap(MergeSplit_Split* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MergeSplit_Split* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MergeSplit_Split* New() const final {
    return CreateMaybeMessage<MergeSplit_Split>(nullptr);
  }

  MergeSplit_Split* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MergeSplit_Split>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MergeSplit_Split& from);
  void MergeFrom(const MergeSplit_Split& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MergeSplit_Split* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.MergeSplit.Split";
  }
  protected:
  explicit MergeSplit_Split(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDirectionFieldNumber = 1,
    kFromLaneIdFieldNumber = 2,
  };
  // .gwm.hdmap.MergeSplit.Direction direction = 1;
  void clear_direction();
  ::gwm::hdmap::MergeSplit_Direction direction() const;
  void set_direction(::gwm::hdmap::MergeSplit_Direction value);
  private:
  ::gwm::hdmap::MergeSplit_Direction _internal_direction() const;
  void _internal_set_direction(::gwm::hdmap::MergeSplit_Direction value);
  public:

  // int32 from_lane_id = 2;
  void clear_from_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 from_lane_id() const;
  void set_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_from_lane_id() const;
  void _internal_set_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.MergeSplit.Split)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int direction_;
  ::PROTOBUF_NAMESPACE_ID::int32 from_lane_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class MergeSplit PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.MergeSplit) */ {
 public:
  inline MergeSplit() : MergeSplit(nullptr) {}
  virtual ~MergeSplit();

  MergeSplit(const MergeSplit& from);
  MergeSplit(MergeSplit&& from) noexcept
    : MergeSplit() {
    *this = ::std::move(from);
  }

  inline MergeSplit& operator=(const MergeSplit& from) {
    CopyFrom(from);
    return *this;
  }
  inline MergeSplit& operator=(MergeSplit&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const MergeSplit& default_instance();

  enum TypeCase {
    kMerge = 1,
    kSplit = 2,
    TYPE_NOT_SET = 0,
  };

  static inline const MergeSplit* internal_default_instance() {
    return reinterpret_cast<const MergeSplit*>(
               &_MergeSplit_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(MergeSplit& a, MergeSplit& b) {
    a.Swap(&b);
  }
  inline void Swap(MergeSplit* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MergeSplit* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline MergeSplit* New() const final {
    return CreateMaybeMessage<MergeSplit>(nullptr);
  }

  MergeSplit* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<MergeSplit>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const MergeSplit& from);
  void MergeFrom(const MergeSplit& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MergeSplit* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.MergeSplit";
  }
  protected:
  explicit MergeSplit(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef MergeSplit_Merge Merge;
  typedef MergeSplit_Split Split;

  typedef MergeSplit_Direction Direction;
  static constexpr Direction UNKNOWN =
    MergeSplit_Direction_UNKNOWN;
  static constexpr Direction LEFT =
    MergeSplit_Direction_LEFT;
  static constexpr Direction RIGHT =
    MergeSplit_Direction_RIGHT;
  static inline bool Direction_IsValid(int value) {
    return MergeSplit_Direction_IsValid(value);
  }
  static constexpr Direction Direction_MIN =
    MergeSplit_Direction_Direction_MIN;
  static constexpr Direction Direction_MAX =
    MergeSplit_Direction_Direction_MAX;
  static constexpr int Direction_ARRAYSIZE =
    MergeSplit_Direction_Direction_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Direction_descriptor() {
    return MergeSplit_Direction_descriptor();
  }
  template<typename T>
  static inline const std::string& Direction_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Direction>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Direction_Name.");
    return MergeSplit_Direction_Name(enum_t_value);
  }
  static inline bool Direction_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Direction* value) {
    return MergeSplit_Direction_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kMergeFieldNumber = 1,
    kSplitFieldNumber = 2,
  };
  // .gwm.hdmap.MergeSplit.Merge merge = 1;
  bool has_merge() const;
  private:
  bool _internal_has_merge() const;
  public:
  void clear_merge();
  const ::gwm::hdmap::MergeSplit_Merge& merge() const;
  ::gwm::hdmap::MergeSplit_Merge* release_merge();
  ::gwm::hdmap::MergeSplit_Merge* mutable_merge();
  void set_allocated_merge(::gwm::hdmap::MergeSplit_Merge* merge);
  private:
  const ::gwm::hdmap::MergeSplit_Merge& _internal_merge() const;
  ::gwm::hdmap::MergeSplit_Merge* _internal_mutable_merge();
  public:
  void unsafe_arena_set_allocated_merge(
      ::gwm::hdmap::MergeSplit_Merge* merge);
  ::gwm::hdmap::MergeSplit_Merge* unsafe_arena_release_merge();

  // .gwm.hdmap.MergeSplit.Split split = 2;
  bool has_split() const;
  private:
  bool _internal_has_split() const;
  public:
  void clear_split();
  const ::gwm::hdmap::MergeSplit_Split& split() const;
  ::gwm::hdmap::MergeSplit_Split* release_split();
  ::gwm::hdmap::MergeSplit_Split* mutable_split();
  void set_allocated_split(::gwm::hdmap::MergeSplit_Split* split);
  private:
  const ::gwm::hdmap::MergeSplit_Split& _internal_split() const;
  ::gwm::hdmap::MergeSplit_Split* _internal_mutable_split();
  public:
  void unsafe_arena_set_allocated_split(
      ::gwm::hdmap::MergeSplit_Split* split);
  ::gwm::hdmap::MergeSplit_Split* unsafe_arena_release_split();

  void clear_type();
  TypeCase type_case() const;
  // @@protoc_insertion_point(class_scope:gwm.hdmap.MergeSplit)
 private:
  class _Internal;
  void set_has_merge();
  void set_has_split();

  inline bool has_type() const;
  inline void clear_has_type();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union TypeUnion {
    TypeUnion() {}
    ::gwm::hdmap::MergeSplit_Merge* merge_;
    ::gwm::hdmap::MergeSplit_Split* split_;
  } type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class NeighborMerge_Ids PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.NeighborMerge.Ids) */ {
 public:
  inline NeighborMerge_Ids() : NeighborMerge_Ids(nullptr) {}
  virtual ~NeighborMerge_Ids();

  NeighborMerge_Ids(const NeighborMerge_Ids& from);
  NeighborMerge_Ids(NeighborMerge_Ids&& from) noexcept
    : NeighborMerge_Ids() {
    *this = ::std::move(from);
  }

  inline NeighborMerge_Ids& operator=(const NeighborMerge_Ids& from) {
    CopyFrom(from);
    return *this;
  }
  inline NeighborMerge_Ids& operator=(NeighborMerge_Ids&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NeighborMerge_Ids& default_instance();

  static inline const NeighborMerge_Ids* internal_default_instance() {
    return reinterpret_cast<const NeighborMerge_Ids*>(
               &_NeighborMerge_Ids_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(NeighborMerge_Ids& a, NeighborMerge_Ids& b) {
    a.Swap(&b);
  }
  inline void Swap(NeighborMerge_Ids* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NeighborMerge_Ids* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NeighborMerge_Ids* New() const final {
    return CreateMaybeMessage<NeighborMerge_Ids>(nullptr);
  }

  NeighborMerge_Ids* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NeighborMerge_Ids>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NeighborMerge_Ids& from);
  void MergeFrom(const NeighborMerge_Ids& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NeighborMerge_Ids* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.NeighborMerge.Ids";
  }
  protected:
  explicit NeighborMerge_Ids(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdsFieldNumber = 1,
  };
  // repeated int32 ids = 1;
  int ids_size() const;
  private:
  int _internal_ids_size() const;
  public:
  void clear_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_ids() const;
  void _internal_add_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_ids();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 ids(int index) const;
  void set_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_ids();

  // @@protoc_insertion_point(class_scope:gwm.hdmap.NeighborMerge.Ids)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > ids_;
  mutable std::atomic<int> _ids_cached_byte_size_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class NeighborMerge PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.NeighborMerge) */ {
 public:
  inline NeighborMerge() : NeighborMerge(nullptr) {}
  virtual ~NeighborMerge();

  NeighborMerge(const NeighborMerge& from);
  NeighborMerge(NeighborMerge&& from) noexcept
    : NeighborMerge() {
    *this = ::std::move(from);
  }

  inline NeighborMerge& operator=(const NeighborMerge& from) {
    CopyFrom(from);
    return *this;
  }
  inline NeighborMerge& operator=(NeighborMerge&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const NeighborMerge& default_instance();

  enum TypeCase {
    kMergeFromLaneIds = 1,
    kMergeToLaneId = 2,
    TYPE_NOT_SET = 0,
  };

  static inline const NeighborMerge* internal_default_instance() {
    return reinterpret_cast<const NeighborMerge*>(
               &_NeighborMerge_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(NeighborMerge& a, NeighborMerge& b) {
    a.Swap(&b);
  }
  inline void Swap(NeighborMerge* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NeighborMerge* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline NeighborMerge* New() const final {
    return CreateMaybeMessage<NeighborMerge>(nullptr);
  }

  NeighborMerge* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<NeighborMerge>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const NeighborMerge& from);
  void MergeFrom(const NeighborMerge& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NeighborMerge* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.NeighborMerge";
  }
  protected:
  explicit NeighborMerge(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef NeighborMerge_Ids Ids;

  // accessors -------------------------------------------------------

  enum : int {
    kSuccessorLaneIdFieldNumber = 3,
    kMergeFromLaneIdsFieldNumber = 1,
    kMergeToLaneIdFieldNumber = 2,
  };
  // int32 successor_lane_id = 3;
  void clear_successor_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 successor_lane_id() const;
  void set_successor_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_successor_lane_id() const;
  void _internal_set_successor_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .gwm.hdmap.NeighborMerge.Ids merge_from_lane_ids = 1;
  bool has_merge_from_lane_ids() const;
  private:
  bool _internal_has_merge_from_lane_ids() const;
  public:
  void clear_merge_from_lane_ids();
  const ::gwm::hdmap::NeighborMerge_Ids& merge_from_lane_ids() const;
  ::gwm::hdmap::NeighborMerge_Ids* release_merge_from_lane_ids();
  ::gwm::hdmap::NeighborMerge_Ids* mutable_merge_from_lane_ids();
  void set_allocated_merge_from_lane_ids(::gwm::hdmap::NeighborMerge_Ids* merge_from_lane_ids);
  private:
  const ::gwm::hdmap::NeighborMerge_Ids& _internal_merge_from_lane_ids() const;
  ::gwm::hdmap::NeighborMerge_Ids* _internal_mutable_merge_from_lane_ids();
  public:
  void unsafe_arena_set_allocated_merge_from_lane_ids(
      ::gwm::hdmap::NeighborMerge_Ids* merge_from_lane_ids);
  ::gwm::hdmap::NeighborMerge_Ids* unsafe_arena_release_merge_from_lane_ids();

  // int32 merge_to_lane_id = 2;
  private:
  bool _internal_has_merge_to_lane_id() const;
  public:
  void clear_merge_to_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 merge_to_lane_id() const;
  void set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_merge_to_lane_id() const;
  void _internal_set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  void clear_type();
  TypeCase type_case() const;
  // @@protoc_insertion_point(class_scope:gwm.hdmap.NeighborMerge)
 private:
  class _Internal;
  void set_has_merge_from_lane_ids();
  void set_has_merge_to_lane_id();

  inline bool has_type() const;
  inline void clear_has_type();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 successor_lane_id_;
  union TypeUnion {
    TypeUnion() {}
    ::gwm::hdmap::NeighborMerge_Ids* merge_from_lane_ids_;
    ::PROTOBUF_NAMESPACE_ID::int32 merge_to_lane_id_;
  } type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::uint32 _oneof_case_[1];

  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class Lane PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.Lane) */ {
 public:
  inline Lane() : Lane(nullptr) {}
  virtual ~Lane();

  Lane(const Lane& from);
  Lane(Lane&& from) noexcept
    : Lane() {
    *this = ::std::move(from);
  }

  inline Lane& operator=(const Lane& from) {
    CopyFrom(from);
    return *this;
  }
  inline Lane& operator=(Lane&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const Lane& default_instance();

  static inline const Lane* internal_default_instance() {
    return reinterpret_cast<const Lane*>(
               &_Lane_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(Lane& a, Lane& b) {
    a.Swap(&b);
  }
  inline void Swap(Lane* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Lane* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline Lane* New() const final {
    return CreateMaybeMessage<Lane>(nullptr);
  }

  Lane* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<Lane>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const Lane& from);
  void MergeFrom(const Lane& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Lane* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.Lane";
  }
  protected:
  explicit Lane(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef Lane_LaneType LaneType;
  static constexpr LaneType UNKNOWN =
    Lane_LaneType_UNKNOWN;
  static constexpr LaneType HIGHWAY =
    Lane_LaneType_HIGHWAY;
  static constexpr LaneType STREET =
    Lane_LaneType_STREET;
  static constexpr LaneType BIDIRECTIONAL =
    Lane_LaneType_BIDIRECTIONAL;
  static constexpr LaneType SHOULDER =
    Lane_LaneType_SHOULDER;
  static constexpr LaneType BIKING =
    Lane_LaneType_BIKING;
  static constexpr LaneType SIDEWALK =
    Lane_LaneType_SIDEWALK;
  static constexpr LaneType RESTRICTED =
    Lane_LaneType_RESTRICTED;
  static constexpr LaneType PARKING =
    Lane_LaneType_PARKING;
  static constexpr LaneType ROADWORK =
    Lane_LaneType_ROADWORK;
  static constexpr LaneType OFFRAMP =
    Lane_LaneType_OFFRAMP;
  static constexpr LaneType ONRAMP =
    Lane_LaneType_ONRAMP;
  static constexpr LaneType BUSLANE =
    Lane_LaneType_BUSLANE;
  static constexpr LaneType LEFTTURNWAITINGAREA =
    Lane_LaneType_LEFTTURNWAITINGAREA;
  static constexpr LaneType PARK =
    Lane_LaneType_PARK;
  static constexpr LaneType ROUNDABOUT =
    Lane_LaneType_ROUNDABOUT;
  static constexpr LaneType RIGHT_TURN_ONLY =
    Lane_LaneType_RIGHT_TURN_ONLY;
  static constexpr LaneType PARK_ON_LANE =
    Lane_LaneType_PARK_ON_LANE;
  static constexpr LaneType DYNAMIC_LANE =
    Lane_LaneType_DYNAMIC_LANE;
  static constexpr LaneType WIDE_LANE =
    Lane_LaneType_WIDE_LANE;
  static constexpr LaneType TIDAL_LANE =
    Lane_LaneType_TIDAL_LANE;
  static constexpr LaneType TRANSFER_LANE =
    Lane_LaneType_TRANSFER_LANE;
  static constexpr LaneType EMERGENCY =
    Lane_LaneType_EMERGENCY;
  static inline bool LaneType_IsValid(int value) {
    return Lane_LaneType_IsValid(value);
  }
  static constexpr LaneType LaneType_MIN =
    Lane_LaneType_LaneType_MIN;
  static constexpr LaneType LaneType_MAX =
    Lane_LaneType_LaneType_MAX;
  static constexpr int LaneType_ARRAYSIZE =
    Lane_LaneType_LaneType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  LaneType_descriptor() {
    return Lane_LaneType_descriptor();
  }
  template<typename T>
  static inline const std::string& LaneType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, LaneType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function LaneType_Name.");
    return Lane_LaneType_Name(enum_t_value);
  }
  static inline bool LaneType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      LaneType* value) {
    return Lane_LaneType_Parse(name, value);
  }

  typedef Lane_LaneTurn LaneTurn;
  static constexpr LaneTurn INVALID =
    Lane_LaneTurn_INVALID;
  static constexpr LaneTurn STRAIGHT =
    Lane_LaneTurn_STRAIGHT;
  static constexpr LaneTurn LEFT =
    Lane_LaneTurn_LEFT;
  static constexpr LaneTurn RIGHT =
    Lane_LaneTurn_RIGHT;
  static constexpr LaneTurn U_TURN_LEFT =
    Lane_LaneTurn_U_TURN_LEFT;
  static constexpr LaneTurn U_TURN_RIGHT =
    Lane_LaneTurn_U_TURN_RIGHT;
  static inline bool LaneTurn_IsValid(int value) {
    return Lane_LaneTurn_IsValid(value);
  }
  static constexpr LaneTurn LaneTurn_MIN =
    Lane_LaneTurn_LaneTurn_MIN;
  static constexpr LaneTurn LaneTurn_MAX =
    Lane_LaneTurn_LaneTurn_MAX;
  static constexpr int LaneTurn_ARRAYSIZE =
    Lane_LaneTurn_LaneTurn_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  LaneTurn_descriptor() {
    return Lane_LaneTurn_descriptor();
  }
  template<typename T>
  static inline const std::string& LaneTurn_Name(T enum_t_value) {
    static_assert(::std::is_same<T, LaneTurn>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function LaneTurn_Name.");
    return Lane_LaneTurn_Name(enum_t_value);
  }
  static inline bool LaneTurn_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      LaneTurn* value) {
    return Lane_LaneTurn_Parse(name, value);
  }

  typedef Lane_LaneDirection LaneDirection;
  static constexpr LaneDirection FORWARD =
    Lane_LaneDirection_FORWARD;
  static constexpr LaneDirection BACKWARD =
    Lane_LaneDirection_BACKWARD;
  static constexpr LaneDirection BIDIRECTION =
    Lane_LaneDirection_BIDIRECTION;
  static inline bool LaneDirection_IsValid(int value) {
    return Lane_LaneDirection_IsValid(value);
  }
  static constexpr LaneDirection LaneDirection_MIN =
    Lane_LaneDirection_LaneDirection_MIN;
  static constexpr LaneDirection LaneDirection_MAX =
    Lane_LaneDirection_LaneDirection_MAX;
  static constexpr int LaneDirection_ARRAYSIZE =
    Lane_LaneDirection_LaneDirection_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  LaneDirection_descriptor() {
    return Lane_LaneDirection_descriptor();
  }
  template<typename T>
  static inline const std::string& LaneDirection_Name(T enum_t_value) {
    static_assert(::std::is_same<T, LaneDirection>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function LaneDirection_Name.");
    return Lane_LaneDirection_Name(enum_t_value);
  }
  static inline bool LaneDirection_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      LaneDirection* value) {
    return Lane_LaneDirection_Parse(name, value);
  }

  typedef Lane_BoundaryDirection BoundaryDirection;
  static constexpr BoundaryDirection SAME =
    Lane_BoundaryDirection_SAME;
  static constexpr BoundaryDirection LEFT_REVERSE =
    Lane_BoundaryDirection_LEFT_REVERSE;
  static constexpr BoundaryDirection RIGHT_REVERSE =
    Lane_BoundaryDirection_RIGHT_REVERSE;
  static constexpr BoundaryDirection BOTH_REVERSE =
    Lane_BoundaryDirection_BOTH_REVERSE;
  static inline bool BoundaryDirection_IsValid(int value) {
    return Lane_BoundaryDirection_IsValid(value);
  }
  static constexpr BoundaryDirection BoundaryDirection_MIN =
    Lane_BoundaryDirection_BoundaryDirection_MIN;
  static constexpr BoundaryDirection BoundaryDirection_MAX =
    Lane_BoundaryDirection_BoundaryDirection_MAX;
  static constexpr int BoundaryDirection_ARRAYSIZE =
    Lane_BoundaryDirection_BoundaryDirection_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  BoundaryDirection_descriptor() {
    return Lane_BoundaryDirection_descriptor();
  }
  template<typename T>
  static inline const std::string& BoundaryDirection_Name(T enum_t_value) {
    static_assert(::std::is_same<T, BoundaryDirection>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function BoundaryDirection_Name.");
    return Lane_BoundaryDirection_Name(enum_t_value);
  }
  static inline bool BoundaryDirection_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      BoundaryDirection* value) {
    return Lane_BoundaryDirection_Parse(name, value);
  }

  typedef Lane_MergeType MergeType;
  static constexpr MergeType NONE =
    Lane_MergeType_NONE;
  static constexpr MergeType FROM_LEFT =
    Lane_MergeType_FROM_LEFT;
  static constexpr MergeType FROM_RIGHT =
    Lane_MergeType_FROM_RIGHT;
  static inline bool MergeType_IsValid(int value) {
    return Lane_MergeType_IsValid(value);
  }
  static constexpr MergeType MergeType_MIN =
    Lane_MergeType_MergeType_MIN;
  static constexpr MergeType MergeType_MAX =
    Lane_MergeType_MergeType_MAX;
  static constexpr int MergeType_ARRAYSIZE =
    Lane_MergeType_MergeType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  MergeType_descriptor() {
    return Lane_MergeType_descriptor();
  }
  template<typename T>
  static inline const std::string& MergeType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, MergeType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function MergeType_Name.");
    return Lane_MergeType_Name(enum_t_value);
  }
  static inline bool MergeType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      MergeType* value) {
    return Lane_MergeType_Parse(name, value);
  }

  typedef Lane_SlopeType SlopeType;
  static constexpr SlopeType UPHILL =
    Lane_SlopeType_UPHILL;
  static constexpr SlopeType DOWNHILL =
    Lane_SlopeType_DOWNHILL;
  static inline bool SlopeType_IsValid(int value) {
    return Lane_SlopeType_IsValid(value);
  }
  static constexpr SlopeType SlopeType_MIN =
    Lane_SlopeType_SlopeType_MIN;
  static constexpr SlopeType SlopeType_MAX =
    Lane_SlopeType_SlopeType_MAX;
  static constexpr int SlopeType_ARRAYSIZE =
    Lane_SlopeType_SlopeType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  SlopeType_descriptor() {
    return Lane_SlopeType_descriptor();
  }
  template<typename T>
  static inline const std::string& SlopeType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, SlopeType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function SlopeType_Name.");
    return Lane_SlopeType_Name(enum_t_value);
  }
  static inline bool SlopeType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      SlopeType* value) {
    return Lane_SlopeType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kOverlapIdFieldNumber = 7,
    kPredecessorIdFieldNumber = 8,
    kSuccessorIdFieldNumber = 9,
    kLeftSampleFieldNumber = 17,
    kRightSampleFieldNumber = 18,
    kLeftRoadSampleFieldNumber = 20,
    kRightRoadSampleFieldNumber = 21,
    kSelfReverseLaneIdFieldNumber = 22,
    kCenterlineSFieldNumber = 24,
    kMergeFromLaneIdFieldNumber = 32,
    kLayersFieldNumber = 33,
    kRoadNameFieldNumber = 37,
    kLaneRulesFieldNumber = 44,
    kLeftBoundaryPlusIdsFieldNumber = 45,
    kRightBoundaryPlusIdsFieldNumber = 46,
    kMergeSplitsFieldNumber = 47,
    kRoadSectionIdsFieldNumber = 48,
    kNeighborMergesFieldNumber = 49,
    kManuallySetPredecessorIdsFieldNumber = 50,
    kManuallySetSuccessorIdsFieldNumber = 51,
    kNextLaneIdsFieldNumber = 52,
    kLastLaneIdsFieldNumber = 53,
    kTypesFieldNumber = 55,
    kLeftBoundaryFieldNumber = 3,
    kRightBoundaryFieldNumber = 4,
    kCenterlineFieldNumber = 23,
    kIdFieldNumber = 1,
    kSpeedLimitFieldNumber = 6,
    kLengthFieldNumber = 5,
    kLeftNeighborForwardLaneIdFieldNumber = 10,
    kRightNeighborForwardLaneIdFieldNumber = 11,
    kTypeFieldNumber = 12,
    kTurnFieldNumber = 13,
    kLeftNeighborReverseLaneIdFieldNumber = 14,
    kRightNeighborReverseLaneIdFieldNumber = 15,
    kJunctionIdFieldNumber = 16,
    kDirectionFieldNumber = 19,
    kLeftBoundaryIdFieldNumber = 25,
    kRightBoundaryIdFieldNumber = 26,
    kBoundaryDirectionFieldNumber = 27,
    kMergeFieldNumber = 30,
    kCostFieldNumber = 29,
    kMergeToLaneIdFieldNumber = 31,
    kBidiCopyFromIdFieldNumber = 34,
    kManuallySetLeftNeighborForwardLaneIdFieldNumber = 35,
    kManuallySetRightNeighborForwardLaneIdFieldNumber = 36,
    kIsDisabledFieldNumber = 38,
    kIsVirtualFieldNumber = 39,
    kTruckSpeedLimitFieldNumber = 40,
    kMinWidthFieldNumber = 41,
    kMaxCurvatureFieldNumber = 42,
    kHeadingDiffFieldNumber = 43,
    kSlopeTypeFieldNumber = 54,
  };
  // repeated int32 overlap_id = 7;
  int overlap_id_size() const;
  private:
  int _internal_overlap_id_size() const;
  public:
  void clear_overlap_id();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_overlap_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_overlap_id() const;
  void _internal_add_overlap_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_overlap_id();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 overlap_id(int index) const;
  void set_overlap_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_overlap_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      overlap_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_overlap_id();

  // repeated int32 predecessor_id = 8;
  int predecessor_id_size() const;
  private:
  int _internal_predecessor_id_size() const;
  public:
  void clear_predecessor_id();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_predecessor_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_predecessor_id() const;
  void _internal_add_predecessor_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_predecessor_id();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 predecessor_id(int index) const;
  void set_predecessor_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_predecessor_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      predecessor_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_predecessor_id();

  // repeated int32 successor_id = 9;
  int successor_id_size() const;
  private:
  int _internal_successor_id_size() const;
  public:
  void clear_successor_id();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_successor_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_successor_id() const;
  void _internal_add_successor_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_successor_id();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 successor_id(int index) const;
  void set_successor_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_successor_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      successor_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_successor_id();

  // repeated .gwm.hdmap.LaneSampleAssociation left_sample = 17;
  int left_sample_size() const;
  private:
  int _internal_left_sample_size() const;
  public:
  void clear_left_sample();
  ::gwm::hdmap::LaneSampleAssociation* mutable_left_sample(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
      mutable_left_sample();
  private:
  const ::gwm::hdmap::LaneSampleAssociation& _internal_left_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* _internal_add_left_sample();
  public:
  const ::gwm::hdmap::LaneSampleAssociation& left_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* add_left_sample();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
      left_sample() const;

  // repeated .gwm.hdmap.LaneSampleAssociation right_sample = 18;
  int right_sample_size() const;
  private:
  int _internal_right_sample_size() const;
  public:
  void clear_right_sample();
  ::gwm::hdmap::LaneSampleAssociation* mutable_right_sample(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
      mutable_right_sample();
  private:
  const ::gwm::hdmap::LaneSampleAssociation& _internal_right_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* _internal_add_right_sample();
  public:
  const ::gwm::hdmap::LaneSampleAssociation& right_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* add_right_sample();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
      right_sample() const;

  // repeated .gwm.hdmap.LaneSampleAssociation left_road_sample = 20;
  int left_road_sample_size() const;
  private:
  int _internal_left_road_sample_size() const;
  public:
  void clear_left_road_sample();
  ::gwm::hdmap::LaneSampleAssociation* mutable_left_road_sample(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
      mutable_left_road_sample();
  private:
  const ::gwm::hdmap::LaneSampleAssociation& _internal_left_road_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* _internal_add_left_road_sample();
  public:
  const ::gwm::hdmap::LaneSampleAssociation& left_road_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* add_left_road_sample();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
      left_road_sample() const;

  // repeated .gwm.hdmap.LaneSampleAssociation right_road_sample = 21;
  int right_road_sample_size() const;
  private:
  int _internal_right_road_sample_size() const;
  public:
  void clear_right_road_sample();
  ::gwm::hdmap::LaneSampleAssociation* mutable_right_road_sample(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
      mutable_right_road_sample();
  private:
  const ::gwm::hdmap::LaneSampleAssociation& _internal_right_road_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* _internal_add_right_road_sample();
  public:
  const ::gwm::hdmap::LaneSampleAssociation& right_road_sample(int index) const;
  ::gwm::hdmap::LaneSampleAssociation* add_right_road_sample();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
      right_road_sample() const;

  // repeated int32 self_reverse_lane_id = 22;
  int self_reverse_lane_id_size() const;
  private:
  int _internal_self_reverse_lane_id_size() const;
  public:
  void clear_self_reverse_lane_id();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_self_reverse_lane_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_self_reverse_lane_id() const;
  void _internal_add_self_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_self_reverse_lane_id();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 self_reverse_lane_id(int index) const;
  void set_self_reverse_lane_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_self_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      self_reverse_lane_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_self_reverse_lane_id();

  // repeated float centerline_s = 24 [packed = true];
  int centerline_s_size() const;
  private:
  int _internal_centerline_s_size() const;
  public:
  void clear_centerline_s();
  private:
  float _internal_centerline_s(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_centerline_s() const;
  void _internal_add_centerline_s(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_centerline_s();
  public:
  float centerline_s(int index) const;
  void set_centerline_s(int index, float value);
  void add_centerline_s(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      centerline_s() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_centerline_s();

  // repeated int32 merge_from_lane_id = 32;
  int merge_from_lane_id_size() const;
  private:
  int _internal_merge_from_lane_id_size() const;
  public:
  void clear_merge_from_lane_id();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_merge_from_lane_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_merge_from_lane_id() const;
  void _internal_add_merge_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_merge_from_lane_id();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 merge_from_lane_id(int index) const;
  void set_merge_from_lane_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_merge_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      merge_from_lane_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_merge_from_lane_id();

  // repeated int32 layers = 33;
  int layers_size() const;
  private:
  int _internal_layers_size() const;
  public:
  void clear_layers();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_layers(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_layers() const;
  void _internal_add_layers(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_layers();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 layers(int index) const;
  void set_layers(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_layers(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      layers() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_layers();

  // repeated string road_name = 37;
  int road_name_size() const;
  private:
  int _internal_road_name_size() const;
  public:
  void clear_road_name();
  const std::string& road_name(int index) const;
  std::string* mutable_road_name(int index);
  void set_road_name(int index, const std::string& value);
  void set_road_name(int index, std::string&& value);
  void set_road_name(int index, const char* value);
  void set_road_name(int index, const char* value, size_t size);
  std::string* add_road_name();
  void add_road_name(const std::string& value);
  void add_road_name(std::string&& value);
  void add_road_name(const char* value);
  void add_road_name(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& road_name() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_road_name();
  private:
  const std::string& _internal_road_name(int index) const;
  std::string* _internal_add_road_name();
  public:

  // repeated .gwm.hdmap.LaneRule lane_rules = 44;
  int lane_rules_size() const;
  private:
  int _internal_lane_rules_size() const;
  public:
  void clear_lane_rules();
  ::gwm::hdmap::LaneRule* mutable_lane_rules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneRule >*
      mutable_lane_rules();
  private:
  const ::gwm::hdmap::LaneRule& _internal_lane_rules(int index) const;
  ::gwm::hdmap::LaneRule* _internal_add_lane_rules();
  public:
  const ::gwm::hdmap::LaneRule& lane_rules(int index) const;
  ::gwm::hdmap::LaneRule* add_lane_rules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneRule >&
      lane_rules() const;

  // repeated int32 left_boundary_plus_ids = 45;
  int left_boundary_plus_ids_size() const;
  private:
  int _internal_left_boundary_plus_ids_size() const;
  public:
  void clear_left_boundary_plus_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_left_boundary_plus_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_left_boundary_plus_ids() const;
  void _internal_add_left_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_left_boundary_plus_ids();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 left_boundary_plus_ids(int index) const;
  void set_left_boundary_plus_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_left_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      left_boundary_plus_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_left_boundary_plus_ids();

  // repeated int32 right_boundary_plus_ids = 46;
  int right_boundary_plus_ids_size() const;
  private:
  int _internal_right_boundary_plus_ids_size() const;
  public:
  void clear_right_boundary_plus_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_right_boundary_plus_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_right_boundary_plus_ids() const;
  void _internal_add_right_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_right_boundary_plus_ids();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 right_boundary_plus_ids(int index) const;
  void set_right_boundary_plus_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_right_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      right_boundary_plus_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_right_boundary_plus_ids();

  // repeated .gwm.hdmap.MergeSplit merge_splits = 47;
  int merge_splits_size() const;
  private:
  int _internal_merge_splits_size() const;
  public:
  void clear_merge_splits();
  ::gwm::hdmap::MergeSplit* mutable_merge_splits(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::MergeSplit >*
      mutable_merge_splits();
  private:
  const ::gwm::hdmap::MergeSplit& _internal_merge_splits(int index) const;
  ::gwm::hdmap::MergeSplit* _internal_add_merge_splits();
  public:
  const ::gwm::hdmap::MergeSplit& merge_splits(int index) const;
  ::gwm::hdmap::MergeSplit* add_merge_splits();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::MergeSplit >&
      merge_splits() const;

  // repeated int32 road_section_ids = 48;
  int road_section_ids_size() const;
  private:
  int _internal_road_section_ids_size() const;
  public:
  void clear_road_section_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_road_section_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_road_section_ids() const;
  void _internal_add_road_section_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_road_section_ids();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 road_section_ids(int index) const;
  void set_road_section_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_road_section_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      road_section_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_road_section_ids();

  // repeated .gwm.hdmap.NeighborMerge neighbor_merges = 49;
  int neighbor_merges_size() const;
  private:
  int _internal_neighbor_merges_size() const;
  public:
  void clear_neighbor_merges();
  ::gwm::hdmap::NeighborMerge* mutable_neighbor_merges(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::NeighborMerge >*
      mutable_neighbor_merges();
  private:
  const ::gwm::hdmap::NeighborMerge& _internal_neighbor_merges(int index) const;
  ::gwm::hdmap::NeighborMerge* _internal_add_neighbor_merges();
  public:
  const ::gwm::hdmap::NeighborMerge& neighbor_merges(int index) const;
  ::gwm::hdmap::NeighborMerge* add_neighbor_merges();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::NeighborMerge >&
      neighbor_merges() const;

  // repeated int32 manually_set_predecessor_ids = 50 [deprecated = true];
  PROTOBUF_DEPRECATED int manually_set_predecessor_ids_size() const;
  private:
  int _internal_manually_set_predecessor_ids_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_manually_set_predecessor_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_manually_set_predecessor_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_manually_set_predecessor_ids() const;
  void _internal_add_manually_set_predecessor_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_manually_set_predecessor_ids();
  public:
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int32 manually_set_predecessor_ids(int index) const;
  PROTOBUF_DEPRECATED void set_manually_set_predecessor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  PROTOBUF_DEPRECATED void add_manually_set_predecessor_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      manually_set_predecessor_ids() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_manually_set_predecessor_ids();

  // repeated int32 manually_set_successor_ids = 51 [deprecated = true];
  PROTOBUF_DEPRECATED int manually_set_successor_ids_size() const;
  private:
  int _internal_manually_set_successor_ids_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_manually_set_successor_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_manually_set_successor_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_manually_set_successor_ids() const;
  void _internal_add_manually_set_successor_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_manually_set_successor_ids();
  public:
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::int32 manually_set_successor_ids(int index) const;
  PROTOBUF_DEPRECATED void set_manually_set_successor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  PROTOBUF_DEPRECATED void add_manually_set_successor_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      manually_set_successor_ids() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_manually_set_successor_ids();

  // repeated int32 next_lane_ids = 52;
  int next_lane_ids_size() const;
  private:
  int _internal_next_lane_ids_size() const;
  public:
  void clear_next_lane_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_next_lane_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_next_lane_ids() const;
  void _internal_add_next_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_next_lane_ids();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 next_lane_ids(int index) const;
  void set_next_lane_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_next_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      next_lane_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_next_lane_ids();

  // repeated int32 last_lane_ids = 53;
  int last_lane_ids_size() const;
  private:
  int _internal_last_lane_ids_size() const;
  public:
  void clear_last_lane_ids();
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_last_lane_ids(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      _internal_last_lane_ids() const;
  void _internal_add_last_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      _internal_mutable_last_lane_ids();
  public:
  ::PROTOBUF_NAMESPACE_ID::int32 last_lane_ids(int index) const;
  void set_last_lane_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value);
  void add_last_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
      last_lane_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
      mutable_last_lane_ids();

  // repeated .gwm.hdmap.Lane.LaneType types = 55;
  int types_size() const;
  private:
  int _internal_types_size() const;
  public:
  void clear_types();
  private:
  ::gwm::hdmap::Lane_LaneType _internal_types(int index) const;
  void _internal_add_types(::gwm::hdmap::Lane_LaneType value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_types();
  public:
  ::gwm::hdmap::Lane_LaneType types(int index) const;
  void set_types(int index, ::gwm::hdmap::Lane_LaneType value);
  void add_types(::gwm::hdmap::Lane_LaneType value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& types() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_types();

  // .gwm.hdmap.LaneBoundary left_boundary = 3;
  bool has_left_boundary() const;
  private:
  bool _internal_has_left_boundary() const;
  public:
  void clear_left_boundary();
  const ::gwm::hdmap::LaneBoundary& left_boundary() const;
  ::gwm::hdmap::LaneBoundary* release_left_boundary();
  ::gwm::hdmap::LaneBoundary* mutable_left_boundary();
  void set_allocated_left_boundary(::gwm::hdmap::LaneBoundary* left_boundary);
  private:
  const ::gwm::hdmap::LaneBoundary& _internal_left_boundary() const;
  ::gwm::hdmap::LaneBoundary* _internal_mutable_left_boundary();
  public:
  void unsafe_arena_set_allocated_left_boundary(
      ::gwm::hdmap::LaneBoundary* left_boundary);
  ::gwm::hdmap::LaneBoundary* unsafe_arena_release_left_boundary();

  // .gwm.hdmap.LaneBoundary right_boundary = 4;
  bool has_right_boundary() const;
  private:
  bool _internal_has_right_boundary() const;
  public:
  void clear_right_boundary();
  const ::gwm::hdmap::LaneBoundary& right_boundary() const;
  ::gwm::hdmap::LaneBoundary* release_right_boundary();
  ::gwm::hdmap::LaneBoundary* mutable_right_boundary();
  void set_allocated_right_boundary(::gwm::hdmap::LaneBoundary* right_boundary);
  private:
  const ::gwm::hdmap::LaneBoundary& _internal_right_boundary() const;
  ::gwm::hdmap::LaneBoundary* _internal_mutable_right_boundary();
  public:
  void unsafe_arena_set_allocated_right_boundary(
      ::gwm::hdmap::LaneBoundary* right_boundary);
  ::gwm::hdmap::LaneBoundary* unsafe_arena_release_right_boundary();

  // .gwm.common.Polyline centerline = 23;
  bool has_centerline() const;
  private:
  bool _internal_has_centerline() const;
  public:
  void clear_centerline();
  const ::gwm::common::Polyline& centerline() const;
  ::gwm::common::Polyline* release_centerline();
  ::gwm::common::Polyline* mutable_centerline();
  void set_allocated_centerline(::gwm::common::Polyline* centerline);
  private:
  const ::gwm::common::Polyline& _internal_centerline() const;
  ::gwm::common::Polyline* _internal_mutable_centerline();
  public:
  void unsafe_arena_set_allocated_centerline(
      ::gwm::common::Polyline* centerline);
  ::gwm::common::Polyline* unsafe_arena_release_centerline();

  // int32 id = 1;
  void clear_id();
  ::PROTOBUF_NAMESPACE_ID::int32 id() const;
  void set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_id() const;
  void _internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // float speed_limit = 6;
  void clear_speed_limit();
  float speed_limit() const;
  void set_speed_limit(float value);
  private:
  float _internal_speed_limit() const;
  void _internal_set_speed_limit(float value);
  public:

  // double length = 5;
  void clear_length();
  double length() const;
  void set_length(double value);
  private:
  double _internal_length() const;
  void _internal_set_length(double value);
  public:

  // int32 left_neighbor_forward_lane_id = 10;
  void clear_left_neighbor_forward_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 left_neighbor_forward_lane_id() const;
  void set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_left_neighbor_forward_lane_id() const;
  void _internal_set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 right_neighbor_forward_lane_id = 11;
  void clear_right_neighbor_forward_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 right_neighbor_forward_lane_id() const;
  void set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_right_neighbor_forward_lane_id() const;
  void _internal_set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .gwm.hdmap.Lane.LaneType type = 12;
  void clear_type();
  ::gwm::hdmap::Lane_LaneType type() const;
  void set_type(::gwm::hdmap::Lane_LaneType value);
  private:
  ::gwm::hdmap::Lane_LaneType _internal_type() const;
  void _internal_set_type(::gwm::hdmap::Lane_LaneType value);
  public:

  // .gwm.hdmap.Lane.LaneTurn turn = 13;
  void clear_turn();
  ::gwm::hdmap::Lane_LaneTurn turn() const;
  void set_turn(::gwm::hdmap::Lane_LaneTurn value);
  private:
  ::gwm::hdmap::Lane_LaneTurn _internal_turn() const;
  void _internal_set_turn(::gwm::hdmap::Lane_LaneTurn value);
  public:

  // int32 left_neighbor_reverse_lane_id = 14;
  void clear_left_neighbor_reverse_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 left_neighbor_reverse_lane_id() const;
  void set_left_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_left_neighbor_reverse_lane_id() const;
  void _internal_set_left_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 right_neighbor_reverse_lane_id = 15;
  void clear_right_neighbor_reverse_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 right_neighbor_reverse_lane_id() const;
  void set_right_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_right_neighbor_reverse_lane_id() const;
  void _internal_set_right_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 junction_id = 16;
  void clear_junction_id();
  ::PROTOBUF_NAMESPACE_ID::int32 junction_id() const;
  void set_junction_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_junction_id() const;
  void _internal_set_junction_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .gwm.hdmap.Lane.LaneDirection direction = 19;
  void clear_direction();
  ::gwm::hdmap::Lane_LaneDirection direction() const;
  void set_direction(::gwm::hdmap::Lane_LaneDirection value);
  private:
  ::gwm::hdmap::Lane_LaneDirection _internal_direction() const;
  void _internal_set_direction(::gwm::hdmap::Lane_LaneDirection value);
  public:

  // int32 left_boundary_id = 25;
  void clear_left_boundary_id();
  ::PROTOBUF_NAMESPACE_ID::int32 left_boundary_id() const;
  void set_left_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_left_boundary_id() const;
  void _internal_set_left_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 right_boundary_id = 26;
  void clear_right_boundary_id();
  ::PROTOBUF_NAMESPACE_ID::int32 right_boundary_id() const;
  void set_right_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_right_boundary_id() const;
  void _internal_set_right_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .gwm.hdmap.Lane.BoundaryDirection boundary_direction = 27;
  void clear_boundary_direction();
  ::gwm::hdmap::Lane_BoundaryDirection boundary_direction() const;
  void set_boundary_direction(::gwm::hdmap::Lane_BoundaryDirection value);
  private:
  ::gwm::hdmap::Lane_BoundaryDirection _internal_boundary_direction() const;
  void _internal_set_boundary_direction(::gwm::hdmap::Lane_BoundaryDirection value);
  public:

  // .gwm.hdmap.Lane.MergeType merge = 30;
  void clear_merge();
  ::gwm::hdmap::Lane_MergeType merge() const;
  void set_merge(::gwm::hdmap::Lane_MergeType value);
  private:
  ::gwm::hdmap::Lane_MergeType _internal_merge() const;
  void _internal_set_merge(::gwm::hdmap::Lane_MergeType value);
  public:

  // double cost = 29;
  void clear_cost();
  double cost() const;
  void set_cost(double value);
  private:
  double _internal_cost() const;
  void _internal_set_cost(double value);
  public:

  // int32 merge_to_lane_id = 31;
  void clear_merge_to_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 merge_to_lane_id() const;
  void set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_merge_to_lane_id() const;
  void _internal_set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 bidi_copy_from_id = 34;
  void clear_bidi_copy_from_id();
  ::PROTOBUF_NAMESPACE_ID::int32 bidi_copy_from_id() const;
  void set_bidi_copy_from_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_bidi_copy_from_id() const;
  void _internal_set_bidi_copy_from_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 manually_set_left_neighbor_forward_lane_id = 35;
  void clear_manually_set_left_neighbor_forward_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 manually_set_left_neighbor_forward_lane_id() const;
  void set_manually_set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_manually_set_left_neighbor_forward_lane_id() const;
  void _internal_set_manually_set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 manually_set_right_neighbor_forward_lane_id = 36;
  void clear_manually_set_right_neighbor_forward_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 manually_set_right_neighbor_forward_lane_id() const;
  void set_manually_set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_manually_set_right_neighbor_forward_lane_id() const;
  void _internal_set_manually_set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // bool is_disabled = 38;
  void clear_is_disabled();
  bool is_disabled() const;
  void set_is_disabled(bool value);
  private:
  bool _internal_is_disabled() const;
  void _internal_set_is_disabled(bool value);
  public:

  // bool is_virtual = 39 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_is_virtual();
  PROTOBUF_DEPRECATED bool is_virtual() const;
  PROTOBUF_DEPRECATED void set_is_virtual(bool value);
  private:
  bool _internal_is_virtual() const;
  void _internal_set_is_virtual(bool value);
  public:

  // float truck_speed_limit = 40;
  void clear_truck_speed_limit();
  float truck_speed_limit() const;
  void set_truck_speed_limit(float value);
  private:
  float _internal_truck_speed_limit() const;
  void _internal_set_truck_speed_limit(float value);
  public:

  // float min_width = 41;
  void clear_min_width();
  float min_width() const;
  void set_min_width(float value);
  private:
  float _internal_min_width() const;
  void _internal_set_min_width(float value);
  public:

  // float max_curvature = 42;
  void clear_max_curvature();
  float max_curvature() const;
  void set_max_curvature(float value);
  private:
  float _internal_max_curvature() const;
  void _internal_set_max_curvature(float value);
  public:

  // float heading_diff = 43;
  void clear_heading_diff();
  float heading_diff() const;
  void set_heading_diff(float value);
  private:
  float _internal_heading_diff() const;
  void _internal_set_heading_diff(float value);
  public:

  // .gwm.hdmap.Lane.SlopeType slope_type = 54;
  void clear_slope_type();
  ::gwm::hdmap::Lane_SlopeType slope_type() const;
  void set_slope_type(::gwm::hdmap::Lane_SlopeType value);
  private:
  ::gwm::hdmap::Lane_SlopeType _internal_slope_type() const;
  void _internal_set_slope_type(::gwm::hdmap::Lane_SlopeType value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.Lane)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > overlap_id_;
  mutable std::atomic<int> _overlap_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > predecessor_id_;
  mutable std::atomic<int> _predecessor_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > successor_id_;
  mutable std::atomic<int> _successor_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation > left_sample_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation > right_sample_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation > left_road_sample_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation > right_road_sample_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > self_reverse_lane_id_;
  mutable std::atomic<int> _self_reverse_lane_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > centerline_s_;
  mutable std::atomic<int> _centerline_s_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > merge_from_lane_id_;
  mutable std::atomic<int> _merge_from_lane_id_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > layers_;
  mutable std::atomic<int> _layers_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> road_name_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneRule > lane_rules_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > left_boundary_plus_ids_;
  mutable std::atomic<int> _left_boundary_plus_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > right_boundary_plus_ids_;
  mutable std::atomic<int> _right_boundary_plus_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::MergeSplit > merge_splits_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > road_section_ids_;
  mutable std::atomic<int> _road_section_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::NeighborMerge > neighbor_merges_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > manually_set_predecessor_ids_;
  mutable std::atomic<int> _manually_set_predecessor_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > manually_set_successor_ids_;
  mutable std::atomic<int> _manually_set_successor_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > next_lane_ids_;
  mutable std::atomic<int> _next_lane_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 > last_lane_ids_;
  mutable std::atomic<int> _last_lane_ids_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> types_;
  mutable std::atomic<int> _types_cached_byte_size_;
  ::gwm::hdmap::LaneBoundary* left_boundary_;
  ::gwm::hdmap::LaneBoundary* right_boundary_;
  ::gwm::common::Polyline* centerline_;
  ::PROTOBUF_NAMESPACE_ID::int32 id_;
  float speed_limit_;
  double length_;
  ::PROTOBUF_NAMESPACE_ID::int32 left_neighbor_forward_lane_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 right_neighbor_forward_lane_id_;
  int type_;
  int turn_;
  ::PROTOBUF_NAMESPACE_ID::int32 left_neighbor_reverse_lane_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 right_neighbor_reverse_lane_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 junction_id_;
  int direction_;
  ::PROTOBUF_NAMESPACE_ID::int32 left_boundary_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 right_boundary_id_;
  int boundary_direction_;
  int merge_;
  double cost_;
  ::PROTOBUF_NAMESPACE_ID::int32 merge_to_lane_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 bidi_copy_from_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 manually_set_left_neighbor_forward_lane_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 manually_set_right_neighbor_forward_lane_id_;
  bool is_disabled_;
  bool is_virtual_;
  float truck_speed_limit_;
  float min_width_;
  float max_curvature_;
  float heading_diff_;
  int slope_type_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class EgoLaneInfo PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.EgoLaneInfo) */ {
 public:
  inline EgoLaneInfo() : EgoLaneInfo(nullptr) {}
  virtual ~EgoLaneInfo();

  EgoLaneInfo(const EgoLaneInfo& from);
  EgoLaneInfo(EgoLaneInfo&& from) noexcept
    : EgoLaneInfo() {
    *this = ::std::move(from);
  }

  inline EgoLaneInfo& operator=(const EgoLaneInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline EgoLaneInfo& operator=(EgoLaneInfo&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const EgoLaneInfo& default_instance();

  static inline const EgoLaneInfo* internal_default_instance() {
    return reinterpret_cast<const EgoLaneInfo*>(
               &_EgoLaneInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(EgoLaneInfo& a, EgoLaneInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(EgoLaneInfo* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EgoLaneInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline EgoLaneInfo* New() const final {
    return CreateMaybeMessage<EgoLaneInfo>(nullptr);
  }

  EgoLaneInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<EgoLaneInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const EgoLaneInfo& from);
  void MergeFrom(const EgoLaneInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(EgoLaneInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.EgoLaneInfo";
  }
  protected:
  explicit EgoLaneInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProjectingPointFieldNumber = 7,
    kLaneIdFieldNumber = 1,
    kLaneIndexFieldNumber = 2,
    kPrevCoordinateIndexFieldNumber = 3,
    kOffsetLengthFromPrevPointFieldNumber = 4,
    kOffsetLengthFromStartPointFieldNumber = 5,
    kDistanceToLineFieldNumber = 6,
  };
  // .gwm.common.Point3D projecting_point = 7;
  bool has_projecting_point() const;
  private:
  bool _internal_has_projecting_point() const;
  public:
  void clear_projecting_point();
  const ::gwm::common::Point3D& projecting_point() const;
  ::gwm::common::Point3D* release_projecting_point();
  ::gwm::common::Point3D* mutable_projecting_point();
  void set_allocated_projecting_point(::gwm::common::Point3D* projecting_point);
  private:
  const ::gwm::common::Point3D& _internal_projecting_point() const;
  ::gwm::common::Point3D* _internal_mutable_projecting_point();
  public:
  void unsafe_arena_set_allocated_projecting_point(
      ::gwm::common::Point3D* projecting_point);
  ::gwm::common::Point3D* unsafe_arena_release_projecting_point();

  // int32 lane_id = 1;
  void clear_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 lane_id() const;
  void set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_lane_id() const;
  void _internal_set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 lane_index = 2;
  void clear_lane_index();
  ::PROTOBUF_NAMESPACE_ID::int32 lane_index() const;
  void set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_lane_index() const;
  void _internal_set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 prev_coordinate_index = 3;
  void clear_prev_coordinate_index();
  ::PROTOBUF_NAMESPACE_ID::int32 prev_coordinate_index() const;
  void set_prev_coordinate_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_prev_coordinate_index() const;
  void _internal_set_prev_coordinate_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // float offset_length_from_prev_point = 4;
  void clear_offset_length_from_prev_point();
  float offset_length_from_prev_point() const;
  void set_offset_length_from_prev_point(float value);
  private:
  float _internal_offset_length_from_prev_point() const;
  void _internal_set_offset_length_from_prev_point(float value);
  public:

  // float offset_length_from_start_point = 5;
  void clear_offset_length_from_start_point();
  float offset_length_from_start_point() const;
  void set_offset_length_from_start_point(float value);
  private:
  float _internal_offset_length_from_start_point() const;
  void _internal_set_offset_length_from_start_point(float value);
  public:

  // float distance_to_line = 6;
  void clear_distance_to_line();
  float distance_to_line() const;
  void set_distance_to_line(float value);
  private:
  float _internal_distance_to_line() const;
  void _internal_set_distance_to_line(float value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.EgoLaneInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::gwm::common::Point3D* projecting_point_;
  ::PROTOBUF_NAMESPACE_ID::int32 lane_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 lane_index_;
  ::PROTOBUF_NAMESPACE_ID::int32 prev_coordinate_index_;
  float offset_length_from_prev_point_;
  float offset_length_from_start_point_;
  float distance_to_line_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class LanePassableInfo PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.LanePassableInfo) */ {
 public:
  inline LanePassableInfo() : LanePassableInfo(nullptr) {}
  virtual ~LanePassableInfo();

  LanePassableInfo(const LanePassableInfo& from);
  LanePassableInfo(LanePassableInfo&& from) noexcept
    : LanePassableInfo() {
    *this = ::std::move(from);
  }

  inline LanePassableInfo& operator=(const LanePassableInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline LanePassableInfo& operator=(LanePassableInfo&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LanePassableInfo& default_instance();

  static inline const LanePassableInfo* internal_default_instance() {
    return reinterpret_cast<const LanePassableInfo*>(
               &_LanePassableInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(LanePassableInfo& a, LanePassableInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(LanePassableInfo* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LanePassableInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LanePassableInfo* New() const final {
    return CreateMaybeMessage<LanePassableInfo>(nullptr);
  }

  LanePassableInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LanePassableInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LanePassableInfo& from);
  void MergeFrom(const LanePassableInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LanePassableInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.LanePassableInfo";
  }
  protected:
  explicit LanePassableInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  typedef LanePassableInfo_LaneAccessibility LaneAccessibility;
  static constexpr LaneAccessibility DEFAULT =
    LanePassableInfo_LaneAccessibility_DEFAULT;
  static constexpr LaneAccessibility RECOMMEND =
    LanePassableInfo_LaneAccessibility_RECOMMEND;
  static constexpr LaneAccessibility NOT_RECOMMEND =
    LanePassableInfo_LaneAccessibility_NOT_RECOMMEND;
  static inline bool LaneAccessibility_IsValid(int value) {
    return LanePassableInfo_LaneAccessibility_IsValid(value);
  }
  static constexpr LaneAccessibility LaneAccessibility_MIN =
    LanePassableInfo_LaneAccessibility_LaneAccessibility_MIN;
  static constexpr LaneAccessibility LaneAccessibility_MAX =
    LanePassableInfo_LaneAccessibility_LaneAccessibility_MAX;
  static constexpr int LaneAccessibility_ARRAYSIZE =
    LanePassableInfo_LaneAccessibility_LaneAccessibility_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  LaneAccessibility_descriptor() {
    return LanePassableInfo_LaneAccessibility_descriptor();
  }
  template<typename T>
  static inline const std::string& LaneAccessibility_Name(T enum_t_value) {
    static_assert(::std::is_same<T, LaneAccessibility>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function LaneAccessibility_Name.");
    return LanePassableInfo_LaneAccessibility_Name(enum_t_value);
  }
  static inline bool LaneAccessibility_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      LaneAccessibility* value) {
    return LanePassableInfo_LaneAccessibility_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kLaneIdFieldNumber = 1,
    kLaneIndexFieldNumber = 2,
    kLaneAccessibilityFieldNumber = 3,
  };
  // int32 lane_id = 1;
  void clear_lane_id();
  ::PROTOBUF_NAMESPACE_ID::int32 lane_id() const;
  void set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_lane_id() const;
  void _internal_set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // int32 lane_index = 2;
  void clear_lane_index();
  ::PROTOBUF_NAMESPACE_ID::int32 lane_index() const;
  void set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  private:
  ::PROTOBUF_NAMESPACE_ID::int32 _internal_lane_index() const;
  void _internal_set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value);
  public:

  // .gwm.hdmap.LanePassableInfo.LaneAccessibility lane_accessibility = 3;
  void clear_lane_accessibility();
  ::gwm::hdmap::LanePassableInfo_LaneAccessibility lane_accessibility() const;
  void set_lane_accessibility(::gwm::hdmap::LanePassableInfo_LaneAccessibility value);
  private:
  ::gwm::hdmap::LanePassableInfo_LaneAccessibility _internal_lane_accessibility() const;
  void _internal_set_lane_accessibility(::gwm::hdmap::LanePassableInfo_LaneAccessibility value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.LanePassableInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::int32 lane_id_;
  ::PROTOBUF_NAMESPACE_ID::int32 lane_index_;
  int lane_accessibility_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class LaneGroup PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.LaneGroup) */ {
 public:
  inline LaneGroup() : LaneGroup(nullptr) {}
  virtual ~LaneGroup();

  LaneGroup(const LaneGroup& from);
  LaneGroup(LaneGroup&& from) noexcept
    : LaneGroup() {
    *this = ::std::move(from);
  }

  inline LaneGroup& operator=(const LaneGroup& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaneGroup& operator=(LaneGroup&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LaneGroup& default_instance();

  static inline const LaneGroup* internal_default_instance() {
    return reinterpret_cast<const LaneGroup*>(
               &_LaneGroup_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(LaneGroup& a, LaneGroup& b) {
    a.Swap(&b);
  }
  inline void Swap(LaneGroup* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaneGroup* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LaneGroup* New() const final {
    return CreateMaybeMessage<LaneGroup>(nullptr);
  }

  LaneGroup* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LaneGroup>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LaneGroup& from);
  void MergeFrom(const LaneGroup& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaneGroup* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.LaneGroup";
  }
  protected:
  explicit LaneGroup(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaneFrameInfoFieldNumber = 1,
  };
  // repeated .gwm.hdmap.LanePassableInfo lane_frame_info = 1;
  int lane_frame_info_size() const;
  private:
  int _internal_lane_frame_info_size() const;
  public:
  void clear_lane_frame_info();
  ::gwm::hdmap::LanePassableInfo* mutable_lane_frame_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LanePassableInfo >*
      mutable_lane_frame_info();
  private:
  const ::gwm::hdmap::LanePassableInfo& _internal_lane_frame_info(int index) const;
  ::gwm::hdmap::LanePassableInfo* _internal_add_lane_frame_info();
  public:
  const ::gwm::hdmap::LanePassableInfo& lane_frame_info(int index) const;
  ::gwm::hdmap::LanePassableInfo* add_lane_frame_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LanePassableInfo >&
      lane_frame_info() const;

  // @@protoc_insertion_point(class_scope:gwm.hdmap.LaneGroup)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LanePassableInfo > lane_frame_info_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class RoutingLaneInfo PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.RoutingLaneInfo) */ {
 public:
  inline RoutingLaneInfo() : RoutingLaneInfo(nullptr) {}
  virtual ~RoutingLaneInfo();

  RoutingLaneInfo(const RoutingLaneInfo& from);
  RoutingLaneInfo(RoutingLaneInfo&& from) noexcept
    : RoutingLaneInfo() {
    *this = ::std::move(from);
  }

  inline RoutingLaneInfo& operator=(const RoutingLaneInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoutingLaneInfo& operator=(RoutingLaneInfo&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RoutingLaneInfo& default_instance();

  static inline const RoutingLaneInfo* internal_default_instance() {
    return reinterpret_cast<const RoutingLaneInfo*>(
               &_RoutingLaneInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(RoutingLaneInfo& a, RoutingLaneInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RoutingLaneInfo* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoutingLaneInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RoutingLaneInfo* New() const final {
    return CreateMaybeMessage<RoutingLaneInfo>(nullptr);
  }

  RoutingLaneInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RoutingLaneInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RoutingLaneInfo& from);
  void MergeFrom(const RoutingLaneInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoutingLaneInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.RoutingLaneInfo";
  }
  protected:
  explicit RoutingLaneInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEgoLaneInfoFieldNumber = 2,
    kLaneGroupsFieldNumber = 3,
    kTimeStampFieldNumber = 1,
  };
  // repeated .gwm.hdmap.EgoLaneInfo ego_lane_info = 2;
  int ego_lane_info_size() const;
  private:
  int _internal_ego_lane_info_size() const;
  public:
  void clear_ego_lane_info();
  ::gwm::hdmap::EgoLaneInfo* mutable_ego_lane_info(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::EgoLaneInfo >*
      mutable_ego_lane_info();
  private:
  const ::gwm::hdmap::EgoLaneInfo& _internal_ego_lane_info(int index) const;
  ::gwm::hdmap::EgoLaneInfo* _internal_add_ego_lane_info();
  public:
  const ::gwm::hdmap::EgoLaneInfo& ego_lane_info(int index) const;
  ::gwm::hdmap::EgoLaneInfo* add_ego_lane_info();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::EgoLaneInfo >&
      ego_lane_info() const;

  // repeated .gwm.hdmap.LaneGroup lane_groups = 3;
  int lane_groups_size() const;
  private:
  int _internal_lane_groups_size() const;
  public:
  void clear_lane_groups();
  ::gwm::hdmap::LaneGroup* mutable_lane_groups(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneGroup >*
      mutable_lane_groups();
  private:
  const ::gwm::hdmap::LaneGroup& _internal_lane_groups(int index) const;
  ::gwm::hdmap::LaneGroup* _internal_add_lane_groups();
  public:
  const ::gwm::hdmap::LaneGroup& lane_groups(int index) const;
  ::gwm::hdmap::LaneGroup* add_lane_groups();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneGroup >&
      lane_groups() const;

  // double time_stamp = 1;
  void clear_time_stamp();
  double time_stamp() const;
  void set_time_stamp(double value);
  private:
  double _internal_time_stamp() const;
  void _internal_set_time_stamp(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.RoutingLaneInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::EgoLaneInfo > ego_lane_info_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneGroup > lane_groups_;
  double time_stamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class RoutingMapInfo PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.RoutingMapInfo) */ {
 public:
  inline RoutingMapInfo() : RoutingMapInfo(nullptr) {}
  virtual ~RoutingMapInfo();

  RoutingMapInfo(const RoutingMapInfo& from);
  RoutingMapInfo(RoutingMapInfo&& from) noexcept
    : RoutingMapInfo() {
    *this = ::std::move(from);
  }

  inline RoutingMapInfo& operator=(const RoutingMapInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RoutingMapInfo& operator=(RoutingMapInfo&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const RoutingMapInfo& default_instance();

  static inline const RoutingMapInfo* internal_default_instance() {
    return reinterpret_cast<const RoutingMapInfo*>(
               &_RoutingMapInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(RoutingMapInfo& a, RoutingMapInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RoutingMapInfo* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RoutingMapInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline RoutingMapInfo* New() const final {
    return CreateMaybeMessage<RoutingMapInfo>(nullptr);
  }

  RoutingMapInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<RoutingMapInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const RoutingMapInfo& from);
  void MergeFrom(const RoutingMapInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RoutingMapInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.RoutingMapInfo";
  }
  protected:
  explicit RoutingMapInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLanesFieldNumber = 1,
    kRoutingLanesFieldNumber = 2,
  };
  // repeated .gwm.hdmap.Lane lanes = 1;
  int lanes_size() const;
  private:
  int _internal_lanes_size() const;
  public:
  void clear_lanes();
  ::gwm::hdmap::Lane* mutable_lanes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::Lane >*
      mutable_lanes();
  private:
  const ::gwm::hdmap::Lane& _internal_lanes(int index) const;
  ::gwm::hdmap::Lane* _internal_add_lanes();
  public:
  const ::gwm::hdmap::Lane& lanes(int index) const;
  ::gwm::hdmap::Lane* add_lanes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::Lane >&
      lanes() const;

  // repeated .gwm.hdmap.RoutingLaneInfo routing_lanes = 2;
  int routing_lanes_size() const;
  private:
  int _internal_routing_lanes_size() const;
  public:
  void clear_routing_lanes();
  ::gwm::hdmap::RoutingLaneInfo* mutable_routing_lanes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::RoutingLaneInfo >*
      mutable_routing_lanes();
  private:
  const ::gwm::hdmap::RoutingLaneInfo& _internal_routing_lanes(int index) const;
  ::gwm::hdmap::RoutingLaneInfo* _internal_add_routing_lanes();
  public:
  const ::gwm::hdmap::RoutingLaneInfo& routing_lanes(int index) const;
  ::gwm::hdmap::RoutingLaneInfo* add_routing_lanes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::RoutingLaneInfo >&
      routing_lanes() const;

  // @@protoc_insertion_point(class_scope:gwm.hdmap.RoutingMapInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::Lane > lanes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::RoutingLaneInfo > routing_lanes_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class LocationInfo PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.LocationInfo) */ {
 public:
  inline LocationInfo() : LocationInfo(nullptr) {}
  virtual ~LocationInfo();

  LocationInfo(const LocationInfo& from);
  LocationInfo(LocationInfo&& from) noexcept
    : LocationInfo() {
    *this = ::std::move(from);
  }

  inline LocationInfo& operator=(const LocationInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline LocationInfo& operator=(LocationInfo&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const LocationInfo& default_instance();

  static inline const LocationInfo* internal_default_instance() {
    return reinterpret_cast<const LocationInfo*>(
               &_LocationInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(LocationInfo& a, LocationInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(LocationInfo* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LocationInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline LocationInfo* New() const final {
    return CreateMaybeMessage<LocationInfo>(nullptr);
  }

  LocationInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<LocationInfo>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const LocationInfo& from);
  void MergeFrom(const LocationInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LocationInfo* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.LocationInfo";
  }
  protected:
  explicit LocationInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPointFieldNumber = 1,
    kRadiasFieldNumber = 2,
    kHeadingDegreeFieldNumber = 3,
    kMaxToleranceAngleFieldNumber = 4,
    kTimeStampFieldNumber = 5,
  };
  // .gwm.common.Point3D point = 1;
  bool has_point() const;
  private:
  bool _internal_has_point() const;
  public:
  void clear_point();
  const ::gwm::common::Point3D& point() const;
  ::gwm::common::Point3D* release_point();
  ::gwm::common::Point3D* mutable_point();
  void set_allocated_point(::gwm::common::Point3D* point);
  private:
  const ::gwm::common::Point3D& _internal_point() const;
  ::gwm::common::Point3D* _internal_mutable_point();
  public:
  void unsafe_arena_set_allocated_point(
      ::gwm::common::Point3D* point);
  ::gwm::common::Point3D* unsafe_arena_release_point();

  // double radias = 2;
  void clear_radias();
  double radias() const;
  void set_radias(double value);
  private:
  double _internal_radias() const;
  void _internal_set_radias(double value);
  public:

  // double heading_degree = 3;
  void clear_heading_degree();
  double heading_degree() const;
  void set_heading_degree(double value);
  private:
  double _internal_heading_degree() const;
  void _internal_set_heading_degree(double value);
  public:

  // double max_tolerance_angle = 4;
  void clear_max_tolerance_angle();
  double max_tolerance_angle() const;
  void set_max_tolerance_angle(double value);
  private:
  double _internal_max_tolerance_angle() const;
  void _internal_set_max_tolerance_angle(double value);
  public:

  // double time_stamp = 5;
  void clear_time_stamp();
  double time_stamp() const;
  void set_time_stamp(double value);
  private:
  double _internal_time_stamp() const;
  void _internal_set_time_stamp(double value);
  public:

  // @@protoc_insertion_point(class_scope:gwm.hdmap.LocationInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::gwm::common::Point3D* point_;
  double radias_;
  double heading_degree_;
  double max_tolerance_angle_;
  double time_stamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// -------------------------------------------------------------------

class TrackList PROTOBUF_FINAL :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:gwm.hdmap.TrackList) */ {
 public:
  inline TrackList() : TrackList(nullptr) {}
  virtual ~TrackList();

  TrackList(const TrackList& from);
  TrackList(TrackList&& from) noexcept
    : TrackList() {
    *this = ::std::move(from);
  }

  inline TrackList& operator=(const TrackList& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackList& operator=(TrackList&& from) noexcept {
    if (GetArena() == from.GetArena()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return GetMetadataStatic().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return GetMetadataStatic().reflection;
  }
  static const TrackList& default_instance();

  static inline const TrackList* internal_default_instance() {
    return reinterpret_cast<const TrackList*>(
               &_TrackList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(TrackList& a, TrackList& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackList* other) {
    if (other == this) return;
    if (GetArena() == other->GetArena()) {
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetArena() == other->GetArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  inline TrackList* New() const final {
    return CreateMaybeMessage<TrackList>(nullptr);
  }

  TrackList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena) const final {
    return CreateMaybeMessage<TrackList>(arena);
  }
  void CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) final;
  void CopyFrom(const TrackList& from);
  void MergeFrom(const TrackList& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  ::PROTOBUF_NAMESPACE_ID::uint8* _InternalSerialize(
      ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  inline void SharedCtor();
  inline void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackList* other);
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "gwm.hdmap.TrackList";
  }
  protected:
  explicit TrackList(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  private:
  static ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadataStatic() {
    ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&::descriptor_table_hd_5fmap_5flane_2eproto);
    return ::descriptor_table_hd_5fmap_5flane_2eproto.file_level_metadata[kIndexInFileMessages];
  }

  public:

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocationsFieldNumber = 1,
  };
  // repeated .gwm.hdmap.LocationInfo locations = 1;
  int locations_size() const;
  private:
  int _internal_locations_size() const;
  public:
  void clear_locations();
  ::gwm::hdmap::LocationInfo* mutable_locations(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LocationInfo >*
      mutable_locations();
  private:
  const ::gwm::hdmap::LocationInfo& _internal_locations(int index) const;
  ::gwm::hdmap::LocationInfo* _internal_add_locations();
  public:
  const ::gwm::hdmap::LocationInfo& locations(int index) const;
  ::gwm::hdmap::LocationInfo* add_locations();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LocationInfo >&
      locations() const;

  // @@protoc_insertion_point(class_scope:gwm.hdmap.TrackList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LocationInfo > locations_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_hd_5fmap_5flane_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LaneBoundaryType

// double s = 1;
inline void LaneBoundaryType::clear_s() {
  s_ = 0;
}
inline double LaneBoundaryType::_internal_s() const {
  return s_;
}
inline double LaneBoundaryType::s() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundaryType.s)
  return _internal_s();
}
inline void LaneBoundaryType::_internal_set_s(double value) {
  
  s_ = value;
}
inline void LaneBoundaryType::set_s(double value) {
  _internal_set_s(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundaryType.s)
}

// repeated .gwm.hdmap.LaneBoundaryType.Type types = 2;
inline int LaneBoundaryType::_internal_types_size() const {
  return types_.size();
}
inline int LaneBoundaryType::types_size() const {
  return _internal_types_size();
}
inline void LaneBoundaryType::clear_types() {
  types_.Clear();
}
inline ::gwm::hdmap::LaneBoundaryType_Type LaneBoundaryType::_internal_types(int index) const {
  return static_cast< ::gwm::hdmap::LaneBoundaryType_Type >(types_.Get(index));
}
inline ::gwm::hdmap::LaneBoundaryType_Type LaneBoundaryType::types(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundaryType.types)
  return _internal_types(index);
}
inline void LaneBoundaryType::set_types(int index, ::gwm::hdmap::LaneBoundaryType_Type value) {
  types_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundaryType.types)
}
inline void LaneBoundaryType::_internal_add_types(::gwm::hdmap::LaneBoundaryType_Type value) {
  types_.Add(value);
}
inline void LaneBoundaryType::add_types(::gwm::hdmap::LaneBoundaryType_Type value) {
  // @@protoc_insertion_point(field_add:gwm.hdmap.LaneBoundaryType.types)
  _internal_add_types(value);
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
LaneBoundaryType::types() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.LaneBoundaryType.types)
  return types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
LaneBoundaryType::_internal_mutable_types() {
  return &types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
LaneBoundaryType::mutable_types() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.LaneBoundaryType.types)
  return _internal_mutable_types();
}

// -------------------------------------------------------------------

// LaneBoundary

// double length = 2;
inline void LaneBoundary::clear_length() {
  length_ = 0;
}
inline double LaneBoundary::_internal_length() const {
  return length_;
}
inline double LaneBoundary::length() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.length)
  return _internal_length();
}
inline void LaneBoundary::_internal_set_length(double value) {
  
  length_ = value;
}
inline void LaneBoundary::set_length(double value) {
  _internal_set_length(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.length)
}

// bool virtual = 3;
inline void LaneBoundary::clear_virtual_() {
  virtual__ = false;
}
inline bool LaneBoundary::_internal_virtual_() const {
  return virtual__;
}
inline bool LaneBoundary::virtual_() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.virtual)
  return _internal_virtual_();
}
inline void LaneBoundary::_internal_set_virtual_(bool value) {
  
  virtual__ = value;
}
inline void LaneBoundary::set_virtual_(bool value) {
  _internal_set_virtual_(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.virtual)
}

// repeated .gwm.hdmap.LaneBoundaryType boundary_type = 4;
inline int LaneBoundary::_internal_boundary_type_size() const {
  return boundary_type_.size();
}
inline int LaneBoundary::boundary_type_size() const {
  return _internal_boundary_type_size();
}
inline void LaneBoundary::clear_boundary_type() {
  boundary_type_.Clear();
}
inline ::gwm::hdmap::LaneBoundaryType* LaneBoundary::mutable_boundary_type(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.LaneBoundary.boundary_type)
  return boundary_type_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneBoundaryType >*
LaneBoundary::mutable_boundary_type() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.LaneBoundary.boundary_type)
  return &boundary_type_;
}
inline const ::gwm::hdmap::LaneBoundaryType& LaneBoundary::_internal_boundary_type(int index) const {
  return boundary_type_.Get(index);
}
inline const ::gwm::hdmap::LaneBoundaryType& LaneBoundary::boundary_type(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.boundary_type)
  return _internal_boundary_type(index);
}
inline ::gwm::hdmap::LaneBoundaryType* LaneBoundary::_internal_add_boundary_type() {
  return boundary_type_.Add();
}
inline ::gwm::hdmap::LaneBoundaryType* LaneBoundary::add_boundary_type() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.LaneBoundary.boundary_type)
  return _internal_add_boundary_type();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneBoundaryType >&
LaneBoundary::boundary_type() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.LaneBoundary.boundary_type)
  return boundary_type_;
}

// int32 id = 5;
inline void LaneBoundary::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LaneBoundary::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LaneBoundary::id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.id)
  return _internal_id();
}
inline void LaneBoundary::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void LaneBoundary::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.id)
}

// .gwm.common.Polyline boundary = 6;
inline bool LaneBoundary::_internal_has_boundary() const {
  return this != internal_default_instance() && boundary_ != nullptr;
}
inline bool LaneBoundary::has_boundary() const {
  return _internal_has_boundary();
}
inline const ::gwm::common::Polyline& LaneBoundary::_internal_boundary() const {
  const ::gwm::common::Polyline* p = boundary_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Polyline&>(
      ::gwm::common::_Polyline_default_instance_);
}
inline const ::gwm::common::Polyline& LaneBoundary::boundary() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.boundary)
  return _internal_boundary();
}
inline void LaneBoundary::unsafe_arena_set_allocated_boundary(
    ::gwm::common::Polyline* boundary) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(boundary_);
  }
  boundary_ = boundary;
  if (boundary) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.LaneBoundary.boundary)
}
inline ::gwm::common::Polyline* LaneBoundary::release_boundary() {
  
  ::gwm::common::Polyline* temp = boundary_;
  boundary_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Polyline* LaneBoundary::unsafe_arena_release_boundary() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.LaneBoundary.boundary)
  
  ::gwm::common::Polyline* temp = boundary_;
  boundary_ = nullptr;
  return temp;
}
inline ::gwm::common::Polyline* LaneBoundary::_internal_mutable_boundary() {
  
  if (boundary_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Polyline>(GetArena());
    boundary_ = p;
  }
  return boundary_;
}
inline ::gwm::common::Polyline* LaneBoundary::mutable_boundary() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.LaneBoundary.boundary)
  return _internal_mutable_boundary();
}
inline void LaneBoundary::set_allocated_boundary(::gwm::common::Polyline* boundary) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(boundary_);
  }
  if (boundary) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(boundary)->GetArena();
    if (message_arena != submessage_arena) {
      boundary = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, boundary, submessage_arena);
    }
    
  } else {
    
  }
  boundary_ = boundary;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.LaneBoundary.boundary)
}

// .gwm.hdmap.LaneBoundary.Crossable crossable = 7;
inline void LaneBoundary::clear_crossable() {
  crossable_ = 0;
}
inline ::gwm::hdmap::LaneBoundary_Crossable LaneBoundary::_internal_crossable() const {
  return static_cast< ::gwm::hdmap::LaneBoundary_Crossable >(crossable_);
}
inline ::gwm::hdmap::LaneBoundary_Crossable LaneBoundary::crossable() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.crossable)
  return _internal_crossable();
}
inline void LaneBoundary::_internal_set_crossable(::gwm::hdmap::LaneBoundary_Crossable value) {
  
  crossable_ = value;
}
inline void LaneBoundary::set_crossable(::gwm::hdmap::LaneBoundary_Crossable value) {
  _internal_set_crossable(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.crossable)
}

// double cost = 8;
inline void LaneBoundary::clear_cost() {
  cost_ = 0;
}
inline double LaneBoundary::_internal_cost() const {
  return cost_;
}
inline double LaneBoundary::cost() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.cost)
  return _internal_cost();
}
inline void LaneBoundary::_internal_set_cost(double value) {
  
  cost_ = value;
}
inline void LaneBoundary::set_cost(double value) {
  _internal_set_cost(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.cost)
}

// repeated int32 layers = 9;
inline int LaneBoundary::_internal_layers_size() const {
  return layers_.size();
}
inline int LaneBoundary::layers_size() const {
  return _internal_layers_size();
}
inline void LaneBoundary::clear_layers() {
  layers_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LaneBoundary::_internal_layers(int index) const {
  return layers_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LaneBoundary::layers(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.layers)
  return _internal_layers(index);
}
inline void LaneBoundary::set_layers(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  layers_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.layers)
}
inline void LaneBoundary::_internal_add_layers(::PROTOBUF_NAMESPACE_ID::int32 value) {
  layers_.Add(value);
}
inline void LaneBoundary::add_layers(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_layers(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.LaneBoundary.layers)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
LaneBoundary::_internal_layers() const {
  return layers_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
LaneBoundary::layers() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.LaneBoundary.layers)
  return _internal_layers();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
LaneBoundary::_internal_mutable_layers() {
  return &layers_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
LaneBoundary::mutable_layers() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.LaneBoundary.layers)
  return _internal_mutable_layers();
}

// .gwm.hdmap.LaneBoundaryType.Type type = 10;
inline void LaneBoundary::clear_type() {
  type_ = 0;
}
inline ::gwm::hdmap::LaneBoundaryType_Type LaneBoundary::_internal_type() const {
  return static_cast< ::gwm::hdmap::LaneBoundaryType_Type >(type_);
}
inline ::gwm::hdmap::LaneBoundaryType_Type LaneBoundary::type() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.type)
  return _internal_type();
}
inline void LaneBoundary::_internal_set_type(::gwm::hdmap::LaneBoundaryType_Type value) {
  
  type_ = value;
}
inline void LaneBoundary::set_type(::gwm::hdmap::LaneBoundaryType_Type value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.type)
}

// bool has_left_deceleration_marking = 12;
inline void LaneBoundary::clear_has_left_deceleration_marking() {
  has_left_deceleration_marking_ = false;
}
inline bool LaneBoundary::_internal_has_left_deceleration_marking() const {
  return has_left_deceleration_marking_;
}
inline bool LaneBoundary::has_left_deceleration_marking() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.has_left_deceleration_marking)
  return _internal_has_left_deceleration_marking();
}
inline void LaneBoundary::_internal_set_has_left_deceleration_marking(bool value) {
  
  has_left_deceleration_marking_ = value;
}
inline void LaneBoundary::set_has_left_deceleration_marking(bool value) {
  _internal_set_has_left_deceleration_marking(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.has_left_deceleration_marking)
}

// bool has_right_deceleration_marking = 13;
inline void LaneBoundary::clear_has_right_deceleration_marking() {
  has_right_deceleration_marking_ = false;
}
inline bool LaneBoundary::_internal_has_right_deceleration_marking() const {
  return has_right_deceleration_marking_;
}
inline bool LaneBoundary::has_right_deceleration_marking() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneBoundary.has_right_deceleration_marking)
  return _internal_has_right_deceleration_marking();
}
inline void LaneBoundary::_internal_set_has_right_deceleration_marking(bool value) {
  
  has_right_deceleration_marking_ = value;
}
inline void LaneBoundary::set_has_right_deceleration_marking(bool value) {
  _internal_set_has_right_deceleration_marking(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneBoundary.has_right_deceleration_marking)
}

// -------------------------------------------------------------------

// LaneSampleAssociation

// double s = 1;
inline void LaneSampleAssociation::clear_s() {
  s_ = 0;
}
inline double LaneSampleAssociation::_internal_s() const {
  return s_;
}
inline double LaneSampleAssociation::s() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneSampleAssociation.s)
  return _internal_s();
}
inline void LaneSampleAssociation::_internal_set_s(double value) {
  
  s_ = value;
}
inline void LaneSampleAssociation::set_s(double value) {
  _internal_set_s(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneSampleAssociation.s)
}

// double width = 2;
inline void LaneSampleAssociation::clear_width() {
  width_ = 0;
}
inline double LaneSampleAssociation::_internal_width() const {
  return width_;
}
inline double LaneSampleAssociation::width() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneSampleAssociation.width)
  return _internal_width();
}
inline void LaneSampleAssociation::_internal_set_width(double value) {
  
  width_ = value;
}
inline void LaneSampleAssociation::set_width(double value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneSampleAssociation.width)
}

// -------------------------------------------------------------------

// Entrance

// int32 id = 1;
inline void Entrance::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Entrance::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Entrance::id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Entrance.id)
  return _internal_id();
}
inline void Entrance::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void Entrance::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Entrance.id)
}

// int32 lane_id = 2;
inline void Entrance::clear_lane_id() {
  lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Entrance::_internal_lane_id() const {
  return lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Entrance::lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Entrance.lane_id)
  return _internal_lane_id();
}
inline void Entrance::_internal_set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  lane_id_ = value;
}
inline void Entrance::set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Entrance.lane_id)
}

// .gwm.common.Point3D location = 3;
inline bool Entrance::_internal_has_location() const {
  return this != internal_default_instance() && location_ != nullptr;
}
inline bool Entrance::has_location() const {
  return _internal_has_location();
}
inline const ::gwm::common::Point3D& Entrance::_internal_location() const {
  const ::gwm::common::Point3D* p = location_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Point3D&>(
      ::gwm::common::_Point3D_default_instance_);
}
inline const ::gwm::common::Point3D& Entrance::location() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Entrance.location)
  return _internal_location();
}
inline void Entrance::unsafe_arena_set_allocated_location(
    ::gwm::common::Point3D* location) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_);
  }
  location_ = location;
  if (location) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.Entrance.location)
}
inline ::gwm::common::Point3D* Entrance::release_location() {
  
  ::gwm::common::Point3D* temp = location_;
  location_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Point3D* Entrance::unsafe_arena_release_location() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.Entrance.location)
  
  ::gwm::common::Point3D* temp = location_;
  location_ = nullptr;
  return temp;
}
inline ::gwm::common::Point3D* Entrance::_internal_mutable_location() {
  
  if (location_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Point3D>(GetArena());
    location_ = p;
  }
  return location_;
}
inline ::gwm::common::Point3D* Entrance::mutable_location() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Entrance.location)
  return _internal_mutable_location();
}
inline void Entrance::set_allocated_location(::gwm::common::Point3D* location) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(location_);
  }
  if (location) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(location)->GetArena();
    if (message_arena != submessage_arena) {
      location = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, location, submessage_arena);
    }
    
  } else {
    
  }
  location_ = location;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.Entrance.location)
}

// repeated int32 layers = 4;
inline int Entrance::_internal_layers_size() const {
  return layers_.size();
}
inline int Entrance::layers_size() const {
  return _internal_layers_size();
}
inline void Entrance::clear_layers() {
  layers_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Entrance::_internal_layers(int index) const {
  return layers_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Entrance::layers(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Entrance.layers)
  return _internal_layers(index);
}
inline void Entrance::set_layers(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  layers_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Entrance.layers)
}
inline void Entrance::_internal_add_layers(::PROTOBUF_NAMESPACE_ID::int32 value) {
  layers_.Add(value);
}
inline void Entrance::add_layers(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_layers(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Entrance.layers)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Entrance::_internal_layers() const {
  return layers_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Entrance::layers() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Entrance.layers)
  return _internal_layers();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Entrance::_internal_mutable_layers() {
  return &layers_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Entrance::mutable_layers() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Entrance.layers)
  return _internal_mutable_layers();
}

// -------------------------------------------------------------------

// Rule

// float speed_limit = 1;
inline bool Rule::_internal_has_speed_limit() const {
  return rule_case() == kSpeedLimit;
}
inline void Rule::set_has_speed_limit() {
  _oneof_case_[0] = kSpeedLimit;
}
inline void Rule::clear_speed_limit() {
  if (_internal_has_speed_limit()) {
    rule_.speed_limit_ = 0;
    clear_has_rule();
  }
}
inline float Rule::_internal_speed_limit() const {
  if (_internal_has_speed_limit()) {
    return rule_.speed_limit_;
  }
  return 0;
}
inline void Rule::_internal_set_speed_limit(float value) {
  if (!_internal_has_speed_limit()) {
    clear_rule();
    set_has_speed_limit();
  }
  rule_.speed_limit_ = value;
}
inline float Rule::speed_limit() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Rule.speed_limit)
  return _internal_speed_limit();
}
inline void Rule::set_speed_limit(float value) {
  _internal_set_speed_limit(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Rule.speed_limit)
}

// bool disabled = 2;
inline bool Rule::_internal_has_disabled() const {
  return rule_case() == kDisabled;
}
inline void Rule::set_has_disabled() {
  _oneof_case_[0] = kDisabled;
}
inline void Rule::clear_disabled() {
  if (_internal_has_disabled()) {
    rule_.disabled_ = false;
    clear_has_rule();
  }
}
inline bool Rule::_internal_disabled() const {
  if (_internal_has_disabled()) {
    return rule_.disabled_;
  }
  return false;
}
inline void Rule::_internal_set_disabled(bool value) {
  if (!_internal_has_disabled()) {
    clear_rule();
    set_has_disabled();
  }
  rule_.disabled_ = value;
}
inline bool Rule::disabled() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Rule.disabled)
  return _internal_disabled();
}
inline void Rule::set_disabled(bool value) {
  _internal_set_disabled(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Rule.disabled)
}

inline bool Rule::has_rule() const {
  return rule_case() != RULE_NOT_SET;
}
inline void Rule::clear_has_rule() {
  _oneof_case_[0] = RULE_NOT_SET;
}
inline Rule::RuleCase Rule::rule_case() const {
  return Rule::RuleCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Trigger

// bool always = 1;
inline bool Trigger::_internal_has_always() const {
  return trigger_case() == kAlways;
}
inline void Trigger::set_has_always() {
  _oneof_case_[0] = kAlways;
}
inline void Trigger::clear_always() {
  if (_internal_has_always()) {
    trigger_.always_ = false;
    clear_has_trigger();
  }
}
inline bool Trigger::_internal_always() const {
  if (_internal_has_always()) {
    return trigger_.always_;
  }
  return false;
}
inline void Trigger::_internal_set_always(bool value) {
  if (!_internal_has_always()) {
    clear_trigger();
    set_has_always();
  }
  trigger_.always_ = value;
}
inline bool Trigger::always() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Trigger.always)
  return _internal_always();
}
inline void Trigger::set_always(bool value) {
  _internal_set_always(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Trigger.always)
}

inline bool Trigger::has_trigger() const {
  return trigger_case() != TRIGGER_NOT_SET;
}
inline void Trigger::clear_has_trigger() {
  _oneof_case_[0] = TRIGGER_NOT_SET;
}
inline Trigger::TriggerCase Trigger::trigger_case() const {
  return Trigger::TriggerCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// LaneRule

// .gwm.hdmap.LaneRule.VehicleType vehicle_type = 1;
inline void LaneRule::clear_vehicle_type() {
  vehicle_type_ = 0;
}
inline ::gwm::hdmap::LaneRule_VehicleType LaneRule::_internal_vehicle_type() const {
  return static_cast< ::gwm::hdmap::LaneRule_VehicleType >(vehicle_type_);
}
inline ::gwm::hdmap::LaneRule_VehicleType LaneRule::vehicle_type() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneRule.vehicle_type)
  return _internal_vehicle_type();
}
inline void LaneRule::_internal_set_vehicle_type(::gwm::hdmap::LaneRule_VehicleType value) {
  
  vehicle_type_ = value;
}
inline void LaneRule::set_vehicle_type(::gwm::hdmap::LaneRule_VehicleType value) {
  _internal_set_vehicle_type(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LaneRule.vehicle_type)
}

// .gwm.hdmap.Trigger trigger = 2;
inline bool LaneRule::_internal_has_trigger() const {
  return this != internal_default_instance() && trigger_ != nullptr;
}
inline bool LaneRule::has_trigger() const {
  return _internal_has_trigger();
}
inline void LaneRule::clear_trigger() {
  if (GetArena() == nullptr && trigger_ != nullptr) {
    delete trigger_;
  }
  trigger_ = nullptr;
}
inline const ::gwm::hdmap::Trigger& LaneRule::_internal_trigger() const {
  const ::gwm::hdmap::Trigger* p = trigger_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::hdmap::Trigger&>(
      ::gwm::hdmap::_Trigger_default_instance_);
}
inline const ::gwm::hdmap::Trigger& LaneRule::trigger() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneRule.trigger)
  return _internal_trigger();
}
inline void LaneRule::unsafe_arena_set_allocated_trigger(
    ::gwm::hdmap::Trigger* trigger) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(trigger_);
  }
  trigger_ = trigger;
  if (trigger) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.LaneRule.trigger)
}
inline ::gwm::hdmap::Trigger* LaneRule::release_trigger() {
  
  ::gwm::hdmap::Trigger* temp = trigger_;
  trigger_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::hdmap::Trigger* LaneRule::unsafe_arena_release_trigger() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.LaneRule.trigger)
  
  ::gwm::hdmap::Trigger* temp = trigger_;
  trigger_ = nullptr;
  return temp;
}
inline ::gwm::hdmap::Trigger* LaneRule::_internal_mutable_trigger() {
  
  if (trigger_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::hdmap::Trigger>(GetArena());
    trigger_ = p;
  }
  return trigger_;
}
inline ::gwm::hdmap::Trigger* LaneRule::mutable_trigger() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.LaneRule.trigger)
  return _internal_mutable_trigger();
}
inline void LaneRule::set_allocated_trigger(::gwm::hdmap::Trigger* trigger) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete trigger_;
  }
  if (trigger) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(trigger);
    if (message_arena != submessage_arena) {
      trigger = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, trigger, submessage_arena);
    }
    
  } else {
    
  }
  trigger_ = trigger;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.LaneRule.trigger)
}

// .gwm.hdmap.Rule rule = 3;
inline bool LaneRule::_internal_has_rule() const {
  return this != internal_default_instance() && rule_ != nullptr;
}
inline bool LaneRule::has_rule() const {
  return _internal_has_rule();
}
inline void LaneRule::clear_rule() {
  if (GetArena() == nullptr && rule_ != nullptr) {
    delete rule_;
  }
  rule_ = nullptr;
}
inline const ::gwm::hdmap::Rule& LaneRule::_internal_rule() const {
  const ::gwm::hdmap::Rule* p = rule_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::hdmap::Rule&>(
      ::gwm::hdmap::_Rule_default_instance_);
}
inline const ::gwm::hdmap::Rule& LaneRule::rule() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneRule.rule)
  return _internal_rule();
}
inline void LaneRule::unsafe_arena_set_allocated_rule(
    ::gwm::hdmap::Rule* rule) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rule_);
  }
  rule_ = rule;
  if (rule) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.LaneRule.rule)
}
inline ::gwm::hdmap::Rule* LaneRule::release_rule() {
  
  ::gwm::hdmap::Rule* temp = rule_;
  rule_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::hdmap::Rule* LaneRule::unsafe_arena_release_rule() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.LaneRule.rule)
  
  ::gwm::hdmap::Rule* temp = rule_;
  rule_ = nullptr;
  return temp;
}
inline ::gwm::hdmap::Rule* LaneRule::_internal_mutable_rule() {
  
  if (rule_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::hdmap::Rule>(GetArena());
    rule_ = p;
  }
  return rule_;
}
inline ::gwm::hdmap::Rule* LaneRule::mutable_rule() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.LaneRule.rule)
  return _internal_mutable_rule();
}
inline void LaneRule::set_allocated_rule(::gwm::hdmap::Rule* rule) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete rule_;
  }
  if (rule) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(rule);
    if (message_arena != submessage_arena) {
      rule = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rule, submessage_arena);
    }
    
  } else {
    
  }
  rule_ = rule;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.LaneRule.rule)
}

// -------------------------------------------------------------------

// MergeSplit_Merge

// .gwm.hdmap.MergeSplit.Direction direction = 1;
inline void MergeSplit_Merge::clear_direction() {
  direction_ = 0;
}
inline ::gwm::hdmap::MergeSplit_Direction MergeSplit_Merge::_internal_direction() const {
  return static_cast< ::gwm::hdmap::MergeSplit_Direction >(direction_);
}
inline ::gwm::hdmap::MergeSplit_Direction MergeSplit_Merge::direction() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.MergeSplit.Merge.direction)
  return _internal_direction();
}
inline void MergeSplit_Merge::_internal_set_direction(::gwm::hdmap::MergeSplit_Direction value) {
  
  direction_ = value;
}
inline void MergeSplit_Merge::set_direction(::gwm::hdmap::MergeSplit_Direction value) {
  _internal_set_direction(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.MergeSplit.Merge.direction)
}

// int32 to_lane_id = 2;
inline void MergeSplit_Merge::clear_to_lane_id() {
  to_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MergeSplit_Merge::_internal_to_lane_id() const {
  return to_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MergeSplit_Merge::to_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.MergeSplit.Merge.to_lane_id)
  return _internal_to_lane_id();
}
inline void MergeSplit_Merge::_internal_set_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  to_lane_id_ = value;
}
inline void MergeSplit_Merge::set_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_to_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.MergeSplit.Merge.to_lane_id)
}

// -------------------------------------------------------------------

// MergeSplit_Split

// .gwm.hdmap.MergeSplit.Direction direction = 1;
inline void MergeSplit_Split::clear_direction() {
  direction_ = 0;
}
inline ::gwm::hdmap::MergeSplit_Direction MergeSplit_Split::_internal_direction() const {
  return static_cast< ::gwm::hdmap::MergeSplit_Direction >(direction_);
}
inline ::gwm::hdmap::MergeSplit_Direction MergeSplit_Split::direction() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.MergeSplit.Split.direction)
  return _internal_direction();
}
inline void MergeSplit_Split::_internal_set_direction(::gwm::hdmap::MergeSplit_Direction value) {
  
  direction_ = value;
}
inline void MergeSplit_Split::set_direction(::gwm::hdmap::MergeSplit_Direction value) {
  _internal_set_direction(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.MergeSplit.Split.direction)
}

// int32 from_lane_id = 2;
inline void MergeSplit_Split::clear_from_lane_id() {
  from_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MergeSplit_Split::_internal_from_lane_id() const {
  return from_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 MergeSplit_Split::from_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.MergeSplit.Split.from_lane_id)
  return _internal_from_lane_id();
}
inline void MergeSplit_Split::_internal_set_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  from_lane_id_ = value;
}
inline void MergeSplit_Split::set_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_from_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.MergeSplit.Split.from_lane_id)
}

// -------------------------------------------------------------------

// MergeSplit

// .gwm.hdmap.MergeSplit.Merge merge = 1;
inline bool MergeSplit::_internal_has_merge() const {
  return type_case() == kMerge;
}
inline bool MergeSplit::has_merge() const {
  return _internal_has_merge();
}
inline void MergeSplit::set_has_merge() {
  _oneof_case_[0] = kMerge;
}
inline void MergeSplit::clear_merge() {
  if (_internal_has_merge()) {
    if (GetArena() == nullptr) {
      delete type_.merge_;
    }
    clear_has_type();
  }
}
inline ::gwm::hdmap::MergeSplit_Merge* MergeSplit::release_merge() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.MergeSplit.merge)
  if (_internal_has_merge()) {
    clear_has_type();
      ::gwm::hdmap::MergeSplit_Merge* temp = type_.merge_;
    if (GetArena() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_.merge_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::gwm::hdmap::MergeSplit_Merge& MergeSplit::_internal_merge() const {
  return _internal_has_merge()
      ? *type_.merge_
      : reinterpret_cast< ::gwm::hdmap::MergeSplit_Merge&>(::gwm::hdmap::_MergeSplit_Merge_default_instance_);
}
inline const ::gwm::hdmap::MergeSplit_Merge& MergeSplit::merge() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.MergeSplit.merge)
  return _internal_merge();
}
inline ::gwm::hdmap::MergeSplit_Merge* MergeSplit::unsafe_arena_release_merge() {
  // @@protoc_insertion_point(field_unsafe_arena_release:gwm.hdmap.MergeSplit.merge)
  if (_internal_has_merge()) {
    clear_has_type();
    ::gwm::hdmap::MergeSplit_Merge* temp = type_.merge_;
    type_.merge_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void MergeSplit::unsafe_arena_set_allocated_merge(::gwm::hdmap::MergeSplit_Merge* merge) {
  clear_type();
  if (merge) {
    set_has_merge();
    type_.merge_ = merge;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.MergeSplit.merge)
}
inline ::gwm::hdmap::MergeSplit_Merge* MergeSplit::_internal_mutable_merge() {
  if (!_internal_has_merge()) {
    clear_type();
    set_has_merge();
    type_.merge_ = CreateMaybeMessage< ::gwm::hdmap::MergeSplit_Merge >(GetArena());
  }
  return type_.merge_;
}
inline ::gwm::hdmap::MergeSplit_Merge* MergeSplit::mutable_merge() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.MergeSplit.merge)
  return _internal_mutable_merge();
}

// .gwm.hdmap.MergeSplit.Split split = 2;
inline bool MergeSplit::_internal_has_split() const {
  return type_case() == kSplit;
}
inline bool MergeSplit::has_split() const {
  return _internal_has_split();
}
inline void MergeSplit::set_has_split() {
  _oneof_case_[0] = kSplit;
}
inline void MergeSplit::clear_split() {
  if (_internal_has_split()) {
    if (GetArena() == nullptr) {
      delete type_.split_;
    }
    clear_has_type();
  }
}
inline ::gwm::hdmap::MergeSplit_Split* MergeSplit::release_split() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.MergeSplit.split)
  if (_internal_has_split()) {
    clear_has_type();
      ::gwm::hdmap::MergeSplit_Split* temp = type_.split_;
    if (GetArena() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_.split_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::gwm::hdmap::MergeSplit_Split& MergeSplit::_internal_split() const {
  return _internal_has_split()
      ? *type_.split_
      : reinterpret_cast< ::gwm::hdmap::MergeSplit_Split&>(::gwm::hdmap::_MergeSplit_Split_default_instance_);
}
inline const ::gwm::hdmap::MergeSplit_Split& MergeSplit::split() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.MergeSplit.split)
  return _internal_split();
}
inline ::gwm::hdmap::MergeSplit_Split* MergeSplit::unsafe_arena_release_split() {
  // @@protoc_insertion_point(field_unsafe_arena_release:gwm.hdmap.MergeSplit.split)
  if (_internal_has_split()) {
    clear_has_type();
    ::gwm::hdmap::MergeSplit_Split* temp = type_.split_;
    type_.split_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void MergeSplit::unsafe_arena_set_allocated_split(::gwm::hdmap::MergeSplit_Split* split) {
  clear_type();
  if (split) {
    set_has_split();
    type_.split_ = split;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.MergeSplit.split)
}
inline ::gwm::hdmap::MergeSplit_Split* MergeSplit::_internal_mutable_split() {
  if (!_internal_has_split()) {
    clear_type();
    set_has_split();
    type_.split_ = CreateMaybeMessage< ::gwm::hdmap::MergeSplit_Split >(GetArena());
  }
  return type_.split_;
}
inline ::gwm::hdmap::MergeSplit_Split* MergeSplit::mutable_split() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.MergeSplit.split)
  return _internal_mutable_split();
}

inline bool MergeSplit::has_type() const {
  return type_case() != TYPE_NOT_SET;
}
inline void MergeSplit::clear_has_type() {
  _oneof_case_[0] = TYPE_NOT_SET;
}
inline MergeSplit::TypeCase MergeSplit::type_case() const {
  return MergeSplit::TypeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// NeighborMerge_Ids

// repeated int32 ids = 1;
inline int NeighborMerge_Ids::_internal_ids_size() const {
  return ids_.size();
}
inline int NeighborMerge_Ids::ids_size() const {
  return _internal_ids_size();
}
inline void NeighborMerge_Ids::clear_ids() {
  ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 NeighborMerge_Ids::_internal_ids(int index) const {
  return ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 NeighborMerge_Ids::ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.NeighborMerge.Ids.ids)
  return _internal_ids(index);
}
inline void NeighborMerge_Ids::set_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.NeighborMerge.Ids.ids)
}
inline void NeighborMerge_Ids::_internal_add_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  ids_.Add(value);
}
inline void NeighborMerge_Ids::add_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.NeighborMerge.Ids.ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
NeighborMerge_Ids::_internal_ids() const {
  return ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
NeighborMerge_Ids::ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.NeighborMerge.Ids.ids)
  return _internal_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
NeighborMerge_Ids::_internal_mutable_ids() {
  return &ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
NeighborMerge_Ids::mutable_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.NeighborMerge.Ids.ids)
  return _internal_mutable_ids();
}

// -------------------------------------------------------------------

// NeighborMerge

// .gwm.hdmap.NeighborMerge.Ids merge_from_lane_ids = 1;
inline bool NeighborMerge::_internal_has_merge_from_lane_ids() const {
  return type_case() == kMergeFromLaneIds;
}
inline bool NeighborMerge::has_merge_from_lane_ids() const {
  return _internal_has_merge_from_lane_ids();
}
inline void NeighborMerge::set_has_merge_from_lane_ids() {
  _oneof_case_[0] = kMergeFromLaneIds;
}
inline void NeighborMerge::clear_merge_from_lane_ids() {
  if (_internal_has_merge_from_lane_ids()) {
    if (GetArena() == nullptr) {
      delete type_.merge_from_lane_ids_;
    }
    clear_has_type();
  }
}
inline ::gwm::hdmap::NeighborMerge_Ids* NeighborMerge::release_merge_from_lane_ids() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.NeighborMerge.merge_from_lane_ids)
  if (_internal_has_merge_from_lane_ids()) {
    clear_has_type();
      ::gwm::hdmap::NeighborMerge_Ids* temp = type_.merge_from_lane_ids_;
    if (GetArena() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_.merge_from_lane_ids_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::gwm::hdmap::NeighborMerge_Ids& NeighborMerge::_internal_merge_from_lane_ids() const {
  return _internal_has_merge_from_lane_ids()
      ? *type_.merge_from_lane_ids_
      : reinterpret_cast< ::gwm::hdmap::NeighborMerge_Ids&>(::gwm::hdmap::_NeighborMerge_Ids_default_instance_);
}
inline const ::gwm::hdmap::NeighborMerge_Ids& NeighborMerge::merge_from_lane_ids() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.NeighborMerge.merge_from_lane_ids)
  return _internal_merge_from_lane_ids();
}
inline ::gwm::hdmap::NeighborMerge_Ids* NeighborMerge::unsafe_arena_release_merge_from_lane_ids() {
  // @@protoc_insertion_point(field_unsafe_arena_release:gwm.hdmap.NeighborMerge.merge_from_lane_ids)
  if (_internal_has_merge_from_lane_ids()) {
    clear_has_type();
    ::gwm::hdmap::NeighborMerge_Ids* temp = type_.merge_from_lane_ids_;
    type_.merge_from_lane_ids_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void NeighborMerge::unsafe_arena_set_allocated_merge_from_lane_ids(::gwm::hdmap::NeighborMerge_Ids* merge_from_lane_ids) {
  clear_type();
  if (merge_from_lane_ids) {
    set_has_merge_from_lane_ids();
    type_.merge_from_lane_ids_ = merge_from_lane_ids;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.NeighborMerge.merge_from_lane_ids)
}
inline ::gwm::hdmap::NeighborMerge_Ids* NeighborMerge::_internal_mutable_merge_from_lane_ids() {
  if (!_internal_has_merge_from_lane_ids()) {
    clear_type();
    set_has_merge_from_lane_ids();
    type_.merge_from_lane_ids_ = CreateMaybeMessage< ::gwm::hdmap::NeighborMerge_Ids >(GetArena());
  }
  return type_.merge_from_lane_ids_;
}
inline ::gwm::hdmap::NeighborMerge_Ids* NeighborMerge::mutable_merge_from_lane_ids() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.NeighborMerge.merge_from_lane_ids)
  return _internal_mutable_merge_from_lane_ids();
}

// int32 merge_to_lane_id = 2;
inline bool NeighborMerge::_internal_has_merge_to_lane_id() const {
  return type_case() == kMergeToLaneId;
}
inline void NeighborMerge::set_has_merge_to_lane_id() {
  _oneof_case_[0] = kMergeToLaneId;
}
inline void NeighborMerge::clear_merge_to_lane_id() {
  if (_internal_has_merge_to_lane_id()) {
    type_.merge_to_lane_id_ = 0;
    clear_has_type();
  }
}
inline ::PROTOBUF_NAMESPACE_ID::int32 NeighborMerge::_internal_merge_to_lane_id() const {
  if (_internal_has_merge_to_lane_id()) {
    return type_.merge_to_lane_id_;
  }
  return 0;
}
inline void NeighborMerge::_internal_set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  if (!_internal_has_merge_to_lane_id()) {
    clear_type();
    set_has_merge_to_lane_id();
  }
  type_.merge_to_lane_id_ = value;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 NeighborMerge::merge_to_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.NeighborMerge.merge_to_lane_id)
  return _internal_merge_to_lane_id();
}
inline void NeighborMerge::set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_merge_to_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.NeighborMerge.merge_to_lane_id)
}

// int32 successor_lane_id = 3;
inline void NeighborMerge::clear_successor_lane_id() {
  successor_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 NeighborMerge::_internal_successor_lane_id() const {
  return successor_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 NeighborMerge::successor_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.NeighborMerge.successor_lane_id)
  return _internal_successor_lane_id();
}
inline void NeighborMerge::_internal_set_successor_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  successor_lane_id_ = value;
}
inline void NeighborMerge::set_successor_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_successor_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.NeighborMerge.successor_lane_id)
}

inline bool NeighborMerge::has_type() const {
  return type_case() != TYPE_NOT_SET;
}
inline void NeighborMerge::clear_has_type() {
  _oneof_case_[0] = TYPE_NOT_SET;
}
inline NeighborMerge::TypeCase NeighborMerge::type_case() const {
  return NeighborMerge::TypeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Lane

// int32 id = 1;
inline void Lane::clear_id() {
  id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_id() const {
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.id)
  return _internal_id();
}
inline void Lane::_internal_set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  id_ = value;
}
inline void Lane::set_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.id)
}

// .gwm.hdmap.LaneBoundary left_boundary = 3;
inline bool Lane::_internal_has_left_boundary() const {
  return this != internal_default_instance() && left_boundary_ != nullptr;
}
inline bool Lane::has_left_boundary() const {
  return _internal_has_left_boundary();
}
inline void Lane::clear_left_boundary() {
  if (GetArena() == nullptr && left_boundary_ != nullptr) {
    delete left_boundary_;
  }
  left_boundary_ = nullptr;
}
inline const ::gwm::hdmap::LaneBoundary& Lane::_internal_left_boundary() const {
  const ::gwm::hdmap::LaneBoundary* p = left_boundary_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::hdmap::LaneBoundary&>(
      ::gwm::hdmap::_LaneBoundary_default_instance_);
}
inline const ::gwm::hdmap::LaneBoundary& Lane::left_boundary() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.left_boundary)
  return _internal_left_boundary();
}
inline void Lane::unsafe_arena_set_allocated_left_boundary(
    ::gwm::hdmap::LaneBoundary* left_boundary) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(left_boundary_);
  }
  left_boundary_ = left_boundary;
  if (left_boundary) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.Lane.left_boundary)
}
inline ::gwm::hdmap::LaneBoundary* Lane::release_left_boundary() {
  
  ::gwm::hdmap::LaneBoundary* temp = left_boundary_;
  left_boundary_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::hdmap::LaneBoundary* Lane::unsafe_arena_release_left_boundary() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.Lane.left_boundary)
  
  ::gwm::hdmap::LaneBoundary* temp = left_boundary_;
  left_boundary_ = nullptr;
  return temp;
}
inline ::gwm::hdmap::LaneBoundary* Lane::_internal_mutable_left_boundary() {
  
  if (left_boundary_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::hdmap::LaneBoundary>(GetArena());
    left_boundary_ = p;
  }
  return left_boundary_;
}
inline ::gwm::hdmap::LaneBoundary* Lane::mutable_left_boundary() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.left_boundary)
  return _internal_mutable_left_boundary();
}
inline void Lane::set_allocated_left_boundary(::gwm::hdmap::LaneBoundary* left_boundary) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete left_boundary_;
  }
  if (left_boundary) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(left_boundary);
    if (message_arena != submessage_arena) {
      left_boundary = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, left_boundary, submessage_arena);
    }
    
  } else {
    
  }
  left_boundary_ = left_boundary;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.Lane.left_boundary)
}

// .gwm.hdmap.LaneBoundary right_boundary = 4;
inline bool Lane::_internal_has_right_boundary() const {
  return this != internal_default_instance() && right_boundary_ != nullptr;
}
inline bool Lane::has_right_boundary() const {
  return _internal_has_right_boundary();
}
inline void Lane::clear_right_boundary() {
  if (GetArena() == nullptr && right_boundary_ != nullptr) {
    delete right_boundary_;
  }
  right_boundary_ = nullptr;
}
inline const ::gwm::hdmap::LaneBoundary& Lane::_internal_right_boundary() const {
  const ::gwm::hdmap::LaneBoundary* p = right_boundary_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::hdmap::LaneBoundary&>(
      ::gwm::hdmap::_LaneBoundary_default_instance_);
}
inline const ::gwm::hdmap::LaneBoundary& Lane::right_boundary() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.right_boundary)
  return _internal_right_boundary();
}
inline void Lane::unsafe_arena_set_allocated_right_boundary(
    ::gwm::hdmap::LaneBoundary* right_boundary) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(right_boundary_);
  }
  right_boundary_ = right_boundary;
  if (right_boundary) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.Lane.right_boundary)
}
inline ::gwm::hdmap::LaneBoundary* Lane::release_right_boundary() {
  
  ::gwm::hdmap::LaneBoundary* temp = right_boundary_;
  right_boundary_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::hdmap::LaneBoundary* Lane::unsafe_arena_release_right_boundary() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.Lane.right_boundary)
  
  ::gwm::hdmap::LaneBoundary* temp = right_boundary_;
  right_boundary_ = nullptr;
  return temp;
}
inline ::gwm::hdmap::LaneBoundary* Lane::_internal_mutable_right_boundary() {
  
  if (right_boundary_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::hdmap::LaneBoundary>(GetArena());
    right_boundary_ = p;
  }
  return right_boundary_;
}
inline ::gwm::hdmap::LaneBoundary* Lane::mutable_right_boundary() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.right_boundary)
  return _internal_mutable_right_boundary();
}
inline void Lane::set_allocated_right_boundary(::gwm::hdmap::LaneBoundary* right_boundary) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete right_boundary_;
  }
  if (right_boundary) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::GetArena(right_boundary);
    if (message_arena != submessage_arena) {
      right_boundary = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, right_boundary, submessage_arena);
    }
    
  } else {
    
  }
  right_boundary_ = right_boundary;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.Lane.right_boundary)
}

// double length = 5;
inline void Lane::clear_length() {
  length_ = 0;
}
inline double Lane::_internal_length() const {
  return length_;
}
inline double Lane::length() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.length)
  return _internal_length();
}
inline void Lane::_internal_set_length(double value) {
  
  length_ = value;
}
inline void Lane::set_length(double value) {
  _internal_set_length(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.length)
}

// float speed_limit = 6;
inline void Lane::clear_speed_limit() {
  speed_limit_ = 0;
}
inline float Lane::_internal_speed_limit() const {
  return speed_limit_;
}
inline float Lane::speed_limit() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.speed_limit)
  return _internal_speed_limit();
}
inline void Lane::_internal_set_speed_limit(float value) {
  
  speed_limit_ = value;
}
inline void Lane::set_speed_limit(float value) {
  _internal_set_speed_limit(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.speed_limit)
}

// repeated int32 overlap_id = 7;
inline int Lane::_internal_overlap_id_size() const {
  return overlap_id_.size();
}
inline int Lane::overlap_id_size() const {
  return _internal_overlap_id_size();
}
inline void Lane::clear_overlap_id() {
  overlap_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_overlap_id(int index) const {
  return overlap_id_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::overlap_id(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.overlap_id)
  return _internal_overlap_id(index);
}
inline void Lane::set_overlap_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  overlap_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.overlap_id)
}
inline void Lane::_internal_add_overlap_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  overlap_id_.Add(value);
}
inline void Lane::add_overlap_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_overlap_id(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.overlap_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_overlap_id() const {
  return overlap_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::overlap_id() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.overlap_id)
  return _internal_overlap_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_overlap_id() {
  return &overlap_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_overlap_id() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.overlap_id)
  return _internal_mutable_overlap_id();
}

// repeated int32 predecessor_id = 8;
inline int Lane::_internal_predecessor_id_size() const {
  return predecessor_id_.size();
}
inline int Lane::predecessor_id_size() const {
  return _internal_predecessor_id_size();
}
inline void Lane::clear_predecessor_id() {
  predecessor_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_predecessor_id(int index) const {
  return predecessor_id_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::predecessor_id(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.predecessor_id)
  return _internal_predecessor_id(index);
}
inline void Lane::set_predecessor_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  predecessor_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.predecessor_id)
}
inline void Lane::_internal_add_predecessor_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  predecessor_id_.Add(value);
}
inline void Lane::add_predecessor_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_predecessor_id(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.predecessor_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_predecessor_id() const {
  return predecessor_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::predecessor_id() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.predecessor_id)
  return _internal_predecessor_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_predecessor_id() {
  return &predecessor_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_predecessor_id() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.predecessor_id)
  return _internal_mutable_predecessor_id();
}

// repeated int32 successor_id = 9;
inline int Lane::_internal_successor_id_size() const {
  return successor_id_.size();
}
inline int Lane::successor_id_size() const {
  return _internal_successor_id_size();
}
inline void Lane::clear_successor_id() {
  successor_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_successor_id(int index) const {
  return successor_id_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::successor_id(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.successor_id)
  return _internal_successor_id(index);
}
inline void Lane::set_successor_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  successor_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.successor_id)
}
inline void Lane::_internal_add_successor_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  successor_id_.Add(value);
}
inline void Lane::add_successor_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_successor_id(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.successor_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_successor_id() const {
  return successor_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::successor_id() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.successor_id)
  return _internal_successor_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_successor_id() {
  return &successor_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_successor_id() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.successor_id)
  return _internal_mutable_successor_id();
}

// int32 left_neighbor_forward_lane_id = 10;
inline void Lane::clear_left_neighbor_forward_lane_id() {
  left_neighbor_forward_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_left_neighbor_forward_lane_id() const {
  return left_neighbor_forward_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::left_neighbor_forward_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.left_neighbor_forward_lane_id)
  return _internal_left_neighbor_forward_lane_id();
}
inline void Lane::_internal_set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  left_neighbor_forward_lane_id_ = value;
}
inline void Lane::set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_left_neighbor_forward_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.left_neighbor_forward_lane_id)
}

// int32 right_neighbor_forward_lane_id = 11;
inline void Lane::clear_right_neighbor_forward_lane_id() {
  right_neighbor_forward_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_right_neighbor_forward_lane_id() const {
  return right_neighbor_forward_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::right_neighbor_forward_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.right_neighbor_forward_lane_id)
  return _internal_right_neighbor_forward_lane_id();
}
inline void Lane::_internal_set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  right_neighbor_forward_lane_id_ = value;
}
inline void Lane::set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_right_neighbor_forward_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.right_neighbor_forward_lane_id)
}

// .gwm.hdmap.Lane.LaneType type = 12;
inline void Lane::clear_type() {
  type_ = 0;
}
inline ::gwm::hdmap::Lane_LaneType Lane::_internal_type() const {
  return static_cast< ::gwm::hdmap::Lane_LaneType >(type_);
}
inline ::gwm::hdmap::Lane_LaneType Lane::type() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.type)
  return _internal_type();
}
inline void Lane::_internal_set_type(::gwm::hdmap::Lane_LaneType value) {
  
  type_ = value;
}
inline void Lane::set_type(::gwm::hdmap::Lane_LaneType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.type)
}

// .gwm.hdmap.Lane.LaneTurn turn = 13;
inline void Lane::clear_turn() {
  turn_ = 0;
}
inline ::gwm::hdmap::Lane_LaneTurn Lane::_internal_turn() const {
  return static_cast< ::gwm::hdmap::Lane_LaneTurn >(turn_);
}
inline ::gwm::hdmap::Lane_LaneTurn Lane::turn() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.turn)
  return _internal_turn();
}
inline void Lane::_internal_set_turn(::gwm::hdmap::Lane_LaneTurn value) {
  
  turn_ = value;
}
inline void Lane::set_turn(::gwm::hdmap::Lane_LaneTurn value) {
  _internal_set_turn(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.turn)
}

// int32 left_neighbor_reverse_lane_id = 14;
inline void Lane::clear_left_neighbor_reverse_lane_id() {
  left_neighbor_reverse_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_left_neighbor_reverse_lane_id() const {
  return left_neighbor_reverse_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::left_neighbor_reverse_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.left_neighbor_reverse_lane_id)
  return _internal_left_neighbor_reverse_lane_id();
}
inline void Lane::_internal_set_left_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  left_neighbor_reverse_lane_id_ = value;
}
inline void Lane::set_left_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_left_neighbor_reverse_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.left_neighbor_reverse_lane_id)
}

// int32 right_neighbor_reverse_lane_id = 15;
inline void Lane::clear_right_neighbor_reverse_lane_id() {
  right_neighbor_reverse_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_right_neighbor_reverse_lane_id() const {
  return right_neighbor_reverse_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::right_neighbor_reverse_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.right_neighbor_reverse_lane_id)
  return _internal_right_neighbor_reverse_lane_id();
}
inline void Lane::_internal_set_right_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  right_neighbor_reverse_lane_id_ = value;
}
inline void Lane::set_right_neighbor_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_right_neighbor_reverse_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.right_neighbor_reverse_lane_id)
}

// int32 junction_id = 16;
inline void Lane::clear_junction_id() {
  junction_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_junction_id() const {
  return junction_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::junction_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.junction_id)
  return _internal_junction_id();
}
inline void Lane::_internal_set_junction_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  junction_id_ = value;
}
inline void Lane::set_junction_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_junction_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.junction_id)
}

// repeated .gwm.hdmap.LaneSampleAssociation left_sample = 17;
inline int Lane::_internal_left_sample_size() const {
  return left_sample_.size();
}
inline int Lane::left_sample_size() const {
  return _internal_left_sample_size();
}
inline void Lane::clear_left_sample() {
  left_sample_.Clear();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::mutable_left_sample(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.left_sample)
  return left_sample_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
Lane::mutable_left_sample() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.left_sample)
  return &left_sample_;
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::_internal_left_sample(int index) const {
  return left_sample_.Get(index);
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::left_sample(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.left_sample)
  return _internal_left_sample(index);
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::_internal_add_left_sample() {
  return left_sample_.Add();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::add_left_sample() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.left_sample)
  return _internal_add_left_sample();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
Lane::left_sample() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.left_sample)
  return left_sample_;
}

// repeated .gwm.hdmap.LaneSampleAssociation right_sample = 18;
inline int Lane::_internal_right_sample_size() const {
  return right_sample_.size();
}
inline int Lane::right_sample_size() const {
  return _internal_right_sample_size();
}
inline void Lane::clear_right_sample() {
  right_sample_.Clear();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::mutable_right_sample(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.right_sample)
  return right_sample_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
Lane::mutable_right_sample() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.right_sample)
  return &right_sample_;
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::_internal_right_sample(int index) const {
  return right_sample_.Get(index);
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::right_sample(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.right_sample)
  return _internal_right_sample(index);
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::_internal_add_right_sample() {
  return right_sample_.Add();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::add_right_sample() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.right_sample)
  return _internal_add_right_sample();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
Lane::right_sample() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.right_sample)
  return right_sample_;
}

// .gwm.hdmap.Lane.LaneDirection direction = 19;
inline void Lane::clear_direction() {
  direction_ = 0;
}
inline ::gwm::hdmap::Lane_LaneDirection Lane::_internal_direction() const {
  return static_cast< ::gwm::hdmap::Lane_LaneDirection >(direction_);
}
inline ::gwm::hdmap::Lane_LaneDirection Lane::direction() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.direction)
  return _internal_direction();
}
inline void Lane::_internal_set_direction(::gwm::hdmap::Lane_LaneDirection value) {
  
  direction_ = value;
}
inline void Lane::set_direction(::gwm::hdmap::Lane_LaneDirection value) {
  _internal_set_direction(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.direction)
}

// repeated .gwm.hdmap.LaneSampleAssociation left_road_sample = 20;
inline int Lane::_internal_left_road_sample_size() const {
  return left_road_sample_.size();
}
inline int Lane::left_road_sample_size() const {
  return _internal_left_road_sample_size();
}
inline void Lane::clear_left_road_sample() {
  left_road_sample_.Clear();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::mutable_left_road_sample(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.left_road_sample)
  return left_road_sample_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
Lane::mutable_left_road_sample() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.left_road_sample)
  return &left_road_sample_;
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::_internal_left_road_sample(int index) const {
  return left_road_sample_.Get(index);
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::left_road_sample(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.left_road_sample)
  return _internal_left_road_sample(index);
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::_internal_add_left_road_sample() {
  return left_road_sample_.Add();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::add_left_road_sample() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.left_road_sample)
  return _internal_add_left_road_sample();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
Lane::left_road_sample() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.left_road_sample)
  return left_road_sample_;
}

// repeated .gwm.hdmap.LaneSampleAssociation right_road_sample = 21;
inline int Lane::_internal_right_road_sample_size() const {
  return right_road_sample_.size();
}
inline int Lane::right_road_sample_size() const {
  return _internal_right_road_sample_size();
}
inline void Lane::clear_right_road_sample() {
  right_road_sample_.Clear();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::mutable_right_road_sample(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.right_road_sample)
  return right_road_sample_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >*
Lane::mutable_right_road_sample() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.right_road_sample)
  return &right_road_sample_;
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::_internal_right_road_sample(int index) const {
  return right_road_sample_.Get(index);
}
inline const ::gwm::hdmap::LaneSampleAssociation& Lane::right_road_sample(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.right_road_sample)
  return _internal_right_road_sample(index);
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::_internal_add_right_road_sample() {
  return right_road_sample_.Add();
}
inline ::gwm::hdmap::LaneSampleAssociation* Lane::add_right_road_sample() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.right_road_sample)
  return _internal_add_right_road_sample();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneSampleAssociation >&
Lane::right_road_sample() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.right_road_sample)
  return right_road_sample_;
}

// repeated int32 self_reverse_lane_id = 22;
inline int Lane::_internal_self_reverse_lane_id_size() const {
  return self_reverse_lane_id_.size();
}
inline int Lane::self_reverse_lane_id_size() const {
  return _internal_self_reverse_lane_id_size();
}
inline void Lane::clear_self_reverse_lane_id() {
  self_reverse_lane_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_self_reverse_lane_id(int index) const {
  return self_reverse_lane_id_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::self_reverse_lane_id(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.self_reverse_lane_id)
  return _internal_self_reverse_lane_id(index);
}
inline void Lane::set_self_reverse_lane_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  self_reverse_lane_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.self_reverse_lane_id)
}
inline void Lane::_internal_add_self_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  self_reverse_lane_id_.Add(value);
}
inline void Lane::add_self_reverse_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_self_reverse_lane_id(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.self_reverse_lane_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_self_reverse_lane_id() const {
  return self_reverse_lane_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::self_reverse_lane_id() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.self_reverse_lane_id)
  return _internal_self_reverse_lane_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_self_reverse_lane_id() {
  return &self_reverse_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_self_reverse_lane_id() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.self_reverse_lane_id)
  return _internal_mutable_self_reverse_lane_id();
}

// .gwm.common.Polyline centerline = 23;
inline bool Lane::_internal_has_centerline() const {
  return this != internal_default_instance() && centerline_ != nullptr;
}
inline bool Lane::has_centerline() const {
  return _internal_has_centerline();
}
inline const ::gwm::common::Polyline& Lane::_internal_centerline() const {
  const ::gwm::common::Polyline* p = centerline_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Polyline&>(
      ::gwm::common::_Polyline_default_instance_);
}
inline const ::gwm::common::Polyline& Lane::centerline() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.centerline)
  return _internal_centerline();
}
inline void Lane::unsafe_arena_set_allocated_centerline(
    ::gwm::common::Polyline* centerline) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(centerline_);
  }
  centerline_ = centerline;
  if (centerline) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.Lane.centerline)
}
inline ::gwm::common::Polyline* Lane::release_centerline() {
  
  ::gwm::common::Polyline* temp = centerline_;
  centerline_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Polyline* Lane::unsafe_arena_release_centerline() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.Lane.centerline)
  
  ::gwm::common::Polyline* temp = centerline_;
  centerline_ = nullptr;
  return temp;
}
inline ::gwm::common::Polyline* Lane::_internal_mutable_centerline() {
  
  if (centerline_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Polyline>(GetArena());
    centerline_ = p;
  }
  return centerline_;
}
inline ::gwm::common::Polyline* Lane::mutable_centerline() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.centerline)
  return _internal_mutable_centerline();
}
inline void Lane::set_allocated_centerline(::gwm::common::Polyline* centerline) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(centerline_);
  }
  if (centerline) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(centerline)->GetArena();
    if (message_arena != submessage_arena) {
      centerline = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, centerline, submessage_arena);
    }
    
  } else {
    
  }
  centerline_ = centerline;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.Lane.centerline)
}

// repeated float centerline_s = 24 [packed = true];
inline int Lane::_internal_centerline_s_size() const {
  return centerline_s_.size();
}
inline int Lane::centerline_s_size() const {
  return _internal_centerline_s_size();
}
inline void Lane::clear_centerline_s() {
  centerline_s_.Clear();
}
inline float Lane::_internal_centerline_s(int index) const {
  return centerline_s_.Get(index);
}
inline float Lane::centerline_s(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.centerline_s)
  return _internal_centerline_s(index);
}
inline void Lane::set_centerline_s(int index, float value) {
  centerline_s_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.centerline_s)
}
inline void Lane::_internal_add_centerline_s(float value) {
  centerline_s_.Add(value);
}
inline void Lane::add_centerline_s(float value) {
  _internal_add_centerline_s(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.centerline_s)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Lane::_internal_centerline_s() const {
  return centerline_s_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
Lane::centerline_s() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.centerline_s)
  return _internal_centerline_s();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Lane::_internal_mutable_centerline_s() {
  return &centerline_s_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
Lane::mutable_centerline_s() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.centerline_s)
  return _internal_mutable_centerline_s();
}

// int32 left_boundary_id = 25;
inline void Lane::clear_left_boundary_id() {
  left_boundary_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_left_boundary_id() const {
  return left_boundary_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::left_boundary_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.left_boundary_id)
  return _internal_left_boundary_id();
}
inline void Lane::_internal_set_left_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  left_boundary_id_ = value;
}
inline void Lane::set_left_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_left_boundary_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.left_boundary_id)
}

// int32 right_boundary_id = 26;
inline void Lane::clear_right_boundary_id() {
  right_boundary_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_right_boundary_id() const {
  return right_boundary_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::right_boundary_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.right_boundary_id)
  return _internal_right_boundary_id();
}
inline void Lane::_internal_set_right_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  right_boundary_id_ = value;
}
inline void Lane::set_right_boundary_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_right_boundary_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.right_boundary_id)
}

// .gwm.hdmap.Lane.BoundaryDirection boundary_direction = 27;
inline void Lane::clear_boundary_direction() {
  boundary_direction_ = 0;
}
inline ::gwm::hdmap::Lane_BoundaryDirection Lane::_internal_boundary_direction() const {
  return static_cast< ::gwm::hdmap::Lane_BoundaryDirection >(boundary_direction_);
}
inline ::gwm::hdmap::Lane_BoundaryDirection Lane::boundary_direction() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.boundary_direction)
  return _internal_boundary_direction();
}
inline void Lane::_internal_set_boundary_direction(::gwm::hdmap::Lane_BoundaryDirection value) {
  
  boundary_direction_ = value;
}
inline void Lane::set_boundary_direction(::gwm::hdmap::Lane_BoundaryDirection value) {
  _internal_set_boundary_direction(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.boundary_direction)
}

// double cost = 29;
inline void Lane::clear_cost() {
  cost_ = 0;
}
inline double Lane::_internal_cost() const {
  return cost_;
}
inline double Lane::cost() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.cost)
  return _internal_cost();
}
inline void Lane::_internal_set_cost(double value) {
  
  cost_ = value;
}
inline void Lane::set_cost(double value) {
  _internal_set_cost(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.cost)
}

// .gwm.hdmap.Lane.MergeType merge = 30;
inline void Lane::clear_merge() {
  merge_ = 0;
}
inline ::gwm::hdmap::Lane_MergeType Lane::_internal_merge() const {
  return static_cast< ::gwm::hdmap::Lane_MergeType >(merge_);
}
inline ::gwm::hdmap::Lane_MergeType Lane::merge() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.merge)
  return _internal_merge();
}
inline void Lane::_internal_set_merge(::gwm::hdmap::Lane_MergeType value) {
  
  merge_ = value;
}
inline void Lane::set_merge(::gwm::hdmap::Lane_MergeType value) {
  _internal_set_merge(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.merge)
}

// int32 merge_to_lane_id = 31;
inline void Lane::clear_merge_to_lane_id() {
  merge_to_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_merge_to_lane_id() const {
  return merge_to_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::merge_to_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.merge_to_lane_id)
  return _internal_merge_to_lane_id();
}
inline void Lane::_internal_set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  merge_to_lane_id_ = value;
}
inline void Lane::set_merge_to_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_merge_to_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.merge_to_lane_id)
}

// repeated int32 merge_from_lane_id = 32;
inline int Lane::_internal_merge_from_lane_id_size() const {
  return merge_from_lane_id_.size();
}
inline int Lane::merge_from_lane_id_size() const {
  return _internal_merge_from_lane_id_size();
}
inline void Lane::clear_merge_from_lane_id() {
  merge_from_lane_id_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_merge_from_lane_id(int index) const {
  return merge_from_lane_id_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::merge_from_lane_id(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.merge_from_lane_id)
  return _internal_merge_from_lane_id(index);
}
inline void Lane::set_merge_from_lane_id(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  merge_from_lane_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.merge_from_lane_id)
}
inline void Lane::_internal_add_merge_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  merge_from_lane_id_.Add(value);
}
inline void Lane::add_merge_from_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_merge_from_lane_id(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.merge_from_lane_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_merge_from_lane_id() const {
  return merge_from_lane_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::merge_from_lane_id() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.merge_from_lane_id)
  return _internal_merge_from_lane_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_merge_from_lane_id() {
  return &merge_from_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_merge_from_lane_id() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.merge_from_lane_id)
  return _internal_mutable_merge_from_lane_id();
}

// repeated int32 layers = 33;
inline int Lane::_internal_layers_size() const {
  return layers_.size();
}
inline int Lane::layers_size() const {
  return _internal_layers_size();
}
inline void Lane::clear_layers() {
  layers_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_layers(int index) const {
  return layers_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::layers(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.layers)
  return _internal_layers(index);
}
inline void Lane::set_layers(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  layers_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.layers)
}
inline void Lane::_internal_add_layers(::PROTOBUF_NAMESPACE_ID::int32 value) {
  layers_.Add(value);
}
inline void Lane::add_layers(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_layers(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.layers)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_layers() const {
  return layers_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::layers() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.layers)
  return _internal_layers();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_layers() {
  return &layers_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_layers() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.layers)
  return _internal_mutable_layers();
}

// int32 bidi_copy_from_id = 34;
inline void Lane::clear_bidi_copy_from_id() {
  bidi_copy_from_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_bidi_copy_from_id() const {
  return bidi_copy_from_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::bidi_copy_from_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.bidi_copy_from_id)
  return _internal_bidi_copy_from_id();
}
inline void Lane::_internal_set_bidi_copy_from_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  bidi_copy_from_id_ = value;
}
inline void Lane::set_bidi_copy_from_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_bidi_copy_from_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.bidi_copy_from_id)
}

// int32 manually_set_left_neighbor_forward_lane_id = 35;
inline void Lane::clear_manually_set_left_neighbor_forward_lane_id() {
  manually_set_left_neighbor_forward_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_manually_set_left_neighbor_forward_lane_id() const {
  return manually_set_left_neighbor_forward_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::manually_set_left_neighbor_forward_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.manually_set_left_neighbor_forward_lane_id)
  return _internal_manually_set_left_neighbor_forward_lane_id();
}
inline void Lane::_internal_set_manually_set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  manually_set_left_neighbor_forward_lane_id_ = value;
}
inline void Lane::set_manually_set_left_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_manually_set_left_neighbor_forward_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.manually_set_left_neighbor_forward_lane_id)
}

// int32 manually_set_right_neighbor_forward_lane_id = 36;
inline void Lane::clear_manually_set_right_neighbor_forward_lane_id() {
  manually_set_right_neighbor_forward_lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_manually_set_right_neighbor_forward_lane_id() const {
  return manually_set_right_neighbor_forward_lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::manually_set_right_neighbor_forward_lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.manually_set_right_neighbor_forward_lane_id)
  return _internal_manually_set_right_neighbor_forward_lane_id();
}
inline void Lane::_internal_set_manually_set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  manually_set_right_neighbor_forward_lane_id_ = value;
}
inline void Lane::set_manually_set_right_neighbor_forward_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_manually_set_right_neighbor_forward_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.manually_set_right_neighbor_forward_lane_id)
}

// repeated string road_name = 37;
inline int Lane::_internal_road_name_size() const {
  return road_name_.size();
}
inline int Lane::road_name_size() const {
  return _internal_road_name_size();
}
inline void Lane::clear_road_name() {
  road_name_.Clear();
}
inline std::string* Lane::add_road_name() {
  // @@protoc_insertion_point(field_add_mutable:gwm.hdmap.Lane.road_name)
  return _internal_add_road_name();
}
inline const std::string& Lane::_internal_road_name(int index) const {
  return road_name_.Get(index);
}
inline const std::string& Lane::road_name(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.road_name)
  return _internal_road_name(index);
}
inline std::string* Lane::mutable_road_name(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.road_name)
  return road_name_.Mutable(index);
}
inline void Lane::set_road_name(int index, const std::string& value) {
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.road_name)
  road_name_.Mutable(index)->assign(value);
}
inline void Lane::set_road_name(int index, std::string&& value) {
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.road_name)
  road_name_.Mutable(index)->assign(std::move(value));
}
inline void Lane::set_road_name(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  road_name_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:gwm.hdmap.Lane.road_name)
}
inline void Lane::set_road_name(int index, const char* value, size_t size) {
  road_name_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:gwm.hdmap.Lane.road_name)
}
inline std::string* Lane::_internal_add_road_name() {
  return road_name_.Add();
}
inline void Lane::add_road_name(const std::string& value) {
  road_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.road_name)
}
inline void Lane::add_road_name(std::string&& value) {
  road_name_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.road_name)
}
inline void Lane::add_road_name(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  road_name_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:gwm.hdmap.Lane.road_name)
}
inline void Lane::add_road_name(const char* value, size_t size) {
  road_name_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:gwm.hdmap.Lane.road_name)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Lane::road_name() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.road_name)
  return road_name_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Lane::mutable_road_name() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.road_name)
  return &road_name_;
}

// bool is_disabled = 38;
inline void Lane::clear_is_disabled() {
  is_disabled_ = false;
}
inline bool Lane::_internal_is_disabled() const {
  return is_disabled_;
}
inline bool Lane::is_disabled() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.is_disabled)
  return _internal_is_disabled();
}
inline void Lane::_internal_set_is_disabled(bool value) {
  
  is_disabled_ = value;
}
inline void Lane::set_is_disabled(bool value) {
  _internal_set_is_disabled(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.is_disabled)
}

// bool is_virtual = 39 [deprecated = true];
inline void Lane::clear_is_virtual() {
  is_virtual_ = false;
}
inline bool Lane::_internal_is_virtual() const {
  return is_virtual_;
}
inline bool Lane::is_virtual() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.is_virtual)
  return _internal_is_virtual();
}
inline void Lane::_internal_set_is_virtual(bool value) {
  
  is_virtual_ = value;
}
inline void Lane::set_is_virtual(bool value) {
  _internal_set_is_virtual(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.is_virtual)
}

// float truck_speed_limit = 40;
inline void Lane::clear_truck_speed_limit() {
  truck_speed_limit_ = 0;
}
inline float Lane::_internal_truck_speed_limit() const {
  return truck_speed_limit_;
}
inline float Lane::truck_speed_limit() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.truck_speed_limit)
  return _internal_truck_speed_limit();
}
inline void Lane::_internal_set_truck_speed_limit(float value) {
  
  truck_speed_limit_ = value;
}
inline void Lane::set_truck_speed_limit(float value) {
  _internal_set_truck_speed_limit(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.truck_speed_limit)
}

// float min_width = 41;
inline void Lane::clear_min_width() {
  min_width_ = 0;
}
inline float Lane::_internal_min_width() const {
  return min_width_;
}
inline float Lane::min_width() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.min_width)
  return _internal_min_width();
}
inline void Lane::_internal_set_min_width(float value) {
  
  min_width_ = value;
}
inline void Lane::set_min_width(float value) {
  _internal_set_min_width(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.min_width)
}

// float max_curvature = 42;
inline void Lane::clear_max_curvature() {
  max_curvature_ = 0;
}
inline float Lane::_internal_max_curvature() const {
  return max_curvature_;
}
inline float Lane::max_curvature() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.max_curvature)
  return _internal_max_curvature();
}
inline void Lane::_internal_set_max_curvature(float value) {
  
  max_curvature_ = value;
}
inline void Lane::set_max_curvature(float value) {
  _internal_set_max_curvature(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.max_curvature)
}

// float heading_diff = 43;
inline void Lane::clear_heading_diff() {
  heading_diff_ = 0;
}
inline float Lane::_internal_heading_diff() const {
  return heading_diff_;
}
inline float Lane::heading_diff() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.heading_diff)
  return _internal_heading_diff();
}
inline void Lane::_internal_set_heading_diff(float value) {
  
  heading_diff_ = value;
}
inline void Lane::set_heading_diff(float value) {
  _internal_set_heading_diff(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.heading_diff)
}

// repeated .gwm.hdmap.LaneRule lane_rules = 44;
inline int Lane::_internal_lane_rules_size() const {
  return lane_rules_.size();
}
inline int Lane::lane_rules_size() const {
  return _internal_lane_rules_size();
}
inline void Lane::clear_lane_rules() {
  lane_rules_.Clear();
}
inline ::gwm::hdmap::LaneRule* Lane::mutable_lane_rules(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.lane_rules)
  return lane_rules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneRule >*
Lane::mutable_lane_rules() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.lane_rules)
  return &lane_rules_;
}
inline const ::gwm::hdmap::LaneRule& Lane::_internal_lane_rules(int index) const {
  return lane_rules_.Get(index);
}
inline const ::gwm::hdmap::LaneRule& Lane::lane_rules(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.lane_rules)
  return _internal_lane_rules(index);
}
inline ::gwm::hdmap::LaneRule* Lane::_internal_add_lane_rules() {
  return lane_rules_.Add();
}
inline ::gwm::hdmap::LaneRule* Lane::add_lane_rules() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.lane_rules)
  return _internal_add_lane_rules();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneRule >&
Lane::lane_rules() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.lane_rules)
  return lane_rules_;
}

// repeated int32 left_boundary_plus_ids = 45;
inline int Lane::_internal_left_boundary_plus_ids_size() const {
  return left_boundary_plus_ids_.size();
}
inline int Lane::left_boundary_plus_ids_size() const {
  return _internal_left_boundary_plus_ids_size();
}
inline void Lane::clear_left_boundary_plus_ids() {
  left_boundary_plus_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_left_boundary_plus_ids(int index) const {
  return left_boundary_plus_ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::left_boundary_plus_ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.left_boundary_plus_ids)
  return _internal_left_boundary_plus_ids(index);
}
inline void Lane::set_left_boundary_plus_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  left_boundary_plus_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.left_boundary_plus_ids)
}
inline void Lane::_internal_add_left_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  left_boundary_plus_ids_.Add(value);
}
inline void Lane::add_left_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_left_boundary_plus_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.left_boundary_plus_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_left_boundary_plus_ids() const {
  return left_boundary_plus_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::left_boundary_plus_ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.left_boundary_plus_ids)
  return _internal_left_boundary_plus_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_left_boundary_plus_ids() {
  return &left_boundary_plus_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_left_boundary_plus_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.left_boundary_plus_ids)
  return _internal_mutable_left_boundary_plus_ids();
}

// repeated int32 right_boundary_plus_ids = 46;
inline int Lane::_internal_right_boundary_plus_ids_size() const {
  return right_boundary_plus_ids_.size();
}
inline int Lane::right_boundary_plus_ids_size() const {
  return _internal_right_boundary_plus_ids_size();
}
inline void Lane::clear_right_boundary_plus_ids() {
  right_boundary_plus_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_right_boundary_plus_ids(int index) const {
  return right_boundary_plus_ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::right_boundary_plus_ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.right_boundary_plus_ids)
  return _internal_right_boundary_plus_ids(index);
}
inline void Lane::set_right_boundary_plus_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  right_boundary_plus_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.right_boundary_plus_ids)
}
inline void Lane::_internal_add_right_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  right_boundary_plus_ids_.Add(value);
}
inline void Lane::add_right_boundary_plus_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_right_boundary_plus_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.right_boundary_plus_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_right_boundary_plus_ids() const {
  return right_boundary_plus_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::right_boundary_plus_ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.right_boundary_plus_ids)
  return _internal_right_boundary_plus_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_right_boundary_plus_ids() {
  return &right_boundary_plus_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_right_boundary_plus_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.right_boundary_plus_ids)
  return _internal_mutable_right_boundary_plus_ids();
}

// repeated .gwm.hdmap.MergeSplit merge_splits = 47;
inline int Lane::_internal_merge_splits_size() const {
  return merge_splits_.size();
}
inline int Lane::merge_splits_size() const {
  return _internal_merge_splits_size();
}
inline void Lane::clear_merge_splits() {
  merge_splits_.Clear();
}
inline ::gwm::hdmap::MergeSplit* Lane::mutable_merge_splits(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.merge_splits)
  return merge_splits_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::MergeSplit >*
Lane::mutable_merge_splits() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.merge_splits)
  return &merge_splits_;
}
inline const ::gwm::hdmap::MergeSplit& Lane::_internal_merge_splits(int index) const {
  return merge_splits_.Get(index);
}
inline const ::gwm::hdmap::MergeSplit& Lane::merge_splits(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.merge_splits)
  return _internal_merge_splits(index);
}
inline ::gwm::hdmap::MergeSplit* Lane::_internal_add_merge_splits() {
  return merge_splits_.Add();
}
inline ::gwm::hdmap::MergeSplit* Lane::add_merge_splits() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.merge_splits)
  return _internal_add_merge_splits();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::MergeSplit >&
Lane::merge_splits() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.merge_splits)
  return merge_splits_;
}

// repeated int32 road_section_ids = 48;
inline int Lane::_internal_road_section_ids_size() const {
  return road_section_ids_.size();
}
inline int Lane::road_section_ids_size() const {
  return _internal_road_section_ids_size();
}
inline void Lane::clear_road_section_ids() {
  road_section_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_road_section_ids(int index) const {
  return road_section_ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::road_section_ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.road_section_ids)
  return _internal_road_section_ids(index);
}
inline void Lane::set_road_section_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  road_section_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.road_section_ids)
}
inline void Lane::_internal_add_road_section_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  road_section_ids_.Add(value);
}
inline void Lane::add_road_section_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_road_section_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.road_section_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_road_section_ids() const {
  return road_section_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::road_section_ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.road_section_ids)
  return _internal_road_section_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_road_section_ids() {
  return &road_section_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_road_section_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.road_section_ids)
  return _internal_mutable_road_section_ids();
}

// repeated .gwm.hdmap.NeighborMerge neighbor_merges = 49;
inline int Lane::_internal_neighbor_merges_size() const {
  return neighbor_merges_.size();
}
inline int Lane::neighbor_merges_size() const {
  return _internal_neighbor_merges_size();
}
inline void Lane::clear_neighbor_merges() {
  neighbor_merges_.Clear();
}
inline ::gwm::hdmap::NeighborMerge* Lane::mutable_neighbor_merges(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.Lane.neighbor_merges)
  return neighbor_merges_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::NeighborMerge >*
Lane::mutable_neighbor_merges() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.neighbor_merges)
  return &neighbor_merges_;
}
inline const ::gwm::hdmap::NeighborMerge& Lane::_internal_neighbor_merges(int index) const {
  return neighbor_merges_.Get(index);
}
inline const ::gwm::hdmap::NeighborMerge& Lane::neighbor_merges(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.neighbor_merges)
  return _internal_neighbor_merges(index);
}
inline ::gwm::hdmap::NeighborMerge* Lane::_internal_add_neighbor_merges() {
  return neighbor_merges_.Add();
}
inline ::gwm::hdmap::NeighborMerge* Lane::add_neighbor_merges() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.neighbor_merges)
  return _internal_add_neighbor_merges();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::NeighborMerge >&
Lane::neighbor_merges() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.neighbor_merges)
  return neighbor_merges_;
}

// repeated int32 manually_set_predecessor_ids = 50 [deprecated = true];
inline int Lane::_internal_manually_set_predecessor_ids_size() const {
  return manually_set_predecessor_ids_.size();
}
inline int Lane::manually_set_predecessor_ids_size() const {
  return _internal_manually_set_predecessor_ids_size();
}
inline void Lane::clear_manually_set_predecessor_ids() {
  manually_set_predecessor_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_manually_set_predecessor_ids(int index) const {
  return manually_set_predecessor_ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::manually_set_predecessor_ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.manually_set_predecessor_ids)
  return _internal_manually_set_predecessor_ids(index);
}
inline void Lane::set_manually_set_predecessor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  manually_set_predecessor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.manually_set_predecessor_ids)
}
inline void Lane::_internal_add_manually_set_predecessor_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  manually_set_predecessor_ids_.Add(value);
}
inline void Lane::add_manually_set_predecessor_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_manually_set_predecessor_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.manually_set_predecessor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_manually_set_predecessor_ids() const {
  return manually_set_predecessor_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::manually_set_predecessor_ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.manually_set_predecessor_ids)
  return _internal_manually_set_predecessor_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_manually_set_predecessor_ids() {
  return &manually_set_predecessor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_manually_set_predecessor_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.manually_set_predecessor_ids)
  return _internal_mutable_manually_set_predecessor_ids();
}

// repeated int32 manually_set_successor_ids = 51 [deprecated = true];
inline int Lane::_internal_manually_set_successor_ids_size() const {
  return manually_set_successor_ids_.size();
}
inline int Lane::manually_set_successor_ids_size() const {
  return _internal_manually_set_successor_ids_size();
}
inline void Lane::clear_manually_set_successor_ids() {
  manually_set_successor_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_manually_set_successor_ids(int index) const {
  return manually_set_successor_ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::manually_set_successor_ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.manually_set_successor_ids)
  return _internal_manually_set_successor_ids(index);
}
inline void Lane::set_manually_set_successor_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  manually_set_successor_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.manually_set_successor_ids)
}
inline void Lane::_internal_add_manually_set_successor_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  manually_set_successor_ids_.Add(value);
}
inline void Lane::add_manually_set_successor_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_manually_set_successor_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.manually_set_successor_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_manually_set_successor_ids() const {
  return manually_set_successor_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::manually_set_successor_ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.manually_set_successor_ids)
  return _internal_manually_set_successor_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_manually_set_successor_ids() {
  return &manually_set_successor_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_manually_set_successor_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.manually_set_successor_ids)
  return _internal_mutable_manually_set_successor_ids();
}

// repeated int32 next_lane_ids = 52;
inline int Lane::_internal_next_lane_ids_size() const {
  return next_lane_ids_.size();
}
inline int Lane::next_lane_ids_size() const {
  return _internal_next_lane_ids_size();
}
inline void Lane::clear_next_lane_ids() {
  next_lane_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_next_lane_ids(int index) const {
  return next_lane_ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::next_lane_ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.next_lane_ids)
  return _internal_next_lane_ids(index);
}
inline void Lane::set_next_lane_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  next_lane_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.next_lane_ids)
}
inline void Lane::_internal_add_next_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  next_lane_ids_.Add(value);
}
inline void Lane::add_next_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_next_lane_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.next_lane_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_next_lane_ids() const {
  return next_lane_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::next_lane_ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.next_lane_ids)
  return _internal_next_lane_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_next_lane_ids() {
  return &next_lane_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_next_lane_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.next_lane_ids)
  return _internal_mutable_next_lane_ids();
}

// repeated int32 last_lane_ids = 53;
inline int Lane::_internal_last_lane_ids_size() const {
  return last_lane_ids_.size();
}
inline int Lane::last_lane_ids_size() const {
  return _internal_last_lane_ids_size();
}
inline void Lane::clear_last_lane_ids() {
  last_lane_ids_.Clear();
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::_internal_last_lane_ids(int index) const {
  return last_lane_ids_.Get(index);
}
inline ::PROTOBUF_NAMESPACE_ID::int32 Lane::last_lane_ids(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.last_lane_ids)
  return _internal_last_lane_ids(index);
}
inline void Lane::set_last_lane_ids(int index, ::PROTOBUF_NAMESPACE_ID::int32 value) {
  last_lane_ids_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.last_lane_ids)
}
inline void Lane::_internal_add_last_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  last_lane_ids_.Add(value);
}
inline void Lane::add_last_lane_ids(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_add_last_lane_ids(value);
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.last_lane_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::_internal_last_lane_ids() const {
  return last_lane_ids_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >&
Lane::last_lane_ids() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.last_lane_ids)
  return _internal_last_lane_ids();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::_internal_mutable_last_lane_ids() {
  return &last_lane_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< ::PROTOBUF_NAMESPACE_ID::int32 >*
Lane::mutable_last_lane_ids() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.last_lane_ids)
  return _internal_mutable_last_lane_ids();
}

// .gwm.hdmap.Lane.SlopeType slope_type = 54;
inline void Lane::clear_slope_type() {
  slope_type_ = 0;
}
inline ::gwm::hdmap::Lane_SlopeType Lane::_internal_slope_type() const {
  return static_cast< ::gwm::hdmap::Lane_SlopeType >(slope_type_);
}
inline ::gwm::hdmap::Lane_SlopeType Lane::slope_type() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.slope_type)
  return _internal_slope_type();
}
inline void Lane::_internal_set_slope_type(::gwm::hdmap::Lane_SlopeType value) {
  
  slope_type_ = value;
}
inline void Lane::set_slope_type(::gwm::hdmap::Lane_SlopeType value) {
  _internal_set_slope_type(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.slope_type)
}

// repeated .gwm.hdmap.Lane.LaneType types = 55;
inline int Lane::_internal_types_size() const {
  return types_.size();
}
inline int Lane::types_size() const {
  return _internal_types_size();
}
inline void Lane::clear_types() {
  types_.Clear();
}
inline ::gwm::hdmap::Lane_LaneType Lane::_internal_types(int index) const {
  return static_cast< ::gwm::hdmap::Lane_LaneType >(types_.Get(index));
}
inline ::gwm::hdmap::Lane_LaneType Lane::types(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.Lane.types)
  return _internal_types(index);
}
inline void Lane::set_types(int index, ::gwm::hdmap::Lane_LaneType value) {
  types_.Set(index, value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.Lane.types)
}
inline void Lane::_internal_add_types(::gwm::hdmap::Lane_LaneType value) {
  types_.Add(value);
}
inline void Lane::add_types(::gwm::hdmap::Lane_LaneType value) {
  // @@protoc_insertion_point(field_add:gwm.hdmap.Lane.types)
  _internal_add_types(value);
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
Lane::types() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.Lane.types)
  return types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
Lane::_internal_mutable_types() {
  return &types_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
Lane::mutable_types() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.Lane.types)
  return _internal_mutable_types();
}

// -------------------------------------------------------------------

// EgoLaneInfo

// int32 lane_id = 1;
inline void EgoLaneInfo::clear_lane_id() {
  lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 EgoLaneInfo::_internal_lane_id() const {
  return lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 EgoLaneInfo::lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.EgoLaneInfo.lane_id)
  return _internal_lane_id();
}
inline void EgoLaneInfo::_internal_set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  lane_id_ = value;
}
inline void EgoLaneInfo::set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.EgoLaneInfo.lane_id)
}

// int32 lane_index = 2;
inline void EgoLaneInfo::clear_lane_index() {
  lane_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 EgoLaneInfo::_internal_lane_index() const {
  return lane_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 EgoLaneInfo::lane_index() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.EgoLaneInfo.lane_index)
  return _internal_lane_index();
}
inline void EgoLaneInfo::_internal_set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  lane_index_ = value;
}
inline void EgoLaneInfo::set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_lane_index(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.EgoLaneInfo.lane_index)
}

// int32 prev_coordinate_index = 3;
inline void EgoLaneInfo::clear_prev_coordinate_index() {
  prev_coordinate_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 EgoLaneInfo::_internal_prev_coordinate_index() const {
  return prev_coordinate_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 EgoLaneInfo::prev_coordinate_index() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.EgoLaneInfo.prev_coordinate_index)
  return _internal_prev_coordinate_index();
}
inline void EgoLaneInfo::_internal_set_prev_coordinate_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  prev_coordinate_index_ = value;
}
inline void EgoLaneInfo::set_prev_coordinate_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_prev_coordinate_index(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.EgoLaneInfo.prev_coordinate_index)
}

// float offset_length_from_prev_point = 4;
inline void EgoLaneInfo::clear_offset_length_from_prev_point() {
  offset_length_from_prev_point_ = 0;
}
inline float EgoLaneInfo::_internal_offset_length_from_prev_point() const {
  return offset_length_from_prev_point_;
}
inline float EgoLaneInfo::offset_length_from_prev_point() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.EgoLaneInfo.offset_length_from_prev_point)
  return _internal_offset_length_from_prev_point();
}
inline void EgoLaneInfo::_internal_set_offset_length_from_prev_point(float value) {
  
  offset_length_from_prev_point_ = value;
}
inline void EgoLaneInfo::set_offset_length_from_prev_point(float value) {
  _internal_set_offset_length_from_prev_point(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.EgoLaneInfo.offset_length_from_prev_point)
}

// float offset_length_from_start_point = 5;
inline void EgoLaneInfo::clear_offset_length_from_start_point() {
  offset_length_from_start_point_ = 0;
}
inline float EgoLaneInfo::_internal_offset_length_from_start_point() const {
  return offset_length_from_start_point_;
}
inline float EgoLaneInfo::offset_length_from_start_point() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.EgoLaneInfo.offset_length_from_start_point)
  return _internal_offset_length_from_start_point();
}
inline void EgoLaneInfo::_internal_set_offset_length_from_start_point(float value) {
  
  offset_length_from_start_point_ = value;
}
inline void EgoLaneInfo::set_offset_length_from_start_point(float value) {
  _internal_set_offset_length_from_start_point(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.EgoLaneInfo.offset_length_from_start_point)
}

// float distance_to_line = 6;
inline void EgoLaneInfo::clear_distance_to_line() {
  distance_to_line_ = 0;
}
inline float EgoLaneInfo::_internal_distance_to_line() const {
  return distance_to_line_;
}
inline float EgoLaneInfo::distance_to_line() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.EgoLaneInfo.distance_to_line)
  return _internal_distance_to_line();
}
inline void EgoLaneInfo::_internal_set_distance_to_line(float value) {
  
  distance_to_line_ = value;
}
inline void EgoLaneInfo::set_distance_to_line(float value) {
  _internal_set_distance_to_line(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.EgoLaneInfo.distance_to_line)
}

// .gwm.common.Point3D projecting_point = 7;
inline bool EgoLaneInfo::_internal_has_projecting_point() const {
  return this != internal_default_instance() && projecting_point_ != nullptr;
}
inline bool EgoLaneInfo::has_projecting_point() const {
  return _internal_has_projecting_point();
}
inline const ::gwm::common::Point3D& EgoLaneInfo::_internal_projecting_point() const {
  const ::gwm::common::Point3D* p = projecting_point_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Point3D&>(
      ::gwm::common::_Point3D_default_instance_);
}
inline const ::gwm::common::Point3D& EgoLaneInfo::projecting_point() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.EgoLaneInfo.projecting_point)
  return _internal_projecting_point();
}
inline void EgoLaneInfo::unsafe_arena_set_allocated_projecting_point(
    ::gwm::common::Point3D* projecting_point) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(projecting_point_);
  }
  projecting_point_ = projecting_point;
  if (projecting_point) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.EgoLaneInfo.projecting_point)
}
inline ::gwm::common::Point3D* EgoLaneInfo::release_projecting_point() {
  
  ::gwm::common::Point3D* temp = projecting_point_;
  projecting_point_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Point3D* EgoLaneInfo::unsafe_arena_release_projecting_point() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.EgoLaneInfo.projecting_point)
  
  ::gwm::common::Point3D* temp = projecting_point_;
  projecting_point_ = nullptr;
  return temp;
}
inline ::gwm::common::Point3D* EgoLaneInfo::_internal_mutable_projecting_point() {
  
  if (projecting_point_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Point3D>(GetArena());
    projecting_point_ = p;
  }
  return projecting_point_;
}
inline ::gwm::common::Point3D* EgoLaneInfo::mutable_projecting_point() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.EgoLaneInfo.projecting_point)
  return _internal_mutable_projecting_point();
}
inline void EgoLaneInfo::set_allocated_projecting_point(::gwm::common::Point3D* projecting_point) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(projecting_point_);
  }
  if (projecting_point) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(projecting_point)->GetArena();
    if (message_arena != submessage_arena) {
      projecting_point = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, projecting_point, submessage_arena);
    }
    
  } else {
    
  }
  projecting_point_ = projecting_point;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.EgoLaneInfo.projecting_point)
}

// -------------------------------------------------------------------

// LanePassableInfo

// int32 lane_id = 1;
inline void LanePassableInfo::clear_lane_id() {
  lane_id_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LanePassableInfo::_internal_lane_id() const {
  return lane_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LanePassableInfo::lane_id() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LanePassableInfo.lane_id)
  return _internal_lane_id();
}
inline void LanePassableInfo::_internal_set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  lane_id_ = value;
}
inline void LanePassableInfo::set_lane_id(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_lane_id(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LanePassableInfo.lane_id)
}

// int32 lane_index = 2;
inline void LanePassableInfo::clear_lane_index() {
  lane_index_ = 0;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LanePassableInfo::_internal_lane_index() const {
  return lane_index_;
}
inline ::PROTOBUF_NAMESPACE_ID::int32 LanePassableInfo::lane_index() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LanePassableInfo.lane_index)
  return _internal_lane_index();
}
inline void LanePassableInfo::_internal_set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  
  lane_index_ = value;
}
inline void LanePassableInfo::set_lane_index(::PROTOBUF_NAMESPACE_ID::int32 value) {
  _internal_set_lane_index(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LanePassableInfo.lane_index)
}

// .gwm.hdmap.LanePassableInfo.LaneAccessibility lane_accessibility = 3;
inline void LanePassableInfo::clear_lane_accessibility() {
  lane_accessibility_ = 0;
}
inline ::gwm::hdmap::LanePassableInfo_LaneAccessibility LanePassableInfo::_internal_lane_accessibility() const {
  return static_cast< ::gwm::hdmap::LanePassableInfo_LaneAccessibility >(lane_accessibility_);
}
inline ::gwm::hdmap::LanePassableInfo_LaneAccessibility LanePassableInfo::lane_accessibility() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LanePassableInfo.lane_accessibility)
  return _internal_lane_accessibility();
}
inline void LanePassableInfo::_internal_set_lane_accessibility(::gwm::hdmap::LanePassableInfo_LaneAccessibility value) {
  
  lane_accessibility_ = value;
}
inline void LanePassableInfo::set_lane_accessibility(::gwm::hdmap::LanePassableInfo_LaneAccessibility value) {
  _internal_set_lane_accessibility(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LanePassableInfo.lane_accessibility)
}

// -------------------------------------------------------------------

// LaneGroup

// repeated .gwm.hdmap.LanePassableInfo lane_frame_info = 1;
inline int LaneGroup::_internal_lane_frame_info_size() const {
  return lane_frame_info_.size();
}
inline int LaneGroup::lane_frame_info_size() const {
  return _internal_lane_frame_info_size();
}
inline void LaneGroup::clear_lane_frame_info() {
  lane_frame_info_.Clear();
}
inline ::gwm::hdmap::LanePassableInfo* LaneGroup::mutable_lane_frame_info(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.LaneGroup.lane_frame_info)
  return lane_frame_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LanePassableInfo >*
LaneGroup::mutable_lane_frame_info() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.LaneGroup.lane_frame_info)
  return &lane_frame_info_;
}
inline const ::gwm::hdmap::LanePassableInfo& LaneGroup::_internal_lane_frame_info(int index) const {
  return lane_frame_info_.Get(index);
}
inline const ::gwm::hdmap::LanePassableInfo& LaneGroup::lane_frame_info(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LaneGroup.lane_frame_info)
  return _internal_lane_frame_info(index);
}
inline ::gwm::hdmap::LanePassableInfo* LaneGroup::_internal_add_lane_frame_info() {
  return lane_frame_info_.Add();
}
inline ::gwm::hdmap::LanePassableInfo* LaneGroup::add_lane_frame_info() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.LaneGroup.lane_frame_info)
  return _internal_add_lane_frame_info();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LanePassableInfo >&
LaneGroup::lane_frame_info() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.LaneGroup.lane_frame_info)
  return lane_frame_info_;
}

// -------------------------------------------------------------------

// RoutingLaneInfo

// double time_stamp = 1;
inline void RoutingLaneInfo::clear_time_stamp() {
  time_stamp_ = 0;
}
inline double RoutingLaneInfo::_internal_time_stamp() const {
  return time_stamp_;
}
inline double RoutingLaneInfo::time_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.RoutingLaneInfo.time_stamp)
  return _internal_time_stamp();
}
inline void RoutingLaneInfo::_internal_set_time_stamp(double value) {
  
  time_stamp_ = value;
}
inline void RoutingLaneInfo::set_time_stamp(double value) {
  _internal_set_time_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.RoutingLaneInfo.time_stamp)
}

// repeated .gwm.hdmap.EgoLaneInfo ego_lane_info = 2;
inline int RoutingLaneInfo::_internal_ego_lane_info_size() const {
  return ego_lane_info_.size();
}
inline int RoutingLaneInfo::ego_lane_info_size() const {
  return _internal_ego_lane_info_size();
}
inline void RoutingLaneInfo::clear_ego_lane_info() {
  ego_lane_info_.Clear();
}
inline ::gwm::hdmap::EgoLaneInfo* RoutingLaneInfo::mutable_ego_lane_info(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.RoutingLaneInfo.ego_lane_info)
  return ego_lane_info_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::EgoLaneInfo >*
RoutingLaneInfo::mutable_ego_lane_info() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.RoutingLaneInfo.ego_lane_info)
  return &ego_lane_info_;
}
inline const ::gwm::hdmap::EgoLaneInfo& RoutingLaneInfo::_internal_ego_lane_info(int index) const {
  return ego_lane_info_.Get(index);
}
inline const ::gwm::hdmap::EgoLaneInfo& RoutingLaneInfo::ego_lane_info(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.RoutingLaneInfo.ego_lane_info)
  return _internal_ego_lane_info(index);
}
inline ::gwm::hdmap::EgoLaneInfo* RoutingLaneInfo::_internal_add_ego_lane_info() {
  return ego_lane_info_.Add();
}
inline ::gwm::hdmap::EgoLaneInfo* RoutingLaneInfo::add_ego_lane_info() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.RoutingLaneInfo.ego_lane_info)
  return _internal_add_ego_lane_info();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::EgoLaneInfo >&
RoutingLaneInfo::ego_lane_info() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.RoutingLaneInfo.ego_lane_info)
  return ego_lane_info_;
}

// repeated .gwm.hdmap.LaneGroup lane_groups = 3;
inline int RoutingLaneInfo::_internal_lane_groups_size() const {
  return lane_groups_.size();
}
inline int RoutingLaneInfo::lane_groups_size() const {
  return _internal_lane_groups_size();
}
inline void RoutingLaneInfo::clear_lane_groups() {
  lane_groups_.Clear();
}
inline ::gwm::hdmap::LaneGroup* RoutingLaneInfo::mutable_lane_groups(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.RoutingLaneInfo.lane_groups)
  return lane_groups_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneGroup >*
RoutingLaneInfo::mutable_lane_groups() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.RoutingLaneInfo.lane_groups)
  return &lane_groups_;
}
inline const ::gwm::hdmap::LaneGroup& RoutingLaneInfo::_internal_lane_groups(int index) const {
  return lane_groups_.Get(index);
}
inline const ::gwm::hdmap::LaneGroup& RoutingLaneInfo::lane_groups(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.RoutingLaneInfo.lane_groups)
  return _internal_lane_groups(index);
}
inline ::gwm::hdmap::LaneGroup* RoutingLaneInfo::_internal_add_lane_groups() {
  return lane_groups_.Add();
}
inline ::gwm::hdmap::LaneGroup* RoutingLaneInfo::add_lane_groups() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.RoutingLaneInfo.lane_groups)
  return _internal_add_lane_groups();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LaneGroup >&
RoutingLaneInfo::lane_groups() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.RoutingLaneInfo.lane_groups)
  return lane_groups_;
}

// -------------------------------------------------------------------

// RoutingMapInfo

// repeated .gwm.hdmap.Lane lanes = 1;
inline int RoutingMapInfo::_internal_lanes_size() const {
  return lanes_.size();
}
inline int RoutingMapInfo::lanes_size() const {
  return _internal_lanes_size();
}
inline void RoutingMapInfo::clear_lanes() {
  lanes_.Clear();
}
inline ::gwm::hdmap::Lane* RoutingMapInfo::mutable_lanes(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.RoutingMapInfo.lanes)
  return lanes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::Lane >*
RoutingMapInfo::mutable_lanes() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.RoutingMapInfo.lanes)
  return &lanes_;
}
inline const ::gwm::hdmap::Lane& RoutingMapInfo::_internal_lanes(int index) const {
  return lanes_.Get(index);
}
inline const ::gwm::hdmap::Lane& RoutingMapInfo::lanes(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.RoutingMapInfo.lanes)
  return _internal_lanes(index);
}
inline ::gwm::hdmap::Lane* RoutingMapInfo::_internal_add_lanes() {
  return lanes_.Add();
}
inline ::gwm::hdmap::Lane* RoutingMapInfo::add_lanes() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.RoutingMapInfo.lanes)
  return _internal_add_lanes();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::Lane >&
RoutingMapInfo::lanes() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.RoutingMapInfo.lanes)
  return lanes_;
}

// repeated .gwm.hdmap.RoutingLaneInfo routing_lanes = 2;
inline int RoutingMapInfo::_internal_routing_lanes_size() const {
  return routing_lanes_.size();
}
inline int RoutingMapInfo::routing_lanes_size() const {
  return _internal_routing_lanes_size();
}
inline void RoutingMapInfo::clear_routing_lanes() {
  routing_lanes_.Clear();
}
inline ::gwm::hdmap::RoutingLaneInfo* RoutingMapInfo::mutable_routing_lanes(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.RoutingMapInfo.routing_lanes)
  return routing_lanes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::RoutingLaneInfo >*
RoutingMapInfo::mutable_routing_lanes() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.RoutingMapInfo.routing_lanes)
  return &routing_lanes_;
}
inline const ::gwm::hdmap::RoutingLaneInfo& RoutingMapInfo::_internal_routing_lanes(int index) const {
  return routing_lanes_.Get(index);
}
inline const ::gwm::hdmap::RoutingLaneInfo& RoutingMapInfo::routing_lanes(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.RoutingMapInfo.routing_lanes)
  return _internal_routing_lanes(index);
}
inline ::gwm::hdmap::RoutingLaneInfo* RoutingMapInfo::_internal_add_routing_lanes() {
  return routing_lanes_.Add();
}
inline ::gwm::hdmap::RoutingLaneInfo* RoutingMapInfo::add_routing_lanes() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.RoutingMapInfo.routing_lanes)
  return _internal_add_routing_lanes();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::RoutingLaneInfo >&
RoutingMapInfo::routing_lanes() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.RoutingMapInfo.routing_lanes)
  return routing_lanes_;
}

// -------------------------------------------------------------------

// LocationInfo

// .gwm.common.Point3D point = 1;
inline bool LocationInfo::_internal_has_point() const {
  return this != internal_default_instance() && point_ != nullptr;
}
inline bool LocationInfo::has_point() const {
  return _internal_has_point();
}
inline const ::gwm::common::Point3D& LocationInfo::_internal_point() const {
  const ::gwm::common::Point3D* p = point_;
  return p != nullptr ? *p : reinterpret_cast<const ::gwm::common::Point3D&>(
      ::gwm::common::_Point3D_default_instance_);
}
inline const ::gwm::common::Point3D& LocationInfo::point() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LocationInfo.point)
  return _internal_point();
}
inline void LocationInfo::unsafe_arena_set_allocated_point(
    ::gwm::common::Point3D* point) {
  if (GetArena() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_);
  }
  point_ = point;
  if (point) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:gwm.hdmap.LocationInfo.point)
}
inline ::gwm::common::Point3D* LocationInfo::release_point() {
  
  ::gwm::common::Point3D* temp = point_;
  point_ = nullptr;
  if (GetArena() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
  return temp;
}
inline ::gwm::common::Point3D* LocationInfo::unsafe_arena_release_point() {
  // @@protoc_insertion_point(field_release:gwm.hdmap.LocationInfo.point)
  
  ::gwm::common::Point3D* temp = point_;
  point_ = nullptr;
  return temp;
}
inline ::gwm::common::Point3D* LocationInfo::_internal_mutable_point() {
  
  if (point_ == nullptr) {
    auto* p = CreateMaybeMessage<::gwm::common::Point3D>(GetArena());
    point_ = p;
  }
  return point_;
}
inline ::gwm::common::Point3D* LocationInfo::mutable_point() {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.LocationInfo.point)
  return _internal_mutable_point();
}
inline void LocationInfo::set_allocated_point(::gwm::common::Point3D* point) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArena();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(point_);
  }
  if (point) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(point)->GetArena();
    if (message_arena != submessage_arena) {
      point = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, point, submessage_arena);
    }
    
  } else {
    
  }
  point_ = point;
  // @@protoc_insertion_point(field_set_allocated:gwm.hdmap.LocationInfo.point)
}

// double radias = 2;
inline void LocationInfo::clear_radias() {
  radias_ = 0;
}
inline double LocationInfo::_internal_radias() const {
  return radias_;
}
inline double LocationInfo::radias() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LocationInfo.radias)
  return _internal_radias();
}
inline void LocationInfo::_internal_set_radias(double value) {
  
  radias_ = value;
}
inline void LocationInfo::set_radias(double value) {
  _internal_set_radias(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LocationInfo.radias)
}

// double heading_degree = 3;
inline void LocationInfo::clear_heading_degree() {
  heading_degree_ = 0;
}
inline double LocationInfo::_internal_heading_degree() const {
  return heading_degree_;
}
inline double LocationInfo::heading_degree() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LocationInfo.heading_degree)
  return _internal_heading_degree();
}
inline void LocationInfo::_internal_set_heading_degree(double value) {
  
  heading_degree_ = value;
}
inline void LocationInfo::set_heading_degree(double value) {
  _internal_set_heading_degree(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LocationInfo.heading_degree)
}

// double max_tolerance_angle = 4;
inline void LocationInfo::clear_max_tolerance_angle() {
  max_tolerance_angle_ = 0;
}
inline double LocationInfo::_internal_max_tolerance_angle() const {
  return max_tolerance_angle_;
}
inline double LocationInfo::max_tolerance_angle() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LocationInfo.max_tolerance_angle)
  return _internal_max_tolerance_angle();
}
inline void LocationInfo::_internal_set_max_tolerance_angle(double value) {
  
  max_tolerance_angle_ = value;
}
inline void LocationInfo::set_max_tolerance_angle(double value) {
  _internal_set_max_tolerance_angle(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LocationInfo.max_tolerance_angle)
}

// double time_stamp = 5;
inline void LocationInfo::clear_time_stamp() {
  time_stamp_ = 0;
}
inline double LocationInfo::_internal_time_stamp() const {
  return time_stamp_;
}
inline double LocationInfo::time_stamp() const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.LocationInfo.time_stamp)
  return _internal_time_stamp();
}
inline void LocationInfo::_internal_set_time_stamp(double value) {
  
  time_stamp_ = value;
}
inline void LocationInfo::set_time_stamp(double value) {
  _internal_set_time_stamp(value);
  // @@protoc_insertion_point(field_set:gwm.hdmap.LocationInfo.time_stamp)
}

// -------------------------------------------------------------------

// TrackList

// repeated .gwm.hdmap.LocationInfo locations = 1;
inline int TrackList::_internal_locations_size() const {
  return locations_.size();
}
inline int TrackList::locations_size() const {
  return _internal_locations_size();
}
inline void TrackList::clear_locations() {
  locations_.Clear();
}
inline ::gwm::hdmap::LocationInfo* TrackList::mutable_locations(int index) {
  // @@protoc_insertion_point(field_mutable:gwm.hdmap.TrackList.locations)
  return locations_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LocationInfo >*
TrackList::mutable_locations() {
  // @@protoc_insertion_point(field_mutable_list:gwm.hdmap.TrackList.locations)
  return &locations_;
}
inline const ::gwm::hdmap::LocationInfo& TrackList::_internal_locations(int index) const {
  return locations_.Get(index);
}
inline const ::gwm::hdmap::LocationInfo& TrackList::locations(int index) const {
  // @@protoc_insertion_point(field_get:gwm.hdmap.TrackList.locations)
  return _internal_locations(index);
}
inline ::gwm::hdmap::LocationInfo* TrackList::_internal_add_locations() {
  return locations_.Add();
}
inline ::gwm::hdmap::LocationInfo* TrackList::add_locations() {
  // @@protoc_insertion_point(field_add:gwm.hdmap.TrackList.locations)
  return _internal_add_locations();
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::gwm::hdmap::LocationInfo >&
TrackList::locations() const {
  // @@protoc_insertion_point(field_list:gwm.hdmap.TrackList.locations)
  return locations_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace hdmap
}  // namespace gwm

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::gwm::hdmap::LaneBoundaryType_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::LaneBoundaryType_Type>() {
  return ::gwm::hdmap::LaneBoundaryType_Type_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::LaneBoundary_Crossable> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::LaneBoundary_Crossable>() {
  return ::gwm::hdmap::LaneBoundary_Crossable_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::LaneRule_VehicleType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::LaneRule_VehicleType>() {
  return ::gwm::hdmap::LaneRule_VehicleType_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::MergeSplit_Direction> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::MergeSplit_Direction>() {
  return ::gwm::hdmap::MergeSplit_Direction_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::Lane_LaneType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::Lane_LaneType>() {
  return ::gwm::hdmap::Lane_LaneType_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::Lane_LaneTurn> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::Lane_LaneTurn>() {
  return ::gwm::hdmap::Lane_LaneTurn_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::Lane_LaneDirection> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::Lane_LaneDirection>() {
  return ::gwm::hdmap::Lane_LaneDirection_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::Lane_BoundaryDirection> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::Lane_BoundaryDirection>() {
  return ::gwm::hdmap::Lane_BoundaryDirection_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::Lane_MergeType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::Lane_MergeType>() {
  return ::gwm::hdmap::Lane_MergeType_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::Lane_SlopeType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::Lane_SlopeType>() {
  return ::gwm::hdmap::Lane_SlopeType_descriptor();
}
template <> struct is_proto_enum< ::gwm::hdmap::LanePassableInfo_LaneAccessibility> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::gwm::hdmap::LanePassableInfo_LaneAccessibility>() {
  return ::gwm::hdmap::LanePassableInfo_LaneAccessibility_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_hd_5fmap_5flane_2eproto
