syntax = "proto3";

package gwm.hdmap;

// import "semantic_map/map_geometry.proto";
// import "semantic_map/map_overlap.proto";
import "common/geometry.proto";

message LaneBoundaryType {
  enum Type {
    UNKNOWN = 0;
    DOTTED_YELLOW = 1;
    DOTTED_WHITE = 2;
    SOLID_YELLOW = 3;
    SOLID_WHITE = 4;
    DOUBLE_YELLOW = 5; // Currently unused.
    CURB = 6;
    DOUBLE_SOLID_YELLOW = 7;
    DOUBLE_DOTTED_WHITE = 8;
    DOUBLE_DOTTED_YELLOW = 9;
    DOUBLE_SOLID_WHITE = 10;
    DOUBLE_LEFT_TO_RIGHT = 11; // Represent DOUBLE_LEFT_TO_RIGHT_WHITE!!!!!!
    DOUBLE_RIGHT_TO_LEFT = 12; // Represent DOUBLE_RIGHT_TO_LEFT_WHITE!!!!!!
    PEDESTRIAN_POLE = 13; // "PEDESTRIAN_POLE" represents a pole-shaped-passage
                          // that is restricted to pedestrians at the intersection.
    OTHER_PHYSICAL_OBSTACLE = 14; // Represents other physical obstacles that all traffic agents cannot pass.
                                  // such as railings and sound-insulation walls, etc.
    DOUBLE_LEFT_TO_RIGHT_YELLOW = 15;
    DOUBLE_RIGHT_TO_LEFT_YELLOW = 16; 
    SHORT_THICK_DOTTED_WHITE = 17; //Represent the entry/exit line on freeway ramp

    DOUBLE_LEFT_WHITE_TO_RIGHT_YELLOW = 18;  // 左白虚右黄实线
    DOUBLE_RIGHT_WHITE_TO_LEFT_YELLOW = 19;  // 右白虚左黄实线

    DOUBLE_LEFT_YELLOW_TO_RIGHT_WHITE = 20;  // 左黄虚右白实线
    DOUBLE_RIGHT_YELLOW_TO_LEFT_WHITE = 21;  // 右黄虚左白实线

    SOLID_ORANGE = 22;  // 橙色实线
    DOTTED_ORANGE = 23; // 橙色虚线

    SOLID_BLUE = 24;   // 蓝色实线
    DOTTED_BLUE = 25;  // 蓝色虚线

    EDGE = 26;  // 道路边界

    DOUBLE_SOLID_LEFT_YELLOW_AND_RIGHT_WHITE = 27;   // 左黄右白双实线
    DOUBLE_DOTTED_LEFT_YELLOW_AND_RIGHT_WHITE = 28;  // 左黄右白双虚线    
    DOUBLE_SOLID_LEFT_WHITE_AND_RIGHT_YELLOW = 29;   // 左白右黄双实线
    DOUBLE_DOTTED_LEFT_WHITE_AND_RIGHT_YELLOW = 30;  // 左白右黄双虚线

  };
  // Offset relative to the starting point of boundary
  double s = 1;
  // support multiple types
  repeated Type types = 2;
}

message LaneBoundary {
  //Curve curve = 1; // Currently unused.

  double length = 2; //----
  // indicate whether the lane boundary exists in real world
  bool virtual = 3;
  repeated LaneBoundaryType boundary_type = 4; // Currently unused. //----

  int32 id = 5; //----
  // 'boundary' represente the centerlines of both single and double lines.
  // 'dotted_line_geometry' below described two endpoints of each ground marking section of dotted-lines.
  // The geometry information of the current lane is generated by boundary (left_boundary_id, right_boundary_id).
  // 'dotted_line_geometry' is usually used to localization.
  gwm.common.Polyline boundary = 6; //----

  enum Crossable {
    PHYSICALLY_NOT = 0; // This field represent buondary is PHYSICALLY_NOT for all type.
    LEGALLY_NOT = 1; // For all type, LEGALLY_NOT represent LEGALLY_NOT but PHYSICALLY_YES.
    RIGHT_TO_LEFT = 2;
    LEFT_TO_RIGHT = 3;
    BOTH = 4;
    CAR_PHYSICALLY_NOT = 5; // This type represent boundary is PHYSICALLY_NOT for car,
                            // but PHYSICALLY_YES for pedestrian、bicycle、motorcycle or other two wheeled vehicle.
  }
  Crossable crossable = 7;

  double cost = 8; // Currently unused, only use 'cost' in Lane.

  repeated int32 layers = 9;
  LaneBoundaryType.Type type = 10; //----
  // Only dotted lines has this element.
  // 'dotted_line_geometry' described two endpoints of each ground marking section of dotted-lines.
  // Dotted lines represented by several boundary_segment.
  // 'boundary' in 'LaneBoundary' represent the centerlines of both single and double lines.
  // The geometry information of the current lane is generated by boundary (left_boundary_id, right_boundary_id).
  // 'dotted_line_geometry' is usually used to localization.
  //DottedLineGeometry dotted_line_geometry = 11;

  //deceleration_marking表示纵向减速标识线,用于提示车辆应当减速慢行,与行车方向平行，以单虚线方式呈现，通常在车道线内侧
  //分为左右两侧
  bool has_left_deceleration_marking = 12;   
  bool has_right_deceleration_marking = 13;
}

// Association between central point to closest boundary.
message LaneSampleAssociation { //---- 取值待讨论
  double s = 1;
  double width = 2;
}

message Entrance{ // Currently unused.
  int32 id = 1;
  int32 lane_id = 2;
  gwm.common.Point3D location = 3;
  repeated int32 layers = 4;
}

message Rule {
  oneof rule{
    float speed_limit = 1; //---- 限速需要
    bool disabled = 2;
  }
}

// Trigger is a member of "LaneRule", who indicates the trigger condition of the
// corresponding rule of this "LaneRule".
message Trigger {
  oneof trigger{
    bool always = 1;
  }
}

message LaneRule {
  enum VehicleType {
    DEFAULT = 0;
    LIGHT_TRUCK = 1;
  }
  VehicleType vehicle_type = 1;
  Trigger trigger = 2;
  Rule rule = 3;
}
  
message MergeSplit {//----
  enum Direction{
    UNKNOWN = 0;
    LEFT = 1;
    RIGHT = 2;
  }
  message Merge {
    Direction direction = 1;
    int32 to_lane_id = 2;
  }
  message Split {
    Direction direction = 1;
    int32 from_lane_id = 2;
  }
  oneof type{
    Merge merge = 1;
    Split split = 2;
  }
}

message NeighborMerge {
  message Ids {
    repeated int32 ids = 1;
  }
  // Only the current lane has a successor_id and lane(successor_id)->predecessor_id > 1, the following two fields will be filled.
  // The meaning of "merge_to_lane_id" and "merge_from_lane_id" can reference the file: https://rqk9rsooi4.feishu.cn/docs/doccnKVIB9rwMtpTQ0EoxfTblHL
  oneof type{
    Ids merge_from_lane_ids = 1;
    int32 merge_to_lane_id = 2;
  }
  //"successor_lane_id" represent the based successor lane of current "NeighborMerge" compute.
  int32 successor_lane_id = 3;
}

// A lane is part of a roadway, that is designated for use by a single line of
// vehicles.
// Most public roads (include highways) have more than two lanes.
message Lane {
  int32 id = 1; //----

  // Central lane as reference trajectory, not necessary to be the geometry
  // central.
  //Curve central_curve = 2; // Currently unused.

  // Lane boundary curve.
  LaneBoundary left_boundary = 3; // Currently unused. //----
  LaneBoundary right_boundary = 4; // Currently unused. //----

  // in meters.
  double length = 5; // Currently unused.//----

  // Speed limit of the lane, in meters per second.
  float speed_limit = 6;//----

  repeated int32 overlap_id = 7; // Currently unused.

  // All lanes can be driving into (or from).
  repeated int32 predecessor_id = 8;//----
  repeated int32 successor_id = 9;//----

  // Neighbor lanes on the same direction.
  int32 left_neighbor_forward_lane_id = 10;//----
  int32 right_neighbor_forward_lane_id = 11;//----

  enum LaneType {
    UNKNOWN = 0;
    HIGHWAY = 1;  // Controlled access. Expects no pedestrians or bicycles.
    STREET = 2;  // No access control. Expects pedestrians and bicycles.
    BIDIRECTIONAL = 3;  // Continuous two-way left-turn lane.
    SHOULDER = 4;  // Emergency stopping lane
    BIKING = 5;
    SIDEWALK = 6;
    RESTRICTED = 7;
    PARKING = 8;
    ROADWORK = 9;
    OFFRAMP = 10;
    ONRAMP = 11;
    BUSLANE = 12;
    LEFTTURNWAITINGAREA = 13;
    PARK = 14; // Lanes located inside high-tech parks or industrial parks.
    ROUNDABOUT = 15;
    RIGHT_TURN_ONLY = 16;  
    PARK_ON_LANE = 17;
    DYNAMIC_LANE = 18;   //可变车道
    WIDE_LANE = 19;      //超宽道
    TIDAL_LANE = 20;     //潮汐车道
    TRANSFER_LANE = 21;  //中转车道
    EMERGENCY = 22; // 应急车道
  };
  LaneType type = 12;//----

  // The turn type when drive from predecessor lane to this lane.
  enum LaneTurn {
    INVALID = 0;
    STRAIGHT = 1;
    LEFT = 2;
    RIGHT = 4;
    U_TURN_LEFT = 8;
    U_TURN_RIGHT = 16;
  };
  LaneTurn turn = 13;
  
  int32 left_neighbor_reverse_lane_id = 14;
  int32 right_neighbor_reverse_lane_id = 15;

  int32 junction_id = 16; // Currently unused.

  // Association between central point to closest boundary.
  repeated LaneSampleAssociation left_sample = 17; // Currently unused. //----
  repeated LaneSampleAssociation right_sample = 18; // Currently unused. //----

  enum LaneDirection {
    FORWARD = 0;
    BACKWARD = 1;
    BIDIRECTION = 2;
  }
  LaneDirection direction = 19; // Currently unused.//----  如果有就增加一下

  // Association between central point to closest road boundary.
  repeated LaneSampleAssociation left_road_sample = 20; // Currently unused.//----
  repeated LaneSampleAssociation right_road_sample = 21; // Currently unused.//----

  repeated int32 self_reverse_lane_id = 22; // Currently unused.

  common.Polyline centerline = 23;  // Includes height information.//----
  repeated float centerline_s = 24 [packed = true];  // In m.//----

  int32 left_boundary_id = 25;//----
  int32 right_boundary_id = 26;//----

  enum BoundaryDirection {
    SAME = 0;
    LEFT_REVERSE = 1;
    RIGHT_REVERSE = 2;
    BOTH_REVERSE = 3;
  }
  BoundaryDirection boundary_direction = 27;

 // repeated Overlap overlaps = 28;
  // The routing algorithm will refer to the value of cost to find the shortest path. 
  // The current usage is to adjust the cost of this lane to a large value 
  // if you don’t want the car to walk in a certain lane.
  double cost = 29;//----
  
  // Merge direction of the merge-in lane
  enum MergeType {
    NONE = 0;
    FROM_LEFT = 1;
    FROM_RIGHT = 2; 
  }
  MergeType merge = 30; // Currently unused.//----

  // Only the current lane has a successor_id and lane(successor_id)->predecessor_id > 1, the following two fields will be filled.
  // The meaning of merge_to_lane_id and merge_from_lane_id can reference the file: https://rqk9rsooi4.feishu.cn/docs/doccnKVIB9rwMtpTQ0EoxfTblHL
  int32 merge_to_lane_id = 31; // Currently unused. Replaced by "NeighborMerge".//----
  repeated int32 merge_from_lane_id = 32; // Currently unused. Replaced by "NeighborMerge".//----

  repeated int32 layers = 33;

  //indicate whether this lane is a copy of another bidirectional real lane
  int32 bidi_copy_from_id = 34; // Currently unused.
  // Lane neighbor requires a shared Boundary.
  // When there is no shared boundary but the neighbor needs to be forcibly set, fill in this field.
  // It mainly deals with the situation of the hole (side road).
  // The lanes on both sides of the hole need to be added manually_set_left_neighbor_forward_lane_id information.
  int32 manually_set_left_neighbor_forward_lane_id = 35;
  int32 manually_set_right_neighbor_forward_lane_id = 36;

  // Road name in the real world, encoded in utf-8
  repeated string road_name = 37;
  // Car is not permitted to run into this lane
  bool is_disabled = 38;
  // Virtual lane doesn't exist in the real world
  // Deprecated due to same field existed in message LaneBoundary.
  bool is_virtual = 39 [deprecated = true];

  // If our car type is truck and this field exists, use this for the lane speed limit.
  float truck_speed_limit = 40; // In m/s.
  float min_width = 41;  // In m.//----
  float max_curvature = 42; // In m^-1.//----
  // The heading_diff between the current lane and its successor is calculated by taking the 
  // last two points of the lane's centerline to calculate the angle, then taking the first two points
  // of the lane's successor's centerline to calculate the angle, and then taking the absolute value of the angle difference.
  float heading_diff = 43; // In radians.

  // "lane_rules" is repeated rule of this lane, which contains the vehicle type、trigger conditions and corresponding rules.
  // When the user uses this field, all repeated "lane_rule" must be read, and then decide which rule to use based on the field
  // contained in the "LaneRule"、the current vehicle type and other information.
  repeated LaneRule lane_rules = 44;//----
  // "boundary_plus" represents another boundary associate with boundary.
  // Such as:
  // 1.railing placed above the boundary
  // 2.green belt close to boundary
  // 3.kerb close to the boundary， etc.
  repeated int32 left_boundary_plus_ids = 45;
  repeated int32 right_boundary_plus_ids = 46;
  repeated MergeSplit merge_splits = 47;  //----
  repeated int32 road_section_ids = 48;
  repeated NeighborMerge neighbor_merges = 49;
  //Used to cache the successor relationships for manual settings during the annotation stage
  repeated int32 manually_set_predecessor_ids = 50 [deprecated = true];
  repeated int32 manually_set_successor_ids = 51 [deprecated = true]; 
  //"next lane" and "last lane" represent discontinuous successors and predecessors
  repeated int32 next_lane_ids = 52;
  repeated int32 last_lane_ids = 53;  

  enum SlopeType {
    UPHILL = 0;
    DOWNHILL = 1;
  };
  SlopeType slope_type = 54;
  //support many lane types
  repeated LaneType types = 55;
}

message EgoLaneInfo {
  int32 lane_id = 1;
  int32 lane_index = 2;
  // 垂足在线上前一个点下标
  int32 prev_coordinate_index = 3;
  // 垂足到线上前一个点距离，单位：米 
  float offset_length_from_prev_point = 4;
  // 垂足到线上第一个点距离，单位：米
  float offset_length_from_start_point = 5;
  // 原始点到垂足的距离，参考线左侧为正，右侧为负，单位：米
  float distance_to_line = 6;
  // 垂足坐标点
  gwm.common.Point3D  projecting_point = 7;
}

message LanePassableInfo{
  int32 lane_id = 1;
  int32 lane_index = 2;
  // Lane accessibility.
  enum LaneAccessibility {
	DEFAULT = 0; // default value, unused Currently.
    RECOMMEND = 1; // recommend lane.
    NOT_RECOMMEND = 2; // not recommend lane.
  };
  // Set whether the lane is recommended lane.
  LaneAccessibility lane_accessibility = 3;
}

// 按帧率每一帧给的车道索引信息
message LaneGroup {
  // 按从左到右顺序排列所有车道序号
  repeated LanePassableInfo lane_frame_info = 1;
}

//
message RoutingLaneInfo {
  // 定位信息帧率时间
  double time_stamp = 1;
  // 自车所在车道索信息
  repeated EgoLaneInfo ego_lane_info = 2;
  // 按帧率设置的车道索引信息
  repeated LaneGroup lane_groups = 3;
}

message RoutingMapInfo {
  // 本次轨迹获取到的所有车道的高精数据信息
  repeated Lane lanes = 1;
  // 按帧率设置的车道索引信息
  repeated RoutingLaneInfo  routing_lanes = 2;
}

message LocationInfo {
  // 定位点
  gwm.common.Point3D  point = 1;
  // 搜索半径
  double radias = 2;
  // 航线角度 [0, 360)
  double heading_degree = 3;
  // 最大容忍角度偏差 [0.0, 360)
  double max_tolerance_angle = 4;
  // 定位信息帧率时间
  double time_stamp = 5;
}
message TrackList {
  repeated LocationInfo locations = 1;
};
