// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rtk_gps.proto

#include "rtk_gps.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_rtk_5fgps_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_GNSSInfo_rtk_5fgps_2eproto;
namespace gwm {
namespace hdmap {
class GNSSInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<GNSSInfo> _instance;
} _GNSSInfo_default_instance_;
class GNSSCollectionDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<GNSSCollection> _instance;
} _GNSSCollection_default_instance_;
}  // namespace hdmap
}  // namespace gwm
static void InitDefaultsscc_info_GNSSCollection_rtk_5fgps_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_GNSSCollection_default_instance_;
    new (ptr) ::gwm::hdmap::GNSSCollection();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_GNSSCollection_rtk_5fgps_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_GNSSCollection_rtk_5fgps_2eproto}, {
      &scc_info_GNSSInfo_rtk_5fgps_2eproto.base,}};

static void InitDefaultsscc_info_GNSSInfo_rtk_5fgps_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::hdmap::_GNSSInfo_default_instance_;
    new (ptr) ::gwm::hdmap::GNSSInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_GNSSInfo_rtk_5fgps_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_GNSSInfo_rtk_5fgps_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_rtk_5fgps_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_rtk_5fgps_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_rtk_5fgps_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_rtk_5fgps_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSInfo, time_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSInfo, easting_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSInfo, northing_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSInfo, up_),
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSInfo, status_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSCollection, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::hdmap::GNSSCollection, gnss_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::gwm::hdmap::GNSSInfo)},
  { 10, -1, sizeof(::gwm::hdmap::GNSSCollection)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_GNSSInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::hdmap::_GNSSCollection_default_instance_),
};

const char descriptor_table_protodef_rtk_5fgps_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\rrtk_gps.proto\022\tgwm.hdmap\"]\n\010GNSSInfo\022\022"
  "\n\ntime_stamp\030\001 \001(\003\022\017\n\007easting\030\002 \001(\001\022\020\n\010n"
  "orthing\030\003 \001(\001\022\n\n\002up\030\004 \001(\001\022\016\n\006status\030\005 \001("
  "\005\"3\n\016GNSSCollection\022!\n\004gnss\030\001 \003(\0132\023.gwm."
  "hdmap.GNSSInfob\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_rtk_5fgps_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_rtk_5fgps_2eproto_sccs[2] = {
  &scc_info_GNSSCollection_rtk_5fgps_2eproto.base,
  &scc_info_GNSSInfo_rtk_5fgps_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_rtk_5fgps_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_rtk_5fgps_2eproto = {
  false, false, descriptor_table_protodef_rtk_5fgps_2eproto, "rtk_gps.proto", 182,
  &descriptor_table_rtk_5fgps_2eproto_once, descriptor_table_rtk_5fgps_2eproto_sccs, descriptor_table_rtk_5fgps_2eproto_deps, 2, 0,
  schemas, file_default_instances, TableStruct_rtk_5fgps_2eproto::offsets,
  file_level_metadata_rtk_5fgps_2eproto, 2, file_level_enum_descriptors_rtk_5fgps_2eproto, file_level_service_descriptors_rtk_5fgps_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_rtk_5fgps_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_rtk_5fgps_2eproto)), true);
namespace gwm {
namespace hdmap {

// ===================================================================

class GNSSInfo::_Internal {
 public:
};

GNSSInfo::GNSSInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.GNSSInfo)
}
GNSSInfo::GNSSInfo(const GNSSInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&time_stamp_, &from.time_stamp_,
    static_cast<size_t>(reinterpret_cast<char*>(&status_) -
    reinterpret_cast<char*>(&time_stamp_)) + sizeof(status_));
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.GNSSInfo)
}

void GNSSInfo::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&time_stamp_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&status_) -
      reinterpret_cast<char*>(&time_stamp_)) + sizeof(status_));
}

GNSSInfo::~GNSSInfo() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.GNSSInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void GNSSInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void GNSSInfo::ArenaDtor(void* object) {
  GNSSInfo* _this = reinterpret_cast< GNSSInfo* >(object);
  (void)_this;
}
void GNSSInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GNSSInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const GNSSInfo& GNSSInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_GNSSInfo_rtk_5fgps_2eproto.base);
  return *internal_default_instance();
}


void GNSSInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.GNSSInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&time_stamp_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_) -
      reinterpret_cast<char*>(&time_stamp_)) + sizeof(status_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GNSSInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int64 time_stamp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          time_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double easting = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          easting_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double northing = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          northing_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double up = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 33)) {
          up_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // int32 status = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          status_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GNSSInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.GNSSInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 time_stamp = 1;
  if (this->time_stamp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_time_stamp(), target);
  }

  // double easting = 2;
  if (!(this->easting() <= 0 && this->easting() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_easting(), target);
  }

  // double northing = 3;
  if (!(this->northing() <= 0 && this->northing() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_northing(), target);
  }

  // double up = 4;
  if (!(this->up() <= 0 && this->up() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_up(), target);
  }

  // int32 status = 5;
  if (this->status() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_status(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.GNSSInfo)
  return target;
}

size_t GNSSInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.GNSSInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 time_stamp = 1;
  if (this->time_stamp() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_time_stamp());
  }

  // double easting = 2;
  if (!(this->easting() <= 0 && this->easting() >= 0)) {
    total_size += 1 + 8;
  }

  // double northing = 3;
  if (!(this->northing() <= 0 && this->northing() >= 0)) {
    total_size += 1 + 8;
  }

  // double up = 4;
  if (!(this->up() <= 0 && this->up() >= 0)) {
    total_size += 1 + 8;
  }

  // int32 status = 5;
  if (this->status() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_status());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GNSSInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.GNSSInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const GNSSInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<GNSSInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.GNSSInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.GNSSInfo)
    MergeFrom(*source);
  }
}

void GNSSInfo::MergeFrom(const GNSSInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.GNSSInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.time_stamp() != 0) {
    _internal_set_time_stamp(from._internal_time_stamp());
  }
  if (!(from.easting() <= 0 && from.easting() >= 0)) {
    _internal_set_easting(from._internal_easting());
  }
  if (!(from.northing() <= 0 && from.northing() >= 0)) {
    _internal_set_northing(from._internal_northing());
  }
  if (!(from.up() <= 0 && from.up() >= 0)) {
    _internal_set_up(from._internal_up());
  }
  if (from.status() != 0) {
    _internal_set_status(from._internal_status());
  }
}

void GNSSInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.GNSSInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GNSSInfo::CopyFrom(const GNSSInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.GNSSInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GNSSInfo::IsInitialized() const {
  return true;
}

void GNSSInfo::InternalSwap(GNSSInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GNSSInfo, status_)
      + sizeof(GNSSInfo::status_)
      - PROTOBUF_FIELD_OFFSET(GNSSInfo, time_stamp_)>(
          reinterpret_cast<char*>(&time_stamp_),
          reinterpret_cast<char*>(&other->time_stamp_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GNSSInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class GNSSCollection::_Internal {
 public:
};

GNSSCollection::GNSSCollection(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  gnss_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.hdmap.GNSSCollection)
}
GNSSCollection::GNSSCollection(const GNSSCollection& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      gnss_(from.gnss_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.hdmap.GNSSCollection)
}

void GNSSCollection::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_GNSSCollection_rtk_5fgps_2eproto.base);
}

GNSSCollection::~GNSSCollection() {
  // @@protoc_insertion_point(destructor:gwm.hdmap.GNSSCollection)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void GNSSCollection::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void GNSSCollection::ArenaDtor(void* object) {
  GNSSCollection* _this = reinterpret_cast< GNSSCollection* >(object);
  (void)_this;
}
void GNSSCollection::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GNSSCollection::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const GNSSCollection& GNSSCollection::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_GNSSCollection_rtk_5fgps_2eproto.base);
  return *internal_default_instance();
}


void GNSSCollection::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.hdmap.GNSSCollection)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  gnss_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GNSSCollection::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .gwm.hdmap.GNSSInfo gnss = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_gnss(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GNSSCollection::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.hdmap.GNSSCollection)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .gwm.hdmap.GNSSInfo gnss = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_gnss_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_gnss(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.hdmap.GNSSCollection)
  return target;
}

size_t GNSSCollection::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.hdmap.GNSSCollection)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.hdmap.GNSSInfo gnss = 1;
  total_size += 1UL * this->_internal_gnss_size();
  for (const auto& msg : this->gnss_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GNSSCollection::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.hdmap.GNSSCollection)
  GOOGLE_DCHECK_NE(&from, this);
  const GNSSCollection* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<GNSSCollection>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.hdmap.GNSSCollection)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.hdmap.GNSSCollection)
    MergeFrom(*source);
  }
}

void GNSSCollection::MergeFrom(const GNSSCollection& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.hdmap.GNSSCollection)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  gnss_.MergeFrom(from.gnss_);
}

void GNSSCollection::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.hdmap.GNSSCollection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GNSSCollection::CopyFrom(const GNSSCollection& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.hdmap.GNSSCollection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GNSSCollection::IsInitialized() const {
  return true;
}

void GNSSCollection::InternalSwap(GNSSCollection* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  gnss_.InternalSwap(&other->gnss_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GNSSCollection::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace hdmap
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::gwm::hdmap::GNSSInfo* Arena::CreateMaybeMessage< ::gwm::hdmap::GNSSInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::GNSSInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::hdmap::GNSSCollection* Arena::CreateMaybeMessage< ::gwm::hdmap::GNSSCollection >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::hdmap::GNSSCollection >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
