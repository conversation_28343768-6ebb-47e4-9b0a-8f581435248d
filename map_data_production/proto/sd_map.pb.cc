// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sd_map.proto

#include "sd_map.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_sd_5fmap_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ConnectRoads_sd_5fmap_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_sd_5fmap_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Header_sd_5fmap_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_sd_5fmap_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Point3D_sd_5fmap_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_sd_5fmap_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_Road_sd_5fmap_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_sd_5fmap_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_SDMapLocation_sd_5fmap_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_sd_5fmap_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SensorStamp_sd_5fmap_2eproto;
namespace gwm {
namespace sdmap {
class SensorStampDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SensorStamp> _instance;
} _SensorStamp_default_instance_;
class HeaderDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Header> _instance;
} _Header_default_instance_;
class Point3DDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Point3D> _instance;
} _Point3D_default_instance_;
class ConnectRoadsDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ConnectRoads> _instance;
} _ConnectRoads_default_instance_;
class RoadDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<Road> _instance;
} _Road_default_instance_;
class SDMapLocationDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SDMapLocation> _instance;
} _SDMapLocation_default_instance_;
class SDMapDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SDMap> _instance;
} _SDMap_default_instance_;
}  // namespace sdmap
}  // namespace gwm
static void InitDefaultsscc_info_ConnectRoads_sd_5fmap_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::sdmap::_ConnectRoads_default_instance_;
    new (ptr) ::gwm::sdmap::ConnectRoads();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ConnectRoads_sd_5fmap_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ConnectRoads_sd_5fmap_2eproto}, {}};

static void InitDefaultsscc_info_Header_sd_5fmap_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::sdmap::_Header_default_instance_;
    new (ptr) ::gwm::sdmap::Header();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_Header_sd_5fmap_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_Header_sd_5fmap_2eproto}, {
      &scc_info_SensorStamp_sd_5fmap_2eproto.base,}};

static void InitDefaultsscc_info_Point3D_sd_5fmap_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::sdmap::_Point3D_default_instance_;
    new (ptr) ::gwm::sdmap::Point3D();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_Point3D_sd_5fmap_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_Point3D_sd_5fmap_2eproto}, {}};

static void InitDefaultsscc_info_Road_sd_5fmap_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::sdmap::_Road_default_instance_;
    new (ptr) ::gwm::sdmap::Road();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_Road_sd_5fmap_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_Road_sd_5fmap_2eproto}, {
      &scc_info_Point3D_sd_5fmap_2eproto.base,
      &scc_info_ConnectRoads_sd_5fmap_2eproto.base,}};

static void InitDefaultsscc_info_SDMap_sd_5fmap_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::sdmap::_SDMap_default_instance_;
    new (ptr) ::gwm::sdmap::SDMap();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<3> scc_info_SDMap_sd_5fmap_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 3, 0, InitDefaultsscc_info_SDMap_sd_5fmap_2eproto}, {
      &scc_info_Header_sd_5fmap_2eproto.base,
      &scc_info_Road_sd_5fmap_2eproto.base,
      &scc_info_SDMapLocation_sd_5fmap_2eproto.base,}};

static void InitDefaultsscc_info_SDMapLocation_sd_5fmap_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::sdmap::_SDMapLocation_default_instance_;
    new (ptr) ::gwm::sdmap::SDMapLocation();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_SDMapLocation_sd_5fmap_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_SDMapLocation_sd_5fmap_2eproto}, {
      &scc_info_Point3D_sd_5fmap_2eproto.base,}};

static void InitDefaultsscc_info_SensorStamp_sd_5fmap_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::gwm::sdmap::_SensorStamp_default_instance_;
    new (ptr) ::gwm::sdmap::SensorStamp();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SensorStamp_sd_5fmap_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_SensorStamp_sd_5fmap_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_sd_5fmap_2eproto[7];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_sd_5fmap_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_sd_5fmap_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_sd_5fmap_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, lidar_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, radar_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, uss_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, chassis_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, camera_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, imuins_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SensorStamp, gnss_stamp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Header, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Header, seq_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Header, frame_id_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Header, publish_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Header, gnss_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Header, sensor_stamp_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Header, data_stamp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Point3D, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Point3D, x_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Point3D, y_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Point3D, z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::ConnectRoads, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::ConnectRoads, fromroadids_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::ConnectRoads, toroadids_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, roadid_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, points_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, lane_count_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, arrowmarkings_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, anglef_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, anglet_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, road_type_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, road_form_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, connect_road_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::Road, length_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMapLocation, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMapLocation, roadid_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMapLocation, offset_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMapLocation, matchpoint_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMap, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMap, header_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMap, road_),
  PROTOBUF_FIELD_OFFSET(::gwm::sdmap::SDMap, sd_location_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::gwm::sdmap::SensorStamp)},
  { 12, -1, sizeof(::gwm::sdmap::Header)},
  { 23, -1, sizeof(::gwm::sdmap::Point3D)},
  { 31, -1, sizeof(::gwm::sdmap::ConnectRoads)},
  { 38, -1, sizeof(::gwm::sdmap::Road)},
  { 53, -1, sizeof(::gwm::sdmap::SDMapLocation)},
  { 61, -1, sizeof(::gwm::sdmap::SDMap)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::sdmap::_SensorStamp_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::sdmap::_Header_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::sdmap::_Point3D_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::sdmap::_ConnectRoads_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::sdmap::_Road_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::sdmap::_SDMapLocation_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::gwm::sdmap::_SDMap_default_instance_),
};

const char descriptor_table_protodef_sd_5fmap_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\014sd_map.proto\022\tgwm.sdmap\"\241\001\n\013SensorStam"
  "p\022\023\n\013lidar_stamp\030\001 \001(\001\022\023\n\013radar_stamp\030\002 "
  "\001(\001\022\021\n\tuss_stamp\030\003 \001(\001\022\025\n\rchassis_stamp\030"
  "\004 \001(\001\022\024\n\014camera_stamp\030\005 \001(\001\022\024\n\014imuins_st"
  "amp\030\006 \001(\001\022\022\n\ngnss_stamp\030\007 \001(\001\"\224\001\n\006Header"
  "\022\013\n\003seq\030\001 \001(\005\022\020\n\010frame_id\030\002 \001(\t\022\025\n\rpubli"
  "sh_stamp\030\003 \001(\001\022\022\n\ngnss_stamp\030\004 \001(\001\022,\n\014se"
  "nsor_stamp\030\005 \001(\0132\026.gwm.sdmap.SensorStamp"
  "\022\022\n\ndata_stamp\030\006 \001(\001\"*\n\007Point3D\022\t\n\001x\030\001 \001"
  "(\001\022\t\n\001y\030\002 \001(\001\022\t\n\001z\030\003 \001(\001\"6\n\014ConnectRoads"
  "\022\023\n\013fromRoadIds\030\001 \003(\004\022\021\n\ttoRoadIds\030\002 \003(\004"
  "\"\376\t\n\004Road\022\016\n\006roadId\030\001 \001(\004\022\"\n\006points\030\002 \003("
  "\0132\022.gwm.sdmap.Point3D\022\022\n\nlane_count\030\003 \001("
  "\r\022\025\n\rarrowMarkings\030\004 \003(\r\022\016\n\006angleF\030\005 \001(\r"
  "\022\016\n\006angleT\030\006 \001(\r\022+\n\troad_type\030\007 \001(\0162\030.gw"
  "m.sdmap.Road.RoadType\022,\n\troad_form\030\010 \001(\016"
  "2\031.gwm.sdmap.Road.FormOfWay\022-\n\014connect_r"
  "oad\030\t \001(\0132\027.gwm.sdmap.ConnectRoads\022\016\n\006le"
  "ngth\030\n \001(\004\"\323\002\n\010RoadType\022\025\n\021ROADCLASS_FRE"
  "EWAY\020\000\022\033\n\027ROADCLASS_NATIONAL_ROAD\020\001\022\033\n\027R"
  "OADCLASS_PROVINCE_ROAD\020\002\022\031\n\025ROADCLASS_CO"
  "UNTY_ROAD\020\003\022\030\n\024ROADCLASS_RURAL_ROAD\020\004\022\034\n"
  "\030ROADCLASS_IN_COUNTY_ROAD\020\005\022\034\n\030ROADCLASS"
  "_CITY_SPEED_WAY\020\006\022\027\n\023ROADCLASS_MAIN_ROAD"
  "\020\007\022\034\n\030ROADCLASS_SECONDARY_ROAD\020\010\022\031\n\025ROAD"
  "CLASS_COMMON_ROAD\020\t\022\033\n\027ROADCLASS_NON_NAV"
  "I_ROAD\020\n\022\026\n\021ROADCLASS_INVALID\020\377\001\"\206\005\n\tFor"
  "mOfWay\022\023\n\017FORMWAY_UNKNOWN\020\000\022\030\n\024FORMWAY_D"
  "IVISED_LINK\020\001\022\026\n\022FORMWAY_CROSS_LINK\020\002\022\017\n"
  "\013FORMWAY_JCT\020\003\022\030\n\024FORMWAY_ROUND_CIRCLE\020\004"
  "\022\030\n\024FORMWAY_SERVICE_ROAD\020\005\022\025\n\021FORMWAY_SL"
  "IP_ROAD\020\006\022\025\n\021FORMWAY_SIDE_ROAD\020\007\022\024\n\020FORM"
  "WAY_SLIP_JCT\020\010\022\025\n\021FORMWAY_EXIT_LINK\020\t\022\031\n"
  "\025FORMWAY_ENTRANCE_LINK\020\n\022\034\n\030FORMWAY_TURN"
  "_RIGHT_LINEA\020\013\022\034\n\030FORMWAY_TURN_RIGHT_LIN"
  "EB\020\014\022\033\n\027FORMWAY_TURN_LEFT_LINEA\020\r\022\033\n\027FOR"
  "MWAY_TURN_LEFT_LINEB\020\016\022\027\n\023FORMWAY_COMMON"
  "_LINK\020\017\022\037\n\033FORMWAY_TURN_LEFTRIGHT_LINE\020\020"
  "\022!\n\035FORMWAY_NONMOTORIZED_DRIVEWAY\020\021\022\032\n\026F"
  "ORMWAY_FRONTDOOR_ROAD\020\022\022\035\n\031FORMWAY_SERVI"
  "CE_SLIP_ROAD\020\023\022\027\n\023FORMWAY_SERVICE_JCT\020\024\022"
  "!\n\035FORMWAY_SERVICE_JCT_SLIP_ROAD\020\025\022\027\n\023FO"
  "RMWAY_NON_VEHICLE\020\026\022\024\n\017FORMWAY_INVALID\020\377"
  "\001\"W\n\rSDMapLocation\022\016\n\006roadId\030\001 \001(\004\022\016\n\006of"
  "fset\030\002 \001(\r\022&\n\nmatchPoint\030\003 \001(\0132\022.gwm.sdm"
  "ap.Point3D\"x\n\005SDMap\022!\n\006header\030\001 \001(\0132\021.gw"
  "m.sdmap.Header\022\035\n\004road\030\002 \003(\0132\017.gwm.sdmap"
  ".Road\022-\n\013sd_Location\030\003 \001(\0132\030.gwm.sdmap.S"
  "DMapLocationb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_sd_5fmap_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_sd_5fmap_2eproto_sccs[7] = {
  &scc_info_ConnectRoads_sd_5fmap_2eproto.base,
  &scc_info_Header_sd_5fmap_2eproto.base,
  &scc_info_Point3D_sd_5fmap_2eproto.base,
  &scc_info_Road_sd_5fmap_2eproto.base,
  &scc_info_SDMap_sd_5fmap_2eproto.base,
  &scc_info_SDMapLocation_sd_5fmap_2eproto.base,
  &scc_info_SensorStamp_sd_5fmap_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_sd_5fmap_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_sd_5fmap_2eproto = {
  false, false, descriptor_table_protodef_sd_5fmap_2eproto, "sd_map.proto", 1940,
  &descriptor_table_sd_5fmap_2eproto_once, descriptor_table_sd_5fmap_2eproto_sccs, descriptor_table_sd_5fmap_2eproto_deps, 7, 0,
  schemas, file_default_instances, TableStruct_sd_5fmap_2eproto::offsets,
  file_level_metadata_sd_5fmap_2eproto, 7, file_level_enum_descriptors_sd_5fmap_2eproto, file_level_service_descriptors_sd_5fmap_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_sd_5fmap_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_sd_5fmap_2eproto)), true);
namespace gwm {
namespace sdmap {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Road_RoadType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_sd_5fmap_2eproto);
  return file_level_enum_descriptors_sd_5fmap_2eproto[0];
}
bool Road_RoadType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 255:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Road_RoadType Road::ROADCLASS_FREEWAY;
constexpr Road_RoadType Road::ROADCLASS_NATIONAL_ROAD;
constexpr Road_RoadType Road::ROADCLASS_PROVINCE_ROAD;
constexpr Road_RoadType Road::ROADCLASS_COUNTY_ROAD;
constexpr Road_RoadType Road::ROADCLASS_RURAL_ROAD;
constexpr Road_RoadType Road::ROADCLASS_IN_COUNTY_ROAD;
constexpr Road_RoadType Road::ROADCLASS_CITY_SPEED_WAY;
constexpr Road_RoadType Road::ROADCLASS_MAIN_ROAD;
constexpr Road_RoadType Road::ROADCLASS_SECONDARY_ROAD;
constexpr Road_RoadType Road::ROADCLASS_COMMON_ROAD;
constexpr Road_RoadType Road::ROADCLASS_NON_NAVI_ROAD;
constexpr Road_RoadType Road::ROADCLASS_INVALID;
constexpr Road_RoadType Road::RoadType_MIN;
constexpr Road_RoadType Road::RoadType_MAX;
constexpr int Road::RoadType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Road_FormOfWay_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_sd_5fmap_2eproto);
  return file_level_enum_descriptors_sd_5fmap_2eproto[1];
}
bool Road_FormOfWay_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
    case 255:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr Road_FormOfWay Road::FORMWAY_UNKNOWN;
constexpr Road_FormOfWay Road::FORMWAY_DIVISED_LINK;
constexpr Road_FormOfWay Road::FORMWAY_CROSS_LINK;
constexpr Road_FormOfWay Road::FORMWAY_JCT;
constexpr Road_FormOfWay Road::FORMWAY_ROUND_CIRCLE;
constexpr Road_FormOfWay Road::FORMWAY_SERVICE_ROAD;
constexpr Road_FormOfWay Road::FORMWAY_SLIP_ROAD;
constexpr Road_FormOfWay Road::FORMWAY_SIDE_ROAD;
constexpr Road_FormOfWay Road::FORMWAY_SLIP_JCT;
constexpr Road_FormOfWay Road::FORMWAY_EXIT_LINK;
constexpr Road_FormOfWay Road::FORMWAY_ENTRANCE_LINK;
constexpr Road_FormOfWay Road::FORMWAY_TURN_RIGHT_LINEA;
constexpr Road_FormOfWay Road::FORMWAY_TURN_RIGHT_LINEB;
constexpr Road_FormOfWay Road::FORMWAY_TURN_LEFT_LINEA;
constexpr Road_FormOfWay Road::FORMWAY_TURN_LEFT_LINEB;
constexpr Road_FormOfWay Road::FORMWAY_COMMON_LINK;
constexpr Road_FormOfWay Road::FORMWAY_TURN_LEFTRIGHT_LINE;
constexpr Road_FormOfWay Road::FORMWAY_NONMOTORIZED_DRIVEWAY;
constexpr Road_FormOfWay Road::FORMWAY_FRONTDOOR_ROAD;
constexpr Road_FormOfWay Road::FORMWAY_SERVICE_SLIP_ROAD;
constexpr Road_FormOfWay Road::FORMWAY_SERVICE_JCT;
constexpr Road_FormOfWay Road::FORMWAY_SERVICE_JCT_SLIP_ROAD;
constexpr Road_FormOfWay Road::FORMWAY_NON_VEHICLE;
constexpr Road_FormOfWay Road::FORMWAY_INVALID;
constexpr Road_FormOfWay Road::FormOfWay_MIN;
constexpr Road_FormOfWay Road::FormOfWay_MAX;
constexpr int Road::FormOfWay_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)

// ===================================================================

class SensorStamp::_Internal {
 public:
};

SensorStamp::SensorStamp(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.sdmap.SensorStamp)
}
SensorStamp::SensorStamp(const SensorStamp& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&lidar_stamp_, &from.lidar_stamp_,
    static_cast<size_t>(reinterpret_cast<char*>(&gnss_stamp_) -
    reinterpret_cast<char*>(&lidar_stamp_)) + sizeof(gnss_stamp_));
  // @@protoc_insertion_point(copy_constructor:gwm.sdmap.SensorStamp)
}

void SensorStamp::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&lidar_stamp_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&gnss_stamp_) -
      reinterpret_cast<char*>(&lidar_stamp_)) + sizeof(gnss_stamp_));
}

SensorStamp::~SensorStamp() {
  // @@protoc_insertion_point(destructor:gwm.sdmap.SensorStamp)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void SensorStamp::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void SensorStamp::ArenaDtor(void* object) {
  SensorStamp* _this = reinterpret_cast< SensorStamp* >(object);
  (void)_this;
}
void SensorStamp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SensorStamp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SensorStamp& SensorStamp::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SensorStamp_sd_5fmap_2eproto.base);
  return *internal_default_instance();
}


void SensorStamp::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.sdmap.SensorStamp)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&lidar_stamp_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&gnss_stamp_) -
      reinterpret_cast<char*>(&lidar_stamp_)) + sizeof(gnss_stamp_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SensorStamp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double lidar_stamp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          lidar_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double radar_stamp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          radar_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double uss_stamp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          uss_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double chassis_stamp = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 33)) {
          chassis_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double camera_stamp = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 41)) {
          camera_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double imuins_stamp = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 49)) {
          imuins_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double gnss_stamp = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 57)) {
          gnss_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SensorStamp::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.sdmap.SensorStamp)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double lidar_stamp = 1;
  if (!(this->lidar_stamp() <= 0 && this->lidar_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_lidar_stamp(), target);
  }

  // double radar_stamp = 2;
  if (!(this->radar_stamp() <= 0 && this->radar_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_radar_stamp(), target);
  }

  // double uss_stamp = 3;
  if (!(this->uss_stamp() <= 0 && this->uss_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_uss_stamp(), target);
  }

  // double chassis_stamp = 4;
  if (!(this->chassis_stamp() <= 0 && this->chassis_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_chassis_stamp(), target);
  }

  // double camera_stamp = 5;
  if (!(this->camera_stamp() <= 0 && this->camera_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_camera_stamp(), target);
  }

  // double imuins_stamp = 6;
  if (!(this->imuins_stamp() <= 0 && this->imuins_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(6, this->_internal_imuins_stamp(), target);
  }

  // double gnss_stamp = 7;
  if (!(this->gnss_stamp() <= 0 && this->gnss_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(7, this->_internal_gnss_stamp(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.sdmap.SensorStamp)
  return target;
}

size_t SensorStamp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.sdmap.SensorStamp)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double lidar_stamp = 1;
  if (!(this->lidar_stamp() <= 0 && this->lidar_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double radar_stamp = 2;
  if (!(this->radar_stamp() <= 0 && this->radar_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double uss_stamp = 3;
  if (!(this->uss_stamp() <= 0 && this->uss_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double chassis_stamp = 4;
  if (!(this->chassis_stamp() <= 0 && this->chassis_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double camera_stamp = 5;
  if (!(this->camera_stamp() <= 0 && this->camera_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double imuins_stamp = 6;
  if (!(this->imuins_stamp() <= 0 && this->imuins_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double gnss_stamp = 7;
  if (!(this->gnss_stamp() <= 0 && this->gnss_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SensorStamp::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.sdmap.SensorStamp)
  GOOGLE_DCHECK_NE(&from, this);
  const SensorStamp* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SensorStamp>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.sdmap.SensorStamp)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.sdmap.SensorStamp)
    MergeFrom(*source);
  }
}

void SensorStamp::MergeFrom(const SensorStamp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.sdmap.SensorStamp)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.lidar_stamp() <= 0 && from.lidar_stamp() >= 0)) {
    _internal_set_lidar_stamp(from._internal_lidar_stamp());
  }
  if (!(from.radar_stamp() <= 0 && from.radar_stamp() >= 0)) {
    _internal_set_radar_stamp(from._internal_radar_stamp());
  }
  if (!(from.uss_stamp() <= 0 && from.uss_stamp() >= 0)) {
    _internal_set_uss_stamp(from._internal_uss_stamp());
  }
  if (!(from.chassis_stamp() <= 0 && from.chassis_stamp() >= 0)) {
    _internal_set_chassis_stamp(from._internal_chassis_stamp());
  }
  if (!(from.camera_stamp() <= 0 && from.camera_stamp() >= 0)) {
    _internal_set_camera_stamp(from._internal_camera_stamp());
  }
  if (!(from.imuins_stamp() <= 0 && from.imuins_stamp() >= 0)) {
    _internal_set_imuins_stamp(from._internal_imuins_stamp());
  }
  if (!(from.gnss_stamp() <= 0 && from.gnss_stamp() >= 0)) {
    _internal_set_gnss_stamp(from._internal_gnss_stamp());
  }
}

void SensorStamp::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.sdmap.SensorStamp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SensorStamp::CopyFrom(const SensorStamp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.sdmap.SensorStamp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SensorStamp::IsInitialized() const {
  return true;
}

void SensorStamp::InternalSwap(SensorStamp* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SensorStamp, gnss_stamp_)
      + sizeof(SensorStamp::gnss_stamp_)
      - PROTOBUF_FIELD_OFFSET(SensorStamp, lidar_stamp_)>(
          reinterpret_cast<char*>(&lidar_stamp_),
          reinterpret_cast<char*>(&other->lidar_stamp_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SensorStamp::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Header::_Internal {
 public:
  static const ::gwm::sdmap::SensorStamp& sensor_stamp(const Header* msg);
};

const ::gwm::sdmap::SensorStamp&
Header::_Internal::sensor_stamp(const Header* msg) {
  return *msg->sensor_stamp_;
}
Header::Header(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.sdmap.Header)
}
Header::Header(const Header& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  frame_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_frame_id().empty()) {
    frame_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_frame_id(), 
      GetArena());
  }
  if (from._internal_has_sensor_stamp()) {
    sensor_stamp_ = new ::gwm::sdmap::SensorStamp(*from.sensor_stamp_);
  } else {
    sensor_stamp_ = nullptr;
  }
  ::memcpy(&publish_stamp_, &from.publish_stamp_,
    static_cast<size_t>(reinterpret_cast<char*>(&seq_) -
    reinterpret_cast<char*>(&publish_stamp_)) + sizeof(seq_));
  // @@protoc_insertion_point(copy_constructor:gwm.sdmap.Header)
}

void Header::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Header_sd_5fmap_2eproto.base);
  frame_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&sensor_stamp_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&seq_) -
      reinterpret_cast<char*>(&sensor_stamp_)) + sizeof(seq_));
}

Header::~Header() {
  // @@protoc_insertion_point(destructor:gwm.sdmap.Header)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Header::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  frame_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete sensor_stamp_;
}

void Header::ArenaDtor(void* object) {
  Header* _this = reinterpret_cast< Header* >(object);
  (void)_this;
}
void Header::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Header::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Header& Header::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Header_sd_5fmap_2eproto.base);
  return *internal_default_instance();
}


void Header::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.sdmap.Header)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  frame_id_.ClearToEmpty();
  if (GetArena() == nullptr && sensor_stamp_ != nullptr) {
    delete sensor_stamp_;
  }
  sensor_stamp_ = nullptr;
  ::memset(&publish_stamp_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&seq_) -
      reinterpret_cast<char*>(&publish_stamp_)) + sizeof(seq_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Header::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int32 seq = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          seq_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string frame_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_frame_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "gwm.sdmap.Header.frame_id"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double publish_stamp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          publish_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double gnss_stamp = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 33)) {
          gnss_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // .gwm.sdmap.SensorStamp sensor_stamp = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_sensor_stamp(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double data_stamp = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 49)) {
          data_stamp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Header::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.sdmap.Header)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 seq = 1;
  if (this->seq() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_seq(), target);
  }

  // string frame_id = 2;
  if (this->frame_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_frame_id().data(), static_cast<int>(this->_internal_frame_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "gwm.sdmap.Header.frame_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_frame_id(), target);
  }

  // double publish_stamp = 3;
  if (!(this->publish_stamp() <= 0 && this->publish_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_publish_stamp(), target);
  }

  // double gnss_stamp = 4;
  if (!(this->gnss_stamp() <= 0 && this->gnss_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_gnss_stamp(), target);
  }

  // .gwm.sdmap.SensorStamp sensor_stamp = 5;
  if (this->has_sensor_stamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::sensor_stamp(this), target, stream);
  }

  // double data_stamp = 6;
  if (!(this->data_stamp() <= 0 && this->data_stamp() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(6, this->_internal_data_stamp(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.sdmap.Header)
  return target;
}

size_t Header::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.sdmap.Header)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string frame_id = 2;
  if (this->frame_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_frame_id());
  }

  // .gwm.sdmap.SensorStamp sensor_stamp = 5;
  if (this->has_sensor_stamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *sensor_stamp_);
  }

  // double publish_stamp = 3;
  if (!(this->publish_stamp() <= 0 && this->publish_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double gnss_stamp = 4;
  if (!(this->gnss_stamp() <= 0 && this->gnss_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // double data_stamp = 6;
  if (!(this->data_stamp() <= 0 && this->data_stamp() >= 0)) {
    total_size += 1 + 8;
  }

  // int32 seq = 1;
  if (this->seq() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_seq());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Header::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.sdmap.Header)
  GOOGLE_DCHECK_NE(&from, this);
  const Header* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Header>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.sdmap.Header)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.sdmap.Header)
    MergeFrom(*source);
  }
}

void Header::MergeFrom(const Header& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.sdmap.Header)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.frame_id().size() > 0) {
    _internal_set_frame_id(from._internal_frame_id());
  }
  if (from.has_sensor_stamp()) {
    _internal_mutable_sensor_stamp()->::gwm::sdmap::SensorStamp::MergeFrom(from._internal_sensor_stamp());
  }
  if (!(from.publish_stamp() <= 0 && from.publish_stamp() >= 0)) {
    _internal_set_publish_stamp(from._internal_publish_stamp());
  }
  if (!(from.gnss_stamp() <= 0 && from.gnss_stamp() >= 0)) {
    _internal_set_gnss_stamp(from._internal_gnss_stamp());
  }
  if (!(from.data_stamp() <= 0 && from.data_stamp() >= 0)) {
    _internal_set_data_stamp(from._internal_data_stamp());
  }
  if (from.seq() != 0) {
    _internal_set_seq(from._internal_seq());
  }
}

void Header::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.sdmap.Header)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Header::CopyFrom(const Header& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.sdmap.Header)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Header::IsInitialized() const {
  return true;
}

void Header::InternalSwap(Header* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  frame_id_.Swap(&other->frame_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Header, seq_)
      + sizeof(Header::seq_)
      - PROTOBUF_FIELD_OFFSET(Header, sensor_stamp_)>(
          reinterpret_cast<char*>(&sensor_stamp_),
          reinterpret_cast<char*>(&other->sensor_stamp_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Header::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Point3D::_Internal {
 public:
};

Point3D::Point3D(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.sdmap.Point3D)
}
Point3D::Point3D(const Point3D& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&z_) -
    reinterpret_cast<char*>(&x_)) + sizeof(z_));
  // @@protoc_insertion_point(copy_constructor:gwm.sdmap.Point3D)
}

void Point3D::SharedCtor() {
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
}

Point3D::~Point3D() {
  // @@protoc_insertion_point(destructor:gwm.sdmap.Point3D)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Point3D::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void Point3D::ArenaDtor(void* object) {
  Point3D* _this = reinterpret_cast< Point3D* >(object);
  (void)_this;
}
void Point3D::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Point3D::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Point3D& Point3D::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Point3D_sd_5fmap_2eproto.base);
  return *internal_default_instance();
}


void Point3D::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.sdmap.Point3D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&z_) -
      reinterpret_cast<char*>(&x_)) + sizeof(z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Point3D::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 9)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 17)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 25)) {
          z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Point3D::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.sdmap.Point3D)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  // double z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.sdmap.Point3D)
  return target;
}

size_t Point3D::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.sdmap.Point3D)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  if (!(this->x() <= 0 && this->x() >= 0)) {
    total_size += 1 + 8;
  }

  // double y = 2;
  if (!(this->y() <= 0 && this->y() >= 0)) {
    total_size += 1 + 8;
  }

  // double z = 3;
  if (!(this->z() <= 0 && this->z() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Point3D::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.sdmap.Point3D)
  GOOGLE_DCHECK_NE(&from, this);
  const Point3D* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Point3D>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.sdmap.Point3D)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.sdmap.Point3D)
    MergeFrom(*source);
  }
}

void Point3D::MergeFrom(const Point3D& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.sdmap.Point3D)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (!(from.x() <= 0 && from.x() >= 0)) {
    _internal_set_x(from._internal_x());
  }
  if (!(from.y() <= 0 && from.y() >= 0)) {
    _internal_set_y(from._internal_y());
  }
  if (!(from.z() <= 0 && from.z() >= 0)) {
    _internal_set_z(from._internal_z());
  }
}

void Point3D::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.sdmap.Point3D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Point3D::CopyFrom(const Point3D& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.sdmap.Point3D)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Point3D::IsInitialized() const {
  return true;
}

void Point3D::InternalSwap(Point3D* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Point3D, z_)
      + sizeof(Point3D::z_)
      - PROTOBUF_FIELD_OFFSET(Point3D, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Point3D::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class ConnectRoads::_Internal {
 public:
};

ConnectRoads::ConnectRoads(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  fromroadids_(arena),
  toroadids_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.sdmap.ConnectRoads)
}
ConnectRoads::ConnectRoads(const ConnectRoads& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      fromroadids_(from.fromroadids_),
      toroadids_(from.toroadids_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:gwm.sdmap.ConnectRoads)
}

void ConnectRoads::SharedCtor() {
}

ConnectRoads::~ConnectRoads() {
  // @@protoc_insertion_point(destructor:gwm.sdmap.ConnectRoads)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ConnectRoads::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void ConnectRoads::ArenaDtor(void* object) {
  ConnectRoads* _this = reinterpret_cast< ConnectRoads* >(object);
  (void)_this;
}
void ConnectRoads::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConnectRoads::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ConnectRoads& ConnectRoads::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ConnectRoads_sd_5fmap_2eproto.base);
  return *internal_default_instance();
}


void ConnectRoads::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.sdmap.ConnectRoads)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  fromroadids_.Clear();
  toroadids_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConnectRoads::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated uint64 fromRoadIds = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt64Parser(_internal_mutable_fromroadids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8) {
          _internal_add_fromroadids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated uint64 toRoadIds = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt64Parser(_internal_mutable_toroadids(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16) {
          _internal_add_toroadids(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ConnectRoads::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.sdmap.ConnectRoads)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated uint64 fromRoadIds = 1;
  {
    int byte_size = _fromroadids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt64Packed(
          1, _internal_fromroadids(), byte_size, target);
    }
  }

  // repeated uint64 toRoadIds = 2;
  {
    int byte_size = _toroadids_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt64Packed(
          2, _internal_toroadids(), byte_size, target);
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.sdmap.ConnectRoads)
  return target;
}

size_t ConnectRoads::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.sdmap.ConnectRoads)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated uint64 fromRoadIds = 1;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt64Size(this->fromroadids_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _fromroadids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated uint64 toRoadIds = 2;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt64Size(this->toroadids_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _toroadids_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ConnectRoads::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.sdmap.ConnectRoads)
  GOOGLE_DCHECK_NE(&from, this);
  const ConnectRoads* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ConnectRoads>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.sdmap.ConnectRoads)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.sdmap.ConnectRoads)
    MergeFrom(*source);
  }
}

void ConnectRoads::MergeFrom(const ConnectRoads& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.sdmap.ConnectRoads)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  fromroadids_.MergeFrom(from.fromroadids_);
  toroadids_.MergeFrom(from.toroadids_);
}

void ConnectRoads::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.sdmap.ConnectRoads)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ConnectRoads::CopyFrom(const ConnectRoads& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.sdmap.ConnectRoads)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConnectRoads::IsInitialized() const {
  return true;
}

void ConnectRoads::InternalSwap(ConnectRoads* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  fromroadids_.InternalSwap(&other->fromroadids_);
  toroadids_.InternalSwap(&other->toroadids_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ConnectRoads::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class Road::_Internal {
 public:
  static const ::gwm::sdmap::ConnectRoads& connect_road(const Road* msg);
};

const ::gwm::sdmap::ConnectRoads&
Road::_Internal::connect_road(const Road* msg) {
  return *msg->connect_road_;
}
Road::Road(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  points_(arena),
  arrowmarkings_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.sdmap.Road)
}
Road::Road(const Road& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      points_(from.points_),
      arrowmarkings_(from.arrowmarkings_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_connect_road()) {
    connect_road_ = new ::gwm::sdmap::ConnectRoads(*from.connect_road_);
  } else {
    connect_road_ = nullptr;
  }
  ::memcpy(&roadid_, &from.roadid_,
    static_cast<size_t>(reinterpret_cast<char*>(&road_form_) -
    reinterpret_cast<char*>(&roadid_)) + sizeof(road_form_));
  // @@protoc_insertion_point(copy_constructor:gwm.sdmap.Road)
}

void Road::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_Road_sd_5fmap_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&connect_road_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&road_form_) -
      reinterpret_cast<char*>(&connect_road_)) + sizeof(road_form_));
}

Road::~Road() {
  // @@protoc_insertion_point(destructor:gwm.sdmap.Road)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void Road::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete connect_road_;
}

void Road::ArenaDtor(void* object) {
  Road* _this = reinterpret_cast< Road* >(object);
  (void)_this;
}
void Road::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Road::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const Road& Road::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_Road_sd_5fmap_2eproto.base);
  return *internal_default_instance();
}


void Road::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.sdmap.Road)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  points_.Clear();
  arrowmarkings_.Clear();
  if (GetArena() == nullptr && connect_road_ != nullptr) {
    delete connect_road_;
  }
  connect_road_ = nullptr;
  ::memset(&roadid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&road_form_) -
      reinterpret_cast<char*>(&roadid_)) + sizeof(road_form_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Road::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // uint64 roadId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          roadid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.sdmap.Point3D points = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_points(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      // uint32 lane_count = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          lane_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated uint32 arrowMarkings = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_arrowmarkings(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32) {
          _internal_add_arrowmarkings(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 angleF = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          anglef_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 angleT = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          anglet_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.sdmap.Road.RoadType road_type = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_road_type(static_cast<::gwm::sdmap::Road_RoadType>(val));
        } else goto handle_unusual;
        continue;
      // .gwm.sdmap.Road.FormOfWay road_form = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_road_form(static_cast<::gwm::sdmap::Road_FormOfWay>(val));
        } else goto handle_unusual;
        continue;
      // .gwm.sdmap.ConnectRoads connect_road = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_connect_road(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint64 length = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 80)) {
          length_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* Road::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.sdmap.Road)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 roadId = 1;
  if (this->roadid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_roadid(), target);
  }

  // repeated .gwm.sdmap.Point3D points = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_points_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_points(i), target, stream);
  }

  // uint32 lane_count = 3;
  if (this->lane_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_lane_count(), target);
  }

  // repeated uint32 arrowMarkings = 4;
  {
    int byte_size = _arrowmarkings_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteUInt32Packed(
          4, _internal_arrowmarkings(), byte_size, target);
    }
  }

  // uint32 angleF = 5;
  if (this->anglef() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_anglef(), target);
  }

  // uint32 angleT = 6;
  if (this->anglet() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_anglet(), target);
  }

  // .gwm.sdmap.Road.RoadType road_type = 7;
  if (this->road_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      7, this->_internal_road_type(), target);
  }

  // .gwm.sdmap.Road.FormOfWay road_form = 8;
  if (this->road_form() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_road_form(), target);
  }

  // .gwm.sdmap.ConnectRoads connect_road = 9;
  if (this->has_connect_road()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::connect_road(this), target, stream);
  }

  // uint64 length = 10;
  if (this->length() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(10, this->_internal_length(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.sdmap.Road)
  return target;
}

size_t Road::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.sdmap.Road)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.sdmap.Point3D points = 2;
  total_size += 1UL * this->_internal_points_size();
  for (const auto& msg : this->points_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated uint32 arrowMarkings = 4;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      UInt32Size(this->arrowmarkings_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<::PROTOBUF_NAMESPACE_ID::int32>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _arrowmarkings_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // .gwm.sdmap.ConnectRoads connect_road = 9;
  if (this->has_connect_road()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *connect_road_);
  }

  // uint64 roadId = 1;
  if (this->roadid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_roadid());
  }

  // uint32 lane_count = 3;
  if (this->lane_count() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_lane_count());
  }

  // uint32 angleF = 5;
  if (this->anglef() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_anglef());
  }

  // uint32 angleT = 6;
  if (this->anglet() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_anglet());
  }

  // .gwm.sdmap.Road.RoadType road_type = 7;
  if (this->road_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_road_type());
  }

  // uint64 length = 10;
  if (this->length() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_length());
  }

  // .gwm.sdmap.Road.FormOfWay road_form = 8;
  if (this->road_form() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_road_form());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Road::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.sdmap.Road)
  GOOGLE_DCHECK_NE(&from, this);
  const Road* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<Road>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.sdmap.Road)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.sdmap.Road)
    MergeFrom(*source);
  }
}

void Road::MergeFrom(const Road& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.sdmap.Road)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  points_.MergeFrom(from.points_);
  arrowmarkings_.MergeFrom(from.arrowmarkings_);
  if (from.has_connect_road()) {
    _internal_mutable_connect_road()->::gwm::sdmap::ConnectRoads::MergeFrom(from._internal_connect_road());
  }
  if (from.roadid() != 0) {
    _internal_set_roadid(from._internal_roadid());
  }
  if (from.lane_count() != 0) {
    _internal_set_lane_count(from._internal_lane_count());
  }
  if (from.anglef() != 0) {
    _internal_set_anglef(from._internal_anglef());
  }
  if (from.anglet() != 0) {
    _internal_set_anglet(from._internal_anglet());
  }
  if (from.road_type() != 0) {
    _internal_set_road_type(from._internal_road_type());
  }
  if (from.length() != 0) {
    _internal_set_length(from._internal_length());
  }
  if (from.road_form() != 0) {
    _internal_set_road_form(from._internal_road_form());
  }
}

void Road::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.sdmap.Road)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Road::CopyFrom(const Road& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.sdmap.Road)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Road::IsInitialized() const {
  return true;
}

void Road::InternalSwap(Road* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  points_.InternalSwap(&other->points_);
  arrowmarkings_.InternalSwap(&other->arrowmarkings_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Road, road_form_)
      + sizeof(Road::road_form_)
      - PROTOBUF_FIELD_OFFSET(Road, connect_road_)>(
          reinterpret_cast<char*>(&connect_road_),
          reinterpret_cast<char*>(&other->connect_road_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Road::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class SDMapLocation::_Internal {
 public:
  static const ::gwm::sdmap::Point3D& matchpoint(const SDMapLocation* msg);
};

const ::gwm::sdmap::Point3D&
SDMapLocation::_Internal::matchpoint(const SDMapLocation* msg) {
  return *msg->matchpoint_;
}
SDMapLocation::SDMapLocation(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.sdmap.SDMapLocation)
}
SDMapLocation::SDMapLocation(const SDMapLocation& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_matchpoint()) {
    matchpoint_ = new ::gwm::sdmap::Point3D(*from.matchpoint_);
  } else {
    matchpoint_ = nullptr;
  }
  ::memcpy(&roadid_, &from.roadid_,
    static_cast<size_t>(reinterpret_cast<char*>(&offset_) -
    reinterpret_cast<char*>(&roadid_)) + sizeof(offset_));
  // @@protoc_insertion_point(copy_constructor:gwm.sdmap.SDMapLocation)
}

void SDMapLocation::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_SDMapLocation_sd_5fmap_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&matchpoint_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&offset_) -
      reinterpret_cast<char*>(&matchpoint_)) + sizeof(offset_));
}

SDMapLocation::~SDMapLocation() {
  // @@protoc_insertion_point(destructor:gwm.sdmap.SDMapLocation)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void SDMapLocation::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete matchpoint_;
}

void SDMapLocation::ArenaDtor(void* object) {
  SDMapLocation* _this = reinterpret_cast< SDMapLocation* >(object);
  (void)_this;
}
void SDMapLocation::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SDMapLocation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SDMapLocation& SDMapLocation::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SDMapLocation_sd_5fmap_2eproto.base);
  return *internal_default_instance();
}


void SDMapLocation::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.sdmap.SDMapLocation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArena() == nullptr && matchpoint_ != nullptr) {
    delete matchpoint_;
  }
  matchpoint_ = nullptr;
  ::memset(&roadid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&offset_) -
      reinterpret_cast<char*>(&roadid_)) + sizeof(offset_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SDMapLocation::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // uint64 roadId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          roadid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // uint32 offset = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          offset_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .gwm.sdmap.Point3D matchPoint = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_matchpoint(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SDMapLocation::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.sdmap.SDMapLocation)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 roadId = 1;
  if (this->roadid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_roadid(), target);
  }

  // uint32 offset = 2;
  if (this->offset() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_offset(), target);
  }

  // .gwm.sdmap.Point3D matchPoint = 3;
  if (this->has_matchpoint()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::matchpoint(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.sdmap.SDMapLocation)
  return target;
}

size_t SDMapLocation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.sdmap.SDMapLocation)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .gwm.sdmap.Point3D matchPoint = 3;
  if (this->has_matchpoint()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *matchpoint_);
  }

  // uint64 roadId = 1;
  if (this->roadid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64Size(
        this->_internal_roadid());
  }

  // uint32 offset = 2;
  if (this->offset() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32Size(
        this->_internal_offset());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SDMapLocation::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.sdmap.SDMapLocation)
  GOOGLE_DCHECK_NE(&from, this);
  const SDMapLocation* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SDMapLocation>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.sdmap.SDMapLocation)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.sdmap.SDMapLocation)
    MergeFrom(*source);
  }
}

void SDMapLocation::MergeFrom(const SDMapLocation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.sdmap.SDMapLocation)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_matchpoint()) {
    _internal_mutable_matchpoint()->::gwm::sdmap::Point3D::MergeFrom(from._internal_matchpoint());
  }
  if (from.roadid() != 0) {
    _internal_set_roadid(from._internal_roadid());
  }
  if (from.offset() != 0) {
    _internal_set_offset(from._internal_offset());
  }
}

void SDMapLocation::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.sdmap.SDMapLocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SDMapLocation::CopyFrom(const SDMapLocation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.sdmap.SDMapLocation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SDMapLocation::IsInitialized() const {
  return true;
}

void SDMapLocation::InternalSwap(SDMapLocation* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SDMapLocation, offset_)
      + sizeof(SDMapLocation::offset_)
      - PROTOBUF_FIELD_OFFSET(SDMapLocation, matchpoint_)>(
          reinterpret_cast<char*>(&matchpoint_),
          reinterpret_cast<char*>(&other->matchpoint_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SDMapLocation::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

class SDMap::_Internal {
 public:
  static const ::gwm::sdmap::Header& header(const SDMap* msg);
  static const ::gwm::sdmap::SDMapLocation& sd_location(const SDMap* msg);
};

const ::gwm::sdmap::Header&
SDMap::_Internal::header(const SDMap* msg) {
  return *msg->header_;
}
const ::gwm::sdmap::SDMapLocation&
SDMap::_Internal::sd_location(const SDMap* msg) {
  return *msg->sd_location_;
}
SDMap::SDMap(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  road_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:gwm.sdmap.SDMap)
}
SDMap::SDMap(const SDMap& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      road_(from.road_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_header()) {
    header_ = new ::gwm::sdmap::Header(*from.header_);
  } else {
    header_ = nullptr;
  }
  if (from._internal_has_sd_location()) {
    sd_location_ = new ::gwm::sdmap::SDMapLocation(*from.sd_location_);
  } else {
    sd_location_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:gwm.sdmap.SDMap)
}

void SDMap::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_SDMap_sd_5fmap_2eproto.base);
  ::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
      reinterpret_cast<char*>(&header_) - reinterpret_cast<char*>(this)),
      0, static_cast<size_t>(reinterpret_cast<char*>(&sd_location_) -
      reinterpret_cast<char*>(&header_)) + sizeof(sd_location_));
}

SDMap::~SDMap() {
  // @@protoc_insertion_point(destructor:gwm.sdmap.SDMap)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void SDMap::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  if (this != internal_default_instance()) delete header_;
  if (this != internal_default_instance()) delete sd_location_;
}

void SDMap::ArenaDtor(void* object) {
  SDMap* _this = reinterpret_cast< SDMap* >(object);
  (void)_this;
}
void SDMap::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SDMap::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SDMap& SDMap::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SDMap_sd_5fmap_2eproto.base);
  return *internal_default_instance();
}


void SDMap::Clear() {
// @@protoc_insertion_point(message_clear_start:gwm.sdmap.SDMap)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  road_.Clear();
  if (GetArena() == nullptr && header_ != nullptr) {
    delete header_;
  }
  header_ = nullptr;
  if (GetArena() == nullptr && sd_location_ != nullptr) {
    delete sd_location_;
  }
  sd_location_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SDMap::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .gwm.sdmap.Header header = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_header(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .gwm.sdmap.Road road = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_road(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      // .gwm.sdmap.SDMapLocation sd_Location = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_sd_location(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SDMap::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:gwm.sdmap.SDMap)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .gwm.sdmap.Header header = 1;
  if (this->has_header()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::header(this), target, stream);
  }

  // repeated .gwm.sdmap.Road road = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_road_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_road(i), target, stream);
  }

  // .gwm.sdmap.SDMapLocation sd_Location = 3;
  if (this->has_sd_location()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::sd_location(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:gwm.sdmap.SDMap)
  return target;
}

size_t SDMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:gwm.sdmap.SDMap)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .gwm.sdmap.Road road = 2;
  total_size += 1UL * this->_internal_road_size();
  for (const auto& msg : this->road_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .gwm.sdmap.Header header = 1;
  if (this->has_header()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *header_);
  }

  // .gwm.sdmap.SDMapLocation sd_Location = 3;
  if (this->has_sd_location()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *sd_location_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SDMap::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:gwm.sdmap.SDMap)
  GOOGLE_DCHECK_NE(&from, this);
  const SDMap* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SDMap>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:gwm.sdmap.SDMap)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:gwm.sdmap.SDMap)
    MergeFrom(*source);
  }
}

void SDMap::MergeFrom(const SDMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:gwm.sdmap.SDMap)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  road_.MergeFrom(from.road_);
  if (from.has_header()) {
    _internal_mutable_header()->::gwm::sdmap::Header::MergeFrom(from._internal_header());
  }
  if (from.has_sd_location()) {
    _internal_mutable_sd_location()->::gwm::sdmap::SDMapLocation::MergeFrom(from._internal_sd_location());
  }
}

void SDMap::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:gwm.sdmap.SDMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SDMap::CopyFrom(const SDMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:gwm.sdmap.SDMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SDMap::IsInitialized() const {
  return true;
}

void SDMap::InternalSwap(SDMap* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  road_.InternalSwap(&other->road_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SDMap, sd_location_)
      + sizeof(SDMap::sd_location_)
      - PROTOBUF_FIELD_OFFSET(SDMap, header_)>(
          reinterpret_cast<char*>(&header_),
          reinterpret_cast<char*>(&other->header_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SDMap::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace sdmap
}  // namespace gwm
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::gwm::sdmap::SensorStamp* Arena::CreateMaybeMessage< ::gwm::sdmap::SensorStamp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::sdmap::SensorStamp >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::sdmap::Header* Arena::CreateMaybeMessage< ::gwm::sdmap::Header >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::sdmap::Header >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::sdmap::Point3D* Arena::CreateMaybeMessage< ::gwm::sdmap::Point3D >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::sdmap::Point3D >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::sdmap::ConnectRoads* Arena::CreateMaybeMessage< ::gwm::sdmap::ConnectRoads >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::sdmap::ConnectRoads >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::sdmap::Road* Arena::CreateMaybeMessage< ::gwm::sdmap::Road >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::sdmap::Road >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::sdmap::SDMapLocation* Arena::CreateMaybeMessage< ::gwm::sdmap::SDMapLocation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::sdmap::SDMapLocation >(arena);
}
template<> PROTOBUF_NOINLINE ::gwm::sdmap::SDMap* Arena::CreateMaybeMessage< ::gwm::sdmap::SDMap >(Arena* arena) {
  return Arena::CreateMessageInternal< ::gwm::sdmap::SDMap >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
